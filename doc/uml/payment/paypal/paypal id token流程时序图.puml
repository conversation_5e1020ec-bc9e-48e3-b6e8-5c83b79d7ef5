@startuml
'https://plantuml.com/sequence-diagram

title paypal id token流程时序图

'用户
actor       customer   as customer

'客户端（浏览器）
participant Client  as c

'服务端（服务器）
participant Server  as s

'服务端（paypal平台）
participant paypal  as paypal

'数据库
database    db      as db

customer -> c : 0.进入结算页（登录用户&存在paypal 客户id）

c -> s: 1.获取id token

s -> s: 2.登录用户校验

s -> s: 3.订阅信息校验
note left
1、需是paypal渠道
2、存在paypal 客户id
end note

s -> paypal: 4.请求/v1/oauth2/token api
note left
1、网络超时重试3次
2、网络请求失败，抛异常，页面需做兜底
end note

paypal --> s:5.返回响应结果

s -> s: 6.解析id token

s --> c:7.返回id token

@enduml