@startuml

title 信用卡支付流程时序图

'用户
actor       customer   as customer

'客户端（浏览器）
participant Client  as c

'服务端（服务器）
participant Server  as s

'checkout（三方支付平台）
participant CreditCardSer  as ccs

'银行
participant bank    as bank

'数据库
database    db      as db

'队列
queue       queue   as queue

customer -> c: 0、store.insta360.com(输入信用卡信息【Confirmed】)
c -> s: 1、获取支付规则
note left
具体详情见"信用卡支付规则流程时序图"。
end note
s --> c: 2、返回支付规则
c -> s: 3、发起支付
s -> db: 4、订单源数据获取
db --> s: 5、数据获取成功
s -> s: 6、订单支付前置处理
'备注
note left
a、获取支付渠道（pay_channel）
b、订单参数校验
c、支付信息初始化
d、获取对应的支付逻辑处理器
e、获取对应的PaymentRequest
f、构造支付接口信息
g、参数签名
end note

s -> ccs: 7、发起支付请求
ccs --> s: 8、响应前置支付结果
s -> s: 9、签名参数验证
note right
出现这一步的原因是可能会出现最开始IP地址解析就被拦截的情况。
不过只要与对方的公私钥未被泄露，则可保证不会收到第三方的恶意信息。
这种情况应该只会理论上存在。
end note

group 不通过
s --> c: 10、返回该订单的订单号
c --> customer: 11、支付失败
else 通过
s -> s: 12、获取3D验证链接

group 未携带3D验证链接
s --> c: 13、返回该订单的订单号、页面Redirect（加载中，等待后续的支付结果同步）
ccs --> s: 14、支付结果异步通知
s -> s: 15、签名参数验证
note right
出现这一步的原因则可能是第三方的恶意调用。
由于我们接受异步通知的接口是没有做黑白名单限制，公网环境下，任何人都可以调用，
这时只要对方掌握了我们与支付平台之间的数据结构，然后对数据稍加修改，即可完成
订单支付伪造。且概率极高。性质恶劣。不可不防。
end note
group 不通过
s -> s: 16、不予处理
else 通过
s -> s: 17、获取支付结果

group 支付成功
s -> db: 18、修改订单状态、记录支付信息
s -> queue: 19、发送支付成功邮件
c --> customer: 20、支付成功（步骤11的加载结果）
else 支付失败
s -> queue: 21、发送支付失败邮件
c --> customer: 22、支付失败（步骤11的加载结果）
end
end

else 携带3D验证链接
s --> c: 23、返回3D验证链接
c --> customer: 24、页面Redirect
customer --> bank: 25、3D验证
bank --> ccs: 26、3D验证结果返回

group 验证成功
ccs --> s: 27、回调接口
s --> c: 28、返回该订单的订单号、页面Redirect（加载中，等待后续的支付结果同步）
ccs --> s: 29、支付结果异步通知
s -> s: 30、签名参数验证

group 不通过
s -> s: 31、不予处理
else 通过
s -> s: 32、获取支付结果

group 支付成功
s -> db: 33、修改订单状态、记录支付信息
s -> queue: 34、发送支付成功邮件
c --> customer: 35、支付成功（步骤26的加载结果）
else 支付失败
s -> queue: 36、发送支付失败邮件
c --> customer: 37、支付失败（步骤26的加载结果）
end
end

else 验证失败
ccs --> s: 38、回调接口
s --> c: 39、拼接页面链接路径
c --> customer: 40、支付失败

end
end
end

@enduml
