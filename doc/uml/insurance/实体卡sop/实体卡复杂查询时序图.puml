@startuml
'https://plantuml.com/sequence-diagram

autonumber

'用户
actor       Customer   as customer

'客户端（浏览器）
participant Client  as c

'服务端（服务器）
participant storeServer  as s

'数据库
database    Db      as  db

title 实体卡复杂查询时序图

customer ->c:选择查询参数

c ->s:进行实体卡信息查询

s ->db:通过工单号、卡号、绑定机型、厂商、卡片版本、是否绑定、创建时间、绑定时间、是否激活都可以直接查care_activation_card

s -->s:如果存在激活状态

s ->db:再去查询care_insurance_activation_card，获得过期时间、激活时间等

s ->db:根据cardId查询绑定记录care_card_device_change_record，获取历史绑定设备、和工单号等


s -->s:封装数据

s -->c:返回结果


@enduml