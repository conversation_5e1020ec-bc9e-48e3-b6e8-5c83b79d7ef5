@startuml
'https://plantuml.com/sequence-diagram

autonumber

'用户
actor       用户   as  c

participant 官网服务 as w


participant 商城  as s

'数据库
database    数据库      as db


c ->w:登录后输入序列号查询权益
w ->s:权益查询

group 查询云存订阅权益
s ->db:查询云存订阅权益

end group

group 查询云存实体卡权益
s -->s:设置查询条件
note right
1用户存在实体卡权益
2实体卡权益未过期
3care/延保存在记录且未被使用
end note
s ->db:查询云存实体卡权益

end group

s -->s:判断care&延保权益的存在性

s -->w:返回结果
w -->c:返回结果

c ->w:选择绑定care/延保
w ->s:选择绑定care/延保

s -->s:代金券权益判断和发放

s ->db:查询云存订阅权益
s ->db:查询云存实体卡权益

s -->s:pk权益有效期

s -->s:得到一个未过期的权益

s ->db:查询该权益对应的一对care&延保权益(若有)
note left
实体卡权益要做后续激活所需字段的兼容
1比如expire，可以手动封装
2区分权益来源是线上订阅还是实体卡，便于绑定信息记录
end note

s -->s:进行对应的care/延保激活流程(已有)
note left
包括前置校验和保险激活等，已有逻辑
end note

s -->s:校验设备是否超期
group 设备超期
s -->w:结束，标记为实体卡不发放代金券
else 设备未超期
s ->db:权益绑定记录
s -->w:激活结束
end group


@enduml