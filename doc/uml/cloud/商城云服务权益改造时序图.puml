@startuml
'https://plantuml.com/sequence-diagram



'用户
actor       用户   as customer

'客户端（浏览器）
participant 客户端  as c

'服务端（服务器）
participant 商城服务  as s

'数据库
database    数据库      as db

customer -> c : 1、check out

c -> s: 2、/store/trade/order/create (购买商品含配件下单)

group 是否订阅用户
    group 是否年包
            group 配件八折权益未过期 && 剩余可使用额度 > 0
                s -> s: 3、配件商品使用‘配件八折’权益
            else
                s -> s: 3、‘配件八折’权益不可用
            end
    else
        s -> s: 3、‘配件八折’权益不可用
    end
else
    s -> s: 3、‘配件八折’权益不可用
end

s -> s: 4、创建订单

s -> db: 5、订单保存

db --> s: 6、响应保存信息

s --> c: 响应订单信息(订单号等等)

@enduml