@startuml
'https://plantuml.com/sequence-diagram

autonumber
'用户
actor       用户   as customer

'客户端（浏览器）
participant 浏览器  as c

'服务端（服务器）
participant 订单模块  as s

'服务端（服务器）
participant 支付模块  as pay

'数据库
database    数据库      as db

queue 队列 as q

participant 云存储 as cl

participant 飞书 as f

participant 官网 as user


customer -[#red]> c: place order

c -[#red]> s: 调用订单创建api

s -[#red]> s: 云服务订单检查

group 是否注册用户？
s -[#red]> cl: 调用云存储查询用户订阅记录API
cl --[#red]> s: 返回用户订阅记录
    group 是否订阅？
        group 是否过期？
            s -[#red]> s: 订阅用户权益已过期场景-创建订单
        else 未过期
            s --[#red]> c: 订阅用户权益未过期场景，阻断下单
        end
    else 未订阅
        s -[#red]> s: 注册用户未订阅云服务场景-创建订单
    end
else 非注册用户
    s -[#red]> s: 访客下单，查询该邮箱是否已存在'已支付'的云服务订单
    group 存在'已支付'的云服务订单
        s --[#red]> c: 访客下单，访客邮箱已存在'已支付'的云服务订单场景，阻断下单
    else 不存在'已支付'的云服务订单
        s -[#red]> s: 访客下单场景 - 创建订单
    end
end

s -[#red]> s: 封装订单数据并标记为'云服务'订单
s -[#red]> db: 保存订单、用户订阅支付卡信息等等

s -[#red]> q: 发送订单创建事件通知
q --[#red]> s: 消费订单创建事件消息
s -[#red]> s: 处理订单创建各类衍生事件
s -[#red]> cl: 发送预订阅通知（RPC）
s -[#red]> q: 发送取消云服务订单事件通知
q --[#red]> s: 取消当前用户/访客前N笔'未支付'的云服务订单
s -[#red]> s: 是否游客订单
    group 是
        s -[#red]> s: 取消以该游客邮箱下的所有'未支付'的云服务订单
    else 否
        s -[#red]> s: 取消该用户下的所有'未支付'的云服务订单
    end

s -[#red]> pay: 调用支付

pay -[#red]> pay: 处理支付数据

pay --[#red]> s: 支付响应

s -[#red]> db: 保存订单支付信息
s -[#red]> q: 发送订单支付成功事件通知

group 云服务订单?
    q -[#red]> q: 发送云服务订阅记录初始化事件通知
    q --[#red]> s: 消费云服务订阅记录初始化事件通知
    s -[#red]> user: 查询当前下单邮箱注册信息
    group 已注册
       s -[#red]> s: 初始化用户'已支付'订单的云服务订阅记录——提前落user_id

       s -[#red]> q: 发送重复订阅检查事件通知(需延迟消费)
       q --[#red]> s: 消费重复订阅检查事件通知

       s -[#red]> s: 检查当前用户是否存在多条'已生效'的订阅记录
       group 是
           s -[#red]> f: 飞书预警，提醒相关人员处理重复支付的订单并人工退款
       else 否
           s -[#red]> cl: 推送云存储权益下发所需数据 （RPC）
       end

    else 未注册
        s -[#red]> s: 初始化访客'已支付'订单的云服务订阅记录——暂时不落user_id,等待该邮箱注册通知
    end
end
@enduml