@startuml
'https://plantuml.com/sequence-diagram



'用户
actor       Customer   as customer

'客户端（浏览器）
participant Client  as c

'服务端（服务器）
participant Server  as s

'服务端（服务器）
participant Ava<PERSON><PERSON>  as a

'缓存服务器
database    cache   as  redis

'数据库
database    Db   as  db

title 下单前税费计算时序图


customer -[#red]> c: 1、Checkout
|||
c -[#red]> s: 2、默认地址发起计税（前端是否也会判定是否US地区订单才触发调用？）携带uuid
|||
s -[#red]> s: 3、是否携带分销码且绑定优惠券
group YES
    s -[#red]> s: 4、下单商品各套餐优惠总金额及订单优惠总金额计算
end
group US订单？
s -[#red]> s: 5、下单商品套餐子项拆分
s -[#red]> s: 6、拆分后的子项单价、优惠金额计算
s -[#red]> s: 7、封装 avalara CreateTransaction Api Request
|||
s -[#red]> a: 8、发起计税post请求
group 请求成功
a --[#red]> s: 9、 avalara Response 响应
s -[#red]> s: 10、解析avalara Response响应并封装子项税费、税率信息
else 请求失败
    loop 发起计税重试 3次？
            group 重试成功
                a --[#red]> s: 9、 avalara Response 响应
                s -[#red]> s: 10、解析avalara Response响应并封装子项税费、税率信息
            else 重试失败
                s -[#red]> db: 9、获取拆分后的子项套餐在当前地区的离线税率
                db --[#red]> s: 10、 返回子项套餐离线税率
                s -[#red]> s: 11、根据离线税率计算拆分后的子项套餐税费并封装成计税结果
            end
        end
end
|||
s -[#red]> s: 11、使用携带的uuid封装成缓存key
s -[#red]> redis: 12、将该地址初次调用计税结果放入缓存，指定过期时间
s -[#red]> s: 13、计算税费
s --[#red]> c: 14、 返回计税结果（税费、地址ID、UUID）
else NO
s --[#red]> c: 5、返回计税结果（0税费）
end



@enduml