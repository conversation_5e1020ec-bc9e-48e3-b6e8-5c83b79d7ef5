@startuml
'https://plantuml.com/sequence-diagram

autonumber

'用户
actor   customer    as   customer

'客户端（浏览器）
participant client  as  c

'服务端（服务器）
participant Server  as  s

'数据库
database    db      as db

'缓存服务器
database    cache   as  redis

'attentive
participant attentive

customer ->c:填写电话号进行订阅

c ->s:上报数据（接口A）

s -->c:数据传到服务端成功

s -->db:通过电话号和上报attentive成功字段查询数据
note right
select * from email_subscribe
where phone =xx and subscribe_status = true
得到一个list（包括在其他弹窗订阅的数据）
end note

s -->s:判断

s ->db:通过电话号和activityPage查询数据

group 重复订阅

s -->s:判断用户是否确认订阅（回复yes）

group 用户确认订阅
s -->s:结束流程(已经订阅过了)

else  用户没有确认订阅

s ->attentive:调接口查询用户是否回复yes

group 回复yes

s ->db:更新数据（标记用户已经回复过yes了，更新时间）

s -->s:结束流程

else 没有回复yes

s ->db:更新数据（更新下创建时间）
s -->s:结束流程
end group

else 没有重复订阅

s ->db:订阅数据落库

s ->attentive:订阅用户数据上报

s ->attentive:用户兴趣上报（标记大促预热）

attentive -->s:全部返回成功，修改数据库的订阅字段（上报成功，回复yes标记为未回复）

s -->s:结束流程

end group
end group



c ->s:请求接口B（仍需携带电话和activityPage） 确认订阅结果
s ->db:查询订阅数据
s -->c:查不到数据，返回还在处理中
s -->c:查到数据，订阅字段未标记为已上报，返回还在处理中
s -->c:查到数据，回复yes字段标记为已经回复了，返回重复订阅
s -->c:查到数据，回复yes字段标记为未回复+更新时间在五分钟内，返回订阅成功



@enduml