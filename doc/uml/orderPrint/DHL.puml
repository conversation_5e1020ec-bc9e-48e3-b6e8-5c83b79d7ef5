@startuml
'https://plantuml.com/sequence-diagram

autonumber

'用户
actor       Customer   as customer

'客户端（浏览器）
participant Client  as c

'服务端（服务器）
participant Server  as s

'DHL平台
participant DHL as d

participant OSS as o

'数据库
database    Db      as db

customer -> c: 批量上传订单号
c -> s: 同步调用DHL报关批量导入接口
s -> s: 订单数据常规检查
group 通过
group 异常捕获
s -> s: 循环构建DHL API请求参数
s -> d: http循环调用DHL API进行运单数据上传
d --> s: DHL API响应
s -> s: 解析Response响应参数
s -> s: 将返回的三类Base64编码流的文件图片字符串进行转图片并上传至临时文件夹
s -> o: 循环结束，将文件夹中的所有文件进行压缩并上传至OSS
o --> s: 返回OSS文件下载链接
s -> db: 保存OSS文件下载链接
else 有异常
s -> s: 记录上传DHL失败订单号，并发送飞书告警
end

else  不通过
s --> c: 返回不通过原因
end
@enduml