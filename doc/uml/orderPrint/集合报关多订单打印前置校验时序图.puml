@startuml
'https://plantuml.com/sequence-diagram

'用户
actor       customer   as customer

'客户端（浏览器）
participant Client  as c

'服务端（服务器）
participant Server  as s

'数据库
database    db      as db

title 集合报关多订单打印前置校验时序图

customer -> c: 0.选择打印订单类型

c -> s:1.订单打印请求

s -> s:2.参数校验
group 不通过
s -> s: 3.抛异常，终止流程
s -->c: 4.返回异常
else 通过
s -> db:5.遍历查询订单
s -> s:6.校验订单类型
group 不通过
s -->c:7.返回异常
note right
出现这一步的原因是只允许相同类型批次订单（商城paypal、商城信用卡、乐天订单、Lazada订单）
end note
else 通过
s -> s: 8.校验国家地区是否一致
group 不通过
s --> c: 9.返回异常
else 通过

s -> s:10.记录所有订单
note right
用于后续的订单层面数据统计
end note
s -> db:11.遍历查询订单子项
s -> s:12.分割每个订单子项
note right
针对组合套餐，分割成最细套餐
end note
s -> s:13.统计并合并相同子项价格&数量
s -> s:14.统计计算订单层面数据
s -> s:15.校验子项种类数量
group 不通过
s -->c:16.返回异常
else 通过
s -> s:17.异步处理
note right
防止订单量过大导致接口超时问题
end note

end
end
end
end
@enduml