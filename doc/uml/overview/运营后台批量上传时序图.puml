@startuml
'https://plantuml.com/sequence-diagram

autonumber

'用户
actor       customer   as customer

'客户端（浏览器）
participant Client  as c

'服务端（服务器）
participant Server  as s

'数据库
database    db      as db

title 运营后台批量上传文案时序图

customer ->c:批量上传文件

c ->s:上传Excel文件

group 文件上传校验

s -->s:解析文件
s-->s:开始逐行读取文件
group 没有使用正确的文案导入的模板格式
s -->c:抛出异常
end
group 如果是移动端的模版，只能包含 UD1 和 LOGO 两种类型

s -->c:抛出异常
end
group 关键参数判空（productId、templateKey）
s -->c:抛出异常
end
group 产品id是否存在
s -->c:抛出异常
end
s-->s:全部文件读取完
s ->db:查询是否存在overview配置
group 产品已经配置过模块化overview
group 标识第一次
s -->c:抛出异常，等待用户确认

else 标识:确认
c ->s:通过校验，继续上传
end
end

group 是否包含两个及以上的产品ID
group 标识第一次
s -->c:抛出异常，等待用户确认

else 标识:确认
c ->s:通过校验，继续上传
end
end

group 没有包含当前产品id
group 标识第一次
s -->c:抛出异常，等待用户确认

else 标识:确认
c ->s:通过校验，继续上传
end
end

end

s -->s:封装对象

s->db:导入数据

s -->c:显示产品id对应的配置信息
@enduml