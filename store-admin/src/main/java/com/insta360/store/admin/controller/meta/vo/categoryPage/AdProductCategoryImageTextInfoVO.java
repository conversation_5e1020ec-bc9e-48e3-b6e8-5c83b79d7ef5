package com.insta360.store.admin.controller.meta.vo.categoryPage;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategoryImageTextInfo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
public class AdProductCategoryImageTextInfoVO implements Serializable {

    private Integer id;

    /**
     * 图文筛选器id
     */
    private Integer selectorId;

    /**
     * 筛选器名称
     */
    private String imageTextName;

    /**
     * 语言
     */
    private String language;

    /**
     * 地区
     */
    private String country;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public AdProductCategoryImageTextInfoVO() {
    }

    public AdProductCategoryImageTextInfoVO(ProductCategoryImageTextInfo imageTextInfo) {
        if (Objects.nonNull(imageTextInfo)) {
            BeanUtil.copyProperties(imageTextInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSelectorId() {
        return selectorId;
    }

    public void setSelectorId(Integer selectorId) {
        this.selectorId = selectorId;
    }

    public String getImageTextName() {
        return imageTextName;
    }

    public void setImageTextName(String imageTextName) {
        this.imageTextName = imageTextName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AdProductCategoryImageTextInfoVO{" +
                "id=" + id +
                ", selectorId=" + selectorId +
                ", imageTextName='" + imageTextName + '\'' +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
