package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.meta.dto.NavigationBarBannerMainDTO;
import com.insta360.store.business.meta.dto.NavigationBarBannerSubsetDTO;
import com.insta360.store.business.meta.model.NavigationBarBannerMain;
import com.insta360.store.business.meta.model.NavigationBarBannerSubset;
import com.insta360.store.business.meta.service.NavigationBarBannerMainService;
import com.insta360.store.business.meta.service.NavigationBarBannerSubsetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/8/30
 * @Description:
 */
@Component
public class AdNavigationBarBannerCachePack {

    @Autowired
    NavigationBarBannerMainService navigationBarBannerMainService;

    @Autowired
    NavigationBarBannerSubsetService navigationBarBannerSubsetService;

    /**
     * 更新导航栏BannerMain
     *
     * @param bannerMain
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void updateBannerMain(NavigationBarBannerMain bannerMain) {
        navigationBarBannerMainService.updateBannerMain(bannerMain);
    }

    /**
     * 删除导航栏BannerMain
     *
     * @param bannerMainId
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void deleteByBannerMainId(Integer bannerMainId) {
        navigationBarBannerMainService.deleteByBannerMainId(bannerMainId);
    }

    /**
     * 启用导航栏BannerMain
     *
     * @param bannerMain
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void enableCategoryInside(NavigationBarBannerMain bannerMain) {
        navigationBarBannerMainService.enableCategoryInside(bannerMain);
    }

    /**
     * 禁用导航栏BannerMain
     *
     * @param bannerMain
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void disableCategoryInside(NavigationBarBannerMain bannerMain) {
        navigationBarBannerMainService.disableCategoryInside(bannerMain);
    }

    /**
     * 更新导航栏BannerMain排序
     *
     * @param barBannerMainParam
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void updateBannerMainIndex(List<NavigationBarBannerMainDTO> barBannerMainParam) {
        navigationBarBannerMainService.updateBannerMainIndex(barBannerMainParam);
    }

    /**
     * 创建导航栏Banner
     *
     * @param barBannerSubsetParam
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public NavigationBarBannerSubset createBannerInfo(NavigationBarBannerSubsetDTO barBannerSubsetParam) {
        return navigationBarBannerSubsetService.createBannerInfo(barBannerSubsetParam);
    }

    /**
     * 删除导航栏Banner
     *
     * @param subsetId
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void deleteBannerInfo(Integer subsetId) {
        navigationBarBannerSubsetService.deleteBannerInfo(subsetId);
    }

    /**
     * 启用导航栏Banner
     *
     * @param bannerSubset
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void enableBannerInfo(NavigationBarBannerSubset bannerSubset) {
        navigationBarBannerSubsetService.enableBannerInfo(bannerSubset);
    }

    /**
     * 禁用导航栏Banner
     *
     * @param bannerSubset
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void disableBannerInfo(NavigationBarBannerSubset bannerSubset) {
        navigationBarBannerSubsetService.disableBannerInfo(bannerSubset);
    }

    /**
     * 更新导航栏Banner
     *
     * @param barBannerSubsetParam
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void updateBannerInfo(NavigationBarBannerSubsetDTO barBannerSubsetParam) {
        navigationBarBannerSubsetService.updateBannerInfo(barBannerSubsetParam);
    }
}
