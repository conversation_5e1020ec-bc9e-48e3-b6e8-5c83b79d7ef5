package com.insta360.store.admin.controller.order.controller;

import com.alibaba.excel.annotation.ExcelProperty;
import com.insta360.store.business.meta.model.MisBehaviorNames;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/07/01
 */
public class MisBehaviorData {

    @ExcelProperty("邮箱")
    private String email;

    @ExcelProperty("姓")
    private String lastName;

    @ExcelProperty("名")
    private String firstName;

    @ExcelProperty("区号")
    private String areaCode;

    @ExcelProperty("电话")
    private String phone;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public MisBehaviorNames addUpdateTime() {
        LocalDateTime now = LocalDateTime.now();
        MisBehaviorNames misBehaviorNames = new MisBehaviorNames();
        misBehaviorNames.setEmail(email);
        misBehaviorNames.setLastName(lastName);
        misBehaviorNames.setFirstName(firstName);
        misBehaviorNames.setAreaCode(areaCode);
        misBehaviorNames.setPhone(phone);
        misBehaviorNames.setCreateTime(now);
        misBehaviorNames.setUpdateTime(now);
        return misBehaviorNames;
    }
}
