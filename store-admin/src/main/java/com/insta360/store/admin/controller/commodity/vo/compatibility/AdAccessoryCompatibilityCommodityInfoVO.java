package com.insta360.store.admin.controller.commodity.vo.compatibility;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.model.AccessoryCompatibilityCommodityInfo;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2022/03/12
 * @Description:
 */
public class AdAccessoryCompatibilityCommodityInfoVO implements Serializable {

    private Integer id;

    /**
     * 配件套餐id（accessory_compatibility_commodity_group的id）
     */
    private Integer groupId;

    /**
     * 语言
     */
    private String language;

    /**
     * 配件名称
     */
    private String accessoryName;

    /**
     * 配件说明
     */
    private String accessoryContext;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public AdAccessoryCompatibilityCommodityInfoVO() {
    }

    public AdAccessoryCompatibilityCommodityInfoVO(AccessoryCompatibilityCommodityInfo accessoryCompatibilityCommodityInfo) {
        if (accessoryCompatibilityCommodityInfo != null) {
            BeanUtil.copyProperties(accessoryCompatibilityCommodityInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getAccessoryName() {
        return accessoryName;
    }

    public void setAccessoryName(String accessoryName) {
        this.accessoryName = accessoryName;
    }

    public String getAccessoryContext() {
        return accessoryContext;
    }

    public void setAccessoryContext(String accessoryContext) {
        this.accessoryContext = accessoryContext;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AdAccessoryCompatibilityCommodityInfoVO{" +
                "id=" + id +
                ", groupId=" + groupId +
                ", language='" + language + '\'' +
                ", accessoryName='" + accessoryName + '\'' +
                ", accessoryContext='" + accessoryContext + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
