package com.insta360.store.admin.rpc.discount;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.discount.dto.DiscountModelCheckDTO;
import com.insta360.store.business.discount.dto.DiscountModelQueryDTO;
import com.insta360.store.business.discount.dto.GiftCardOpenPlatformCreateDTO;
import com.insta360.store.business.discount.dto.ao.BatchCreateDiscountCommonAO;
import com.insta360.store.business.discount.dto.ro.BatchCreateCommonDiscountRO;
import com.insta360.store.business.discount.dto.ro.DiscountModelCheckRO;
import com.insta360.store.business.discount.dto.ro.DiscountModelRO;
import com.insta360.store.business.discount.dto.ro.DiscountOldModelRO;
import com.insta360.store.business.discount.enums.DiscountModel;
import com.insta360.store.business.discount.enums.PlatformSourceType;
import com.insta360.store.business.discount.exception.StoreDiscountErrorCode;
import com.insta360.store.business.discount.model.GiftCardTemplate;
import com.insta360.store.business.discount.provider.DiscountBusinessProvider;
import com.insta360.store.business.discount.service.DiscountWritService;
import com.insta360.store.business.discount.service.GiftCardTemplateService;
import com.insta360.store.business.user.service.impl.helper.StoreTokenHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2021/05/13
 * @Description:
 */
@RestController
public class DiscountServiceApi {

    @Autowired
    GiftCardTemplateService giftCardTemplateService;

    @Autowired
    DiscountWritService discountWritService;

    @Autowired
    StoreTokenHelper storeTokenHelper;

    @Autowired
    DiscountBusinessProvider discountBusinessProvider;

    /**
     * 从模板复制代金券（废弃）
     * 使用jwt token操作，token 解密后包含
     * {template_code} : 模板代金券码
     * {bind_user} : 生成到某用户的邮箱中
     * {remark} : 自定义备注
     */
    @GetMapping("/rpc/admin/service/discount/generateByTemplate")
    public Response<String> generateByTemplate(@RequestParam(required = false, value = "token") String token) {
        // 校验token，解析出变量
        String templateCode = (String) storeTokenHelper.verify(token, "template_code");
        String bindEmail = (String) storeTokenHelper.verify(token, "bind_user");
        String remark = (String) storeTokenHelper.verify(token, "remark");

        // 校验代金券模板
        GiftCardTemplate giftCardTemplate = giftCardTemplateService.getByCode(templateCode);
        if (Objects.isNull(giftCardTemplate) || !giftCardTemplate.effectTimeCheck()) {
            return Response.failed();
        }
        //构建代金券生成请求参数
        BatchCreateDiscountCommonAO batchCreateGiftCardAo = new BatchCreateDiscountCommonAO();
        batchCreateGiftCardAo.setTemplateCode(giftCardTemplate.getTemplateCode());
        batchCreateGiftCardAo.setBindEmail(bindEmail);
        batchCreateGiftCardAo.setRemark(remark);
        batchCreateGiftCardAo.setDiscountModel(DiscountModel.GIFT_CODE.code);
        batchCreateGiftCardAo.setPlatformSource(PlatformSourceType.TEMPLATE.code);

        String giftCardCode = null;
        BatchCreateCommonDiscountRO resp = discountWritService.batchCreate(batchCreateGiftCardAo);
        if(Objects.nonNull(resp)) {
            giftCardCode = resp.getCodeList().stream().findFirst().get();
        }
        return Response.ok(giftCardCode);
    }

    /**
     * 根据模版生成代金券
     * @param giftCardOpenPlatformCreateDto
     * @return
     */
    @PostMapping(path = "/rpc/admin/service/discount/generateGiftCard")
    public Response<String> generateGiftCard(@Validated @RequestBody GiftCardOpenPlatformCreateDTO giftCardOpenPlatformCreateDto) {
        if(Objects.isNull(giftCardOpenPlatformCreateDto)) {
            throw new InstaException(StoreDiscountErrorCode.ILLEGAL_PARAMETER_EXCEPTION);
        }
        String templateCode = giftCardOpenPlatformCreateDto.getTemplateCode();
        // 校验代金券模板
        GiftCardTemplate giftCardTemplate = giftCardTemplateService.getByCode(templateCode);
        if (Objects.isNull(giftCardTemplate)) {
            throw new InstaException(StoreDiscountErrorCode.NOT_EXIST_GIFT_CARD_TEMPLATE);
        }
        if(!giftCardTemplate.effectTimeCheck()) {
            throw new InstaException(StoreDiscountErrorCode.INVALID_GIFT_CARD_TEMPLATE_EXCEPTION);
        }
        //构建代金券生成请求参数
        BatchCreateDiscountCommonAO batchCreateGiftCardAo = new BatchCreateDiscountCommonAO();
        batchCreateGiftCardAo.setTemplateCode(giftCardTemplate.getTemplateCode());
        batchCreateGiftCardAo.setBindEmail(giftCardOpenPlatformCreateDto.getBindEmail());
        batchCreateGiftCardAo.setRemark(giftCardOpenPlatformCreateDto.getRemark());
        batchCreateGiftCardAo.setDiscountModel(DiscountModel.GIFT_CODE.code);
        batchCreateGiftCardAo.setPlatformSource(PlatformSourceType.TEMPLATE.code);

        String giftCardCode = null;
        BatchCreateCommonDiscountRO resp = discountWritService.batchCreate(batchCreateGiftCardAo);
        if(Objects.nonNull(resp)) {
            giftCardCode = resp.getCodeList().stream().findFirst().get();
        }
        return Response.ok(giftCardCode);
    }

    /**
     * 查询优惠模型
     *
     * @param tradeCodeCheckDTO
     * @return
     */
    @PostMapping(path = "/rpc/store/admin/service/discount/queryDiscountModel")
    public Response<List<DiscountOldModelRO>> queryDiscountModel(@Validated @RequestBody DiscountModelQueryDTO tradeCodeCheckDTO) {
        List<DiscountOldModelRO> list = discountBusinessProvider.queryDiscountModelOld(tradeCodeCheckDTO);
        return Response.ok(list);
    }

    /**
     * 查询优惠模型
     *
     * @param tradeCodeCheckDTO
     * @return
     */
    @PostMapping(path = "/rpc/store/admin/service/discount/queryDiscountModelNew")
    public Response<DiscountModelRO> queryDiscountModelNew(@Validated @RequestBody DiscountModelQueryDTO tradeCodeCheckDTO) {
        DiscountModelRO discountModelRO = discountBusinessProvider.queryDiscountModel(tradeCodeCheckDTO);
        return Response.ok(discountModelRO);
    }

    /**
     * 优惠模型检查
     *
     * @param discountModelCheckDTO
     * @return
     */
    @PostMapping(path = "/rpc/store/admin/service/discount/discountCodeCheck")
    public Response<DiscountModelCheckRO> discountCodeCheck(@Validated @RequestBody DiscountModelCheckDTO discountModelCheckDTO) {

        DiscountModelCheckRO discountModelCheck = discountBusinessProvider.checkSpecifyTradeCode(discountModelCheckDTO);
        return Response.ok(discountModelCheck);
    }
}
