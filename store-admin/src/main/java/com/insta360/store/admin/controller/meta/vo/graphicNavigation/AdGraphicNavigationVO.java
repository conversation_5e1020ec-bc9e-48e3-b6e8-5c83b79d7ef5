package com.insta360.store.admin.controller.meta.vo.graphicNavigation;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.GraphicNavigation;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/3/9
 * @Description:
 */
public class AdGraphicNavigationVO implements Serializable {

    private Integer id;

    /**
     * 导航类型
     */
    private String navigationType;

    /**
     * 入口类型 1（pc），2（mobile）
     */
    private Integer endpoint;

    /**
     * 图文导航配置
     */
    List<AdGraphicNavigationMainVO> graphicNavigationMains;

    public AdGraphicNavigationVO() {
    }

    public AdGraphicNavigationVO(GraphicNavigation graphicNavigation) {
        if (Objects.nonNull(graphicNavigation)) {
            BeanUtil.copyProperties(graphicNavigation, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNavigationType() {
        return navigationType;
    }

    public void setNavigationType(String navigationType) {
        this.navigationType = navigationType;
    }

    public Integer getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(Integer endpoint) {
        this.endpoint = endpoint;
    }

    public List<AdGraphicNavigationMainVO> getGraphicNavigationMains() {
        return graphicNavigationMains;
    }

    public void setGraphicNavigationMains(List<AdGraphicNavigationMainVO> graphicNavigationMains) {
        this.graphicNavigationMains = graphicNavigationMains;
    }
}
