package com.insta360.store.admin.controller.meta.vo.livebroadcast;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.LiveBroadcastConfigInfo;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/10/21
 * @Description:
 */
public class LiveBroadcastInfoVO implements Serializable {

    /**
     * 地区
     */
    private String country;

    /**
     * 语言
     */
    private String language;

    /**
     * 标题
     */
    private String title;

    public LiveBroadcastInfoVO(LiveBroadcastConfigInfo broadcastConfigInfo) {
        if (broadcastConfigInfo != null) {
            BeanUtil.copyProperties(broadcastConfigInfo, this);
        }
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
