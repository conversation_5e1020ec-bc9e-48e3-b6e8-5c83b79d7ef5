package com.insta360.store.admin.controller.test.wxq.active.entity;

import com.insta360.store.business.meta.enums.ActivityDynamicParamEnum;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public class ActivityDynamicParam {

    /**
     * id
     */
    private Integer id;

    /**
     * 活动ID
     */
    private Integer activityId;

    /**
     * 组件ID
     */
    private Integer componentId;

    /**
     * 动态参数ID
     */
    private Integer paramId;

    /**
     * ID 所属类型
     */
    private ActivityDynamicParamEnum paramType;

    /**
     * 活动创建时间
     */
    private LocalDateTime createTime;

    /**
     * 活动更新时间
     */
    private LocalDateTime updateTime;

}
