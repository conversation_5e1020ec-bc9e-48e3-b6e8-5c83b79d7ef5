package com.insta360.store.admin.controller.meta.vo.categoryPage;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
public class AdProductCategoryImageTextCommodityVO implements Serializable {

    private Integer id;

    /**
     * 图文筛选器id
     */
    private Integer selectorId;

    /**
     * 类目key
     */
    private String categoryKey;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSelectorId() {
        return selectorId;
    }

    public void setSelectorId(Integer selectorId) {
        this.selectorId = selectorId;
    }

    public String getCategoryKey() {
        return categoryKey;
    }

    public void setCategoryKey(String categoryKey) {
        this.categoryKey = categoryKey;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AdProductCategoryImageTextCommodityVO{" +
                "id=" + id +
                ", selectorId=" + selectorId +
                ", categoryKey='" + categoryKey + '\'' +
                ", commodityId=" + commodityId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
