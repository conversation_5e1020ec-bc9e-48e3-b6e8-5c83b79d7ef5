package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.model.AdapterTypeInfo;
import com.insta360.store.business.meta.model.AdapterTypeMain;
import com.insta360.store.business.meta.service.AdapterTypeInfoService;
import com.insta360.store.business.meta.service.AdapterTypeMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2022/8/30
 * @Description:
 */
@Component
public class AdMetaAdapterTypeCachePack {

    @Autowired
    AdapterTypeInfoService adapterTypeInfoService;

    @Autowired
    AdapterTypeMainService adapterTypeMainService;

    /**
     * 更新 adapter type info
     *
     * @param cachePutKeyParameter
     * @param adapterTypeInfo
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_INFO)
    public void updateAdapterTypeInfo(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeInfo adapterTypeInfo) {
        adapterTypeInfoService.updateAdapterTypeInfo(adapterTypeInfo);
    }

    /**
     * 删除 adapter type info
     *
     * @param cachePutKeyParameter
     * @param adapterTypeInfo
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_INFO)
    public void deleteAdapterTypeInfo(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeInfo adapterTypeInfo) {
        adapterTypeInfoService.deleteAdapterTypeInfo(adapterTypeInfo);
    }

    /**
     * 启用 adapter type info
     *
     * @param cachePutKeyParameter
     * @param adapterTypeInfo
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_INFO)
    public void enableAdapterTypeInfo(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeInfo adapterTypeInfo) {
        adapterTypeInfoService.enableAdapterTypeInfo(adapterTypeInfo);
    }

    /**
     * 禁用 adapter type info
     *
     * @param cachePutKeyParameter
     * @param adapterTypeInfo
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_INFO)
    public void disableAdapterTypeInfo(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeInfo adapterTypeInfo) {
        adapterTypeInfoService.disableAdapterTypeInfo(adapterTypeInfo);
    }

    /****************************************** 多语言分界线 ******************************************/

    /**
     * 新增 adapter type
     *
     * @param adapterTypeMain
     * @return
     */
    public void createAdapterType(AdapterTypeMain adapterTypeMain) {
        adapterTypeMainService.createAdapterType(adapterTypeMain);
    }

    /**
     * 更新 adapter type
     *
     * @param adapterTypeMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_MAIN)
    public void updateAdapterType(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeMain adapterTypeMain) {
        adapterTypeMainService.updateAdapterType(adapterTypeMain);
    }

    /**
     * 删除 adapter type
     *
     * @param adapterTypeMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_MAIN)
    public void removeAdapterType(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeMain adapterTypeMain) {
        adapterTypeMainService.removeAdapterType(adapterTypeMain);
    }

    /**
     * 启用 adapter type
     *
     * @param adapterTypeMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_MAIN)
    public void enableAdapterType(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeMain adapterTypeMain) {
        adapterTypeMainService.enableAdapterType(adapterTypeMain);
    }

    /**
     * 禁用 adapter type
     *
     * @param cachePutKeyParameter
     * @param adapterTypeMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.ADAPTER_TYPE_MAIN)
    public void disableAdapterType(CachePutKeyParameterBO cachePutKeyParameter, AdapterTypeMain adapterTypeMain) {
        adapterTypeMainService.disableAdapterType(adapterTypeMain);
    }
}
