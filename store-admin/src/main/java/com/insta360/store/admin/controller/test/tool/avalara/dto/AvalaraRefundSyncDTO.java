package com.insta360.store.admin.controller.test.tool.avalara.dto;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/1/15
 */
public class AvalaraRefundSyncDTO implements Serializable {

    /**
     * 售后单号列表
     */
    @NotEmpty(message = "售后单号列表不能为空")
    private List<String> rmaOrderNumbers;

    public List<String> getRmaOrderNumbers() {
        return rmaOrderNumbers;
    }

    public void setRmaOrderNumbers(List<String> rmaOrderNumbers) {
        this.rmaOrderNumbers = rmaOrderNumbers;
    }

    @Override
    public String toString() {
        return "AvalaraRefundSyncDTO{" +
                "rmaOrderNumbers=" + rmaOrderNumbers +
                '}';
    }
}
