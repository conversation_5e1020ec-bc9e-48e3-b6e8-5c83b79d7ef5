package com.insta360.store.admin.controller.trade.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.trade.vo.AdCreditCardTypeVO;
import com.insta360.store.business.trade.model.CreditCardType;
import com.insta360.store.business.trade.service.CreditCardTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/12/08
 * @Description:
 */
@RestController
@PermissionResource(code = "creditCardType", desc = "信用卡类型配置")
public class AdCreditCardTypeApi extends BaseAdminApi {

    @Autowired
    CreditCardTypeService creditCardTypeService;

    /**
     * 获取所有的信用卡类型
     *
     * @return
     */
    @LogAttr(desc = "获取所有的信用卡类型", logType = LogType.query)
    @Permission(code = "store.trade.creditCardType.getCreditCardTypes", desc = "获取所有的信用卡类型")
    @GetMapping("/admin/trade/credit/card/getCreditCardTypes")
    public Response<? extends Map> getCreditCardTypes() {
        List<CreditCardType> creditCardTypes = creditCardTypeService.listCreditCardTypes();

        // 数据格式转换
        List<AdCreditCardTypeVO> creditCardTypeVos = creditCardTypes.stream().map(AdCreditCardTypeVO::new).collect(Collectors.toList());
        return Response.ok("creditCardTypes", creditCardTypeVos);
    }
}
