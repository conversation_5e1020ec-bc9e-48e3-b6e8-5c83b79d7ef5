package com.insta360.store.admin.controller.trade.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.trade.model.CreditCardPayRule;

/**
 * @Author: wbt
 * @Date: 2021/11/16
 * @Description:
 */
public class AdCreditCardPayRuleVO {

    private Integer id;

    /**
     * 卡类型
     */
    private String cardTypeName;

    /**
     * 卡类型内部名称
     */
    private String payTypeName;

    /**
     * 钱海支付通道id
     */
    private Integer oceanPayChannelId;

    /**
     * 钱海支付通道
     */
    private String oceanPayChannelName;

    /**
     * checkout支付通道id
     */
    private Integer checkoutPayChannelId;

    /**
     * checkout支付通道
     */
    private String checkoutPayChannelName;

    /**
     * 地区
     */
    private String country;

    /**
     * 钱海支付权重
     */
    private Integer oceanWeight;

    /**
     * cko支付权重
     */
    private Integer checkoutWeight;

    /**
     * 是否启用（0：禁用；1：启用）
     */
    private Boolean enabled;

    public AdCreditCardPayRuleVO(CreditCardPayRule creditCardPayRule) {
        if (creditCardPayRule != null) {
            BeanUtil.copyProperties(creditCardPayRule, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCardTypeName() {
        return cardTypeName;
    }

    public void setCardTypeName(String cardTypeName) {
        this.cardTypeName = cardTypeName;
    }

    public String getPayTypeName() {
        return payTypeName;
    }

    public void setPayTypeName(String payTypeName) {
        this.payTypeName = payTypeName;
    }

    public Integer getOceanPayChannelId() {
        return oceanPayChannelId;
    }

    public void setOceanPayChannelId(Integer oceanPayChannelId) {
        this.oceanPayChannelId = oceanPayChannelId;
    }

    public String getOceanPayChannelName() {
        return oceanPayChannelName;
    }

    public void setOceanPayChannelName(String oceanPayChannelName) {
        this.oceanPayChannelName = oceanPayChannelName;
    }

    public Integer getCheckoutPayChannelId() {
        return checkoutPayChannelId;
    }

    public void setCheckoutPayChannelId(Integer checkoutPayChannelId) {
        this.checkoutPayChannelId = checkoutPayChannelId;
    }

    public String getCheckoutPayChannelName() {
        return checkoutPayChannelName;
    }

    public void setCheckoutPayChannelName(String checkoutPayChannelName) {
        this.checkoutPayChannelName = checkoutPayChannelName;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getOceanWeight() {
        return oceanWeight;
    }

    public void setOceanWeight(Integer oceanWeight) {
        this.oceanWeight = oceanWeight;
    }

    public Integer getCheckoutWeight() {
        return checkoutWeight;
    }

    public void setCheckoutWeight(Integer checkoutWeight) {
        this.checkoutWeight = checkoutWeight;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
        return "AdCreditCardPayRuleVO{" +
                "id=" + id +
                ", cardTypeName='" + cardTypeName + '\'' +
                ", payTypeName='" + payTypeName + '\'' +
                ", oceanPayChannelId=" + oceanPayChannelId +
                ", oceanPayChannelName='" + oceanPayChannelName + '\'' +
                ", checkoutPayChannelId=" + checkoutPayChannelId +
                ", checkoutPayChannelName='" + checkoutPayChannelName + '\'' +
                ", country='" + country + '\'' +
                ", oceanWeight=" + oceanWeight +
                ", checkoutWeight=" + checkoutWeight +
                ", enabled=" + enabled +
                '}';
    }
}
