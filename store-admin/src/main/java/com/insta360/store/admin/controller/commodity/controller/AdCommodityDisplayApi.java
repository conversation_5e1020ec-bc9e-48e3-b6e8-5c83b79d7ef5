package com.insta360.store.admin.controller.commodity.controller;

import com.google.common.collect.Lists;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.commodity.controller.cache.AdCommodityDisplayCachePack;
import com.insta360.store.admin.controller.commodity.format.AdCommodityDisplayPack;
import com.insta360.store.admin.controller.commodity.vo.AdCommodityDisplayVO;
import com.insta360.store.business.commodity.dto.CommodityDisplayDTO;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityDisplayHelper;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @Author: hyc
 * @Date: 2019-05-17
 * @Description: 套餐展示图
 */
@RestController
@PermissionResource(code = "commodityDisplay", desc = "套餐展示图")
public class AdCommodityDisplayApi extends BaseAdminApi {

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    AdCommodityDisplayCachePack adCommodityDisplayCachePack;

    @Autowired
    AdCommodityDisplayPack commodityDisplayPack;

    @Autowired
    CommodityDisplayHelper commodityDisplayHelper;

    /**
     * 获取某个套餐的展示信息
     *
     * @param commodityId
     * @return
     */
    @LogAttr(desc = "获取某个套餐的展示信息", logType = LogType.query)
    @Permission(code = "store.commodity.commodityDisplay.listDisplays", desc = "获取某个套餐的展示信息")
    @GetMapping("/admin/commodity/listDisplays")
    public Response<Object> listDisplays(@RequestParam Integer commodityId,
                                         @RequestParam String country) {
        List<AdCommodityDisplayVO> displayList = commodityDisplayPack.listDisplays(commodityId, country);
        return Response.ok(displayList);
    }

    /**
     * 新增某个套餐的展示信息
     *
     * @param commodityDisplayParam
     * @return
     */
    @LogAttr(desc = "新增某个套餐的展示信息")
    @Permission(code = "store.commodity.commodityDisplay.saveDisplay", desc = "新增某个套餐的展示信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/saveDisplay", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> saveDisplay(@RequestBody CommodityDisplayDTO commodityDisplayParam) {
        if (commodityDisplayParam == null || Objects.isNull(commodityDisplayParam.getCommodity())) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityDisplayParam.getCommodity()));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityDisplayParam.getCommodity()));
        SearchDataChangeContext.set(searchDataChangeParams);

        Integer displayId = commodityDisplayHelper.saveDisplay(commodityDisplayParam);
        return Response.ok(displayId);
    }

    /**
     * 获取转码结果
     *
     * @param displayId
     * @return
     */
    @LogAttr(desc = "获取转码结果")
    @Permission(code = "store.commodity.commodityDisplay.getTranscodeResult", desc = "获取转码结果")
    @GetMapping(path = "/admin/commodity/getTranscodeResult")
    public Response<Object> getTranscodeResult(Integer displayId) {
        CommodityDisplay commodityDisplay = commodityDisplayService.getById(displayId);
        if (Objects.isNull(commodityDisplay)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 校验转码结果
        commodityDisplayPack.checkTranscodeResult(commodityDisplay);
        return Response.ok(commodityDisplay);
    }


    /**
     * 修改某个套餐的展示信息
     *
     * @param commodityDisplayParam
     * @return
     */
    @LogAttr(desc = "修改某个套餐的展示信息")
    @Permission(code = "store.commodity.commodityDisplay.modifyDisplay", desc = "修改某个套餐的展示信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/modifyDisplay", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> modifyDisplay(@RequestBody CommodityDisplayDTO commodityDisplayParam) {
        if (commodityDisplayParam == null || commodityDisplayParam.getCommodity() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityDisplayParam.getCommodity()));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityDisplayParam.getCommodity()));
        SearchDataChangeContext.set(searchDataChangeParams);

        adCommodityDisplayCachePack.modifyDisplay(cachePutKeyParameter, commodityDisplayParam);
        return Response.ok();
    }

    /**
     * 删除某个套餐的展示信息
     *
     * @param commodityDisplayParam
     * @return
     */
    @LogAttr(desc = "删除某个套餐的展示信息")
    @Permission(code = "store.commodity.commodityDisplay.removeDisplay", desc = "删除某个套餐的展示信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/removeDisplay", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> removeDisplay(@RequestBody CommodityDisplayDTO commodityDisplayParam) {
        CommodityDisplay commodityDisplay = commodityDisplayService.getById(commodityDisplayParam.getId());
        if (Objects.isNull(commodityDisplay)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityDisplay.getCommodity()));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityDisplay.getCommodity()));
        SearchDataChangeContext.set(searchDataChangeParams);

        adCommodityDisplayCachePack.removeDisplay(cachePutKeyParameter, commodityDisplayParam.getId());
        return Response.ok();
    }

}
