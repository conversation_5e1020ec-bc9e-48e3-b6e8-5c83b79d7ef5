package com.insta360.store.admin.controller.test.tool.cloud;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.admin.controller.test.tool.cloud.bo.CloudSubscribeCreateTestBO;
import com.insta360.store.business.cloud.dto.BenefitChangeDTO;
import com.insta360.store.business.cloud.dto.CloudSubscribeNotifyDTO;
import com.insta360.store.business.cloud.dto.CloudSubscribeRenewMqDTO;
import com.insta360.store.business.cloud.enums.SubscribeSubStatus;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSkuService;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.impl.helper.CloudServiceSubscribeEngineHelper;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.cloud.helper.StoreCloudProductMessageSendHelper;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/20
 */
@RestController
public class StoreCloudTestApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudTestApi.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_change_notify, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender;

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_callback_notify, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender2;

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_renew_deduction, messageType = MessageSenderType.time)
    RocketTcpMessageSender rocketTcpMessageSender3;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    CloudServiceSubscribeEngineHelper cloudServiceSubscribeEngineHelper;

    @Autowired
    StoreCloudProductMessageSendHelper storeCloudProductMessageSendHelper;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_order_payed, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender4;

    @PostMapping(path = "/admin/tool/cloud/appChannelBenefitChangeNotify")
    public Response<Object> appChannelBenefitChangeNotify(@RequestBody BenefitChangeDTO benefitChangeParam) {
        String messageId = rocketTcpMessageSender.sendMessage(JSON.toJSONString(benefitChangeParam));
        return Response.ok(messageId);
    }

    @PostMapping(path = "/admin/tool/cloud/storeCloudSubscribeCallbackNotify")
    public Response<Object> storeCloudSubscribeCallbackNotify(@RequestBody CloudSubscribeNotifyDTO cloudSubscribeNotifyDto) {
        String messageId = rocketTcpMessageSender2.sendMessage(JSON.toJSONString(cloudSubscribeNotifyDto));
        return Response.ok(messageId);
    }

    /**
     * 测试续订邮件发送
     */
    @GetMapping("/admin/tool/cloud/renewEmailSend")
    public Response<Object> renewEmailSend(@RequestParam String orderNumber) {
        if (StringUtils.isBlank(orderNumber)) {
            return Response.failed("参数异常");
        }

        CloudStorageSubscribe cloudStorageSubscribe = cloudStorageSubscribeService.getByOrderNumber(orderNumber);
        if (Objects.isNull(cloudStorageSubscribe) || !cloudStorageSubscribe.isSubscribing()) {
            return Response.failed("订阅不存在或已过期");
        }

        // 发送续订邮件
        cloudServiceSubscribeEngineHelper.renewalEmailReminder(cloudStorageSubscribe);

        return Response.ok();
    }

    /**
     * 测试续订
     */
    @GetMapping("/admin/tool/cloud/renew")
    public Response<Object> renew(@RequestParam String orderNumber) {
        if (StringUtils.isBlank(orderNumber)) {
            return Response.failed("参数异常");
        }

        CloudStorageSubscribe cloudStorageSubscribe = cloudStorageSubscribeService.getByOrderNumber(orderNumber);
        if (Objects.isNull(cloudStorageSubscribe) || !cloudStorageSubscribe.isSubscribing()) {
            return Response.failed("订阅不存在或已过期");
        }

        cloudStorageSubscribe.setSubscribeSubState(SubscribeSubStatus.RENEW_DEDUCTION_HANDLE_ING.getCode());
        cloudStorageSubscribeService.updateCloudSubscribe(cloudStorageSubscribe);

        // 创建消息DTO并设置云服务订阅信息
        CloudSubscribeRenewMqDTO cloudSubscribeRenewMqDto = new CloudSubscribeRenewMqDTO();
        cloudSubscribeRenewMqDto.setCloudStorageSubscribe(cloudStorageSubscribe);
        // 发送延迟消息并获取消息ID
        String messageId = rocketTcpMessageSender3.sendDelayMessage(JSON.toJSONString(cloudSubscribeRenewMqDto), 60000L);

        return Response.ok(messageId);
    }

    @PostMapping("/admin/tool/cloud/createCloudSubscribe")
    public Response<Object> createCloudSubscribe(@RequestBody CloudSubscribeCreateTestBO cloudSubscribeCreateTest) {

        if (Objects.isNull(cloudSubscribeCreateTest) || CollectionUtils.isEmpty(cloudSubscribeCreateTest.getOrderNumbers())) {
            return Response.failed();
        }

        List<String> orderNumbers = cloudSubscribeCreateTest.getOrderNumbers();
        for (String orderNumber : orderNumbers) {
            Order order = orderService.getByOrderNumber(orderNumber);
            if (Objects.isNull(order)) {
                continue;
            }
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (Objects.isNull(orderPayment)) {
                continue;
            }

            // 云服务订单订阅记录场景通知
            storeCloudProductMessageSendHelper.sendCloudProductSubscribeCreateMessage(order, orderPayment);
        }

        return Response.ok();
    }

    @PostMapping("/admin/tool/cloud/orderPayedSend")
    public Response<Object> orderPayedSend(@RequestBody CloudSubscribeCreateTestBO cloudSubscribeCreateTest) {

        if (Objects.isNull(cloudSubscribeCreateTest) || CollectionUtils.isEmpty(cloudSubscribeCreateTest.getOrderNumbers())) {
            return Response.failed();
        }

        List<String> orderNumbers = cloudSubscribeCreateTest.getOrderNumbers();
        for (String orderNumber : orderNumbers) {
            Order order = orderService.getByOrderNumber(orderNumber);
            if (Objects.isNull(order)) {
                continue;
            }

            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (Objects.isNull(orderPayment)) {
                continue;
            }

            LOGGER.info("send order payed message. pending... order_number:{}", order.getOrderNumber());

            OrderMessageDTO orderMessage = new OrderMessageDTO();
            orderMessage.setOrder(order);
            orderMessage.setOrderPayment(orderPayment);
            String messageId = rocketTcpMessageSender4.sendMessage(JSONObject.toJSONString(orderMessage));
            MqUtils.isBlankMessageIdHandle(messageId, this, orderMessage);

            LOGGER.info("send order payed message. success... order_number:{}", order.getOrderNumber());

        }

        return Response.ok();
    }
}
