package com.insta360.store.admin.controller.meta.vo.categoryPage;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategoryImageTextSelector;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
public class AdProductCategoryImageTextSelectorVO implements Serializable {

    private Integer id;

    /**
     * 内部名称
     */
    private String insideName;

    /**
     * 图文链接
     */
    private String imageLink;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 默认展示
     */
    private Boolean defaultDisplay;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 图片筛选器多语言
     */
    List<AdProductCategoryImageTextInfoVO> imageTextInfos;

    /**
     * 类目列表
     */
    List<String> categoryKeys;

    /**
     * 套餐ids列表
     */
    List<Integer> commodityIds;

    public AdProductCategoryImageTextSelectorVO() {
    }

    public AdProductCategoryImageTextSelectorVO(ProductCategoryImageTextSelector imageTextSelector) {
        if (Objects.nonNull(imageTextSelector)) {
            BeanUtil.copyProperties(imageTextSelector, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInsideName() {
        return insideName;
    }

    public void setInsideName(String insideName) {
        this.insideName = insideName;
    }

    public String getImageLink() {
        return imageLink;
    }

    public void setImageLink(String imageLink) {
        this.imageLink = imageLink;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getDefaultDisplay() {
        return defaultDisplay;
    }

    public void setDefaultDisplay(Boolean defaultDisplay) {
        this.defaultDisplay = defaultDisplay;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public List<AdProductCategoryImageTextInfoVO> getImageTextInfos() {
        return imageTextInfos;
    }

    public void setImageTextInfos(List<AdProductCategoryImageTextInfoVO> imageTextInfos) {
        this.imageTextInfos = imageTextInfos;
    }

    public List<String> getCategoryKeys() {
        return categoryKeys;
    }

    public void setCategoryKeys(List<String> categoryKeys) {
        this.categoryKeys = categoryKeys;
    }

    public List<Integer> getCommodityIds() {
        return commodityIds;
    }

    public void setCommodityIds(List<Integer> commodityIds) {
        this.commodityIds = commodityIds;
    }

    @Override
    public String toString() {
        return "AdProductCategoryImageTextSelectorVO{" +
                "id=" + id +
                ", insideName='" + insideName + '\'' +
                ", imageLink='" + imageLink + '\'' +
                ", orderIndex=" + orderIndex +
                ", defaultDisplay=" + defaultDisplay +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", imageTextInfos=" + imageTextInfos +
                ", categoryKeys=" + categoryKeys +
                ", commodityIds=" + commodityIds +
                '}';
    }
}
