package com.insta360.store.admin.rpc.commodity.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 工单套餐信息
 * @author: py
 * @create: 2024-12-13 10:50
 */
public class RepairCommodityVO implements Serializable {

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 根据铜一个productId封装的套餐信息
     */
    private List<RepairCommodityInfoVO> repairCommodityPrices;

    public static class RepairCommodityInfoVO implements Serializable {

        /**
         * 套餐Id
         */
        private Integer commodityId;

        /**
         * 套餐金额
         */
        private Float amount;

        /**
         * 货币类型
         */
        private String currency;

        /**
         * 产品名 + 套餐名
         */
        private String productName;

        /**
         * 多语言类别
         */
        private String language;

        /**
         * 地区
         */
        private String area;

        public Integer getCommodityId() {
            return commodityId;
        }

        public void setCommodityId(Integer commodity) {
            this.commodityId = commodity;
        }

        public Float getAmount() {
            return amount;
        }

        public void setAmount(Float amount) {
            this.amount = amount;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }

        public String getArea() {
            return area;
        }

        public void setArea(String area) {
            this.area = area;
        }

        @Override
        public String toString() {
            return "RepairCommodityInfoVO{" +
                    "commodityId=" + commodityId +
                    ", amount=" + amount +
                    ", currency='" + currency + '\'' +
                    ", productName='" + productName + '\'' +
                    ", language='" + language + '\'' +
                    ", area='" + area + '\'' +
                    '}';
        }
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<RepairCommodityInfoVO> getRepairCommodityPrices() {
        return repairCommodityPrices;
    }

    public void setRepairCommodityPrices(List<RepairCommodityInfoVO> repairCommodityPrices) {
        this.repairCommodityPrices = repairCommodityPrices;
    }

    @Override
    public String toString() {
        return "RepairCommodityVO{" +
                "productId=" + productId +
                ", repairCommodityPrices=" + repairCommodityPrices +
                '}';
    }
}
