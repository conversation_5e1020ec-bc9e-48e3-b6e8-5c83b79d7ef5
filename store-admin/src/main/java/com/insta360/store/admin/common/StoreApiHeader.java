package com.insta360.store.admin.common;

/**
 * @Author: hyc
 * @Date: 2019-08-15
 * @Description:
 */
public enum StoreApiHeader {

    // 商城用户token
    StoreToken("X-Store-Token"),

    // 运营后台账户Token
    AdminUserToken("X-User-Token"),

    // 后台页面路径
    AdminUrlPath("X-Url-Path"),

    // 商城后台管理员工工号
    JobNumber("X-Job-Number"),

    // 分销码
    ResellerCode("X-insrc"),

    // 限时推广分销码
    ResellerFlashPromoCode("X-insfpc");

    public static StoreApiHeader parse(String s) {
        for (StoreApiHeader apiHeader : values()) {
            if (apiHeader.key.equalsIgnoreCase(s)) {
                return apiHeader;
            }
        }
        return null;
    }

    private String key;

    StoreApiHeader(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}
