package com.insta360.store.admin.controller.meta.vo.categoryPage;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategoryTextFilterInfo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
public class AdProductCategoryTextFilterInfoVO implements Serializable {

    private Integer id;

    /**
     * 文字筛选器id
     */
    private Integer textFilterId;

    /**
     * 文字筛选器名称
     */
    private String textFilterName;

    /**
     * 语言
     */
    private String language;

    /**
     * 地区
     */
    private String country;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public AdProductCategoryTextFilterInfoVO() {
    }

    public AdProductCategoryTextFilterInfoVO(ProductCategoryTextFilterInfo textFilterInfo) {
        if (Objects.nonNull(textFilterInfo)) {
            BeanUtil.copyProperties(textFilterInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTextFilterId() {
        return textFilterId;
    }

    public void setTextFilterId(ProductCategoryTextFilterInfo textFilterInfo) {
        if (Objects.nonNull(textFilterInfo)) {
            BeanUtil.copyProperties(textFilterInfo, this);
        }
    }

    public String getTextFilterName() {
        return textFilterName;
    }

    public void setTextFilterName(String textFilterName) {
        this.textFilterName = textFilterName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AdProductCategoryTextFilterInfoVO{" +
                "id=" + id +
                ", textFilterId=" + textFilterId +
                ", textFilterName='" + textFilterName + '\'' +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
