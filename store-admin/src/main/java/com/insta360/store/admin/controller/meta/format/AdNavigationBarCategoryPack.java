package com.insta360.store.admin.controller.meta.format;

import com.insta360.store.admin.controller.meta.vo.navigationbar.*;
import com.insta360.store.business.meta.enums.NavigationBarTypeEnum;
import com.insta360.store.business.meta.model.*;
import com.insta360.store.business.meta.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/5/23
 * @Description:
 */
@Component
public class AdNavigationBarCategoryPack {

    @Autowired
    NavigationBarCategoryInsideService navigationBarCategoryInsideService;

    @Autowired
    NavigationBarCategoryInfoService navigationBarCategoryInfoService;

    @Autowired
    NavigationBarCategorySubsetInsideService navigationBarCategorySubsetInsideService;

    @Autowired
    NavigationBarBannerMainService navigationBarBannerMainService;

    @Autowired
    NavigationBarBannerSubsetService navigationBarBannerSubsetService;

    @Autowired
    NavigationBarBannerInfoService navigationBarBannerInfoService;

    /**
     * 封装一二级内部类目
     *
     * @return
     */
    public List<AdNavigationBarCategoryInsideVO> listCategoryNavigationBars() {
        List<NavigationBarCategoryInside> navigationBarCategoryInsides = navigationBarCategoryInsideService.listCategoryNavigationBars();
        return navigationBarCategoryInsides.stream().map(navigationBarCategoryInside -> {
            AdNavigationBarCategoryInsideVO navigationBarCategoryInsideVO = new AdNavigationBarCategoryInsideVO(navigationBarCategoryInside);
            // 一级多地区
            List<NavigationBarCategoryInfo> categoryInfos = navigationBarCategoryInfoService.listByCategoryInsideId(navigationBarCategoryInside.getId());
            List<AdNavigationBarCategoryInfoVO> categoryInfoVOList = categoryInfos.stream().map(AdNavigationBarCategoryInfoVO::new).collect(Collectors.toList());
            navigationBarCategoryInsideVO.setCategoryInfos(categoryInfoVOList);
            // 活动banner相关
            if (NavigationBarTypeEnum.isBannerType(navigationBarCategoryInside.getType())) {
                return packBannerMain(navigationBarCategoryInsideVO, navigationBarCategoryInside.getId());
            }
            // 二级内部类目
            List<NavigationBarCategorySubsetInside> categorySubsetInsides = navigationBarCategorySubsetInsideService.listByCategoryInsideId(navigationBarCategoryInside.getId());
            List<AdNavigationBarCategorySubsetInsideVO> subsetInsideVos = categorySubsetInsides.stream().map(AdNavigationBarCategorySubsetInsideVO::new).collect(Collectors.toList());
            navigationBarCategoryInsideVO.setCategorySubsetInsides(subsetInsideVos);
            return navigationBarCategoryInsideVO;
        }).collect(Collectors.toList());
    }

    /**
     * 封装banner数据
     *
     * @param categoryInsideVO
     * @param categoryId
     * @return
     */
    private AdNavigationBarCategoryInsideVO packBannerMain(AdNavigationBarCategoryInsideVO categoryInsideVO, Integer categoryId) {
        List<NavigationBarBannerMain> bannerMainList = navigationBarBannerMainService.listByCategoryInsideId(categoryId);
        categoryInsideVO.setBannerMains(bannerMainList.stream().map(bannerMain -> {
            AdNavigationBarBannerMainVO bannerMainVO = new AdNavigationBarBannerMainVO(bannerMain);
            List<NavigationBarBannerSubset> bannerSubsetList = navigationBarBannerSubsetService.listByBannerMainId(bannerMain.getId());
            bannerMainVO.setBannerSubsets(bannerSubsetList.stream().map(bannerSubset -> {
                AdNavigationBarBannerSubsetVO bannerSubsetVO = new AdNavigationBarBannerSubsetVO(bannerSubset);
                List<NavigationBarBannerInfo> bannerInfos = navigationBarBannerInfoService.listByBannerSubsetId(Collections.singletonList(bannerSubset.getId()));
                bannerSubsetVO.setBannerInfos(bannerInfos.stream().map(AdNavigationBarBannerInfoVO::new).collect(Collectors.toList()));
                return bannerSubsetVO;
            }).collect(Collectors.toList()));
            return bannerMainVO;
        }).collect(Collectors.toList()));
        return categoryInsideVO;
    }
}
