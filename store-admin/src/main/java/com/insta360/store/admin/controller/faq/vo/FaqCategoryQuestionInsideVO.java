package com.insta360.store.admin.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInside;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/4/22
 * @Description:
 */
public class FaqCategoryQuestionInsideVO implements Serializable {

    private Integer id;

    /**
     * 二级内部分类id
     */
    private Integer categorySubsetInsideId;

    /**
     * 问题内部分类名称
     */
    private String categoryQuestionInsideName;

    /**
     * 是否启用（0：默认不启用，1：启用）
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public FaqCategoryQuestionInsideVO() {
    }

    public FaqCategoryQuestionInsideVO(FaqCategoryQuestionInside questionInside) {
        if (questionInside != null) {
            BeanUtil.copyProperties(questionInside, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategorySubsetInsideId() {
        return categorySubsetInsideId;
    }

    public void setCategorySubsetInsideId(Integer categorySubsetInsideId) {
        this.categorySubsetInsideId = categorySubsetInsideId;
    }

    public String getCategoryQuestionInsideName() {
        return categoryQuestionInsideName;
    }

    public void setCategoryQuestionInsideName(String categoryQuestionInsideName) {
        this.categoryQuestionInsideName = categoryQuestionInsideName;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
