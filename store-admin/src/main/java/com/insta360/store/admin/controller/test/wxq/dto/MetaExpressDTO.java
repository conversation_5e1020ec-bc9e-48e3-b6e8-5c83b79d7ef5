package com.insta360.store.admin.controller.test.wxq.dto;

import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Meta Express DTO
 *
 * <AUTHOR>
 * @date 2024/12/06
 */
public class MetaExpressDTO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "物流ID不能为空")
    @NotNull
    private String id;

    /**
     * 测试物流单号
     */
    @NotBlank(message = "物流跟踪链接不能为空")
    @NotNull
    private String testNumber;

    /**
     * 跟踪 URL
     */
    @NotBlank(message = "物流跟踪链接不能为空")
    @NotNull
    @URL
    private String trackUrl;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTestNumber() {
        return testNumber;
    }

    public void setTestNumber(String testNumber) {
        this.testNumber = testNumber;
    }

    public String getTrackUrl() {
        return trackUrl;
    }

    public void setTrackUrl(String trackUrl) {
        this.trackUrl = trackUrl;
    }

    @Override
    public String toString() {
        return "MetaExpressDTO{" +
                "id='" + id + '\'' +
                ", testNumber='" + testNumber + '\'' +
                ", trackUrl='" + trackUrl + '\'' +
                '}';
    }
}
