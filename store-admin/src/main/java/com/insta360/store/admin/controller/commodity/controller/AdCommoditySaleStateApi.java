package com.insta360.store.admin.controller.commodity.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.commodity.controller.cache.AdCommoditySaleStateCachePack;
import com.insta360.store.admin.controller.commodity.format.AdCommodityPack;
import com.insta360.store.admin.controller.commodity.vo.AdCommodityVO;
import com.insta360.store.business.admin.commodity.service.impl.helper.ProductCommodityHelper;
import com.insta360.store.business.commodity.dto.CommoditySaleStateDTO;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommoditySaleState;
import com.insta360.store.business.commodity.service.CommoditySaleStateService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommoditySaleStateHelper;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import com.insta360.store.business.product.model.Product;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019-08-05
 * @Description: 套餐销售状态
 */
@RestController
@PermissionResource(code = "commoditySaleState", desc = "套餐销售状态")
public class AdCommoditySaleStateApi extends BaseAdminApi {

    @Autowired
    AdCommodityPack adCommodityPack;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductCommodityHelper productCommodityHelper;

    @Autowired
    CommoditySaleStateHelper commoditySaleStateHelper;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    @Autowired
    AdCommoditySaleStateCachePack adCommoditySaleStateCachePack;

    /**
     * 获取所有的销售状态值
     *
     * @return
     */
    @LogAttr(desc = "获取所有的销售状态值", logType = LogType.query)
    @Permission(code = "store.commodity.commoditySaleState.listSaleStateOptions", desc = "获取所有的销售状态值")
    @GetMapping("/admin/commodity/listSaleStateOptions")
    public Response<? extends Map> listSaleStateOptions() {
        List<JSONObject> res = Arrays.stream(SaleState.values()).map(saleState -> {
            JSONObject json = new JSONObject();
            json.put("key", saleState.name());
            json.put("code", saleState.getCode());
            json.put("name", saleState.getName());
            return json;
        }).collect(Collectors.toList());

        return Response.ok("sale_states", res);
    }

    /**
     * 获取套餐的销售状态
     *
     * @param commodityId
     */
    @LogAttr(desc = "获取套餐的销售状态", logType = LogType.query)
    @Permission(code = "store.commodity.commoditySaleState.getSaleStates", desc = "获取套餐的销售状态")
    @GetMapping("/admin/commodity/getSaleStates")
    public Response<? extends Map> getSaleStates(@RequestParam(required = false, value = "commodity_id") Integer commodityId) {
        AdCommodityPack.PackSetting packSetting = new AdCommodityPack.PackSetting(this);
        packSetting.setWithSaleState(true);

        Commodity commodity = commodityService.getById(commodityId);
        AdCommodityVO commodityVO = adCommodityPack.doPack(commodity, packSetting);

        return Response.ok("sale_states", commodityVO.getSaleStates());
    }

    /**
     * upsert 套餐销售状态
     *
     * @param saleStateDataParam
     */
    @LogAttr(desc = "upsert 套餐销售状态")
    @Permission(code = "store.commodity.commoditySaleState.upsertSaleState", desc = "新增套餐销售状态")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/upsertSaleState", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> upsertSaleState(@RequestBody CommoditySaleStateDTO saleStateDataParam) {
        if (Objects.isNull(saleStateDataParam.getCommodityId())) {
            return Response.failed();
        }

        CommoditySaleState commoditySaleState = saleStateDataParam.getPojoObject();
        Integer commodityId = commoditySaleState.getCommodityId();
        if (commodityId != null) {
            Commodity commodity = commodityService.getById(commodityId);
            if (Product.INSURANCE_SERVICE_PRODUCT.contains(commodity.getProduct())) {
                // 状态改变异步处理
                CommoditySaleState oldCommoditySaleState = commoditySaleStateService.getSaleState(commodityId, InstaCountry.parse(commoditySaleState.getCountry()));
                if (oldCommoditySaleState != null && !oldCommoditySaleState.getSaleState().equals(commoditySaleState.getSaleState())) {
                    productCommodityHelper.saveCommoditySaleState(commoditySaleState, oldCommoditySaleState, commodity);
                }
            }
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityId));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityId));
        SearchDataChangeContext.set(searchDataChangeParams);

        // 单个更新
        adCommoditySaleStateCachePack.upsertCommoditySaleState(cachePutKeyParameter, commoditySaleState);

        commoditySaleStateHelper.publishEvent(commoditySaleState);
        return Response.ok();
    }

    /**
     * 批量修改单个套餐的销售状态
     *
     * @param
     */
    @LogAttr(desc = "批量修改单个套餐的销售状态")
    @Permission(code = "store.commodity.commoditySaleState.batchUpsertSaleState", desc = "批量修改单个套餐的销售状态")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/batchUpsertState", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> batchUpsertSaleState(@RequestBody CommoditySaleStateDTO saleStateDataParam) {
        SaleState saleStateEnum = SaleState.parse(saleStateDataParam.getSaleState());
        if (saleStateEnum == null) {
            return Response.failed("不支持的销售状态!");
        }

        Integer commodityId = saleStateDataParam.getCommodityId();
        if (Objects.isNull(commodityId)) {
            Response.failed("套餐ID缺失！");
        }

        List<CommoditySaleState> commoditySaleStates = saleStateDataParam.getCountries()
                .stream()
                .map(country -> {
                    CommoditySaleState commoditySaleState = new CommoditySaleState();
                    commoditySaleState.setCommodityId(commodityId);
                    commoditySaleState.setCountry(country);
                    commoditySaleState.setSaleState(saleStateEnum.getCode());
                    return commoditySaleState;
                }).collect(Collectors.toList());

        List<Integer> commodityIds = Lists.newArrayList(commodityId);
        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(commodityIds);

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(commodityIds);
        SearchDataChangeContext.set(searchDataChangeParams);

        // 批量更新
        adCommoditySaleStateCachePack.upsertBatchCommoditySaleState(cachePutKeyParameter, commoditySaleStates);

        // 保存保险服务套餐的销售状态修改记录
        commoditySaleStateHelper.saveCommoditySaleStateRecord(saleStateDataParam.getPojoObject());

        // 去除通知重复的套餐（不考虑地区）
        if (CollectionUtils.isNotEmpty(commoditySaleStates)) {
            commoditySaleStateHelper.publishEvent(commoditySaleStates.get(0));
        }
        return Response.ok();
    }
}
