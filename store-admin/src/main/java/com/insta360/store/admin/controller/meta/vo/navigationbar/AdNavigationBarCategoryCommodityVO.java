package com.insta360.store.admin.controller.meta.vo.navigationbar;


import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategoryCommodity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/5/30
 * @Description:
 */
public class AdNavigationBarCategoryCommodityVO implements Serializable {

    private Integer id;

    /**
     * 二级内部分类id
     */
    private Integer subsetInsideId;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 新品标签
     */
    private Boolean newTag;

    /**
     * 套餐排序
     */
    private Integer orderIndex;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public AdNavigationBarCategoryCommodityVO() {
    }

    public AdNavigationBarCategoryCommodityVO(NavigationBarCategoryCommodity categoryCommodity) {
        if (categoryCommodity != null) {
            BeanUtil.copyProperties(categoryCommodity, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSubsetInsideId() {
        return subsetInsideId;
    }

    public void setSubsetInsideId(Integer subsetInsideId) {
        this.subsetInsideId = subsetInsideId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Boolean getNewTag() {
        return newTag;
    }

    public void setNewTag(Boolean newTag) {
        this.newTag = newTag;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AdNavigationBarCategoryCommodityVO{" +
                "id=" + id +
                ", subsetInsideId=" + subsetInsideId +
                ", commodityId=" + commodityId +
                ", newTag=" + newTag +
                ", orderIndex=" + orderIndex +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                '}';
    }
}
