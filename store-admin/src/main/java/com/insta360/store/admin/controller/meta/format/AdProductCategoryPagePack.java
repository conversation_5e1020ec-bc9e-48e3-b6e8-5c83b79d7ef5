package com.insta360.store.admin.controller.meta.format;

import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.admin.controller.meta.vo.categoryPage.*;
import com.insta360.store.admin.controller.meta.vo.scenerySection.AdSceneryCardVO;
import com.insta360.store.admin.controller.meta.vo.scenerySection.AdScenerySectionVO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.enums.CategorySceneryCardType;
import com.insta360.store.business.meta.enums.CategoryTextFilterType;
import com.insta360.store.business.meta.model.*;
import com.insta360.store.business.meta.service.*;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
@Component
public class AdProductCategoryPagePack {

    @Autowired
    ProductCategoryImageTextSelectorService productCategoryImageTextSelectorService;

    @Autowired
    ProductCategoryImageTextInfoService productCategoryImageTextInfoService;

    @Autowired
    ProductCategoryImageTextCommodityService productCategoryImageTextCommodityService;

    @Autowired
    ProductCategoryTextFilterMainService productCategoryTextFilterMainService;

    @Autowired
    ProductCategoryTextFilterService productCategoryTextFilterService;

    @Autowired
    ProductCategoryTextFilterInfoService productCategoryTextFilterInfoService;

    @Autowired
    ProductCategoryTopSortCommodityService productCategoryTopSortCommodityService;

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductCategoryScenerySectionService productCategoryScenerySectionService;

    @Autowired
    ProductCategorySceneryCardMainService productCategorySceneryCardMainService;

    @Autowired
    ProductCategorySceneryCardResourceService productCategorySceneryCardResourceService;

    @Autowired
    ProductCategorySceneryCardCommodityInfoService productCategorySceneryCardCommodityInfoService;

    /**
     * 封装类目管理页数据
     *
     * @param categoryKey
     * @return
     */
    public AdProductCategoryPageVO packCategoryPage(String categoryKey) {
        AdProductCategoryPageVO productCategoryPageVo = new AdProductCategoryPageVO();

        // 图文筛选器
        buildImageTextFilter(categoryKey, productCategoryPageVo);

        // 文字筛选器
        buildTextFilter(productCategoryPageVo, categoryKey);

        // 置顶排序
        buildTopCommodity(categoryKey, productCategoryPageVo);

        // 场景专区
        buildScenerySection(productCategoryPageVo);

        return productCategoryPageVo;
    }

    /**
     * 封装场景专区
     *
     * @param productCategoryPageVo
     */
    private void buildScenerySection(AdProductCategoryPageVO productCategoryPageVo) {
        List<ProductCategoryScenerySection> productCategoryScenerySections = productCategoryScenerySectionService.list(null);
        if (CollectionUtils.isEmpty(productCategoryScenerySections)) {
            productCategoryPageVo.setScenerySectionList(new ArrayList<>());
            return;
        }
        List<Integer> sectionIds = productCategoryScenerySections.stream().map(ProductCategoryScenerySection::getId).collect(Collectors.toList());

        List<ProductCategorySceneryCardMain> productCategorySceneryCardMains = productCategorySceneryCardMainService.listBySectionIds(sectionIds);

        Map<Integer, List<ProductCategorySceneryCardMain>> sceneryCardMainMap = productCategorySceneryCardMains.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardMain::getScenerySectionId));
        List<Integer> sceneryCardMainIds = productCategorySceneryCardMains.stream().map(ProductCategorySceneryCardMain::getId).collect(Collectors.toList());

        List<ProductCategorySceneryCardCommodityInfo> productCategorySceneryCardCommodityInfos = productCategorySceneryCardCommodityInfoService.listByCardIds(sceneryCardMainIds);
        Map<Integer, List<ProductCategorySceneryCardCommodityInfo>> commodityInfoMap = productCategorySceneryCardCommodityInfos.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardCommodityInfo::getSceneryCardId));

        List<ProductCategorySceneryCardResource> productCategorySceneryCardResources = productCategorySceneryCardResourceService.listByCardIds(sceneryCardMainIds);
        Map<Integer, List<ProductCategorySceneryCardResource>> cardResourceMap = productCategorySceneryCardResources.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardResource::getSceneryCardId));

        List<AdScenerySectionVO> scenerySectionVoList = productCategoryScenerySections.stream()
                .sorted(Comparator.comparingInt(ProductCategoryScenerySection::getOrderIndex).reversed())
                .map(productCategoryScenerySection -> {
                    Integer sectionId = productCategoryScenerySection.getId();
                    AdScenerySectionVO scenerySectionVo = new AdScenerySectionVO(productCategoryScenerySection);
                    List<ProductCategorySceneryCardMain> categorySceneryCardMains = sceneryCardMainMap.get(sectionId);
                    if (CollectionUtils.isEmpty(categorySceneryCardMains)) {
                        return scenerySectionVo;
                    }
                    Map<String, List<ProductCategorySceneryCardMain>> cardMap = categorySceneryCardMains.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardMain::getCardType));

                    // 大小卡片
                    List<AdSceneryCardVO> largeCardList = getCardList(cardMap.get(CategorySceneryCardType.large.name()), commodityInfoMap, cardResourceMap);
                    List<AdSceneryCardVO> smallCardList = getCardList(cardMap.get(CategorySceneryCardType.small.name()), commodityInfoMap, cardResourceMap);

                    scenerySectionVo.setLargeCardList(largeCardList);
                    scenerySectionVo.setSmallCardList(smallCardList);
                    return scenerySectionVo;
                }).collect(Collectors.toList());
        productCategoryPageVo.setScenerySectionList(scenerySectionVoList);
    }

    /**
     * 获取卡片列表
     *
     * @param cardMains
     * @param commodityInfoMap
     * @param cardResourceMap
     * @return
     */
    private List<AdSceneryCardVO> getCardList(List<ProductCategorySceneryCardMain> cardMains,
                                              Map<Integer, List<ProductCategorySceneryCardCommodityInfo>> commodityInfoMap,
                                              Map<Integer, List<ProductCategorySceneryCardResource>> cardResourceMap) {
        if (CollectionUtils.isEmpty(cardMains)) {
            return null;
        }

        return cardMains.stream()
                .sorted(Comparator.comparingInt(ProductCategorySceneryCardMain::getOrderIndex).reversed())
                .map(card -> {
                    AdSceneryCardVO sceneryCardVo = new AdSceneryCardVO(card);
                    Integer cardId = card.getId();
                    List<ProductCategorySceneryCardResource> cardResources = cardResourceMap.get(cardId);
                    List<ProductCategorySceneryCardCommodityInfo> cardCommodityInfos = commodityInfoMap.get(cardId);
                    sceneryCardVo.setSceneryCardResourceList(cardResources);
                    sceneryCardVo.setSceneryCardCommodityInfoList(cardCommodityInfos);
                    return sceneryCardVo;
                }).collect(Collectors.toList());
    }

    /**
     * 封装置顶排序
     *
     * @param categoryKey
     * @param productCategoryPageVo
     */
    private void buildTopCommodity(String categoryKey, AdProductCategoryPageVO productCategoryPageVo) {
        List<ProductCategoryTopSortCommodity> sortCommodityList = productCategoryTopSortCommodityService.listByCategoryKey(categoryKey);
        if (CollectionUtils.isEmpty(sortCommodityList)) {
            productCategoryPageVo.setTopSortCommodityList(new ArrayList<>(0));
            return;
        }
        List<Integer> productIds = sortCommodityList.stream().map(ProductCategoryTopSortCommodity::getProductId).collect(Collectors.toList());
        List<Integer> commodityIds = sortCommodityList.stream().map(ProductCategoryTopSortCommodity::getCommodityId).collect(Collectors.toList());
        Map<Integer, Product> productMap = productService.listByIds(productIds).stream().collect(Collectors.toMap(Product::getId, p -> p));
        Map<Integer, Commodity> commodityMap = commodityService.listByIds(commodityIds).stream().collect(Collectors.toMap(Commodity::getId, c -> c));

        List<AdProductCategoryTopSortCommodityVO> sortCommodityVoList = sortCommodityList.stream().map(sortCommodity -> {
            AdProductCategoryTopSortCommodityVO topSortCommodityVo = new AdProductCategoryTopSortCommodityVO(sortCommodity);
            topSortCommodityVo.setName(productMap.get(sortCommodity.getProductId()).getName()
                    + "【" + commodityMap.get(sortCommodity.getCommodityId()).getName() + "】");
            return topSortCommodityVo;
        }).collect(Collectors.toList());
        productCategoryPageVo.setTopSortCommodityList(sortCommodityVoList);
    }

    /**
     * 构建图文筛选器
     *
     * @param categoryKey
     * @param productCategoryPageVo
     */
    private void buildImageTextFilter(String categoryKey, AdProductCategoryPageVO productCategoryPageVo) {
        List<ProductCategoryImageTextSelector> textSelectorList = productCategoryImageTextSelectorService.listImageTextSelectorByCategoryKey(categoryKey);
        if (CollectionUtils.isEmpty(textSelectorList)) {
            productCategoryPageVo.setImageTextSelectorList(new ArrayList<>(0));
            return;
        }

        List<Integer> selectorIds = textSelectorList.stream().map(ProductCategoryImageTextSelector::getId).collect(Collectors.toList());
        Map<Integer, List<ProductCategoryImageTextInfo>> imageTextInfoMap = productCategoryImageTextInfoService.listBySelectorIds(selectorIds)
                .stream().collect(Collectors.groupingBy(ProductCategoryImageTextInfo::getSelectorId));
        Map<Integer, List<ProductCategoryImageTextCommodity>> imageTextCommodityMap = productCategoryImageTextCommodityService.listBySelectorIds(selectorIds)
                .stream().collect(Collectors.groupingBy(ProductCategoryImageTextCommodity::getSelectorId));

        List<AdProductCategoryImageTextSelectorVO> imageTextSelectorList = textSelectorList.stream().map(textSelector -> {
            AdProductCategoryImageTextSelectorVO textSelectorVo = new AdProductCategoryImageTextSelectorVO(textSelector);
            List<ProductCategoryImageTextInfo> imageTextInfos = imageTextInfoMap.get(textSelector.getId());
            List<ProductCategoryImageTextCommodity> commodityList = imageTextCommodityMap.get(textSelector.getId());

            List<String> categoryKeys = new ArrayList<>();
            List<Integer> commodityIds = new ArrayList<>();
            textSelectorVo.setImageTextInfos(imageTextInfos.stream().map(AdProductCategoryImageTextInfoVO::new).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(commodityList)) {
                commodityList.forEach(imageTextCommodity -> {
                    if (StringUtil.isNotBlank(imageTextCommodity.getCategoryKey())) {
                        categoryKeys.add(imageTextCommodity.getCategoryKey());
                    }

                    if (-1 != imageTextCommodity.getCommodityId()) {
                        commodityIds.add(imageTextCommodity.getCommodityId());
                    }
                });
            }
            textSelectorVo.setCategoryKeys(categoryKeys);
            textSelectorVo.setCommodityIds(commodityIds);
            return textSelectorVo;
        }).collect(Collectors.toList());

        // 封装图文筛选器数据
        productCategoryPageVo.setImageTextSelectorList(imageTextSelectorList);
    }

    /**
     * 构建文字筛选器
     *
     * @param productCategoryPageVo
     * @param categoryKey
     */
    private void buildTextFilter(AdProductCategoryPageVO productCategoryPageVo, String categoryKey) {
        List<ProductCategoryTextFilterMain> productCategoryTextFilterMains = productCategoryTextFilterMainService.listTextFilter(categoryKey);
        if (CollectionUtils.isEmpty(productCategoryTextFilterMains)) {
            productCategoryPageVo.setTextFilterList(new ArrayList<>(0));
            productCategoryPageVo.setTextFilter(new AdProductCategoryTextFilterMainVO());
            return;
        }

        List<AdProductCategoryTextFilterMainVO> textFilterMainVoList = productCategoryTextFilterMains.stream().map(productCategoryTextFilterMain -> {
            Integer textFilterMainId = productCategoryTextFilterMain.getId();
            AdProductCategoryTextFilterMainVO textFilterMainVo = new AdProductCategoryTextFilterMainVO(productCategoryTextFilterMain);
            List<ProductCategoryTextFilter> textFilterList = productCategoryTextFilterService.listTextFilterByMainId(textFilterMainId);
            List<AdProductCategoryTextFilterInfoVO> filterInfoList = productCategoryTextFilterInfoService.listBySelectorId(textFilterMainId).stream().map(AdProductCategoryTextFilterInfoVO::new).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(textFilterList)) {
                textFilterMainVo.setTextFilterTypeList(textFilterList.stream().map(AdProductCategoryTextFilterVO::new).collect(Collectors.toList()));
                // todo:兼容旧字段
                textFilterMainVo.setAdapterTypeList(textFilterList.stream().map(AdProductCategoryTextFilterVO::new).collect(Collectors.toList()));
            }
            textFilterMainVo.setTextFilterInfos(filterInfoList);

            // todo:兼容旧字段
            if (CategoryTextFilterType.adapter_type.getTextFilterMainId().equals(textFilterMainId)) {
                productCategoryPageVo.setTextFilter(textFilterMainVo);
            }
            return textFilterMainVo;
        }).collect(Collectors.toList());

        productCategoryPageVo.setTextFilterList(textFilterMainVoList);
    }

    /**
     * 根据场景专区ID查询大小卡片
     *
     * @param scenerySectionId
     */
    public AdScenerySectionVO getSceneryCard(Integer scenerySectionId) {
        AdScenerySectionVO scenerySectionVo = new AdScenerySectionVO();
        List<ProductCategorySceneryCardMain> productCategorySceneryCardMains = productCategorySceneryCardMainService.listBySectionId(scenerySectionId);
        if (CollectionUtils.isEmpty(productCategorySceneryCardMains)) {
            return scenerySectionVo;
        }
        Map<String, List<ProductCategorySceneryCardMain>> cardMap = productCategorySceneryCardMains.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardMain::getCardType));
        List<Integer> sceneryCardMainIds = productCategorySceneryCardMains.stream().map(ProductCategorySceneryCardMain::getId).collect(Collectors.toList());

        List<ProductCategorySceneryCardCommodityInfo> productCategorySceneryCardCommodityInfos = productCategorySceneryCardCommodityInfoService.listByCardIds(sceneryCardMainIds);
        Map<Integer, List<ProductCategorySceneryCardCommodityInfo>> commodityInfoMap = productCategorySceneryCardCommodityInfos.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardCommodityInfo::getSceneryCardId));

        List<ProductCategorySceneryCardResource> productCategorySceneryCardResources = productCategorySceneryCardResourceService.listByCardIds(sceneryCardMainIds);
        Map<Integer, List<ProductCategorySceneryCardResource>> cardResourceMap = productCategorySceneryCardResources.stream().collect(Collectors.groupingBy(ProductCategorySceneryCardResource::getSceneryCardId));

        // 大小卡片
        List<AdSceneryCardVO> largeCardList = getCardList(cardMap.get(CategorySceneryCardType.large.name()), commodityInfoMap, cardResourceMap);
        List<AdSceneryCardVO> smallCardList = getCardList(cardMap.get(CategorySceneryCardType.small.name()), commodityInfoMap, cardResourceMap);

        scenerySectionVo.setLargeCardList(largeCardList);
        scenerySectionVo.setSmallCardList(smallCardList);
        return scenerySectionVo;
    }
}
