package com.insta360.store.admin.controller.cloud.vo;

import com.insta360.store.business.cloud.model.CloudStorageInsuranceBenefitBind;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitDetail;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/20
 */
public class AdStoreBenefitVO implements Serializable {

    /**
     * 权益类型
     * @see com.insta360.store.business.cloud.enums.BenefitType
     */
    private String benefitType;

    /**
     * 是否过期
     */
    private Boolean expired;

    /**
     * 使用已用
     */
    private Boolean used;

    /**
     * 绑定序列号
     */
    private String bindSerialNumber;

    /**
     * 剩余额度
     */
    private Integer remainderQuota;

    /**
     * 固定额度
     */
    private Integer fixedQuota;

    public AdStoreBenefitVO() {
    }

    public AdStoreBenefitVO(CloudStorageStoreBenefitDetail benefitDetail, CloudStorageInsuranceBenefitBind insuranceBenefitBind) {
        if(Objects.nonNull(benefitDetail)) {
            BeanUtils.copyProperties(benefitDetail, this);
            this.bindSerialNumber = Objects.nonNull(insuranceBenefitBind) ? insuranceBenefitBind.getSerialNumber() : null;
        }
    }

    public String getBenefitType() {
        return benefitType;
    }

    public void setBenefitType(String benefitType) {
        this.benefitType = benefitType;
    }

    public Boolean getExpired() {
        return expired;
    }

    public void setExpired(Boolean expired) {
        this.expired = expired;
    }

    public Boolean getUsed() {
        return used;
    }

    public void setUsed(Boolean used) {
        this.used = used;
    }

    public String getBindSerialNumber() {
        return bindSerialNumber;
    }

    public void setBindSerialNumber(String bindSerialNumber) {
        this.bindSerialNumber = bindSerialNumber;
    }

    public Integer getRemainderQuota() {
        return remainderQuota;
    }

    public void setRemainderQuota(Integer remainderQuota) {
        this.remainderQuota = remainderQuota;
    }

    public Integer getFixedQuota() {
        return fixedQuota;
    }

    public void setFixedQuota(Integer fixedQuota) {
        this.fixedQuota = fixedQuota;
    }

    @Override
    public String toString() {
        return "AdStoreBenefitVO{" +
                "benefitType='" + benefitType + '\'' +
                ", expired=" + expired +
                ", used=" + used +
                ", bindSerialNumber='" + bindSerialNumber + '\'' +
                ", remainderQuota=" + remainderQuota +
                ", fixedQuota=" + fixedQuota +
                '}';
    }
}
