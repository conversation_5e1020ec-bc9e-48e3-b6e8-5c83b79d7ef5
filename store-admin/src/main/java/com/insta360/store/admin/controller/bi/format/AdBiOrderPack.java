package com.insta360.store.admin.controller.bi.format;

import com.insta360.store.admin.controller.commodity.vo.AdCommodityVO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2023-08-23 15:53
 */
@Component
public class AdBiOrderPack {

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductService productService;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    /**
     * 订单统计
     *
     * @param from
     * @param end
     * @param productId
     * @param country
     * @return
     */
    public List<AdCommodityVO> listCommodityCount(LocalDateTime from, LocalDateTime end, Integer productId, String country) {
        List<Order> orders = orderService.listByTimeRangeAndArea(from, end, country);

        // order id list
        List<Integer> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        List<OrderPayment> orderPayments = orderPaymentService.listByOrderIds(orderIds);

        // 剔除支付方式 之后的订单id
        List<Integer> orderIdFilterList = orderPayments.stream()
                .filter(orderPayment -> !PaymentChannel.isTransferPayment(orderPayment.paymentChannel()))
                .map(OrderPayment::getOrder)
                .collect(Collectors.toList());
        List<Order> orderFilterList = orders.stream()
                .filter(order -> orderIdFilterList.contains(order.getId()))
                .collect(Collectors.toList());

        // key:order id  value:order items  -- 全部为需要的产品id的orderItem
        List<OrderItem> orderItemList = orderItemService.listByOrderIdsAndProductId(orderIdFilterList, productId);
        if (CollectionUtils.isEmpty(orderItemList)) {
            return new ArrayList<>();
        }

        // 第二次筛选一下order--需要满足产品id符合的
        List<Integer> orderIdFilters = orderItemList.stream().map(OrderItem::getOrder).collect(Collectors.toList());
        List<Order> finalOrders = orderFilterList.stream().filter(order -> orderIdFilters.contains(order.getId())).collect(Collectors.toList());

        // key：套餐id value:key：订单状态 value 订单子项数量
        Map<Integer, Map<OrderState, Integer>> allCommodityMap = new HashMap<>();

        Map<OrderState, List<Integer>> orderMap = finalOrders.stream()
                .collect(Collectors.groupingBy(Order::orderState, Collectors.mapping(Order::getId, Collectors.toList())));

        orderMap.forEach((orderState, orderIdList) -> {
            List<OrderItem> orderItems = orderItemList.stream().filter(orderItem -> orderIdList.contains(orderItem.getOrder())).collect(Collectors.toList());
            Map<Integer, Integer> commodityResult = orderItems.stream()
                    .collect(Collectors.groupingBy(OrderItem::getCommodity, Collectors.summingInt(OrderItem::getNumber)));

            commodityResult.forEach((commodityId, number) -> {
                Map<OrderState, Integer> orderStateIntegerMap = allCommodityMap.get(commodityId);
                if (orderStateIntegerMap == null) {
                    Map<OrderState, Integer> orderItemNumber = new HashMap<>();
                    orderItemNumber.put(orderState, number);
                    allCommodityMap.put(commodityId, orderItemNumber);
                } else {
                    Map<OrderState, Integer> item = allCommodityMap.get(commodityId);
                    item.put(orderState, number);
                }
            });
        });

        // 获取产品套餐数据
        List<Integer> commodityIdList = new ArrayList<>(allCommodityMap.keySet());
        List<Commodity> commodityList = commodityService.listCommodities(commodityIdList);
        // key:commodity id value :object
        Map<Integer, Commodity> commodityMap = commodityList
                .stream().collect(Collectors.toMap(Commodity::getId, commodity -> commodity));

        List<Integer> productIds = commodityList.stream().map(Commodity::getProduct).distinct().collect(Collectors.toList());
        // key:product id value :object
        Map<Integer, Product> productMap = productService.listByIds(productIds).stream().collect(Collectors.toMap(Product::getId, o -> o));

        List<AdCommodityVO> commodities = new ArrayList<>();
        allCommodityMap
                .forEach((commodityId, value) -> {
                    Commodity commodity = commodityMap.get(commodityId);
                    AdCommodityVO commodityVO = new AdCommodityVO(commodity);
                    Product product = productMap.get(commodity.getProduct());

                    commodityVO.setProductName(product.getName());
                    commodityVO.setStatisticsData(value);
                    commodities.add(commodityVO);
                });
        return commodities;
    }
}
