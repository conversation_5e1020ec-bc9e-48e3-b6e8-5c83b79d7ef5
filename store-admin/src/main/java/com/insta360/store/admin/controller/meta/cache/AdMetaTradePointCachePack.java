package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.dto.TradePointInfoDTO;
import com.insta360.store.business.meta.model.TradePointInfo;
import com.insta360.store.business.meta.model.TradePointMain;
import com.insta360.store.business.meta.service.TradePointInfoService;
import com.insta360.store.business.meta.service.TradePointMainService;
import com.insta360.store.business.meta.service.impl.helper.TradePointHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/8/31
 * @Description:
 */
@Component
public class AdMetaTradePointCachePack {

    @Autowired
    TradePointInfoService tradePointInfoService;

    @Autowired
    TradePointMainService tradePointMainService;

    @Autowired
    TradePointHelper tradePointHelper;

    /**
     * 新增 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void saveTradePointInfo(TradePointInfoDTO tradePointInfoParam) {
        tradePointInfoService.saveTradePointInfo(tradePointInfoParam);
    }

    /**
     * 更新 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void updateTradePointInfo(TradePointInfoDTO tradePointInfoParam) {
        tradePointInfoService.updateTradePointInfo(tradePointInfoParam);
    }

    /**
     * 删除 trade point info
     *
     * @param tradePointInfos
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void deleteBatchByTradePointInfo(List<TradePointInfo> tradePointInfos) {
        tradePointInfoService.deleteBatchByTradePointInfo(tradePointInfos);
    }

    /**
     * 启用 trade point info
     *
     * @param tradePointInfo
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void enableTradePointInfo(TradePointInfo tradePointInfo) {
        tradePointInfoService.enableTradePointInfo(tradePointInfo);
    }

    /**
     * 批量启用 trade point info
     *
     * @param tradePointInfoIds
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void enableBatchTradePointInfo(List<Integer> tradePointInfoIds) {
        tradePointInfoService.enableBatchTradePointInfo(tradePointInfoIds);
    }

    /**
     * 禁用 trade point info
     *
     * @param tradePointInfo
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void disableTradePointInfo(TradePointInfo tradePointInfo) {
        tradePointInfoService.disableTradePointInfo(tradePointInfo);
    }

    /**
     * 新建 trade point
     *
     * @param tradePointMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void createTradePoint(TradePointMain tradePointMain) {
        tradePointHelper.createTradePoint(tradePointMain);
    }

    /**
     * 更新 trade point
     *
     * @param tradePointMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void updateTradePoint(TradePointMain tradePointMain) {
        tradePointHelper.updateTradePoint(tradePointMain);
    }

    /**
     * 删除 trade point
     *
     * @param tradePointMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void removeTradePoint(TradePointMain tradePointMain) {
        tradePointHelper.removeTradePoint(tradePointMain);
    }

    /**
     * 启用 trade point
     *
     * @param tradePointMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void enableTradePoint(TradePointMain tradePointMain) {
        tradePointMainService.enableTradePoint(tradePointMain);
    }

    /**
     * 禁用 trade point
     *
     * @param tradePointMain
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.TRADE_POINT)
    public void disableTradePoint(TradePointMain tradePointMain) {
        tradePointMainService.disableTradePoint(tradePointMain);
    }
}
