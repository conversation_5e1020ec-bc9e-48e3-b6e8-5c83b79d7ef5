package com.insta360.store.admin.controller.rma.vo;

import com.insta360.store.business.rma.bo.ReturnMoneyDetailBO;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/6/26
 */
public class ReturnMoneyDetailVO implements Serializable {

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 订单商品ID
     */
    private Integer orderItemId;

    /**
     * 商品单价
     */
    private BigDecimal itemPrice;

    /**
     * 商品总折扣金额
     */
    private BigDecimal itemTotalDiscountAmount;

    /**
     * 优惠扣减金额
     */
    private BigDecimal couponDeductionAmount;

    /**
     * 商品总金额
     */
    private BigDecimal itemTotalAmount;

    /**
     * 商品最大可退数量
     */
    private Integer returnMaxNum;

    /**
     * 商品售后数量
     */
    private Integer returnNum;

    /**
     * 商品实退总退金额
     */
    private BigDecimal itemActualRefundAmount;

    /**
     * 商品最大实退总退金额
     */
    private BigDecimal itemMaxActualRefundAmount;

    /**
     * 商品应退税费
     */
    private BigDecimal refundableTax;

    /**
     * 商品最大可退税费
     */
    private BigDecimal refundableMaxTax;

    /**
     * 售后单实退总金额
     */
    private BigDecimal refundAmount;

    /**
     * 退关税
     */
    private BigDecimal refundCustomsTaxAmount;

    /**
     * 商品最大应退关税
     */
    private BigDecimal refundableMaxCustomsTax;


    public ReturnMoneyDetailVO() {
    }

    public ReturnMoneyDetailVO(ReturnMoneyDetailBO returnMoneyDetail) {
        if (Objects.nonNull(returnMoneyDetail)) {
            BeanUtils.copyProperties(returnMoneyDetail, this);
        }
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public BigDecimal getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(BigDecimal itemPrice) {
        this.itemPrice = itemPrice;
    }

    public BigDecimal getItemTotalDiscountAmount() {
        return itemTotalDiscountAmount;
    }

    public void setItemTotalDiscountAmount(BigDecimal itemTotalDiscountAmount) {
        this.itemTotalDiscountAmount = itemTotalDiscountAmount;
    }

    public BigDecimal getItemTotalAmount() {
        return itemTotalAmount;
    }

    public void setItemTotalAmount(BigDecimal itemTotalAmount) {
        this.itemTotalAmount = itemTotalAmount;
    }

    public Integer getReturnMaxNum() {
        return returnMaxNum;
    }

    public void setReturnMaxNum(Integer returnMaxNum) {
        this.returnMaxNum = returnMaxNum;
    }

    public Integer getReturnNum() {
        return returnNum;
    }

    public void setReturnNum(Integer returnNum) {
        this.returnNum = returnNum;
    }

    public BigDecimal getItemActualRefundAmount() {
        return itemActualRefundAmount;
    }

    public void setItemActualRefundAmount(BigDecimal itemActualRefundAmount) {
        this.itemActualRefundAmount = itemActualRefundAmount;
    }

    public BigDecimal getItemMaxActualRefundAmount() {
        return itemMaxActualRefundAmount;
    }

    public void setItemMaxActualRefundAmount(BigDecimal itemMaxActualRefundAmount) {
        this.itemMaxActualRefundAmount = itemMaxActualRefundAmount;
    }

    public BigDecimal getRefundableTax() {
        return refundableTax;
    }

    public void setRefundableTax(BigDecimal refundableTax) {
        this.refundableTax = refundableTax;
    }

    public BigDecimal getRefundableMaxTax() {
        return refundableMaxTax;
    }

    public void setRefundableMaxTax(BigDecimal refundableMaxTax) {
        this.refundableMaxTax = refundableMaxTax;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getCouponDeductionAmount() {
        return couponDeductionAmount;
    }

    public void setCouponDeductionAmount(BigDecimal couponDeductionAmount) {
        this.couponDeductionAmount = couponDeductionAmount;
    }

    public BigDecimal getRefundCustomsTaxAmount() {
        return refundCustomsTaxAmount;
    }

    public void setRefundCustomsTaxAmount(BigDecimal refundCustomsTaxAmount) {
        this.refundCustomsTaxAmount = refundCustomsTaxAmount;
    }

    public BigDecimal getRefundableMaxCustomsTax() {
        return refundableMaxCustomsTax;
    }

    public void setRefundableMaxCustomsTax(BigDecimal refundableMaxCustomsTax) {
        this.refundableMaxCustomsTax = refundableMaxCustomsTax;
    }

    @Override
    public String toString() {
        return "ReturnMoneyDetailVO{" +
                "orderId=" + orderId +
                ", orderItemId=" + orderItemId +
                ", itemPrice=" + itemPrice +
                ", itemTotalDiscountAmount=" + itemTotalDiscountAmount +
                ", couponDeductionAmount=" + couponDeductionAmount +
                ", itemTotalAmount=" + itemTotalAmount +
                ", returnMaxNum=" + returnMaxNum +
                ", returnNum=" + returnNum +
                ", itemActualRefundAmount=" + itemActualRefundAmount +
                ", itemMaxActualRefundAmount=" + itemMaxActualRefundAmount +
                ", refundableTax=" + refundableTax +
                ", refundableMaxTax=" + refundableMaxTax +
                ", refundAmount=" + refundAmount +
                ", refundCustomsTaxAmount=" + refundCustomsTaxAmount +
                ", refundableMaxCustomsTax=" + refundableMaxCustomsTax +
                '}';
    }
}
