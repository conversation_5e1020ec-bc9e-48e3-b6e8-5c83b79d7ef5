package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.admin.controller.meta.format.AdSeoConfigPack;
import com.insta360.store.business.admin.meta.upload.SeoExcelUpload;
import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.meta.dto.SeoConfigDTO;
import com.insta360.store.business.meta.service.SeoConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2022/11/15
 * @Description:
 */
@Component
public class AdSeoCachePack {

    @Autowired
    SeoConfigService seoConfigService;

    @Autowired
    AdSeoConfigPack adSeoConfigPack;

    /**
     * 新增seo config
     *
     * @param seoConfigParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SEO_INFO)
    public void addSeoConfig(SeoConfigDTO seoConfigParam) {
        seoConfigService.addSeoConfig(seoConfigParam);
    }

    /**
     * 删除seo config
     *
     * @param seoConfigId
     */
    @CachePutMonitor(cacheableType = CachePutType.SEO_INFO)
    public void deleteSeoConfig(Integer seoConfigId) {
        seoConfigService.deleteSeoConfig(seoConfigId);
    }

    /**
     * 更新seo config
     *
     * @param seoConfigParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SEO_INFO)
    public void updateSeoConfig(SeoConfigDTO seoConfigParam) {
        seoConfigService.updateSeoConfig(seoConfigParam);
    }

    /**
     * seo excel import
     *
     * @param packSeoExcelMap
     */
    @CachePutMonitor(cacheableType = CachePutType.SEO_INFO)
    public void doPackSeoExcel(Map<Integer, List<SeoExcelUpload>> packSeoExcelMap) {
        adSeoConfigPack.doPackSeoExcel(packSeoExcelMap);
    }
}
