package com.insta360.store.admin.controller.commodity.vo.type;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/6 上午11:46
 */
public class AdSingleCommodityProductVO implements Serializable {

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 独立套餐集合
     */
    private List<AdSingleCommodityVO> commodities;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public List<AdSingleCommodityVO> getCommodities() {
        return commodities;
    }

    public void setCommodities(List<AdSingleCommodityVO> commodities) {
        this.commodities = commodities;
    }

    @Override
    public String toString() {
        return "AdSingleCommodityProductVO{" +
                "productId=" + productId +
                ", productName='" + productName + '\'' +
                ", enabled=" + enabled +
                ", commodities=" + commodities +
                '}';
    }
}
