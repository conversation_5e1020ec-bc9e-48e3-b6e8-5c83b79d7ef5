package com.insta360.store.admin.controller.meta.controller.adaptertype;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdMetaAdapterTypeCachePack;
import com.insta360.store.admin.controller.meta.vo.adaptertype.AdAdapterTypeMainVO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import com.insta360.store.business.configuration.search.enums.DataHandlerActionType;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dto.AdapterTypeMainDTO;
import com.insta360.store.business.meta.dto.condition.AdapterTypeMainQueryCondition;
import com.insta360.store.business.meta.exception.MetaErrorCode;
import com.insta360.store.business.meta.model.AdapterTypeMain;
import com.insta360.store.business.meta.service.AdapterTypeMainService;
import com.insta360.store.business.product.model.ProductAdapterType;
import com.insta360.store.business.product.service.ProductAdapterTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/09/13
 * @Description:
 */
@RestController
@PermissionResource(code = "adapterTypeMain", desc = "适配机型内部分组")
public class AdAdapterTypeMainApi extends BaseAdminApi {

    @Autowired
    AdapterTypeMainService adapterTypeMainService;

    @Autowired
    ProductAdapterTypeService productAdapterTypeService;

    @Autowired
    AdMetaAdapterTypeCachePack adMetaAdapterTypeCachePack;

    /**
     * 查询符合条件的 adapter type
     *
     * @param pageNumber
     * @param pageSize
     * @param language
     * @return
     */
    @LogAttr(desc = "查询符合条件的适配器类型", logType = LogType.query)
    @Permission(code = "store.meta.adapterTypeMain.listAdapterTypes", desc = "查询符合条件的适配器类型")
    @GetMapping("/admin/meta/at/listAdapterTypes")
    public Response<? extends Map> listAdapterTypes(@RequestParam(required = false) Integer pageNumber,
                                                    @RequestParam(required = false) Integer pageSize,
                                                    @RequestParam(required = false) String language) {
        // 查询条件
        AdapterTypeMainQueryCondition queryCondition = new AdapterTypeMainQueryCondition();
        queryCondition.setLanguage(language);

        // 分页条件
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber, 1);
        pageQuery.setPageSize(pageSize, 10);

        // 分页值转化
        IPage<AdapterTypeMain> adapterTypeMainPage = adapterTypeMainService.listAdapterTypes(PageUtil.toIPage(pageQuery), queryCondition);
        List<AdAdapterTypeMainVO> adapterTypeMainVos = adapterTypeMainPage.getRecords().stream().map(AdAdapterTypeMainVO::new).collect(Collectors.toList());

        // 分页返回值封装
        PageResult<AdapterTypeMain> adapterTypeMainPageResult = PageUtil.toPageResult(adapterTypeMainPage);
        PageResult<AdAdapterTypeMainVO> pageResult = new PageResult<>();
        pageResult.setList(adapterTypeMainVos);
        pageResult.setPageNumber(adapterTypeMainPageResult.getPageNumber());
        pageResult.setPageSize(adapterTypeMainPageResult.getPageSize());
        pageResult.setTotalPage(adapterTypeMainPageResult.getTotalPage());
        pageResult.setTotalCount(adapterTypeMainPageResult.getTotalCount());

        return Response.ok(pageResult);
    }

    /**
     * 获取所有的适配类型
     *
     * @return
     */
    @LogAttr(desc = "获取所有的适配类型", logType = LogType.query)
    @Permission(code = "store.meta.adapterTypeMain.listAllAdapterTypes", desc = "获取所有的适配类型")
    @GetMapping("/admin/meta/at/listAllAdapterTypes")
    public Response<? extends Map> listAllAdapterTypes() {
        List<AdapterTypeMain> adapterTypeMains = adapterTypeMainService.listAllAdapterTypes();

        // 数据结构转换
        List<AdAdapterTypeMainVO> adapterTypeMainVos = adapterTypeMains.stream().map(AdAdapterTypeMainVO::new).collect(Collectors.toList());
        return Response.ok("adapterTypeMains", adapterTypeMainVos);
    }

    /**
     * 新增 adapter type
     *
     * @param adapterTypeMainParam
     * @return
     */
    @LogAttr(desc = "新增适配器类型")
    @Permission(code = "store.meta.adapterTypeMain.createAdapterType", desc = "新增适配器类型")
    @PostMapping("/admin/meta/at/createAdapterType")
    public Response<Object> createAdapterType(@RequestBody AdapterTypeMainDTO adapterTypeMainParam) {
        AdapterTypeMain adapterTypeMain = adapterTypeMainParam.getPojoObject();
        if (StringUtil.isBlank(adapterTypeMain.getAdapterTypeName())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // save
        adMetaAdapterTypeCachePack.createAdapterType(adapterTypeMain);
        return Response.ok();
    }

    /**
     * 更新 adapter type
     *
     * @param adapterTypeMainParam
     * @return
     */
    @LogAttr(desc = "更新适配器类型")
    @Permission(code = "store.meta.adapterTypeMain.updateAdapterType", desc = "更新适配器类型")
    @PostMapping("/admin/meta/at/updateAdapterType")
    public Response<Object> updateAdapterType(@RequestBody AdapterTypeMainDTO adapterTypeMainParam) {
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeMainParam.getId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        // update
        adMetaAdapterTypeCachePack.updateAdapterType(cachePutKeyParameter, adapterTypeMainParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 删除 adapter type
     *
     * @param adapterTypeMainParam
     * @return
     */
    @LogAttr(desc = "删除适配器类型")
    @Permission(code = "store.meta.adapterTypeMain.deleteAdapterType", desc = "删除适配器类型")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN)
    @PostMapping("/admin/meta/at/deleteAdapterType")
    public Response<Object> deleteAdapterType(@RequestBody AdapterTypeMainDTO adapterTypeMainParam) {
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeMainParam.getId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }
        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        // 搜索数据同步封装 需要先获取到产品ID 不能直接同步 adapterTypeId 因为是删除
        List<ProductAdapterType> productAdapterTypes = productAdapterTypeService.listByAdapterType(adapterTypeMain.getId());
        List<Integer> productIds = productAdapterTypes.stream().map(ProductAdapterType::getProductId).collect(Collectors.toList());

        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setProductIds(productIds);
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.DeleteSpecialHandling);
        SearchDataChangeContext.set(searchDataChangeContext);

        // remove
        adMetaAdapterTypeCachePack.removeAdapterType(cachePutKeyParameter, adapterTypeMain);

        return Response.ok();
    }

    /**
     * 启用 adapter type
     *
     * @param adapterTypeMainParam
     * @return
     */
    @LogAttr(desc = "启用适配器类型")
    @Permission(code = "store.meta.adapterTypeMain.enableAdapterType", desc = "启用适配器类型")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN)
    @PostMapping("/admin/meta/at/enableAdapterType")
    public Response<Object> enableAdapterType(@RequestBody AdapterTypeMainDTO adapterTypeMainParam) {
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeMainParam.getId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeContext);
        // enable
        adMetaAdapterTypeCachePack.enableAdapterType(cachePutKeyParameter, adapterTypeMain);
        return Response.ok();
    }

    /**
     * 禁用 adapter type
     *
     * @param adapterTypeMainParam
     * @return
     */
    @LogAttr(desc = "禁用适配器类型")
    @Permission(code = "store.meta.adapterTypeMain.disableAdapterType", desc = "禁用适配器类型")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN)
    @PostMapping("/admin/meta/at/disableAdapterType")
    public Response<Object> disableAdapterType(@RequestBody AdapterTypeMainDTO adapterTypeMainParam) {
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeMainParam.getId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeContext);

        // delete
        adMetaAdapterTypeCachePack.disableAdapterType(cachePutKeyParameter, adapterTypeMain);
        return Response.ok();
    }
}
