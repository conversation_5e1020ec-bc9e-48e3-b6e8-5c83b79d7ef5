package com.insta360.store.admin.controller.test.wxq.dto;

import com.insta360.store.business.configuration.validation.annotation.MinIntegerNumber;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/17
 */
public class CopyCommodityDTO implements Serializable {

    @NotNull
    @MinIntegerNumber
    private Integer productId;

    @Size(min = 1)
    private List<Integer> commodityIds;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<Integer> getCommodityIds() {
        return commodityIds;
    }

    public void setCommodityIds(List<Integer> commodityIds) {
        this.commodityIds = commodityIds;
    }
}
