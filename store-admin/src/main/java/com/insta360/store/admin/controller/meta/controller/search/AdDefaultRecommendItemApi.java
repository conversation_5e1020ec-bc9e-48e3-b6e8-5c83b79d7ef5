package com.insta360.store.admin.controller.meta.controller.search;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.meta.dto.ProductCategoryImageTextSelectorDTO;
import com.insta360.store.business.meta.dto.SearchRecommendItemDTO;
import com.insta360.store.business.meta.model.ProductCategoryImageTextSelector;
import com.insta360.store.business.meta.model.SearchDefaultRecommendItem;
import com.insta360.store.business.meta.service.SearchDefaultRecommendItemService;
import com.insta360.store.business.meta.service.SearchRecommendItemTextService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.Errors;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/14 下午3:04
 */
@PermissionResource(code = "searchDefaultRecommendItem", desc = "搜索推荐位子项")
@RestController
public class AdDefaultRecommendItemApi {

    @Autowired
    SearchDefaultRecommendItemService searchDefaultRecommendItemService;

    /**
     * 新增搜索推荐项
     *
     * @return
     */
    @LogAttr(desc = "新增搜索推荐项", logType = LogType.update)
    @Permission(code = "store.search.recommendItem.insert", desc = "新增搜索推荐项")
    @PostMapping(path = "/admin/search/recommendItem/insert", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> insertRecommendItem(@RequestBody SearchRecommendItemDTO searchRecommendItemParam) {
        // 参数校验
        if (Objects.isNull(searchRecommendItemParam.getCommodityId()) && StringUtil.isBlank(searchRecommendItemParam.getActivityLink())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "必填项不能为空");
        }

        searchDefaultRecommendItemService.insertDefaultRecommendItem(searchRecommendItemParam);
        return Response.ok();
    }

    /**
     * 编辑搜索推荐项
     *
     * @param searchRecommendItemParam
     * @return
     */
    @LogAttr(desc = "编辑搜索推荐项", logType = LogType.update)
    @Permission(code = "store.search.recommendItem.update", desc = "编辑搜索推荐项")
    @PostMapping(path = "/admin/search/recommendItem/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateRecommendItem(@RequestBody SearchRecommendItemDTO searchRecommendItemParam) {
        // 参数校验
        if (Objects.isNull(searchRecommendItemParam.getCommodityId()) && StringUtil.isBlank(searchRecommendItemParam.getActivityLink())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "必填项不能为空");
        }

        // 先反查
        SearchDefaultRecommendItem recommendItem = searchDefaultRecommendItemService.getById(searchRecommendItemParam.getId());
        if (Objects.isNull(recommendItem)) {
            return Response.failed("该记录已被删除");
        }

        searchDefaultRecommendItemService.updateDefaultRecommendItem(searchRecommendItemParam);
        return Response.ok();
    }

    /**
     * 搜索推荐项启用/禁用
     *
     * @param searchRecommendItemParam
     * @return
     */
    @LogAttr(desc = "搜索推荐项启用或禁用", logType = LogType.update)
    @Permission(code = "store.search.recommendItem.enabled", desc = "搜索推荐项启用或禁用")
    @PostMapping(path = "/admin/search/recommendItem/enabled", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> enabledOrDisabledRecommendItem(@RequestBody SearchRecommendItemDTO searchRecommendItemParam) {
        // 参数校验
        if (Objects.isNull(searchRecommendItemParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "参数异常");
        }

        if (Objects.isNull(searchRecommendItemParam.getId()) || Objects.isNull(searchRecommendItemParam.getEnabled())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "必填项不能为空");
        }

        // 先反查
        SearchDefaultRecommendItem recommendItem = searchDefaultRecommendItemService.getById(searchRecommendItemParam.getId());
        if (Objects.isNull(recommendItem)) {
            return Response.failed("该记录已被删除");
        }

        searchDefaultRecommendItemService.enabledOrDisabledRecommendItem(searchRecommendItemParam);
        return Response.ok();
    }

    /**
     * 搜索推荐项排序
     *
     * @param searchRecommendItemParam
     * @return
     */
    @LogAttr(desc = "搜索推荐项排序", logType = LogType.update)
    @Permission(code = "store.search.recommendItem.sort", desc = "搜索推荐项排序")
    @PostMapping(path = "/admin/search/recommendItem/sort", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> sortRecommendItem(@RequestBody SearchRecommendItemDTO searchRecommendItemParam) {
        // 参数校验
        if (Objects.isNull(searchRecommendItemParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "参数异常");
        }

        int[] sortArray = searchRecommendItemParam.getSortArray();
        if (sortArray.length == 0) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "必填项不能为空");
        }

        // 遍历数组，组装参数
        List<SearchDefaultRecommendItem> searchDefaultRecommendItems = new ArrayList<>();
        int len = sortArray.length;
        for (int i = 0; i < len; i++) {
            SearchDefaultRecommendItem searchDefaultRecommendItem = new SearchDefaultRecommendItem();
            searchDefaultRecommendItem.setId(sortArray[i]);
            searchDefaultRecommendItem.setSort(len - i - 1);
            searchDefaultRecommendItems.add(searchDefaultRecommendItem);
        }
        searchDefaultRecommendItemService.sortDefaultRecommendItem(searchDefaultRecommendItems);
        return Response.ok();
    }

    /**
     * 根据推荐项id删除
     *
     * @return
     */
    @LogAttr(desc = "根据推荐项id删除")
    @Permission(code = "store.search.recommendItem.delete", desc = "根据推荐项id删除")
    @PostMapping(path = "/admin/search/recommendItem/delete", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> deleteRecommendItem(@RequestBody SearchRecommendItemDTO searchRecommendItemParam) {
        // 参数校验
        if (Objects.isNull(searchRecommendItemParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "参数异常");
        }

        // 排序数组sortArray允许为空，相当于子项全部被删除
        if (Objects.isNull(searchRecommendItemParam.getId())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "必填项不能为空");
        }

        // 先反差记录是否存在，不存在给出提示
        SearchDefaultRecommendItem recommendItem = searchDefaultRecommendItemService.getById(searchRecommendItemParam.getId());
        if (Objects.isNull(recommendItem)) {
            return Response.failed("该记录已被删除");
        }

        searchDefaultRecommendItemService.deleteDefaultRecommendItem(searchRecommendItemParam.getId(), searchRecommendItemParam.getSortArray());
        return Response.ok();
    }
}
