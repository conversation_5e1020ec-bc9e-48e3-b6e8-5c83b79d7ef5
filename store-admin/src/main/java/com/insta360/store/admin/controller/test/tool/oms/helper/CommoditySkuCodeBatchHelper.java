package com.insta360.store.admin.controller.test.tool.oms.helper;

import com.google.common.collect.Lists;
import com.insta360.store.business.commodity.enums.SkuCodeType;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/5/20
 */
@Component
public class CommoditySkuCodeBatchHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommoditySkuCodeBatchHelper.class);

    @Autowired
    BundleCommodityDetailService bundleCommodityDetailService;

    @Autowired
    ProductCommoditySkuCodeService productCommoditySkuCodeService;

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    ProductCommoditySkuCodeCountryService productCommoditySkuCodeCountryService;

    @Autowired
    ProductCommoditySkuCodeDetailService productCommoditySkuCodeDetailService;

    @Resource(name = "searchSyncExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchInitBundleSkuCode(List<Integer> bundleCommodityIds) {
        // 子明细套餐列表
        List<BundleCommodityDetail> childBundleCommodityDetails = bundleCommodityDetailService.listByBundleCommodityId(bundleCommodityIds);
        Map<Integer, List<BundleCommodityDetail>> bundleCommodityDetailsMap = childBundleCommodityDetails.stream().collect(Collectors.groupingBy(BundleCommodityDetail::getCommodityId, Collectors.mapping(bundleCommodityDetail -> bundleCommodityDetail, Collectors.toList())));

        // 组合商品料号列表
        List<CommodityCode> commodityCodeList = commodityCodeService.listCommodityCodeGroup(bundleCommodityIds);

        List<ProductCommoditySkuCode> productCommoditySkuCodeList = new ArrayList<>(commodityCodeList.size());
        // 构建组合套餐料号适配地区Map、组合套餐料号po列表
        Map<String, List<ProductCommoditySkuCodeCountry>> skuCodeCountryMap = new HashMap<>();
        commodityCodeList.forEach(commodityCode -> {
            if (StringUtils.isBlank(commodityCode.getArea())) {
                return;
            }
            // 料号适配国家列表
            List<String> countryCodes = Lists.newArrayList(commodityCode.getArea().split(","));
            List<ProductCommoditySkuCodeCountry> commoditySkuCodeCountryList = countryCodes.stream().map(countryCode -> new ProductCommoditySkuCodeCountry(countryCode)).collect(Collectors.toList());
            skuCodeCountryMap.putIfAbsent(String.format("%s#%s", commodityCode.getCommodity(), commodityCode.getCode()), commoditySkuCodeCountryList);

            ProductCommoditySkuCode productCommoditySkuCode = new ProductCommoditySkuCode(commodityCode.getCommodity(), commodityCode.getCode(), SkuCodeType.ASSEMBLE_DEFAULT_SKU.getType());
            productCommoditySkuCodeList.add(productCommoditySkuCode);
        });

        //  批量保存料号记录
        int i = productCommoditySkuCodeService.batchSaveSkuCodes(productCommoditySkuCodeList);
        LOGGER.info("线程名称:{} 处理完成, 组合套餐料号保存成功，共计:{}条, 处理完成:{}条", Thread.currentThread().getName(), productCommoditySkuCodeList.size(), i);
        FeiShuMessageUtil.storeGeneralMessage(String.format("线程名称:{%s} 处理完成, 组合套餐料号保存成功，共计:{%s}条, 处理完成:{%s}条", Thread.currentThread().getName(), productCommoditySkuCodeList.size(), i), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);

        List<Integer> childCommodityIds = bundleCommodityDetailsMap.values().stream().flatMap(List::stream).map(BundleCommodityDetail::getSubCommodityId).distinct().collect(Collectors.toList());
        List<ProductCommoditySkuCode> childCommoditySkuCodes = productCommoditySkuCodeService.listSkuCodeByCommodityIds(childCommodityIds);
        Map<Integer, List<ProductCommoditySkuCode>> childCommoditySkuCodeMap = childCommoditySkuCodes.stream().collect(Collectors.groupingBy(ProductCommoditySkuCode::getCommodityId, Collectors.mapping(commoditySkuCode -> commoditySkuCode, Collectors.toList())));

        List<ProductCommoditySkuCodeDetail> productCommoditySkuCodeDetails = new ArrayList<>();
        List<ProductCommoditySkuCodeCountry> productCommoditySkuCodeCountryList = new ArrayList<>();
        for (ProductCommoditySkuCode productCommoditySkuCode : productCommoditySkuCodeList) {
            // 子明细
            List<ProductCommoditySkuCodeDetail> childSkuCodeDetails = Optional.ofNullable(bundleCommodityDetailsMap.get(productCommoditySkuCode.getCommodityId()))
                    .filter(CollectionUtils::isNotEmpty)
                    .map(bundleCommodityDetails -> bundleCommodityDetails.stream().map(bundleCommodityDetail -> {
                        List<ProductCommoditySkuCode> commoditySkuCodes = childCommoditySkuCodeMap.get(bundleCommodityDetail.getSubCommodityId());
                        if (CollectionUtils.isNotEmpty(commoditySkuCodes)) {
                            ProductCommoditySkuCode childCommoditySkuCode = commoditySkuCodes.get(0);
                            return new ProductCommoditySkuCodeDetail(productCommoditySkuCode.getId(), productCommoditySkuCode.getCommodityId(), childCommoditySkuCode.getId(), childCommoditySkuCode.getCommodityId(), bundleCommodityDetail.getNumber());
                        } else {
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList())).orElse(null);

            if (CollectionUtils.isEmpty(childSkuCodeDetails)) {
                LOGGER.info("套餐ID:[],料号:[] 没有配置子明细", productCommoditySkuCode.getCommodityId(), productCommoditySkuCode.getSkuCode());
                FeiShuMessageUtil.storeGeneralMessage(String.format("套餐ID:[%s],料号:[%s] 没有配置子明细", productCommoditySkuCode.getCommodityId(), productCommoditySkuCode.getSkuCode()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            } else {
                productCommoditySkuCodeDetails.addAll(childSkuCodeDetails);
            }

            // 适配地区
            List<ProductCommoditySkuCodeCountry> countryList = skuCodeCountryMap.get(String.format("%s#%s", productCommoditySkuCode.getCommodityId(), productCommoditySkuCode.getSkuCode()));
            if (CollectionUtils.isEmpty(countryList)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("套餐ID:[%s],料号:[%s] 没有配置适配国家/地区", productCommoditySkuCode.getCommodityId(), productCommoditySkuCode.getSkuCode()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            } else {
                countryList.forEach(productCommoditySkuCodeCountry -> productCommoditySkuCodeCountry.setSkuId(productCommoditySkuCode.getId()));
                productCommoditySkuCodeCountryList.addAll(countryList);
            }
        }

        String currentThreadName = Thread.currentThread().getName();
        if (CollectionUtils.isNotEmpty(productCommoditySkuCodeCountryList)) {
            int b = productCommoditySkuCodeCountryService.batchSaveSkuCodeCountry(productCommoditySkuCodeCountryList);
            LOGGER.info("线程名称:{} 处理完成, 组合商品适配适配国家/地区保存成功，共计:{}条, 处理完成{}条", currentThreadName, productCommoditySkuCodeCountryList.size(), b);
            FeiShuMessageUtil.storeGeneralMessage(String.format("线程名称:{%s} 处理完成, 组合商品适配适配国家/地区保存成功，共计:{%s}条, 处理完成{%s}条", currentThreadName, productCommoditySkuCodeCountryList.size(), b), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }

        if (CollectionUtils.isNotEmpty(productCommoditySkuCodeDetails)) {
            int c = productCommoditySkuCodeDetailService.batchSaveSkuCodeDetail(productCommoditySkuCodeDetails);
            LOGGER.info("线程名称:{} 处理完成, 组合商品默认料号详情保存成功，共计:{}条, 处理完成{}条", currentThreadName, productCommoditySkuCodeDetails.size(), c);
            FeiShuMessageUtil.storeGeneralMessage(String.format("线程名称:{%s} 处理完成, 组合商品料号详情保存成功，共计:{%s}条, 处理完成{}条", currentThreadName, productCommoditySkuCodeDetails.size(), c), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void batchInitSingleSkuCode(List<Integer> singleCommodityIds) {
        // 单商品列表
        List<CommodityCode> commodityCodeList = commodityCodeService.listCommodityCodeGroup(singleCommodityIds);

        List<ProductCommoditySkuCode> productCommoditySkuCodeList = new ArrayList<>(commodityCodeList.size());
        Map<String, List<ProductCommoditySkuCodeCountry>> skuCodeCountryMap = new HashMap<>();

        commodityCodeList.forEach(commodityCode -> {
            if (StringUtils.isBlank(commodityCode.getArea())) {
                return;
            }
            // 料号适配国家列表
            List<String> countryCodes = Lists.newArrayList(commodityCode.getArea().split(","));
            List<ProductCommoditySkuCodeCountry> commoditySkuCodeCountryList = countryCodes.stream().map(countryCode -> new ProductCommoditySkuCodeCountry(countryCode)).collect(Collectors.toList());
            skuCodeCountryMap.putIfAbsent(String.format("%s#%s", commodityCode.getCommodity(), commodityCode.getCode()), commoditySkuCodeCountryList);

            ProductCommoditySkuCode productCommoditySkuCode = new ProductCommoditySkuCode(commodityCode.getCommodity(), commodityCode.getCode(), SkuCodeType.SINGLE_SKU.getType());
            productCommoditySkuCodeList.add(productCommoditySkuCode);
        });

        String currentThreadName = Thread.currentThread().getName();
        //  批量保存料号记录
        int i = productCommoditySkuCodeService.batchSaveSkuCodes(productCommoditySkuCodeList);
        LOGGER.info("线程名称:{} 处理完成, 单套餐料号保存成功，共计:{}条, 处理完成:{}条", currentThreadName, productCommoditySkuCodeList.size(), i);
        FeiShuMessageUtil.storeGeneralMessage(String.format("线程名称:{%s} 处理完成, 单套餐料号保存成功，共计:{%s}条, 处理完成:{%s}条", currentThreadName, productCommoditySkuCodeList.size(), i), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);


        List<ProductCommoditySkuCodeCountry> commoditySkuCodeCountryList = productCommoditySkuCodeList
                .stream()
                .map(productCommoditySkuCode -> {
                    List<ProductCommoditySkuCodeCountry> countryList = skuCodeCountryMap.get(String.format("%s#%s", productCommoditySkuCode.getCommodityId(), productCommoditySkuCode.getSkuCode()));
                    if (CollectionUtils.isEmpty(countryList)) {
                        FeiShuMessageUtil.storeGeneralMessage(String.format("套餐ID:[%s],料号:[%s] 没有配置适配国家/地区", productCommoditySkuCode.getCommodityId(), productCommoditySkuCode.getSkuCode()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                        return null;
                    }
                    countryList.forEach(productCommoditySkuCodeCountry -> productCommoditySkuCodeCountry.setSkuId(productCommoditySkuCode.getId()));
                    return countryList;
                })
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 批量保存料号适配国家记录
        int b = productCommoditySkuCodeCountryService.batchSaveSkuCodeCountry(commoditySkuCodeCountryList);
        LOGGER.info("线程名称:{} 处理完成, 单套餐料号适配适配国家/地区保存成功，共计:{}条, 处理完成{}条", currentThreadName, commoditySkuCodeCountryList.size(), b);
        FeiShuMessageUtil.storeGeneralMessage(String.format("线程名称:{%s} 处理完成, 单套餐料号适配适配国家/地区保存成功，共计:{%s}条, 处理完成{%s}条", currentThreadName, commoditySkuCodeCountryList.size(), b), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
    }
}
