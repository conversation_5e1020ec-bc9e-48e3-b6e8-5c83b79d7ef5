package com.insta360.store.admin.config;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.insta360.compass.admin.audit.log.aspect.LogAspect;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.web.config.BaseWebMvcConfigure;
import com.insta360.store.admin.common.StoreApiHeaderParser;
import com.insta360.store.admin.common.interceptor.*;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: mowi
 * @Date: 2018/11/26
 * @Description:
 */
@Configuration
public class WebConfiguration extends BaseWebMvcConfigure {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        super.addInterceptors(registry);

        // 项目自定义的拦截器必须在super方法之后定义，因为可能会依赖父类中定义的拦截器
        registry.addInterceptor(adminInterceptor());

        // 有可能依赖用户的信息
        registry.addInterceptor(orderInterceptor());
        registry.addInterceptor(tradeUpOrderInterceptor());
        registry.addInterceptor(createCareCardInterceptor());
        registry.addInterceptor(reviewStateChangeInterceptor());
        registry.addInterceptor(cardRecordInterceptor());
        registry.addInterceptor(searchDataChangeSyncInterceptor());
        registry.addInterceptor(traceLogInterceptor());
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        super.configureMessageConverters(converters);

        // 把返回值转换成xml
        converters.add(new StringHttpMessageConverter());
    }

    @Bean
    public StoreApiHeaderParser storeApiHeaderParser() {
        return new StoreApiHeaderParser();
    }

    @Bean
    public AdminInterceptor adminInterceptor() {
        return new AdminInterceptor();
    }

    @Bean
    public OrderInterceptor orderInterceptor() {
        return new OrderInterceptor(ApplicationContextHolder.getApplicationContext());
    }

    @Bean
    public TradeUpOrderInterceptor tradeUpOrderInterceptor() {
        return new TradeUpOrderInterceptor(ApplicationContextHolder.getApplicationContext());
    }

    @Bean
    public CreateCareCardInterceptor createCareCardInterceptor() {
        return new CreateCareCardInterceptor();
    }

    @Bean
    public BusinessLogInterceptor createOperateLogInterceptor() {
        return new BusinessLogInterceptor();
    }

    @Bean
    public ReviewStateChangeInterceptor reviewStateChangeInterceptor() {
        return new ReviewStateChangeInterceptor();
    }

    @Bean
    public CardRecordInterceptor cardRecordInterceptor() {
        return new CardRecordInterceptor();
    }

    @Bean
    public SearchDataChangeSyncInterceptor searchDataChangeSyncInterceptor() {
        return new SearchDataChangeSyncInterceptor();
    }

    @Bean
    public TraceLogInterceptor traceLogInterceptor() {
        return new TraceLogInterceptor();
    }

    /**
     * 自定义 FastJson Serializer Converters
     *
     * @return
     */
    @Bean
    public HttpMessageConverters fastJsonConverters() {
        FastJsonHttpMessageConverter fastJsonConverter = new FastJsonHttpMessageConverter();

        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(SerializerFeature.DisableCircularReferenceDetect);
        fastJsonConverter.setFastJsonConfig(fastJsonConfig);
        // 请求支持类型
        fastJsonConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, new MediaType("application", "*+json")));

        return new HttpMessageConverters(fastJsonConverter);
    }

    @Bean("commonLogAspect")
    public LogAspect logAspect() {
        return new LogAspect();
    }
}
