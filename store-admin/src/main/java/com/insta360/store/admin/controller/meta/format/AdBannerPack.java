package com.insta360.store.admin.controller.meta.format;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.store.admin.controller.meta.vo.AdBannerMainVO;
import com.insta360.store.business.meta.dto.BannerQueryDTO;
import com.insta360.store.business.meta.dto.condition.BannerQueryCondition;
import com.insta360.store.business.meta.model.BannerInfo;
import com.insta360.store.business.meta.model.BannerMain;
import com.insta360.store.business.meta.service.BannerMainService;
import com.insta360.store.business.meta.service.impl.helper.BannerHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: py
 * @create: 2023-10-18 12:03
 */
@Component
public class AdBannerPack {

    @Autowired
    BannerMainService bannerMainService;

    @Autowired
    BannerHelper bannerHelper;

    /**
     * 多条件获取banner信息
     *
     * @param bannerQueryParam
     * @return
     */
    public PageResult<AdBannerMainVO> listBanners(BannerQueryDTO bannerQueryParam) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(bannerQueryParam.getPageSize(), 15);
        pageQuery.setPageNumber(bannerQueryParam.getPageNumber(), 1);

        BannerQueryCondition condition = new BannerQueryCondition();
        condition.setBannerName(bannerQueryParam.getBannerName());
        condition.setCountry(bannerQueryParam.getCountry());
        condition.setLanguage(bannerQueryParam.getLanguage());
        condition.setApp(bannerQueryParam.getApp());

        IPage<BannerMain> bannerMainPage = bannerMainService.listBannerMainPage(PageUtil.toIPage(pageQuery), condition);
        List<BannerMain> bannerMains = bannerMainPage.getRecords();
        Map<Integer, List<BannerInfo>> bannerInfoMap = bannerHelper.getIntegerListMap(bannerMains);

        List<AdBannerMainVO> bannerMainVos = new ArrayList<>();

        for (BannerMain bannerMain : bannerMains) {
            StringBuilder languages = new StringBuilder();
            StringBuilder countries = new StringBuilder();
            AdBannerMainVO bannerMainVo = new AdBannerMainVO(bannerMain);
            List<BannerInfo> infos = bannerInfoMap.get(bannerMain.getId());
            if (infos != null) {
                for (BannerInfo info : infos) {
                    languages.append(info.getLanguage()).append(",");
                    countries.append(info.getCountry()).append(",");
                }
                if (languages.length() > 0) {
                    bannerMainVo.setCountry(countries.deleteCharAt(countries.length() - 1).toString());
                    bannerMainVo.setLanguage(languages.deleteCharAt(languages.length() - 1).toString());
                }
            }
            bannerMainVo.setInfoList(infos);
            bannerMainVos.add(bannerMainVo);
        }

        PageResult<BannerMain> bannerMainPageResult = PageUtil.toPageResult(bannerMainPage);
        PageResult<AdBannerMainVO> pageResult = new PageResult<>();
        pageResult.setList(bannerMainVos);
        pageResult.setPageNumber(bannerMainPageResult.getPageNumber());
        pageResult.setTotalCount(bannerMainPageResult.getTotalCount());
        pageResult.setTotalPage(bannerMainPageResult.getTotalPage());
        pageResult.setPageSize(bannerMainPageResult.getPageSize());
        return pageResult;
    }
}
