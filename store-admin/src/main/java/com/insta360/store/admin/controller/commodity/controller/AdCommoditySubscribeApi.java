package com.insta360.store.admin.controller.commodity.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.commodity.format.AdCommodityPack;
import com.insta360.store.admin.controller.commodity.vo.AdCommodityVO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: hyc
 * @Date: 2019-08-05
 * @Description: 套餐订阅
 */
@RestController
@PermissionResource(code = "commoditySubscribe", desc = "套餐订阅")
public class AdCommoditySubscribeApi extends BaseAdminApi {

    @Autowired
    CommodityService commodityService;

    @Autowired
    AdCommodityPack adCommodityPack;

    /**
     * 获取套餐订阅信息
     *
     * @param commodityId
     * @return
     */
    @LogAttr(desc = "获取套餐订阅信息",logType = LogType.query)
    @Permission(code = "store.commodity.commoditySubscribe.getSubscribe", desc = "获取套餐订阅信息")
    @GetMapping("/admin/commodity/getSubscribe")
    public Response<? extends Map> getSubscribe(@RequestParam(required = false, value = "commodity_id") Integer commodityId) {
        Commodity commodity = commodityService.getById(commodityId);

        AdCommodityPack.PackSetting packSetting = new AdCommodityPack.PackSetting(this);
        packSetting.setWithSubscribe(true);

        AdCommodityVO commodityVO = adCommodityPack.doPack(commodity, packSetting);
        return Response.ok("subscribes", commodityVO.getSubscribeQuantityMap());
    }
}
