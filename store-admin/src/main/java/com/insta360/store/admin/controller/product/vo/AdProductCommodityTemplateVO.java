package com.insta360.store.admin.controller.product.vo;

import java.util.List;

/**
 * @description:
 * @author: py
 * @create: 2022-09-21 17:57
 */
public class AdProductCommodityTemplateVO {
    /**
     * 产品id
     */
    private Integer productId;
    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 语言
     */
    private String language;

    /**
     * 具体的模板信息
     */
    private List<AdProductOverviewVO> productOverviews;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public List<AdProductOverviewVO> getProductOverviews() {
        return productOverviews;
    }

    public void setProductOverviews(List<AdProductOverviewVO> productOverviews) {
        this.productOverviews = productOverviews;
    }

    @Override
    public String toString() {
        return "AdProductComodityTemplateVO{" +
                "productId=" + productId +
                ", commodityId=" + commodityId +
                ", language='" + language + '\'' +
                ", productOverviews=" + productOverviews +
                '}';
    }
}
