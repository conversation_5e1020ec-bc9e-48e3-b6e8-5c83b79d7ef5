package com.insta360.store.admin.controller.meta.format;

import com.insta360.store.admin.controller.meta.vo.tax.CustomerInvoiceTaxRateVO;
import com.insta360.store.business.meta.model.OverseasOrderInvoiceTaxRate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/29
 */
@Component
public class AdOverseasOrderInvoiceTaxRatePack {

    /**
     * 封装
     *
     * @param overseasOrderInvoiceTaxRate
     * @return
     */
    public CustomerInvoiceTaxRateVO doPack(OverseasOrderInvoiceTaxRate overseasOrderInvoiceTaxRate) {
        if (Objects.isNull(overseasOrderInvoiceTaxRate)) {
            return null;
        }

        CustomerInvoiceTaxRateVO customerInvoiceTaxRateVo = new CustomerInvoiceTaxRateVO();
        BeanUtils.copyProperties(overseasOrderInvoiceTaxRate, customerInvoiceTaxRateVo);

        return customerInvoiceTaxRateVo;
    }
}
