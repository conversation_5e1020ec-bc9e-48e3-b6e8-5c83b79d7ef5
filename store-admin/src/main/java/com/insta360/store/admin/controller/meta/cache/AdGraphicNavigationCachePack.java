package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.dto.GraphicNavigationDTO;
import com.insta360.store.business.meta.model.GraphicNavigationMain;
import com.insta360.store.business.meta.service.GraphicNavigationMainService;
import com.insta360.store.business.meta.service.GraphicNavigationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2023/3/16
 * @Description:
 */
@Component
public class AdGraphicNavigationCachePack {

    @Autowired
    GraphicNavigationMainService graphicNavigationMainService;

    @Autowired
    GraphicNavigationService graphicNavigationService;

    /**
     * 更新图文导航
     *
     * @param cachePutKeyParameter
     * @param graphicNavigationParam
     */
    @CachePutMonitor(cacheableType = CachePutType.GRAPHIC_NAVIGATION)
    public void updateGraphicNavigation(CachePutKeyParameterBO cachePutKeyParameter, GraphicNavigationDTO graphicNavigationParam) {
        graphicNavigationService.updateGraphicNavigation(graphicNavigationParam);
    }

    /**
     * 删除图文导航
     *
     * @param cachePutKeyParameter
     * @param navigationMainId
     */
    @CachePutMonitor(cacheableType = CachePutType.GRAPHIC_NAVIGATION)
    public void delGraphicNavigationMain(CachePutKeyParameterBO cachePutKeyParameter, Integer navigationMainId) {
        graphicNavigationMainService.delGraphicNavigationMain(navigationMainId);
    }

    /**
     * 启用图文导航
     *
     * @param cachePutKeyParameter
     * @param navigationMain
     */
    @CachePutMonitor(cacheableType = CachePutType.GRAPHIC_NAVIGATION)
    public void enableGraphicNavigation(CachePutKeyParameterBO cachePutKeyParameter, GraphicNavigationMain navigationMain) {
        graphicNavigationMainService.enableGraphicNavigation(navigationMain);
    }

    /**
     * 禁用图文导航
     *
     * @param cachePutKeyParameter
     * @param navigationMain
     */
    @CachePutMonitor(cacheableType = CachePutType.GRAPHIC_NAVIGATION)
    public void disableGraphicNavigation(CachePutKeyParameterBO cachePutKeyParameter, GraphicNavigationMain navigationMain) {
        graphicNavigationMainService.disableGraphicNavigation(navigationMain);
    }

    /**
     * 图文导航排序
     *
     * @param cachePutKeyParameter
     * @param graphicNavigationMains
     * @param orderIndex
     * @param toIndex
     */
    @CachePutMonitor(cacheableType = CachePutType.GRAPHIC_NAVIGATION)
    public void updateGnOrderIndex(CachePutKeyParameterBO cachePutKeyParameter, List<GraphicNavigationMain> graphicNavigationMains
            , Integer orderIndex, Integer toIndex) {
        graphicNavigationMainService.updateGnOrderIndex(graphicNavigationMains, orderIndex, toIndex);
    }
}
