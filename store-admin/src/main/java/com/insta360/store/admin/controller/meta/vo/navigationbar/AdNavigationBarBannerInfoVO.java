package com.insta360.store.admin.controller.meta.vo.navigationbar;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarBannerInfo;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/5/30
 * @Description:
 */
public class AdNavigationBarBannerInfoVO implements Serializable {

    private Integer id;

    /**
     * 关联活动id
     */
    private Integer bannerSubsetId;

    /**
     * 地区
     */
    private String country;

    /**
     * 语言
     */
    private String language;

    public AdNavigationBarBannerInfoVO() {
    }

    public AdNavigationBarBannerInfoVO(NavigationBarBannerInfo bannerInfo) {
        if (bannerInfo != null) {
            BeanUtil.copyProperties(bannerInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBannerSubsetId() {
        return bannerSubsetId;
    }

    public void setBannerSubsetId(Integer bannerSubsetId) {
        this.bannerSubsetId = bannerSubsetId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return "AdNavigationBarBannerInfoVO{" +
                "id=" + id +
                ", bannerSubsetId=" + bannerSubsetId +
                ", country='" + country + '\'' +
                ", language='" + language + '\'' +
                '}';
    }
}
