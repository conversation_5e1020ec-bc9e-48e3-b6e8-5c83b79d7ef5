package com.insta360.store.admin.controller.meta.vo.tax;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/29
 */
public class CustomerInvoiceTaxRateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 国家地区二字码
     */
    private String countryCode;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }


    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    @Override
    public String toString() {
        return "CustomerInvoiceTaxRateVO{" +
                "countryCode='" + countryCode + '\'' +
                ", taxRate=" + taxRate +
                '}';
    }
}
