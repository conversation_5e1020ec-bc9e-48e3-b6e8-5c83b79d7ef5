package com.insta360.store.admin.controller.test.evelyn.helper;

import com.insta360.store.business.meta.model.SceneryTagMain;
import com.insta360.store.business.meta.service.ProductCategoryScenerySectionService;
import com.insta360.store.business.meta.service.SceneryTagMainService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2025-03-18 11:01
 */
@Component
public class EvelynHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(EvelynHelper.class);

    @Autowired
    ProductCategoryScenerySectionService productCategoryScenerySectionService;

    @Autowired
    SceneryTagMainService sceneryTagMainService;

    @Async
    public void sectionSync() {
        List<SceneryTagMain> sceneryTagMainList = sceneryTagMainService.listAll();
        List<SceneryTagMain> tagMainList = sceneryTagMainList.stream().sorted(Comparator.comparing(SceneryTagMain::getId)).collect(Collectors.toList());
        tagMainList.forEach(sceneryTagMain -> {
            productCategoryScenerySectionService.addScenerySection(sceneryTagMain);
            LOGGER.info("[场景专区数据同步]新增一条:{}", sceneryTagMain);
        });
    }
}
