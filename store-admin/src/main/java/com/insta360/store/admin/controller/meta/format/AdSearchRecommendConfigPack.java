package com.insta360.store.admin.controller.meta.format;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.ErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.admin.controller.meta.vo.categoryPage.*;
import com.insta360.store.admin.controller.meta.vo.searchRecommend.AdSearchRecommendConfigVO;
import com.insta360.store.admin.controller.meta.vo.searchRecommend.AdSearchRecommendItemVO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.meta.model.*;
import com.insta360.store.business.meta.service.SearchDefaultRecommendCategoryService;
import com.insta360.store.business.meta.service.SearchDefaultRecommendItemService;
import com.insta360.store.business.meta.service.SearchRecommendCategoryTextService;
import com.insta360.store.business.meta.service.SearchRecommendItemTextService;
import com.insta360.store.business.product.model.Product;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/18 上午9:51
 */
@Component
public class AdSearchRecommendConfigPack {

    @Autowired
    SearchDefaultRecommendCategoryService recommendCategoryService;

    @Autowired
    SearchDefaultRecommendItemService recommendItemService;

    @Autowired
    SearchRecommendItemTextService itemTextService;

    @Autowired
    SearchRecommendCategoryTextService categoryTextService;

    /**
     * 封装搜索默认配置数据
     *
     * @return
     */
    public List<AdSearchRecommendConfigVO> packCategoryPage() {
        List<AdSearchRecommendConfigVO> defaultRecommendCategorySet = new ArrayList<>();
        // 查出标题项
        List<SearchDefaultRecommendCategory> defaultRecommendCategories = recommendCategoryService.list(new QueryWrapper<>());
        if (CollectionUtils.isEmpty(defaultRecommendCategories)) {
            return new ArrayList<>(0);
        }

        // 查出子项
        List<Integer> categoryIds = defaultRecommendCategories.stream().map(SearchDefaultRecommendCategory::getId).collect(Collectors.toList());
        List<SearchDefaultRecommendItem> searchDefaultRecommendItems = recommendItemService.listByCategoryIds(categoryIds);

        // 查出子项文案
        List<Integer> itemIds = searchDefaultRecommendItems.stream().map(SearchDefaultRecommendItem::getId).collect(Collectors.toList());
        // key -> 子项主键id  value -> 子项文案集合
        Map<Integer, List<SearchRecommendItemText>> itemTextInfoMap = itemTextService.listByItemIds(itemIds)
                .stream()
                .collect(Collectors.groupingBy(SearchRecommendItemText::getRecommendItemId));
        // 查出标题文案
        // key -> 标题主键id  value -> 标题文案集合
        Map<Integer, List<SearchRecommendCategoryText>> categoryTextInfoMap = categoryTextService.listByCategoryIds(categoryIds)
                .stream()
                .collect(Collectors.groupingBy(SearchRecommendCategoryText::getRecommendCategoryId));

        // 标题转VO
        for (SearchDefaultRecommendCategory searchDefaultRecommendCategory : defaultRecommendCategories) {
            AdSearchRecommendConfigVO adSearchRecommendConfig = new AdSearchRecommendConfigVO();
            BeanUtils.copyProperties(searchDefaultRecommendCategory, adSearchRecommendConfig);
            defaultRecommendCategorySet.add(adSearchRecommendConfig);
        }

        // 封装子项vo
        List<AdSearchRecommendItemVO> adSearchRecommendItemVOList = new ArrayList<>();
        for (SearchDefaultRecommendItem searchDefaultRecommendItem : searchDefaultRecommendItems) {
            AdSearchRecommendItemVO adSearchRecommendItem = new AdSearchRecommendItemVO();
            BeanUtils.copyProperties(searchDefaultRecommendItem, adSearchRecommendItem);
            // 设置子项的多语言文案
            adSearchRecommendItem.setSearchRecommendItemTexts(itemTextInfoMap.getOrDefault(searchDefaultRecommendItem.getId(), new ArrayList<>(0)));
            adSearchRecommendItemVOList.add(adSearchRecommendItem);
        }
        // key -> categoryId, value -> 子项vo集合
        Map<Integer, List<AdSearchRecommendItemVO>> recommendItemVoMap = adSearchRecommendItemVOList
                .stream().collect(Collectors.groupingBy(AdSearchRecommendItemVO::getRecommendCategoryId));

        // 标题绑定子项并返回
        for (AdSearchRecommendConfigVO adSearchRecommendConfig : defaultRecommendCategorySet) {
            List<AdSearchRecommendItemVO> adSearchRecommendItems = recommendItemVoMap.getOrDefault(adSearchRecommendConfig.getId(), new ArrayList<>(0));
            adSearchRecommendItems.forEach(item -> item.setSubType(adSearchRecommendConfig.getType()));
            // 根据sort逆序排序
            adSearchRecommendItems.sort((o1, o2) -> o2.getSort() - o1.getSort());
            adSearchRecommendConfig.setDefaultRecommendSet(adSearchRecommendItems);
            // 设置标题的多语言文案
            adSearchRecommendConfig.setSearchRecommendCategoryTexts(categoryTextInfoMap.getOrDefault(adSearchRecommendConfig.getId(), new ArrayList<>(0)));
        }
        return defaultRecommendCategorySet;
    }
}
