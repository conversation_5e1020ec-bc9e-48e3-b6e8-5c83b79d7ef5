package com.insta360.store.admin.controller.meta.vo.tradepoint;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.TradePointInfo;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/08/31
 * @Description:
 */
public class AdTradePointInfoVO {

    private Integer id;

    /**
     * trade point
     */
    private Integer tradePointId;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String content;

    /**
     * 国家
     */
    private String country;

    /**
     * 语言
     */
    private String language;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否禁用（0：未禁用；1：禁用）
     */
    private Boolean disabled;

    /**
     * 是否删除（0：未删除；1：删除）
     */
    private Boolean deleted;

    public AdTradePointInfoVO() {
    }

    public AdTradePointInfoVO(TradePointInfo tradePointInfo) {
        if (tradePointInfo != null) {
            BeanUtil.copyProperties(tradePointInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTradePointId() {
        return tradePointId;
    }

    public void setTradePointId(Integer tradePointId) {
        this.tradePointId = tradePointId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        return "AdTradePointInfoVO{" +
                "id=" + id +
                ", tradePointId=" + tradePointId +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", country='" + country + '\'' +
                ", language='" + language + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", disabled=" + disabled +
                ", deleted=" + deleted +
                '}';
    }
}
