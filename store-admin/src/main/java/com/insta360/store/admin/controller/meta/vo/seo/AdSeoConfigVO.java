package com.insta360.store.admin.controller.meta.vo.seo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.SeoConfig;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/11/3
 * @Description:
 */
public class AdSeoConfigVO implements Serializable {

    private Integer id;

    /**
     * 页面类型
     */
    private String type;

    /**
     * 页面key
     */
    private String pageUrlKey;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品key
     */
    private String productUrlKey;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 多语言
     */
    private List<AdSeoConfigInfoVO> seoConfigInfos;

    public AdSeoConfigVO() {
    }

    public AdSeoConfigVO(SeoConfig seoConfig) {
        if (seoConfig != null) {
            BeanUtil.copyProperties(seoConfig, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPageUrlKey() {
        return pageUrlKey;
    }

    public void setPageUrlKey(String pageUrlKey) {
        this.pageUrlKey = pageUrlKey;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<AdSeoConfigInfoVO> getSeoConfigInfos() {
        return seoConfigInfos;
    }

    public void setSeoConfigInfos(List<AdSeoConfigInfoVO> seoConfigInfos) {
        this.seoConfigInfos = seoConfigInfos;
    }

    public String getProductUrlKey() {
        return productUrlKey;
    }

    public void setProductUrlKey(String productUrlKey) {
        this.productUrlKey = productUrlKey;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AdSeoConfigVO{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", pageUrlKey='" + pageUrlKey + '\'' +
                ", productId=" + productId +
                ", productUrlKey='" + productUrlKey + '\'' +
                ", productName='" + productName + '\'' +
                ", createTime=" + createTime +
                ", seoConfigInfos=" + seoConfigInfos +
                '}';
    }
}
