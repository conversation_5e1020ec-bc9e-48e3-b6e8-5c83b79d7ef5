package com.insta360.store.admin.controller.faq.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.faq.cache.AdFaqOtherQuestionCachePack;
import com.insta360.store.admin.controller.faq.vo.AdFaqOtherQuestionQaBindVO;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.faq.dto.OtherQuestionQaBindDTO;
import com.insta360.store.business.faq.exception.FaqErrorCode;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInside;
import com.insta360.store.business.faq.model.FaqOtherQuestionQaBind;
import com.insta360.store.business.faq.service.FaqCategoryQuestionInsideService;
import com.insta360.store.business.faq.service.FaqOtherQuestionQaBindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/4/29
 * @Description:
 */
@PermissionResource(code = "faqOtherQuestionQaBind", desc = "Faq其他页面问题绑定")
@RestController
public class AdFaqOtherQuestionQaBindApi extends BaseAdminApi {

    @Autowired
    FaqOtherQuestionQaBindService questionQaBindService;

    @Autowired
    FaqCategoryQuestionInsideService questionInsideService;

    @Autowired
    AdFaqOtherQuestionCachePack adFaqOtherQuestionCachePack;

    /**
     * 查询页面问题绑定记录
     *
     * @param otherQuestionBindId
     * @return
     */
    @LogAttr(desc = "查询页面问题绑定记录",logType = LogType.query)
    @Permission(code = "store.faq.other.listQuestionQaBind", desc = "查询页面问题绑定记录")
    @GetMapping("/admin/faq/other/listQuestionQaBind")
    public Response<? extends Map> listQuestionQaBind(@RequestParam(required = false, value = "otherQuestionBindId") Integer otherQuestionBindId) {
        if (otherQuestionBindId == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        List<FaqOtherQuestionQaBind> questionQaBinds = questionQaBindService.listByQuestionBindId(otherQuestionBindId);
        if (CollectionUtils.isEmpty(questionQaBinds)) {
            return Response.ok();
        }
        List<Integer> categoryQuestionInsideIds = questionQaBinds.stream().map(FaqOtherQuestionQaBind::getQuestionId).collect(Collectors.toList());
        Map<Integer, String> categorySubsetInsideMap = questionInsideService.listByIds(categoryQuestionInsideIds)
                .stream().collect(Collectors.toMap(FaqCategoryQuestionInside::getId, FaqCategoryQuestionInside::getCategoryQuestionInsideName));
        // 封装返回
        List<AdFaqOtherQuestionQaBindVO> questionQaBindList = questionQaBinds.stream().map(qaBind -> {
            AdFaqOtherQuestionQaBindVO questionQaBindVO = new AdFaqOtherQuestionQaBindVO(qaBind);
            questionQaBindVO.setQuestionName(categorySubsetInsideMap.get(qaBind.getQuestionId()));
            return questionQaBindVO;
        }).collect(Collectors.toList());
        return Response.ok("faqOtherQuestionQaBinds", questionQaBindList);
    }

    /**
     * 创建页面问题绑定
     *
     * @param qaBindParam
     * @return
     */
    @LogAttr(desc = "创建页面问题绑定")
    @Permission(code = "store.faq.other.createQuestionQaBind", desc = "创建页面问题绑定")
    @PostMapping(path = "/admin/faq/other/create/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> createQuestionQaBind(@Validated @RequestBody OtherQuestionQaBindDTO qaBindParam) {
        if (qaBindParam.getQuestionId() == null || qaBindParam.getOtherQuestionBindId() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        // 防止重复绑定
        FaqOtherQuestionQaBind qaBind = questionQaBindService.getBySubSetQuestionId(qaBindParam.getQuestionId(), qaBindParam.getOtherQuestionBindId());
        if (qaBind != null) {
            throw new InstaException(FaqErrorCode.FaqQuestionQaBindAlreadyExistException);
        }
        questionQaBindService.createQuestionQaBind(qaBindParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 启用页面问题绑定
     *
     * @param qaBindParam
     * @return
     */
    @LogAttr(desc = "启用页面问题绑定")
    @Permission(code = "store.faq.other.enableQuestionQaBind", desc = "启用页面问题绑定")
    @PostMapping(path = "/admin/faq/other/enable/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> enableQuestionQaBind(@RequestBody OtherQuestionQaBindDTO qaBindParam) {
        FaqOtherQuestionQaBind questionQaBind = questionQaBindService.getById(qaBindParam.getId());
        if (questionQaBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqOtherQuestionCachePack.enableQuestionQaBind(questionQaBind);
        return Response.ok();
    }

    /**
     * 禁用页面问题绑定
     *
     * @param qaBindParam
     * @return、
     */
    @LogAttr(desc = "禁用页面问题绑定")
    @Permission(code = "store.faq.other.disableQuestionQaBind", desc = "禁用页面问题绑定")
    @PostMapping(path = "/admin/faq/other/disable/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> disableQuestionQaBind(@RequestBody OtherQuestionQaBindDTO qaBindParam) {
        FaqOtherQuestionQaBind questionQaBind = questionQaBindService.getById(qaBindParam.getId());
        if (questionQaBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqOtherQuestionCachePack.disableQuestionQaBind(questionQaBind);
        return Response.ok();
    }

    /**
     * 删除页面问题绑定
     *
     * @param qaBindParam
     * @return
     */
    @LogAttr(desc = "删除页面问题绑定")
    @Permission(code = "store.faq.other.deleteQuestionQaBind", desc = "删除页面问题绑定")
    @PostMapping(path = "/admin/faq/other/delete/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> deleteQuestionQaBind(@RequestBody OtherQuestionQaBindDTO qaBindParam) {
        FaqOtherQuestionQaBind questionQaBind = questionQaBindService.getById(qaBindParam.getId());
        if (questionQaBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqOtherQuestionCachePack.deleteQuestionQaBind(questionQaBind.getId());
        return Response.ok();
    }

    /**
     * 更新问题排序规则
     *
     * @param questionQaBindParam
     * @return
     */
    @LogAttr(desc = "更新问题排序规则")
    @Permission(code = "store.faq.other.updateQuestionQaBindIndex", desc = "更新问题排序规则")
    @PostMapping(path = "/admin/faq/other/update/questionQaBindIndex", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateQuestionQaBindIndex(@RequestBody List<OtherQuestionQaBindDTO> questionQaBindParam) {
        adFaqOtherQuestionCachePack.updateQuestionQaBindIndex(questionQaBindParam);
        return Response.ok();
    }
}
