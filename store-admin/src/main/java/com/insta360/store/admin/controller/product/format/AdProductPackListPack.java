package com.insta360.store.admin.controller.product.format;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.insta360.store.admin.controller.product.vo.*;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityDifference;
import com.insta360.store.business.commodity.model.CommodityDifferencePoint;
import com.insta360.store.business.commodity.model.CommodityDifferencePointText;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.product.enums.ProductPackingListType;
import com.insta360.store.business.product.model.*;
import com.insta360.store.business.product.service.impl.helper.ProductParametersTextHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/1
 */
@Component
public class AdProductPackListPack {

    @Autowired
    private CommodityService commodityService;

    @Autowired
    ProductParametersTextHelper productParametersTextHelper;

    public AdProductPackingListVO doPackCameraPackList(List<CommodityDifferencePoint> differencePointList, List<CommodityDifferencePointText> differencePointTextList, List<CommodityDifference> commodityDifferenceList) {
        if (CollectionUtils.isEmpty(differencePointList) || CollectionUtils.isEmpty(differencePointTextList)) {
            return null;
        }

        Map<Integer, List<CommodityDifferencePointText>> pointTextMap = differencePointTextList.stream().collect(Collectors.groupingBy(CommodityDifferencePointText::getDifferencePointId, Collectors.toList()));
        Map<Integer, List<CommodityDifference>> commodityDifferenceMap = Optional.ofNullable(commodityDifferenceList).map(
                commodityDifferences ->
                        commodityDifferences.stream().collect(Collectors.groupingBy(CommodityDifference::getPointId, Collectors.toList()))
        ).orElse(Maps.newHashMap());

        List<AdCameraPackListVO> cameraPackListList = differencePointList.stream().map(
                commodityDifferencePoint -> {
                    AdCameraPackListVO cameraPackListVO = new AdCameraPackListVO();
                    cameraPackListVO.setBaseId(commodityDifferencePoint.getId());
                    cameraPackListVO.setBaseName(commodityDifferencePoint.getNameZh());
                    cameraPackListVO.setImageUrl(commodityDifferencePoint.getImageUrl());
                    cameraPackListVO.setProductId(commodityDifferencePoint.getProductId());
                    cameraPackListVO.setSortNumber(commodityDifferencePoint.getOrderIndex());
                    List<CommodityDifferencePointText> commodityDifferencePointTextList = pointTextMap.get(commodityDifferencePoint.getId());
                    if (CollectionUtils.isNotEmpty(commodityDifferencePointTextList)) {
                        List<AdCameraPackListVO.LanguageTextBean> languageTextBeanList = commodityDifferencePointTextList.stream()
                                .map(
                                        commodityDifferencePointText -> {
                                            AdCameraPackListVO.LanguageTextBean languageTextBean = new AdCameraPackListVO.LanguageTextBean();
                                            languageTextBean.setTextId(commodityDifferencePointText.getId());
                                            languageTextBean.setLanguage(commodityDifferencePointText.getLanguage());
                                            languageTextBean.setName(commodityDifferencePointText.getText());
                                            return languageTextBean;
                                        })
                                .collect(Collectors.toList());

                        cameraPackListVO.setLanguageText(languageTextBeanList);
                    }

                    List<CommodityDifference> differenceList = commodityDifferenceMap.get(commodityDifferencePoint.getId());
                    if (CollectionUtils.isNotEmpty(differenceList)) {
                        List<AdCameraPackListVO.CommodityConfigBean> commodityConfigBeanList = differenceList.stream()
                                .map(
                                        commodityDifference -> {
                                            AdCameraPackListVO.CommodityConfigBean commodityConfigBean = new AdCameraPackListVO.CommodityConfigBean();
                                            commodityConfigBean.setConfigId(commodityDifference.getId());
                                            commodityConfigBean.setCommodityId(commodityDifference.getCommodityId());
                                            commodityConfigBean.setBaseId(commodityDifference.getPointId());
                                            commodityConfigBean.setContentType(commodityDifference.contentType().name());
                                            commodityConfigBean.setNumber(commodityDifference.getContent());
                                            commodityConfigBean.setSortNumber(commodityDifference.getOrderIndex());

                                            Commodity commodity = commodityService.getById(commodityDifference.getCommodityId());
                                            String commodityName = Optional.ofNullable(commodity).map(Commodity::getName).orElse(null);
                                            commodityConfigBean.setCommodityName(commodityName);
                                            return commodityConfigBean;
                                        })
                                .collect(Collectors.toList());

                        cameraPackListVO.setCommodityConfig(commodityConfigBeanList);
                    }

                    return cameraPackListVO;
                }).collect(Collectors.toList());

        AdProductPackingListVO productPackingListVO = new AdProductPackingListVO();
        productPackingListVO.setProductPackListType(ProductPackingListType.CAMERA_TEMPLATE.getType());
        productPackingListVO.setCameraPackList(cameraPackListList);

        return productPackingListVO;
    }

    /**
     * 组装配件模版类型包装清单
     *
     * @param productPackListTextList
     * @return
     */
    public AdProductPackingListVO doPackAccessoriesPackList(List<ProductPackListText> productPackListTextList) {
        List<AdAccessoriesPackListVO> adAccessoriesPackListList = Lists.newArrayList();
        for (ProductPackListText productPackListText : productPackListTextList) {
            AdAccessoriesPackListVO accessoriesPackListVO = new AdAccessoriesPackListVO();
            BeanUtils.copyProperties(productPackListText, accessoriesPackListVO);
            adAccessoriesPackListList.add(accessoriesPackListVO);
        }

        AdProductPackingListVO productPackingListVO = new AdProductPackingListVO();
        productPackingListVO.setProductPackListType(ProductPackingListType.ACCESSORIES_TEMPLATE.getType());
        productPackingListVO.setAccessoriesPackList(adAccessoriesPackListList);
        return productPackingListVO;
    }

    /**
     * 组装产品参数主体
     *
     * @param productParametersTextList
     * @return
     */
    public List<AdProductParametersTextVO> doPackProductParametersText(List<ProductParametersText> productParametersTextList) {
        if (CollectionUtils.isEmpty(productParametersTextList)) {
            return null;
        }
        List<Integer> parameterIdList = productParametersTextList.stream().map(ProductParametersText::getId).collect(Collectors.toList());
        // key:参数id value:多语言list
        Map<Integer, List<ProductParametersTextInfo>> parametersTextInfoMap = productParametersTextHelper.listByParametersIdList(parameterIdList);
        return productParametersTextList.stream()
                .map(
                        productParametersText -> {
                            List<ProductParametersTextInfo> productParametersTextInfos = parametersTextInfoMap.get(productParametersText.getId());
                            return new AdProductParametersTextVO(productParametersText, productParametersTextInfos);
                        })
                .collect(Collectors.toList());
    }

    /**
     * 组装产品参数主体及多语言明细
     *
     * @param productParametersText
     * @param productParametersTextInfoList
     * @return
     */
    public AdProductParametersTextVO doPackProductParametersTextInfo(ProductParametersText productParametersText, List<ProductParametersTextInfo> productParametersTextInfoList) {
        if (Objects.isNull(productParametersText) || CollectionUtils.isEmpty(productParametersTextInfoList)) {
            return null;
        }
        return new AdProductParametersTextVO(productParametersText, productParametersTextInfoList);
    }

    /**
     * 组装产品注意事项多语言文案
     *
     * @param productPrecautionsText
     * @param productPrecautionsTextInfoList
     * @return
     */
    public AdProductPrecautionsTextVO doPackProductPrecautionsText(ProductPrecautionsText productPrecautionsText, List<ProductPrecautionsTextInfo> productPrecautionsTextInfoList) {
        if (CollectionUtils.isEmpty(productPrecautionsTextInfoList) || Objects.isNull(productPrecautionsText)) {
            return null;
        }

        return new AdProductPrecautionsTextVO(productPrecautionsText, productPrecautionsTextInfoList);
    }
}
