package com.insta360.store.admin.controller.order.controller.print;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.controller.order.vo.AdOrderPrintInfoVO;
import com.insta360.store.business.admin.order.print.bo.OrderPrintInfo;
import com.insta360.store.business.admin.order.print.loader.LxOrderPrintInfoLoader;
import com.insta360.store.business.integration.lingxing.dto.LxOrderDTO;
import com.insta360.store.business.integration.lingxing.model.LxOrder;
import com.insta360.store.business.integration.lingxing.model.LxOrderDelivery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 订单/订单打印/领星订单打印
 *
 * <AUTHOR>
 * @date 2023/10/10
 */
@RestController
@PermissionResource(code = "LxOrderPrint", desc = "订单打印（领星）")
public class AdLxOrderPrintApi extends BaseOrderPrintApi {

    /**
     * 根据订单号或物流号查询
     *
     * @param lxOrderParam
     * @return
     */
    @LogAttr(desc = "根据订单号或物流号查询", logType = LogType.query)
    @Permission(code = "store.order.lxOrderPrint.findOrder", desc = "根据订单号或物流号查询")
    @PostMapping(path = "/admin/lx/order/print/findOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> findOrder(@RequestBody LxOrderDTO lxOrderParam) {
        if (StringUtils.isAllBlank(lxOrderParam.getLxOrderNumber(), lxOrderParam.getSalesNumber(), lxOrderParam.getExpressCode())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "缺少查询参数");
        }

        LxOrder lxOrder = null;

        // 销售单号
        if (StringUtil.isNotBlank(lxOrderParam.getSalesNumber())) {
            List<LxOrder> lxOrders = lxOrderService.listByLxPlatformNumber(lxOrderParam.getSalesNumber());
            // 拆单订单通过子订单号查询
            if (lxOrders.size() > 1) {
                throw new InstaException(CommonErrorCode.InvalidParameter, "该订单已拆单发货，请使用领星子订单号打印");
            }
            if (CollectionUtils.isEmpty(lxOrders)) {
                throw new InstaException(-1, "订单号不存在数据");
            }
            lxOrder = lxOrders.get(0);
        }

        // 物流号
        if (StringUtil.isNotBlank(lxOrderParam.getExpressCode())) {
            LxOrderDelivery lxOrderDelivery = lxOrderDeliveryService.getByExpressCode(lxOrderParam.getExpressCode());
            if (lxOrderDelivery == null) {
                throw new InstaException(-1, "物流号查不到数据");
            }
            lxOrder = lxOrderService.getByLxOrderNumber(lxOrderDelivery.getLxOrderNumber());
        }

        // 订单号
        if (StringUtil.isNotBlank(lxOrderParam.getLxOrderNumber())) {
            lxOrder = lxOrderService.getByLxOrderNumber(lxOrderParam.getLxOrderNumber());
        }

        return Response.ok("lxOrder", lxOrder);
    }

    /**
     * 领星订单报关打印
     *
     * @param orderId
     * @return
     */
    @LogAttr(desc = "获取订单报关信息", logType = LogType.query)
    @Permission(code = "store.order.lxOrderPrint.getLxOrderInfo", desc = "获取订单报关信息")
    @GetMapping("/admin/lx/order/print/getLxOrderInfo")
    public Response<Object> getLxOrderInfo(@RequestParam @NotNull(message = "订单ID不能为空") Integer orderId) {
        LxOrder lxOrder = lxOrderService.getById(orderId);
        // 配置加载
        LxOrderPrintInfoLoader loader = (LxOrderPrintInfoLoader) loaderFactory.getLoader(LxOrderPrintInfoLoader.class);
        loader.buildOrderInfo(lxOrder);
        OrderPrintInfo orderPrintInfo = loader.load();

        // 处理订单打印相关的计算逻辑
        orderPrintCalculation.run(orderPrintInfo);
        LocalDateTime expressTime = lxOrderDeliveryService.getByLxOrderNumber(lxOrder.getLxOrderNumber()).getExpressTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String expressDate = expressTime != null ? formatter.format(expressTime) : formatter.format(LocalDateTime.now());
        AdOrderPrintInfoVO lxOrderPrintInfo = buildAdMbOrderInfoVO(orderPrintInfo, expressDate);
        return Response.ok(lxOrderPrintInfo);
    }
}
