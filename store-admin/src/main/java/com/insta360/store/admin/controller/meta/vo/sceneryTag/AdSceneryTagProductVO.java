package com.insta360.store.admin.controller.meta.vo.sceneryTag;


import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.SceneryTagProduct;

import java.io.Serializable;

/**
 * @description:
 * @author: py
 * @create: 2024-12-19 15:44
 */
public class AdSceneryTagProductVO implements Serializable {


    private Integer id;

    /**
     * 关联产品ID
     */
    private Integer productId;

    public AdSceneryTagProductVO(SceneryTagProduct sceneryTagProduct) {
        if (sceneryTagProduct != null) {
            BeanUtil.copyProperties(sceneryTagProduct, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Override
    public String toString() {
        return "AdSceneryTagProductVO{" +
                "id=" + id +
                ", productId=" + productId +
                '}';
    }
}
