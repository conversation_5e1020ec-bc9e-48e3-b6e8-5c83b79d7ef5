package com.insta360.store.admin.controller.meta.vo.navigationbar;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarBannerSubset;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/5/30
 * @Description:
 */
public class AdNavigationBarBannerSubsetVO implements Serializable {

    private Integer id;

    /**
     * banner主关联
     */
    private Integer bannerMainId;

    /**
     * 标题
     */
    private String title;

    /**
     * 图片链接
     */
    private String imgUrl;

    /**
     * 是否启用(默认启用)
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    List<AdNavigationBarBannerInfoVO> bannerInfos;

    public AdNavigationBarBannerSubsetVO() {
    }

    public AdNavigationBarBannerSubsetVO(NavigationBarBannerSubset bannerSubset) {
        if (bannerSubset != null) {
            BeanUtil.copyProperties(bannerSubset, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBannerMainId() {
        return bannerMainId;
    }

    public void setBannerMainId(Integer bannerMainId) {
        this.bannerMainId = bannerMainId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public List<AdNavigationBarBannerInfoVO> getBannerInfos() {
        return bannerInfos;
    }

    public void setBannerInfos(List<AdNavigationBarBannerInfoVO> bannerInfos) {
        this.bannerInfos = bannerInfos;
    }

    @Override
    public String toString() {
        return "AdNavigationBarBannerSubsetVO{" +
                "id=" + id +
                ", bannerMainId=" + bannerMainId +
                ", title='" + title + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                ", bannerInfos=" + bannerInfos +
                '}';
    }
}
