package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.dto.MetaShippingCostDTO;
import com.insta360.store.business.meta.service.MetaShippingCostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * admin meta 运费门槛封装
 * 收税州
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/3
 */
@Component
public class AdMetaShippingCostCachePack {

    @Autowired
    MetaShippingCostService metaShippingCostService;

    /**
     * 按国家/地区更新批处理
     *
     * @param metaShippingCostList   meta运费
     * @param cachePutKeyParameterBo 缓存更新参数
     */
    @CachePutMonitor(cacheableType = CachePutType.META_SHIPPING_COST)
    public void updateBatchByCountry(CachePutKeyParameterBO cachePutKeyParameterBo, List<MetaShippingCostDTO> metaShippingCostList) {
        metaShippingCostService.updateBatchByCountry(metaShippingCostList);
    }
}
