package com.insta360.store.admin.common.interceptor;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.admin.common.WebApiContext;
import com.insta360.store.business.insurance.aop.CareCardContext;
import com.insta360.store.business.insurance.aop.LogOperatorChange;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: wbt
 * @Date: 2020/09/17
 * @Description:
 */
public class CreateCareCardInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        LogOperatorChange annotation = handlerMethod.getMethodAnnotation(LogOperatorChange.class);

        if (annotation != null) {

            // 请求上下文，记录操作人员信息
            CareCardContext careCardContext = ApplicationContextHolder.getApplicationContext().getBean(CareCardContext.class);
            if (careCardContext != null) {
                WebApiContext webContext = WebApiContext.get();

                // 工号
                if (webContext != null) {
                    String adminJobNumber = webContext.getAdminJobNumber();
                    if (StringUtil.isNotBlank(adminJobNumber)) {
                        careCardContext.setOperator(adminJobNumber);
                    }
                }
            }

            // 创建
            CareCardContext.set(careCardContext);
        }

        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);

        // 销毁
        CareCardContext.remove();
    }
}
