package com.insta360.store.admin.rpc.country.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.OceanCountryConfig;

import java.io.Serializable;

/**
 * 国家/地区州vo
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public class CountryProvinceVO implements Serializable {

    private Integer id;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 省份/州名称
     */
    private String provinceName;

    /**
     * 省份/州代码中文名称
     */
    private String provinceNameZh;

    public CountryProvinceVO() {
    }

    public CountryProvinceVO(OceanCountryConfig oceanCountryConfig) {
        if (oceanCountryConfig != null) {
            BeanUtil.copyProperties(oceanCountryConfig, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getProvinceNameZh() {
        return provinceNameZh;
    }

    public void setProvinceNameZh(String provinceNameZh) {
        this.provinceNameZh = provinceNameZh;
    }

    @Override
    public String toString() {
        return "OceanCountryConfig{" +
                "id=" + id +
                ", countryCode='" + countryCode + '\'' +
                ", provinceName='" + provinceName + '\'' +
                ", provinceNameZh='" + provinceNameZh + '\'' +
                '}';
    }
}