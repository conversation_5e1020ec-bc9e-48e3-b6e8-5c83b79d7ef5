package com.insta360.store.admin.controller.order.format;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.admin.controller.order.vo.AdOrderDeliveryPartlyVO;
import com.insta360.store.admin.controller.order.vo.AdOrderDeliveryVO;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderStateRecord;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.OrderStateRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/06/09
 * @Description:
 */
@Component
public class AdOrderDeliveryPartPack {

    @Autowired
    OrderService orderService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderStateRecordService stateRecordService;

    @Autowired
    AdOrderDeliveryPack adOrderDeliveryPack;

    public AdOrderDeliveryVO doPack(Integer orderId, InstaLanguage language) {
        Order order = orderService.getById(orderId);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 先从发货记录中获取（需要兼容以前没有记录多条物流的订单信息）
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(orderId);

        // 部分发货的套餐详情信息
        List<AdOrderDeliveryPartlyVO> deliveryPartlyVOS = adOrderDeliveryPack.listPartlyDeliveryDetailInfo(orderId, language);

        // 订单状态变化记录
        List<OrderStateRecord> orderStateRecords = stateRecordService.getOrderStateRecords(orderId);

        // 封装参数
        AdOrderDeliveryVO deliveryVO = new AdOrderDeliveryVO(orderDelivery);
        deliveryVO.setDeliveryPartlyVO(deliveryPartlyVOS);
        deliveryVO.setOrderStateRecords(orderStateRecords);

        return deliveryVO;
    }
}
