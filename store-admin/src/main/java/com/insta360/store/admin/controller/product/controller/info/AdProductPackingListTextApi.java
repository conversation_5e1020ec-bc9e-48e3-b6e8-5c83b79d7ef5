package com.insta360.store.admin.controller.product.controller.info;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.product.cache.AdProductPackListCachePack;
import com.insta360.store.admin.controller.product.cache.AdProductParameterCachePack;
import com.insta360.store.admin.controller.product.cache.AdProductPrecautionsCachePack;
import com.insta360.store.admin.controller.product.format.AdProductPackListPack;
import com.insta360.store.admin.controller.product.vo.AdProductPackListConfirmVO;
import com.insta360.store.admin.controller.product.vo.AdProductPackingListVO;
import com.insta360.store.admin.controller.product.vo.AdProductParametersTextVO;
import com.insta360.store.admin.controller.product.vo.AdProductPrecautionsTextVO;
import com.insta360.store.business.admin.product.dto.ProductExcelUploadDTO;
import com.insta360.store.business.admin.product.dto.bo.ProductExcelCheckBO;
import com.insta360.store.business.admin.product.factory.ProductPackSetUpUploadServiceFactory;
import com.insta360.store.business.admin.product.service.ProductPackSetUpUploadService;
import com.insta360.store.business.commodity.enums.DifferenceContentType;
import com.insta360.store.business.commodity.enums.DifferencePointType;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityDifference;
import com.insta360.store.business.commodity.model.CommodityDifferencePoint;
import com.insta360.store.business.commodity.model.CommodityDifferencePointText;
import com.insta360.store.business.commodity.service.CommodityDifferencePointService;
import com.insta360.store.business.commodity.service.CommodityDifferencePointTextService;
import com.insta360.store.business.commodity.service.CommodityDifferenceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.product.dto.*;
import com.insta360.store.business.product.exception.ProductErrorCode;
import com.insta360.store.business.product.model.ProductPackListText;
import com.insta360.store.business.product.model.ProductParametersText;
import com.insta360.store.business.product.model.ProductPrecautionsText;
import com.insta360.store.business.product.model.ProductPrecautionsTextInfo;
import com.insta360.store.business.product.service.ProductPackingListService;
import com.insta360.store.business.product.service.ProductParametersTextService;
import com.insta360.store.business.product.service.ProductPrecautionsTextInfoService;
import com.insta360.store.business.product.service.ProductPrecautionsTextService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 产品包装清单文案配置接入层
 * @Date 2022/8/30
 */
@RestController
@PermissionResource(code = "productPackingListText", desc = "产品包装清单文案配置")
public class AdProductPackingListTextApi extends BaseAdminApi {

    @Autowired
    private CommodityDifferencePointService commodityDifferencePointService;

    @Autowired
    private CommodityDifferencePointTextService commodityDifferencePointTextService;

    @Autowired
    private CommodityDifferenceService commodityDifferenceService;

    @Autowired
    private AdProductPackListPack adProductPackListPack;

    @Autowired
    private ProductPackingListService productPackingListService;

    @Autowired
    private ProductParametersTextService productParametersTextService;

    @Autowired
    private ProductPrecautionsTextService productPrecautionsTextService;

    @Autowired
    private ProductPrecautionsTextInfoService productPrecautionsTextInfoService;

    @Autowired
    private ProductPackSetUpUploadServiceFactory productPackSetUpUploadServiceFactory;

    @Autowired
    private AdProductPackListCachePack adProductPackListCachePack;

    @Autowired
    private AdProductParameterCachePack adProductParameterCachePack;

    @Autowired
    private AdProductPrecautionsCachePack adProductPrecautionsCachePack;

    @Autowired
    private CommodityService commodityService;

    /**
     * 查询产品包装清单
     *
     * @param productId
     * @return
     */
    @LogAttr(desc = "查询产品包装清单", logType = LogType.query)
    @Permission(code = "store.product.productPackingListText.queryProductPackingList", desc = "查询产品包装清单")
    @GetMapping("/admin/product/packingList/queryProductPackingList")
    public Response<AdProductPackingListVO> queryProductPackingList(@RequestParam("productId") Integer productId) {
        List<CommodityDifferencePoint> differencePointList = commodityDifferencePointService.getDifferencePoints(productId, DifferencePointType.packing_list);
        if (CollectionUtils.isNotEmpty(differencePointList)) {
            List<Integer> pointIdList = differencePointList.stream().map(CommodityDifferencePoint::getId).collect(Collectors.toList());
            List<CommodityDifferencePointText> differencePointTextList = commodityDifferencePointTextService.listByPointId(pointIdList);
            if (CollectionUtils.isEmpty(differencePointTextList)) {
                return Response.ok();
            }
            List<CommodityDifference> commodityDifferenceList = commodityDifferenceService.listByPointId(pointIdList, DifferenceContentType.number.name());
            AdProductPackingListVO adProductPackingListVO = adProductPackListPack.doPackCameraPackList(differencePointList, differencePointTextList, commodityDifferenceList);
            return Response.ok(adProductPackingListVO);
        } else {
            List<ProductPackListText> productPackListTextList = productPackingListService.listAccessoriesPackListTextByProductId(productId);
            if (CollectionUtils.isNotEmpty(productPackListTextList)) {
                AdProductPackingListVO adProductPackingListVO = adProductPackListPack.doPackAccessoriesPackList(productPackListTextList);
                return Response.ok(adProductPackingListVO);
            }
        }

        return Response.ok();
    }

    /**
     * 新增或更新相机产品模版类型包装清单子项
     *
     * @param productPackingListCamera
     * @return
     */
    @LogAttr(desc = "新增或更新相机产品模版类型包装清单子项")
    @Permission(code = "store.product.productPackingListText.saveOrUpdateCameraPackList", desc = "新增或更新产品相机模版类型包装清单子项")
    @PostMapping(path = "/admin/product/packingList/saveOrUpdateCameraPackList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<AdProductPackListConfirmVO> saveOrUpdateCameraPackList(@Validated @RequestBody ProductPackingListCameraDTO productPackingListCamera) {
        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(productPackingListCamera.getProductId()));

        return Response.ok(adProductPackListCachePack.saveOrUpdateCameraPackList(cachePutKeyParameter, productPackingListCamera));
    }

    /**
     * 新增或更新相机产品模版类型包装清单配置明细
     *
     * @param cameraCommodityPackingListConfigList
     * @return
     */
    @LogAttr(desc = "新增或更新相机产品模版类型包装清单配置明细")
    @Permission(code = "store.product.productPackingListText.saveOrUpdateCameraPackListConfig", desc = "新增或更新产品相机模版类型包装清单配置明细")
    @PostMapping(path = "/admin/product/packingList/saveOrUpdateCameraPackListConfig", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> saveOrUpdateCameraPackListConfig(@Validated @RequestBody List<CameraCommodityPackingListConfigDTO> cameraCommodityPackingListConfigList) {
        if (CollectionUtils.isEmpty(cameraCommodityPackingListConfigList)) {
            return Response.failed("入参非法！");
        }

        // 获取对应套餐
        Integer commodityId = cameraCommodityPackingListConfigList.get(0).getCommodityId();
        Commodity commodity = commodityService.getById(commodityId);
        if (commodity == null) {
            throw new InstaException(ProductErrorCode.CommodityNotExistException);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(commodity.getProduct()));

        adProductPackListCachePack.saveOrUpdateCameraPackListConfig(cachePutKeyParameter, cameraCommodityPackingListConfigList);
        return Response.ok();
    }

    /**
     * 更新产品相机模版类型包装清单子项排序
     *
     * @param productCameraPackListSortList
     * @return
     */
    @LogAttr(desc = "更新产品相机模版类型包装清单子项排序")
    @Permission(code = "store.product.productPackingListText.updateCameraPackListSort", desc = "更新产品相机模版类型包装清单子项排序")
    @PostMapping(path = "/admin/product/packingList/updateCameraPackListSort", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateCameraPackListSort(@Validated @RequestBody List<ProductCameraPackListSortDTO> productCameraPackListSortList) {
        if (CollectionUtils.isEmpty(productCameraPackListSortList)) {
            return Response.failed("入参非法！");
        }

        List<Integer> pointIds = productCameraPackListSortList.stream().map(ProductCameraPackListSortDTO::getPointId).distinct().collect(Collectors.toList());
        Collection<CommodityDifferencePoint> commodityDifferencePoints = commodityDifferencePointService.listByIds(pointIds);
        if (CollectionUtils.isEmpty(commodityDifferencePoints)) {
            return Response.failed();
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(commodityDifferencePoints.stream().map(CommodityDifferencePoint::getProductId).distinct().collect(Collectors.toList()));

        adProductPackListCachePack.updateCameraPackListSort(cachePutKeyParameter, productCameraPackListSortList);
        return Response.ok();
    }

    /**
     * 删除产品相机模版类型包装清单子项
     *
     * @param pointId
     * @return
     */
    @LogAttr(desc = "删除产品相机模版类型包装清单子项")
    @Permission(code = "store.product.productPackingListText.deleteCameraPackListItem", desc = "删除产品相机模版类型包装清单子项")
    @PostMapping(path = "/admin/product/packingList/deleteCameraPackListItem")
    public Response<Object> deleteCameraPackListItem(@RequestParam("pointId") Integer pointId) {
        if (Objects.isNull(pointId)) {
            return Response.failed("入参非法！");
        }

        // 参数校验
        CommodityDifferencePoint differencePoint = commodityDifferencePointService.getById(pointId);
        if (Objects.isNull(differencePoint)) {
            return Response.failed();
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(differencePoint.getProductId()));

        adProductPackListCachePack.deleteCameraPackListItem(cachePutKeyParameter, pointId);
        return Response.ok();
    }

    /**
     * 删除产品相机模版类型包装清单子项套餐
     *
     * @param commodityId
     * @return
     */
    @LogAttr(desc = "删除产品相机模版类型包装清单子项套餐")
    @Permission(code = "store.product.productPackingListText.deleteCameraPackListCommodity", desc = "删除产品相机模版类型包装清单子项套餐")
    @PostMapping(path = "/admin/product/packingList/deleteCameraPackListCommodity")
    public Response<Object> deleteCameraPackListCommodity(@RequestParam("commodityId") Integer commodityId) {
        if (Objects.isNull(commodityId)) {
            return Response.failed("入参非法！");
        }
        // 获取对应套餐
        Commodity commodity = commodityService.getById(commodityId);
        if (commodity == null) {
            throw new InstaException(ProductErrorCode.CommodityNotExistException);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(commodity.getProduct()));

        adProductPackListCachePack.deleteCameraPackListCommodity(cachePutKeyParameter, commodityId);
        return Response.ok();
    }

    /**
     * 新增或更新配件产品模版类型包装清单
     *
     * @param accessoriesPackListParam
     * @return
     */
    @LogAttr(desc = "新增或更新配件产品模版类型包装清单")
    @Permission(code = "store.product.productPackingListText.saveOrUpdateAccessoriesPackList", desc = "新增或更新配件产品模版类型包装清单")
    @PostMapping(path = "/admin/product/packingList/saveOrUpdateAccessoriesPackList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> saveOrUpdateAccessoriesPackList(@Validated @RequestBody AccessoriesPackListDTO accessoriesPackListParam) {
        Integer productId = accessoriesPackListParam.getProductId();
        if (Objects.isNull(productId)) {
            return Response.failed("入参非法！");
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(productId));

        return Response.ok(adProductPackListCachePack.saveOrUpdateAccessoriesPackList(cachePutKeyParameter, accessoriesPackListParam));
    }

    /**
     * 查询产品参数文案
     *
     * @param productId
     * @return
     */
    @LogAttr(desc = "查询产品参数文案", logType = LogType.query)
    @Permission(code = "store.product.productPackingListText.queryProductParametersText", desc = "查询产品参数文案")
    @GetMapping("/admin/product/packingList/queryProductParametersText")
    public Response<List<AdProductParametersTextVO>> queryProductParametersText(@Validated @RequestParam("productId") Integer productId) {
        List<ProductParametersText> productParametersTextList = productParametersTextService.listByProductId(productId);
        List<AdProductParametersTextVO> adProductParametersTextVOList = adProductPackListPack.doPackProductParametersText(productParametersTextList);
        return Response.ok(adProductParametersTextVOList);
    }

    /**
     * 更新产品参数文案
     *
     * @param productParametersTextCreateDTO
     * @return
     */
    @LogAttr(desc = "更新产品参数文案")
    @Permission(code = "store.product.productPackingListText.updateProductParametersText", desc = "更新产品参数文案")
    @PostMapping(path = "/admin/product/packingList/updateProductParametersText", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateProductParametersText(@Validated @RequestBody ProductParametersTextCreateDTO productParametersTextCreateDTO) {
        Integer productId = productParametersTextCreateDTO.getProductId();
        if (Objects.isNull(productId)) {
            return Response.failed("入参非法！");
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(productId));

        adProductParameterCachePack.updateProductParametersText(cachePutKeyParameter, productParametersTextCreateDTO);
        return Response.ok();
    }

    /**
     * 新增参数文案
     *
     * @param productParametersTextCreateDTO
     * @return
     */
    @LogAttr(desc = "新增产品参数文案")
    @Permission(code = "store.product.productPackingListText.saveProductParametersText", desc = "新增产品参数文案")
    @PostMapping(path = "/admin/product/packingList/saveProductParametersText", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> saveProductParametersText(@Validated @RequestBody ProductParametersTextCreateDTO productParametersTextCreateDTO) {
        Integer productId = productParametersTextCreateDTO.getProductId();
        if (Objects.isNull(productId)) {
            return Response.failed("入参非法！");
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(productId));

        adProductParameterCachePack.saveProductParametersText(cachePutKeyParameter, productParametersTextCreateDTO);
        return Response.ok();
    }

    /**
     * 删除产品参数文案
     *
     * @param parametersId
     * @return
     */
    @LogAttr(desc = "删除产品参数文案")
    @Permission(code = "store.product.productPackingListText.deleteProductParametersText", desc = "删除产品参数文案")
    @PostMapping("/admin/product/packingList/deleteProductParametersText")
    public Response<Object> deleteProductParametersText(@Validated @RequestParam("parametersId") Integer parametersId) {
        ProductParametersText productParametersText = productParametersTextService.getById(parametersId);
        if (Objects.isNull(productParametersText)) {
            return Response.failed();
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(productParametersText.getProductId()));

        adProductParameterCachePack.deleteProductParametersText(cachePutKeyParameter, parametersId);
        return Response.ok();
    }

    /**
     * 查询产品注意事项文案
     *
     * @param productId
     * @return
     */
    @LogAttr(desc = "查询产品注意事项文案", logType = LogType.query)
    @Permission(code = "store.product.productPackingListText.queryProductPrecautionsText", desc = "查询产品注意事项文案")
    @GetMapping("/admin/product/packingList/queryProductPrecautionsText")
    public Response<AdProductPrecautionsTextVO> queryProductPrecautionsText(@Validated @RequestParam("productId") Integer productId) {
        ProductPrecautionsText precautionsText = productPrecautionsTextService.getPrecautionsText(productId);
        if (Objects.isNull(precautionsText)) {
            return Response.ok();
        }
        List<ProductPrecautionsTextInfo> productPrecautionsTextInfoList = productPrecautionsTextInfoService.listByPrecautionsId(precautionsText.getId());
        AdProductPrecautionsTextVO adProductPrecautionsTextVO = adProductPackListPack.doPackProductPrecautionsText(precautionsText, productPrecautionsTextInfoList);
        return Response.ok(adProductPrecautionsTextVO);
    }

    /**
     * 新增或更新产品注意事项文案
     *
     * @param productPrecautionsTextCreateParam
     * @return
     */
    @LogAttr(desc = "新增或更新产品注意事项文案")
    @Permission(code = "store.product.productPackingListText.saveOrUpdateProductPrecautionsText", desc = "新增或更新产品注意事项文案案")
    @PostMapping(path = "/admin/product/packingList/saveOrUpdateProductPrecautionsText", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> saveOrUpdateProductPrecautionsText(@Validated @RequestBody ProductPrecautionsTextCreateDTO productPrecautionsTextCreateParam) {
        Integer productId = productPrecautionsTextCreateParam.getProductId();
        if (Objects.isNull(productId)) {
            return Response.failed("入参非法！");
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setProductIds(Arrays.asList(productId));

        adProductPrecautionsCachePack.saveOrUpdateProductPrecautionsText(cachePutKeyParameter, productPrecautionsTextCreateParam);
        return Response.ok();
    }

    /**
     * 产品设置包装配置信息文件上传
     *
     * @param file
     * @param productExcelUploadDTO
     * @return
     */
    @LogAttr(desc = "产品设置包装配置信息文件上传")
    @Permission(code = "store.product.productPackingListText.packListExcelUpload", desc = "产品设置包装配置信息文件上传")
    @PostMapping(value = "/admin/product/packingList/packListExcelUpload", consumes = {MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE})
    public Response<ProductExcelCheckBO> packListExcelUpload(MultipartFile file, @ModelAttribute ProductExcelUploadDTO productExcelUploadDTO) {
        if (Objects.isNull(file)
                || Objects.isNull(productExcelUploadDTO)
                || Objects.isNull(productExcelUploadDTO.getModuleType())
                || Objects.isNull(productExcelUploadDTO.getReplaceFlag())
        ) {
            return Response.failed("请求失败,参数非法!");
        }
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!"xlsx".equals(suffix) && !"xls".equals(suffix)) {
            throw new InstaException(-1, "目前只支持'xlsx'、'xls'文件");
        }

        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            throw new InstaException(-1, "文件解析错误！");
        }
        ProductPackSetUpUploadService packSetUpUploadService = productPackSetUpUploadServiceFactory.getService(productExcelUploadDTO.getModuleType());
        ProductExcelCheckBO productExcelCheckBO = packSetUpUploadService.excelHandle(inputStream, productExcelUploadDTO.getReplaceFlag());
        return Response.ok(productExcelCheckBO);
    }
}
