package com.insta360.store.admin.controller.reseller.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.reseller.vo.AdResellerLinkVO;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.reseller.config.ResellerConfig;
import com.insta360.store.business.reseller.enums.PromoLinkType;
import com.insta360.store.business.reseller.model.ResellerPromoLink;
import com.insta360.store.business.reseller.service.ResellerPromoLinkService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/7/14
 */
@RestController
@PermissionResource(code = "resellerPromoLink", desc = "分销产品链接配置")
public class AdResellerPromoLinkApi extends BaseAdminApi {

    @Autowired
    private ResellerPromoLinkService resellerPromoLinkService;

    @Autowired
    private ProductService productService;

    @Autowired
    ResellerConfig resellerConfig;

    /**
     * 获取分销链接
     *
     * @return
     */
    @LogAttr(desc = "获取分销链接", logType = LogType.query)
    @Permission(code = "store.reseller.resellerPromoLink.getResellerOrders", desc = "获取分销链接")
    @GetMapping("/admin/reseller/getLinks")
    public Response<? extends Map> getResellerOrders() {
        List<ResellerPromoLink> promoLinkList = resellerPromoLinkService.getByType(PromoLinkType.product);
        if (CollectionUtils.isNotEmpty(promoLinkList)) {
            List<AdResellerLinkVO> linkVOList = promoLinkList.stream()
                    .filter(
                            resellerPromoLink ->
                                    !resellerPromoLink.getRelatedProduct().equals(resellerConfig.getDefaultAccessoriesId()))
                    .map(
                            resellerPromoLink ->
                            {
                                AdResellerLinkVO adResellerLinkVO = new AdResellerLinkVO();
                                Integer relatedProduct = resellerPromoLink.getRelatedProduct();
                                Product product = productService.getById(relatedProduct);
                                BeanUtils.copyProperties(resellerPromoLink, adResellerLinkVO);
                                PromoLinkType promoLinkType = resellerPromoLink.toPromoLinkType();
                                adResellerLinkVO.setTypeStr(promoLinkType != null ? promoLinkType.value : null);
                                adResellerLinkVO.setRelatedProductName(product.getName());
                                return adResellerLinkVO;
                            })
                    .collect(Collectors.toList());

            return Response.ok("linkVOList", linkVOList);
        }

        return Response.ok();
    }
}
