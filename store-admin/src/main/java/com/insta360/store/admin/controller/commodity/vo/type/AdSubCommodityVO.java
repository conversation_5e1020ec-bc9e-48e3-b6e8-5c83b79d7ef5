package com.insta360.store.admin.controller.commodity.vo.type;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/6 下午5:07
 */
public class AdSubCommodityVO implements Serializable {

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 子商品数量
     */
    private Integer number;

    /**
     * 套餐料号
     */
    private String commodityPlatformCode;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getCommodityPlatformCode() {
        return commodityPlatformCode;
    }

    public void setCommodityPlatformCode(String commodityPlatformCode) {
        this.commodityPlatformCode = commodityPlatformCode;
    }

    @Override
    public String toString() {
        return "AdSubCommodityVO{" +
                "productId=" + productId +
                ", productName='" + productName + '\'' +
                ", commodityId=" + commodityId +
                ", commodityName='" + commodityName + '\'' +
                ", number=" + number +
                ", commodityPlatformCode='" + commodityPlatformCode + '\'' +
                '}';
    }
}
