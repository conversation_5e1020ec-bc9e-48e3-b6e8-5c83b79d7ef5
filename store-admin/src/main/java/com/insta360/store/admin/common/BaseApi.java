package com.insta360.store.admin.common;

import com.insta360.compass.core.common.BaseController;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.admin.config.StoreConfiguration;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: mowi
 * @Date: 2018/11/26
 * @Description:
 */
public class Base<PERSON><PERSON> extends BaseController {

    @Autowired
    StoreConfiguration storeConfiguration;

    @Autowired
    StoreApiHeaderParser storeApiHeaderParser;

    public String getAdminJobNumber() {
        WebApiContext apiContext = WebApiContext.get();
        if (apiContext != null) {
            return apiContext.getAdminJobNumber();
        } else {
            return null;
        }
    }

    @Override
    public InstaLanguage getApiLanguage() {
        InstaLanguage language = super.getApiLanguage();
        return language != null ? language : storeConfiguration.getDefaultLanguage();
    }

    @Override
    public InstaCountry getApiCountry() {
        InstaCountry country = super.getApiCountry();
        return country != null ? country : storeConfiguration.getDefaultCountry();
    }

    public String getResellerCode() {
        return storeApiHeaderParser.parseHeaderValue(request, StoreApiHeader.ResellerCode);
    }

    public String getFlashPromoCode() {
        return storeApiHeaderParser.parseHeaderValue(request, StoreApiHeader.ResellerFlashPromoCode);
    }
}
