package com.insta360.store.admin.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.BannerInfo;
import com.insta360.store.business.meta.model.BannerMain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/01/20
 * @Description:
 */
public class AdBannerMainVO implements Serializable {

    /**
     * 语言
     */
    private String language;

    /**
     * 国家
     */
    private String country;

    /**
     * id
     */
    private Integer id;

    /**
     * 渠道
     */
    private String app;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 排序权重
     */
    private Integer bannerIndex;

    /**
     * 是否启用，0启用 1 不启用
     */
    private Boolean disable;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动名称
     */
    private String bannerName;

    /**
     * 颜色类型
     */
    private Integer colorType;

    /**
     * info list
     */
    private List<BannerInfo> infoList;

    public AdBannerMainVO() {
    }

    public AdBannerMainVO(BannerMain bannerMain) {
        if (bannerMain != null) {
            BeanUtil.copyProperties(bannerMain, this);
        }
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getApp() {
        return app;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getBannerIndex() {
        return bannerIndex;
    }

    public void setBannerIndex(Integer bannerIndex) {
        this.bannerIndex = bannerIndex;
    }

    public Boolean getDisable() {
        return disable;
    }

    public void setDisable(Boolean disable) {
        this.disable = disable;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getBannerName() {
        return bannerName;
    }

    public void setBannerName(String bannerName) {
        this.bannerName = bannerName;
    }

    public Integer getColorType() {
        return colorType;
    }

    public void setColorType(Integer colorType) {
        this.colorType = colorType;
    }

    public List<BannerInfo> getInfoList() {
        return infoList;
    }

    public void setInfoList(List<BannerInfo> infoList) {
        this.infoList = infoList;
    }

    @Override
    public String toString() {
        return "AdBannerMainVO{" +
                "language='" + language + '\'' +
                ", country='" + country + '\'' +
                ", id=" + id +
                ", app='" + app + '\'' +
                ", createTime=" + createTime +
                ", bannerIndex=" + bannerIndex +
                ", disable=" + disable +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", bannerName='" + bannerName + '\'' +
                ", colorType=" + colorType +
                ", infoList=" + infoList +
                '}';
    }
}
