package com.insta360.store.admin.controller.test.evelyn;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.controller.test.evelyn.helper.EvelynHelper;
import com.insta360.store.business.configuration.gateway.GatewayConfiguration;
import com.insta360.store.business.email.service.impl.helper.EmailMarketingHelper;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDeliveryUniqueCode;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderStateRecord;
import com.insta360.store.business.order.service.OrderDeliveryUniqueCodeService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.OrderStateRecordService;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import com.insta360.store.business.outgoing.mq.subscribe.helper.SubscribeMessageSendHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.reseller.email.*;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.service.EmailSubscribeService;
import com.insta360.store.business.utils.SecurityUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2023-12-01 10:30
 */
@RestController
public class EvelynTestApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(EvelynTestApi.class);

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    OrderDeliveryUniqueCodeService orderDeliveryUniqueCodeService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderService orderService;

    @Autowired
    EvelynHelper evelynHelper;

    @Autowired
    ResellerEmailFactory resellerEmailFactory;

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    GatewayConfiguration gatewayConfiguration;

    @Autowired
    SubscribeMessageSendHelper subscribeMessageSendHelper;

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    EmailMarketingHelper emailMarketingHelper;

    @Autowired
    OrderStateRecordService orderStateRecordService;

    /**
     * 模拟管易发货保存序列号信息
     *
     * @return
     */
    @GetMapping(value = "/test/insurance/saveUniqueCode")
    public Response<Object> saveUniqueCode(@RequestParam Integer orderItemId, @RequestParam String uniqueCode) {
        LOGGER.info(String.format("模拟管易发货保存序列号信息开始...."));
        OrderItem orderItem = orderItemService.getById(orderItemId);
        Integer orderId = orderItem.getOrder();
        Order order = orderService.getById(orderId);

        CountryConfig countryConfig = countryConfigService.getByCountry(order.country());
        OrderDeliveryUniqueCode deliveryUniqueCode = new OrderDeliveryUniqueCode();
        deliveryUniqueCode.setOrderId(order.getId());
        deliveryUniqueCode.setOrderItem(orderItemId);
        deliveryUniqueCode.setUniqueCode(uniqueCode);
        deliveryUniqueCode.setCreateTime(LocalDateTime.now());
        deliveryUniqueCode.setUpdateTime(LocalDateTime.now());
        orderDeliveryUniqueCodeService.save(deliveryUniqueCode);
        LOGGER.info(String.format("准备发送延时消息...."));
        orderMessageSendHelper.sendInsuranceActivationDelayMessage(countryConfig, deliveryUniqueCode);
        return Response.ok();
    }

    /**
     * 场景专区数据同步
     *
     * @return
     */
    @GetMapping(value = "/store/scenery/section/date/section")
    public Response<Object> sectionSync() {
        LOGGER.info("[场景专区数据同步]开始");
        evelynHelper.sectionSync();
        LOGGER.info("[场景专区数据同步]结束");
        return Response.ok();
    }

    /**
     * 邮件测试
     *
     * @return
     */
    @GetMapping(value = "/admin/course/email/date/test")
    public Response<Object> test(@RequestParam Integer recordId) {
        OrderStateRecord orderOperatorRecord = orderStateRecordService.getById(recordId);
        emailMarketingHelper.sendEducatedEmail(orderOperatorRecord);
        return Response.ok();
    }

    /**
     * 批量发送邮件
     *
     * @return
     */
    @GetMapping(value = "/admin/batch/course/email/date/batchSend")
    public Response<Object> batchSend(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm") LocalDateTime endTime) {
        LOGGER.info("[用户教育邮件]当前参数时间{}", endTime);
        List<OrderItem> orderItems = orderItemService.listByProduct(Product.X5_ID);
        if (CollectionUtils.isEmpty(orderItems)) {
            LOGGER.info("[用户教育邮件]不存在x5订单,不发送");
            return Response.ok();
        }

        List<Integer> orderIds = orderItems.stream().map(OrderItem::getOrder).distinct().collect(Collectors.toList());
        List<OrderStateRecord> orderStateRecordList = orderStateRecordService.listByOrderIdsAndDelivery(orderIds, endTime);
        if (CollectionUtils.isEmpty(orderStateRecordList)) {
            LOGGER.info("[用户教育邮件]不存在符合条件的订单记录");
            return Response.ok();
        }

        emailMarketingHelper.sendBatchEducatedEmail(orderStateRecordList);
        LOGGER.info("[用户教育邮件]发送结束");
        return Response.ok();
    }

    /**
     * 分销发送脚本
     *
     * @return
     */
    @GetMapping(value = "/admin/email/send/reseller/syncEmail")
    public Response<Object> syncEmail(@RequestParam String email, @RequestParam Integer userId, @RequestParam String orderNumber) {
        Reseller reseller = resellerService.getByUserId(userId);
        ResellerOrder resellerOrder = resellerOrderService.getByOrderNumber(orderNumber);

        BaseResellerEmail resellerApplyFailEmail = resellerEmailFactory.getResellerOrderEmail(reseller, resellerOrder, ResellerApplyFailEmail.class);
        resellerApplyFailEmail.doSend(email);

        BaseResellerEmail resellerApplySuccessEmail = resellerEmailFactory.getResellerOrderEmail(reseller, resellerOrder, ResellerApplySuccessEmail.class);
        resellerApplySuccessEmail.doSend(email);

        BaseResellerEmail resellerOrderEmail = resellerEmailFactory.getResellerOrderEmail(reseller, resellerOrder, ResellerOrderCreatedEmail.class);
        resellerOrderEmail.doSend(email);

        BaseResellerEmail agentResellerApplyEmail = resellerEmailFactory.getResellerOrderEmail(reseller, resellerOrder, AgentResellerApplyEmail.class);
        agentResellerApplyEmail.doSend(email);
        return Response.ok();
    }

    /**
     * unsubscribe脚本
     *
     * @return
     */
    @GetMapping(value = "/store/scenery/section/generate/unsubscribe")
    public Response<Object> generate(@RequestParam String email, @RequestParam String secret) {
        String emailEncode = new String(Base64.getEncoder().encode(email.getBytes(StandardCharsets.UTF_8)));
        String sign = SecurityUtil.sign(emailEncode, secret);

        String UNSUBSCRIBE_LINK_TEMPLATE = "%s/marketing/unsubscribe?secret=%s&sign=%s";
        String unsubscribeLink = String.format(UNSUBSCRIBE_LINK_TEMPLATE, gatewayConfiguration.getWwwUrl(), emailEncode, sign);
        return Response.ok(unsubscribeLink);
    }

    /**
     * 订阅
     *
     * @return
     */
    @GetMapping(value = "/store/scenery/section/generate/subscribe")
    public Response<Object> subscribe(@RequestParam Integer emailId) {
        EmailSubscribe emailSubscribe = emailSubscribeService.getById(emailId);
        subscribeMessageSendHelper.sendEmailSubscribeMessage(emailSubscribe);
        return Response.ok();
    }

}
