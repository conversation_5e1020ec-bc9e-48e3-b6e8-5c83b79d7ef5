package com.insta360.store.admin.controller.meta.vo.homeitem;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.HomepageItemCommodityGroup;

/**
 * @Author: wbt
 * @Date: 2021/09/16
 * @Description:
 */
public class AdHomeItemCommodityGroupVO {

    private Integer id;

    /**
     * 类目id
     */
    private Integer homeItemId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 是否禁用（0:启用；1:禁用）
     */
    private Boolean disabled;

    public AdHomeItemCommodityGroupVO() {
    }

    public AdHomeItemCommodityGroupVO(HomepageItemCommodityGroup homepageItemCommodityGroup) {
        if (homepageItemCommodityGroup != null) {
            BeanUtil.copyProperties(homepageItemCommodityGroup, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getHomeItemId() {
        return homeItemId;
    }

    public void setHomeItemId(Integer homeItemId) {
        this.homeItemId = homeItemId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "AdCommodityGroupVO{" +
                "id=" + id +
                ", homeItemId=" + homeItemId +
                ", productId=" + productId +
                ", commodityId=" + commodityId +
                ", orderIndex=" + orderIndex +
                ", disabled=" + disabled +
                '}';
    }
}
