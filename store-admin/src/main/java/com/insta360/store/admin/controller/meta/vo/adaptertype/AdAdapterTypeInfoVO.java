package com.insta360.store.admin.controller.meta.vo.adaptertype;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.AdapterTypeInfo;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/09/13
 * @Description:
 */
public class AdAdapterTypeInfoVO {

    private Integer id;

    /**
     * 适配类型id
     */
    private Integer adapterTypeId;

    /**
     * 语言
     */
    private String language;

    /**
     * 描述信息
     */
    private String infoName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否禁用（0：未禁用；1：禁用）
     */
    private Boolean disabled;

    public AdAdapterTypeInfoVO() {
    }

    public AdAdapterTypeInfoVO(AdapterTypeInfo adapterTypeInfo) {
        if (adapterTypeInfo != null) {
            BeanUtil.copyProperties(adapterTypeInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAdapterTypeId() {
        return adapterTypeId;
    }

    public void setAdapterTypeId(Integer adapterTypeId) {
        this.adapterTypeId = adapterTypeId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getInfoName() {
        return infoName;
    }

    public void setInfoName(String infoName) {
        this.infoName = infoName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "AdAdapterTypeInfoVO{" +
                "id=" + id +
                ", adapterTypeId=" + adapterTypeId +
                ", language='" + language + '\'' +
                ", infoName='" + infoName + '\'' +
                ", createTime=" + createTime +
                ", disabled=" + disabled +
                '}';
    }
}
