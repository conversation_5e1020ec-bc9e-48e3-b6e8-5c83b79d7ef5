package com.insta360.store.admin.controller.insurance.controller.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.insurance.dto.SuitableProductDTO;
import com.insta360.store.business.insurance.service.impl.helper.BindClimbServiceHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/2/18 下午4:24
 */
@Component
public class AdClimbServiceBindProductCachePack {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdClimbServiceBindProductCachePack.class);

    @Autowired
    BindClimbServiceHelper bindClimbServiceHelper;

    /**
     * 更新增值服务套餐信息
     *
     * @param cachePutKeyParameter
     * @param serviceCommodityId
     * @param serviceType
     * @param suitableProducts
     */
    @CachePutMonitor(cacheableType = CachePutType.CLIMB_SERVICE_COMMODITY)
    public void updateClimbServiceCommodity(CachePutKeyParameterBO cachePutKeyParameter, Integer serviceCommodityId,
                                            String serviceType, List<SuitableProductDTO> suitableProducts) {
        LOGGER.info(String.format("增值服务套餐绑定关系更新，缓存更新参数:%s", cachePutKeyParameter.toString()));
        bindClimbServiceHelper.buildBindClimbServiceParams(serviceCommodityId, serviceType, suitableProducts);
    }
}
