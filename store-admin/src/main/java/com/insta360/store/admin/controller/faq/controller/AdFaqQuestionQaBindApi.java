package com.insta360.store.admin.controller.faq.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.faq.cache.AdFaqQuestionCachePack;
import com.insta360.store.admin.controller.faq.vo.AdFaqQuestionQaBindVO;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.faq.dto.QuestionQaBindDTO;
import com.insta360.store.business.faq.exception.FaqErrorCode;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInside;
import com.insta360.store.business.faq.model.FaqQuestionQaBind;
import com.insta360.store.business.faq.service.FaqCategoryQuestionInsideService;
import com.insta360.store.business.faq.service.FaqQuestionQaBindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/4/24
 * @Description:
 */
@PermissionResource(code = "faqQuestionQaBind", desc = "Faq分类页面问题绑定")
@RestController
public class AdFaqQuestionQaBindApi extends BaseAdminApi {

    @Autowired
    FaqQuestionQaBindService questionQaBindService;

    @Autowired
    FaqCategoryQuestionInsideService questionInsideService;

    @Autowired
    AdFaqQuestionCachePack adFaqQuestionCachePack;

    /**
     * 查询页面问题绑定记录
     *
     * @param subsetBindId
     * @return
     */
    @LogAttr(desc = "查询页面问题绑定记录",logType = LogType.query)
    @Permission(code = "store.faq.category.listQuestionQaBind", desc = "查询页面问题绑定记录")
    @GetMapping("/admin/faq/listQuestionQaBind")
    public Response<? extends Map> listQuestionQaBind(@RequestParam(required = false, value = "subsetBindId") Integer subsetBindId) {
        if (subsetBindId == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        List<FaqQuestionQaBind> questionQaBinds = questionQaBindService.listByQuestionSubsetBindsIds(Collections.singletonList(subsetBindId));
        if (CollectionUtils.isEmpty(questionQaBinds)) {
            return Response.ok();
        }
        List<Integer> categoryQuestionInsideIds = questionQaBinds.stream().map(FaqQuestionQaBind::getQuestionId).collect(Collectors.toList());
        Map<Integer, String> categorySubsetInsideMap = questionInsideService.listByIds(categoryQuestionInsideIds)
                .stream().collect(Collectors.toMap(FaqCategoryQuestionInside::getId, FaqCategoryQuestionInside::getCategoryQuestionInsideName));
        // 封装返回
        List<AdFaqQuestionQaBindVO> questionQaBindList = questionQaBinds.stream().map(qaBind -> {
            AdFaqQuestionQaBindVO questionQaBindVO = new AdFaqQuestionQaBindVO(qaBind);
            questionQaBindVO.setQuestionName(categorySubsetInsideMap.get(qaBind.getQuestionId()));
            return questionQaBindVO;
        }).collect(Collectors.toList());
        return Response.ok("faqQuestionQaBinds", questionQaBindList);
    }

    /**
     * 创建页面问题绑定
     *
     * @param qaBindParam
     * @return
     */
    @LogAttr(desc = "创建页面问题绑定")
    @Permission(code = "store.faq.category.createQuestionQaBind", desc = "创建页面问题绑定")
    @PostMapping(path = "/admin/faq/create/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> createQuestionQaBind(@Validated @RequestBody QuestionQaBindDTO qaBindParam) {
        if (qaBindParam.getQuestionId() == null || qaBindParam.getSubsetBindId() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        // 防止重复绑定
        FaqQuestionQaBind qaBind = questionQaBindService.getBySubSetQuestionId(qaBindParam.getQuestionId(), qaBindParam.getSubsetBindId());
        if (qaBind != null) {
            throw new InstaException(FaqErrorCode.FaqQuestionQaBindAlreadyExistException);
        }
        adFaqQuestionCachePack.createQuestionQaBind(qaBindParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 启用页面问题绑定
     *
     * @param qaBindParam
     * @return
     */
    @LogAttr(desc = "启用页面问题绑定")
    @Permission(code = "store.faq.category.enableQuestionQaBind", desc = "启用页面问题绑定")
    @PostMapping(path = "/admin/faq/enable/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> enableQuestionQaBind(@RequestBody QuestionQaBindDTO qaBindParam) {
        FaqQuestionQaBind questionQaBind = questionQaBindService.getById(qaBindParam.getId());
        if (questionQaBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqQuestionCachePack.enableQuestionQaBind(questionQaBind);
        return Response.ok();
    }

    /**
     * 禁用页面问题绑定
     *
     * @param qaBindParam
     * @return、
     */
    @LogAttr(desc = "禁用页面问题绑定")
    @Permission(code = "store.faq.category.disableQuestionQaBind", desc = "禁用页面问题绑定")
    @PostMapping(path = "/admin/faq/disable/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> disableQuestionQaBind(@RequestBody QuestionQaBindDTO qaBindParam) {
        FaqQuestionQaBind questionQaBind = questionQaBindService.getById(qaBindParam.getId());
        if (questionQaBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqQuestionCachePack.disableQuestionQaBind(questionQaBind);
        return Response.ok();
    }

    /**
     * 删除页面问题绑定
     *
     * @param qaBindParam
     * @return
     */
    @LogAttr(desc = "删除页面问题绑定")
    @Permission(code = "store.faq.category.deleteQuestionQaBind", desc = "删除页面问题绑定")
    @PostMapping(path = "/admin/faq/delete/questionQaBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> deleteQuestionQaBind(@RequestBody QuestionQaBindDTO qaBindParam) {
        FaqQuestionQaBind questionQaBind = questionQaBindService.getById(qaBindParam.getId());
        if (questionQaBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqQuestionCachePack.deleteQuestionQaBind(questionQaBind.getId());
        return Response.ok();
    }

    /**
     * 更新问题排序规则
     *
     * @param questionQaBindParam
     * @return
     */
    @LogAttr(desc = "更新问题排序规则")
    @Permission(code = "store.faq.category.updateQuestionQaBindIndex", desc = "更新问题排序规则")
    @PostMapping(path = "/admin/faq/update/questionQaBindIndex", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateQuestionQaBindIndex(@RequestBody List<QuestionQaBindDTO> questionQaBindParam) {
        adFaqQuestionCachePack.updateQuestionQaBindIndex(questionQaBindParam);
        return Response.ok();
    }
}
