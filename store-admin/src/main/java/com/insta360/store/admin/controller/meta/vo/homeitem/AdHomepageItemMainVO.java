package com.insta360.store.admin.controller.meta.vo.homeitem;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.HomepageItemMain;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/09/11
 * @Description:
 */
public class AdHomepageItemMainVO {

    private Integer id;

    /**
     * 栏目名称
     */
    private String homeItemName;

    /**
     * 栏目类型
     */
    private String homeItemType;

    /**
     * 对应多语言文案系统的key
     */
    private String nameKey;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否禁用（0:启用；1:禁用）
     */
    private Boolean disabled;

    public AdHomepageItemMainVO() {
    }

    public AdHomepageItemMainVO(HomepageItemMain homepageItemMain) {
        if (homepageItemMain != null) {
            BeanUtil.copyProperties(homepageItemMain, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getHomeItemName() {
        return homeItemName;
    }

    public void setHomeItemName(String homeItemName) {
        this.homeItemName = homeItemName;
    }

    public String getHomeItemType() {
        return homeItemType;
    }

    public void setHomeItemType(String homeItemType) {
        this.homeItemType = homeItemType;
    }

    public String getNameKey() {
        return nameKey;
    }

    public void setNameKey(String nameKey) {
        this.nameKey = nameKey;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "AdHomepageItemMainVO{" +
                "id=" + id +
                ", homeItemName='" + homeItemName + '\'' +
                ", homeItemType='" + homeItemType + '\'' +
                ", nameKey='" + nameKey + '\'' +
                ", createTime=" + createTime +
                ", disabled=" + disabled +
                '}';
    }
}
