package com.insta360.store.admin.controller.product.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.product.model.ProductVideo;
import com.insta360.store.business.product.service.ProductVideoService;
import com.insta360.store.business.product.service.impl.helper.ProductVideoHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/8/30
 * @Description:
 */
@Component
public class AdProductVideoCachePack {

    @Autowired
    ProductVideoHelper productVideoHelper;

    @Autowired
    ProductVideoService productVideoService;

    /**
     * 新增视频
     *
     * @param cachePutKeyParameter
     * @param productVideoData
     */
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_VIDEO)
    public void insertVideo(CachePutKeyParameterBO cachePutKeyParameter, ProductVideo productVideoData) {
        productVideoHelper.insertVideo(productVideoData);
    }

    /**
     * 删除视频
     *
     * @param cachePutKeyParameter
     * @param productVideoParamId
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_VIDEO)
    public void deleteVideo(CachePutKeyParameterBO cachePutKeyParameter, Integer productVideoParamId) {
        productVideoHelper.deleteVideo(productVideoParamId);
    }


    /**
     * 更新视频
     *
     * @param cachePutKeyParameter
     * @param productVideoData
     */
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_VIDEO)
    public void updateVideo(CachePutKeyParameterBO cachePutKeyParameter, ProductVideo productVideoData) {
        productVideoHelper.updateVideo(productVideoData);
    }

    /**
     * 关联视频拖拽排序
     *
     * @param cachePutKeyParameter
     * @param productVideos
     */
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_VIDEO)
    public void updateOrderIndexByIds(CachePutKeyParameterBO cachePutKeyParameter, List<ProductVideo> productVideos) {
        productVideoService.updateOrderIndexByIds(productVideos);
    }
}
