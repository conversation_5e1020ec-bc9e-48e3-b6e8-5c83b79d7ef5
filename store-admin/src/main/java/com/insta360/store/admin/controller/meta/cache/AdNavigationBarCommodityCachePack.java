package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.meta.dto.NavigationBarCategoryCommodityDTO;
import com.insta360.store.business.meta.model.NavigationBarCategoryCommodity;
import com.insta360.store.business.meta.service.NavigationBarCategoryCommodityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/8/31
 * @Description:
 */
@Component
public class AdNavigationBarCommodityCachePack {

    @Autowired
    NavigationBarCategoryCommodityService navigationBarCategoryCommodityService;

    /**
     * 创建套餐绑定
     *
     * @param categoryCommodityParam
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void createCategoryCommodity(List<NavigationBarCategoryCommodityDTO> categoryCommodityParam) {
        navigationBarCategoryCommodityService.createCategoryCommodity(categoryCommodityParam);
    }

    /**
     * 更新套餐绑定
     *
     * @param categoryCommodity
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void updateCategoryCommodity(NavigationBarCategoryCommodity categoryCommodity) {
        navigationBarCategoryCommodityService.updateCategoryCommodity(categoryCommodity);
    }

    /**
     * 删除套餐绑定
     *
     * @param categoryCommodityId
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void deleteCategoryCommodity(Integer categoryCommodityId) {
        navigationBarCategoryCommodityService.deleteCategoryCommodity(categoryCommodityId);
    }

    /**
     * 启用套餐绑定
     *
     * @param categoryCommodityIds
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void enableCategoryCommodity(List<Integer> categoryCommodityIds) {
        navigationBarCategoryCommodityService.enableCategoryCommodity(categoryCommodityIds);
    }

    /**
     * 禁用套餐绑定
     *
     * @param categoryCommodityIds
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void disableCategoryCommodity(List<Integer> categoryCommodityIds) {
        navigationBarCategoryCommodityService.disableCategoryCommodity(categoryCommodityIds);
    }

    /**
     * 更新套餐绑定排序
     *
     * @param categoryCommodityParam
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void updateCategoryCommodityIndex(List<NavigationBarCategoryCommodityDTO> categoryCommodityParam) {
        navigationBarCategoryCommodityService.updateCategoryCommodityIndex(categoryCommodityParam);
    }

    /**
     * 标记为新品
     *
     * @param categoryCommodity
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void enableNewCategoryCommodity(NavigationBarCategoryCommodity categoryCommodity) {
        navigationBarCategoryCommodityService.enableNewCategoryCommodity(categoryCommodity);
    }

    /**
     * 标记为非新品
     *
     * @param categoryCommodity
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.NAVIGATION_BAR_CATEGORY_KEY)
    public void disableNewCategoryCommodity(NavigationBarCategoryCommodity categoryCommodity) {
        navigationBarCategoryCommodityService.disableNewCategoryCommodity(categoryCommodity);
    }
}
