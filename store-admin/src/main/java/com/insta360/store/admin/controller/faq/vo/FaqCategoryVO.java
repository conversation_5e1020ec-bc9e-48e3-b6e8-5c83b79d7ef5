package com.insta360.store.admin.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqCategory;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/4/22
 * @Description:
 */
public class FaqCategoryVO implements Serializable {

    private Integer id;

    /**
     * 内部一级目录id
     */
    private Integer categoryInsideId;

    /**
     * 多语言类目名称
     */
    private String categoryName;

    /**
     * 语言
     */
    private String language;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public FaqCategoryVO() {
    }

    public FaqCategoryVO(FaqCategory category) {
        if (category != null) {
            BeanUtil.copyProperties(category, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
