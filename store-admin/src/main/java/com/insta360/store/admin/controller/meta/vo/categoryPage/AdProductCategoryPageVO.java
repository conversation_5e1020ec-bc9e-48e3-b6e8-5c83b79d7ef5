package com.insta360.store.admin.controller.meta.vo.categoryPage;

import com.insta360.store.admin.controller.meta.vo.scenerySection.AdScenerySectionVO;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
public class AdProductCategoryPageVO implements Serializable {

    /**
     * 文字筛选器
     */
    private AdProductCategoryTextFilterMainVO textFilter;

    /**
     * 文字筛选器
     */
    private List<AdProductCategoryTextFilterMainVO> textFilterList;

    /**
     * 图文筛选器
     */
    private List<AdProductCategoryImageTextSelectorVO> imageTextSelectorList;

    /**
     * 置顶排序
     */
    private List<AdProductCategoryTopSortCommodityVO> topSortCommodityList;

    /**
     * 场景专区
     */
    private List<AdScenerySectionVO> scenerySectionList;

    public AdProductCategoryTextFilterMainVO getTextFilter() {
        return textFilter;
    }

    public void setTextFilter(AdProductCategoryTextFilterMainVO textFilter) {
        this.textFilter = textFilter;
    }

    public List<AdProductCategoryImageTextSelectorVO> getImageTextSelectorList() {
        return imageTextSelectorList;
    }

    public void setImageTextSelectorList(List<AdProductCategoryImageTextSelectorVO> imageTextSelectorList) {
        this.imageTextSelectorList = imageTextSelectorList;
    }

    public List<AdProductCategoryTopSortCommodityVO> getTopSortCommodityList() {
        return topSortCommodityList;
    }

    public void setTopSortCommodityList(List<AdProductCategoryTopSortCommodityVO> topSortCommodityList) {
        this.topSortCommodityList = topSortCommodityList;
    }

    public List<AdProductCategoryTextFilterMainVO> getTextFilterList() {
        return textFilterList;
    }

    public void setTextFilterList(List<AdProductCategoryTextFilterMainVO> textFilterList) {
        this.textFilterList = textFilterList;
    }

    public List<AdScenerySectionVO> getScenerySectionList() {
        return scenerySectionList;
    }

    public void setScenerySectionList(List<AdScenerySectionVO> scenerySectionList) {
        this.scenerySectionList = scenerySectionList;
    }

    @Override
    public String toString() {
        return "AdProductCategoryPageVO{" +
                "textFilter=" + textFilter +
                ", textFilterList=" + textFilterList +
                ", imageTextSelectorList=" + imageTextSelectorList +
                ", topSortCommodityList=" + topSortCommodityList +
                ", scenerySectionList=" + scenerySectionList +
                '}';
    }
}
