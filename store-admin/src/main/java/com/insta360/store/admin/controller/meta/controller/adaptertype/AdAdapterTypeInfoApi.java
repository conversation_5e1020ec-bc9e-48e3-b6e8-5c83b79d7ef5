package com.insta360.store.admin.controller.meta.controller.adaptertype;

import com.google.common.collect.Lists;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdMetaAdapterTypeCachePack;
import com.insta360.store.admin.controller.meta.vo.adaptertype.AdAdapterTypeInfoVO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import com.insta360.store.business.configuration.search.enums.BatchActionType;
import com.insta360.store.business.configuration.search.enums.DataHandlerActionType;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dto.AdapterTypeInfoDTO;
import com.insta360.store.business.meta.exception.MetaErrorCode;
import com.insta360.store.business.meta.model.AdapterTypeInfo;
import com.insta360.store.business.meta.model.AdapterTypeMain;
import com.insta360.store.business.meta.service.AdapterTypeInfoService;
import com.insta360.store.business.meta.service.AdapterTypeMainService;
import com.insta360.store.business.product.service.ProductAdapterTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/09/13
 * @Description:
 */
@RestController
@PermissionResource(code = "adapterTypeInfo", desc = "适配机型详细信息")
public class AdAdapterTypeInfoApi extends BaseAdminApi {

    @Autowired
    AdapterTypeMainService adapterTypeMainService;

    @Autowired
    AdapterTypeInfoService adapterTypeInfoService;

    @Autowired
    ProductAdapterTypeService productAdapterTypeService;

    @Autowired
    AdMetaAdapterTypeCachePack adMetaAdapterTypeCachePack;

    /**
     * 获取某个适配机型的详细配置
     *
     * @param adapterTypeId
     * @return
     */
    @LogAttr(desc = "获取某个适配机型的详细配置", logType = LogType.query)
    @Permission(code = "store.meta.adapterTypeInfo.listAdapterTypeInfos", desc = "获取某个适配机型的详细配置")
    @GetMapping("/admin/meta/ati/listAdapterTypeInfos")
    public Response<? extends Map> listAdapterTypeInfos(@RequestParam Integer adapterTypeId) {
        // 适配机型配置列表
        List<AdapterTypeInfo> adapterTypeInfos = adapterTypeInfoService.listAdapterTypeInfos(adapterTypeId);
        List<AdAdapterTypeInfoVO> adAdapterTypeInfoVos = adapterTypeInfos.stream().map(AdAdapterTypeInfoVO::new).collect(Collectors.toList());
        return Response.ok("adapterTypeInfos", adAdapterTypeInfoVos);
    }

    /**
     * 新增 adapter type info
     *
     * @param adapterTypeInfoParam
     * @return
     */
    @LogAttr(desc = "新增adapter类型信息")
    @Permission(code = "store.meta.adapterTypeInfo.createAdapterTypeInfo", desc = "新增adapter类型信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN, actionType = BatchActionType.UPDATE)
    @PostMapping("/admin/meta/ati/createAdapterTypeInfo")
    public Response<Object> createAdapterTypeInfo(@RequestBody AdapterTypeInfoDTO adapterTypeInfoParam) {
        if (StringUtil.isBlank(adapterTypeInfoParam.getInfoName()) || StringUtil.isBlank(adapterTypeInfoParam.getLanguage())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 分类校验
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeInfoParam.getAdapterTypeId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        // 搜索数据同步封装
        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeContext);

        // save
        adapterTypeInfoService.createAdapterTypeInfo(adapterTypeInfoParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 更新 adapter type
     *
     * @param adapterTypeInfoParam
     * @return
     */
    @LogAttr(desc = "更新 adapter type")
    @Permission(code = "store.meta.adapterTypeInfo.updateAdapterTypeInfo", desc = "更新adapter")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN)
    @PostMapping("/admin/meta/ati/updateAdapterTypeInfo")
    public Response<Object> updateAdapterTypeInfo(@RequestBody AdapterTypeInfoDTO adapterTypeInfoParam) {
        AdapterTypeInfo adapterTypeInfo = adapterTypeInfoService.getById(adapterTypeInfoParam.getId());
        if (adapterTypeInfo == null || adapterTypeInfo.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeInfoNotFoundException);
        }

        // 分类校验
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeInfoParam.getAdapterTypeId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setLanguage(InstaLanguage.parse(adapterTypeInfo.getLanguage()));
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        // 搜索数据封装
        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeContext);

        // update
        adMetaAdapterTypeCachePack.updateAdapterTypeInfo(cachePutKeyParameter, adapterTypeInfoParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 删除 adapter type
     *
     * @param adapterTypeInfoParam
     * @return
     */
    @LogAttr(desc = "删除 adapter type")
    @Permission(code = "store.meta.adapterTypeInfo.deleteAdapterTypeInfo", desc = "删除adapter")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN)
    @PostMapping("/admin/meta/ati/deleteAdapterTypeInfo")
    public Response<Object> deleteAdapterTypeInfo(@RequestBody AdapterTypeInfoDTO adapterTypeInfoParam) {
        AdapterTypeInfo adapterTypeInfo = adapterTypeInfoService.getById(adapterTypeInfoParam.getId());
        if (adapterTypeInfo == null || adapterTypeInfo.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeInfoNotFoundException);
        }

        // 分类校验
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeInfoParam.getAdapterTypeId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setLanguage(InstaLanguage.parse(adapterTypeInfo.getLanguage()));
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        // 搜索数据同步封装参数 不需要查询product是因为删除的是子记录,重新查一次即可
        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeContext);

        // delete
        adMetaAdapterTypeCachePack.deleteAdapterTypeInfo(cachePutKeyParameter, adapterTypeInfo);
        return Response.ok();
    }

    /**
     * 启用 adapter type
     *
     * @param adapterTypeInfoParam
     * @return
     */
    @LogAttr(desc = "启用 adapter type")
    @Permission(code = "store.meta.adapterTypeInfo.enableAdapterTypeInfo", desc = "启用adapter")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN)
    @PostMapping("/admin/meta/ati/enableAdapterTypeInfo")
    public Response<Object> enableAdapterTypeInfo(@RequestBody AdapterTypeInfoDTO adapterTypeInfoParam) {
        AdapterTypeInfo adapterTypeInfo = adapterTypeInfoService.getById(adapterTypeInfoParam.getId());
        if (adapterTypeInfo == null || adapterTypeInfo.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeInfoNotFoundException);
        }

        // 分类校验
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeInfoParam.getAdapterTypeId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setLanguage(InstaLanguage.parse(adapterTypeInfo.getLanguage()));
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        // 搜索数据封装
        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeContext);


        // enable
        adMetaAdapterTypeCachePack.enableAdapterTypeInfo(cachePutKeyParameter, adapterTypeInfo);
        return Response.ok();
    }

    /**
     * 禁用 adapter type
     *
     * @param adapterTypeInfoParam
     * @return
     */
    @LogAttr(desc = "禁用 adapter type")
    @Permission(code = "store.meta.adapterTypeInfo.disableAdapterTypeInfo", desc = "禁用adapter")
    @TrackSearchDataChange(changeType = SearchDataChangeType.ADAPTER_TYPE_MAIN)
    @PostMapping("/admin/meta/ati/disableAdapterTypeInfo")
    public Response<Object> disableAdapterTypeInfo(@RequestBody AdapterTypeInfoDTO adapterTypeInfoParam) {
        AdapterTypeInfo adapterTypeInfo = adapterTypeInfoService.getById(adapterTypeInfoParam.getId());
        if (adapterTypeInfo == null || adapterTypeInfo.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeInfoNotFoundException);
        }

        // 分类校验
        AdapterTypeMain adapterTypeMain = adapterTypeMainService.getById(adapterTypeInfoParam.getAdapterTypeId());
        if (adapterTypeMain == null || adapterTypeMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setLanguage(InstaLanguage.parse(adapterTypeInfo.getLanguage()));
        cachePutKeyParameter.setAdapterTypeId(adapterTypeMain.getId());

        // 搜索数据封装
        SearchDataChangeContext searchDataChangeContext = new SearchDataChangeContext();
        searchDataChangeContext.setAdapterTypeMainIds(Lists.newArrayList(adapterTypeMain.getId()));
        searchDataChangeContext.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeContext);

        // disable
        adMetaAdapterTypeCachePack.disableAdapterTypeInfo(cachePutKeyParameter, adapterTypeInfo);
        return Response.ok();
    }
}
