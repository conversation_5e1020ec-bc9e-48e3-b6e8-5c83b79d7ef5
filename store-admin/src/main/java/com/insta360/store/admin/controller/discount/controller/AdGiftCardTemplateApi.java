package com.insta360.store.admin.controller.discount.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.business.discount.dto.ao.CreateCommonDiscountAO;
import com.insta360.store.business.discount.dto.ao.DeleteCommonDiscountAO;
import com.insta360.store.business.discount.dto.ao.GiftCardTemplateUpdateAO;
import com.insta360.store.business.discount.dto.ao.giftcard.GiftCardQueryPageAO;
import com.insta360.store.business.discount.dto.ro.*;
import com.insta360.store.business.discount.enums.DiscountModel;
import com.insta360.store.business.discount.enums.GiftCardEffectType;
import com.insta360.store.business.discount.enums.PlatformSourceType;
import com.insta360.store.business.discount.model.GiftCardInfo;
import com.insta360.store.business.discount.model.GiftCardTemplate;
import com.insta360.store.business.discount.service.DiscountDomainService;
import com.insta360.store.business.discount.service.DiscountWritService;
import com.insta360.store.business.discount.service.GiftCardInfoService;
import com.insta360.store.business.discount.service.GiftCardTemplateService;
import com.insta360.store.business.discount.service.impl.factory.DiscountServiceFactory;
import com.insta360.store.business.exception.CommonErrorCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2021/3/23
 * @Description:
 */
@RestController
@PermissionResource(code = "giftCardTemplate", desc = "优惠-代金券模板管理")
public class AdGiftCardTemplateApi extends BaseAdminApi {


    @Autowired
    GiftCardInfoService giftCardInfoService;

    @Autowired
    DiscountWritService discountWritService;

    @Autowired
    GiftCardTemplateService giftCardTemplateService;

    @Autowired
    DiscountServiceFactory discountServiceFactory;


    /**
     * 创建代金券模版
     *
     * @param createCommonDiscountAO
     * @return
     */
    @LogAttr(desc = "创建代金券模版")
    @Permission(code = "store.discount.giftCardTemplate.createGiftCardTemplate", desc = "创建代金券模版")
    @PostMapping(path = "/admin/giftCardTemplate/generate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<CreateCommonDiscountRo> create(@Validated @RequestBody CreateCommonDiscountAO createCommonDiscountAO) {
        if (Objects.isNull(createCommonDiscountAO)) {
            throw new InstaException(-1, "创建失败，参数为空");
        }
        //构建创建模版必要参数
        buildCreateReq(createCommonDiscountAO);
        CreateCommonDiscountRo commonDiscountRo = discountWritService.create(createCommonDiscountAO);
        return Response.ok(commonDiscountRo);
    }

    /**
     * 分页查询代金券模版
     *
     * @param code
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @LogAttr(desc = "分页查询代金券模版", logType = LogType.query)
    @Permission(code = "store.discount.giftCardTemplate.queryGiftCardTemplatePage", desc = "查询代金券模版列表")
    @GetMapping(path = "/admin/giftCardTemplate/query")
    public Response<? extends Map> queryGiftCardTemplatePage(@RequestParam(required = false, value = "code") String code,
                                                             @RequestParam(required = false, value = "pageNumber", defaultValue = "1") Integer pageNumber,
                                                             @RequestParam(required = false, value = "pageSize", defaultValue = "10") Integer pageSize) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber);
        pageQuery.setPageSize(pageSize);

        GiftCardQueryPageAO giftCardQueryPageAo = new GiftCardQueryPageAO();
        giftCardQueryPageAo.setCode(code);
        giftCardQueryPageAo.setAdminJobNumber(getAdminJobNumber());
        giftCardQueryPageAo.setPageQuery(pageQuery);

        PageResult<GiftCardTemplate> giftCardTemplatePageResult = giftCardTemplateService.queryGiftCardTemplatePage(giftCardQueryPageAo);
        List<GiftCardTemplate> giftCardTemplateList = giftCardTemplatePageResult.getList();
        if (CollectionUtils.isEmpty(giftCardTemplateList)) {
            return Response.ok();
        }

        List<GiftCardCommonQueryRespRO> giftCardCommonQueryRespRoList = giftCardTemplateList.stream().map(giftCardTemplate -> {
            GiftCardCommonQueryRespRO giftCardCommonQueryRespRo = new GiftCardCommonQueryRespRO();
            BeanUtils.copyProperties(giftCardTemplate, giftCardCommonQueryRespRo);
            giftCardCommonQueryRespRo.setCode(giftCardTemplate.getTemplateCode());
            GiftCardInfo giftCardInfo = giftCardInfoService.getById(giftCardTemplate.getInfoTag());
            if (Objects.nonNull(giftCardInfo)) {
                InfoTagRO infoTagRO = new InfoTagRO();
                BeanUtils.copyProperties(giftCardInfo, infoTagRO);
                giftCardCommonQueryRespRo.setInfo(infoTagRO);
            }
            return giftCardCommonQueryRespRo;
        }).collect(Collectors.toList());

        DiscountDomainService service = discountServiceFactory.getDiscountDomainService(DiscountModel.GIFT_CODE.code);
        giftCardCommonQueryRespRoList.stream().forEach(giftCardCommonQueryRespRo -> {
            String templateCode = giftCardCommonQueryRespRo.getCode();
            //2、门槛组装
            ThresholdCommonRO giftCardThresholdRo = service.getDiscountThreshold(templateCode, getApiLanguage());
            //3、政策组装
            List<PolicyCommonRO> giftCardPolicyRoList = service.getDiscountPolicy(templateCode, getApiLanguage());
            giftCardCommonQueryRespRo.setGiftCardThreshold(giftCardThresholdRo);
            giftCardCommonQueryRespRo.setGiftCardPolicyList(giftCardPolicyRoList);
        });

        return Response.ok(giftCardTemplatePageResult.replaceList(giftCardCommonQueryRespRoList));
    }

    /**
     * 查询代金券模版详情
     *
     * @param templateCode
     * @return
     */
    @LogAttr(desc = "查询代金券模版详情", logType = LogType.query)
    @Permission(code = "store.discount.giftCardTemplate.getGiftCardTemplateDetail", desc = "查询代金券模版详情")
    @GetMapping(path = "/admin/giftCardTemplate/getGiftCardTemplateDetail")
    public Response<GiftCardCommonQueryRespRO> getGiftCardTemplateDetail(@RequestParam String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        GiftCardTemplate giftCardTemplate = giftCardTemplateService.getByCode(templateCode);
        GiftCardCommonQueryRespRO giftCardCommonQueryRespRO = new GiftCardCommonQueryRespRO(giftCardTemplate);
        giftCardCommonQueryRespRO.setCode(giftCardTemplate.getTemplateCode());
        Optional.ofNullable(giftCardTemplate.getInfoTag())
                .ifPresent(
                        infoTag -> {
                            Optional.ofNullable(giftCardInfoService.getById(infoTag)).ifPresent(giftCardInfo -> {
                                InfoTagRO infoTagRO = new InfoTagRO();
                                BeanUtils.copyProperties(giftCardInfo, infoTagRO);
                                giftCardCommonQueryRespRO.setInfo(infoTagRO);
                            });
                        });
        DiscountDomainService service = discountServiceFactory.getDiscountDomainService(DiscountModel.GIFT_CODE.code);
        Optional.ofNullable(service.getDiscountThreshold(templateCode, getApiLanguage())).ifPresent(thresholdCommonRO -> giftCardCommonQueryRespRO.setGiftCardThreshold(thresholdCommonRO));
        Optional.ofNullable(service.getDiscountPolicy(templateCode, getApiLanguage())).ifPresent(policyCommonList -> giftCardCommonQueryRespRO.setGiftCardPolicyList(policyCommonList));

        return Response.ok(giftCardCommonQueryRespRO);
    }

    /**
     * 修改代金券模版
     *
     * @param giftCardTemplateUpdate
     * @return
     */
    @LogAttr(desc = "修改代金券模版")
    @Permission(code = "store.discount.giftCardTemplate.updateGiftCardTemplate", desc = "修改代金券模版")
    @PostMapping(path = "/admin/giftCardTemplate/updateGiftCardTemplate", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateGiftCardTemplate(@RequestBody GiftCardTemplateUpdateAO giftCardTemplateUpdate) {
        //模版更新前置检查
        giftCardTemplateUpdateCheck(giftCardTemplateUpdate);
        //设置当前操作人工号
        giftCardTemplateUpdate.setJobNumber(getAdminJobNumber());
        return Response.ok(giftCardTemplateService.update(giftCardTemplateUpdate));
    }

    /**
     * 删除代金券模版
     *
     * @param code
     * @return
     */
    @LogAttr(desc = "删除代金券模版")
    @Permission(code = "store.discount.giftCardTemplate.removeGiftCardTemplate", desc = "删除代金券模版")
    @GetMapping(path = "/admin/giftCardTemplate/remove")
    public Response<Object> deleteGiftCardTemplate(@RequestParam String code) {
        GiftCardTemplate giftCardTemplate = giftCardTemplateService.getByCode(code);
        if (Objects.isNull(giftCardTemplate)) {
            return Response.ok();
        }
        DeleteCommonDiscountAO deleteCommonDiscountAO = new DeleteCommonDiscountAO();
        deleteCommonDiscountAO.setId(giftCardTemplate.getId());
        deleteCommonDiscountAO.setTemplateMark(true);

        DiscountDomainService service = discountServiceFactory.getDiscountDomainService(DiscountModel.GIFT_CODE.code);
        service.remove(deleteCommonDiscountAO);
        return Response.ok(giftCardTemplate.getTemplateCode());
    }


    /**
     * 构建创建模版的必要请求参数
     *
     * @param createCommonDiscountAO
     */
    private void buildCreateReq(CreateCommonDiscountAO createCommonDiscountAO) {
        createCommonDiscountAO.setAdminJobNumber(getAdminJobNumber());
        createCommonDiscountAO.setDiscountModel(DiscountModel.GIFT_CODE.code);
        createCommonDiscountAO.setPlatformSource(PlatformSourceType.OPERATION.code);
        createCommonDiscountAO.setTemplateMark(true);
    }

    /**
     * 代金券模版更新前置检查
     *
     * @param giftCardTemplateUpdate
     */
    private void giftCardTemplateUpdateCheck(GiftCardTemplateUpdateAO giftCardTemplateUpdate) {
        GiftCardEffectType giftCardEffectType = GiftCardEffectType.matchType(giftCardTemplateUpdate.getSubEffectType());
        switch (giftCardEffectType) {
            case FIXED_EFFECTIVE_DAYS:
                if (Objects.isNull(giftCardTemplateUpdate.getValidDayNumber())) {
                    throw new InstaException(-1, "子券有效天数不能为空");
                }
                break;

            case FLEXIBLE_TIME_PERIOD:
                if (Objects.isNull(giftCardTemplateUpdate.getSubEffectTime()) || Objects.isNull(giftCardTemplateUpdate.getSubInvalidTime())) {
                    throw new InstaException(-1, "子券生效时间段不能为空");
                }
                LocalDateTime subInvalidTime = TimeUtil.parseLocalDateTime(giftCardTemplateUpdate.getSubInvalidTime());
                LocalDateTime subEffectTime = TimeUtil.parseLocalDateTime(giftCardTemplateUpdate.getSubEffectTime());
                if (subInvalidTime.isBefore(subEffectTime)) {
                    throw new InstaException(-1, "子券失效时间不能在生效时间之前");
                }
                break;

            default:
                throw new InstaException(-1, "子券有效类型异常");
        }
    }

}
