package com.insta360.store.admin.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInfo;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/4/22
 * @Description:
 */
public class FaqCategoryQuestionInfoVO implements Serializable {

    private Integer id;

    /**
     * 内部分类问题id
     */
    private Integer categoryQuestionInsideId;

    /**
     * 语言
     */
    private String language;

    /**
     * 地区
     */
    private String area;

    /**
     * 问题
     */
    private String question;

    /**
     * 回答
     */
    private String answer;


    /**
     * 回答-text版
     */
    private String answerText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public FaqCategoryQuestionInfoVO() {
    }

    public FaqCategoryQuestionInfoVO(FaqCategoryQuestionInfo questionInfo) {
        if (questionInfo != null) {
            BeanUtil.copyProperties(questionInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryQuestionInsideId() {
        return categoryQuestionInsideId;
    }

    public void setCategoryQuestionInsideId(Integer categoryQuestionInsideId) {
        this.categoryQuestionInsideId = categoryQuestionInsideId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getAnswerText() {
        return answerText;
    }

    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }
}
