package com.insta360.store.admin.controller.meta.controller.navigationbar;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdNavigationBarSubsetCachePack;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.bo.NavigationCategoryBO;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import com.insta360.store.business.configuration.search.enums.DataHandlerActionType;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dto.NavigationBarCategorySubsetInsideDTO;
import com.insta360.store.business.meta.enums.NavigationBarTypeEnum;
import com.insta360.store.business.meta.model.NavigationBarCategorySubsetInside;
import com.insta360.store.business.meta.service.NavigationBarCategorySubsetInsideService;
import com.insta360.store.business.meta.service.impl.helper.NavigationBarCategoryHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/5/25
 * @Description:
 */
@PermissionResource(code = "navigationBarCategorySubsetInside", desc = "导航栏二级内部")
@RestController
public class AdNavigationBarCategorySubsetInsideApi extends BaseAdminApi {

    @Autowired
    NavigationBarCategorySubsetInsideService navigationBarCategorySubsetInsideService;

    @Autowired
    AdNavigationBarSubsetCachePack adNavigationBarSubsetCachePack;

    @Autowired
    NavigationBarCategoryHelper navigationBarCategoryHelper;

    /**
     * 创建二级内部类目
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "创建二级内部类目")
    @Permission(code = "store.meta.nbc.createCategorySubsetInside", desc = "创建二级内部类目")
    @PostMapping(path = "/admin/meta/nbc/create/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> createCategorySubsetInside(@Validated @RequestBody NavigationBarCategorySubsetInsideDTO categorySubsetInsideParam) {
        NavigationBarCategorySubsetInside categorySubsetInside = categorySubsetInsideParam.getPojoObject();
        navigationBarCategorySubsetInsideService.createCategorySubsetInside(categorySubsetInside);
        return Response.ok(categorySubsetInside.getId());
    }

    /**
     * 更新二级内部类目
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "更新二级内部类目")
    @Permission(code = "store.meta.nbc.updateCategorySubsetInside", desc = "更新二级内部类目")
    @PostMapping(path = "/admin/meta/nbc/update/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateCategorySubsetInside(@Validated @RequestBody NavigationBarCategorySubsetInsideDTO categorySubsetInsideParam) {
        NavigationBarCategorySubsetInside categorySubsetInside = navigationBarCategorySubsetInsideService.getById(categorySubsetInsideParam.getId());
        if (categorySubsetInside == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adNavigationBarSubsetCachePack.updateCategorySubsetInside(categorySubsetInsideParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 删除二级内部类目
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "删除二级内部类目")
    @Permission(code = "store.meta.nbc.deleteCategorySubsetInside", desc = "删除二级内部类目")
    @TrackSearchDataChange(changeType = SearchDataChangeType.NAVIGATION_BAR_CATEGORY)
    @PostMapping(path = "/admin/meta/nbc/delete/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> deleteCategorySubsetInside(@RequestBody NavigationBarCategorySubsetInsideDTO categorySubsetInsideParam) {
        NavigationBarCategorySubsetInside categorySubsetInside = navigationBarCategorySubsetInsideService.getById(categorySubsetInsideParam.getId());
        if (categorySubsetInside == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 搜索数据同步
        NavigationCategoryBO navigationCategoryBo = navigationBarCategoryHelper.packNavigationBo(categorySubsetInside.getCategoryInsideId());
        NavigationBarTypeEnum navigationBarType = navigationCategoryBo.getNavigationBarType();
        if (navigationBarType != null && navigationBarType.isSearchSyncType()) {
            List<Integer> commodityIds = navigationBarCategoryHelper.listNavigationSearchCommodityIds(categorySubsetInside.getCategoryInsideId());
            SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
            searchDataChangeParams.setCommodityIds(commodityIds);
            searchDataChangeParams.setNavigationCategoryBo(navigationCategoryBo);
            searchDataChangeParams.setDataHandlerActionType(DataHandlerActionType.DeleteSpecialHandling);
            SearchDataChangeContext.set(searchDataChangeParams);
        }

        adNavigationBarSubsetCachePack.deleteCategorySubsetInside(categorySubsetInside.getId());
        return Response.ok();
    }

    /**
     * 启用二级内部类目
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "启用二级内部类目")
    @Permission(code = "store.meta.nbc.enableCategorySubsetInside", desc = "启用二级内部类目")
    @PostMapping(path = "/admin/meta/nbc/enable/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> enableCategorySubsetInside(@RequestBody NavigationBarCategorySubsetInsideDTO categorySubsetInsideParam) {
        NavigationBarCategorySubsetInside categorySubsetInside = navigationBarCategorySubsetInsideService.getById(categorySubsetInsideParam.getId());
        if (categorySubsetInside == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adNavigationBarSubsetCachePack.enableCategorySubsetInside(categorySubsetInside);
        return Response.ok();
    }

    /**
     * 禁用二级内部类目
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "禁用二级内部类目")
    @Permission(code = "store.meta.nbc.disableCategorySubsetInside", desc = "禁用二级内部类目")
    @PostMapping(path = "/admin/meta/nbc/disable/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> disableCategorySubsetInside(@RequestBody NavigationBarCategorySubsetInsideDTO categorySubsetInsideParam) {
        NavigationBarCategorySubsetInside categorySubsetInside = navigationBarCategorySubsetInsideService.getById(categorySubsetInsideParam.getId());
        if (categorySubsetInside == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adNavigationBarSubsetCachePack.disableCategorySubsetInside(categorySubsetInside);
        return Response.ok();
    }

    /**
     * 二级类目排序
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "二级类目排序")
    @Permission(code = "store.meta.nbc.updateCategorySubsetInsideIndex", desc = "二级类目排序")
    @PostMapping(path = "/admin/meta/nbc/updateCategorySubsetInsideIndex", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateCategorySubsetInsideIndex(@RequestBody List<NavigationBarCategorySubsetInsideDTO> categorySubsetInsideParam) {
        if (CollectionUtils.isEmpty(categorySubsetInsideParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adNavigationBarSubsetCachePack.updateCategorySubsetInsideIndex(categorySubsetInsideParam);
        return Response.ok();
    }
}
