package com.insta360.store.admin.controller.meta.vo.activity;

import com.insta360.compass.core.enums.InstaCountry;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public class AdActivityLocalesConfigVO implements Serializable {

    /**
     * id
     */
    private Integer id;

    /**
     * 活动ID
     */
    private Integer activityId;

    /**
     * 分组Key
     */
    private String groupKey;

    /**
     * 生效国家
     */
    private InstaCountry country;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public String getGroupKey() {
        return groupKey;
    }

    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public void setCountry(InstaCountry country) {
        this.country = country;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}
