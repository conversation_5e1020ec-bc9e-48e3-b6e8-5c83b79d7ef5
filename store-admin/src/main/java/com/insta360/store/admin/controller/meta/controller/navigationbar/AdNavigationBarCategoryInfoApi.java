package com.insta360.store.admin.controller.meta.controller.navigationbar;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdNavigationBarInsideCachePack;
import com.insta360.store.admin.controller.meta.vo.navigationbar.AdNavigationBarCategoryInfoVO;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.bo.NavigationCategoryBO;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import com.insta360.store.business.configuration.search.enums.DataHandlerActionType;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dto.NavigationBarCategoryInfoDTO;
import com.insta360.store.business.meta.model.NavigationBarCategoryInfo;
import com.insta360.store.business.meta.service.NavigationBarCategoryInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/5/23
 * @Description:
 */
@PermissionResource(code = "navigationBarCategoryInfo", desc = "导航栏一级多语言")
@RestController
public class AdNavigationBarCategoryInfoApi extends BaseAdminApi {

    @Autowired
    NavigationBarCategoryInfoService navigationBarCategoryInfoService;

    @Autowired
    AdNavigationBarInsideCachePack adNavigationBarInsideCachePack;

    /**
     * 查询一级类目多语言
     *
     * @return
     */
    @LogAttr(desc = "查询一级类目多语言", logType = LogType.query)
    @Permission(code = "store.meta.nbc.listCategoryInfo", desc = "查询一级类目多语言")
    @GetMapping("/admin/meta/nbc/listCategoryInfo")
    public Response<Object> listCategoryInfo(@RequestParam(required = false, value = "categoryInsideId") Integer categoryInsideId) {
        List<NavigationBarCategoryInfo> categoryInfoList = navigationBarCategoryInfoService.listByCategoryInsideId(categoryInsideId);
        List<AdNavigationBarCategoryInfoVO> categoryInfos = categoryInfoList.stream().map(AdNavigationBarCategoryInfoVO::new).collect(Collectors.toList());
        return Response.ok(categoryInfos);
    }

    /**
     * 创建一级类目多语言
     *
     * @return
     */
    @LogAttr(desc = "创建一级类目多语言")
    @Permission(code = "store.meta.nbc.createCategoryInfo", desc = "创建一级类目多语言")
    @TrackSearchDataChange(changeType = SearchDataChangeType.NAVIGATION_BAR_CATEGORY)
    @PostMapping(path = "/admin/meta/nbc/create/categoryInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> createCategoryInfo(@Valid @RequestBody List<NavigationBarCategoryInfoDTO> categoryInfoParam) {
        if (CollectionUtils.isEmpty(categoryInfoParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 搜索数据同步
        NavigationBarCategoryInfoDTO navigationBarCategoryInfoDto = categoryInfoParam.get(0);
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setNavigationCategoryBo(new NavigationCategoryBO(navigationBarCategoryInfoDto.getCategoryInsideId()));
        searchDataChangeParams.setDataHandlerActionType(DataHandlerActionType.GeneralHandling);
        SearchDataChangeContext.set(searchDataChangeParams);
        adNavigationBarInsideCachePack.createCategoryInfo(categoryInfoParam);
        return Response.ok();
    }

    /**
     * 更新一级类目多语言
     *
     * @return
     */
    @LogAttr(desc = "更新一级类目多语言")
    @Permission(code = "store.meta.nbc.updateCategoryInfo", desc = "更新一级类目多语言")
    @TrackSearchDataChange(changeType = SearchDataChangeType.NAVIGATION_BAR_CATEGORY)
    @PostMapping(path = "/admin/meta/nbc/update/categoryInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateCategoryInfo(@Valid @RequestBody List<NavigationBarCategoryInfoDTO> categoryInfoParam) {
        if (CollectionUtils.isEmpty(categoryInfoParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        adNavigationBarInsideCachePack.updateCategoryInfo(categoryInfoParam);
        return Response.ok();
    }
}
