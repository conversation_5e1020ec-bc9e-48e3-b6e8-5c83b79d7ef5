package com.insta360.store.admin.controller.test.wxq.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/27
 */
public enum ToolLanguage {

    zh_TW("繁体中文"),
    zh_CN("简体中文"),
    en_US("英语"),
    ja_<PERSON>("日语"),
    de_DE("德语"),
    ko_KR("韩语"),
    ru_RU("俄语"),
    fr_FR("法语"),
    es_ES("西班牙语"),
    pt_PT("葡萄牙语"),
    id_ID("印尼语"),
    it_IT("意大利语"),
    pl_PL("波兰语"),
    ar_SA("阿拉伯语"),
    th_TH("泰语"),
    tr_TR("土耳其语"),
    he_IL("希伯来语"),
    yi_YI("依地语");

    private final String name;

    ToolLanguage(String name) {
        this.name = name;
    }

    public static ToolLanguage getByName(String name) {
        for (ToolLanguage language : ToolLanguage.values()) {
            if (language.name.equals(name)) {
                return language;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }
}
