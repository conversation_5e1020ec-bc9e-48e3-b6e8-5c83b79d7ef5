package com.insta360.store.admin.controller.meta.vo.categoryPage;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategoryTextFilter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
public class AdProductCategoryTextFilterVO implements Serializable {

    private Integer id;

    /**
     * 适配类型id
     */
    private Integer adapterTypeMainId;

    /**
     * 文字筛选器主ID
     */
    private Integer filterTypeMainId;

    /**
     * 内部名称
     */
    private String insideName;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public AdProductCategoryTextFilterVO() {
    }

    public AdProductCategoryTextFilterVO(ProductCategoryTextFilter textFilter) {
        if (Objects.nonNull(textFilter)) {
            BeanUtil.copyProperties(textFilter, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getFilterTypeMainId() {
        return filterTypeMainId;
    }

    public void setFilterTypeMainId(Integer filterTypeMainId) {
        this.filterTypeMainId = filterTypeMainId;
    }

    public Integer getAdapterTypeMainId() {
        return adapterTypeMainId;
    }

    public void setAdapterTypeMainId(Integer adapterTypeMainId) {
        this.adapterTypeMainId = adapterTypeMainId;
    }

    public String getInsideName() {
        return insideName;
    }

    public void setInsideName(String insideName) {
        this.insideName = insideName;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AdProductCategoryTextFilterVO{" +
                "id=" + id +
                ", adapterTypeMainId=" + adapterTypeMainId +
                ", filterTypeMainId=" + filterTypeMainId +
                ", insideName='" + insideName + '\'' +
                ", orderIndex=" + orderIndex +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
