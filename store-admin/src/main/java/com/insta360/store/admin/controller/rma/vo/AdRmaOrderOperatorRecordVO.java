package com.insta360.store.admin.controller.rma.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.rma.model.RmaOrderOperatorRecord;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/1
 */
public class AdRmaOrderOperatorRecordVO implements Serializable {

    /**
     * id
     */
    private Integer id;

    /**
     * 售后订单id
     */
    private Integer rmaOrderId;

    /**
     * 操作员工姓名
     */
    private String username;

    /**
     * 操作员工邮箱
     */
    private String jobEmail;

    /**
     * 编辑标题
     */
    private String operatorTitle;

    /**
     * 编辑类型
     */
    private String operatorType;

    /**
     * 修改前的数据
     */
    private String fromData;

    /**
     * 修改后的数据
     */
    private String toData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public AdRmaOrderOperatorRecordVO() {
    }

    public AdRmaOrderOperatorRecordVO(RmaOrderOperatorRecord rmaOrderOperatorRecord) {
        if (Objects.nonNull(rmaOrderOperatorRecord)) {
            BeanUtil.copyProperties(rmaOrderOperatorRecord, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRmaOrderId() {
        return rmaOrderId;
    }

    public void setRmaOrderId(Integer rmaOrderId) {
        this.rmaOrderId = rmaOrderId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getJobEmail() {
        return jobEmail;
    }

    public void setJobEmail(String jobEmail) {
        this.jobEmail = jobEmail;
    }

    public String getOperatorTitle() {
        return operatorTitle;
    }

    public void setOperatorTitle(String operatorTitle) {
        this.operatorTitle = operatorTitle;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }

    public String getFromData() {
        return fromData;
    }

    public void setFromData(String fromData) {
        this.fromData = fromData;
    }

    public String getToData() {
        return toData;
    }

    public void setToData(String toData) {
        this.toData = toData;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AdRmaOrderOperatorRecordVO{" +
                "id=" + id +
                ", rmaOrderId=" + rmaOrderId +
                ", username='" + username + '\'' +
                ", jobEmail='" + jobEmail + '\'' +
                ", operatorTitle='" + operatorTitle + '\'' +
                ", operatorType='" + operatorType + '\'' +
                ", fromData='" + fromData + '\'' +
                ", toData='" + toData + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
