package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.model.AvalaraTaxProvince;
import com.insta360.store.business.meta.service.AvalaraTaxProvinceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 收税州
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/3
 */
@Component
public class AdAvalaraTaxProvinceCachePack {

    @Autowired
    AvalaraTaxProvinceService avalaraTaxProvinceService;

    /**
     * 启用收税州
     *
     * @param cachePutKeyParameter 缓存更新参数
     * @param avalaraTaxProvince   avalara税州
     * @return {@link Boolean}
     */
    @CachePutMonitor(cacheableType = CachePutType.AVALARA_TAX_PROVINCE)
    public Boolean enabledTaxProvince(CachePutKeyParameterBO cachePutKeyParameter, AvalaraTaxProvince avalaraTaxProvince) {
        return avalaraTaxProvinceService.enabledTaxProvince(avalaraTaxProvince);
    }

    /**
     * 禁用收税州
     *
     * @param cachePutKeyParameter 缓存更新参数
     * @param avalaraTaxProvince   avalara税州
     * @return {@link Boolean}
     */
    @CachePutMonitor(cacheableType = CachePutType.AVALARA_TAX_PROVINCE)
    public Boolean disableTaxProvince(CachePutKeyParameterBO cachePutKeyParameter, AvalaraTaxProvince avalaraTaxProvince) {
        return avalaraTaxProvinceService.disableTaxProvince(avalaraTaxProvince);
    }
}
