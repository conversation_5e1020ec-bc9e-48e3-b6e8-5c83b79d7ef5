package com.insta360.store.admin.controller.commodity.controller.cache;

import com.insta360.store.business.commodity.dto.CommodityStockInfoDTO;
import com.insta360.store.business.commodity.service.impl.helper.CommodityStockHelper;
import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/18 上午10:24
 */
@Component
public class AdCommodityStockCachePack {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdCommodityStockCachePack.class);

    @Autowired
    CommodityStockHelper commodityStockHelper;

    /**
     * 更新/新增库存，可发货地区
     *
     * @param cachePutKeyParameter 缓存更新参数
     * @param commodityStockParams 库存信息
     */
    @CacheEvict(value = {CacheableType.COMMODITY_RECOMMENDATION_CART, CacheableType.COMMODITY_LIST_INFO}, allEntries = true)
    @CachePutMonitor(cacheableType = CachePutType.STOCK_DISPLAY)
    public void doUpsertStockCount(CachePutKeyParameterBO cachePutKeyParameter, CommodityStockInfoDTO commodityStockParams) {
        LOGGER.info(String.format("编辑库存，进入缓存更新层，缓存参数为：%s", cachePutKeyParameter.toString()));
        commodityStockHelper.doUpdateStocks(commodityStockParams);
    }
}
