package com.insta360.store.admin.rpc.commodity.controller;

import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.rpc.commodity.format.AdRepairCommodityPack;
import com.insta360.store.admin.rpc.commodity.vo.RepairCommodityVO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
public class AdRepairmentCommodityServiceApi {

    protected static final Logger LOGGER = LoggerFactory.getLogger(AdRepairmentCommodityServiceApi.class);

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    ProductService productService;

    @Autowired
    AdRepairCommodityPack repairCommodityPack;

    /**
     * the max length of productIdss
     */
    private final int PRODUCTIDS_MAX_SIZE = 50;

    @GetMapping("/rpc/store/admin/repair/product/commodity/listCommodityInfos")
    public Response<List<RepairCommodityVO>> listCommodityInfos(@RequestParam List<Integer> productIds,
                                                                @RequestParam List<String> languages,
                                                                @RequestParam List<String> areas) {

        if (productIds.size() > PRODUCTIDS_MAX_SIZE) {
            throw new InstaException(-1, "The length of the productIds list is greater than the limit length, PRODUCTIDS_MAX_SIZE: " + PRODUCTIDS_MAX_SIZE);
        }

        LOGGER.info("/rpc/store/admin/repair/product/commodity/listCommodityInfos, productIds: {}, languages: {}, areas: {}", productIds, languages, areas);

        List<Commodity> commodities = commodityService.getCommodityByProductList(productIds);
        List<Integer> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());

        // encapsulate as a map object， key：productId， value：List<Commodity>
        Map<Integer, List<Commodity>> commodityMap = commodities.stream().collect(Collectors.groupingBy(Commodity::getProduct));

        // encapsulate as a map object， key：productId， value：Map<String, ProductInfo>
        List<ProductInfo> productInfos = productInfoService.listInfoByProductIdsAndLanguages(productIds, languages);
        Map<Integer, Map<String, ProductInfo>> productLanguageInfoMap = productInfos.stream().collect(Collectors.groupingBy(ProductInfo::getProduct, Collectors.toMap(ProductInfo::getLanguage, productInfo -> productInfo)));

        // encapsulate as a map object， key：commodityId， value：Map<String, CommodityInfo>
        List<CommodityInfo> commodityInfos = commodityInfoService.listInfoByCommodityIdsAndLanguages(commodityIds, languages);
        Map<Integer, Map<String, CommodityInfo>> commodityLanguageInfoMap = commodityInfos.stream().collect(Collectors.groupingBy(CommodityInfo::getCommodity, Collectors.toMap(CommodityInfo::getLanguage, commodityInfo -> commodityInfo)));

        // encapsulate as a map object， key：commodityId， value：Map<Integer, CommodityPrice>
        List<CommodityPrice> commodityPrices = commodityPriceService.listCommodityPriceByProductIdsAndAreas(commodityIds, areas);
        Map<String, Map<Integer, CommodityPrice>> areaLanguagePriceMap = commodityPrices.stream().collect(Collectors.groupingBy(CommodityPrice::getArea, Collectors.toMap(CommodityPrice::getCommodityId, commodityPrice -> commodityPrice)));

        // regional and Language Mapping Relationship
        List<CountryConfig> countryConfigs = countryConfigService.listByCountries(areas);
        Map<String, String> areaLanguageMap = new HashMap<>(2);

        // encapsulate as a map object， key：language， value：area
        for (int i = 0; i < areas.size(); i++) {
            areaLanguageMap.put(countryConfigs.get(i).getLanguage(), areas.get(i));
        }

        // group by productId
        List<RepairCommodityVO> repairCommodityVos = productLanguageInfoMap.entrySet().stream().map(productEntry -> {
            RepairCommodityVO repairCommodityVo = new RepairCommodityVO();
            Integer productId = productEntry.getKey();

            // obtain ProductInfo based on language
            Map<String, ProductInfo> productInfoEntry = productEntry.getValue();
            List<RepairCommodityVO.RepairCommodityInfoVO> repairCommodityInfoVos = new ArrayList<>();

            productInfoEntry.forEach((language, productInfo) ->
                    commodityMap.get(productId).forEach(commodity -> {
                        // encapsulate data for each commodity based on language
                        RepairCommodityVO.RepairCommodityInfoVO repairCommodityInfo = new RepairCommodityVO.RepairCommodityInfoVO();
                        Integer commodityId = commodity.getId();
                        String area = areaLanguageMap.get(language);
                        CommodityPrice commodityPrice = areaLanguagePriceMap.get(area).get(commodityId);
                        if (Objects.nonNull(commodityPrice)) {
                            repairCommodityInfo.setLanguage(language);
                            repairCommodityInfo.setCurrency(commodityPrice.getCurrency());
                            repairCommodityInfo.setAmount(commodityPrice.getAmount());
                            repairCommodityInfo.setCommodityId(commodityId);

                            // if commodity multilingual information is missing, the commodity name is an empty string
                            if (!Objects.isNull(commodityLanguageInfoMap.get(commodityId).get(language))) {
                                CommodityInfo commodityInfo = commodityLanguageInfoMap.get(commodityId).get(language);
                                repairCommodityInfo.setProductName(commodityInfo.getName());
                            }
                        }
                        repairCommodityInfoVos.add(repairCommodityInfo);
                    })
            );

            // filter out elements of repairCommodityInfoVos hollow data
            List<RepairCommodityVO.RepairCommodityInfoVO> repairCommodityInfoVOList = repairCommodityInfoVos.stream()
                    .filter(repairCommodityInfoVO -> Objects.nonNull(repairCommodityInfoVO.getCommodityId()) || Objects.nonNull(repairCommodityInfoVO.getAmount()))
                    .collect(Collectors.toList());

            repairCommodityVo.setProductId(productId);
            repairCommodityVo.setRepairCommodityPrices(repairCommodityInfoVOList);
            return repairCommodityVo;
        }).collect(Collectors.toList());

        return Response.ok(repairCommodityVos);
    }

    /**
     * 批量获取工单套餐信息
     *
     * @param productId
     * @return
     */
    @GetMapping("/rpc/store/admin/repair/product/commodity/listCommodityInfo")
    public Response<RepairCommodityVO> listCommodityInfo(@RequestParam Integer productId) {
        if (Objects.isNull(productId)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 产品ID真实性校验
        Product product = productService.getById(productId);
        if (Objects.isNull(product)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        RepairCommodityVO repairCommodityVo = repairCommodityPack.listCommodityInfo(productId);
        return Response.ok(repairCommodityVo);
    }
}
