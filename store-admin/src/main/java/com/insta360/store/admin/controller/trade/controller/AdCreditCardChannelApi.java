package com.insta360.store.admin.controller.trade.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.trade.vo.AdCreditCardPayChannelVO;
import com.insta360.store.business.trade.model.CreditCardPayChannel;
import com.insta360.store.business.trade.service.CreditCardPayChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/11/16
 * @Description:
 */
@RestController
@PermissionResource(code = "creditCardChannel", desc = "信用卡支付渠道配置")
public class AdCreditCardChannelApi extends BaseAdminApi {

    @Autowired
    CreditCardPayChannelService creditCardPayChannelService;

    /**
     * 获取支持的支付通道
     *
     * @return
     */
    @LogAttr(desc = "获取支持的支付通道", logType = LogType.query)
    @Permission(code = "store.trade.creditCardChannel.listCreditCardPayChannel", desc = "获取支持的支付通道")
    @GetMapping("/admin/trade/credit/card/listCreditCardPayChannel")
    public Response<? extends Map> listCreditCardPayChannel() {
        List<CreditCardPayChannel> creditCardPayChannels = creditCardPayChannelService.listCreditCardPayChannel();

        // 格式转换
        List<AdCreditCardPayChannelVO> creditCardPayChannelVos = creditCardPayChannels.stream().map(AdCreditCardPayChannelVO::new).collect(Collectors.toList());
        return Response.ok("creditCardPayChannels", creditCardPayChannelVos);
    }

}
