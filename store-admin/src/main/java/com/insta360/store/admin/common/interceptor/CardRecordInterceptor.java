package com.insta360.store.admin.common.interceptor;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.admin.common.WebApiContext;
import com.insta360.store.business.insurance.aop.CardRecordContext;
import com.insta360.store.business.insurance.aop.LogCardRecordChange;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description: 实体卡机型和工单号变更操作人
 * @author: py
 * @create: 2023-04-26 17:23
 */
public class CardRecordInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        LogCardRecordChange annotation = handlerMethod.getMethodAnnotation(LogCardRecordChange.class);

        if (annotation != null) {

            // 请求上下文，记录操作人员信息
            CardRecordContext cardRecordContext = ApplicationContextHolder.getApplicationContext().getBean(CardRecordContext.class);
            if (cardRecordContext != null) {
                WebApiContext webContext = WebApiContext.get();

                // 工号
                if (webContext != null) {
                    String adminJobNumber = webContext.getAdminJobNumber();
                    if (StringUtil.isNotBlank(adminJobNumber)) {
                        cardRecordContext.setOperator(adminJobNumber);
                    }
                }
            }

            // 创建
            CardRecordContext.set(cardRecordContext);
        }

        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);

        // 销毁
        CardRecordContext.remove();
    }
}
