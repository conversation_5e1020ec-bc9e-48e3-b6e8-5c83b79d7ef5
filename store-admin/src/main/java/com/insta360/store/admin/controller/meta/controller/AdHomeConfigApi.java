package com.insta360.store.admin.controller.meta.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.vo.AdHomeItemVO;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.meta.dto.HomepageItemDTO;
import com.insta360.store.business.meta.model.HomepageItem;
import com.insta360.store.business.meta.service.HomepageItemService;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019-07-15
 * @Description: 文档完备
 */
@RestController
@PermissionResource(code = "homeConfig", desc = "主页设置")
public class AdHomeConfigApi extends BaseAdminApi {

    @Autowired
    HomepageItemService homeItemService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    /**
     * 获取页面元素
     *
     * @return
     */
    @LogAttr(desc = "获取页面元素", logType = LogType.query)
    @Permission(code = "store.meta.homeConfig.getHomeItems", desc = "获取页面元素")
    @GetMapping("/admin/meta/getHomeItems")
    public Response<? extends Map> getHomeItems() {
        InstaLanguage language = getApiLanguage();

        List<HomepageItem> items = homeItemService.listHomepageItems();
        List<AdHomeItemVO> homeItemVOS = items
                .stream()
                .map(item -> {
                    AdHomeItemVO homeItemVO = new AdHomeItemVO(item);
                    ProductInfo productInfo = productInfoService.getInfo(item.getProduct(), language);
                    homeItemVO.setProductInfo(productInfo);

                    Integer commodityId = item.getPriceReferenceCommodity();
                    CommodityInfo commodityInfo = commodityInfoService.getInfo(commodityId, language);
                    homeItemVO.setCommodityInfo(commodityInfo);
                    return homeItemVO;
                }).collect(Collectors.toList());

        return Response.ok("items", homeItemVOS);
    }

    /**
     * 更新页面元素
     *
     * @param homeItemParam
     * @return
     */
    @LogAttr(desc = "更新页面元素")
    @Permission(code = "store.meta.homeConfig.upsertHomeItem", desc = "更新页面元素")
    @PostMapping(path = "/admin/meta/upsertHomeItem", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Boolean> upsertHomeItem(@RequestBody HomepageItemDTO homeItemParam) {
        boolean b = homeItemService.saveOrUpdate(homeItemParam.getPojoObject());
        return Response.ok(b);
    }
}
