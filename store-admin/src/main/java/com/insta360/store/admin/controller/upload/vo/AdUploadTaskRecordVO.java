package com.insta360.store.admin.controller.upload.vo;

import com.insta360.store.business.admin.upload.common.UploadConstant;
import com.insta360.store.business.admin.upload.enums.UploadStatus;
import com.insta360.store.business.admin.upload.model.UploadTaskRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 上传任务记录VO
 * @Date 2023/10/11
 */
public class AdUploadTaskRecordVO implements Serializable {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 模块类型
     */
    private Integer moduleType;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 异常文件oss链接
     */
    private String errorFileUrl;

    /**
     * 文件上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 文件导入时间
     */
    private LocalDateTime importTime;

    /**
     * 节点状态
     *
     * @see UploadStatus
     */
    private Integer state;

    /**
     * 节点状态描述
     */
    private String stateDesc;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 总提示
     */
    private String generalTips;

    /**
     * 转换
     *
     * @param uploadTaskRecord
     * @return
     */
    public static AdUploadTaskRecordVO convert(UploadTaskRecord uploadTaskRecord) {
        if (Objects.isNull(uploadTaskRecord)) {
            return null;
        }
        AdUploadTaskRecordVO recordVo = new AdUploadTaskRecordVO();
        BeanUtils.copyProperties(uploadTaskRecord, recordVo);

        UploadStatus status = uploadTaskRecord.statusParse();
        String tips = null;
        if (UploadStatus.FAIL.equals(status) && StringUtils.isNotBlank(uploadTaskRecord.getErrorFileUrl())) {
            tips = UploadConstant.uploadFailTips;
        } else if (UploadStatus.WAITING_CONFIRMED.equals(status) && StringUtils.isNotBlank(uploadTaskRecord.getErrorFileUrl())) {
            tips = UploadConstant.uploadWaitingConfirmedTips;
        }

        recordVo.setGeneralTips(tips);
        recordVo.setStateDesc(status.getValue());

        return recordVo;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getModuleType() {
        return moduleType;
    }

    public void setModuleType(Integer moduleType) {
        this.moduleType = moduleType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getErrorFileUrl() {
        return errorFileUrl;
    }

    public void setErrorFileUrl(String errorFileUrl) {
        this.errorFileUrl = errorFileUrl;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    public LocalDateTime getImportTime() {
        return importTime;
    }

    public void setImportTime(LocalDateTime importTime) {
        this.importTime = importTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getStateDesc() {
        return stateDesc;
    }

    public void setStateDesc(String stateDesc) {
        this.stateDesc = stateDesc;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public String getGeneralTips() {
        return generalTips;
    }

    public void setGeneralTips(String generalTips) {
        this.generalTips = generalTips;
    }
}
