package com.insta360.store.admin.controller.discount.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.discount.model.ActivityGift;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019-11-04
 * @Description:
 */
public class AdActivityGiftVO extends ActivityGift {

    @JSO<PERSON>ield(name = "tag_id")
    private Integer tagId;

    @JSONField(name = "product_name")
    private String productName;

    @JSONField(name = "commodity_name")
    private String commodityName;

    @JSONField(name = "gift_items")
    private List<AdGiftItemVO> giftItems;

    public AdActivityGiftVO() {
    }

    public AdActivityGiftVO(ActivityGift gift) {
        if (gift != null) {
            BeanUtil.copyProperties(gift, this);
        }
    }

    public Integer getTagId() {
        return tagId;
    }

    public void setTagId(Integer tagId) {
        this.tagId = tagId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public List<AdGiftItemVO> getGiftItems() {
        return giftItems;
    }

    public void setGiftItems(List<AdGiftItemVO> giftItems) {
        this.giftItems = giftItems;
    }
}
