package com.insta360.store.admin.controller.product.format;

import com.insta360.store.admin.controller.product.vo.AdProductFaqVO;
import com.insta360.store.business.product.model.FaqQuestion;
import com.insta360.store.business.product.model.ProductFaq;
import com.insta360.store.business.product.service.FaqQuestionService;
import com.insta360.store.business.product.service.ProductFaqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: faq pack
 * @author: py
 * @create: 2023-10-17 11:54
 */
@Component
public class AdProductFaqPack {

    @Autowired
    FaqQuestionService faqQuestionService;

    @Autowired
    ProductFaqService productFaqService;


    /**
     * 获取FAQs编辑单个问题的所有语言信息
     *
     * @param questionId
     */
    public AdProductFaqVO listFaqDetails(Integer questionId) {
        FaqQuestion faqQuestion = faqQuestionService.getById(questionId);
        AdProductFaqVO productFaqVo = new AdProductFaqVO(faqQuestion);
        // faqs
        List<ProductFaq> faqs = productFaqService.listByQuestionId(questionId);
        List<AdProductFaqVO.AdProductFaqBean> faqVos = faqs.stream().map(AdProductFaqVO.AdProductFaqBean::new).collect(Collectors.toList());
        productFaqVo.setFaqs(faqVos);
        return productFaqVo;
    }

    /**
     * 获取FAQs页面展示信息
     *
     * @param productId
     * @return
     */
    public List<AdProductFaqVO.AdFaqQuestionBean> listFaqs(Integer productId) {
        List<FaqQuestion> faqQuestions = faqQuestionService.listByProductId(productId);
        return faqQuestions.stream()
                .map(AdProductFaqVO.AdFaqQuestionBean::new).collect(Collectors.toList());
    }
}
