package com.insta360.store.admin.controller.faq.format;

import com.insta360.store.admin.controller.faq.vo.*;
import com.insta360.store.business.faq.model.*;
import com.insta360.store.business.faq.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/5/5
 * @Description:
 */
@Component
public class AdFaqCategoryPack {

    @Autowired
    FaqCategorySubsetInsideService subsetInsideService;

    @Autowired
    FaqCategoryInsideService categoryInsideService;

    @Autowired
    FaqQuestionSubsetBindService questionSubsetBindService;

    @Autowired
    FaqCategoryQuestionInsideService questionInsideService;

    @Autowired
    FaqQuestionQaBindService questionQaBindService;

    /**
     * 封装一二级类目问题内部类目
     *
     * @param categoryInsideList
     * @return
     */
    public List<FaqCategoryInsideVO> packCategoryInsides(List<FaqCategoryInside> categoryInsideList) {
        return categoryInsideList.stream().map(categoryInside -> {
            FaqCategoryInsideVO categoryInsideVO = new FaqCategoryInsideVO(categoryInside);
            List<FaqCategorySubsetInsideVO> subsetInsideVOS = subsetInsideService.listCategoryInsideId(categoryInside.getId())
                    .stream().map(categorySubsetInside -> {
                        FaqCategorySubsetInsideVO categorySubsetInsideVO = new FaqCategorySubsetInsideVO(categorySubsetInside);
                        List<FaqCategoryQuestionInside> categoryQuestionInsides = questionInsideService.listBySubsetInsideIds(Collections.singletonList(categorySubsetInside.getId()));
                        List<FaqCategoryQuestionInsideVO> questionInsides = categoryQuestionInsides.stream().map(FaqCategoryQuestionInsideVO::new).collect(Collectors.toList());
                        categorySubsetInsideVO.setCategoryQuestionInsides(questionInsides);
                        return categorySubsetInsideVO;
                    }).collect(Collectors.toList());
            categoryInsideVO.setSubsetInsides(subsetInsideVOS);
            return categoryInsideVO;
        }).collect(Collectors.toList());
    }

    /**
     * 封装页面一级绑定
     *
     * @param questionBinds
     * @return
     */
    public List<AdFaqQuestionBindVO> packQuestionBinds(List<FaqQuestionBind> questionBinds) {
        List<Integer> categoryInsideIds = questionBinds.stream().map(FaqQuestionBind::getCategoryInsideId).collect(Collectors.toList());
        Map<Integer, String> categoryInsideMap = categoryInsideService.listByIds(categoryInsideIds)
                .stream().collect(Collectors.toMap(FaqCategoryInside::getId, FaqCategoryInside::getCategoryInsideName));
        // 封装返回
        return questionBinds.stream().map(questionBind -> {
            AdFaqQuestionBindVO questionBindVO = new AdFaqQuestionBindVO(questionBind);
            questionBindVO.setCategoryInsideName(categoryInsideMap.get(questionBind.getCategoryInsideId()));
            // 封装二级绑定
            List<FaqQuestionSubsetBind> questionSubsetBinds = questionSubsetBindService.listByQuestionBindsIds(Collections.singletonList(questionBind.getId()));
            if (CollectionUtils.isEmpty(questionSubsetBinds)) {
                questionBindVO.setQuestionSubsetBinds(Collections.emptyList());
                return questionBindVO;
            }
            // 封装返回
            questionBindVO.setQuestionSubsetBinds(packQuestionSubsetBinds(questionSubsetBinds));
            return questionBindVO;
        }).collect(Collectors.toList());
    }

    /**
     * 封装页面二级绑定
     *
     * @param questionSubsetBinds
     * @return
     */
    public List<AdFaqQuestionSubsetBindVO> packQuestionSubsetBinds(List<FaqQuestionSubsetBind> questionSubsetBinds) {
        List<Integer> categorySubsetInsideIds = questionSubsetBinds.stream().map(FaqQuestionSubsetBind::getCategorySubsetInsideId).collect(Collectors.toList());
        Map<Integer, String> categorySubsetInsideMap = subsetInsideService.listByIds(categorySubsetInsideIds)
                .stream().collect(Collectors.toMap(FaqCategorySubsetInside::getId, FaqCategorySubsetInside::getCategorySubsetInsideName));

        return questionSubsetBinds.stream().map(subsetBind -> {
            AdFaqQuestionSubsetBindVO questionSubsetBindVO = new AdFaqQuestionSubsetBindVO(subsetBind);
            questionSubsetBindVO.setCategorySubsetInsideName(categorySubsetInsideMap.get(subsetBind.getCategorySubsetInsideId()));
            // 封装问题
            List<FaqQuestionQaBind> questionQaBinds = questionQaBindService.listByQuestionSubsetBindsIds(Collections.singletonList(subsetBind.getId()));
            if (CollectionUtils.isEmpty(questionQaBinds)) {
                questionSubsetBindVO.setQuestionQaBinds(Collections.emptyList());
                return questionSubsetBindVO;
            }
            // 封装返回
            questionSubsetBindVO.setQuestionQaBinds(packQuestionQaBinds(questionQaBinds));
            return questionSubsetBindVO;
        }).collect(Collectors.toList());
    }

    /**
     * 封装页面问题绑定
     *
     * @param questionQaBinds
     * @return
     */
    public List<AdFaqQuestionQaBindVO> packQuestionQaBinds(List<FaqQuestionQaBind> questionQaBinds) {
        List<Integer> categoryQuestionInsideIds = questionQaBinds.stream().map(FaqQuestionQaBind::getQuestionId).collect(Collectors.toList());
        Map<Integer, String> categoryQuestionInsideMap = questionInsideService.listByIds(categoryQuestionInsideIds)
                .stream().collect(Collectors.toMap(FaqCategoryQuestionInside::getId, FaqCategoryQuestionInside::getCategoryQuestionInsideName));

        return questionQaBinds.stream().map(qaBind -> {
            AdFaqQuestionQaBindVO questionQaBindVO = new AdFaqQuestionQaBindVO(qaBind);
            questionQaBindVO.setQuestionName(categoryQuestionInsideMap.get(qaBind.getQuestionId()));
            return questionQaBindVO;
        }).collect(Collectors.toList());
    }
}
