package com.insta360.store.admin.controller.faq.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.faq.cache.AdFaqCategoryCachePack;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.faq.dto.CategorySubsetInsideDTO;
import com.insta360.store.business.faq.exception.FaqErrorCode;
import com.insta360.store.business.faq.model.FaqCategorySubsetInside;
import com.insta360.store.business.faq.service.FaqCategorySubsetInsideService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/4/19
 * @Description:
 */
@PermissionResource(code = "faqCategorySubsetInside", desc = "Faq二级多语言分类")
@RestController
public class AdFaqCategorySubsetInsideApi extends BaseAdminApi {

    @Autowired
    FaqCategorySubsetInsideService subsetInsideService;

    @Autowired
    AdFaqCategoryCachePack adFaqCategoryCachePack;

    /**
     * 创建二级内部类目
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "创建二级内部类目")
    @Permission(code = "store.faq.category.createCategorySubsetInside", desc = "创建二级内部类目")
    @PostMapping(path = "/admin/faq/create/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> createCategorySubsetInside(@Validated @RequestBody CategorySubsetInsideDTO categorySubsetInsideParam) {
        if (StringUtil.isBlank(categorySubsetInsideParam.getCategorySubsetInsideName())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 新的二级目录key
        String newFaqCategorySubsetKey = categorySubsetInsideParam.getCategorySubsetInsideKey();
        // 二级目录Key校验
        validateCategorySubsetInsideKey(StringUtils.EMPTY, newFaqCategorySubsetKey);
        // 执行创建
        FaqCategorySubsetInside subsetInside = categorySubsetInsideParam.getPojoObject();
        subsetInsideService.createCategorySubsetInside(subsetInside);
        return Response.ok(subsetInside.getId());
    }

    /**
     * 更新二级内部名称
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "更新二级内部名称")
    @Permission(code = "store.faq.category.updateCategorySubsetInside", desc = "更新二级内部名称")
    @PostMapping(path = "/admin/faq/update/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateCategorySubsetInside(@RequestBody CategorySubsetInsideDTO categorySubsetInsideParam) {
        if (categorySubsetInsideParam.getId() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 当前的二级目录key
        String currentFaqCategorySubsetKey = subsetInsideService.getById(categorySubsetInsideParam.getId()).getCategorySubsetInsideKey();
        // 新的二级目录key
        String newFaqCategorySubsetKey = categorySubsetInsideParam.getCategorySubsetInsideKey();
        // 二级目录Key校验
        validateCategorySubsetInsideKey(currentFaqCategorySubsetKey, newFaqCategorySubsetKey);
        // 执行更新
        subsetInsideService.updateCategorySubsetInside(categorySubsetInsideParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 删除二级内部类目
     *
     * @param categorySubsetInsideParam
     * @return
     */
    @LogAttr(desc = "删除二级内部类目")
    @Permission(code = "store.faq.category.deleteCategorySubsetInside", desc = "删除二级内部类目")
    @PostMapping(path = "/admin/faq/delete/categorySubsetInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> deleteCategorySubsetInside(@RequestBody CategorySubsetInsideDTO categorySubsetInsideParam) {
        FaqCategorySubsetInside categorySubsetInside = subsetInsideService.getById(categorySubsetInsideParam.getId());
        if (categorySubsetInside == null) {
            throw new InstaException(FaqErrorCode.FaqCategoryNotFoundException);
        }
        adFaqCategoryCachePack.deleteCategorySubsetInside(categorySubsetInside.getId());
        return Response.ok();
    }

    /**
     * 校验二级目录Key是否合法（非空且唯一）
     *
     * @param currentSubsetKey 当前的二级目录Key
     * @param newSubsetKey     新的二级目录Key
     * @throws InstaException 如果校验失败（为空或已存在）
     */
    private void validateCategorySubsetInsideKey(String currentSubsetKey, String newSubsetKey) {
        // 非空校验
        if (StringUtils.isBlank(newSubsetKey)) {
            throw new InstaException(FaqErrorCode.FaqCategorySubsetInsideKeyBlankException);
        }
        // 所有的二级目录key
        List<String> allCategorySubsetKey = subsetInsideService.listCategorySubsetKeyNotBlank()
                .stream()
                .map(FaqCategorySubsetInside::getCategorySubsetInsideKey)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allCategorySubsetKey)) {
            return;
        }
        // 当前二级目录key需移除
        if (StringUtils.isNotBlank(currentSubsetKey)) {
            allCategorySubsetKey.remove(currentSubsetKey);
        }
        // 唯一性校验
        if (allCategorySubsetKey.contains(newSubsetKey)) {
            throw new InstaException(FaqErrorCode.FaqCategorySubsetInsideKeyDuplicateException);
        }
    }
}
