package com.insta360.store.admin.controller.meta.vo.topbar;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.TopBarMain;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/08/16
 * @Description:
 */
public class AdTopBarMainVO {

    private Integer id;

    private String topBarName;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer orderIndex;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Boolean disabled;

    public AdTopBarMainVO() {
    }

    public AdTopBarMainVO(TopBarMain topBarMain) {
        if (topBarMain != null) {
            BeanUtil.copyProperties(topBarMain, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTopBarName() {
        return topBarName;
    }

    public void setTopBarName(String topBarName) {
        this.topBarName = topBarName;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "AdTopBarMainVO{" +
                "id=" + id +
                ", topBarName='" + topBarName + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", orderIndex=" + orderIndex +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", disabled=" + disabled +
                '}';
    }
}
