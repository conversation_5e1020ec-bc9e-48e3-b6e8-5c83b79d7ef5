package com.insta360.store.admin.controller.product.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.product.dto.ProductPrecautionsTextCreateDTO;
import com.insta360.store.business.product.service.impl.helper.ProductPrecautionsTextHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 产品注意事项缓存层
 * @Date 2022/9/27
 */
@Component
public class AdProductPrecautionsCachePack {

    @Autowired
    private ProductPrecautionsTextHelper productPrecautionsTextHelper;

    /**
     * 新增或更新产品注意事项文案
     *
     * @param cachePutKeyParameter
     * @param productPrecautionsTextCreateDto
     */
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_PACK_LIST)
    public void saveOrUpdateProductPrecautionsText(CachePutKeyParameterBO cachePutKeyParameter, ProductPrecautionsTextCreateDTO productPrecautionsTextCreateDto) {
        productPrecautionsTextHelper.savePrecautionsTextAndLanguageInfo(productPrecautionsTextCreateDto);
    }

}
