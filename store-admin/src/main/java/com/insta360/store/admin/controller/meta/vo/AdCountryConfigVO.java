package com.insta360.store.admin.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.CountryConfig;

/**
 * @Author: hyc
 * @Date: 2019-05-16
 * @Description:
 */
public class AdCountryConfigVO extends CountryConfig {

    private String name;

    public AdCountryConfigVO() {
    }

    public AdCountryConfigVO(CountryConfig countryConfig) {
        if (countryConfig != null) {
            BeanUtil.copyProperties(countryConfig, this);
        }
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
