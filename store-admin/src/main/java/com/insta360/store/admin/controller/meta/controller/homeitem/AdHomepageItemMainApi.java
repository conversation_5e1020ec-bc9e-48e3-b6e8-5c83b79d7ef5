package com.insta360.store.admin.controller.meta.controller.homeitem;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdMetaHomePageMainCachePack;
import com.insta360.store.admin.controller.meta.vo.homeitem.AdHomepageItemMainVO;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dto.HomepageItemMainDTO;
import com.insta360.store.business.meta.exception.MetaErrorCode;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/09/11
 * @Description:
 */
@RestController
@PermissionResource(code = "homepageItemMain", desc = "首页模块内部信息配置")
public class AdHomepageItemMainApi extends BaseAdminApi {

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Autowired
    AdMetaHomePageMainCachePack adMetaHomePageMainCachePack;

    /**
     * 获取所有的 home item 类型
     *
     * @return
     */
    @LogAttr(desc = "获取所有的 home item 类型",logType = LogType.query)
    @Permission(code = "store.meta.homepageItemMain.listHomepageItems", desc = "获取所有的主页商品类型")
    @GetMapping("/admin/meta/hp/listHomepageItems")
    public Response<? extends Map> listHomepageItems() {
        // 所有的home item列表
        List<HomepageItemMain> homepageItemMains = homepageItemMainService.listHomeItem();

        // 返回值转换
        List<AdHomepageItemMainVO> homepageItemMainVos = homepageItemMains.stream().map(AdHomepageItemMainVO::new).collect(Collectors.toList());
        return Response.ok("homeItems", homepageItemMainVos);
    }

    /**
     * 新增 homepage item
     *
     * @param homepageItemMainParam
     * @return
     */
    @LogAttr(desc = "新增 homepage item")
    @Permission(code = "store.meta.homepageItemMain.createHomepageItem", desc = "新增")
    @PostMapping("/admin/meta/hp/createHomepageItem")
    public Response<Object> createHomepageItem(@RequestBody HomepageItemMainDTO homepageItemMainParam) {
        HomepageItemMain homepageItemMain = homepageItemMainParam.getPojoObject();
        if (StringUtil.isBlank(homepageItemMain.getHomeItemName()) || StringUtil.isBlank(homepageItemMain.getNameKey())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // save
        adMetaHomePageMainCachePack.createHomepageItem(homepageItemMain);
        return Response.ok();
    }

    /**
     * 更新 homepage item
     *
     * @param homepageItemMainParam
     * @return
     */
    @LogAttr(desc = "更新 homepage item")
    @Permission(code = "store.meta.homepageItemMain.updateHomepageItem", desc = "更新")
    @PostMapping("/admin/meta/hp/updateHomepageItem")
    public Response<Object> updateHomepageItem(@RequestBody HomepageItemMainDTO homepageItemMainParam) {
        HomepageItemMain homepageItemMain = homepageItemMainService.getById(homepageItemMainParam.getId());
        if (homepageItemMain == null || homepageItemMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.HomeItemNotFoundException);
        }

        // update
        adMetaHomePageMainCachePack.updateHomepageItem(homepageItemMainParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 删除 homepage item
     *
     * @param homepageItemMainParam
     * @return
     */
    @LogAttr(desc = "删除 homepage item")
    @Permission(code = "store.meta.homepageItemMain.deleteHomepageItem", desc = "删除")
    @PostMapping("/admin/meta/hp/deleteHomepageItem")
    public Response<Object> deleteHomepageItem(@RequestBody HomepageItemMainDTO homepageItemMainParam) {
        HomepageItemMain homepageItemMain = homepageItemMainService.getById(homepageItemMainParam.getId());
        if (homepageItemMain == null || homepageItemMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.HomeItemNotFoundException);
        }

        // remove
        adMetaHomePageMainCachePack.removeHomepageItem(homepageItemMain);
        return Response.ok();
    }

    /**
     * 启用 homepage item
     *
     * @param homepageItemMainParam
     * @return
     */
    @LogAttr(desc = "启用 homepage item")
    @Permission(code = "store.meta.homepageItemMain.enableHomepageItem", desc = "启用")
    @PostMapping("/admin/meta/hp/enableHomepageItem")
    public Response<Object> enableHomepageItem(@RequestBody HomepageItemMainDTO homepageItemMainParam) {
        HomepageItemMain homepageItemMain = homepageItemMainService.getById(homepageItemMainParam.getId());
        if (homepageItemMain == null || homepageItemMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.HomeItemNotFoundException);
        }

        // enable
        adMetaHomePageMainCachePack.enableHomepageItem(homepageItemMain);
        return Response.ok();
    }

    /**
     * 禁用 homepage item
     *
     * @param homepageItemMainParam
     * @return
     */
    @LogAttr(desc = "禁用 homepage item")
    @Permission(code = "store.meta.homepageItemMain.disableHomepageItem", desc = "禁用")
    @PostMapping("/admin/meta/hp/disableHomepageItem")
    public Response<Object> disableHomepageItem(@RequestBody HomepageItemMainDTO homepageItemMainParam) {
        HomepageItemMain homepageItemMain = homepageItemMainService.getById(homepageItemMainParam.getId());
        if (homepageItemMain == null || homepageItemMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.HomeItemNotFoundException);
        }

        // disable
        adMetaHomePageMainCachePack.disableHomepageItem(homepageItemMain);
        return Response.ok();
    }
}
