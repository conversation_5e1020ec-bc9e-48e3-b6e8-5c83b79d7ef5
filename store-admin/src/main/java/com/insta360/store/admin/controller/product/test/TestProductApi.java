package com.insta360.store.admin.controller.product.test;

import com.alibaba.excel.EasyExcel;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: wkx
 * @Date: 2023/9/19
 * @Description:
 */
@RestController
public class TestProductApi {

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Autowired
    ProductService productService;

    /**
     * 更新产品类型
     *
     * @param file
     * @return
     * @throws IOException
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @PostMapping(value = "/admin/product/updateCategoryType", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response<Object> updateIntroduction(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        ExcelUploadListener listener = new ExcelUploadListener();
        AtomicInteger countUpdate = new AtomicInteger();

        EasyExcel.read(inputStream, ExcelUpload.class, listener).sheet().doRead();
        List<ExcelUpload> excelCacheDataList = listener.getExcelCacheDataList();

        excelCacheDataList.forEach(excelUpload -> {
            ProductCategoryMainType mainType = productCategoryHelper.getCategoryMainByKey(excelUpload.getCategoryKey());
            Product product = productService.getById(excelUpload.getProductId());
            if (Objects.isNull(mainType) || Objects.isNull(product)) {
                throw new InstaException(-1, String.format("参数错误%s", excelUpload.getCategoryKey()));
            }

            if (!productCategoryHelper.checkCategoryKeyBind(excelUpload.getCategoryKey())) {
                throw new InstaException(-1, String.format("非末级绑定%s", excelUpload.getCategoryKey()));
            }

            product.setCategoryKey(excelUpload.getCategoryKey());
            productService.updateById(product);
            countUpdate.getAndIncrement();
        });
        return Response.ok(countUpdate.get());
    }


}
