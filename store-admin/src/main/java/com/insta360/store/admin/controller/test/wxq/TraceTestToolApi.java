package com.insta360.store.admin.controller.test.wxq;

import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.configuration.trace.TraceLogContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * TraceLog注解测试工具API
 * 提供多个接口测试TraceLog注解的不同功能
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6
 */
@RestController
public class TraceTestToolApi extends BaseAdminApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(TraceTestToolApi.class);

    /**
     * 基础测试 - 使用默认参数
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLog1")
    @TraceLog()
    public void testTraceLog(@RequestBody String body) {
        // 测试基本功能，使用默认参数
        LOGGER.info("基础测试 - 默认参数，检查日志中是否有TTT");
    }

    /**
     * 测试自定义日志前缀
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogPrefix")
    @TraceLog(logPrefix = "CustomPrefix")
    public void testTraceLogPrefix(@RequestBody String body) {
        // 测试自定义日志前缀功能
        LOGGER.info("自定义前缀测试 - 当前TTT");
    }

    /**
     * 测试记录方法执行时间
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogExecutionTime")
    @TraceLog(logExecutionTime = true)
    public void testTraceLogExecutionTime(@RequestBody String body) {
        // 模拟耗时操作
        LOGGER.info("执行时间测试开始 - 当前TTT");
        try {
            Thread.sleep(1000);
            LOGGER.info("执行时间测试结束 - 当前TTT");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("执行时间测试异常 - 当前TTT", e);
        }
    }

    /**
     * 测试记录方法参数
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogParams")
    @TraceLog(logParams = true)
    public void testTraceLogParams(@RequestBody String body) {
        // 测试记录方法参数功能
        LOGGER.info("参数记录测试 - 输入参数: {}, TTT: {}", body, "-");
    }

    /**
     * 测试记录方法返回值
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogResult")
    @TraceLog(logResult = true)
    public String testTraceLogResult(@RequestBody String body) {
        // 返回一个结果以测试记录返回值功能
        String result = "测试返回结果: " + body;
        LOGGER.info("返回值记录测试 - 返回结果: {}, TTT: {}", result, "-");
        return result;
    }

    /**
     * 测试禁用自动清除TraceId
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogNoCleanup")
    @TraceLog(autoCleanup = false)
    public void testTraceLogNoCleanup(@RequestBody String body) {
        // 测试禁用自动清除TraceId功能
        LOGGER.info("禁用自动清除测试 - 方法执行中");
        // 在此方法完成后，TraceId不会被清除，可以通过连续调用此接口查看是否使用了相同的TraceId
    }

    /**
     * 测试异常记录功能
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogException")
    @TraceLog(logException = true)
    public void testTraceLogException(@RequestBody String body) {
        // 故意抛出异常以测试异常记录功能
        LOGGER.info("异常记录测试 - 抛出异常前");
        throw new RuntimeException("测试TraceLog异常记录功能");
    }

    /**
     * 测试不发送飞书通知
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogNoFeishuMessage")
    @TraceLog(feishuMessage = false)
    public void testTraceLogNoFeishuMessage(@RequestBody String body) {
        // 故意抛出异常但不发送飞书通知
        LOGGER.info("飞书通知测试 - 抛出异常前");
        throw new RuntimeException("测试TraceLog不发送飞书通知功能");
    }

    /**
     * 测试组合功能1 - 完整日志记录
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogFull")
    @TraceLog(
            logPrefix = "FullTest",
            logExecutionTime = true,
            logParams = true,
            logResult = true
    )
    public String testTraceLogFull(@RequestBody String body) {
        // 测试多个功能组合
        LOGGER.info("完整测试开始");
        try {
            Thread.sleep(500);
            LOGGER.info("完整测试中间过程");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("完整测试异常", e);
        }
        String result = "完整测试返回: " + body;
        LOGGER.info("完整测试结束");
        return result;
    }

    /**
     * 测试组合功能2 - 异常处理与计时
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogErrorWithTime")
    @TraceLog(
            logPrefix = "ErrorTest",
            logExecutionTime = true,
            logException = true,
            feishuMessage = true
    )
    public void testTraceLogErrorWithTime(@RequestBody String body) {
        // 模拟耗时操作后抛出异常
        LOGGER.info("异常与计时测试开始");
        try {
            Thread.sleep(800);
            LOGGER.info("异常与计时测试延时后");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LOGGER.error("异常与计时测试中断", e);
        }
        LOGGER.info("异常与计时测试抛出异常前");
        throw new IllegalArgumentException("测试TraceLog异常处理与计时组合功能");
    }

    /**
     * 测试手动获取和设置TraceId
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogManual")
    public void testTraceLogManual(@RequestBody String body) {
        // 获取当前上下文中的TraceId（如果不存在则会生成一个新的）
        String traceId = TraceLogContext.getTraceId();
        LOGGER.info("手动管理测试 - 获取到的traceId: {}", traceId);

        // 清除当前TraceId
        TraceLogContext.clear();
        LOGGER.info("手动管理测试 - 清除后");

        // 生成新的TraceId
        String newTraceId = TraceLogContext.generateTraceId();
        LOGGER.info("手动管理测试 - 生成新的traceId: {}", newTraceId);

        // 设置自定义TraceId
        String customTraceId = "CUSTOM-" + System.currentTimeMillis();
        TraceLogContext.setTraceId(customTraceId);
        LOGGER.info("手动管理测试 - 设置自定义traceId后");
    }

    /**
     * 测试异步任务中的TraceId传递
     */
    @GetMapping("/admin/tool/wxq/temp/testTraceLogAsync")
    @TraceLog(logPrefix = "Async")
    public void testTraceLogAsync(@RequestBody String body) {
        String mainThreadTraceId = "-";
        LOGGER.info("异步任务测试 - 主线程traceId: {}", mainThreadTraceId);

        // 使用runWithTraceId确保异步任务中的TraceId与当前线程一致
        TraceLogContext.runWithTraceId(() -> {
            LOGGER.info("异步任务测试 - 任务内部traceId");
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            LOGGER.info("异步任务测试 - 任务完成后traceId");
        });

        LOGGER.info("异步任务测试 - 主线程继续执行，traceId");
    }
}
