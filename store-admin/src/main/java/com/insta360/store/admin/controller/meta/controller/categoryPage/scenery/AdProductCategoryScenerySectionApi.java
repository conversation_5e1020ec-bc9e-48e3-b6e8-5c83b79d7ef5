package com.insta360.store.admin.controller.meta.controller.categoryPage.scenery;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdCategoryScenerySectionCachePack;
import com.insta360.store.business.meta.dto.scenerySection.ScenerySectionMainDTO;
import com.insta360.store.business.meta.model.ProductCategorySceneryCardMain;
import com.insta360.store.business.meta.model.ProductCategoryScenerySection;
import com.insta360.store.business.meta.service.ProductCategorySceneryCardMainService;
import com.insta360.store.business.meta.service.ProductCategoryScenerySectionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * @description: 场景专区
 * @author: py
 * @create: 2025-03-11 18:00
 */
@PermissionResource(code = "productCategoryScenerySection", desc = "场景专区卡片配置")
@RestController
public class AdProductCategoryScenerySectionApi extends BaseAdminApi {

    @Autowired
    AdCategoryScenerySectionCachePack categoryScenerySectionCachePack;

    @Autowired
    ProductCategoryScenerySectionService productCategoryScenerySectionService;

    @Autowired
    ProductCategorySceneryCardMainService productCategorySceneryCardMainService;

    /**
     * 排序场景专区
     *
     * @param scenerySectionMainList
     * @return
     */
    @LogAttr(desc = "排序场景专区")
    @Permission(code = "store.meta.categoryPage.orderScenerySection", desc = "排序场景专区")
    @PostMapping(path = "/admin/meta/ss/orderScenerySection", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> orderScenerySection(@RequestBody List<ScenerySectionMainDTO> scenerySectionMainList) {
        if (CollectionUtils.isEmpty(scenerySectionMainList)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "场景专区排序数据缺失");
        }

        categoryScenerySectionCachePack.orderScenerySection(scenerySectionMainList);
        return Response.ok();
    }

    /**
     * 更新场景专区
     *
     * @param scenerySectionMainParam
     * @return
     */
    @LogAttr(desc = "更新场景专区")
    @Permission(code = "store.meta.categoryPage.updateScenerySection", desc = "更新场景专区")
    @PostMapping(path = "/admin/meta/ss/updateScenerySection", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateScenerySection(@RequestBody @Validated ScenerySectionMainDTO scenerySectionMainParam) {
        if (Objects.isNull(scenerySectionMainParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "请求数据缺失");
        }

        categoryScenerySectionCachePack.updateScenerySection(scenerySectionMainParam);
        return Response.ok();
    }

    /**
     * 启用场景专区
     *
     * @param scenerySectionMainParam
     * @return
     */
    @LogAttr(desc = "启用场景专区")
    @Permission(code = "store.meta.categoryPage.enableScenerySection", desc = "启用场景专区")
    @PostMapping(path = "/admin/meta/ss/enableScenerySection", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> enableScenerySection(@RequestBody ScenerySectionMainDTO scenerySectionMainParam) {
        Integer scenerySectionId = scenerySectionMainParam.getId();
        if (scenerySectionId == null) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        ProductCategoryScenerySection categoryScenerySection = productCategoryScenerySectionService.getById(scenerySectionId);
        if (Objects.isNull(categoryScenerySection)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "场景专区不存在");
        }

        // 必填字段是否存在
        if (StringUtil.isBlank(categoryScenerySection.getScenerySuffix())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "后缀字段未填写");
        }

        if (StringUtil.isBlank(categoryScenerySection.getMoBanner())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "移动端banner字段未填写");
        }

        if (StringUtil.isBlank(categoryScenerySection.getPcBanner())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "pc端banner字段未填写");
        }

        // 是否配置卡片
        List<ProductCategorySceneryCardMain> productCategorySceneryCardMains = productCategorySceneryCardMainService.listBySectionId(scenerySectionId);
        if (CollectionUtils.isEmpty(productCategorySceneryCardMains)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "需配置>=1个套餐");
        }

        categoryScenerySectionCachePack.enableScenerySection(scenerySectionMainParam.getId());
        return Response.ok();
    }

    /**
     * 禁用场景专区
     *
     * @param scenerySectionMainParam
     * @return
     */
    @LogAttr(desc = "禁用场景专区")
    @Permission(code = "store.meta.categoryPage.disableScenerySection", desc = "禁用场景专区")
    @PostMapping(path = "/admin/meta/ss/disableScenerySection", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> disableScenerySection(@RequestBody ScenerySectionMainDTO scenerySectionMainParam) {
        if (Objects.isNull(scenerySectionMainParam) || scenerySectionMainParam.getId() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "请求数据缺失");
        }

        categoryScenerySectionCachePack.disableScenerySection(scenerySectionMainParam.getId());
        return Response.ok();
    }
}
