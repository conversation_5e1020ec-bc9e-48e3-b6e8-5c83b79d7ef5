package com.insta360.store.admin.controller.rma.vo;

import com.insta360.store.business.rma.model.RmaReasonOption;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 售后原因vo
 * @Date 2023/6/13
 */
public class AdRefundReasonVO implements Serializable {

    /**
     * 原因ID
     */
    private Integer id;

    /**
     * 售后类型
     *
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    private String rmaType;

    /**
     * 售后原因key
     */
    private String rmaReasonKey;

    /**
     * 排序号
     */
    private Integer orderIndex;

    public AdRefundReasonVO() {
    }

    public AdRefundReasonVO(RmaReasonOption rmaReasonOption) {
        if(Objects.nonNull(rmaReasonOption)) {
            BeanUtils.copyProperties(rmaReasonOption,this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public String getRmaReasonKey() {
        return rmaReasonKey;
    }

    public void setRmaReasonKey(String rmaReasonKey) {
        this.rmaReasonKey = rmaReasonKey;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }
}
