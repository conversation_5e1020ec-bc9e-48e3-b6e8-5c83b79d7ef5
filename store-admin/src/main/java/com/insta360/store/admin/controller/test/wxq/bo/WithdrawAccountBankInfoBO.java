package com.insta360.store.admin.controller.test.wxq.bo;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/31
 */
public class WithdrawAccountBankInfoBO implements Serializable {

    /**
     * 银行账号
     */
    @ExcelProperty(value = "银行账号")
    private String account;

    /**
     * email
     */
    @ExcelProperty(value = "email")
    private String email;

    /**
     * 账户类型
     */
    @ExcelProperty(value = "账户类型")
    private String type;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private String id;

    /**
     * enable
     */
    @ExcelProperty(value = "启用禁用")
    private String enable;

    /**
     * 银行名称详细信息
     */
    @ExcelProperty(value = "银行名称详细信息")
    private String bankNameDetail;

    /**
     * 用户的联系电话
     */
    @ExcelProperty(value = "用户的联系电话")
    private String phone;

    /**
     * 银行名称
     */
    @ExcelProperty(value = "银行名称")
    private String bankName;

    /**
     * 银行名称全称
     */
    @ExcelProperty(value = "银行名称全称")
    private String bankNameFill;

    /**
     * 用户姓名
     */
    @ExcelProperty(value = "姓名")
    private String username;

    /**
     * swiftCode
     */
    @ExcelProperty(value = "Swift Code")
    private String swiftCode;

    @ExcelProperty(value = "ibanCode")
    private String ibanCode;

    @ExcelProperty(value = "address")
    private String address;

    @ExcelProperty(value = "提现金额")
    private BigDecimal totalPrice;

    @Override
    public String toString() {
        return "WithdrawAccountBankInfoBO{" +
                "account='" + account + '\'' +
                ", email='" + email + '\'' +
                ", type='" + type + '\'' +
                ", id='" + id + '\'' +
                ", enable='" + enable + '\'' +
                ", bankNameDetail='" + bankNameDetail + '\'' +
                ", phone='" + phone + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankNameFill='" + bankNameFill + '\'' +
                ", username='" + username + '\'' +
                ", swiftCode='" + swiftCode + '\'' +
                ", ibanCode='" + ibanCode + '\'' +
                ", address='" + address + '\'' +
                ", totalPrice=" + totalPrice +
                '}';
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEnable() {
        return enable;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public String getBankNameDetail() {
        return bankNameDetail;
    }

    public void setBankNameDetail(String bankNameDetail) {
        this.bankNameDetail = bankNameDetail;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNameFill() {
        return bankNameFill;
    }

    public void setBankNameFill(String bankNameFill) {
        this.bankNameFill = bankNameFill;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getIbanCode() {
        return ibanCode;
    }

    public void setIbanCode(String ibanCode) {
        this.ibanCode = ibanCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }
}
