package com.insta360.store.admin.controller.meta.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.meta.model.HomepageItem;
import com.insta360.store.business.product.model.ProductInfo;

/**
 * @Author: hyc
 * @Date: 2019-05-16
 * @Description:
 */
public class AdHomeItemVO extends HomepageItem {

    @JSONField(name = "product_info")
    private ProductInfo productInfo;

    @JSONField(name = "commodity_info")
    private CommodityInfo commodityInfo;

    public AdHomeItemVO() {
    }

    public AdHomeItemVO(HomepageItem homeItem) {
        if (homeItem != null) {
            BeanUtil.copyProperties(homeItem, this);
        }
    }

    public ProductInfo getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(ProductInfo productInfo) {
        this.productInfo = productInfo;
    }

    public CommodityInfo getCommodityInfo() {
        return commodityInfo;
    }

    public void setCommodityInfo(CommodityInfo commodityInfo) {
        this.commodityInfo = commodityInfo;
    }
}
