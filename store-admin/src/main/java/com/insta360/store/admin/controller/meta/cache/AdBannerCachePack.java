package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.model.BannerInfo;
import com.insta360.store.business.meta.model.BannerMain;
import com.insta360.store.business.meta.service.BannerInfoService;
import com.insta360.store.business.meta.service.impl.helper.BannerHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: py
 * @create: 2023-12-08 15:17
 */
@Component
public class AdBannerCachePack {

    @Autowired
    BannerInfoService bannerInfoService;

    @Autowired
    BannerHelper bannerHelper;

    /**
     * 更新banner
     *
     * @param cachePutKeyParameter
     * @param bannerMain
     */
    @CachePutMonitor(cacheableType = CachePutType.BANNER_INFO)
    public void updateBanner(CachePutKeyParameterBO cachePutKeyParameter, BannerMain bannerMain) {
        bannerHelper.updateBanner(bannerMain);
    }

    /**
     * 删除banner
     *
     * @param bannerMain
     * @param cachePutKeyParameter
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.BANNER_INFO)
    public void deleteBanner(CachePutKeyParameterBO cachePutKeyParameter, BannerMain bannerMain) {
        bannerHelper.deleteBanner(bannerMain);
    }

    /**
     * 创建banner info
     *
     * @param cachePutKeyParameter
     * @param bannerInfo
     */
    @CachePutMonitor(cacheableType = CachePutType.BANNER_INFO)
    public void createBannerInfo(CachePutKeyParameterBO cachePutKeyParameter, BannerInfo bannerInfo) {
        bannerInfoService.createBannerInfo(bannerInfo);
    }

    /**
     * 更新banner info
     *
     * @param cachePutKeyParameter
     * @param bannerInfo
     */
    @CachePutMonitor(cacheableType = CachePutType.BANNER_INFO)
    public void updateBannerInfo(CachePutKeyParameterBO cachePutKeyParameter, BannerInfo bannerInfo) {
        bannerInfoService.updateBannerInfo(bannerInfo);
    }

    /**
     * 删除banner info
     *
     * @param cachePutKeyParameter
     * @param bannerInfoId
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.BANNER_INFO)
    public boolean deleteBannerInfo(CachePutKeyParameterBO cachePutKeyParameter, Integer bannerInfoId) {
        return bannerInfoService.deleteBannerInfo(bannerInfoId);
    }
}
