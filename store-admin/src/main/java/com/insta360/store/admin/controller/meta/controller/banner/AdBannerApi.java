package com.insta360.store.admin.controller.meta.controller.banner;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdBannerCachePack;
import com.insta360.store.admin.controller.meta.format.AdBannerPack;
import com.insta360.store.admin.controller.meta.vo.AdBannerMainVO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.meta.dto.BannerInfoDTO;
import com.insta360.store.business.meta.dto.BannerMainDTO;
import com.insta360.store.business.meta.dto.BannerQueryDTO;
import com.insta360.store.business.meta.model.BannerInfo;
import com.insta360.store.business.meta.model.BannerMain;
import com.insta360.store.business.meta.service.BannerInfoService;
import com.insta360.store.business.meta.service.BannerMainService;
import com.insta360.store.business.meta.service.impl.helper.BannerHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2021/01/20
 * @Description:
 */
@RestController
@PermissionResource(code = "banner", desc = "banner配置")
public class AdBannerApi extends BaseAdminApi {

    @Autowired
    AdBannerCachePack adBannerCachePack;

    @Autowired
    AdBannerPack bannerPack;

    @Autowired
    BannerHelper bannerHelper;

    @Autowired
    BannerMainService bannerMainService;

    @Autowired
    BannerInfoService bannerInfoService;

    /**
     * 创建 banner
     *
     * @param bannerMainParam
     */
    @LogAttr(desc = "创建banner")
    @Permission(code = "store.meta.banner.createBanner", desc = "创建banner")
    @PostMapping(path = "/admin/meta/banner/createBanner", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> createBanner(@RequestBody BannerMainDTO bannerMainParam) {
        BannerMain bannerMainData = bannerMainParam.getPojoObject();

        // create
        Integer bannerId = bannerHelper.createBanner(bannerMainData);
        return Response.ok("id", bannerId);
    }

    /**
     * 更新 banner
     *
     * @param bannerMainParam
     */
    @LogAttr(desc = "更新banner")
    @Permission(code = "store.meta.banner.updateBanner", desc = "更新banner")
    @PostMapping(path = "/admin/meta/banner/updateBanner", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateBanner(@RequestBody BannerMainDTO bannerMainParam) {
        BannerMain bannerMainData = bannerMainParam.getPojoObject();
        if (Objects.isNull(bannerMainData.getId())) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setApp(bannerMainData.getApp());

        // update
        adBannerCachePack.updateBanner(cachePutKeyParameter, bannerMainData);
        return Response.ok();
    }

    /**
     * 删除 banner
     *
     * @param bannerInfoParam
     */
    @LogAttr(desc = "删除banner")
    @Permission(code = "store.meta.banner.deleteBanner", desc = "删除banner")
    @PostMapping("/admin/meta/banner/deleteBanner")
    public Response<? extends Map> deleteBanner(@RequestBody BannerInfoDTO bannerInfoParam) {
        if (bannerInfoParam.getId() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        BannerMain bannerMain = bannerMainService.getById(bannerInfoParam.getId());
        if (Objects.isNull(bannerMain)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setApp(bannerMain.getApp());

        // delete
        adBannerCachePack.deleteBanner(cachePutKeyParameter, bannerMain);
        return Response.ok();
    }

    /**
     * 多条件获取banner信息
     *
     * @param bannerQueryParam
     */
    @LogAttr(desc = "多条件获取banner信息")
    @Permission(code = "store.meta.banner.listBanners", desc = "多条件获取banner信息")
    @PostMapping(path = "/admin/meta/banner/listBanners", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> listBanners(@RequestBody BannerQueryDTO bannerQueryParam) {
        PageResult<AdBannerMainVO> pageResult = bannerPack.listBanners(bannerQueryParam);
        return Response.ok(pageResult);
    }

    /**
     * 创建 banner 详细信息
     *
     * @param bannerInfoParam
     */
    @LogAttr(desc = "创建banner详细信息")
    @Permission(code = "store.meta.banner.createBannerInfo", desc = "创建banner详细信息")
    @PostMapping(path = "/admin/meta/banner/createBannerInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> createBannerInfo(@RequestBody BannerInfoDTO bannerInfoParam) {
        BannerInfo bannerInfo = bannerInfoParam.getPojoObject();
        if (Objects.isNull(bannerInfo.getBannerId())) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        BannerMain bannerMain = bannerMainService.getById(bannerInfo.getBannerId());
        if (Objects.isNull(bannerMain)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setApp(bannerMain.getApp());

        // create
        adBannerCachePack.createBannerInfo(cachePutKeyParameter, bannerInfo);
        return Response.ok("bannerInfo", bannerInfo);
    }

    /**
     * 更新 banner 详细信息
     *
     * @param bannerInfoParam
     */
    @LogAttr(desc = "更新banner详细信息")
    @Permission(code = "store.meta.banner.updateBannerInfo", desc = "更新banner详细信息")
    @PostMapping(path = "/admin/meta/banner/updateBannerInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateBannerInfo(@RequestBody BannerInfoDTO bannerInfoParam) {
        BannerInfo bannerInfo = bannerInfoParam.getPojoObject();
        if (Objects.isNull(bannerInfo.getBannerId())) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        BannerMain bannerMain = bannerMainService.getById(bannerInfo.getBannerId());
        if (Objects.isNull(bannerMain)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setApp(bannerMain.getApp());

        // update
        adBannerCachePack.updateBannerInfo(cachePutKeyParameter, bannerInfo);
        return Response.ok("bannerInfo", bannerInfo);
    }

    /**
     * 删除 banner 详细信息
     *
     * @param bannerInfoParam
     */
    @LogAttr(desc = "删除banner详细信息")
    @Permission(code = "store.meta.banner.deleteBannerInfo", desc = "删除banner详细信息")
    @PostMapping("/admin/meta/banner/deleteBannerInfo")
    public Response<Boolean> deleteBannerInfo(@RequestBody BannerInfoDTO bannerInfoParam) {
        BannerInfo bannerInfo = bannerInfoService.getById(bannerInfoParam.getBannerInfoId());
        if (Objects.isNull(bannerInfo)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        BannerMain bannerMain = bannerMainService.getById(bannerInfo.getBannerId());
        if (Objects.isNull(bannerMain)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setApp(bannerMain.getApp());

        // delete
        boolean remove = adBannerCachePack.deleteBannerInfo(cachePutKeyParameter, bannerInfoParam.getBannerInfoId());
        return Response.ok(remove);
    }
}
