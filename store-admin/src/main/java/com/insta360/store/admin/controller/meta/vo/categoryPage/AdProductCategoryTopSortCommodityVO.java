package com.insta360.store.admin.controller.meta.vo.categoryPage;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategoryTopSortCommodity;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/10/20
 * @Description:
 */
public class AdProductCategoryTopSortCommodityVO implements Serializable {

    private Integer id;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 产品套餐名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public AdProductCategoryTopSortCommodityVO() {
    }

    public AdProductCategoryTopSortCommodityVO(ProductCategoryTopSortCommodity topSortCommodity) {
        if (Objects.nonNull(topSortCommodity)) {
            BeanUtil.copyProperties(topSortCommodity, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AdProductCategoryTopSortCommodityVO{" +
                "id=" + id +
                ", productId=" + productId +
                ", commodityId=" + commodityId +
                ", name='" + name + '\'' +
                ", orderIndex=" + orderIndex +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
