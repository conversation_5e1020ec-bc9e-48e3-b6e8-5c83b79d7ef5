package com.insta360.store.admin.controller.commodity.controller;

import com.google.common.collect.Lists;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.commodity.controller.cache.AdCommodityInfoCachePack;
import com.insta360.store.admin.controller.commodity.format.AdCommodityPack;
import com.insta360.store.admin.controller.commodity.vo.AdCommodityVO;
import com.insta360.store.business.commodity.dto.CommodityInfoDTO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.search.annotation.TrackSearchDataChange;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.context.SearchDataChangeContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

/**
 * @Author: hyc
 * @Date: 2019-08-05
 * @Description: 套餐多语言信息
 */
@RestController
@PermissionResource(code = "commodityInfo", desc = "套餐多语言信息")
public class AdCommodityInfoApi extends BaseAdminApi {

    @Autowired
    AdCommodityPack adCommodityPack;

    @Autowired
    CommodityService commodityService;

    @Autowired
    AdCommodityInfoCachePack adCommodityCachePack;

    /**
     * 获取套餐语言信息
     *
     * @param commodityId
     * @return
     */
    @LogAttr(desc = "获取套餐语言信息", logType = LogType.query)
    @Permission(code = "store.commodity.commodityInfo.getInfos", desc = "获取套餐语言信息")
    @GetMapping("/admin/commodity/getInfos")
    public Response<? extends Map> getInfos(@RequestParam(required = false, value = "commodity_id") Integer commodityId) {
        Commodity commodity = commodityService.getById(commodityId);

        AdCommodityPack.PackSetting packSetting = new AdCommodityPack.PackSetting(this);
        packSetting.setWithInfo(true);
        AdCommodityVO commodityVO = adCommodityPack.doPack(commodity, packSetting);

        return Response.ok("commodity", commodityVO.getInfos());
    }

    /**
     * 新增或更新套餐多语言信息
     *
     * @param commodityInfoParam
     * @return
     */
    @LogAttr(desc = "新增或更新套餐多语言信息")
    @Permission(code = "store.commodity.commodityInfo.upsertInfo", desc = "新增或更新套餐多语言信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/upsertInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> upsertInfo(@RequestBody CommodityInfoDTO commodityInfoParam) {
        CommodityInfo commodityInfoData = commodityInfoParam.getPojoObject();

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityInfoData.getCommodity()));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityInfoData.getCommodity()));
        SearchDataChangeContext.set(searchDataChangeParams);

        adCommodityCachePack.upsertCommodityInfo(cachePutKeyParameter, commodityInfoData);
        return Response.ok("info", commodityInfoData);
    }

    /**
     * 批量新增或更新套餐多语言信息
     *
     * @param commodityInfoParam
     * @return
     */
    @LogAttr(desc = "批量新增或更新套餐多语言信息")
    @Permission(code = "store.commodity.commodityInfo.updateInfo", desc = "批量新增或更新套餐多语言信息")
    @TrackSearchDataChange(changeType = SearchDataChangeType.COMMODITY)
    @PostMapping(path = "/admin/commodity/updateInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateInfo(@RequestBody CommodityInfoDTO commodityInfoParam) {
        if (commodityInfoParam.getCommodity() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = this.getCachePutKeyParameter();
        cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityInfoParam.getCommodity()));

        // 搜索数据同步
        SearchDataChangeContext searchDataChangeParams = new SearchDataChangeContext();
        searchDataChangeParams.setCommodityIds(Lists.newArrayList(commodityInfoParam.getCommodity()));
        SearchDataChangeContext.set(searchDataChangeParams);

        adCommodityCachePack.updateCommodityInfo(cachePutKeyParameter, commodityInfoParam);
        return Response.ok();
    }
}
