package com.insta360.store.admin.rpc.order.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.order.model.OrderInvoice;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2022/01/10
 * @Description:
 */
public class AdOrderInvoiceVO {

    /**
     * 订单id
     */
    private Integer order;

    /**
     * 发票类型
     */
    private String type;

    /**
     * 发票抬头
     */
    private String title;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司手机号
     */
    private String companyPhone;

    /**
     * 公司银行
     */
    private String companyBank;

    /**
     * 公司银行账户
     */
    private String companyBankAccount;

    /**
     * 公司文件
     */
    private String companyFile;

    /**
     * 发送邮箱
     */
    private String sendEmail;

    /**
     * 发送地区
     */
    private String sendAddress;

    /**
     * 快递编号
     */
    private String sendExpressNumber;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 发货时间
     */
    private LocalDateTime expressTime;

    /**
     * 开票状态
     * 0:未完成
     * 1：已完成
     */
    private Integer state;

    public AdOrderInvoiceVO() {
    }

    public AdOrderInvoiceVO(OrderInvoice orderInvoice) {
        if (orderInvoice != null) {
            BeanUtil.copyProperties(orderInvoice, this);
        }
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public String getCompanyBank() {
        return companyBank;
    }

    public void setCompanyBank(String companyBank) {
        this.companyBank = companyBank;
    }

    public String getCompanyBankAccount() {
        return companyBankAccount;
    }

    public void setCompanyBankAccount(String companyBankAccount) {
        this.companyBankAccount = companyBankAccount;
    }

    public String getCompanyFile() {
        return companyFile;
    }

    public void setCompanyFile(String companyFile) {
        this.companyFile = companyFile;
    }

    public String getSendEmail() {
        return sendEmail;
    }

    public void setSendEmail(String sendEmail) {
        this.sendEmail = sendEmail;
    }

    public String getSendAddress() {
        return sendAddress;
    }

    public void setSendAddress(String sendAddress) {
        this.sendAddress = sendAddress;
    }

    public String getSendExpressNumber() {
        return sendExpressNumber;
    }

    public void setSendExpressNumber(String sendExpressNumber) {
        this.sendExpressNumber = sendExpressNumber;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public LocalDateTime getExpressTime() {
        return expressTime;
    }

    public void setExpressTime(LocalDateTime expressTime) {
        this.expressTime = expressTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "AdOrderInvoiceVO{" +
                "order=" + order +
                ", type='" + type + '\'' +
                ", title='" + title + '\'' +
                ", taxNumber='" + taxNumber + '\'' +
                ", companyAddress='" + companyAddress + '\'' +
                ", companyPhone='" + companyPhone + '\'' +
                ", companyBank='" + companyBank + '\'' +
                ", companyBankAccount='" + companyBankAccount + '\'' +
                ", companyFile='" + companyFile + '\'' +
                ", sendEmail='" + sendEmail + '\'' +
                ", sendAddress='" + sendAddress + '\'' +
                ", sendExpressNumber='" + sendExpressNumber + '\'' +
                ", finishTime=" + finishTime +
                ", orderNumber='" + orderNumber + '\'' +
                ", orderState=" + orderState +
                ", expressTime=" + expressTime +
                ", state=" + state +
                '}';
    }
}
