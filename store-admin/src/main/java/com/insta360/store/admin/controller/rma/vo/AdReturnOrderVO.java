package com.insta360.store.admin.controller.rma.vo;

import com.insta360.store.business.rma.model.RmaOrder;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 售后订单VO
 * @Date 2023/6/27
 */
public class AdReturnOrderVO implements Serializable {

    /**
     * 售后ID
     */
    private Integer id;

    /**
     * 售后单号
     */
    private String rmaNumber;

    /**
     * 客服确认原因
     */
    private String adminReason;

    /**
     * 主责归因
     */
    private Integer rmaMainDuty;

    /**
     * 客服备注
     */
    private String adminRemark;

    /**
     * 用户售后原因
     */
    private String reason;

    /**
     * 用户售后额外说明
     */
    private String extraReason;

    /**
     * 退款货币
     */
    private String refundCurrency;

    /**
     * 退款金额
     */
    private Float refundAmount;

    /**
     * 是否需要退回
     */
    private Boolean needReturn;

    /**
     * 售后数量
     */
    private Integer quantity;

    /**
     * 售后类型
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    private String rmaType;

    /**
     * 售后状态
     * @see com.insta360.store.business.rma.enums.RmaState
     */
    private Integer state;

    /**
     * 售后商品类型
     * @see com.insta360.store.business.order.enums.OrderItemType
     */
    private Integer itemType;

    /**
     * 是否退回额度
     */
    private Boolean returnQuota;

    /**
     * 是否关闭自动订阅
     */
    private Boolean closeAutoSubscribe;

    /**
     * 售后单明细
     */
    private AdReturnOrderDetailVO returnOrderDetail;

    /**
     * 下单地区
     */
    private String area;

    /**
     * 是否终止权益（用于psp）
     */
    private Boolean terminationEquity;

    public AdReturnOrderVO() {
    }

    public AdReturnOrderVO(RmaOrder rmaOrder) {
        if(Objects.nonNull(rmaOrder)) {
            BeanUtils.copyProperties(rmaOrder,this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public Integer getRmaMainDuty() {
        return rmaMainDuty;
    }

    public void setRmaMainDuty(Integer rmaMainDuty) {
        this.rmaMainDuty = rmaMainDuty;
    }

    public String getAdminReason() {
        return adminReason;
    }

    public void setAdminReason(String adminReason) {
        this.adminReason = adminReason;
    }

    public String getAdminRemark() {
        return adminRemark;
    }

    public void setAdminRemark(String adminRemark) {
        this.adminRemark = adminRemark;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getExtraReason() {
        return extraReason;
    }

    public void setExtraReason(String extraReason) {
        this.extraReason = extraReason;
    }

    public String getRefundCurrency() {
        return refundCurrency;
    }

    public void setRefundCurrency(String refundCurrency) {
        this.refundCurrency = refundCurrency;
    }

    public Float getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Float refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Boolean getNeedReturn() {
        return needReturn;
    }

    public void setNeedReturn(Boolean needReturn) {
        this.needReturn = needReturn;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public AdReturnOrderDetailVO getReturnOrderDetail() {
        return returnOrderDetail;
    }

    public void setReturnOrderDetail(AdReturnOrderDetailVO returnOrderDetail) {
        this.returnOrderDetail = returnOrderDetail;
    }

    public Boolean getReturnQuota() {
        return returnQuota;
    }

    public void setReturnQuota(Boolean returnQuota) {
        this.returnQuota = returnQuota;
    }

    public Boolean getCloseAutoSubscribe() {
        return closeAutoSubscribe;
    }

    public void setCloseAutoSubscribe(Boolean closeAutoSubscribe) {
        this.closeAutoSubscribe = closeAutoSubscribe;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Boolean getTerminationEquity() {
        return terminationEquity;
    }

    public void setTerminationEquity(Boolean terminationEquity) {
        this.terminationEquity = terminationEquity;
    }

    @Override
    public String toString() {
        return "AdReturnOrderVO{" +
                "id=" + id +
                ", rmaNumber='" + rmaNumber + '\'' +
                ", adminReason='" + adminReason + '\'' +
                ", rmaMainDuty=" + rmaMainDuty +
                ", adminRemark='" + adminRemark + '\'' +
                ", reason='" + reason + '\'' +
                ", extraReason='" + extraReason + '\'' +
                ", refundCurrency='" + refundCurrency + '\'' +
                ", refundAmount=" + refundAmount +
                ", needReturn=" + needReturn +
                ", quantity=" + quantity +
                ", rmaType='" + rmaType + '\'' +
                ", state=" + state +
                ", itemType=" + itemType +
                ", returnQuota=" + returnQuota +
                ", closeAutoSubscribe=" + closeAutoSubscribe +
                ", returnOrderDetail=" + returnOrderDetail +
                ", area='" + area + '\'' +
                ", terminationEquity=" + terminationEquity +
                '}';
    }
}
