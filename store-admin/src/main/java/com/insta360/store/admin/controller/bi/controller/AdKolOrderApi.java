package com.insta360.store.admin.controller.bi.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseApi;
import com.insta360.store.admin.controller.bi.vo.AdPayOrderVO;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.service.PriceService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Ciel
 * @Date: 2019/9/25
 * @Description:
 */
@RestController
@PermissionResource(code = "kolOrder", desc = "kol订单")
public class AdKolOrderApi extends BaseApi {

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductService productService;

    @Autowired
    PriceService priceService;

    @Autowired
    CommodityPriceService commodityPriceService;

    /**
     * KOL订单统计
     *
     * @param orderNumber
     * @return
     */
    @LogAttr(desc = "KOL订单统计",logType = LogType.query)
    @Permission(code = "store.bi.kolOrder.getPayedOrders", desc = "KOL订单统计")
    @GetMapping("/admin/kol/orderInfo")
    public Response<AdPayOrderVO> getPayedOrders(@RequestParam(required = false, value = "order_number") String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            return Response.failed("未查询到对应订单！");
        }
        Integer orderId = order.getId();
        List<OrderItem> orderItems = orderItemService.getByOrder(orderId);
        List<OrderItem> cameraItems = orderItems.stream().filter(o -> isCamera(o.getProduct())).collect(Collectors.toList());
        List<OrderItem> accessoryItems = orderItems.stream().filter(o -> !isCamera(o.getProduct())).collect(Collectors.toList());

        List<String> cameraList = cameraItems.stream().map(o -> productService.getById(o.getProduct()).getName()).distinct().collect(Collectors.toList());
        List<String> accessoryList = accessoryItems.stream().map(o -> commodityService.getById(o.getCommodity()).getName()).distinct().collect(Collectors.toList());

        Integer cameraCount = cameraItems.stream().mapToInt(OrderItem::getNumber).sum();
        Integer accessoryCount = accessoryItems.stream().mapToInt(OrderItem::getNumber).sum();

        double sum = orderItems.stream().mapToDouble(o -> {
            CommodityPrice commodityPrice = commodityPriceService.getPrice(o.getCommodity(), InstaCountry.CN);
            return commodityPrice.getAmount() * o.getNumber();
        }).sum();
        double productCost = Math.round(sum * 100) / 100.0;

        AdPayOrderVO adPayOrderVO = new AdPayOrderVO();
        adPayOrderVO.setProduct(cameraList);
        adPayOrderVO.setCameraCount(cameraCount);
        adPayOrderVO.setAccessoryProducts(accessoryList);
        adPayOrderVO.setAccessoryCount(accessoryCount);
        adPayOrderVO.setProductCost(productCost);

        return Response.ok(adPayOrderVO);
    }

    private boolean isCamera(Integer productId) {
        Product product = productService.getById(productId);
        if (product != null) {
            return product.whetherCamera();
        }
        return false;
    }
}
