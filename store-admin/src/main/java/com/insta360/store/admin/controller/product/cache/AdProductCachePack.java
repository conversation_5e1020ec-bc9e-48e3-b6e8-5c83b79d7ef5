package com.insta360.store.admin.controller.product.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.product.dto.ProductInfoDTO;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductInfoHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2022/8/30
 * @Description:
 */
@Component
public class AdProductCachePack {

    @Autowired
    ProductService productService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    ProductInfoHelper productInfoHelper;

    /**
     * 更新或新增产品基本信息
     *
     * @param cachePutKeyParameter
     * @param product
     * @param adminUserEmail
     */
    @CacheEvict(value = {CacheableType.COMMODITY_LIST_INFO, CacheableType.COMMODITY_RECOMMENDATION_CART}, allEntries = true)
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_INFO)
    public void upsertProduct(CachePutKeyParameterBO cachePutKeyParameter, Product product, String adminUserEmail) {
        productService.upsertProduct(product, adminUserEmail);
    }

    /**
     * upsert 产品多语言信息文案
     *
     * @param cachePutKeyParameter
     * @param productInfoData
     */
    @CacheEvict(value = {CacheableType.COMMODITY_LIST_INFO, CacheableType.COMMODITY_RECOMMENDATION_CART}, allEntries = true)
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_INFO)
    public void upsertProductInfo(CachePutKeyParameterBO cachePutKeyParameter, ProductInfo productInfoData) {
        productInfoService.upsertProductInfo(productInfoData);
    }

    /**
     * 批量新增或更新产品多语言信息
     *
     * @param cachePutKeyParameter
     * @param productInfoParam
     * @return
     */
    @CacheEvict(value = {CacheableType.COMMODITY_LIST_INFO, CacheableType.COMMODITY_RECOMMENDATION_CART}, allEntries = true)
    @CachePutMonitor(cacheableType = CachePutType.PRODUCT_INFO)
    public void updateBasicInfos(CachePutKeyParameterBO cachePutKeyParameter, ProductInfoDTO productInfoParam) {
        productInfoHelper.updateBasicInfos(productInfoParam);
    }
}
