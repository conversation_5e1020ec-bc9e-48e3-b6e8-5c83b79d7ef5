package com.insta360.store.admin.controller.faq.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.faq.cache.AdFaqOtherQuestionCachePack;
import com.insta360.store.admin.controller.faq.vo.AdFaqOtherQuestionBindVO;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.faq.dto.OtherQuestionBindDTO;
import com.insta360.store.business.faq.exception.FaqErrorCode;
import com.insta360.store.business.faq.model.FaqOtherQuestionBind;
import com.insta360.store.business.faq.service.FaqOtherQuestionBindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/4/29
 * @Description:
 */
@PermissionResource(code = "faqOtherQuestionBind", desc = "Faq其他页面绑定")
@RestController
public class AdFaqOtherQuestionBindApi extends BaseAdminApi {

    @Autowired
    FaqOtherQuestionBindService questionBindService;

    @Autowired
    AdFaqOtherQuestionCachePack adFaqOtherQuestionCachePack;

    /**
     * 查询页面绑定记录
     *
     * @return
     */
    @LogAttr(desc = "查询页面绑定记录",logType = LogType.query)
    @Permission(code = "store.faq.other.listQuestionBind", desc = "查询页面绑定记录")
    @GetMapping("/admin/faq/other/listQuestionBind")
    public Response<? extends Map> listQuestionBind() {
        List<FaqOtherQuestionBind> questionBinds = questionBindService.listQuestionBind();
        if (CollectionUtils.isEmpty(questionBinds)) {
            return Response.ok();
        }

        // 封装返回
        List<AdFaqOtherQuestionBindVO> questionBindList = questionBinds.stream().map(AdFaqOtherQuestionBindVO::new).collect(Collectors.toList());
        return Response.ok("faqOtherQuestionBinds", questionBindList);
    }

    /**
     * 创建页面绑定
     *
     * @param questionBindParam
     * @return
     */
    @LogAttr(desc = "创建页面绑定")
    @Permission(code = "store.faq.other.createQuestionBind", desc = "创建页面绑定")
    @PostMapping(path = "/admin/faq/other/create/questionBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> createQuestionBind(@Validated @RequestBody OtherQuestionBindDTO questionBindParam) {
        if (StringUtil.isBlank(questionBindParam.getType()) || StringUtil.isBlank(questionBindParam.getPageKey())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        // 防止绑定页面重复创建
        FaqOtherQuestionBind questionBind = questionBindService.getByTypePageKey(questionBindParam.getType(), questionBindParam.getPageKey());
        if (questionBind != null) {
            throw new InstaException(FaqErrorCode.FaqPageBindAlreadyExistException);
        }
        questionBindService.createQuestionBind(questionBindParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 启用页面绑定
     *
     * @param questionBindParam
     * @return
     */
    @LogAttr(desc = "启用页面绑定")
    @Permission(code = "store.faq.other.enableQuestionBind", desc = "启用页面绑定")
    @PostMapping(path = "/admin/faq/other/enable/questionBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> enableQuestionBind(@RequestBody OtherQuestionBindDTO questionBindParam) {
        FaqOtherQuestionBind questionBind = questionBindService.getById(questionBindParam.getId());
        if (questionBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqOtherQuestionCachePack.enableQuestionBind(questionBind);
        return Response.ok();
    }

    /**
     * 禁用页面绑定
     *
     * @param questionBindParam
     * @return
     */
    @LogAttr(desc = "禁用页面绑定")
    @Permission(code = "store.faq.other.disableQuestionBind", desc = "禁用页面绑定")
    @PostMapping(path = "/admin/faq/other/disable/questionBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> disableQuestionBind(@RequestBody OtherQuestionBindDTO questionBindParam) {
        FaqOtherQuestionBind questionBind = questionBindService.getById(questionBindParam.getId());
        if (questionBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqOtherQuestionCachePack.disableQuestionBind(questionBind);
        return Response.ok();
    }

    /**
     * 删除页面绑定
     *
     * @param questionBindParam
     * @return
     */
    @LogAttr(desc = "删除页面绑定")
    @Permission(code = "store.faq.other.deleteQuestionBind", desc = "删除页面绑定")
    @PostMapping(path = "/admin/faq/other/delete/questionBind", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> deleteQuestionBind(@RequestBody OtherQuestionBindDTO questionBindParam) {
        FaqOtherQuestionBind questionBind = questionBindService.getById(questionBindParam.getId());
        if (questionBind == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adFaqOtherQuestionCachePack.deleteQuestionBind(questionBind.getId());
        return Response.ok();
    }
}
