package com.insta360.store.admin.controller.tradeup.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.tradeup.model.TradeupEvaluationRule;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2020/04/10
 * @Description:
 */
public class AdTradeupEvaluationRuleVO implements Serializable {

    /**
     * id
     */
    private Integer id;

    /**
     * 设备类型
     */
    private Integer device;

    /**
     * 价格
     */
    @JSONField(name = "price_map")
    private String priceMap;

    /**
     * 促销价格
     */
    @JSONField(name = "promo_price_map")
    private String promoPriceMap;

    /**
     * 设备名
     */
    @JSONField(name = "device_name")
    private String deviceName;

    /**
     * 品牌名
     */
    @JSONField(name = "device_brand")
    private String deviceBrand;

    public AdTradeupEvaluationRuleVO() {
    }

    public AdTradeupEvaluationRuleVO(TradeupEvaluationRule evaluationRule) {
        if (evaluationRule != null) {
            BeanUtil.copyProperties(evaluationRule, this);
        }
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceBrand() {
        return deviceBrand;
    }

    public void setDeviceBrand(String deviceBrand) {
        this.deviceBrand = deviceBrand;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getDevice() {
        return device;
    }

    public void setDevice(Integer device) {
        this.device = device;
    }

    public String getPriceMap() {
        return priceMap;
    }

    public void setPriceMap(String priceMap) {
        this.priceMap = priceMap;
    }

    public String getPromoPriceMap() {
        return promoPriceMap;
    }

    public void setPromoPriceMap(String promoPriceMap) {
        this.promoPriceMap = promoPriceMap;
    }

    @Override
    public String toString() {
        return "AdTradeupEvaluationRuleVO{" +
                "id=" + id +
                ", device=" + device +
                ", priceMap='" + priceMap + '\'' +
                ", promoPriceMap='" + promoPriceMap + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", deviceBrand='" + deviceBrand + '\'' +
                '}';
    }
}
