package com.insta360.store.admin.controller.faq.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.faq.cache.AdFaqCategoryCachePack;
import com.insta360.store.admin.controller.faq.format.AdFaqCategoryPack;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.faq.dto.CategoryInsideDTO;
import com.insta360.store.business.faq.exception.FaqErrorCode;
import com.insta360.store.business.faq.model.FaqCategoryInside;
import com.insta360.store.business.faq.service.FaqCategoryInsideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2022/4/7
 * @Description:
 */
@PermissionResource(code = "faqCategoryInside", desc = "Faq一级内部类目")
@RestController
public class AdFaqCategoryInsideApi extends BaseAdminApi {

    @Autowired
    FaqCategoryInsideService categoryInsideService;

    @Autowired
    AdFaqCategoryPack categoryPack;

    @Autowired
    AdFaqCategoryCachePack adFaqCategoryCachePack;

    /**
     * 获取一二级类目问题内部类目
     *
     * @return
     */
    @LogAttr(desc = "获取一二级类目问题内部类目",logType = LogType.query)
    @Permission(code = "store.faq.category.listCategoryInside", desc = "获取一二级类目问题内部类目")
    @GetMapping("/admin/faq/listCategoryInside")
    public Response<? extends Map> listCategoryInside() {
        List<FaqCategoryInside> categoryInsideList = categoryInsideService.listCategoryInside();
        return Response.ok("faqCategoryInsides", categoryPack.packCategoryInsides(categoryInsideList));
    }

    /**
     * 创建一级内部类目
     *
     * @param categoryInsideParam
     * @return
     */
    @LogAttr(desc = "创建一级内部类目")
    @Permission(code = "store.faq.category.createCategoryInside", desc = "创建一级内部类目")
    @PostMapping(path = "/admin/faq/create/categoryInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> createCategoryInside(@Validated @RequestBody CategoryInsideDTO categoryInsideParam) {
        if (StringUtil.isBlank(categoryInsideParam.getCategoryInsideName())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        FaqCategoryInside categoryInside = categoryInsideParam.getPojoObject();
        categoryInsideService.createCategoryInside(categoryInside);
        return Response.ok(categoryInside.getId());
    }

    /**
     * 更新一级内部名称
     *
     * @param categoryInsideParam
     * @return
     */
    @LogAttr(desc = "更新一级内部名称")
    @Permission(code = "store.faq.category.updateCategoryInside", desc = "更新一级内部名称")
    @PostMapping(path = "/admin/faq/update/categoryInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateCategoryInside(@Validated @RequestBody CategoryInsideDTO categoryInsideParam) {
        if (categoryInsideParam.getId() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        categoryInsideService.updateCategoryInside(categoryInsideParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 删除一级类目
     *
     * @param categoryInsideParam
     * @return
     */
    @LogAttr(desc = "删除一级类目")
    @Permission(code = "store.faq.category.deleteCategoryInside", desc = "删除一级类目")
    @PostMapping(path = "/admin/faq/delete/categoryInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> deleteCategoryInside(@RequestBody CategoryInsideDTO categoryInsideParam) {
        FaqCategoryInside categoryInside = categoryInsideService.getById(categoryInsideParam.getId());
        if (categoryInside == null) {
            throw new InstaException(FaqErrorCode.FaqCategoryNotFoundException);
        }
        adFaqCategoryCachePack.deleteCategoryInside(categoryInside.getId());
        return Response.ok();
    }
}
