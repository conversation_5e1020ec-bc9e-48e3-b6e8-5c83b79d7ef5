package com.insta360.store.admin.controller.test.wxq.dto;

import com.insta360.store.business.configuration.validation.annotation.URLCharacterCheck;
import org.hibernate.validator.constraints.URL;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/1
 */
public class UrlCheckDTO {

    /**
     * 网址
     */
    @URL
    private String url;

    /**
     * 文件名
     */
    @URLCharacterCheck
    private String fileName;

    /**
     * 文件名
     */
    @URLCharacterCheck
    private String name;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "UrlCheckDTO{" +
                "url='" + url + '\'' +
                ", fileName='" + fileName + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
