package com.insta360.store.admin.controller.faq.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.faq.model.FaqCategory;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInfo;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInside;
import com.insta360.store.business.faq.model.FaqCategorySubset;
import com.insta360.store.business.faq.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/9/1
 * @Description:
 */
@Component
public class AdFaqCategoryCachePack {

    @Autowired
    FaqCategoryService categoryService;

    @Autowired
    FaqCategoryInsideService categoryInsideService;

    @Autowired
    FaqCategoryQuestionInfoService questionInfoService;

    @Autowired
    FaqCategoryQuestionInsideService questionInsideService;

    @Autowired
    FaqCategorySubsetService categorySubsetService;

    @Autowired
    FaqCategorySubsetInsideService subsetInsideService;

    /**
     * 更新一级类目多语言
     *
     * @param categoryList
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void updateCategory(List<FaqCategory> categoryList) {
        categoryService.updateCategory(categoryList);
    }

    /**
     * 删除一级类目
     *
     * @param categoryInsideId
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void deleteCategoryInside(Integer categoryInsideId) {
        categoryInsideService.deleteCategoryInside(categoryInsideId);
    }

    /**
     * 更新问题多语言
     *
     * @param categoryQuestionInfos
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void updateQuestionInfo(List<FaqCategoryQuestionInfo> categoryQuestionInfos) {
        questionInfoService.updateQuestionInfo(categoryQuestionInfos);
    }

    /**
     * 启用问题
     *
     * @param categoryQuestionInside
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void enableQuestionInside(FaqCategoryQuestionInside categoryQuestionInside) {
        questionInsideService.enableQuestionInside(categoryQuestionInside);
    }

    /**
     * 启用问题
     *
     * @param categoryQuestionInside
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void disableQuestionInside(FaqCategoryQuestionInside categoryQuestionInside) {
        questionInsideService.disableQuestionInside(categoryQuestionInside);
    }

    /**
     * 启用问题
     *
     * @param categoryQuestionInsideId
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void deleteQuestionInside(Integer categoryQuestionInsideId) {
        questionInsideService.deleteQuestionInside(categoryQuestionInsideId);
    }

    /**
     * 更新二级类目多语言
     *
     * @param categorySubsetList
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void updateCategorySubset(List<FaqCategorySubset> categorySubsetList) {
        categorySubsetService.updateCategorySubset(categorySubsetList);
    }

    /**
     * 删除二级内部类目
     *
     * @param categorySubsetInsideId
     * @return
     */
    @CachePutMonitor(cacheableType = CachePutType.FAQ_QUESTION)
    public void deleteCategorySubsetInside(Integer categorySubsetInsideId) {
        subsetInsideService.deleteCategorySubsetInside(categorySubsetInsideId);
    }
}
