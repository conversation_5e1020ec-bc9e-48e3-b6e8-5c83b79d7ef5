package com.insta360.store.admin.controller.test.tool.user;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.controller.test.tool.user.helper.UserSyncHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/2/27
 */
@RestController
public class UserSyncTestApi {

    @Autowired
    UserSyncHelper userSyncHelper;

    /**
     * 批量同步订单user_id
     *
     * @param batchSize
     * @return
     */
    @GetMapping("/admin/tool/user/batchSyncOrder")
    public Response<Object> batchSyncOrder(@RequestParam Integer batchSize) {
        if (Objects.isNull(batchSize)) {
            return Response.failed();
        }

        userSyncHelper.syncOrder(batchSize);

        return Response.ok();
    }

    /**
     * 批量同步用户购物车user_id
     *
     * @param batchSize
     * @return
     */
    @GetMapping("/admin/tool/user/batchSyncUserCart")
    public Response<Object> batchSyncUserCart(@RequestParam Integer batchSize) {
        if (Objects.isNull(batchSize)) {
            return Response.failed();
        }

        userSyncHelper.batchSyncUserCart(batchSize);
        return Response.ok();
    }

    /**
     * 批量同步用户地址user_id
     *
     * @param batchSize
     * @return
     */
    @GetMapping("/admin/tool/user/batchSyncUserDelivery")
    public Response<Object> batchSyncUserDelivery(@RequestParam Integer batchSize) {
        if (Objects.isNull(batchSize)) {
            return Response.failed();
        }

        userSyncHelper.syncUserDelivery(batchSize);
        return Response.ok();
    }
}
