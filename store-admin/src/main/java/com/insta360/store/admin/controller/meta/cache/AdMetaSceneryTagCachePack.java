package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.dto.sceneryTag.SceneryTagMainDTO;
import com.insta360.store.business.meta.dto.sceneryTag.SceneryTagProductDTO;
import com.insta360.store.business.meta.service.impl.helper.SceneryTagHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: py
 * @create: 2024-12-18 17:43
 */
@Component
public class AdMetaSceneryTagCachePack {

    @Autowired
    SceneryTagHelper sceneryTagHelper;

    /**
     * 新增场景标签
     *
     * @param sceneryTagMainParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_TAG_MAIN)
    public void addSceneryTag(SceneryTagMainDTO sceneryTagMainParam) {
        sceneryTagHelper.addSceneryTag(sceneryTagMainParam);
    }

    /**
     * 更新场景标签
     *
     * @param sceneryTagMainParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_TAG_MAIN)
    public void updateSceneryTag(SceneryTagMainDTO sceneryTagMainParam) {
        sceneryTagHelper.updateSceneryTag(sceneryTagMainParam);
    }

    /**
     * 删除场景标签
     *
     * @param sceneryTagMainParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_TAG_MAIN)
    public void deleteSceneryTag(SceneryTagMainDTO sceneryTagMainParam) {
        sceneryTagHelper.deleteSceneryTag(sceneryTagMainParam);
    }

    /**************************************** 场景标签关联产品 ****************************************/

    /**
     * 删除关联产品
     *
     * @param sceneryTagProductId
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_TAG_MAIN)
    public void deleteProduct(Integer sceneryTagProductId) {
        sceneryTagHelper.deleteProduct(sceneryTagProductId);
    }

    /**
     * 新增关联产品
     *
     * @param sceneryTagProductParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_TAG_MAIN)
    public void addProducts(SceneryTagProductDTO sceneryTagProductParam) {
        sceneryTagHelper.addProducts(sceneryTagProductParam);
    }
}
