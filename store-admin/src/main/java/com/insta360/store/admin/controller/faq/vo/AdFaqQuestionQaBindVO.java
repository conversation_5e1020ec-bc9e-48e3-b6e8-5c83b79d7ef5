package com.insta360.store.admin.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqQuestionQaBind;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/4/24
 * @Description:
 */
public class AdFaqQuestionQaBindVO implements Serializable {

    private Integer id;

    /**
     * 内部分类问题id
     */
    private Integer questionId;

    /**
     * 内部分类问题名称
     */
    private String questionName;

    /**
     * 关联绑定二级目录id
     */
    private Integer subsetBindId;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 是否启用（0：默认不启用，1：启用）
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public AdFaqQuestionQaBindVO(FaqQuestionQaBind questionQaBind) {
        if (questionQaBind != null) {
            BeanUtil.copyProperties(questionQaBind, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }

    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public Integer getSubsetBindId() {
        return subsetBindId;
    }

    public void setSubsetBindId(Integer subsetBindId) {
        this.subsetBindId = subsetBindId;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
