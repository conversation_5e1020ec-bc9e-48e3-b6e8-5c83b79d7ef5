package com.insta360.store.admin.controller.meta.vo.navigationbar;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategoryInfo;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/5/24
 * @Description:
 */
public class AdNavigationBarCategoryInfoVO implements Serializable {

    private Integer id;

    /**
     * 一级内部分类id
     */
    private Integer categoryInsideId;

    /**
     * 一级分类名称
     */
    private String categoryName;

    /**
     * 语言
     */
    private String language;

    /**
     * 地区
     */
    private String country;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public AdNavigationBarCategoryInfoVO() {
    }

    public AdNavigationBarCategoryInfoVO(NavigationBarCategoryInfo categoryInfo) {
        if (categoryInfo != null) {
            BeanUtil.copyProperties(categoryInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AdNavigationBarCategoryInfoVO{" +
                "id=" + id +
                ", categoryInsideId=" + categoryInsideId +
                ", categoryName='" + categoryName + '\'' +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
