package com.insta360.store.admin.controller.test.wxq.active.entity;

import com.insta360.compass.core.enums.InstaCountry;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public class ActivityLocalesConfig {

    /**
     * id
     */
    private Integer id;

    /**
     * 分组Key
     */
    private String groupKey;

    /**
     * 活动ID
     */
    private Integer activityId;

    /**
     * 生效国家
     */
    private InstaCountry country;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 活动创建时间
     */
    private LocalDateTime createTime;

    /**
     * 活动更新时间
     */
    private LocalDateTime updateTime;

}
