package com.insta360.store.admin.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.MetaShippingCost;

import java.io.Serializable;
import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2023-12-12 17:17
 */
public class AdShippingCostVO implements Serializable {

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 货币金额
     */
    private String currency;

    /**
     * 包邮门槛
     */
    private Float freeLimit;

    /**
     * 组织code
     */
    private String groupCode;

    /**
     * 中文名
     */
    private String nameZh;

    public AdShippingCostVO() {
    }

    public AdShippingCostVO(MetaShippingCost metaShippingCost) {
        if (Objects.nonNull(metaShippingCost)) {
            BeanUtil.copyProperties(metaShippingCost, this);
        }
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Float getFreeLimit() {
        return freeLimit;
    }

    public void setFreeLimit(Float freeLimit) {
        this.freeLimit = freeLimit;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    @Override
    public String toString() {
        return "AdShippingCostVO{" +
                "countryCode='" + countryCode + '\'' +
                ", currency='" + currency + '\'' +
                ", freeLimit=" + freeLimit +
                ", groupCode='" + groupCode + '\'' +
                ", nameZh='" + nameZh + '\'' +
                '}';
    }
}
