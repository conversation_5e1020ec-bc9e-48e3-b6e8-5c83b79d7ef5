package com.insta360.store.admin.controller.meta.controller.tradepoint;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdMetaTradePointCachePack;
import com.insta360.store.admin.controller.meta.vo.tradepoint.AdTradePointInfoVO;
import com.insta360.store.business.meta.dto.TradePointInfoDTO;
import com.insta360.store.business.meta.exception.MetaErrorCode;
import com.insta360.store.business.meta.model.TradePointInfo;
import com.insta360.store.business.meta.model.TradePointMain;
import com.insta360.store.business.meta.service.TradePointInfoService;
import com.insta360.store.business.meta.service.TradePointMainService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/09/01
 * @Description:
 */
@RestController
@PermissionResource(code = "tradePointInfo", desc = "卖点信息配置")
public class AdTradePointInfoApi extends BaseAdminApi {

    @Autowired
    TradePointMainService tradePointMainService;

    @Autowired
    TradePointInfoService tradePointInfoService;

    @Autowired
    AdMetaTradePointCachePack adMetaTradePointCachePack;

    /**
     * 获取某个 trade point的详细配置
     *
     * @param tradePointId
     * @return
     */
    @LogAttr(desc = "获取某个卖点的详细配置", logType = LogType.query)
    @Permission(code = "store.meta.tradePointInfo.listTradePointInfos", desc = "获取某个卖点的详细配置")
    @GetMapping("/admin/meta/tp/info/listTradePointInfos")
    public Response<? extends Map> listTradePointInfos(@RequestParam Integer tradePointId) {
        List<TradePointInfo> tradePointInfos = tradePointInfoService.listTradePointInfos(tradePointId);
        // 返回值转换
        List<AdTradePointInfoVO> tradePointInfoVos = tradePointInfos.stream().map(AdTradePointInfoVO::new).collect(Collectors.toList());
        return Response.ok("tradePointInfos", tradePointInfoVos);
    }

    /**
     * 新增 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @LogAttr(desc = "新增 trade point info")
    @Permission(code = "store.meta.tradePointInfo.createTradePointInfo", desc = "新增")
    @PostMapping("/admin/meta/tp/info/createTradePointInfo")
    public Response<Object> createTradePointInfo(@RequestBody TradePointInfoDTO tradePointInfoParam) {
        // 分类校验
        TradePointMain tradePointMain = tradePointMainService.getById(tradePointInfoParam.getTradePointId());
        if (tradePointMain == null || tradePointMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.TradePointNotFoundException);
        }

        // save
        adMetaTradePointCachePack.saveTradePointInfo(tradePointInfoParam);
        return Response.ok();
    }

    /**
     * 更新 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @LogAttr(desc = "更新 trade point info")
    @Permission(code = "store.meta.tradePointInfo.updateTradePointInfo", desc = "更新")
    @PostMapping("/admin/meta/tp/info/updateTradePointInfo")
    public Response<Object> updateTradePointInfo(@RequestBody TradePointInfoDTO tradePointInfoParam) {
        if (CollectionUtils.isEmpty(tradePointInfoParam.getTradePointInfoIds())) {
            throw new InstaException(MetaErrorCode.TradePointInfoNotFoundException);
        }

        // update
        adMetaTradePointCachePack.updateTradePointInfo(tradePointInfoParam);
        return Response.ok();
    }

    /**
     * 删除 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @LogAttr(desc = "删除 trade point info")
    @Permission(code = "store.meta.tradePointInfo.deleteTradePointInfo", desc = "删除")
    @PostMapping("/admin/meta/tp/info/deleteTradePointInfo")
    public Response<Object> deleteTradePointInfo(@RequestBody TradePointInfoDTO tradePointInfoParam) {
        // 信息校验
        TradePointInfo tradePointInfo = tradePointInfoService.getById(tradePointInfoParam.getId());
        if (tradePointInfo == null || tradePointInfo.getDeleted()) {
            throw new InstaException(MetaErrorCode.TradePointInfoNotFoundException);
        }

        // delete
        adMetaTradePointCachePack.deleteBatchByTradePointInfo(Arrays.asList(tradePointInfo));
        return Response.ok();
    }

    /**
     * 启用 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @LogAttr(desc = "启用 trade point info")
    @Permission(code = "store.meta.tradePointInfo.enableTradePointInfo", desc = "启用")
    @PostMapping("/admin/meta/tp/info/enableTradePointInfo")
    public Response<Object> enableTradePointInfo(@RequestBody TradePointInfoDTO tradePointInfoParam) {
        // 信息校验
        TradePointInfo tradePointInfo = tradePointInfoService.getById(tradePointInfoParam.getId());
        if (tradePointInfo == null || tradePointInfo.getDeleted()) {
            throw new InstaException(MetaErrorCode.TradePointInfoNotFoundException);
        }

        // enable
        adMetaTradePointCachePack.enableTradePointInfo(tradePointInfo);
        return Response.ok();
    }

    /**
     * 批量启用 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @LogAttr(desc = "批量启用 trade point info")
    @Permission(code = "store.meta.tradePointInfo.enableBatchTradePointInfo", desc = "批量启用")
    @PostMapping("/admin/meta/tp/info/enableBatchTradePointInfo")
    public Response<Object> enableBatchTradePointInfo(@RequestBody TradePointInfoDTO tradePointInfoParam) {
        // 信息校验
        if (CollectionUtils.isEmpty(tradePointInfoParam.getTradePointInfoIds())) {
            throw new InstaException(MetaErrorCode.TradePointInfoNotFoundException);
        }

        // enable batch
        adMetaTradePointCachePack.enableBatchTradePointInfo(tradePointInfoParam.getTradePointInfoIds());
        return Response.ok();
    }

    /**
     * 禁用 trade point info
     *
     * @param tradePointInfoParam
     * @return
     */
    @LogAttr(desc = "禁用 trade point info")
    @Permission(code = "store.meta.tradePointInfo.disableTradePointInfo", desc = "禁用")
    @PostMapping("/admin/meta/tp/info/disableTradePointInfo")
    public Response<Object> disableTradePointInfo(@RequestBody TradePointInfoDTO tradePointInfoParam) {
        // 信息校验
        TradePointInfo tradePointInfo = tradePointInfoService.getById(tradePointInfoParam.getId());
        if (tradePointInfo == null || tradePointInfo.getDeleted()) {
            throw new InstaException(MetaErrorCode.TradePointInfoNotFoundException);
        }

        // disable
        adMetaTradePointCachePack.disableTradePointInfo(tradePointInfo);
        return Response.ok();
    }
}
