package com.insta360.store.admin.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqQuestionBind;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/4/24
 * @Description:
 */
public class AdFaqQuestionBindVO implements Serializable {

    private Integer id;

    /**
     * 内部一级目录id
     */
    private Integer categoryInsideId;

    /**
     * 内部一级目录名称
     */
    private String categoryInsideName;

    /**
     * 页面类型
     */
    private String type;

    /**
     * 产品页是产品页url_key,邮件是模板key，普通页面协商
     */
    private String pageKey;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 是否启用（0：默认不启用，1：启用）
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 页面二级绑定
     */
    private List<AdFaqQuestionSubsetBindVO> questionSubsetBinds;

    public AdFaqQuestionBindVO(FaqQuestionBind questionBind) {
        if (questionBind != null) {
            BeanUtil.copyProperties(questionBind, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public String getCategoryInsideName() {
        return categoryInsideName;
    }

    public void setCategoryInsideName(String categoryInsideName) {
        this.categoryInsideName = categoryInsideName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPageKey() {
        return pageKey;
    }

    public void setPageKey(String pageKey) {
        this.pageKey = pageKey;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public List<AdFaqQuestionSubsetBindVO> getQuestionSubsetBinds() {
        return questionSubsetBinds;
    }

    public void setQuestionSubsetBinds(List<AdFaqQuestionSubsetBindVO> questionSubsetBinds) {
        this.questionSubsetBinds = questionSubsetBinds;
    }
}
