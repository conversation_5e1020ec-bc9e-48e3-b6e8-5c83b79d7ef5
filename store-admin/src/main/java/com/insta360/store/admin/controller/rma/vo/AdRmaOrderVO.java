package com.insta360.store.admin.controller.rma.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.admin.controller.product.vo.AdProductCategoryMainVO;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.rma.model.RmaDelivery;
import com.insta360.store.business.rma.model.RmaOrder;

/**
 * @Author: hyc
 * @Date: 2019-09-16
 * @Description:
 */
public class AdRmaOrderVO extends RmaOrder {


    @JSONField(name = "order_area")
    private String orderArea;

    @JSONField(name = "order_total_price")
    private Price orderTotalPrice;

    private RmaDelivery delivery;

    @JSONField(name = "order_item")
    private AdRmaItemVO orderItem;

    @JSONField(name = "product_name")
    private String productName;

    @JSONField(name = "commodity_name")
    private String commodityName;

    @JSONField(name = "commodity_display")
    private String commodityDisplay;

    @JSONField(name = "order_number")
    private String orderNumber;

    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 产品类目
     */
    private AdProductCategoryMainVO productCategory;

    /**
     * ShippingLabel开具按钮
     */
    private Boolean slButton;

    /**
     * 售后商品类型
     */
    private Integer itemType;

    /**
     * 推送平台
     *
     * @see com.insta360.store.business.order.enums.PushPlatformType
     */
    private Integer pushPlatform;

    public AdRmaOrderVO() {
    }

    public AdRmaOrderVO(RmaOrder rmaOrder) {
        if (rmaOrder != null) {
            BeanUtil.copyProperties(rmaOrder, this);
        }
    }

    public RmaDelivery getDelivery() {
        return delivery;
    }

    public void setDelivery(RmaDelivery delivery) {
        this.delivery = delivery;
    }

    public String getOrderArea() {
        return orderArea;
    }

    public void setOrderArea(String orderArea) {
        this.orderArea = orderArea;
    }

    public Price getOrderTotalPrice() {
        return orderTotalPrice;
    }

    public void setOrderTotalPrice(Price orderTotalPrice) {
        this.orderTotalPrice = orderTotalPrice;
    }

    public AdRmaItemVO getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(AdRmaItemVO orderItem) {
        this.orderItem = orderItem;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommodityDisplay() {
        return commodityDisplay;
    }

    public void setCommodityDisplay(String commodityDisplay) {
        this.commodityDisplay = commodityDisplay;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public AdProductCategoryMainVO getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(AdProductCategoryMainVO productCategory) {
        this.productCategory = productCategory;
    }

    public Boolean getSlButton() {
        return slButton;
    }

    public void setSlButton(Boolean slButton) {
        this.slButton = slButton;
    }

    public Integer getItemType() {
        return itemType;
    }

    public void setItemType(Integer itemType) {
        this.itemType = itemType;
    }

    public Integer getPushPlatform() {
        return pushPlatform;
    }

    public void setPushPlatform(Integer pushPlatform) {
        this.pushPlatform = pushPlatform;
    }

    @Override
    public String toString() {
        return "AdRmaOrderVO{" +
                "orderArea='" + orderArea + '\'' +
                ", orderTotalPrice=" + orderTotalPrice +
                ", delivery=" + delivery +
                ", orderItem=" + orderItem +
                ", productName='" + productName + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", commodityDisplay='" + commodityDisplay + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", orderState=" + orderState +
                ", productType=" + productType +
                ", productCategory=" + productCategory +
                ", slButton=" + slButton +
                ", itemType=" + itemType +
                ", pushPlatform=" + pushPlatform +
                '}';
    }
}
