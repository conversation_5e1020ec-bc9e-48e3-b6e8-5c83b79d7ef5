package com.insta360.store.admin.controller.meta.controller.livebroadcast;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdMetaLiveBroadcastCachePack;
import com.insta360.store.admin.controller.meta.format.AdLiveBroadcastConfigPack;
import com.insta360.store.admin.controller.meta.vo.livebroadcast.LiveBroadcastVO;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dto.LiveBroadcastConfigDTO;
import com.insta360.store.business.meta.dto.condition.LiveBroadcastQueryCondition;
import com.insta360.store.business.meta.model.LiveBroadcastConfig;
import com.insta360.store.business.meta.service.LiveBroadcastConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2022/10/19
 * @Description:
 */
@PermissionResource(code = "liveBroadcastConfigApi", desc = "直播配置")
@RestController
public class AdLiveBroadcastConfigApi extends BaseAdminApi {

    @Autowired
    LiveBroadcastConfigService liveBroadcastConfigService;

    @Autowired
    AdLiveBroadcastConfigPack adLiveBroadcastConfigPack;

    @Autowired
    AdMetaLiveBroadcastCachePack adMetaLiveBroadcastCachePack;

    /**
     * 新增直播配置
     *
     * @return
     */
    @LogAttr(desc = "新增直播配置")
    @Permission(code = "store.meta.addLiveBroadcast", desc = "新增直播配置")
    @PostMapping(value = "/admin/meta/addLiveBroadcast", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> addLiveBroadcast(@Valid @RequestBody LiveBroadcastConfigDTO liveBroadcastConfigParam) {
        liveBroadcastConfigService.addLiveBroadcast(liveBroadcastConfigParam);
        return Response.ok();
    }

    /**
     * 更新直播配置
     *
     * @return
     */
    @LogAttr(desc = "更新直播配置")
    @Permission(code = "store.meta.updateLiveBroadcast", desc = "更新直播配置")
    @PostMapping(value = "/admin/meta/updateLiveBroadcast", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateLiveBroadcast(@Valid @RequestBody LiveBroadcastConfigDTO liveBroadcastConfigParam) {
        LiveBroadcastConfig broadcastConfig = liveBroadcastConfigService.getById(liveBroadcastConfigParam.getId());
        if (null == broadcastConfig) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adMetaLiveBroadcastCachePack.updateLiveBroadcast(liveBroadcastConfigParam);
        return Response.ok();
    }

    /**
     * 删除直播配置
     *
     * @return
     */
    @LogAttr(desc = "删除直播配置")
    @Permission(code = "store.meta.deleteLiveBroadcast", desc = "删除直播配置")
    @PostMapping(value = "/admin/meta/deleteLiveBroadcast", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> deleteLiveBroadcast(@RequestBody LiveBroadcastConfigDTO liveBroadcastConfigParam) {
        LiveBroadcastConfig broadcastConfig = liveBroadcastConfigService.getById(liveBroadcastConfigParam.getId());
        if (null == broadcastConfig) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adMetaLiveBroadcastCachePack.deleteLiveBroadcast(liveBroadcastConfigParam.getId());
        return Response.ok();
    }

    /**
     * 启用直播配置
     *
     * @return
     */
    @LogAttr(desc = "启用直播配置")
    @Permission(code = "store.meta.enableLiveBroadcast", desc = "启用直播配置")
    @PostMapping(value = "/admin/meta/enableLiveBroadcast", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> enableLiveBroadcast(@RequestBody LiveBroadcastConfigDTO liveBroadcastConfigParam) {
        LiveBroadcastConfig broadcastConfig = liveBroadcastConfigService.getById(liveBroadcastConfigParam.getId());
        if (null == broadcastConfig || broadcastConfig.getEnabled()) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adMetaLiveBroadcastCachePack.enableLiveBroadcast(broadcastConfig);
        return Response.ok();
    }

    /**
     * 禁用直播配置
     *
     * @return
     */
    @LogAttr(desc = "禁用直播配置")
    @Permission(code = "store.meta.disableLiveBroadcast", desc = "禁用直播配置")
    @PostMapping(value = "/admin/meta/disableLiveBroadcast", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> disableLiveBroadcast(@RequestBody LiveBroadcastConfigDTO liveBroadcastConfigParam) {
        LiveBroadcastConfig broadcastConfig = liveBroadcastConfigService.getById(liveBroadcastConfigParam.getId());
        if (null == broadcastConfig || !broadcastConfig.getEnabled()) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        adMetaLiveBroadcastCachePack.disableLiveBroadcast(broadcastConfig);
        return Response.ok();
    }

    /**
     * 查询直播配置
     *
     * @return
     */
    @LogAttr(desc = "查询直播配置", logType = LogType.query)
    @Permission(code = "store.meta.listLiveBroadcast", desc = "查询直播配置")
    @PostMapping(value = "/admin/meta/listLiveBroadcast", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> listLiveBroadcast(@RequestBody LiveBroadcastConfigDTO liveBroadcastConfigParam) {
        // 查询条件
        LiveBroadcastQueryCondition queryCondition = new LiveBroadcastQueryCondition();
        queryCondition.setStartTime(liveBroadcastConfigParam.getStartTime());
        queryCondition.setEndTime(liveBroadcastConfigParam.getEndTime());
        queryCondition.setInsideName(liveBroadcastConfigParam.getInsideName());
        queryCondition.setLiveBroadCastConfigInfos(liveBroadcastConfigParam.getLiveBroadCastConfigInfos());
        queryCondition.setDisplayPage(liveBroadcastConfigParam.getDisplayPage());

        // 分页条件
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(liveBroadcastConfigParam.getPageNumber(), 1);
        pageQuery.setPageSize(liveBroadcastConfigParam.getPageSize(), 10);

        // 返回值封装
        Page<LiveBroadcastConfig> broadcastConfigIPage = liveBroadcastConfigService.listLiveBroadcast(PageUtil.toIPage(pageQuery), queryCondition);
        List<LiveBroadcastVO> broadcastList = adLiveBroadcastConfigPack.packListLiveBroadcast(broadcastConfigIPage.getRecords());

        // 分页返回值封装
        PageResult<LiveBroadcastConfig> broadcastConfigPageResult = PageUtil.toPageResult(broadcastConfigIPage);
        PageResult<LiveBroadcastVO> pageResult = new PageResult<>();
        pageResult.setList(broadcastList);
        pageResult.setPageNumber(broadcastConfigPageResult.getPageNumber());
        pageResult.setPageSize(broadcastConfigPageResult.getPageSize());
        pageResult.setTotalPage(broadcastConfigPageResult.getTotalPage());
        pageResult.setTotalCount(broadcastConfigPageResult.getTotalCount());
        return Response.ok(pageResult);
    }
}
