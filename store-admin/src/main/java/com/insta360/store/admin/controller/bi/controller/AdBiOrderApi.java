package com.insta360.store.admin.controller.bi.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.log.enums.LogType;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseApi;
import com.insta360.store.admin.controller.bi.format.AdBiOrderPack;
import com.insta360.store.admin.controller.commodity.vo.AdCommodityVO;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @Author: hyc
 * @Date: 2019/3/25
 * @Description:
 */
@RestController
@PermissionResource(code = "biOrder", desc = "订单统计配置")
public class AdBiOrderApi extends BaseApi {

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    AdBiOrderPack biOrderPack;

    /**
     * 订单货币
     *
     * @param orderNumber
     */
    @LogAttr(desc = "订单货币", logType = LogType.query)
    @Permission(code = "store.bi.biOrder.getOrderCurrency", desc = "订单货币")
    @GetMapping("/admin/bi/getOrderCurrency")
    public Response<? extends Map> getOrderCurrency(@RequestParam(required = false, value = "order_number") String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        Currency currency = null;
        if (order != null) {
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (orderPayment != null) {
                currency = orderPayment.currency();
            }
        }
        return Response.ok("currency", currency);
    }

    /**
     * 订单统计
     *
     * @param fromTime
     * @param endTime
     * @param productId
     * @param country
     */
    @LogAttr(desc = "订单统计", logType = LogType.query)
    @Permission(code = "store.bi.biOrder.getCommodityCount", desc = "订单统计")
    @GetMapping("/admin/bi/getCommodityCount")
    public Response<? extends Map> getCommodityCount(@RequestParam(value = "from_time") Long fromTime,
                                                     @RequestParam(value = "end_time") Long endTime,
                                                     @RequestParam(value = "product_id") Integer productId,
                                                     @RequestParam(value = "country") String country) {
        LocalDateTime from = TimeUtil.parseLocalDateTime(fromTime);
        LocalDateTime end = TimeUtil.parseLocalDateTime(endTime);

        List<AdCommodityVO> commodities = biOrderPack.listCommodityCount(from, end, productId, country);
        return Response.ok("data_map", commodities);
    }
}
