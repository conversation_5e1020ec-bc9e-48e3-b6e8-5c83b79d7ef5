package com.insta360.store.admin.controller.meta.cache;

import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.meta.dto.scenerySection.SceneryCardMainDTO;
import com.insta360.store.business.meta.dto.scenerySection.ScenerySectionCardDTO;
import com.insta360.store.business.meta.dto.scenerySection.ScenerySectionMainDTO;
import com.insta360.store.business.meta.service.ProductCategorySceneryCardMainService;
import com.insta360.store.business.meta.service.ProductCategoryScenerySectionService;
import com.insta360.store.business.meta.service.impl.helper.ScenerySectionHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: py
 * @create: 2025-03-12 16:31
 */
@Component
public class AdCategoryScenerySectionCachePack {

    @Autowired
    ProductCategoryScenerySectionService productCategoryScenerySectionService;

    @Autowired
    ScenerySectionHelper scenerySectionHelper;

    @Autowired
    ProductCategorySceneryCardMainService productCategorySceneryCardMainService;

    /**
     * 场景专区排序
     *
     * @param scenerySectionMainList
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void orderScenerySection(List<ScenerySectionMainDTO> scenerySectionMainList) {
        productCategoryScenerySectionService.orderScenerySection(scenerySectionMainList);
    }

    /**
     * 更新场景专区
     *
     * @param scenerySectionMainParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void updateScenerySection(ScenerySectionMainDTO scenerySectionMainParam) {
        productCategoryScenerySectionService.updateScenerySection(scenerySectionMainParam);
    }

    /**
     * 启用场景专区
     *
     * @param scenerySectionId
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void enableScenerySection(Integer scenerySectionId) {
        productCategoryScenerySectionService.enableScenerySection(scenerySectionId);
    }

    /**
     * 禁用场景专区
     *
     * @param scenerySectionId
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void disableScenerySection(Integer scenerySectionId) {
        productCategoryScenerySectionService.disableScenerySection(scenerySectionId);
    }

    /**************************************** 场景专区卡片配置 ****************************************/

    /**
     * 新增卡片
     *
     * @param scenerySectionCardParam
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void addSceneryCard(ScenerySectionCardDTO scenerySectionCardParam) {
        scenerySectionHelper.addSceneryCard(scenerySectionCardParam);
    }

    /**
     * 卡片排序
     *
     * @param scenerySectionCardList
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void orderSceneryCard(List<SceneryCardMainDTO> scenerySectionCardList) {
        productCategorySceneryCardMainService.orderSceneryCard(scenerySectionCardList);
    }

    /**
     * 删除卡片配置
     *
     * @param sceneryCardMainId
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void deleteSceneryCard(Integer sceneryCardMainId, Integer scenerySectionId) {
        productCategorySceneryCardMainService.deleteSceneryCard(sceneryCardMainId, scenerySectionId);
    }

    /**
     * 更新卡片配置
     *
     * @param sceneryCardMain
     */
    @CachePutMonitor(cacheableType = CachePutType.SCENERY_SECTION)
    public void updateSceneryCard(SceneryCardMainDTO sceneryCardMain) {
        scenerySectionHelper.updateSceneryCard(sceneryCardMain);
    }
}
