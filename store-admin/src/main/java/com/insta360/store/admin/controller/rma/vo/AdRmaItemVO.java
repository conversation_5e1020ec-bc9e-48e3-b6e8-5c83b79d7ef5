package com.insta360.store.admin.controller.rma.vo;

import com.insta360.store.business.order.model.OrderItem;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 售后商品vo
 * @Date 2023/6/28
 */
public class AdRmaItemVO implements Serializable {

    /**
     * 订单商品ID
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer order;

    /**
     * 产品id
     */
    private Integer product;

    /**
     * 套餐id
     */
    private Integer commodity;

    /**
     * 下单现价
     */
    private Float price;

    /**
     * 下单原价
     */
    private Float originAmount;

    /**
     * 子项折扣
     */
    private Float discountFee;

    /**
     * 子项货币
     */
    private String currency;

    /**
     * 子项数量
     */
    private Integer number;

    /**
     * 是否是赠品
     */
    private Boolean isGift;

    /**
     * 子项状态
     */
    private Integer state;

    /**
     * 子项发货状态（避免与子项售后发生冲突，所以独立于state字段）
     */
    private Integer deliveryState;

    /**
     * 商品总税费
     */
    private BigDecimal totalTax;

    /**
     * 商品总折扣金额
     */
    private BigDecimal totalDiscount;

    /**
     * 商品总关税
     */
    private BigDecimal totalCustomsTax;

    public AdRmaItemVO() {
    }

    public AdRmaItemVO(OrderItem orderItem) {
        if (Objects.nonNull(orderItem)) {
            BeanUtils.copyProperties(orderItem, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getCommodity() {
        return commodity;
    }

    public void setCommodity(Integer commodity) {
        this.commodity = commodity;
    }

    public Float getPrice() {
        return price;
    }

    public void setPrice(Float price) {
        this.price = price;
    }

    public Float getOriginAmount() {
        return originAmount;
    }

    public void setOriginAmount(Float originAmount) {
        this.originAmount = originAmount;
    }

    public Float getDiscountFee() {
        return discountFee;
    }

    public void setDiscountFee(Float discountFee) {
        this.discountFee = discountFee;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Boolean getGift() {
        return isGift;
    }

    public void setGift(Boolean gift) {
        isGift = gift;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getDeliveryState() {
        return deliveryState;
    }

    public void setDeliveryState(Integer deliveryState) {
        this.deliveryState = deliveryState;
    }

    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public BigDecimal getTotalCustomsTax() {
        return totalCustomsTax;
    }

    public void setTotalCustomsTax(BigDecimal totalCustomsTax) {
        this.totalCustomsTax = totalCustomsTax;
    }

    @Override
    public String toString() {
        return "AdRmaItemVO{" +
                "id=" + id +
                ", order=" + order +
                ", product=" + product +
                ", commodity=" + commodity +
                ", price=" + price +
                ", originAmount=" + originAmount +
                ", discountFee=" + discountFee +
                ", currency='" + currency + '\'' +
                ", number=" + number +
                ", isGift=" + isGift +
                ", state=" + state +
                ", deliveryState=" + deliveryState +
                ", totalTax=" + totalTax +
                ", totalDiscount=" + totalDiscount +
                ", totalCustomsTax=" + totalCustomsTax +
                '}';
    }
}
