package com.insta360.store.admin.controller.meta.controller.categoryPage.scenery;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.meta.cache.AdCategoryScenerySectionCachePack;
import com.insta360.store.admin.controller.meta.format.AdProductCategoryPagePack;
import com.insta360.store.admin.controller.meta.vo.scenerySection.AdScenerySectionVO;
import com.insta360.store.business.meta.dto.scenerySection.SceneryCardMainDTO;
import com.insta360.store.business.meta.dto.scenerySection.ScenerySectionCardDTO;
import com.insta360.store.business.meta.model.ProductCategorySceneryCardMain;
import com.insta360.store.business.meta.model.ProductCategoryScenerySection;
import com.insta360.store.business.meta.service.ProductCategorySceneryCardMainService;
import com.insta360.store.business.meta.service.ProductCategoryScenerySectionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * @description: 场景专区
 * @author: py
 * @create: 2025-03-11 18:00
 */
@PermissionResource(code = "productCategoryScenerySection", desc = "场景专区配置")
@RestController
public class AdProductCategorySceneryCardApi extends BaseAdminApi {

    @Autowired
    AdCategoryScenerySectionCachePack categoryScenerySectionCachePack;

    @Autowired
    ProductCategoryScenerySectionService productCategoryScenerySectionService;

    @Autowired
    ProductCategorySceneryCardMainService productCategorySceneryCardMainService;

    @Autowired
    AdProductCategoryPagePack adProductCategoryPagePack;

    /**
     * 新增卡片配置
     *
     * @param scenerySectionCardParam
     * @return
     */
    @LogAttr(desc = "新增卡片配置")
    @Permission(code = "store.meta.categoryPage.addSceneryCard", desc = "新增卡片配置")
    @PostMapping(path = "/admin/meta/sc/addSceneryCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> addSceneryCard(@RequestBody @Validated ScenerySectionCardDTO scenerySectionCardParam) {
        if (Objects.isNull(scenerySectionCardParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "请求数据缺失");
        }

        Integer scenerySectionId = scenerySectionCardParam.getScenerySectionId();
        ProductCategoryScenerySection categoryScenerySection = productCategoryScenerySectionService.getById(scenerySectionId);
        if (Objects.isNull(categoryScenerySection)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        categoryScenerySectionCachePack.addSceneryCard(scenerySectionCardParam);
        return Response.ok();
    }

    /**
     * 卡片排序
     *
     * @param sceneryCardMainList
     * @return
     */
    @LogAttr(desc = "卡片排序")
    @Permission(code = "store.meta.categoryPage.orderSceneryCard", desc = "卡片排序")
    @PostMapping(path = "/admin/meta/sc/orderSceneryCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> orderSceneryCard(@RequestBody List<SceneryCardMainDTO> sceneryCardMainList) {
        if (CollectionUtils.isEmpty(sceneryCardMainList)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "场景专区排序数据缺失");
        }

        categoryScenerySectionCachePack.orderSceneryCard(sceneryCardMainList);
        return Response.ok();
    }

    /**
     * 删除卡片配置
     *
     * @param sceneryCardMain
     * @return
     */
    @LogAttr(desc = "删除卡片配置")
    @Permission(code = "store.meta.categoryPage.deleteSceneryCard", desc = "删除卡片配置")
    @PostMapping(path = "/admin/meta/sc/deleteSceneryCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> deleteSceneryCard(@RequestBody SceneryCardMainDTO sceneryCardMain) {
        if (Objects.isNull(sceneryCardMain) || Objects.isNull(sceneryCardMain.getId())) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "场景专区排序数据缺失");
        }

        ProductCategorySceneryCardMain categorySceneryCardMain = productCategorySceneryCardMainService.getById(sceneryCardMain.getId());
        if (Objects.isNull(categorySceneryCardMain)) {
            throw new InstaException(CommonErrorCode.InvalidParameter,"卡片不存在");
        }

        Integer scenerySectionId = categorySceneryCardMain.getScenerySectionId();
        ProductCategoryScenerySection categoryScenerySection = productCategoryScenerySectionService.getById(scenerySectionId);
        if (Objects.isNull(categoryScenerySection)) {
            throw new InstaException(CommonErrorCode.InvalidParameter,"场景专区不存在");
        }

        categoryScenerySectionCachePack.deleteSceneryCard(sceneryCardMain.getId(), scenerySectionId);
        return Response.ok();
    }

    /**
     * 更新卡片配置
     *
     * @param sceneryCardMain
     * @return
     */
    @LogAttr(desc = "更新卡片配置")
    @Permission(code = "store.meta.categoryPage.updateSceneryCard", desc = "更新卡片配置")
    @PostMapping(path = "/admin/meta/sc/updateSceneryCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateSceneryCard(@RequestBody SceneryCardMainDTO sceneryCardMain) {
        if (Objects.isNull(sceneryCardMain)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "场景专区排序数据缺失");
        }

        categoryScenerySectionCachePack.updateSceneryCard(sceneryCardMain);
        return Response.ok();
    }

    /**
     * 查询大小卡片
     *
     * @param scenerySectionId
     * @return
     */
    @LogAttr(desc = "查询大小卡片")
    @Permission(code = "store.meta.categoryPage.getSceneryCard", desc = "查询大小卡片")
    @GetMapping(path = "/admin/meta/sc/getSceneryCard")
    public Response<Object> getSceneryCard(@RequestParam Integer scenerySectionId) {
        if (Objects.isNull(scenerySectionId)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "场景专区ID缺失");
        }

        ProductCategoryScenerySection categoryScenerySection = productCategoryScenerySectionService.getById(scenerySectionId);
        if (Objects.isNull(categoryScenerySection)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "场景专区ID错误");
        }

        AdScenerySectionVO scenerySection = adProductCategoryPagePack.getSceneryCard(scenerySectionId);
        return Response.ok(scenerySection);
    }

}
