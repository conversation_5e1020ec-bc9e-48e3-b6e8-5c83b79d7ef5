package com.insta360.store.admin.controller.meta.vo.navigationbar;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategoryThird;

import java.io.Serializable;

/**
 * @description:
 * @author: py
 * @create: 2023-11-24 18:38
 */
public class AdNavigationBarCategoryThirdVO implements Serializable {

    private Integer id;

    /**
     * 二级导航id
     */
    private Integer subsetId;

    /**
     * 三级导航内部名称
     */
    private String thirdInsideName;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * view more跳转链接
     */
    private String urlLink;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 图片链接
     */
    private String imgUrl;

    /**
     * 排序
     */
    private Integer orderIndex;

    public AdNavigationBarCategoryThirdVO() {
    }

    public AdNavigationBarCategoryThirdVO(NavigationBarCategoryThird navigationBarCategoryThird) {
        if (navigationBarCategoryThird != null) {
            BeanUtil.copyProperties(navigationBarCategoryThird, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSubsetId() {
        return subsetId;
    }

    public void setSubsetId(Integer subsetId) {
        this.subsetId = subsetId;
    }

    public String getThirdInsideName() {
        return thirdInsideName;
    }

    public void setThirdInsideName(String thirdInsideName) {
        this.thirdInsideName = thirdInsideName;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public void setUrlLink(String urlLink) {
        this.urlLink = urlLink;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    @Override
    public String toString() {
        return "AdNavigationBarCategoryThirdVO{" +
                "id=" + id +
                ", subsetId=" + subsetId +
                ", thirdInsideName='" + thirdInsideName + '\'' +
                ", commodityId=" + commodityId +
                ", urlLink='" + urlLink + '\'' +
                ", enabled=" + enabled +
                ", imgUrl='" + imgUrl + '\'' +
                ", orderIndex=" + orderIndex +
                '}';
    }
}
