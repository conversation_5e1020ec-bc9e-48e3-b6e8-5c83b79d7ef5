package com.insta360.store.admin.common;

import com.insta360.compass.core.web.api.ApiContext;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: mowi
 * @Date: 2018/9/20
 * @Description: 请求上下文，每个请求线程生成一个，记录用户账号和分销账户信息
 */
@Scope("prototype")
@Component
public class WebApiContext extends ApiContext {

    private String adminJobNumber;

    public String getAdminJobNumber() {
        return adminJobNumber;
    }

    public void setAdminJobNumber(String adminJobNumber) {
        this.adminJobNumber = adminJobNumber;
    }
}
