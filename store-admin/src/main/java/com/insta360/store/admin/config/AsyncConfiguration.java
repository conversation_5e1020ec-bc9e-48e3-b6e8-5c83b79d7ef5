package com.insta360.store.admin.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;

import java.util.concurrent.Executor;

/**
 * @Author: mowi
 * @Date: 2019-04-02
 * @Description: 异步调用线程池
 */
@Configuration
public class AsyncConfiguration implements AsyncConfigurer {

    @Override
    public Executor getAsyncExecutor() {

//        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
//        taskExecutor.setCorePoolSize(0);
//        taskExecutor.setMaxPoolSize(2147483647);
//        taskExecutor.setKeepAliveSeconds(60);
//        taskExecutor.setRejectedExecutionHandler(new MyThreadPoolPolicy());
//        taskExecutor.initialize();

//        return taskExecutor;

        return MyThreadPoolExecutor.newCachedThreadPool("Async线程池");
    }
}
