package com.insta360.store.admin.controller.test.tool.oms.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.enums.CommodityTypeEnum;
import com.insta360.store.business.commodity.model.ProductCommodityType;
import com.insta360.store.business.commodity.service.ProductCommodityTypeService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/5/15
 */
@Component
public class CommoditySkuCodeHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommoditySkuCodeHelper.class);

    @Autowired
    ProductCommodityTypeService productCommodityTypeService;

    @Resource(name = "searchSyncExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    CommoditySkuCodeBatchHelper commoditySkuCodeBatchHelper;

    /**
     * 组合商品料号初始化
     *
     * @param type
     * @throws InterruptedException
     */
    public void bundleSkuCodeInit(Integer type) throws InterruptedException {
        // 查询出所有组合商品类型的套餐
        List<ProductCommodityType> productCommodityTypeList = productCommodityTypeService.listByType(type);
        List<Integer> commodityIdList = productCommodityTypeList.stream().map(ProductCommodityType::getCommodityId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(commodityIdList)) {
            LOGGER.info("[料号初始化]没有找到对应套餐信息");
            throw new InstaException(-1, "没有找到对应套餐信息");
        }

        // 组合商品类型
        CommodityTypeEnum commodityType = CommodityTypeEnum.parse(type);
        // 切割组合商品列表便于多线程处理
        List<List<Integer>> commodityPartitionList = Lists.partition(commodityIdList, 10);
        // 设置线程等待锁
        CountDownLatch countDownLatch = new CountDownLatch(commodityPartitionList.size());


        for (List<Integer> commodityIds : commodityPartitionList) {
            threadPoolTaskExecutor.execute(() -> {
                try {
                    LOGGER.info("[料号初始化]任务线程名称:{},套餐类型:{} 料号初始化开始... commodityIds:{}", Thread.currentThread().getName(), commodityType.getDesc(), JSON.toJSONString(commodityIds));
                    if (CommodityTypeEnum.BUNDLE.equals(commodityType)) {
                        // 初始化组合商品料号
                        commoditySkuCodeBatchHelper.batchInitBundleSkuCode(commodityIds);
                    } else if (CommodityTypeEnum.SINGLE.equals(commodityType)) {
                        // 初始化单商品料号
                        commoditySkuCodeBatchHelper.batchInitSingleSkuCode(commodityIds);
                    }
                    LOGGER.info("[料号初始化]任务线程名称:{},套餐类型:{} 料号初始化结束... commodityIds:{}", Thread.currentThread().getName(), commodityType.getDesc(), JSON.toJSONString(commodityIds));
                } catch (Exception e) {
                    LOGGER.error(String.format("任务[%s] 执行失败,commodityIds: [%s]", Thread.currentThread().getName(), JSON.toJSONString(commodityIds)), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
    }
}
