package com.insta360.store.admin.controller.meta.format;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.admin.controller.meta.vo.sceneryTag.AdSceneryTagInfoVO;
import com.insta360.store.admin.controller.meta.vo.sceneryTag.AdSceneryTagProductVO;
import com.insta360.store.admin.controller.meta.vo.sceneryTag.AdSceneryTagVO;
import com.insta360.store.business.meta.exception.MetaErrorCode;
import com.insta360.store.business.meta.model.SceneryTagInfo;
import com.insta360.store.business.meta.model.SceneryTagMain;
import com.insta360.store.business.meta.model.SceneryTagProduct;
import com.insta360.store.business.meta.service.SceneryTagInfoService;
import com.insta360.store.business.meta.service.SceneryTagMainService;
import com.insta360.store.business.meta.service.SceneryTagProductService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2024-12-18 19:00
 */
@Component
public class AdSceneryTagPack {

    @Autowired
    SceneryTagMainService sceneryTagMainService;

    @Autowired
    SceneryTagProductService sceneryTagProductService;

    @Autowired
    SceneryTagInfoService sceneryTagInfoService;

    /**
     * 查询场景标签
     *
     * @param productId
     */
    public List<AdSceneryTagVO> listSceneryTags(Integer productId) {
        if (Objects.nonNull(productId)) {
            return listProductSceneryTags(productId);
        }

        // 查询场景标签主数据
        List<SceneryTagMain> sceneryTagMains = sceneryTagMainService.listAll();
        List<Integer> sceneryMainIds = sceneryTagMains.stream().map(SceneryTagMain::getId).collect(Collectors.toList());

        // 查询关联产品
        List<SceneryTagProduct> sceneryTagProductList = sceneryTagProductService.listByTagIds(sceneryMainIds);
        Map<Integer, List<SceneryTagProduct>> sceneryTagProductMap = sceneryTagProductList.stream().collect(Collectors.groupingBy(SceneryTagProduct::getSceneryTagId));

        // 数据封装
        List<AdSceneryTagVO> sceneryTagVos = sceneryTagMains.stream().map(AdSceneryTagVO::new).collect(Collectors.toList());
        return buildSceneryTagVoList(sceneryTagVos, sceneryTagProductMap);
    }

    /**
     * 通过产品id查询关联产品
     *
     * @param productId
     * @return
     */
    private List<AdSceneryTagVO> listProductSceneryTags(Integer productId) {
        // 查询关联产品
        List<SceneryTagProduct> sceneryTagProductList = sceneryTagProductService.listByProductId(productId);
        List<Integer> sceneryTagMainIds = sceneryTagProductList.stream().map(SceneryTagProduct::getSceneryTagId).distinct().collect(Collectors.toList());

        // 查询场景标签主数据
        List<SceneryTagMain> sceneryTagMains = sceneryTagMainService.listByMainIds(sceneryTagMainIds);

        // 查询场景标签下全部的关联产品
        List<Integer> sceneryMainIds = sceneryTagMains.stream().map(SceneryTagMain::getId).collect(Collectors.toList());
        List<SceneryTagProduct> sceneryTagProducts = sceneryTagProductService.listByTagIds(sceneryMainIds);
        Map<Integer, List<SceneryTagProduct>> sceneryTagProductMap = sceneryTagProducts.stream().collect(Collectors.groupingBy(SceneryTagProduct::getSceneryTagId));

        // 数据封装
        List<AdSceneryTagVO> sceneryTagVos = sceneryTagMains.stream().map(AdSceneryTagVO::new).collect(Collectors.toList());
        return buildSceneryTagVoList(sceneryTagVos, sceneryTagProductMap);
    }

    /**
     * 数据封装
     *
     * @param sceneryTagVos
     * @param sceneryTagProductMap
     * @return
     */
    private List<AdSceneryTagVO> buildSceneryTagVoList(List<AdSceneryTagVO> sceneryTagVos, Map<Integer, List<SceneryTagProduct>> sceneryTagProductMap) {
        return sceneryTagVos.stream().peek(sceneryTag -> {
            List<SceneryTagProduct> tagProducts = sceneryTagProductMap.get(sceneryTag.getId());
            sceneryTag.setProductNumber(CollectionUtils.isEmpty(tagProducts) ? 0 : tagProducts.size());
        }).collect(Collectors.toList());
    }

    /**
     * 查询场景标签多语言文案
     *
     * @param sceneryTagId
     * @return
     */
    public AdSceneryTagVO listSceneryTagInfos(Integer sceneryTagId) {
        SceneryTagMain sceneryTagMain = sceneryTagMainService.getById(sceneryTagId);
        if (Objects.isNull(sceneryTagMain)) {
            throw new InstaException(MetaErrorCode.SceneryTagMainNotFoundException);
        }

        // 查询多语言信息
        List<SceneryTagInfo> sceneryTagInfos = sceneryTagInfoService.listByTagId(sceneryTagId);
        List<AdSceneryTagInfoVO> sceneryTagInfoVos = sceneryTagInfos.stream().map(AdSceneryTagInfoVO::new).collect(Collectors.toList());

        AdSceneryTagVO sceneryTagVo = new AdSceneryTagVO(sceneryTagMain);
        sceneryTagVo.setSceneryTagInfoList(sceneryTagInfoVos);
        return sceneryTagVo;
    }

    /**
     * 查询场景标签的关联产品
     *
     * @param sceneryTagId
     * @return
     */
    public List<AdSceneryTagProductVO> listProduct(Integer sceneryTagId) {
        SceneryTagMain sceneryTagMain = sceneryTagMainService.getById(sceneryTagId);
        if (Objects.isNull(sceneryTagMain)) {
            throw new InstaException(MetaErrorCode.SceneryTagMainNotFoundException);
        }

        List<SceneryTagProduct> sceneryTagProducts = sceneryTagProductService.listByTagId(sceneryTagId);
        return sceneryTagProducts.stream().map(AdSceneryTagProductVO::new).collect(Collectors.toList());
    }
}
