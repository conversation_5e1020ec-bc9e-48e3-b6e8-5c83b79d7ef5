package com.insta360.store.admin.common;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.api.ResponseCode;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.outgoing.rpc.user.admin.OaUserAdmin;
import com.insta360.store.business.outgoing.rpc.user.dto.OaUser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: hyc
 * @Date: 2019/3/28
 * @Description:
 */
public class BaseAdminApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseAdminApi.class);

    @Autowired
    OaUserAdmin oaUserAdmin;

    /**
     * 获取运营后台操作人
     * <p>
     * 优先获取姓名，如果获取不到其次获取工号
     * </p>
     *
     * @return
     */
    public String getOperator() {
        return StringUtils.defaultIfBlank(getOaUserInfo().getName(), getAdminJobNumber());
    }

    /**
     * 获取运营后台账户Token
     *
     * @return
     */
    public String getAdminUserToken() {
        return storeApiHeaderParser.parseHeaderValue(request, StoreApiHeader.AdminUserToken);
    }

    @Override
    public InstaLanguage getApiLanguage() {
        InstaLanguage parseLanguage = this.apiHeaderParser.parseLanguage(this.request);
        return parseLanguage != null ? parseLanguage : InstaLanguage.zh_CN;
    }

    /**
     * 获取OaUser信息
     *
     * @return
     */
    public OaUser getOaUserInfo() {
        String adminToken = getAdminUserToken();
        LOGGER.info("OaUser操作人获取...adminToken:" + adminToken);
        try {
            Response<OaUser> oaUserResponse = oaUserAdmin.getByUserToken(adminToken);
            OaUser oaUser = oaUserResponse.getData();
            if (ResponseCode.FAIL.equals(oaUserResponse.getCode()) || oaUser == null) {
                return new OaUser();
            }
            return oaUser;
        } catch (Exception e) {
            LOGGER.error(String.format("OaUser操作人获取失败。admin_token：%s", adminToken), e);
            return new OaUser();
        }
    }

    /**
     * 获取缓存更新关键参数
     *
     * @return
     */
    public CachePutKeyParameterBO getCachePutKeyParameter() {
        return new CachePutKeyParameterBO();
    }
}
