package com.insta360.store.admin.rpc.bi;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2023/07/11
 * @Description:
 */
@RestController
public class BiOrderApi {

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    /**
     * 订单货币
     *
     * @param orderNumber
     * @return
     */
    @GetMapping("/rpc/store/admin/bi/getOrderCurrency")
    public Response<? extends Map> getOrderCurrency(@RequestParam(required = false, value = "order_number") String orderNumber) {
        // 订单信息
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            return Response.ok();
        }

        // 订单支付信息
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (orderPayment == null) {
            return Response.ok();
        }

        // 订单货币
        return Response.ok("currency", orderPayment.currency());
    }

}
