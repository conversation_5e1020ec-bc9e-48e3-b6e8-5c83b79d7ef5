package com.insta360.store.admin.controller.email.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.business.order.dto.RealEstateEmailDTO;
import com.insta360.store.business.order.email.RealEstateSoftwareEmail;
import com.insta360.store.business.order.email.RealEstateSoftwareEmailFactory;
import com.insta360.store.business.order.email.RealEstateSoftwareEmailIStaging;
import com.insta360.store.business.order.enums.RealEstateSoftwareName;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.RealEstateSoftwareSubscribe;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.RealEstateSoftwareSubscribeService;
import com.insta360.store.business.order.service.impl.helper.RealEstateSoftwareCodeHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: hyc
 * @Date: 2020-01-11
 * @Description:
 */
@RestController
@PermissionResource(code = "realEstateEmailSend", desc = "邮件-房地产邮件发送配置")
public class AdRealEstateEmailSendApi extends BaseAdminApi {

    @Autowired
    OrderService orderService;

    @Autowired
    RealEstateSoftwareEmailFactory emailFactory;

    @Autowired
    RealEstateSoftwareSubscribeService subscribeService;

    @Autowired
    RealEstateSoftwareCodeHelper softwareCodeHelper;

    /**
     * 房地产邮件发送
     *
     * @param realEstateEmailParam
     * @return
     */
    @LogAttr(desc = "房地产邮件发送")
    @Permission(code = "store.email.realEstateEmailSend.sendEmail", desc = "房地产邮件发送")
    @PostMapping(path = "/admin/email/realEstateSoftware/send", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> sendEmail(@RequestBody RealEstateEmailDTO realEstateEmailParam) {

        String orderNumber = realEstateEmailParam.getOrderNumber();
        String address = realEstateEmailParam.getAddress();
        RealEstateSoftwareName softwareName = RealEstateSoftwareName.parse(realEstateEmailParam.getSoftware());
        if (softwareName == null) {
            return Response.failed("不存在的软件");
        }
        RealEstateSoftwareSubscribe subscribe = subscribeService.subscribeSoftware(orderNumber, softwareName);

        Order order = orderService.getByOrderNumber(orderNumber);
        RealEstateSoftwareEmail email = emailFactory.getEmail(order, softwareName);

        String toAddress = StringUtil.isNotBlank(address) ? address : order.getContactEmail();

        if (RealEstateSoftwareName.istaging.equals(softwareName)) {
            String softwareCode = softwareCodeHelper.getCode(subscribe, softwareName);

            if (StringUtil.isNotBlank(softwareCode)) {
                RealEstateSoftwareEmailIStaging emailIStaging = (RealEstateSoftwareEmailIStaging) email;
                ((RealEstateSoftwareEmailIStaging) email).setSoftwareCode(softwareCode);
                emailIStaging.doSend(toAddress);
            } else {
                return Response.failed("房产软件istaging软件码不足," + order.country().name());
            }
        } else {
            email.doSend(toAddress);
        }

        return Response.ok();
    }
}
