package com.insta360.store.admin.controller.faq.controller;

import com.insta360.compass.admin.audit.log.annotations.LogAttr;
import com.insta360.compass.admin.audit.permission.annotations.Permission;
import com.insta360.compass.admin.audit.permission.annotations.PermissionResource;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.admin.common.BaseAdminApi;
import com.insta360.store.admin.controller.faq.cache.AdFaqCategoryCachePack;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.faq.dto.CategoryQuestionInsideDTO;
import com.insta360.store.business.faq.exception.FaqErrorCode;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInside;
import com.insta360.store.business.faq.service.FaqCategoryQuestionInsideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2022/4/19
 * @Description:
 */
@PermissionResource(code = "faqCategoryQuestionInside", desc = "Faq问题内部分类")
@RestController
public class AdFaqCategoryQuestionInsideApi extends BaseAdminApi {

    @Autowired
    FaqCategoryQuestionInsideService questionInsideService;

    @Autowired
    AdFaqCategoryCachePack adFaqCategoryCachePack;

    /**
     * 创建问题内部名称
     *
     * @param questionInsideParam
     * @return
     */
    @LogAttr(desc = "创建问题内部名称")
    @Permission(code = "store.faq.category.createCategoryQuestionInside", desc = "创建问题内部名称")
    @PostMapping(path = "/admin/faq/create/categoryQuestionInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> createCategoryQuestionInside(@Validated @RequestBody CategoryQuestionInsideDTO questionInsideParam) {
        if (StringUtil.isBlank(questionInsideParam.getCategoryQuestionInsideName())) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        FaqCategoryQuestionInside questionInside = questionInsideParam.getPojoObject();
        questionInsideService.createQuestionInside(questionInside);
        return Response.ok(questionInside.getId());
    }

    /**
     * 更新问题内部名称
     *
     * @param questionInsideParam
     * @return
     */
    @LogAttr(desc = "更新问题内部名称")
    @Permission(code = "store.faq.category.updateCategoryQuestionInside", desc = "更新问题内部名称")
    @PostMapping(path = "/admin/faq/update/categoryQuestionInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> updateCategoryQuestionInside(@RequestBody CategoryQuestionInsideDTO questionInsideParam) {
        if (questionInsideParam.getId() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        questionInsideService.updateQuestionInside(questionInsideParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 启用问题
     *
     * @param questionInsideParam
     * @return
     */
    @LogAttr(desc = "启用问题")
    @Permission(code = "store.faq.category.enableCategoryQuestionInside", desc = "启用问题")
    @PostMapping(path = "/admin/faq/enable/categoryQuestionInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> enableCategoryQuestionInside(@RequestBody CategoryQuestionInsideDTO questionInsideParam) {
        FaqCategoryQuestionInside categoryQuestionInside = questionInsideService.getById(questionInsideParam.getId());
        if (categoryQuestionInside == null) {
            throw new InstaException(FaqErrorCode.FaqQuestionInsideNotFoundException);
        }
        adFaqCategoryCachePack.enableQuestionInside(categoryQuestionInside);
        return Response.ok();
    }

    /**
     * 禁用问题
     *
     * @param questionInsideParam
     * @return
     */
    @LogAttr(desc = "禁用问题")
    @Permission(code = "store.faq.category.disableCategoryQuestionInside", desc = "禁用问题")
    @PostMapping(path = "/admin/faq/disable/categoryQuestionInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> disableCategoryQuestionInside(@RequestBody CategoryQuestionInsideDTO questionInsideParam) {
        FaqCategoryQuestionInside categoryQuestionInside = questionInsideService.getById(questionInsideParam.getId());
        if (categoryQuestionInside == null) {
            throw new InstaException(FaqErrorCode.FaqQuestionInsideNotFoundException);
        }
        adFaqCategoryCachePack.disableQuestionInside(categoryQuestionInside);
        return Response.ok();
    }

    /**
     * 删除问题
     *
     * @param questionInsideParam
     * @return
     */
    @LogAttr(desc = "删除问题")
    @Permission(code = "store.faq.category.deleteCategoryQuestionInside", desc = "删除问题")
    @PostMapping(path = "/admin/faq/delete/categoryQuestionInside", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> deleteCategoryQuestionInside(@RequestBody CategoryQuestionInsideDTO questionInsideParam) {
        FaqCategoryQuestionInside categoryQuestionInside = questionInsideService.getById(questionInsideParam.getId());
        if (categoryQuestionInside == null) {
            throw new InstaException(FaqErrorCode.FaqQuestionInsideNotFoundException);
        }
        adFaqCategoryCachePack.deleteQuestionInside(categoryQuestionInside.getId());
        return Response.ok();
    }
}
