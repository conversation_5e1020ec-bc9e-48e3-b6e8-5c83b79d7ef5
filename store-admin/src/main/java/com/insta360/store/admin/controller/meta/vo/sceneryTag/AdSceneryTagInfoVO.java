package com.insta360.store.admin.controller.meta.vo.sceneryTag;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.SceneryTagInfo;

import java.io.Serializable;

/**
 * @description:
 * @author: py
 * @create: 2024-12-19 12:16
 */
public class AdSceneryTagInfoVO implements Serializable {

    /**
     * 场景标签ID
     */
    private Integer id;

    /**
     * 多语言名称
     */
    private String name;

    /**
     * 语言
     */
    private String language;

    public AdSceneryTagInfoVO(SceneryTagInfo sceneryTagInfo) {
        if (sceneryTagInfo != null) {
            BeanUtil.copyProperties(sceneryTagInfo, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return "AdSceneryTagInfoVO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", language='" + language + '\'' +
                '}';
    }
}
