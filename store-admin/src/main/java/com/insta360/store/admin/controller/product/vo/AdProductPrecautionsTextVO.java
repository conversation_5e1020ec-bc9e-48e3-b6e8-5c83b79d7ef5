package com.insta360.store.admin.controller.product.vo;

import com.insta360.store.business.product.model.ProductPrecautionsText;
import com.insta360.store.business.product.model.ProductPrecautionsTextInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 多语言产品注意事项VO
 * @Date 2022/9/2
 */
public class AdProductPrecautionsTextVO implements Serializable {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 产品ID
     *
     */
    private Integer productId;

    /**
     * 样式类型
     * @see com.insta360.store.business.product.enums.ProductPackingListType
     */
    private Integer styleType;

    /**
     * 内部名称
     */
    private String nameZh;

    /**
     * 多语言文本明细
     */
    private List<PrecautionsLanguageInfo> languageInfoList;


    public AdProductPrecautionsTextVO() {
    }

    public AdProductPrecautionsTextVO(ProductPrecautionsText productPrecautionsText, List<ProductPrecautionsTextInfo> productPrecautionsTextInfoList) {
        if(Objects.nonNull(productPrecautionsText)) {
            BeanUtils.copyProperties(productPrecautionsText,this);
        }
        if(CollectionUtils.isNotEmpty(productPrecautionsTextInfoList)) {
            List<PrecautionsLanguageInfo> languageInfoList = productPrecautionsTextInfoList.stream().map(productPrecautionsTextInfo -> {
                PrecautionsLanguageInfo precautionsLanguageInfo = new PrecautionsLanguageInfo();
                BeanUtils.copyProperties(productPrecautionsTextInfo, precautionsLanguageInfo);
                return precautionsLanguageInfo;
            }).collect(Collectors.toList());
            this.setLanguageInfoList(languageInfoList);
        }

    }

    public static class PrecautionsLanguageInfo implements Serializable {
        /**
         * 内容文本
         */
        private String contentText;

        /**
         * 多语言
         */
        private String language;

        public String getContentText() {
            return contentText;
        }

        public void setContentText(String contentText) {
            this.contentText = contentText;
        }

        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStyleType() {
        return styleType;
    }

    public void setStyleType(Integer styleType) {
        this.styleType = styleType;
    }

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    public List<PrecautionsLanguageInfo> getLanguageInfoList() {
        return languageInfoList;
    }

    public void setLanguageInfoList(List<PrecautionsLanguageInfo> languageInfoList) {
        this.languageInfoList = languageInfoList;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

}
