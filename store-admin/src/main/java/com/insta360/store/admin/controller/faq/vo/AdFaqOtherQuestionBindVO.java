package com.insta360.store.admin.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqOtherQuestionBind;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/4/29
 * @Description:
 */
public class AdFaqOtherQuestionBindVO implements Serializable {

    private Integer id;

    /**
     * 页面内部名称
     */
    private String pageInsideName;

    /**
     * 页面类型
     */
    private String type;

    /**
     * 产品页是产品页url_key,邮件是模板key，普通页面协商
     */
    private String pageKey;

    /**
     * 是否启用（0：默认不启用，1：启用）
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public AdFaqOtherQuestionBindVO(FaqOtherQuestionBind questionBind) {
        if (questionBind != null) {
            BeanUtil.copyProperties(questionBind, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPageInsideName() {
        return pageInsideName;
    }

    public void setPageInsideName(String pageInsideName) {
        this.pageInsideName = pageInsideName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPageKey() {
        return pageKey;
    }

    public void setPageKey(String pageKey) {
        this.pageKey = pageKey;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
