package com.insta360.store.admin.controller.meta.vo.tradepoint;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.TradePointMain;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/08/31
 * @Description:
 */
public class AdTradePointMainVO {

    private Integer id;

    /**
     * 交易点的名称
     */
    private String tradePointName;

    /**
     * 图片链接
     */
    private String imageUrl;

    /**
     * 展示顺序
     */
    private Integer orderIndex;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否禁用（0：未禁用；1：禁用）
     */
    private Boolean disabled;

    /**
     * 是否删除（0：未删除；1：删除）
     */
    private Boolean deleted;

    public AdTradePointMainVO() {
    }

    public AdTradePointMainVO(TradePointMain tradePointMain) {
        if (tradePointMain != null) {
            BeanUtil.copyProperties(tradePointMain, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTradePointName() {
        return tradePointName;
    }

    public void setTradePointName(String tradePointName) {
        this.tradePointName = tradePointName;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        return "AdTradePointMainVO{" +
                "id=" + id +
                ", tradePointName='" + tradePointName + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", orderIndex=" + orderIndex +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", disabled=" + disabled +
                ", deleted=" + deleted +
                '}';
    }
}
