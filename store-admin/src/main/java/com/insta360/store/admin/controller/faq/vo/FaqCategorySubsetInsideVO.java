package com.insta360.store.admin.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqCategorySubsetInside;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/4/22
 * @Description:
 */
public class FaqCategorySubsetInsideVO implements Serializable {

    private Integer id;

    /**
     * 一级类目分类id
     */
    private Integer categoryInsideId;

    /**
     * 二级内部分类名称
     */
    private String categorySubsetInsideName;

    /**
     * 二级内部分类key
     */
    private String categorySubsetInsideKey;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 封装问题列表
     */
    List<FaqCategoryQuestionInsideVO> categoryQuestionInsides;

    public FaqCategorySubsetInsideVO() {
    }

    public FaqCategorySubsetInsideVO(FaqCategorySubsetInside categorySubsetInside) {
        if (categorySubsetInside != null) {
            BeanUtil.copyProperties(categorySubsetInside, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public String getCategorySubsetInsideName() {
        return categorySubsetInsideName;
    }

    public void setCategorySubsetInsideName(String categorySubsetInsideName) {
        this.categorySubsetInsideName = categorySubsetInsideName;
    }

    public String getCategorySubsetInsideKey() {
        return categorySubsetInsideKey;
    }

    public void setCategorySubsetInsideKey(String categorySubsetInsideKey) {
        this.categorySubsetInsideKey = categorySubsetInsideKey;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public List<FaqCategoryQuestionInsideVO> getCategoryQuestionInsides() {
        return categoryQuestionInsides;
    }

    public void setCategoryQuestionInsides(List<FaqCategoryQuestionInsideVO> categoryQuestionInsides) {
        this.categoryQuestionInsides = categoryQuestionInsides;
    }
}
