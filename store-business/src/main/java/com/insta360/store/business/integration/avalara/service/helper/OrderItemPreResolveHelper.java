package com.insta360.store.business.integration.avalara.service.helper;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.bo.ServiceItem;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.order.bo.OrderSheetBO;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.OrderItem;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description Avalara 商品分解器
 * @Date 2023/6/19
 */
@Component
public class OrderItemPreResolveHelper {

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    /**
     * 预购买商品列表解析
     *
     * @param sheetItems
     * @param country
     * @return
     */
    public List<OrderItem> orderSheetItemsResolve(List<OrderSheet.SheetItem> sheetItems, InstaCountry country) {
        // 下单商品集合
        if (CollectionUtils.isEmpty(sheetItems)) {
            throw new InstaException(OrderErrorCode.OrderInfoMissingException);
        }

        // 获取所有的商品项（解析绑定服务）
        List<OrderSheetBO> allSheetItems = this.listAllSheetItems(sheetItems);

        // 下单套餐ID集合
        List<Integer> commodityIds = allSheetItems.stream()
                .map(OrderSheetBO::getCommodityId)
                .collect(Collectors.toList());

        // 下单套餐价格Map
        Map<Integer, CommodityPrice> commodityPriceMap = commodityBatchHelper.priceMapCommodityIds(commodityIds, country);

        // 下单套餐集合
        Map<Integer, Commodity> commodityMap = commodityBatchHelper.commodityAllMapCommodityIds(commodityIds);

        // 解析商品项
        return this.parseOrderItem(allSheetItems, commodityPriceMap, commodityMap);
    }

    /**
     * 获取所有的商品项（增值服务不计税）
     *
     * @param sheetItems
     * @return
     */
    private List<OrderSheetBO> listAllSheetItems(List<OrderSheet.SheetItem> sheetItems) {
        List<OrderSheetBO> allSheetItems = new ArrayList<>();
        for (OrderSheet.SheetItem sheetItem : sheetItems) {
            // 当前商品
            OrderSheetBO orderSheetBo = new OrderSheetBO();
            orderSheetBo.setBindService(false);
            orderSheetBo.setBuyNum(sheetItem.getNumber());
            orderSheetBo.setCommodityId(sheetItem.getCommodityId());
            orderSheetBo.setBelongToCommodityId(sheetItem.getCommodityId());
            allSheetItems.add(orderSheetBo);

            List<ServiceItem> bindServices = sheetItem.getBindServices();
            if (CollectionUtils.isEmpty(bindServices)) {
                continue;
            }

            // 构造为一个sheetItem
            List<OrderSheetBO> bindServiceSheetItems = bindServices.stream()
                    .map(bindService -> {
                        OrderSheetBO bindServiceSheetItem = new OrderSheetBO();
                        bindServiceSheetItem.setBindService(true);
                        bindServiceSheetItem.setBuyNum(bindService.getNumber());
                        bindServiceSheetItem.setCommodityId(bindService.getId());
                        bindServiceSheetItem.setBelongToCommodityId(sheetItem.getCommodityId());
                        return bindServiceSheetItem;
                    })
                    .collect(Collectors.toList());

            // 将保险服务与普通商品项合并
            allSheetItems.addAll(bindServiceSheetItems);
        }
        return allSheetItems;
    }

    /**
     * 解析商品项
     *
     * @param sheetItems
     * @param commodityPriceMap
     * @param commodityMap
     * @return
     */
    private List<OrderItem> parseOrderItem(List<OrderSheetBO> sheetItems, Map<Integer, CommodityPrice> commodityPriceMap, Map<Integer, Commodity> commodityMap) {
        return sheetItems
                .stream()
                .map(sheetItem -> {
                    // 获取套餐信息
                    Commodity commodity = commodityMap.get(sheetItem.getCommodityId());

                    // 获取套餐价格信息
                    CommodityPrice commodityPrice = commodityPriceMap.get(commodity.getId());

                    if (commodityPrice == null) {
                        throw new InstaException(OrderErrorCode.OrderInfoMissingException);
                    }

                    OrderItem orderItem = new OrderItem();
                    orderItem.setProduct(commodity.getProduct());
                    orderItem.setCommodity(commodity.getId());
                    orderItem.setNumber(sheetItem.getBuyNum());
                    orderItem.setPrice(commodityPrice.getAmount());
                    orderItem.setOriginAmount(commodityPrice.getOriginAmount());
                    orderItem.setCurrency(commodityPrice.getCurrency());
                    orderItem.setItemCommodity(commodity);
                    orderItem.setIsGift(false);
                    orderItem.setBelongToCommodityId(sheetItem.getBelongToCommodityId());
                    return orderItem;
                })
                .collect(Collectors.toList());
    }
}
