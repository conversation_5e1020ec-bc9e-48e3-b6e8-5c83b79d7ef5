package com.insta360.store.business.utils;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.exception.CommonErrorCode;

import javax.net.ssl.*;
import javax.xml.bind.DatatypeConverter;
import java.io.ByteArrayInputStream;
import java.security.KeyFactory;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Arrays;

/**
 * @Author: wkx
 * @Date: 2023/12/18
 * @Description:
 */
public class SSLUtil {

    /**
     * 获取ssl工厂
     *
     * @param pem
     * @param pKey
     * @return
     * @throws Exception
     */
    public static SSLSocketFactory getSocketFactoryPEM(String pem, String pKey) throws Exception {
        byte[] certBytes = parseDERFromPEM(pem);
        byte[] keyBytes = parseDERFromPEM(pKey);

        X509Certificate cert = generateCertificateFromDER(certBytes);
        RSAPrivateKey key = generatePrivateKeyFromDER(keyBytes);

        KeyStore keystore = KeyStore.getInstance("JKS");
        keystore.load(null);
        keystore.setCertificateEntry("cert-alias", cert);
        keystore.setKeyEntry("key-alias", key, "insta360".toCharArray(), new Certificate[]{cert});

        KeyManagerFactory kmf = KeyManagerFactory.getInstance("SunX509");
        kmf.init(keystore, "insta360".toCharArray());

        KeyManager[] km = kmf.getKeyManagers();

        SSLContext context = SSLContext.getInstance("TLS");
        context.init(km, null, null);

        return context.getSocketFactory();
    }

    /**
     * 解析64编码
     *
     * @param key
     * @return
     */
    public static byte[] parseDERFromPEM(String key) {
        return DatatypeConverter.parseBase64Binary(key);
    }

    /**
     * 生成 RSAPrivateKey
     *
     * @param keyBytes
     * @return
     * @throws InvalidKeySpecException
     * @throws NoSuchAlgorithmException
     */
    public static RSAPrivateKey generatePrivateKeyFromDER(byte[] keyBytes) throws InvalidKeySpecException, NoSuchAlgorithmException {
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory factory = KeyFactory.getInstance("RSA");
        return (RSAPrivateKey) factory.generatePrivate(spec);
    }

    /**
     * 生成 X509Certificate
     *
     * @param certBytes
     * @return
     * @throws CertificateException
     */
    public static X509Certificate generateCertificateFromDER(byte[] certBytes) throws CertificateException {
        CertificateFactory factory = CertificateFactory.getInstance("X.509");
        return (X509Certificate) factory.generateCertificate(new ByteArrayInputStream(certBytes));
    }

    /**
     * 获取X509TrustManager
     *
     * @return
     */
    public static X509TrustManager getX509TrustManager() throws KeyStoreException, NoSuchAlgorithmException {
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustManagerFactory.init((KeyStore) null);
        TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
        if (trustManagers.length != 1 || !(trustManagers[0] instanceof X509TrustManager)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "Unexpected default trust managers:" + Arrays.toString(trustManagers));
        }
        return (X509TrustManager) trustManagers[0];
    }
}
