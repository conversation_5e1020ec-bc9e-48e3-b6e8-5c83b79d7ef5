package com.insta360.store.business.payment.lib.paypal;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wbt
 * @Date: 2024/05/24
 * @Description:
 */
@Configuration
public class PayPalHkYsConfiguration implements PaypalConfig {

    @Value("${payment.paypal.hk_ys.client_id}")
    protected String clientId;

    @Value("${payment.paypal.hk_ys.client_secret}")
    protected String clientSecret;

    @Value("${payment.paypal.request_url}")
    protected String requestUrl;

    @Value("${payment.paypal.hk_ys.webhook_id}")
    protected String webhookId;

    @Value("${payment.paypal.subscribe.vault_approve_url}")
    protected String vaultApproveUrl;

    @Value("${payment.paypal.subscribe.approve_callback_url}")
    protected String approveCallbackUrl;

    @Override
    public String getClientId() {
        return clientId;
    }

    @Override
    public String getClientSecret() {
        return clientSecret;
    }

    @Override
    public String getRequestUrl() {
        return requestUrl;
    }

    @Override
    public String getWebhookId() {
        return webhookId;
    }

    @Override
    public String getVaultApproveUrl() {
        return vaultApproveUrl;
    }

    @Override
    public String getApproveCallbackUrl() {
        return approveCallbackUrl;
    }
}
