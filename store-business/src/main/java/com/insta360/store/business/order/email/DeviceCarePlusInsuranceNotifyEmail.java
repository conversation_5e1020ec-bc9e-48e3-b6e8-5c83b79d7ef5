package com.insta360.store.business.order.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: py
 * @Date: 2024/2/24
 * @Description: 订单支付成功包含相机且不包含Care+服务发送Care+促销邮件
 */
@Scope("prototype")
@Component
public class DeviceCarePlusInsuranceNotifyEmail extends BaseOrderEmail {

    @Override
    public String getTemplateName() {
        return "device_careplus_notify_order_payed";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        // buyNow跳转到care+对应的产品页
        templateParams.addBodyParam("insurance_url", this.getInsuranceUrl());
    }

    @Override
    protected InstaLanguage getLanguage() {
        if (InstaCountry.HK.equals(getOrder().country())) {
            return InstaLanguage.zh_TW;
        }

        return super.getLanguage();
    }
}
