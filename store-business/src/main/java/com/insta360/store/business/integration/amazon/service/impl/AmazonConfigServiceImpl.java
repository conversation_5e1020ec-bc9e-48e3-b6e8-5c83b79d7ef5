package com.insta360.store.business.integration.amazon.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.integration.amazon.dao.AmazonConfigDao;
import com.insta360.store.business.integration.amazon.model.AmazonConfig;
import com.insta360.store.business.integration.amazon.service.AmazonConfigService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-05-09
 * @Description:
 */
@Service
public class AmazonConfigServiceImpl extends BaseServiceImpl<AmazonConfigDao, AmazonConfig> implements AmazonConfigService {

    @Override
    public List<String> getKeys() {
        List<String> keys = new ArrayList<>();
        QueryWrapper<AmazonConfig> qw = new QueryWrapper<>();
        qw.groupBy("`key`");
        baseMapper.selectList(qw)
                .stream()
                .forEach(amazonConfig -> keys.add(amazonConfig.getKey()));
        return keys;
    }

    @Override
    public AmazonConfig getByKey(String key) {
        QueryWrapper<AmazonConfig> qw = new QueryWrapper<>();
        qw.eq("`key`", key);
        return baseMapper.selectOne(qw);
    }
}
