package com.insta360.store.business.meta.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.model.StoreSdkCallRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 接入第三方SDK调用记录service
 * @Date 2021/6/1
 */
public interface StoreSdkCallRecordService extends BaseService<StoreSdkCallRecord> {

    /**
     * 根据订单号，业务类型，API类型查询出对应调用记录信息
     *
     * @param orderNumber
     * @param storeSdkCallBusinessType
     * @param storeSdkCallApiType
     * @return
     */
    List<StoreSdkCallRecord> listSdkCallRecordByType(String orderNumber, StoreSdkCallBusinessType storeSdkCallBusinessType, StoreSdkCallApiType storeSdkCallApiType);

    /**
     * 根据订单号，子订单号，业务类型，API类型查询出对应调用记录信息
     *
     * @param orderNumber
     * @param subNumber
     * @param storeSdkCallBusinessType
     * @param storeSdkCallApiType
     * @return
     */
    List<StoreSdkCallRecord> listSdkCallRecordByType(String orderNumber, String subNumber, StoreSdkCallBusinessType storeSdkCallBusinessType, StoreSdkCallApiType storeSdkCallApiType);

    /**
     * 保存三方调用信息
     *
     * @param orderNumber
     * @param subCode
     * @param requestJsonInfo
     * @param responseJsonInfo
     * @param callBusinessType
     * @param callApiType
     */
    void saveSdkCallRecord(String orderNumber, String subCode, String requestJsonInfo, String responseJsonInfo,
                           StoreSdkCallBusinessType callBusinessType, StoreSdkCallApiType callApiType);
}
