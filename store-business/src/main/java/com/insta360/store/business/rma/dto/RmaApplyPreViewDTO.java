package com.insta360.store.business.rma.dto;

import com.insta360.compass.core.enums.InstaLanguage;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 售后申请视图预览DTO
 * @Date 2022/3/9
 */
public class RmaApplyPreViewDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单商品ID
     */
    private Integer orderItemId;

    /**
     * 语种
     * @see InstaLanguage
     */
    private String language;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}
