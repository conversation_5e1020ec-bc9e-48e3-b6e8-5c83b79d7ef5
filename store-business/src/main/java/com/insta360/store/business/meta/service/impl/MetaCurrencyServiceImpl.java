package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.dao.MetaCurrencyDao;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.model.MetaCurrency;
import com.insta360.store.business.meta.service.MetaCurrencyService;
import com.insta360.store.business.meta.service.impl.helper.CurrencyExchangeRateFetcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/24
 * @Description:
 */
@Service
public class MetaCurrencyServiceImpl extends BaseServiceImpl<MetaCurrencyDao, MetaCurrency> implements MetaCurrencyService {

    @Autowired
    CurrencyExchangeRateFetcher exchangeRateFetcher;

    @Override
    public MetaCurrency getByCurrency(Currency currency) {
        if (currency == null) {
            return null;
        }
        return getById(currency.name());
    }

    @Override
    public Float getRateFromDB(Currency fromCurrency, Currency toCurrency) {

        MetaCurrency fromMetaCurrency = getByCurrency(fromCurrency);
        MetaCurrency toMetaCurrency = getByCurrency(toCurrency);

        if (fromMetaCurrency != null && toMetaCurrency != null) {
            float a = fromMetaCurrency.getCnyRate();
            a = a / toMetaCurrency.getCnyRate();
            return a;
        }
        return null;
    }

    @Override
    public List<MetaCurrency> getCurrencies() {
        QueryWrapper<MetaCurrency> qw = new QueryWrapper<>();
        qw.orderByAsc("id");
        return baseMapper.selectList(qw);
    }

    @Override
    public Price toCurrency(Price price, Currency currency) {
        if (price.getCurrency().equals(currency)) {
            return price;
        }

        float exchangeRate = getRateFromDB(price.getCurrency(), currency);
        float amount = price.getAmount();
        amount = amount * exchangeRate;
        return new Price(currency, amount);
    }

    @Override
    public void syncCurrencyFromInternet(MetaCurrency metaCurrency) {
        Currency currency = metaCurrency.currency();
        Float cny = this.getRateFromInternet(currency, Currency.CNY);
        if (cny != null) {
            metaCurrency.setCnyRate(cny);
        }

        Float usd = this.getRateFromInternet(currency, Currency.USD);
        if (usd != null) {
            metaCurrency.setUsdRate(usd);
        }

        baseMapper.updateById(metaCurrency);
    }

    private Float getRateFromInternet(Currency fromCurrency, Currency toCurrency) {
        if (fromCurrency.equals(toCurrency)) {
            return 1f;
        }

        return exchangeRateFetcher.fetch(fromCurrency, toCurrency);
    }
}
