package com.insta360.store.business.admin.order.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.admin.order.enums.LogisticsRestrictionType;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-10-24
 * @Description: 物流限制
 */
@TableName("logistics_restriction")
public class LogisticsRestriction extends BaseModel<LogisticsRestriction> {

    private static final long serialVersionUID = 1L;

    /**
     * 物流限制ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 限制类型（0-无需限制，1-危险品物流，2-不支持危险品物流）
     */
    private Integer restriction;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 解析国家/地区
     *
     * @return {@link InstaCountry}
     */
    public InstaCountry parseCountry() {
        return InstaCountry.parse(countryCode);
    }

    /**
     * 解析限制
     *
     * @return {@link LogisticsRestrictionType}
     */
    public LogisticsRestrictionType parseRestriction() {
        return LogisticsRestrictionType.fromCode(restriction);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Integer getRestriction() {
        return restriction;
    }

    public void setRestriction(Integer restriction) {
        this.restriction = restriction;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "LogisticsRestriction{" +
                "id=" + id +
                ", countryCode=" + countryCode +
                ", restriction=" + restriction +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}