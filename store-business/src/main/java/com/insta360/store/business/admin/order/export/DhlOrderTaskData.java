package com.insta360.store.business.admin.order.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.insta360.store.business.integration.dhl.lib.enums.ValueAddedServiceCodeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description DHL运单任务批次Excel数据导出模版
 * @Date 2023/3/22
 */
public class DhlOrderTaskData implements Serializable {

    @ExcelProperty("单据编号")
    private String orderNumber;

    @ExcelProperty("物流公司")
    private String expressCompany;

    @ExcelProperty("物流单号")
    private String expressCode;

    @ExcelProperty("会员名称")
    private String memberName;

    @ExcelProperty("承重重量")
    private String bearWeight;

    @ExcelProperty("物流成本")
    private String expressCosts;

    @ExcelProperty("备注")
    private String remark;

    /**
     * 构建DHL运单任务批次Excel数据导出模版
     *
     * @param orderNumber
     * @param expressCode
     * @param valueAddedServiceCode
     * @return
     */
    public static DhlOrderTaskData build(String orderNumber, String expressCode, ValueAddedServiceCodeEnum valueAddedServiceCode) {
        DhlOrderTaskData dhlOrderTaskData = new DhlOrderTaskData();
        dhlOrderTaskData.setOrderNumber(orderNumber);
        dhlOrderTaskData.setExpressCode(expressCode);
        dhlOrderTaskData.setExpressCompany(valueAddedServiceCode.getExpressCompany());
        return dhlOrderTaskData;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getExpressCompany() {
        return expressCompany;
    }

    public void setExpressCompany(String expressCompany) {
        this.expressCompany = expressCompany;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getBearWeight() {
        return bearWeight;
    }

    public void setBearWeight(String bearWeight) {
        this.bearWeight = bearWeight;
    }

    public String getExpressCosts() {
        return expressCosts;
    }

    public void setExpressCosts(String expressCosts) {
        this.expressCosts = expressCosts;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
