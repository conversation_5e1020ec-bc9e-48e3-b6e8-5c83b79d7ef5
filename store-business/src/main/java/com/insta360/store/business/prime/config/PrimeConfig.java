package com.insta360.store.business.prime.config;

import com.insta360.store.business.configuration.graphql.GraphqlConfig;
import com.insta360.store.business.prime.constants.PrimeConstants;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4
 * @description Amazon Prime 服务的配置类
 * 该类定义了与Amazon Prime服务通信所需的配置参数，包括API端点URL、授权令牌、API版本和目标ID
 */
@Configuration
@ConfigurationProperties(prefix = "aws.prime")
public class PrimeConfig {

    /**
     * Amazon Prime GraphQL API 的端点URL
     * 所有的GraphQL查询和变更操作都会发送到这个地址
     */
    private String graphqlUrl = "https://api.buywithprime.amazon.com/graphql";

    /**
     * bwpTokenUrl
     */
    private String bwpTokenUrl = "https://api.buywithprime.amazon.com/token";

    /**
     * 访问Amazon Prime API的授权令牌
     * 用于身份验证和授权，确保API请求来自合法来源
     */
    private String bwpToken = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

    /**
     * Amazon Prime API的版本号
     * 用于确保API调用与特定版本的接口兼容
     */
    private String version = "2024-11-01";

    /**
     * Amazon Prime API的目标ID
     * 指定API请求的目标对象，通常是商户或商店的唯一标识符
     */
    private String targetId = "bp-1e719710-061b-48d0-8f8e-75da204127f4";

    private String clientId = PrimeConstants.Request.DEFAULT_CLIENT_ID;

    private String clientSecret = PrimeConstants.Request.DEFAULT_CLIENT_SECRET;

    private String grantType = PrimeConstants.Request.DEFAULT_GRANT_TYPE;

    /**
     * 创建并配置GraphqlConfig对象的Bean方法
     * 该方法将当前PrimeConfig的参数转换为通用的GraphqlConfig格式
     *
     * @return 配置好的GraphqlConfig对象，可用于发送GraphQL请求
     */
    public GraphqlConfig buildGraphqlConfig() {
        return GraphqlConfig.builder()
                .url(graphqlUrl)
                .authorization(bwpToken)
                .apiVersion(version)
                .targetId(targetId)
                .build();
    }

    /**
     * 获取API端点URL
     *
     * @return GraphQL API的URL
     */
    public String getGraphqlUrl() {
        return graphqlUrl;
    }

    /**
     * 设置API端点URL
     *
     * @param graphqlUrl GraphQL API的URL
     */
    public void setGraphqlUrl(String graphqlUrl) {
        this.graphqlUrl = graphqlUrl;
    }

    public String getBwpTokenUrl() {
        return bwpTokenUrl;
    }

    public void setBwpTokenUrl(String bwpTokenUrl) {
        this.bwpTokenUrl = bwpTokenUrl;
    }

    /**
     * 获取授权令牌
     *
     * @return 用于访问API的授权令牌
     */
    public String getBwpToken() {
        return bwpToken;
    }

    /**
     * 设置授权令牌
     *
     * @param bwpToken 用于访问API的授权令牌
     */
    public void setBwpToken(String bwpToken) {
        this.bwpToken = bwpToken;
    }

    /**
     * 获取API版本号
     *
     * @return API版本号
     */
    public String getVersion() {
        return version;
    }

    /**
     * 设置API版本号
     *
     * @param version API版本号
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * 获取目标ID
     *
     * @return API目标ID
     */
    public String getTargetId() {
        return targetId;
    }

    /**
     * 设置目标ID
     *
     * @param targetId API目标ID
     */
    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }
}
