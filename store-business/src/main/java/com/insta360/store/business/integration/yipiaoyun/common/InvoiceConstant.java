package com.insta360.store.business.integration.yipiaoyun.common;

import com.google.common.collect.Lists;
import com.insta360.store.business.meta.robot.FeiShuAtUser;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 易票云开票参数常量类
 * @Date 2021/9/9
 */
public class InvoiceConstant {

    /**
     * 照相器材税收分类编码
     */
    public static final String cameraItemTaxCode = "1090622020000000000";

    /**
     * 软件税收分类编码
     */
    public static final String softwareItemTaxCode = "1060301029900000000";

    /**
     * 发票商品计量单位
     */
    public static final String invoiceItemUnit = "个";

    /**
     * 8k软件套餐ID
     */
    public static final List<Integer> softwareItemCommodityId = Lists.newArrayList(1053, 1054, 1055);

    /**
     * 商城大陆开票相关艾特人员列表
     */
    public static final FeiShuAtUser[] STORE_AT_USERS = {FeiShuAtUser.HSM, FeiShuAtUser.JHF, FeiShuAtUser.GN};

    /**
     * 官网售后大陆开票相关艾特人员列表
     */
    public static final FeiShuAtUser[] WWW_AT_USERS = {FeiShuAtUser.ZHY, FeiShuAtUser.GN};
}
