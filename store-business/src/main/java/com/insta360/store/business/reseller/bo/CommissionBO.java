package com.insta360.store.business.reseller.bo;

import com.insta360.store.business.meta.enums.Currency;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 分销订单佣金BO
 * @Date 2022/12/14
 */
public class CommissionBO implements Serializable {

    /**
     * 总收益
     */
    private Double totalIncome;

    /**
     * 佣金明细
     */
    private List<CommissionDetailBO> commissionDetails;

    /**
     * 提现币种
     */
    private Currency currency;

    /**
     * 支付货币美元初始汇率
     */
    private Double payCurrencyUsdRate;

    /**
     * 提现货币美元初始汇率
     */
    private Double withdrawCurrencyUsdRate;

    public CommissionBO() {
    }

    public CommissionBO(Double totalIncome, List<CommissionDetailBO> commissionDetails, Currency currency, Double payCurrencyUsdRate, Double withdrawCurrencyUsdRate) {
        this.totalIncome = totalIncome;
        this.commissionDetails = commissionDetails;
        this.currency = currency;
        this.payCurrencyUsdRate = payCurrencyUsdRate;
        this.withdrawCurrencyUsdRate = withdrawCurrencyUsdRate;
    }

    public Currency getCurrency() {
        return currency;
    }

    public void setCurrency(Currency currency) {
        this.currency = currency;
    }

    public Double getTotalIncome() {
        return totalIncome;
    }

    public void setTotalIncome(Double totalIncome) {
        this.totalIncome = totalIncome;
    }

    public List<CommissionDetailBO> getCommissionDetails() {
        return commissionDetails;
    }

    public void setCommissionDetails(List<CommissionDetailBO> commissionDetails) {
        this.commissionDetails = commissionDetails;
    }

    public Double getPayCurrencyUsdRate() {
        return payCurrencyUsdRate;
    }

    public void setPayCurrencyUsdRate(Double payCurrencyUsdRate) {
        this.payCurrencyUsdRate = payCurrencyUsdRate;
    }

    public Double getWithdrawCurrencyUsdRate() {
        return withdrawCurrencyUsdRate;
    }

    public void setWithdrawCurrencyUsdRate(Double withdrawCurrencyUsdRate) {
        this.withdrawCurrencyUsdRate = withdrawCurrencyUsdRate;
    }
}
