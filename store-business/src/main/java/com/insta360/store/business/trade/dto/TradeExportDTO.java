package com.insta360.store.business.trade.dto;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2022/01/10
 * @Description:
 */
public class TradeExportDTO {

    /**
     * 导出类型
     */
    private String exportType;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 卡种
     */
    private String creditCardType;

    /**
     * 支付机构（ocean || cko）
     */
    private String payMethod;

    /**
     * 支付通道
     */
    private Integer payChannelId;

    /**
     * 订单所属国家
     */
    private String orderCountry;

    /**
     * forter决策
     */
    private String forterDecision;

    /**
     * 是否3d
     */
    private Boolean isThreeDomainSecure;

    /**
     * 订单创建开始时间
     */
    private LocalDateTime orderCreateStartTime;

    /**
     * 订单创建结束时间
     */
    private LocalDateTime orderCreateEndTime;

    /**
     * 支付发起开始时间
     */
    private LocalDateTime orderPayRequestStartTime;

    /**
     * 支付发起结束时间
     */
    private LocalDateTime orderPayRequestEndTime;

    /**
     * 支付结果
     */
    private Boolean payResult;

    public String getExportType() {
        return exportType;
    }

    public void setExportType(String exportType) {
        this.exportType = exportType;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getCreditCardType() {
        return creditCardType;
    }

    public void setCreditCardType(String creditCardType) {
        this.creditCardType = creditCardType;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getPayChannelId() {
        return payChannelId;
    }

    public void setPayChannelId(Integer payChannelId) {
        this.payChannelId = payChannelId;
    }

    public String getOrderCountry() {
        return orderCountry;
    }

    public void setOrderCountry(String orderCountry) {
        this.orderCountry = orderCountry;
    }

    public String getForterDecision() {
        return forterDecision;
    }

    public void setForterDecision(String forterDecision) {
        this.forterDecision = forterDecision;
    }

    public Boolean getThreeDomainSecure() {
        return isThreeDomainSecure;
    }

    public void setThreeDomainSecure(Boolean threeDomainSecure) {
        isThreeDomainSecure = threeDomainSecure;
    }

    public LocalDateTime getOrderCreateStartTime() {
        return orderCreateStartTime;
    }

    public void setOrderCreateStartTime(LocalDateTime orderCreateStartTime) {
        this.orderCreateStartTime = orderCreateStartTime;
    }

    public LocalDateTime getOrderCreateEndTime() {
        return orderCreateEndTime;
    }

    public void setOrderCreateEndTime(LocalDateTime orderCreateEndTime) {
        this.orderCreateEndTime = orderCreateEndTime;
    }

    public LocalDateTime getOrderPayRequestStartTime() {
        return orderPayRequestStartTime;
    }

    public void setOrderPayRequestStartTime(LocalDateTime orderPayRequestStartTime) {
        this.orderPayRequestStartTime = orderPayRequestStartTime;
    }

    public LocalDateTime getOrderPayRequestEndTime() {
        return orderPayRequestEndTime;
    }

    public void setOrderPayRequestEndTime(LocalDateTime orderPayRequestEndTime) {
        this.orderPayRequestEndTime = orderPayRequestEndTime;
    }

    public Boolean getPayResult() {
        return payResult;
    }

    public void setPayResult(Boolean payResult) {
        this.payResult = payResult;
    }

    @Override
    public String toString() {
        return "TradeExportDTO{" +
                "exportType='" + exportType + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", creditCardType='" + creditCardType + '\'' +
                ", payMethod='" + payMethod + '\'' +
                ", payChannelId=" + payChannelId +
                ", orderCountry='" + orderCountry + '\'' +
                ", forterDecision='" + forterDecision + '\'' +
                ", isThreeDomainSecure=" + isThreeDomainSecure +
                ", orderCreateStartTime=" + orderCreateStartTime +
                ", orderCreateEndTime=" + orderCreateEndTime +
                ", orderPayRequestStartTime=" + orderPayRequestStartTime +
                ", orderPayRequestEndTime=" + orderPayRequestEndTime +
                ", payResult=" + payResult +
                '}';
    }
}
