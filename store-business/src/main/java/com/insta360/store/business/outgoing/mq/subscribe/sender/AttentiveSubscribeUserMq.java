package com.insta360.store.business.outgoing.mq.subscribe.sender;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.subscribe.dto.EmailSubscribeDTO;
import com.insta360.store.business.user.model.EmailSubscribe;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @description: attentive user上报mq
 * @author: py
 * @create: 2023-09-11 14:02
 */
@Component
public class AttentiveSubscribeUserMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(AttentiveSubscribeUserMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_attentive_subscribe, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * attentive上报user
     *
     * @param emailSubscribe
     */
    public void sendAttentiveSubscribeUserMessage(EmailSubscribe emailSubscribe) {
        Integer subscribeId = emailSubscribe.getId();
        if (subscribeId == null) {
            LOGGER.error("[attentive]user上报失败。subscribeId缺失。request:{}", emailSubscribe.toString());
            return;
        }

        LOGGER.info("[attentive]user上报准备中：subscribeId:{}", subscribeId);
        EmailSubscribeDTO subscribe = new EmailSubscribeDTO();
        subscribe.setSubscribeId(subscribeId);
        String messageId = rocketTcpMessageSender.sendMessage(JSONObject.toJSONString(subscribe));
        MqUtils.isBlankMessageIdHandle(messageId, this, subscribe);
        if (StringUtils.isBlank(messageId)) {
            LOGGER.info("[attentive]user上报failed：subscribeId:{}", subscribeId);
            FeiShuMessageUtil.storeGeneralMessage("[attentive]电话订阅上报同步消息失败：subscribeId:{}" + subscribeId, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return;
        }
        LOGGER.info("[attentive]user上报success：subscribeId:{}", subscribeId);
    }
}
