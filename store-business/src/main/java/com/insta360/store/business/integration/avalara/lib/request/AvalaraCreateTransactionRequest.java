package com.insta360.store.business.integration.avalara.lib.request;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.integration.avalara.AvalaraConfiguration;
import com.insta360.store.business.integration.avalara.lib.module.AvalaraCreateTransaction;

/**
 * @Author: wbt
 * @Date: 2023/06/06
 * @Description:
 */
public class AvalaraCreateTransactionRequest extends BaseAvalaraRequest {

    /**
     * 接口路径
     */
    private static final String METHOD = "/api/v2/transactions/create";

    /**
     * 创建事务
     */
    private AvalaraCreateTransaction createTransaction;

    public AvalaraCreateTransactionRequest(AvalaraConfiguration avalaraConfiguration) {
        super(METHOD, avalaraConfiguration);
    }

    @Override
    protected String getRequestBody() {
        return JSON.toJSONString(createTransaction);
    }

    public AvalaraCreateTransaction getCreateTransaction() {
        return createTransaction;
    }

    public void setCreateTransaction(AvalaraCreateTransaction createTransaction) {
        this.createTransaction = createTransaction;
    }

    @Override
    public Object getRequestParam() {
        return createTransaction.getLines();
    }

    @Override
    public String toString() {
        return "AvalaraCreateTransactionRequest{" +
                "createTransaction=" + createTransaction +
                '}';
    }
}
