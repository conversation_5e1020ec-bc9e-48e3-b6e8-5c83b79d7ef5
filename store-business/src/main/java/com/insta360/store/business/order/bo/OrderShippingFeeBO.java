package com.insta360.store.business.order.bo;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单运费dto
 *
 * <AUTHOR>
 * @date 2023/08/15
 */
public class OrderShippingFeeBO implements Serializable {

    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    private Integer id;

    /**
     * 数量
     */
    @NotNull(message = "套餐数量不能为空")
    private Integer number;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    @Override
    public String toString() {
        return "OrderShippingFeeBO{" +
                "id=" + id +
                ", number=" + number +
                '}';
    }
}
