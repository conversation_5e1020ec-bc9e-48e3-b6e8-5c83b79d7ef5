package com.insta360.store.business.reseller.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: hyc
 * @Date: 2019/2/27
 * @Description:
 */
@TableName("reseller_exp_rule")
public class ResellerExpRule extends BaseModel<ResellerExpRule> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "product")
    private Integer product;

    private Integer experience;

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getExperience() {
        return experience;
    }

    public void setExperience(Integer experience) {
        this.experience = experience;
    }

    @Override
    public String toString() {
        return "ResellerExpRule{" +
        "product=" + product +
        ", experience=" + experience +
        "}";
    }
}
