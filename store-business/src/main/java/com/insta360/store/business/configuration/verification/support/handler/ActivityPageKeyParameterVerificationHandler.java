package com.insta360.store.business.configuration.verification.support.handler;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.verification.enums.ParameterBusinessType;
import com.insta360.store.business.configuration.verification.exception.StoreParameterVerificationErrorCode;
import com.insta360.store.business.meta.model.ActivityPage;
import com.insta360.store.business.meta.service.ActivityPageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 活动页面KEY验证处理器
 *
 * <AUTHOR>
 * @date 2025/02/10
 */
@Component
public class ActivityPageKeyParameterVerificationHandler extends BaseStoreParameterVerificationHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivityPageKeyParameterVerificationHandler.class);

    /**
     * 缓存业务类型
     */
    private final ParameterBusinessType type = ParameterBusinessType.ACTIVITY_PAGE_KEY;

    @Autowired
    ActivityPageService activityPageService;

    @Autowired
    private Cache<String, Object> storeEhCache;

    /**
     * 执行URLke的参数检查
     * 此方法旨在验证给定的键是否存在于特定的缓存业务类型中，并且该键对应的活动ke是有效的
     * 如果验证失败，将记录相关信息并抛出异常
     *
     * @param businessType 业务类型枚举，用于指定需要进行参数检查的缓存类型
     * @param key          需要验证的键，其有效性将被检查
     */
    @Override
    public void parameterCheck(ParameterBusinessType businessType, Object key) {
        // 检查缓存中是否包含指定业务类型的条目
        boolean containsKey = storeEhCache.containsKey(businessType.name());
        // 如果缓存不包含该业务类型或键为空，则无需进一步检查
        if (!containsKey || Objects.isNull(key)) {
            return;
        }
        // 从缓存中获取与业务类型对应的映射
        Map<String, String> map = (Map<String, String>) storeEhCache.get(type.name());

        // 使用键从映射中获取活动key
        String activityKey = map.get(key.toString());
        // 检查活动key是否为空或仅包含空白字符
        boolean isBlank = StringUtils.isBlank(activityKey);
        if (isBlank) {
            LOGGER.info("商城字符串类型参数验证不通过 value:{}", key);
            // 非法请求被拦截次数上报
            grafanaInterfaceParamVerificationHelper.interfaceParamRequestReport(businessType);
            // 抛出异常，指示缓存未命中错误
            throw new InstaException(StoreParameterVerificationErrorCode.CACHE_MISS_ERROR);
        }
    }

    /**
     * 添加缓存
     *
     * @param key
     */
    @Override
    public void putCache(String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        if (storeEhCache.containsKey(type.name())) {
            // 获取已有的缓存Map
            Map<String, String> cacheMap = (Map<String, String>) storeEhCache.get(type.name());
            cacheMap.put(key, key);
            // 将新的值添加进缓存
            storeEhCache.put(type.name(), cacheMap);
            LOGGER.info("EhCache本地缓存_Map数据更新成功 CacheMap:{}", JSON.toJSONString(cacheMap));
        } else {
            initCache();
            LOGGER.info("EhCache本地缓存_Map数据初始化成功 CacheMap:{}", JSON.toJSONString(key));
        }
    }

    /**
     * 初始化缓存
     */
    @Override
    public void initCache() {
        List<ActivityPage> activityPageList = activityPageService.list(null);

        if (CollectionUtils.isEmpty(activityPageList)) {
            return;
        }
        Map<String, String> activityKeyMap = new HashMap<>();
        activityPageList.forEach(activityPage -> activityKeyMap.put(activityPage.getUrlKey(), activityPage.getUrlKey()));
        storeEhCache.put(type.name(), activityKeyMap);
        LOGGER.info("活动配置ActivityKey——EhCache本地缓存初始化完成...");
    }

    @Override
    public String getAllCache() {
        return JSON.toJSONString(storeEhCache);
    }

    /**
     * 删除缓存
     *
     * @param ehCacheBusiness
     */
    private void removeEhCache(ParameterBusinessType ehCacheBusiness) {
        if (Objects.isNull(ehCacheBusiness)) {
            return;
        }
        if (storeEhCache.containsKey(ehCacheBusiness.name())) {
            storeEhCache.remove(ehCacheBusiness.name());
            LOGGER.info("商城接口参数校验-EhCache缓存删除成功,key:{}", ehCacheBusiness.name());
        }
    }
}
