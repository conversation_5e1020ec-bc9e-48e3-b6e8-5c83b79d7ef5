package com.insta360.store.business.reseller.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.AssertUtil;
import com.insta360.compass.core.util.SecureUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.PriceService;
import com.insta360.store.business.payment.bo.EntPayInfo;
import com.insta360.store.business.payment.service.PaymentService;
import com.insta360.store.business.reseller.dao.ResellerBonusDao;
import com.insta360.store.business.reseller.enums.ResellerOrderOrigin;
import com.insta360.store.business.reseller.enums.ResellerWithdrawState;
import com.insta360.store.business.reseller.exception.ResellerErrorCode;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerBonus;
import com.insta360.store.business.reseller.model.ResellerBonusWithdrawRecord;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.service.ResellerBonusService;
import com.insta360.store.business.reseller.service.ResellerBonusWithdrawService;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import com.insta360.store.business.reseller.service.ResellerService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-03-28
 * @Description:
 */
@Service
public class ResellerBonusServiceImpl extends BaseServiceImpl<ResellerBonusDao, ResellerBonus> implements ResellerBonusService {

    @Autowired
    PaymentService paymentService;

    @Autowired
    ResellerBonusWithdrawService bonusWithdrawService;

    @Autowired
    PriceService priceService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    ResellerOrderService resellerOrderService;

    @Override
    public List<ResellerBonus> getResellerBonus(Reseller reseller) {
        QueryWrapper<ResellerBonus> qw = new QueryWrapper<>();
        qw.eq("reseller_auto_id", reseller.getId());

        return baseMapper.selectList(qw);
    }

    @Override
    public void payOffWithdraw(Integer withdrawId) {
        ResellerBonusWithdrawRecord bonusWithdrawRecord = bonusWithdrawService.getById(withdrawId);
        if (bonusWithdrawRecord != null) {

            // 只有未发放的记录能发放
            ResellerWithdrawState withdrawState = bonusWithdrawRecord.withdrawState();
            if (ResellerWithdrawState.applying.equals(withdrawState)) {
                Reseller reseller = resellerService.getResellerByResellerAutoId(bonusWithdrawRecord.getResellerAutoId());

                Price toPay = new Price(Currency.CNY, bonusWithdrawRecord.getAmount());

                EntPayInfo entPayInfo = new EntPayInfo();
                entPayInfo.setToUserId(reseller.getUserId());
                entPayInfo.setToPayPrice(toPay);
                entPayInfo.setTradeNumber(SecureUtil.md5("" + withdrawId + reseller.getUserId() + toPay.getAmount()));
                entPayInfo.setDescription("小程序分销奖励金提现");
                paymentService.payToUserWechatAccount(entPayInfo);

                // 变成已提现状态
                bonusWithdrawRecord.setState(ResellerWithdrawState.success.getCode());
                bonusWithdrawService.updateById(bonusWithdrawRecord);
            }
        }
    }

    @Override
    public void withdraw(Reseller reseller, Price price) {
        AssertUtil.notNull(reseller, price);

        Price zero = new Price(Currency.CNY, 0f);
        if (priceService.moreThan(zero, price)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        Price bonusBalance = this.getResellerBonusBalance(reseller);
        if (priceService.moreThan(price, bonusBalance)) {
            throw new InstaException(ResellerErrorCode.OutOfBonusBalanceException);
        }

        ResellerBonusWithdrawRecord withdrawRecord = new ResellerBonusWithdrawRecord();
        withdrawRecord.setAmount(price.getAmount());
        withdrawRecord.setResellerAutoId(reseller.getId());
        withdrawRecord.setCurrency(price.getCurrency().name());
        withdrawRecord.setState(ResellerWithdrawState.applying.getCode());
        withdrawRecord.setCreateTime(LocalDateTime.now());
        bonusWithdrawService.save(withdrawRecord);
    }

    @Override
    public Price getResellerBonusBalance(Reseller reseller) {
        List<ResellerBonus> resellerBonus = this.getResellerBonus(reseller);
        if (CollectionUtils.isEmpty(resellerBonus)) {
            return new Price(Currency.CNY, 0f);
        }
        double allBonus = resellerBonus.stream().mapToDouble(ResellerBonus::getAmount).sum();

        List<ResellerBonusWithdrawRecord> withdrawRecords = bonusWithdrawService.getByReseller(reseller);
        double withdrawBonus = withdrawRecords.stream().mapToDouble(ResellerBonusWithdrawRecord::getAmount).sum();
        double balance = allBonus - withdrawBonus;
        float formatBalance = priceService.formatPriceAmount((float) balance);

        // 目前固定为RMB
        return new Price(Currency.CNY, formatBalance);
    }

    @Override
    public void giveBonus(ResellerOrder resellerOrder) {

        // 判断是否是分销商的第一个小程序分销订单，发奖励金
        ResellerOrderOrigin orderOrigin = resellerOrder.resellerOrderOrigin();
        if (ResellerOrderOrigin.store_weapp.equals(orderOrigin)) {

            // 如果是第一条分销订单，则有100块奖励金
            boolean isFirst = isFirstAvailableWeappOrder(resellerOrder);
            if (isFirst) {
                ResellerBonus resellerBonus = new ResellerBonus();
                resellerBonus.setAmount(100f);
                resellerBonus.setCurrency(Currency.CNY.name());

                Reseller reseller = resellerService.getByPromoCode(resellerOrder.getPromoCode());
                resellerBonus.setResellerAutoId(reseller.getId());
                baseMapper.insert(resellerBonus);
                FeiShuMessageUtil.storeGeneralMessage(resellerOrder.getOrderNumber() + "为" + reseller.getEmail() + "添加了100块奖励金！", FeiShuGroupRobot.MainNotice, FeiShuAtUser.CYE);
            }
        }
    }

    private boolean isFirstAvailableWeappOrder(ResellerOrder resellerOrder) {
        QueryWrapper<ResellerOrder> qw = new QueryWrapper<>();
        qw.eq("order_origin", ResellerOrderOrigin.store_weapp.name());
        qw.eq("promo_code", resellerOrder.getPromoCode());
        qw.gt("commission", 0);
        qw.isNotNull("receive_time");
        qw.orderByAsc("receive_time");
        qw.last("limit 1");

        ResellerOrder firstResellerOrder = resellerOrderService.getOne(qw);
        if (firstResellerOrder != null && firstResellerOrder.getOrderNumber().equals(resellerOrder.getOrderNumber())) {
            return true;
        }

        return false;
    }
}
