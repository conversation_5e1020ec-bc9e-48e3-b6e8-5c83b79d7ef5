package com.insta360.store.business.prime.lib.variables;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 * @description 创建购买组的GraphQL变量
 * 本类用于封装向Amazon Prime服务发送创建购买组请求时所需的所有参数
 * 遵循GraphQL变量格式，作为请求参数传递给Prime API
 * <p>
 * 参数说明：
 * - input.members: bundle子商品信息
 * - memberAmount.unit: 计量单位，固定使用"UNIT"
 * - memberAmount.value: 子商品数量
 * - product.externalId: 商城套餐id
 * <p>
 * - input.representativeProduct: 代表性产品信息
 * - representativeProduct.externalId: 套餐对应生成的bundle prime SKU
 * - representativeProduct.offerPrime: 开关状态，true表示启用，false表示禁用
 * - representativeProduct.productDetailPageUrl: 商详页URL，可拼接?c={套餐id}做选项定位
 */
public class CreatePurchaseGroupVariables implements PrimeVariables {

    /**
     * 创建购买组的输入参数
     */
    private CreatePurchaseGroupInput input;

    /**
     * 默认构造函数
     */
    public CreatePurchaseGroupVariables() {
    }

    /**
     * 通过购买组输入参数创建GraphQL变量对象
     *
     * @param input 创建购买组的输入参数
     */
    public CreatePurchaseGroupVariables(CreatePurchaseGroupInput input) {
        this.input = input;
    }

    /**
     * 获取创建购买组的输入参数
     *
     * @return 创建购买组的输入参数对象
     */
    public CreatePurchaseGroupInput getInput() {
        return input;
    }

    /**
     * 设置创建购买组的输入参数
     *
     * @param input 创建购买组的输入参数对象
     */
    public void setInput(CreatePurchaseGroupInput input) {
        this.input = input;
    }

    @Override
    public String toString() {
        return "CreatePurchaseGroupVariables{" +
                "input=" + input +
                '}';
    }

    /**
     * 本地化字符串字段值
     */
    public static class LocalizedStringField {

        /**
         * 语言区域
         */
        private String locale;

        /**
         * 值
         */
        private String value;

        /**
         * 默认构造函数
         */
        public LocalizedStringField() {
        }

        /**
         * 带参数构造函数
         * 
         * @param locale 语言区域
         * @param value 值
         */
        public LocalizedStringField(String locale, String value) {
            this.locale = locale;
            this.value = value;
        }

        /**
         * 获取语言区域
         * 
         * @return 语言区域
         */
        public String getLocale() {
            return locale;
        }

        /**
         * 设置语言区域
         * 
         * @param locale 语言区域
         */
        public void setLocale(String locale) {
            this.locale = locale;
        }

        /**
         * 获取值
         * 
         * @return 值
         */
        public String getValue() {
            return value;
        }

        /**
         * 设置值
         * 
         * @param value 值
         */
        public void setValue(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return "LocalizedStringField{" +
                    "locale='" + locale + '\'' +
                    ", value='" + value + '\'' +
                    '}';
        }
    }

    /**
     * 本地化字符串字段输入
     */
    public static class LocalizedStringFieldInput {

        /**
         * 值集合
         */
        private List<LocalizedStringField> values;

        /**
         * 默认构造函数
         */
        public LocalizedStringFieldInput() {
            this.values = new ArrayList<>();
        }

        /**
         * 带参数构造函数
         * 
         * @param values 值集合
         */
        public LocalizedStringFieldInput(List<LocalizedStringField> values) {
            this.values = values;
        }

        /**
         * 获取值集合
         * 
         * @return 值集合
         */
        public List<LocalizedStringField> getValues() {
            return values;
        }

        /**
         * 设置值集合
         * 
         * @param values 值集合
         */
        public void setValues(List<LocalizedStringField> values) {
            this.values = values;
        }

        /**
         * 添加本地化字符串
         * 
         * @param locale 语言区域
         * @param value 值
         * @return 本对象，支持链式调用
         */
        public LocalizedStringFieldInput addValue(String locale, String value) {
            if (this.values == null) {
                this.values = new ArrayList<>();
            }
            this.values.add(new LocalizedStringField(locale, value));
            return this;
        }

        @Override
        public String toString() {
            return "LocalizedStringFieldInput{" +
                    "values=" + values +
                    '}';
        }
    }

    /**
     * 特色图片输入
     */
    public static class FeaturedImageInput {

        /**
         * 图片源URL
         */
        private String sourceUrl;

        /**
         * 默认构造函数
         */
        public FeaturedImageInput() {
        }

        /**
         * 带参数构造函数
         * 
         * @param sourceUrl 图片源URL
         */
        public FeaturedImageInput(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }

        /**
         * 获取图片源URL
         * 
         * @return 图片源URL
         */
        public String getSourceUrl() {
            return sourceUrl;
        }

        /**
         * 设置图片源URL
         * 
         * @param sourceUrl 图片源URL
         */
        public void setSourceUrl(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }

        @Override
        public String toString() {
            return "FeaturedImageInput{" +
                    "sourceUrl='" + sourceUrl + '\'' +
                    '}';
        }
    }

    /**
     * 创建产品输入
     */
    public static class CreateProductInput {

        /**
         * 外部ID，用于在外部系统中标识产品
         */
        private String externalId;

        /**
         * 是否提供Prime服务
         */
        private Boolean offerPrime;

        /**
         * 产品详情页URL
         */
        private String productDetailPageUrl;

        /**
         * 产品图片
         */
        private FeaturedImageInput image;

        /**
         * 产品标题
         */
        private LocalizedStringFieldInput title;

        /**
         * 默认构造函数
         */
        public CreateProductInput() {
        }

        /**
         * 带必要参数的构造函数
         * 
         * @param externalId 外部ID
         * @param offerPrime 是否提供Prime服务
         */
        public CreateProductInput(String externalId, Boolean offerPrime) {
            this.externalId = externalId;
            this.offerPrime = offerPrime;
        }

        /**
         * 带全部参数的构造函数
         * 
         * @param externalId 外部ID
         * @param offerPrime 是否提供Prime服务
         * @param productDetailPageUrl 产品详情页URL
         */
        public CreateProductInput(String externalId, Boolean offerPrime, String productDetailPageUrl) {
            this.externalId = externalId;
            this.offerPrime = offerPrime;
            this.productDetailPageUrl = productDetailPageUrl;
        }

        /**
         * 获取外部ID
         * 
         * @return 外部ID
         */
        public String getExternalId() {
            return externalId;
        }

        /**
         * 设置外部ID
         * 
         * @param externalId 外部ID
         */
        public void setExternalId(String externalId) {
            this.externalId = externalId;
        }

        /**
         * 获取是否提供Prime服务
         * 
         * @return 是否提供Prime服务
         */
        public Boolean getOfferPrime() {
            return offerPrime;
        }

        /**
         * 设置是否提供Prime服务
         * 
         * @param offerPrime 是否提供Prime服务
         */
        public void setOfferPrime(Boolean offerPrime) {
            this.offerPrime = offerPrime;
        }

        /**
         * 获取产品详情页URL
         * 
         * @return 产品详情页URL
         */
        public String getProductDetailPageUrl() {
            return productDetailPageUrl;
        }

        /**
         * 设置产品详情页URL
         * 
         * @param productDetailPageUrl 产品详情页URL
         */
        public void setProductDetailPageUrl(String productDetailPageUrl) {
            this.productDetailPageUrl = productDetailPageUrl;
        }

        /**
         * 获取产品图片
         * 
         * @return 产品图片
         */
        public FeaturedImageInput getImage() {
            return image;
        }

        /**
         * 设置产品图片
         * 
         * @param image 产品图片
         */
        public void setImage(FeaturedImageInput image) {
            this.image = image;
        }

        /**
         * 获取产品标题
         * 
         * @return 产品标题
         */
        public LocalizedStringFieldInput getTitle() {
            return title;
        }

        /**
         * 设置产品标题
         * 
         * @param title 产品标题
         */
        public void setTitle(LocalizedStringFieldInput title) {
            this.title = title;
        }

        /**
         * 构建器
         */
        public static Builder builder() {
            return new Builder();
        }

        @Override
        public String toString() {
            return "CreateProductInput{" +
                    "externalId='" + externalId + '\'' +
                    ", offerPrime=" + offerPrime +
                    ", productDetailPageUrl='" + productDetailPageUrl + '\'' +
                    ", image=" + image +
                    ", title=" + title +
                    '}';
        }

        /**
         * CreateProductInput构建器
         */
        public static class Builder {
            private CreateProductInput product = new CreateProductInput();

            /**
             * 设置外部ID
             *
             * @param externalId 外部ID
             * @return 构建器
             */
            public Builder externalId(String externalId) {
                product.setExternalId(externalId);
                return this;
            }

            /**
             * 设置是否提供Prime服务
             *
             * @param offerPrime 是否提供Prime服务
             * @return 构建器
             */
            public Builder offerPrime(Boolean offerPrime) {
                product.setOfferPrime(offerPrime);
                return this;
            }

            /**
             * 设置产品详情页URL
             *
             * @param url 产品详情页URL
             * @return 构建器
             */
            public Builder productDetailPageUrl(String url) {
                product.setProductDetailPageUrl(url);
                return this;
            }

            /**
             * 设置产品图片URL
             *
             * @param sourceUrl 图片源URL
             * @return 构建器
             */
            public Builder imageSourceUrl(String sourceUrl) {
                if (sourceUrl != null && !sourceUrl.isEmpty()) {
                    product.setImage(new FeaturedImageInput(sourceUrl));
                }
                return this;
            }

            /**
             * 设置产品标题
             *
             * @param locale 语言区域
             * @param value 标题值
             * @return 构建器
             */
            public Builder titleValue(String locale, String value) {
                if (locale != null && !locale.isEmpty() && value != null && !value.isEmpty()) {
                    LocalizedStringFieldInput title = new LocalizedStringFieldInput();
                    title.addValue(locale, value);
                    product.setTitle(title);
                }
                return this;
            }

            /**
             * 构建产品输入对象
             *
             * @return 产品输入对象
             */
            public CreateProductInput build() {
                return product;
            }
        }
    }

    /**
     * 代表性产品输入
     * 购买组的主要产品信息
     */
    public static class RepresentativeProductInput {

        /**
         * 代表性产品创建输入
         */
        private CreateProductInput representativeProduct;

        /**
         * 默认构造函数
         */
        public RepresentativeProductInput() {
        }

        /**
         * 带参数构造函数
         * 
         * @param representativeProduct 代表性产品创建输入
         */
        public RepresentativeProductInput(CreateProductInput representativeProduct) {
            this.representativeProduct = representativeProduct;
        }

        /**
         * 获取代表性产品创建输入
         * 
         * @return 代表性产品创建输入
         */
        public CreateProductInput getRepresentativeProduct() {
            return representativeProduct;
        }

        /**
         * 设置代表性产品创建输入
         * 
         * @param representativeProduct 代表性产品创建输入
         */
        public void setRepresentativeProduct(CreateProductInput representativeProduct) {
            this.representativeProduct = representativeProduct;
        }

        /**
         * 创建新的构建器
         * 
         * @return 构建器
         */
        public static Builder builder() {
            return new Builder();
        }

        @Override
        public String toString() {
            return "RepresentativeProductInput{" +
                    "representativeProduct=" + representativeProduct +
                    '}';
        }

        /**
         * RepresentativeProductInput构建器
         */
        public static class Builder {
            private RepresentativeProductInput productInput = new RepresentativeProductInput();

            /**
             * 设置代表性产品创建输入
             * 
             * @param representativeProduct 代表性产品创建输入
             * @return 构建器
             */
            public Builder representativeProduct(CreateProductInput representativeProduct) {
                productInput.setRepresentativeProduct(representativeProduct);
                return this;
            }

            /**
             * 设置代表性产品基本信息
             * 
             * @param externalId 外部ID
             * @param offerPrime 是否提供Prime服务
             * @param productDetailPageUrl 产品详情页URL
             * @return 构建器
             */
            public Builder basic(String externalId, Boolean offerPrime, String productDetailPageUrl) {
                CreateProductInput createProductInput = new CreateProductInput(externalId, offerPrime, productDetailPageUrl);
                productInput.setRepresentativeProduct(createProductInput);
                return this;
            }

            /**
             * 构建代表性产品输入对象
             * 
             * @return 代表性产品输入对象
             */
            public RepresentativeProductInput build() {
                return productInput;
            }
        }
    }

    /**
     * 产品标识信息
     * 可以通过外部ID或SKU标识产品
     */
    public static class Product {

        /**
         * 外部ID，用于在外部系统中标识产品
         */
        private String externalId;

        /**
         * 默认构造函数
         */
        public Product() {
        }

        /**
         * 带参数构造函数
         * 
         * @param externalId 外部ID
         */
        public Product(String externalId) {
            this.externalId = externalId;
        }

        /**
         * 获取外部ID
         * 
         * @return 外部ID
         */
        public String getExternalId() {
            return externalId;
        }

        /**
         * 设置外部ID
         * 
         * @param externalId 外部ID
         */
        public void setExternalId(String externalId) {
            this.externalId = externalId;
        }

        @Override
        public String toString() {
            return "Product{" +
                    "externalId='" + externalId + '\'' +
                    '}';
        }
    }

    /**
     * 成员数量
     * 定义购买组中产品的数量和单位
     */
    public static class MemberAmount {

        /**
         * 数量值
         */
        private Integer value;

        /**
         * 数量单位
         */
        private String unit;

        /**
         * 默认构造函数
         */
        public MemberAmount() {
        }

        /**
         * 带参数构造函数
         * 
         * @param value 数量值
         * @param unit 数量单位
         */
        public MemberAmount(Integer value, String unit) {
            this.value = value;
            this.unit = unit;
        }

        /**
         * 获取数量值
         * 
         * @return 数量值
         */
        public Integer getValue() {
            return value;
        }

        /**
         * 设置数量值
         * 
         * @param value 数量值
         */
        public void setValue(Integer value) {
            this.value = value;
        }

        /**
         * 获取数量单位
         * 
         * @return 数量单位
         */
        public String getUnit() {
            return unit;
        }

        /**
         * 设置数量单位
         * 
         * @param unit 数量单位
         */
        public void setUnit(String unit) {
            this.unit = unit;
        }

        @Override
        public String toString() {
            return "MemberAmount{" +
                    "value=" + value +
                    ", unit='" + unit + '\'' +
                    '}';
        }
    }


    /**
     * 购买组成员
     * 包含在购买组中的产品信息
     */
    public static class Member {

        /**
         * 产品标识信息
         */
        private Product product;

        /**
         * 成员数量信息
         */
        private MemberAmount memberAmount;

        /**
         * 默认构造函数
         */
        public Member() {
        }

        /**
         * 带必要参数构造函数
         * 
         * @param product 产品标识信息
         * @param memberAmount 成员数量信息
         */
        public Member(Product product, MemberAmount memberAmount) {
            this.product = product;
            this.memberAmount = memberAmount;
        }

        /**
         * 获取产品标识信息
         * 
         * @return 产品标识信息
         */
        public Product getProduct() {
            return product;
        }

        /**
         * 设置产品标识信息
         * 
         * @param product 产品标识信息
         */
        public void setProduct(Product product) {
            this.product = product;
        }

        /**
         * 获取成员数量信息
         * 
         * @return 成员数量信息
         */
        public MemberAmount getMemberAmount() {
            return memberAmount;
        }

        /**
         * 设置成员数量信息
         * 
         * @param memberAmount 成员数量信息
         */
        public void setMemberAmount(MemberAmount memberAmount) {
            this.memberAmount = memberAmount;
        }

        @Override
        public String toString() {
            return "Member{" +
                    "product=" + product +
                    ", memberAmount=" + memberAmount +
                    '}';
        }
    }

    /**
     * 创建购买组的输入参数
     * 包含创建Amazon Prime购买组时所需的所有必要信息
     * 包括代表性产品和成员产品列表
     */
    public static class CreatePurchaseGroupInput {

        /**
         * 代表性产品信息
         */
        private RepresentativeProductInput representativeProduct;

        /**
         * 成员产品列表
         */
        private List<Member> members;

        /**
         * 默认构造函数
         */
        public CreatePurchaseGroupInput() {
            this.members = new ArrayList<>();
        }

        /**
         * 带参数构造函数
         * 
         * @param representativeProduct 代表性产品信息
         */
        public CreatePurchaseGroupInput(RepresentativeProductInput representativeProduct) {
            this.representativeProduct = representativeProduct;
            this.members = new ArrayList<>();
        }

        /**
         * 带参数构造函数
         * 
         * @param representativeProduct 代表性产品信息
         * @param members 成员产品列表
         */
        public CreatePurchaseGroupInput(RepresentativeProductInput representativeProduct, List<Member> members) {
            this.representativeProduct = representativeProduct;
            this.members = members;
        }

        /**
         * 获取代表性产品信息
         * 
         * @return 代表性产品信息
         */
        public RepresentativeProductInput getRepresentativeProduct() {
            return representativeProduct;
        }

        /**
         * 设置代表性产品信息
         * 
         * @param representativeProduct 代表性产品信息
         */
        public void setRepresentativeProduct(RepresentativeProductInput representativeProduct) {
            this.representativeProduct = representativeProduct;
        }

        /**
         * 获取成员产品列表
         * 
         * @return 成员产品列表
         */
        public List<Member> getMembers() {
            return members;
        }

        /**
         * 设置成员产品列表
         * 
         * @param members 成员产品列表
         */
        public void setMembers(List<Member> members) {
            this.members = members;
        }

        /**
         * 添加成员产品
         * 
         * @param member 成员产品
         * @return 本对象，支持链式调用
         */
        public CreatePurchaseGroupInput addMember(Member member) {
            if (this.members == null) {
                this.members = new ArrayList<>();
            }
            this.members.add(member);
            return this;
        }

        /**
         * 创建构建器
         * 
         * @return 构建器
         */
        public static Builder builder() {
            return new Builder();
        }

        @Override
        public String toString() {
            return "CreatePurchaseGroupInput{" +
                    "representativeProduct=" + representativeProduct +
                    ", members=" + members +
                    '}';
        }

        /**
         * CreatePurchaseGroupInput构建器
         */
        public static class Builder {
            private CreatePurchaseGroupInput input = new CreatePurchaseGroupInput();

            /**
             * 设置代表性产品基本信息
             * 
             * @param externalId 外部ID
             * @param offerPrime 是否提供Prime服务
             * @param productDetailPageUrl 产品详情页URL
             * @return 构建器
             */
            public Builder representativeProductDetails(String externalId, Boolean offerPrime, String productDetailPageUrl) {
                CreateProductInput productInput = new CreateProductInput(externalId, offerPrime, productDetailPageUrl);
                RepresentativeProductInput representativeProductInput = new RepresentativeProductInput(productInput);
                input.setRepresentativeProduct(representativeProductInput);
                return this;
            }

            /**
             * 添加成员产品（通过外部ID）
             * 
             * @param externalId 外部ID
             * @param value 数量值
             * @param unit 数量单位
             * @return 构建器
             */
            public Builder addMember(String externalId, Integer value, String unit) {
                Product product = new Product(externalId);
                MemberAmount memberAmount = new MemberAmount(value, unit);
                Member member = new Member(product, memberAmount);
                input.addMember(member);
                return this;
            }

            /**
             * 构建创建购买组的输入参数
             * 
             * @return 创建购买组的输入参数
             */
            public CreatePurchaseGroupInput build() {
                return input;
            }
        }
    }
}
