package com.insta360.store.business.integration.mabang.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.integration.mabang.enums.MbOrderItemState;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-08-17
 * @Description:
 */
@TableName("mb_order_item")
public class MbOrderItem extends BaseModel<MbOrderItem> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("`order`")
    private Integer order;

    /**
     * ERP系统关联订单编号
     */
    @JSONField(name = "origin_order_id")
    private String originOrderId;

    /**
     * 马帮订单商品编号
     */
    @JSONField(name = "product_id")
    private String productId;

    /**
     * 商品名称
     */
    @JSONField(name = "product_name")
    private String productName;

    private String sku;

    @JSONField(name = "origin_price")
    private Float originPrice;

    private String currency;

    private Integer number;

    private Integer state;

    /**
     * 商品备注
     */
    @JSONField(name = "item_remark")
    private String itemRemark;

    @JSONField(name = "platform_item_id")
    private String platformItemId;

    @JSONField(name = "platform_quantity")
    private Integer platformQuantity;

    @JSONField(name = "platform_sku")
    private String platformSku;

    @JSONField(name = "is_gift")
    private Boolean isGift;

    /**
     * 是否组合商品
     */
    @JSONField(name = "is_combo")
    private Boolean isCombo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getOriginOrderId() {
        return originOrderId;
    }

    public void setOriginOrderId(String originOrderId) {
        this.originOrderId = originOrderId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Float getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Float originPrice) {
        this.originPrice = originPrice;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getItemRemark() {
        return itemRemark;
    }

    public void setItemRemark(String itemRemark) {
        this.itemRemark = itemRemark;
    }

    public String getPlatformItemId() {
        return platformItemId;
    }

    public void setPlatformItemId(String platformItemId) {
        this.platformItemId = platformItemId;
    }

    public Integer getPlatformQuantity() {
        return platformQuantity;
    }

    public void setPlatformQuantity(Integer platformQuantity) {
        this.platformQuantity = platformQuantity;
    }

    public String getPlatformSku() {
        return platformSku;
    }

    public void setPlatformSku(String platformSku) {
        this.platformSku = platformSku;
    }

    public Boolean getIsGift() {
        return isGift;
    }

    public void setIsGift(Boolean gift) {
        isGift = gift;
    }

    public Boolean getIsCombo() {
        return isCombo;
    }

    public void setIsCombo(Boolean isCombo) {
        this.isCombo = isCombo;
    }

    @Override
    public String toString() {
        return "MbOrderItem{" +
                "id=" + id +
                ", order=" + order +
                ", originOrderId='" + originOrderId + '\'' +
                ", productId='" + productId + '\'' +
                ", productName='" + productName + '\'' +
                ", sku='" + sku + '\'' +
                ", originPrice=" + originPrice +
                ", currency='" + currency + '\'' +
                ", number=" + number +
                ", state=" + state +
                ", itemRemark='" + itemRemark + '\'' +
                ", platformItemId='" + platformItemId + '\'' +
                ", platformQuantity=" + platformQuantity +
                ", platformSku='" + platformSku + '\'' +
                ", isGift=" + isGift +
                ", isCombo=" + isCombo +
                '}';
    }

    @JSONField(serialize = false)
    public MbOrderItemState orderItemState() {
        return MbOrderItemState.parse(state);
    }
}