package com.insta360.store.business.commodity.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author: hyc
 * @Date: 2019/3/11
 * @Description:
 */
public enum SaleState {

    // 预订
    book(2, "预订"),

    // 预售
    pre_sale(1, "预售"),

    // 缺货
    out_of_stock(-1, "缺货"),

    // 下架
    remove(-2, "下架"),

    // 即将上架
    coming_soon(-3, "即将上架"),

    // 正常选购
    normal(0, "选购");

    private int code;

    private String name;

    SaleState(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SaleState parse(Integer code) {
        // 兼容销售状态没配置场景转换异常
        if (Objects.isNull(code)) {
            return null;
        }

        for (SaleState state : values()) {
            if (state.code == code) {
                return state;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 缺货、即将上架状态
     *
     * @return
     */
    public static List<SaleState> outOfStockStates() {
        return Arrays.asList(SaleState.out_of_stock, SaleState.remove, SaleState.coming_soon);
    }
}
