package com.insta360.store.business.trade.service.impl.helper;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommoditySaleState;
import com.insta360.store.business.commodity.service.CommoditySaleStateService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.outgoing.mq.trade.dto.UserCartBusinessMessageDTO;
import com.insta360.store.business.outgoing.mq.trade.helper.UserCartMessageHelper;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.EmailSubscribeService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2025-04-14 14:42
 */
@Component
public class MarketingEmailHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketingEmailHelper.class);

    @Autowired
    UserCartMessageHelper userCartMessageHelper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    @Autowired
    EmailSubscribeService emailSubscribeService;

    /**
     * 发送购物车邮件
     *
     * @param items
     * @return
     */
    @Async
    public void sendCartEmail(StoreAccount account, List<OrderSheet.SheetItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<Commodity> a3CommodityList = commodityService.listEnabledCommodities(Product.X5_ID);
        List<Commodity> flow2proCommodityList = commodityService.listEnabledCommodities(Product.FLOW2PRO_ID);
        // a3 flow2pro套餐均未启用，发送普通版邮件
        if (CollectionUtils.isEmpty(a3CommodityList) && CollectionUtils.isEmpty(flow2proCommodityList)) {
            UserCartBusinessMessageDTO userCartMessageDto = new UserCartBusinessMessageDTO(account, UserCartEmailEnum.FIRST_EMAIL);
            userCartMessageHelper.sendUpdateUserCartMessage(userCartMessageDto);
            LOGGER.info(String.format("[购物车邮件发送]发送普通版第一封邮件,用户:%s", account));
            return;
        }

        // 用户订阅校验
        EmailSubscribe emailSubscribe = emailSubscribeService.getByEmailLast(account.getUsername());
        if (Objects.isNull(emailSubscribe)) {
            LOGGER.error(String.format("[购物车邮件发送]邮件订阅不存在-流程终止,本次不发送,用户:%s", account));
            return;
        }

        String country = emailSubscribe.getCountry();
        if (StringUtils.isBlank(country)) {
            LOGGER.error(String.format("[购物车邮件发送]邮件订阅国家为空-流程终止,本次不发送,用户:%s", account));
            return;
        }

        List<Integer> a3CommodityIds = a3CommodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        List<Integer> flow2proCommodityIds = flow2proCommodityList.stream().map(Commodity::getId).collect(Collectors.toList());

        boolean a3InCart = specificCommodityInCart(a3CommodityIds, items, InstaCountry.parse(country));
        boolean flow2ProInCart = specificCommodityInCart(flow2proCommodityIds, items, InstaCountry.parse(country));

        // 确定邮件类型
        UserCartEmailEnum userCartEmailEnum = determineEmailType(a3InCart, flow2ProInCart);
        UserCartBusinessMessageDTO userCartMessageDto = new UserCartBusinessMessageDTO(account, userCartEmailEnum);
        userCartMessageHelper.sendUpdateUserCartMessage(userCartMessageDto);
        LOGGER.info(String.format("[购物车邮件发送]发送%s第一封邮件,用户:%s", userCartEmailEnum.getType(), account));
    }

    /**
     * 购物车中是否含有指定产品 且在售
     */
    private boolean specificCommodityInCart(List<Integer> commodityIds,
                                            List<OrderSheet.SheetItem> items,
                                            InstaCountry country) {
        // 筛选出在售的产品
        List<CommoditySaleState> commoditySaleStateList = commoditySaleStateService.listSaleStateByCommodityIds(commodityIds, country);
        Map<Integer, SaleState> commodityStateMap = commoditySaleStateList.stream()
                .collect(
                        Collectors.toMap(CommoditySaleState::getCommodityId,
                                CommoditySaleState::parseSaleState)
                );
        List<OrderSheet.SheetItem> sheetItems = items.stream()
                .map(sheetItem -> {
                    SaleState saleState = commodityStateMap.get(sheetItem.getCommodityId());
                    if (Objects.isNull(saleState)) {
                        return null;
                    }

                    if (commodityIds.contains(sheetItem.getCommodityId()) && SaleState.normal.equals(saleState)) {
                        return sheetItem;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(sheetItems);
    }

    /**
     * 根据购物车内容确定发送的邮件类型（严格按优先级列表顺序检查）
     */
    private UserCartEmailEnum determineEmailType(boolean a3InCart, boolean flow2ProInCart) {
        return UserCartEmailEnum.sortByPriority()
                .stream()
                .filter(emailType -> (emailType == UserCartEmailEnum.A3_FIRST_EMAIL && a3InCart) ||
                        (emailType == UserCartEmailEnum.FLOW2PRO_FIRST_EMAIL && flow2ProInCart) ||
                        (emailType == UserCartEmailEnum.FIRST_EMAIL))
                .findFirst()
                .orElse(UserCartEmailEnum.FIRST_EMAIL);
    }
}
