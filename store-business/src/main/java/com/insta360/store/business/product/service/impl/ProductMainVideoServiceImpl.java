package com.insta360.store.business.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.product.model.ProductMainVideo;
import com.insta360.store.business.product.dao.ProductMainVideoDao;
import com.insta360.store.business.product.model.ProductVideo;
import com.insta360.store.business.product.service.ProductMainVideoService;
import com.insta360.compass.core.common.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-05-10
 * @Description:
 */
@Service
public class ProductMainVideoServiceImpl extends BaseServiceImpl<ProductMainVideoDao, ProductMainVideo> implements ProductMainVideoService {

    @Override
    public List<ProductMainVideo> listByProductIndex(Integer product, String language, Integer orderIndex) {
        QueryWrapper<ProductMainVideo> qw = new QueryWrapper<>();
        qw.eq("product", product);
        qw.eq("language", language);
        qw.ge("order_index", orderIndex);
        return baseMapper.selectList(qw);
    }


    @Override
    public void saveMainVideo(ProductMainVideo productMainVideo) {
        baseMapper.insert(productMainVideo);
    }

    @Override
    public void updateOrderIndexByIds(List<ProductMainVideo> productMainVideos) {
        if (CollectionUtils.isEmpty(productMainVideos)) {
            return;
        }
        baseMapper.updateOrderIndexByIds(productMainVideos);
    }

    @Override
    public void updateByMainVideoId(ProductMainVideo productMainVideoData) {
        baseMapper.updateById(productMainVideoData);
    }

    @Override
    public List<ProductMainVideo> listByProductRange(Integer product, String language, Integer firstIndex, Integer lastIndex) {
        QueryWrapper<ProductMainVideo> qw = new QueryWrapper<>();
        qw.eq("product", product);
        qw.eq("language", language);
        qw.ge("order_index", firstIndex);
        qw.le("order_index", lastIndex);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public void deleteMainVideo(Integer mainVideoId) {
        baseMapper.deleteById(mainVideoId);
    }

    @Override
    public List<ProductMainVideo> listByProductId(Integer product, String language) {
        QueryWrapper<ProductMainVideo> qw = new QueryWrapper<>();
        qw.eq("product", product);
        qw.eq("language", language);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }
}
