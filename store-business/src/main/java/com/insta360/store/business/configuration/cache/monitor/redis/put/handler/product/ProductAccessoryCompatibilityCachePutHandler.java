package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.product;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.outgoing.rpc.store.job.ProductCachePutService;
import com.insta360.store.business.product.model.Product;
import feign.RetryableException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/7
 */
@Component
public class ProductAccessoryCompatibilityCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductAccessoryCompatibilityCachePutHandler.class);

    @Autowired
    ProductCachePutService productCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        List<Integer> productIds = cachePutKeyParameter.getProductIds();
        if (CollectionUtils.isEmpty(productIds)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。产品id集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 如果没有产品数据，则中断流程
        Collection<Product> products = productService.listByIds(productIds);
        if (CollectionUtils.isEmpty(products)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。产品集合为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}。", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(products, countDownLatch));
        cachePutThreadPool.execute(() -> this.task2(products, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新参数
        StoreCacheDataChangeEventBO storeCacheDataChangeEvent = new StoreCacheDataChangeEventBO();
        storeCacheDataChangeEvent.setProductEvents(this.parseProductEvent(products));
        return storeCacheDataChangeEvent;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.PRODUCT_ACCESSORY_COMPATIBILITY;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.PRODUCT_INFO);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            for (Product product : (List<Product>) param) {
                CacheConstant.COUNTIES.forEach(cacheCounties -> {
                    // 更新产品页
                    productCachePutService.getInfo(product.getId(), cacheCounties.getCountry(), cacheCounties.getLanguage());
                });
            }
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
        try {
            for (Product product : (List<Product>) param) {
                // 更新产品配件适配性
                CacheConstant.LANGUAGES.forEach(language -> productCachePutService.getProductAccessoryCompatibility(product.getId(), language));
            }
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务2完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task2}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }

    @Override
    public Integer getTaskNumber() {
        return super.getTaskNumber() - 1;
    }
}
