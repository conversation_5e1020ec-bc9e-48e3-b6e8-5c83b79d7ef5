package com.insta360.store.business.cloud.bo;

import com.insta360.store.business.cloud.model.CloudStorageSubscribe;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/25
 */
public class CloudSubscribeCreateResultBO implements Serializable {

    /**
     * 最新订阅记录
     */
    private CloudStorageSubscribe LatestSubscribe;

    public CloudStorageSubscribe getLatestSubscribe() {
        return LatestSubscribe;
    }

    public void setLatestSubscribe(CloudStorageSubscribe latestSubscribe) {
        LatestSubscribe = latestSubscribe;
    }

    @Override
    public String toString() {
        return "CloudSubscribeCreateResultBO{" +
                "LatestSubscribe=" + LatestSubscribe +
                '}';
    }
}
