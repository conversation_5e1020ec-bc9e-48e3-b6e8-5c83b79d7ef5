package com.insta360.store.business.integration.wto.oms.service.handler.stock;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteResultBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsSkuStockQueryBO;
import com.insta360.store.business.integration.wto.oms.lib.requrest.BaseOmsRequest;
import com.insta360.store.business.integration.wto.oms.lib.requrest.stock.OmsSkuStockGetRequest;
import com.insta360.store.business.integration.wto.oms.lib.response.OmsSkuStockGetResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description OMS获取商品虚拟仓库库存信息实现类
 * @Date 2025/3/25
 */
@Scope("prototype")
@Component
public class StoreSkuStockGetHandler extends BaseStoreSkuStockService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreSkuStockGetHandler.class);


    @Override
    protected OmsExecuteResultBO doExecuteOmsTransaction(OmsExecuteBO omsExecuteBo) {
        LOGGER.info("[OMS系统]OMS获取商品虚拟仓库库存信息开始. omsExecuteBo:{}", JSON.toJSONString(omsExecuteBo));
        if (!this.preCheck(omsExecuteBo)) {
            return new OmsExecuteResultBO();
        }

        BaseOmsRequest omsRequest = this.getOmsRequest(omsExecuteBo);
        String result = null;
        try {
            result = omsRequest.executePost();
        } catch (Exception e) {
            LOGGER.error("[OMS系统]OMS获取商品虚拟仓库库存信息失败，存在异常.method:{} omsRequest: {}", omsRequest.getMethod(), JSON.toJSONString(omsRequest));
        }

        LOGGER.info("[OMS系统]OMS获取商品虚拟仓库库存信息结束. oms响应结果: {}", result);

        OmsExecuteResultBO omsExecuteResult = new OmsExecuteResultBO();
        OmsSkuStockGetResponse omsSkuStockGetResponse = OmsSkuStockGetResponse.parse(result);
        if (Objects.nonNull(omsSkuStockGetResponse) && omsSkuStockGetResponse.isSuccess()) {
            omsExecuteResult.setBaseOmsResponse(omsSkuStockGetResponse);
        }

        return omsExecuteResult;
    }

    @Override
    protected BaseOmsRequest getOmsRequest(OmsExecuteBO omsExecuteBo) {
        OmsSkuStockGetRequest omsSkuStockGetRequest = new OmsSkuStockGetRequest(omsConfiguration);

        OmsSkuStockQueryBO omsSkuStockQuery = omsExecuteBo.getOmsSkuStockQuery();
        omsSkuStockGetRequest.setVirtualWarehouseCode(omsSkuStockQuery.getVirtualWarehouseCode());
        omsSkuStockGetRequest.setProductCodeLists(omsSkuStockQuery.getSkuCodes());
        return omsSkuStockGetRequest;
    }

    @Override
    protected Boolean initializable() {
        return Boolean.FALSE;
    }

    /**
     * 预校验
     *
     * @param omsExecuteBo
     * @return
     */
    private Boolean preCheck(OmsExecuteBO omsExecuteBo) {
        OmsSkuStockQueryBO omsSkuStockQuery = omsExecuteBo.getOmsSkuStockQuery();
        if (Objects.isNull(omsSkuStockQuery)
                || StringUtils.isBlank(omsSkuStockQuery.getVirtualWarehouseCode())
                || CollectionUtils.isEmpty(omsSkuStockQuery.getSkuCodes())
        ) {
            LOGGER.info("[OMS系统]OMS获取商品虚拟仓库库存信息失败，参数错误。 omsExecuteBo:{}", JSON.toJSONString(omsExecuteBo));
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }
}
