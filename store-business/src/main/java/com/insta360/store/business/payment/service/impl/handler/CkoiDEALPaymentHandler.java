package com.insta360.store.business.payment.service.impl.handler;

import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.lib.checkout.CheckoutConfig;
import com.insta360.store.business.payment.lib.checkout.CheckoutHkConfiguration;
import com.insta360.store.business.payment.lib.checkout.model.Source;
import com.insta360.store.business.payment.lib.checkout.request.BaseCkoPaymentRequest;
import com.insta360.store.business.payment.lib.checkout.request.payment.CreateCkoIdealPaymentRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2021/11/23
 * @Description:
 */
@Scope("prototype")
@Component
public class CkoiDEALPaymentHandler extends BaseCkoPaymentNormalHandler {

    @Autowired
    CheckoutHkConfiguration checkoutHkConfiguration;

    @Override
    protected BaseCkoPaymentRequest newPaymentRequest() {
        return new CreateCkoIdealPaymentRequest();
    }

    @Override
    protected String getCreditCardNumber(String cardNumberSign) {
        return null;
    }

    @Override
    protected String getCreditCardCvv(String cardCvvSign) {
        return null;
    }

    @Override
    protected CheckoutConfig getCheckoutConfigInfo() {
        return checkoutHkConfiguration;
    }

    @Override
    protected BaseCkoPaymentRequest buildPaymentRequest(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        BaseCkoPaymentRequest request = super.buildPaymentRequest(paymentInfo, paymentExtra);
        Source source = request.getSource();
        source.setDescription("insta360 iDEAL");
        source.setBic(paymentExtra.getCkoIdealBic());
        request.setSource(source);
        return request;
    }

    @Override
    public PaymentChannel getPaymentChannel() {
        return PaymentChannel.cko_iDEAL;
    }
}
