package com.insta360.store.business.trade.bo;

import com.alibaba.fastjson.JSONArray;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/8/9
 */
public class PaymentChannelDiscountContextBO implements Serializable {

    /**
     * 付费物料
     */
    private JSONArray items;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 交易券Code
     */
    private String tradeCode;

    /**
     * 预选支付渠道
     */
    private String prePaymentChannel;

    /**
     * 下单国家
     */
    private InstaCountry country;

    /**
     * 语言
     */
    private InstaLanguage language;

    /**
     * 订单号
     */
    private String orderNumber;

    public JSONArray getItems() {
        return items;
    }

    public void setItems(JSONArray items) {
        this.items = items;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public String getPrePaymentChannel() {
        return prePaymentChannel;
    }

    public void setPrePaymentChannel(String prePaymentChannel) {
        this.prePaymentChannel = prePaymentChannel;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public void setCountry(InstaCountry country) {
        this.country = country;
    }

    public InstaLanguage getLanguage() {
        return language;
    }

    public void setLanguage(InstaLanguage language) {
        this.language = language;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    @Override
    public String toString() {
        return "PaymentChannelDiscountContextBO{" +
                "items=" + items +
                ", contactEmail='" + contactEmail + '\'' +
                ", tradeCode='" + tradeCode + '\'' +
                ", prePaymentChannel='" + prePaymentChannel + '\'' +
                ", country=" + country +
                ", language=" + language +
                ", orderNumber=" + orderNumber +
                '}';
    }
}
