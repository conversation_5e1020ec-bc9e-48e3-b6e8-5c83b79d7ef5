package com.insta360.store.business.forter;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wbt
 * @Date: 2021/04/28
 * @Description: forter配置
 */
@RefreshScope
@Configuration
public class ForterConfiguration {

    @Value("${payment.forter.request_url}")
    private String request_url;

    @Value("${payment.forter.site_id}}")
    private String site_id;

    @Value("${payment.forter.secret_key}")
    private String secret_key;

    @Value("${payment.forter.api_version}")
    private String api_version;

    public String getRequest_url() {
        return request_url;
    }

    public String getSite_id() {
        return site_id;
    }

    public String getSecret_key() {
        return secret_key;
    }

    public String getApi_version() {
        return api_version;
    }
}
