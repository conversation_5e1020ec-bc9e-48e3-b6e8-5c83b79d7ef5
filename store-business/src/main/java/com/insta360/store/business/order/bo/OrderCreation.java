package com.insta360.store.business.order.bo;

import com.insta360.store.business.cloud.bo.BenefitDiscountResultBO;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.discount.dto.bo.DiscountCheckResult;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.model.OrderShippingFeeRecord;
import com.insta360.store.business.reseller.dto.condition.ResellerCode;

import java.util.List;

/**
 * @Author: mowi
 * @Date: 2019-04-02
 * @Description:
 */
public class OrderCreation {

    /**
     * 订单信息明细
     */
    private OrderSheet orderSheet;

    /**
     * 商品子项明细
     */
    private List<OrderItem> orderItems;

    /**
     * 赠品子项明细
     */
    private List<OrderItem> giftItems;

    /**
     * 支付明细
     */
    private OrderPayment orderPayment;

    /**
     * 预估发货的天数
     */
    private Integer estimateDays;

    /**
     * 支付渠道
     */
    private String paymentChannel;

    /**
     * 折扣校验结果
     */
    private DiscountCheckResult discountCheckResult;

    /**
     * 分销商
     */
    private ResellerCode resellerCode;

    /**
     * 订单运费毛重记录
     */
    private List<OrderShippingFeeRecord> orderShippingFeeRecords;

    /**
     * 云服务-配件折扣商品明细
     */
    private BenefitDiscountResultBO benefitDiscountResult;

    /**
     * 是否权益期内
     */
    private Boolean benefitPeriod;

    /**
     * 云服务订单标记
     */
    private Boolean cloudSubscribeOrderMark;

    /**
     * psp 订单标记
     */
    private Boolean pspOrderMark;

    /**
     * 订阅场景类型
     *
     * @see ServiceScenesType
     */
    private ServiceScenesType serviceScenesType;

    public Boolean getBenefitPeriod() {
        return benefitPeriod;
    }

    public void setBenefitPeriod(Boolean benefitPeriod) {
        this.benefitPeriod = benefitPeriod;
    }

    public OrderSheet getOrderSheet() {
        return orderSheet;
    }

    public void setOrderSheet(OrderSheet orderSheet) {
        this.orderSheet = orderSheet;
    }

    public List<OrderItem> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<OrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    public List<OrderItem> getGiftItems() {
        return giftItems;
    }

    public void setGiftItems(List<OrderItem> giftItems) {
        this.giftItems = giftItems;
    }

    public OrderPayment getOrderPayment() {
        return orderPayment;
    }

    public void setOrderPayment(OrderPayment orderPayment) {
        this.orderPayment = orderPayment;
    }

    public String getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(String paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public DiscountCheckResult getDiscountCheckResult() {
        return discountCheckResult;
    }

    public void setDiscountCheckResult(DiscountCheckResult discountCheckResult) {
        this.discountCheckResult = discountCheckResult;
    }

    public Integer getEstimateDays() {
        return estimateDays;
    }

    public void setEstimateDays(Integer estimateDays) {
        this.estimateDays = estimateDays;
    }

    public ResellerCode getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(ResellerCode resellerCode) {
        this.resellerCode = resellerCode;
    }

    public List<OrderShippingFeeRecord> getOrderShippingFeeRecords() {
        return orderShippingFeeRecords;
    }

    public void setOrderShippingFeeRecords(List<OrderShippingFeeRecord> orderShippingFeeRecords) {
        this.orderShippingFeeRecords = orderShippingFeeRecords;
    }

    public BenefitDiscountResultBO getBenefitDiscountResult() {
        return benefitDiscountResult;
    }

    public void setBenefitDiscountResult(BenefitDiscountResultBO benefitDiscountResult) {
        this.benefitDiscountResult = benefitDiscountResult;
    }

    public Boolean getCloudSubscribeOrderMark() {
        return cloudSubscribeOrderMark;
    }

    public void setCloudSubscribeOrderMark(Boolean cloudSubscribeOrderMark) {
        this.cloudSubscribeOrderMark = cloudSubscribeOrderMark;
    }

    public ServiceScenesType getServiceScenesType() {
        return serviceScenesType;
    }

    public void setServiceScenesType(ServiceScenesType serviceScenesType) {
        this.serviceScenesType = serviceScenesType;
    }

    public Boolean getPspOrderMark() {
        return pspOrderMark;
    }

    public void setPspOrderMark(Boolean pspOrderMark) {
        this.pspOrderMark = pspOrderMark;
    }

    @Override
    public String toString() {
        return "OrderCreation{" +
                "orderSheet=" + orderSheet +
                ", orderItems=" + orderItems +
                ", giftItems=" + giftItems +
                ", orderPayment=" + orderPayment +
                ", estimateDays=" + estimateDays +
                ", paymentChannel='" + paymentChannel + '\'' +
                ", discountCheckResult=" + discountCheckResult +
                ", resellerCode=" + resellerCode +
                ", orderShippingFeeRecords=" + orderShippingFeeRecords +
                ", benefitDiscountResult=" + benefitDiscountResult +
                ", benefitPeriod=" + benefitPeriod +
                ", cloudSubscribeOrderMark=" + cloudSubscribeOrderMark +
                ", pspOrderMark=" + pspOrderMark +
                ", serviceScenesType=" + serviceScenesType +
                '}';
    }
}
