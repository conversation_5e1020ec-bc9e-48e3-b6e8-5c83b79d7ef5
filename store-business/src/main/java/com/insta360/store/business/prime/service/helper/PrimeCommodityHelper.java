package com.insta360.store.business.prime.service.helper;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.model.CommodityDisplayCountry;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityDisplayCountryService;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.prime.bo.PrimeCreateCommodityBO;
import com.insta360.store.business.prime.constants.PrimeConstants;
import com.insta360.store.business.prime.error.PrimeErrorCode;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Prime商品助手类
 * <p>
 * 该类提供了创建Prime商品所需的辅助方法，包括组装商品信息、获取图片URL和标题等。
 * 主要用于辅助PrimeCommodityServiceImpl完成商品创建过程中的数据准备工作。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
@Component
public class PrimeCommodityHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrimeCommodityHelper.class);

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    CommodityDisplayCountryService commodityDisplayCountryService;

    /**
     * 打包Prime商品创建所需的业务对象
     * <p>
     * 该方法从商品对象中提取创建Prime商品所需的关键信息，包括：
     * 1. 获取商品图片URL（优先选择美国地区的展示图片）
     * 2. 组装商品标题（产品名称 + 分隔符 + 套餐名称）
     * </p>
     *
     * @param commodity 商品对象
     * @return Prime商品创建业务对象
     * @throws InstaException 当缺少必要参数时抛出异常
     */
    @TraceLog(logPrefixSub = "封装Prime商品创建所需的业务BO")
    public PrimeCreateCommodityBO packPrimeCreateCommodityBO(Commodity commodity) {
        Integer commodityId = commodity.getId();
        LOGGER.info("开始打包Prime商品创建业务对象，商品ID: {}", commodityId);

        // 获取商品展示图片列表
        List<CommodityDisplay> commodityDisplays = commodityDisplayService.getDisplays(commodityId);
        if (CollectionUtils.isEmpty(commodityDisplays)) {
            LOGGER.error("缺少商品展示图片，商品ID: {}", commodityId);
            throw new InstaException(PrimeErrorCode.PRIME_COMMODITY_PARAMS_DEFICIENCY);
        }
        // 获取所有展示图片ID
        List<Integer> displayIds = commodityDisplays.stream().map(CommodityDisplay::getId).collect(Collectors.toList());
        LOGGER.info("商品展示图片ID列表: {}", displayIds);

        // 获取美国地区可用的展示图片
        List<CommodityDisplayCountry> commodityDisplayCountries = commodityDisplayCountryService.listByDisplayIds(displayIds, InstaCountry.US);
        List<Integer> existDisplayIds = commodityDisplayCountries.stream().map(CommodityDisplayCountry::getDisplayId).collect(Collectors.toList());
        LOGGER.info("美国地区可用的展示图片ID: {}", existDisplayIds);

        // 优先选择美国地区的展示图片，如果没有则使用第一张图片
        Optional<CommodityDisplay> displayOptional = commodityDisplays.stream()
                .filter(commodityDisplay -> existDisplayIds.contains(commodityDisplay.getId()))
                .findFirst();

        String url = displayOptional.map(CommodityDisplay::getUrl).orElse(commodityDisplays.get(0).getUrl());
        LOGGER.info("选择的商品图片URL: {}", url);

        // 获取商品信息中的标题（英文）
        CommodityInfo commodityInfo = commodityInfoService.getInfo(commodityId, InstaLanguage.en_US);
        if (commodityInfo == null) {
            LOGGER.error("缺少商品英文信息，商品ID: {}", commodityId);
            throw new InstaException(PrimeErrorCode.PRIME_COMMODITY_PARAMS_DEFICIENCY);
        }

        // 获取产品信息中的标题（英文）
        ProductInfo productInfo = productInfoService.getInfo(commodity.getProduct(), InstaLanguage.en_US);
        if (productInfo == null) {
            LOGGER.error("缺少产品英文信息，产品ID: {}", commodity.getProduct());
            throw new InstaException(PrimeErrorCode.PRIME_COMMODITY_PARAMS_DEFICIENCY);
        }

        // 组装完整标题：产品名称 + 分隔符 + 套餐名称
        String title = productInfo.getName() + PrimeConstants.Commodity.TITLE_SEPARATOR + commodityInfo.getName();
        LOGGER.info("组装的商品标题: {}", title);

        // 创建并填充Prime商品创建业务对象
        PrimeCreateCommodityBO primeCreateCommodityBO = new PrimeCreateCommodityBO();
        primeCreateCommodityBO.setSourceImage(url);
        primeCreateCommodityBO.setTitle(title);

        Product product = productService.getById(commodity.getProduct());

        String detailUrl = String.format("https://store.insta360.com/product/%s?c=%s", product.getUrlKey(), commodity.getId());
        primeCreateCommodityBO.setDetailUrl(detailUrl);

        LOGGER.info("Prime商品创建业务对象打包完成，商品ID: {} bo:{}", commodityId, primeCreateCommodityBO);
        return primeCreateCommodityBO;

    }
}
