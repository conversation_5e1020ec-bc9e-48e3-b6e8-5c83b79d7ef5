package com.insta360.store.business.trade.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.trade.model.UserCart;
import org.apache.ibatis.annotations.Update;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: hyc
 * @Date: 2019/2/25
 * @Description:
 */
public interface UserCartDao extends BaseDao<UserCart> {

    /**
     * 批量同步购物车用户ID
     *
     * @param limit 限制
     * @return {@link int}
     */
    @Update("UPDATE `trade_user_cart` o " +
            "SET o.user_id = (SELECT ua.insta_account FROM user_account ua WHERE ua.id = o.account) " +
            "WHERE o.id IN ( " +
            "    SELECT id FROM ( " +
            "        SELECT o1.id FROM `trade_user_cart` o1 " +
            "        WHERE o1.user_id <> (SELECT ua.insta_account FROM user_account ua WHERE ua.id = o1.account) " +
            "        LIMIT #{limit} " +
            "    ) t " +
            ") ")
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = RuntimeException.class)
    int batchSyncUserCart(Integer limit);
}
