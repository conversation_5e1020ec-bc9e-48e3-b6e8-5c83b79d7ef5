package com.insta360.store.business.prime.enums;

/**
 * Amazon Buy with Prime 商品操作类型枚举
 *
 * <p>
 * 用于定义对 Amazon Buy with Prime 平台商品的操作类型：
 * - Create: 创建新的商品
 * - Update: 更新已有商品
 * - Delete: 删除商品
 * - Enable: 启用商品的 Prime 服务
 * - Disable: 禁用商品的 Prime 服务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 * @see <a href="https://documents.buywithprime.amazon.com/bwp-api/docs/buy-with-prime-api-overview">Amazon Buy with Prime API</a>
 */
public enum PrimeCommodityHandleType {

    /**
     * 创建新的商品
     * <p>
     * 在 Amazon Buy with Prime 平台创建新的商品项。
     * 可以创建 Individual 或 Bundle 类型的商品。
     * </p>
     */
    Create("create", "创建"),

    /**
     * 更新已有商品
     * <p>
     * 更新 Amazon Buy with Prime 平台已存在的商品信息。
     * 可以更新商品的各种属性，如价格、描述等。
     * </p>
     */
    Update("update", "更新"),

    ;

    /**
     * 操作类型代码
     * 对应 Amazon Buy with Prime API 中的操作标识符
     */
    private final String code;

    /**
     * 操作类型名称
     * 用于界面显示的中文名称
     */
    private final String name;

    PrimeCommodityHandleType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据操作代码匹配枚举值
     *
     * @param code 操作代码，如 "create" 或 "update"
     * @return 匹配的枚举值，如果没有匹配项则返回 null
     */
    public static PrimeCommodityHandleType matchCode(String code) {
        if (code == null) {
            return null;
        }

        for (PrimeCommodityHandleType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取操作类型代码
     *
     * @return 操作代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取操作类型名称
     *
     * @return 操作名称
     */
    public String getName() {
        return name;
    }
}
