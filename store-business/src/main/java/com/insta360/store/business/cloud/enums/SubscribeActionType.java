package com.insta360.store.business.cloud.enums;

/**
 * @Author: wkx
 * @Date: 2024/06/24
 * @Description:
 */
public enum SubscribeActionType {

    /**
     * 更新卡信息
     */
    UPDATE_CARD("updateCard"),

    /**
     * 重新订阅
     */
    RENEW_SUBSCRIBE("renewSubscribe");

    private final String name;

    SubscribeActionType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
