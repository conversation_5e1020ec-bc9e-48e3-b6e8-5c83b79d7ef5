package com.insta360.store.business.user.enums;

/**
 * @description: 订阅来源
 * @author: py
 * @create: 2022-04-11 17:09
 */
public enum EmailSubscribeOriginEnum {

    /**
     * 商城订阅入口
     */
    SHOPPING_MALL(1, "商城订阅入口"),
    /**
     * 官网订阅入口
     */
    OFFICIAL_WEBSITE(2, "官网订阅入口");

    /**
     *区分入口的代码
     */
    private final Integer code;

    /**
     *具体入口描述
     */
    private final String detail;

    EmailSubscribeOriginEnum(Integer code, String detail) {
        this.code = code;
        this.detail = detail;
    }

    public Integer getCode() {
        return code;
    }

    public String getDetail() {
        return detail;
    }
}
