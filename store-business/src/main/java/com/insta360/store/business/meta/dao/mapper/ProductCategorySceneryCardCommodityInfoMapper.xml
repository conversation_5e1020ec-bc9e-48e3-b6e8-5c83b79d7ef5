<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.meta.dao.ProductCategorySceneryCardCommodityInfoDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.meta.dao.ProductCategorySceneryCardCommodityInfoDao"/>

    <insert id="saveCommodityInfos">
        insert into product_category_scenery_card_commodity_info (scenery_card_id, language, commodity_name)
        values
        <foreach collection="categorySceneryCardCommodityInfos" item="commodityInfo" separator=",">
            (
            #{commodityInfo.sceneryCardId},
            #{commodityInfo.language},
            #{commodityInfo.commodityName}
            )
        </foreach>
    </insert>
</mapper>