package com.insta360.store.business.meta.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public enum ActivityStatusEnum {

    /**
     * 草稿状态
     */
    DRAFT("DRAFT", "草稿状态"),

    /**
     * 正式发布
     */
    RELEASE("RELEASE", "正式发布");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String desc;

    ActivityStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ActivityStatusEnum{" +
                "code='" + code + '\'' +
                ", name='" + desc + '\'' +
                '}';
    }

    /**
     * 根据code获取
     * @param code
     * @return
     */
    public static ActivityStatusEnum matchCode(String code){
        for (ActivityStatusEnum activityStatusEnum : ActivityStatusEnum.values()) {
            if (activityStatusEnum.getCode().equals(code)) {
                return activityStatusEnum;
            }
        }
        return null;
    }
}
