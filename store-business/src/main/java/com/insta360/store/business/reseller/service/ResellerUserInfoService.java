package com.insta360.store.business.reseller.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.reseller.model.ResellerUserInfo;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-07-09
 * @Description:
 */
public interface ResellerUserInfoService extends BaseService<ResellerUserInfo> {

    /**
     * 保存分销商用户信息
     *
     * @param resellerUserInfo
     * @return
     */
    ResellerUserInfo saveResellerUserInfo(ResellerUserInfo resellerUserInfo);

    /**
     * 修改分销商用户信息
     *
     * @param resellerUserInfo
     * @return
     */
    void updateByAccount(ResellerUserInfo resellerUserInfo);

    /**
     * 获取分销用户的基本信息
     *
     * @param resellerId
     * @return
     */
    ResellerUserInfo getResellerUserInfo(Integer resellerId);

    /**
     * 根据分销商ID获取分销商用户信息
     *
     * @param resellerId
     * @return
     */
    ResellerUserInfo getUserInfoByResellerAutoId(Integer resellerId);
}
