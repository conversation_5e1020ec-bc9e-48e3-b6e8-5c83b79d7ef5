package com.insta360.store.business.rma.bo;

import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.rma.model.RmaOrder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/2/28
 */
public class RefundApplyWebBO implements Serializable {

    /**
     * 商城订单
     */
    private Order order;

    /**
     * 订单售后商品
     */
    private OrderItem orderItem;

    /**
     * 售后单基础信息
     */
    private RmaOrder rmaOrder;

    /**
     * BO转换
     *
     * @param order
     * @param orderItem
     * @param rmaOrder
     * @return
     */
    public static RefundApplyWebBO convert(Order order, OrderItem orderItem, RmaOrder rmaOrder) {
        RefundApplyWebBO refundApplyWebBo = new RefundApplyWebBO();
        refundApplyWebBo.setOrder(order);
        refundApplyWebBo.setOrderItem(orderItem);
        refundApplyWebBo.setRmaOrder(rmaOrder);
        return refundApplyWebBo;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public OrderItem getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(OrderItem orderItem) {
        this.orderItem = orderItem;
    }

    public RmaOrder getRmaOrder() {
        return rmaOrder;
    }

    public void setRmaOrder(RmaOrder rmaOrder) {
        this.rmaOrder = rmaOrder;
    }

    @Override
    public String toString() {
        return "RefundApplyWebBO{" +
                "order=" + order +
                ", orderItem=" + orderItem +
                ", rmaOrder=" + rmaOrder +
                '}';
    }
}
