<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.product.dao.ProductFaqDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.product.dao.ProductFaqDao"/>

    <insert id="saveProductFaqs">
        insert into product_faq(language,question_id,question,answer) values
        <foreach collection="productFaqs" item="faq" separator="," close=";">
            (
            #{faq.language},#{faq.questionId},#{faq.question},#{faq.answer}
            )
        </foreach>
    </insert>

</mapper>