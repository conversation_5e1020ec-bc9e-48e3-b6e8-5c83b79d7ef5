package com.insta360.store.business.discount.enums.old;

import com.insta360.store.business.discount.model.Coupon;
import com.insta360.store.business.discount.model.GiftCard;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * @Author: smile
 * @Date: 2022/4/26
 * @Description: 交易券折扣类型
 */
public enum DiscountType {

    unknown(0,"unknown","未知"),

    // 满减优惠
    price_achieve_discount(1,"price_achieve_discount","可叠加优惠(订单维度)"),

    // 指定商品免单（精确匹配什么套餐多少个）
    free_commodity_discount(2,"free_commodity_discount","免单优惠"),

    // 可叠加优惠（套餐维度）
    superimposed_discount(3,"superimposed_discount","可叠加优惠（套餐维度）"),

    // 取最低优惠（套餐维度）
    min_discount(4,"min_discount","不可叠加优惠（套餐维度）"),

    // 订单维度不可叠加
    order_min_discount(5,"order_min_discount","不可叠加优惠(订单维度)");

    public final int type;

    public final String value;

    public final String name;

    DiscountType(int type, String value, String name) {
        this.type = type;
        this.value = value;
        this.name = name;
    }

    /**
     * 匹配折扣类型
     * @param value
     * @return
     */
    public static DiscountType parse(String value) {
        if(StringUtils.isBlank(value)) {
            return unknown;
        }

        for (DiscountType discountType : DiscountType.values()) {
            if(discountType.value.equals(value)) {
                return discountType;
            }
        }
        return unknown;
    }

    /**
     * 匹配折扣类型
     * @param type
     * @return
     */
    public static DiscountType matchType(Integer type) {
        if(Objects.isNull(type)) {
            return unknown;
        }

        for (DiscountType discountType : DiscountType.values()) {
            if(discountType.type == type) {
                return discountType;
            }
        }
        return unknown;
    }


    /**
     * 是否是灵活折扣
     *
     * @param discountType
     * @return
     */
    public static boolean isFlexibleDiscount(DiscountType discountType) {
        return DiscountType.min_discount.equals(discountType) || DiscountType.superimposed_discount.equals(discountType);
    }

    /**
     * 是否订单维度折扣
     * @param discountType
     * @return
     */
    public static boolean isOrderDiscount(DiscountType discountType) {
        return DiscountType.order_min_discount.equals(discountType) || DiscountType.price_achieve_discount.equals(discountType);
    }

    /**
     * 是否展示文案
     *
     * @param discountType
     * @return
     */
    public static <TC> boolean isShowText(DiscountType discountType, TC tradeCode) {
        return DiscountType.free_commodity_discount.equals(discountType) && tradeCodeAvailable(tradeCode);
    }

    /**
     * 不需要抛异常
     *
     * @param discountType
     * @return
     */
    public static boolean isInvalidFeeDiscount(DiscountType discountType) {
        return DiscountType.free_commodity_discount.equals(discountType) || DiscountType.order_min_discount.equals(discountType);
    }

    /**
     * 校验优惠码是否可用
     *
     * @param tradeCode
     * @return
     */
    private static <TC> boolean tradeCodeAvailable(TC tradeCode) {
        if (tradeCode instanceof GiftCard) {
            // 判断是否启用
            if (!((GiftCard) tradeCode).isEnable()) {
                return false;
            }

            // 判断是否已使用
            if (((GiftCard) tradeCode).isUsed()) {
                return false;
            }
        }

        if (tradeCode instanceof Coupon) {
            return ((Coupon) tradeCode).isEnable();
        }
        return true;
    }
}
