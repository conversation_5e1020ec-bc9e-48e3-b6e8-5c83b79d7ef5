package com.insta360.store.business.trade.dto.condition;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Map;

/**
 * @Author: hyc
 * @Date: 2019/3/21
 * @Description:
 */
public class StockCheckResult {

    @JSONField(name = "stock_ok")
    private Boolean stockOk;

    @JSONField(name = "stock_detail")
    private Map<Integer, Integer> stockDetail;

    public Boolean getStockOk() {
        return stockOk;
    }

    public void setStockOk(Boolean stockOk) {
        this.stockOk = stockOk;
    }

    public Map<Integer, Integer> getStockDetail() {
        return stockDetail;
    }

    public void setStockDetail(Map<Integer, Integer> stockDetail) {
        this.stockDetail = stockDetail;
    }
}
