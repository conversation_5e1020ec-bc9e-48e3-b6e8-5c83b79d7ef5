package com.insta360.store.business.trade.bo;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.trade.dto.ShippingFeeDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/21
 */
public class ShippingFeeCountBO {

    /**
     * 国家地区
     */
    private InstaCountry country;

    /**
     * 总价格
     */
    private Price totalPrice;

    /**
     * 货币
     */
    private Currency currency;

    /**
     * 订单创建信息
     */
    private OrderCreation orderCreation;

    /**
     * 购买商品信息
     */
    private List<BuyCommodityInfoBO> buyCommodityInfoBoList;

    public ShippingFeeCountBO() {
    }

    public ShippingFeeCountBO(List<ShippingFeeDTO> shippingFeeDtoList) {
        if (CollectionUtils.isEmpty(shippingFeeDtoList)) {
            return;
        }

        List<BuyCommodityInfoBO> buyCommodityInfoBoList = new ArrayList<>(shippingFeeDtoList.size());
        for (ShippingFeeDTO shippingFeeDto : shippingFeeDtoList) {
            BuyCommodityInfoBO buyCommodityInfoBO = new BuyCommodityInfoBO();
            buyCommodityInfoBO.setCommodityId(shippingFeeDto.getCommodityId());
            buyCommodityInfoBO.setNumber(shippingFeeDto.getNumber());
            buyCommodityInfoBoList.add(buyCommodityInfoBO);

            List<ShippingFeeDTO.BinsServices> bindServices = shippingFeeDto.getBindServices();
            if (CollectionUtils.isEmpty(bindServices)) {
                continue;
            }
            List<BuyCommodityInfoBO> buyCommodityInfoBos = bindServices.stream().map(binsServices -> {
                BuyCommodityInfoBO buyCommodityInfoBo = new BuyCommodityInfoBO();
                buyCommodityInfoBo.setCommodityId(binsServices.getCommodityId());
                buyCommodityInfoBo.setNumber(binsServices.getNumber());
                return buyCommodityInfoBo;
            }).collect(Collectors.toList());
            buyCommodityInfoBoList.addAll(buyCommodityInfoBos);
        }
        this.buyCommodityInfoBoList = buyCommodityInfoBoList;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public void setCountry(InstaCountry country) {
        this.country = country;
    }

    public Price getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Price totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Currency getCurrency() {
        return currency;
    }

    public void setCurrency(Currency currency) {
        this.currency = currency;
    }

    public OrderCreation getOrderCreation() {
        return orderCreation;
    }

    public void setOrderCreation(OrderCreation orderCreation) {
        this.orderCreation = orderCreation;
    }

    public List<BuyCommodityInfoBO> getBuyCommodityInfoBoList() {
        return buyCommodityInfoBoList;
    }

    public void setBuyCommodityInfoBoList(List<BuyCommodityInfoBO> buyCommodityInfoBoList) {
        this.buyCommodityInfoBoList = buyCommodityInfoBoList;
    }

    @Override
    public String toString() {
        return "ShippingFeeCountBO{" +
                "country=" + country +
                ", totalPrice=" + totalPrice +
                ", currency=" + currency +
                ", orderCreation=" + orderCreation +
                ", buyCommodityInfoBoList=" + buyCommodityInfoBoList +
                '}';
    }
}
