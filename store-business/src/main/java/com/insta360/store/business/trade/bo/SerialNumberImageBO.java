package com.insta360.store.business.trade.bo;

import java.awt.image.BufferedImage;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/12/30 14:08
 * @Description:
 * @Version 1.0
 */
public class SerialNumberImageBO implements Serializable {
    /**
     * serialNumber 序列号。
     */
    private String serialNumber;

    /**
     * imageUrl 图像的 URL。
     */
    private String imageUrl;

    /**
     * image 图像的缓冲图像。
     */
    private BufferedImage image;

    public SerialNumberImageBO() {
    }

    public SerialNumberImageBO(String serialNumber, String imageUrl) {
        this.serialNumber = serialNumber;
        this.imageUrl = imageUrl;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public BufferedImage getImage() {
        return image;
    }

    public void setImage(BufferedImage image) {
        this.image = image;
    }

    @Override
    public String toString() {
        return "SerialNumberImageBO{" +
                "serialNumber='" + serialNumber + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", image=" + image +
                '}';
    }
}
