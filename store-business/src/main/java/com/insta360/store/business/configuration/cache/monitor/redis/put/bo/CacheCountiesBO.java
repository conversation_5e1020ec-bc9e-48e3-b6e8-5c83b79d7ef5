package com.insta360.store.business.configuration.cache.monitor.redis.put.bo;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2023/09/13
 * @Description:
 */
public class CacheCountiesBO implements Serializable {

    /**
     * 国家
     */
    private InstaCountry country;

    /**
     * 语言
     */
    private InstaLanguage language;

    public CacheCountiesBO() {
    }

    public CacheCountiesBO(InstaCountry country, InstaLanguage language) {
        this.country = country;
        this.language = language;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public void setCountry(InstaCountry country) {
        this.country = country;
    }

    public InstaLanguage getLanguage() {
        return language;
    }

    public void setLanguage(InstaLanguage language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return "CacheCountiesBO{" +
                "country=" + country +
                ", language=" + language +
                '}';
    }
}
