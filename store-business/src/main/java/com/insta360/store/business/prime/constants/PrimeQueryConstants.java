package com.insta360.store.business.prime.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public interface PrimeQueryConstants {

    String CreateProduct = "mutation CreateProduct($input: CreateProductInput!) {\n    createProduct(input: $input) {\n        id\n    }\n}";

    String CreatePurchaseGroup = "mutation createPurchaseGroup($input: CreatePurchaseGroupInput!) {\n    createPurchaseGroup(input: $input) {\n        id\n    }\n}";

    String UpdateProduct = "mutation UpdateProduct(\n\t$identifier: ProductIdentifierInput\n\t$input: UpdateProductInput!\n) {\n    updateProduct(\n        identifier: $identifier\n        input: $input\n    ) {\n        id\n    }\n}";

    String DeleteProduct = "mutation deleteProduct (\n\t$identifier: ProductIdentifierInput\t\n) {\n    deleteProduct(identifier:$identifier) {\n        id\n    }\n}";

    String Product = "query product($identifier: ProductIdentifierInput) {\n  product(identifier: $identifier) {\n    id\n    externalId {\n      value\n    }\n    sku {\n      value\n    }\n    amazonSku {\n      value\n    }\n    offerPrime\n    productDetailPageUrl\n    purchaseGroupMemberships {\n      memberAmount {\n        unit\n        value\n      }\n    }\n    image {\n      displayReadyUrl\n      sourceUrl\n    }\n    buyability {\n      status\n    }\n    representativeOfPurchaseGroup {\n      members {\n        edges {\n          cursor\n          node {\n            memberAmount {\n              unit\n              value\n            }\n            product {\n              ... on Product {\n                externalId {\n                  value\n                }\n              }\n            }\n            productGroup {\n              members {\n                edges {\n                  node {\n                    memberAmount {\n                      unit\n                      value\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}";

    String Products = "query products($first: Int, $after: String) {\n  products(first: $first, after: $after) {\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    edges {\n      cursor\n      node {\n        id\n        ... on Product {\n          id\n          externalId {\n            value\n          }\n        }\n      }\n    }\n  }\n}";

    ;

}
