package com.insta360.store.business.outgoing.mq.order.sender;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.payment.bo.OrderSyncForterBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2022/11/01
 * @Description:
 */
@Component
public class OrderPayedSyncForterMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderSuccessMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_order_payed_sync_forter, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * 订单支付成功同步forter
     *
     * @param orderSyncForterParam
     */
    public void sendOrderPayedSyncForterMessage(OrderSyncForterBO orderSyncForterParam) {
        Order order = orderSyncForterParam.getOrder();
        if (orderSyncForterParam == null || order == null) {
            LOGGER.error("订单支付结果同步forter失败。订单信息缺失。message:{}", orderSyncForterParam);
            return;
        }

        LOGGER.info("send order payed sync forter message. pending... order_number:{}", order.getOrderNumber());

        OrderMessageDTO orderMessage = new OrderMessageDTO();
        orderMessage.setOrderSyncForter(orderSyncForterParam);
        String messageId = rocketTcpMessageSender.sendMessage(JSONObject.toJSONString(orderMessage));
        MqUtils.isBlankMessageIdHandle(messageId, this, orderMessage);

        LOGGER.info("send order payed sync forter message. success... order_number:{}", order.getOrderNumber());
    }
}
