package com.insta360.store.business.commodity.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.commodity.model.CommodityDifferencePointText;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-01-02
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface CommodityDifferencePointTextDao extends BaseDao<CommodityDifferencePointText> {

    void batchInsert(@Param("commodityDifferencePointTextList") List<CommodityDifferencePointText> commodityDifferencePointTextList);

    void batchUpdate(@Param("commodityDifferencePointTextList") List<CommodityDifferencePointText> commodityDifferencePointTextList);
}
