package com.insta360.store.business.trade.bo;

import com.insta360.store.business.order.model.Order;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 订单创建BO
 * @Date 2022/12/6
 */
public class OrderCreateBO implements Serializable {

    /**
     * 订单
     */
    private Order order;

    /**
     * Avalara计税结果
     */
    private CalculateTaxResultBO calculateTaxResult;

    public OrderCreateBO() {
    }

    public OrderCreateBO(Order order, CalculateTaxResultBO calculateTaxResult) {
        this.order = order;
        this.calculateTaxResult = calculateTaxResult;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public CalculateTaxResultBO getCalculateTaxResult() {
        return calculateTaxResult;
    }

    public void setCalculateTaxResult(CalculateTaxResultBO calculateTaxResult) {
        this.calculateTaxResult = calculateTaxResult;
    }

    @Override
    public String toString() {
        return "OrderCreateBO{" +
                "order=" + order +
                ", calculateTaxResult=" + calculateTaxResult +
                '}';
    }
}
