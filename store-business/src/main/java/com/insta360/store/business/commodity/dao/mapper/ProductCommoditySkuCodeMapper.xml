<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.commodity.dao.ProductCommoditySkuCodeDao">

    <!-- 批量插入 -->
    <insert id="batchSaveSkuCodes" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into product_commodity_sku_code (commodity_id, sku_code, sku_code_type)
        values
        <foreach collection="list" item="commoditySkuCode" separator=",">
            (
            #{commoditySkuCode.commodityId},
            #{commoditySkuCode.skuCode},
            #{commoditySkuCode.skuCodeType}
            )
        </foreach>
    </insert>
    
    <select id="selectUnionSingleSkuByCountryCodes" resultType="com.insta360.store.business.commodity.bo.CommoditySkuCodeUnionQueryBO">
        SELECT  p1.id      as 'skuId',
                p4.id      as productId,
                p4.name    as 'productName',
                p4.enabled as 'productEnabled',
                p1.commodity_id as 'commodityId',
                p3.name    as 'commodityName',
                p3.enabled as 'commodityEnabled',
                p1.sku_code as 'skuCode',
                p1.sku_code_type as 'skuCodeType',
                group_concat(p2.country) as 'countryCodes'
        FROM product_commodity_sku_code p1
                  join product_commodity p3 on p1.commodity_id = p3.id
                  join product p4 on p3.product = p4.id
                  join product_commodity_sku_code_country p2 on p1.id = p2.sku_id
        <where>
            <if test="skuCodeType != null">
                and p1.sku_code_type = #{skuCodeType}
            </if>
            <if test="countryCodes != null">
                and p1.id IN (SELECT sku_id
                FROM product_commodity_sku_code_country
                WHERE country IN
                <foreach item="countryCode" collection="countryCodes" separator="," open="(" close=")">
                    #{countryCode}
                </foreach>
                GROUP BY sku_id
                HAVING COUNT(DISTINCT country) = #{countryCount})
                AND p2.country IN
                <foreach item="countryCode" collection="countryCodes" separator="," open="(" close=")">
                    #{countryCode}
                </foreach>
            </if>
        </where>
        group by p1.commodity_id, p1.sku_code;
    </select>
</mapper>