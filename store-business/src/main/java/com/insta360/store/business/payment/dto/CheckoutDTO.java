package com.insta360.store.business.payment.dto;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2/27/24
 * @Description:
 */
public class CheckoutDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * checkout支付通道id
     */
    private Integer paymentChannelId;

    /**
     * Apple Pay token
     */
    private JSONObject innerData;

    /**
     * Google Pay token
     */
    private JSONObject gpInnerData;

    /**
     * 信用卡卡号
     */
    private String cardNumber;

    /**
     * 信用卡年份
     */
    private Integer cardYear;

    /**
     * 信用卡月份
     */
    private Integer cardMonth;

    /**
     * 信用卡CVV
     */
    private String cardCvv;

    /**
     * 持卡人名称
     */
    private String cardName;

    /**
     * 是否走3D校验（信用卡支付）
     */
    private Boolean threeDomainTrade;

    /**
     * 是否3d豁免（信用卡支付）
     */
    private Boolean exemption;

    /**
     * 支付业务模式
     */
    private Integer payMode;

    /**
     * 金额
     */
    private String amount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 支付通道ID
     */
    private String processingChannelId;

    /**
     * flow 流程token
     */
    private String token;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getPaymentChannelId() {
        return paymentChannelId;
    }

    public void setPaymentChannelId(Integer paymentChannelId) {
        this.paymentChannelId = paymentChannelId;
    }

    public JSONObject getInnerData() {
        return innerData;
    }

    public void setInnerData(JSONObject innerData) {
        this.innerData = innerData;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public Integer getCardYear() {
        return cardYear;
    }

    public void setCardYear(Integer cardYear) {
        this.cardYear = cardYear;
    }

    public Integer getCardMonth() {
        return cardMonth;
    }

    public void setCardMonth(Integer cardMonth) {
        this.cardMonth = cardMonth;
    }

    public String getCardCvv() {
        return cardCvv;
    }

    public void setCardCvv(String cardCvv) {
        this.cardCvv = cardCvv;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public Boolean getThreeDomainTrade() {
        return threeDomainTrade;
    }

    public void setThreeDomainTrade(Boolean threeDomainTrade) {
        this.threeDomainTrade = threeDomainTrade;
    }

    public Boolean getExemption() {
        return exemption;
    }

    public void setExemption(Boolean exemption) {
        this.exemption = exemption;
    }

    public JSONObject getGpInnerData() {
        return gpInnerData;
    }

    public void setGpInnerData(JSONObject gpInnerData) {
        this.gpInnerData = gpInnerData;
    }

    public Integer getPayMode() {
        return payMode;
    }

    public void setPayMode(Integer payMode) {
        this.payMode = payMode;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getProcessingChannelId() {
        return processingChannelId;
    }

    public void setProcessingChannelId(String processingChannelId) {
        this.processingChannelId = processingChannelId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return "CheckoutDTO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", paymentChannelId=" + paymentChannelId +
                ", innerData=" + innerData +
                ", gpInnerData=" + gpInnerData +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardYear=" + cardYear +
                ", cardMonth=" + cardMonth +
                ", cardCvv='" + cardCvv + '\'' +
                ", cardName='" + cardName + '\'' +
                ", threeDomainTrade=" + threeDomainTrade +
                ", exemption=" + exemption +
                ", payMode=" + payMode +
                ", amount='" + amount + '\'' +
                ", currency='" + currency + '\'' +
                ", processingChannelId='" + processingChannelId + '\'' +
                ", token='" + token + '\'' +
                '}';
    }
}
