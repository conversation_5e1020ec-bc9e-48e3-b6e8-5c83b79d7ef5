package com.insta360.store.business.commodity.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.commodity.bo.BatchCommodityPriceBO;
import com.insta360.store.business.commodity.dto.BatchCommodityPriceDTO;
import com.insta360.store.business.commodity.model.CommodityPrice;

import java.util.List;
import java.util.Map;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-10-25
 * @Description:
 */
public interface CommodityPriceService extends BaseService<CommodityPrice> {

    /**
     * 获取套餐在所有地区的(包括非启用的)价格
     * @param commodityIds
     * @return
     */
    List<CommodityPrice> listByCommodityIds(List<Integer> commodityIds);

    /**
     * 获取一个套餐在所有地区的(启用的)价格
     * （价格按地区分，除了欧盟地区，其他的地区就是国家）
     *
     * @param commodityIds
     * @return
     */
    List<CommodityPrice> listByCommodityIdEnabled(List<Integer> commodityIds);

    /**
     * 获取套餐在某国家的(启用的)价格
     *
     * @param commodityId
     * @param country
     * @return
     */
    CommodityPrice getPrice(Integer commodityId, InstaCountry country);

    /**
     * 获取套餐在某地区的(启用的)价格
     *
     * @param commodityId
     * @param area
     * @return
     */
    CommodityPrice getAreaPrice(Integer commodityId, String area);

    /**
     * 获取一个套餐在所有地区的(启用的)价格
     * （价格按地区分，除了欧盟地区，其他的地区就是国家）
     *
     * @param commodityId
     * @return
     */
    List<CommodityPrice> getPrices(Integer commodityId);

    /**
     * 获取一个套餐在所有地区的价格（不分是否启用）
     * （价格按地区分，除了欧盟地区，其他的地区就是国家）
     *
     * @param commodityId
     * @return
     */
    List<CommodityPrice> getAllPrices(Integer commodityId);

    /**
     * 启用一个套餐在某地区的某次活动的价格（同时禁用这个套餐在该地区的其他活动的价格）
     *
     * @param commodityPrice
     */
    void enablePrice(CommodityPrice commodityPrice);

    /**
     * 获取套餐在某活动的价格（不分是否启用）
     *
     * @param commodityId
     * @param campaign
     * @return
     */
    List<CommodityPrice> getCampaignPrices(List<Integer> commodityId, String campaign);

    /**
     * 获取套餐在某地区的(所有的)价格
     *
     * @param commodityId
     * @param area
     * @param campaign
     * @return
     */
    CommodityPrice getCampaignPrice(Integer commodityId, String area, String campaign);

    /**
     * 获取金额最多的套餐
     *
     * @param commodityIds
     * @param country
     * @return
     */
    CommodityPrice getMaxPrice(List<Integer> commodityIds, InstaCountry country);

    /**
     * 获取套餐在某国家的(启用的)价格
     *
     * @param commodityIds
     * @param country
     * @return
     */
    List<CommodityPrice> getPriceByCommodityIds(List<Integer> commodityIds, InstaCountry country);

    /**
     * 获取套餐下所有地区的价格（已启用）
     *
     * @param commodityIds
     * @return
     */
    List<CommodityPrice> getPrices(List<Integer> commodityIds);

    /**
     * 新增或更新套餐价格
     *
     * @param commodityPriceData
     */
    void upsertCommodityPrice(CommodityPrice commodityPriceData);

    /**
     * 批量启用套餐在某地区的某次活动的价格（同时禁用套餐在该地区的其他活动的价格）
     *
     * @param subPrices
     */
    void enablePriceBatch(List<CommodityPrice> subPrices);

    /**
     * 套餐价格查询
     *
     * @param commodityIdList
     * @return
     */
    Map<Integer, Float> listPriceByIds(List<Integer> commodityIdList);

    /**
     * 批量更新套餐价格
     *
     * @param updatePriceList
     */
    void batchUpdatePrice(List<CommodityPrice> updatePriceList);

    /**
     * 批量保存套餐价格
     *
     * @param savePriceList
     */
    void batchSavePrice(List<CommodityPrice> savePriceList);

    /**
     * 多条件查询价格数据
     *
     * @param commodityPriceParam
     * @return
     */
    List<BatchCommodityPriceBO> batchExportPrice(BatchCommodityPriceDTO commodityPriceParam);

    /**
     * 根据传入的commodityIds和areas查询出对应的commodityPrice
     * @param productIds
     * @param areas
     * @return
     */
    List<CommodityPrice> listCommodityPriceByProductIdsAndAreas(List<Integer> productIds, List<String> areas);
}
