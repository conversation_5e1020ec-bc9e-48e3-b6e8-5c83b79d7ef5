package com.insta360.store.business.payment.service.impl.aop.payment_notify;

import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.payment.context.PaymentResultDataContext;
import com.insta360.store.business.payment.enums.PaymentResultType;
import com.insta360.store.business.payment.service.PaymentResultService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * @Author: wbt
 * @Date: 2021/06/28
 * @Description:
 */
@Aspect
@Component
public class PaymentNotifyResultHandlerAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentNotifyResultHandlerAspect.class);

    @Autowired
    PaymentResultService paymentResultService;

    /**
     * 记录支付接口的返回参数（notify）
     *
     * @param joinPoint
     * @param pnrh
     */
    @After("@annotation(pnrh)")
    public void after(JoinPoint joinPoint, PaymentNotifyResultHandler pnrh) {
        PaymentResultDataContext paymentResultDataContext = PaymentResultDataContext.get();
        if (paymentResultDataContext == null) {
            LOGGER.error("支付上下文不存在（notify）");
            return;
        }

        // 支付渠道
        PaymentChannel paymentChannel = paymentResultDataContext.getPaymentChannel();
        if (paymentChannel == null) {
            LOGGER.error("Recorded payment notify result fail. payment channel is null");
            return;
        }

        // 订单号
        String orderNumber = paymentResultDataContext.getOrderNumber();
        if (StringUtil.isBlank(orderNumber)) {
            LOGGER.error("Recorded payment notify result fail. payment channel is blank");
            return;
        }

        // 保存返回参数
        paymentResultService.savePaymentResult(paymentChannel, paymentResultDataContext.getResultData(), orderNumber, PaymentResultType.payment_notify);
        LOGGER.info("Recorded payment notify result success. channel:{}, result:{}", paymentChannel, Arrays.toString(joinPoint.getArgs()));
    }
}
