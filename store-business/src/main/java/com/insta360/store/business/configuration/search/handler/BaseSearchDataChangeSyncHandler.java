package com.insta360.store.business.configuration.search.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.enums.CommodityTagType;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.*;
import com.insta360.store.business.commodity.service.*;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.configuration.search.bo.*;
import com.insta360.store.business.configuration.search.constant.SearchConstant;
import com.insta360.store.business.configuration.search.enums.BatchActionType;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.model.*;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.*;
import com.insta360.store.business.meta.service.impl.helper.NavigationBarCategoryHelper;
import com.insta360.store.business.outgoing.mq.search.helper.SearchDataUpdateMessageSendHelper;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductAdapterType;
import com.insta360.store.business.product.model.ProductCategorySubset;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductAdapterTypeService;
import com.insta360.store.business.product.service.ProductCategorySubsetService;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import com.insta360.store.business.review.model.ReviewRateCount;
import com.insta360.store.business.review.service.ReviewRateCountService;
import com.insta360.store.business.search.enums.SearchSource;
import com.insta360.store.business.utils.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/28
 */
public abstract class BaseSearchDataChangeSyncHandler implements SearchDataChangeSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseSearchDataChangeSyncHandler.class);

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    ProductService productService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Autowired
    ProductCategorySubsetService productCategorySubsetService;


    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    @Autowired
    ReviewRateCountService reviewRateCountService;

    @Autowired
    CommodityFunctionDescriptionService commodityFunctionDescriptionService;

    @Autowired
    AdapterTypeInfoService adapterTypeInfoService;

    @Autowired
    AdapterTypeMainService adapterTypeMainService;

    @Autowired
    ProductAdapterTypeService productAdapterTypeService;

    @Autowired
    CommodityTagGroupService commodityTagGroupService;

    @Autowired
    CommodityTagBindService commodityTagBindService;

    @Autowired
    CommodityTagInfoService commodityTagInfoService;

    @Autowired
    ProductCategoryImageTextSelectorService productCategoryImageTextSelectorService;

    @Autowired
    ProductCategoryImageTextInfoService productCategoryImageTextInfoService;

    @Autowired
    ProductCategoryImageTextCommodityService productCategoryImageTextCommodityService;

    @Autowired
    NavigationBarCategoryInsideService navigationBarCategoryInsideService;

    @Autowired
    NavigationBarCategoryHelper navigationBarCategoryHelper;

    @Autowired
    SearchDataUpdateMessageSendHelper searchDataUpdateMessageSendHelper;

    @Autowired
    ProductCommodityStockService productCommodityStockService;

    @Override
    public void syncSearchDataChanges(SearchSyncParametersBO searchSyncParams) {
        // 变更数据模块类型
        String changeType = this.getChangeType();
        try {
            // 操作类型
            BatchActionType batchActionType = searchSyncParams.getBatchActionType();
            if (Objects.isNull(batchActionType)) {
                LOGGER.info("[商城搜索数据同步]操作类型为空，本次数据变更同步操作取消. changeType: {}, searchSyncParams: {}", changeType, JSON.toJSONString(searchSyncParams));
                return;
            }

            LOGGER.info("[商城搜索数据同步]变更数据前置检查开始. changeType:{}, searchSyncParams: {} ", changeType, JSON.toJSONString(searchSyncParams));
            // 数据检查
            SearchChangeDataCheckResultBO searchChangeDataCheckResult = this.changeDataCheck(searchSyncParams);
            LOGGER.info("[商城搜索数据同步]变更数据前置检查结束. changeType:{}, searchSyncParams: {}, changeDataCheckResult:{} ", changeType, JSON.toJSONString(searchSyncParams), JSON.toJSONString(searchChangeDataCheckResult));
            if (Objects.isNull(searchChangeDataCheckResult)) {
                LOGGER.info("[商城搜索数据同步]变更数据检查结果为空，本次数据变更同步操作取消. changeType: {}, searchSyncParams: {}", changeType, JSON.toJSONString(searchSyncParams));
                return;
            }

            // 封装需同步的数据
            List<StoreSearchDataBO> storeSearchDataList = this.prepareSearchData(searchChangeDataCheckResult);
            if (CollectionUtils.isEmpty(storeSearchDataList)) {
                LOGGER.info("[商城搜索数据同步]数据为空，本次数据变更同步操作取消. changeType: {}, searchSyncParams: {}", changeType, JSON.toJSONString(searchSyncParams));
                return;
            }

            LOGGER.info("[商城搜索数据同步]开始执行数据同步，changeType: {}, 变更业务数据: {}, 数据检查结果为：{}", changeType, JSON.toJSONString(searchSyncParams), storeSearchDataList.size());
            Lists.partition(storeSearchDataList, SearchConstant.DEFAULT_SPLIT_NUM).forEach(list -> {
                SearchBaseSyncBO searchBaseSyncBo = new SearchBaseSyncBO(batchActionType.name(), SearchSource.STORE.name(), list);
                // 发送数据同步消息
                searchDataUpdateMessageSendHelper.sendSearchDataSyncMessage(searchBaseSyncBo);
            });

            LOGGER.info("[商城搜索数据同步]结束执行数据同步，changeType: {}, 变更业务数据: {}, 数据检查结果为：{}", changeType, JSON.toJSONString(searchSyncParams), storeSearchDataList.size());
        } catch (Exception e) {
            LOGGER.error(String.format("[商城搜索数据同步]数据同步失败，异常：{%s}, changeType: {%s}, searchSyncParams: %s", e.getMessage(), changeType, JSON.toJSONString(searchSyncParams)), e);
            if (e instanceof InstaException) {
                return;
            }
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城搜索数据同步]数据同步失败，异常：{%s}, changeType: {%s}, searchSyncParams: %s", e.getMessage(), changeType, JSON.toJSONString(searchSyncParams)), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
    }

    /**
     * 获取搜索变更数据类型
     *
     * @return
     */
    abstract String getChangeType();

    /**
     * 变更数据检查
     *
     * @param searchSyncParams
     * @return
     */
    abstract SearchChangeDataCheckResultBO changeDataCheck(SearchSyncParametersBO searchSyncParams);

    /**
     * 准备需要同步的数据
     *
     * @param searchChangeDataCheckResult
     * @return
     */
    protected List<StoreSearchDataBO> prepareSearchData(SearchChangeDataCheckResultBO searchChangeDataCheckResult) {
        // 所有需同步的套餐
        List<Commodity> commodityList = searchChangeDataCheckResult.getCommodityList();
        // 所有需同步的产品
        Map<Integer, Product> productAllMap = searchChangeDataCheckResult.getProductAllMap();

        // 产品套餐Map
        Map<Integer, List<Commodity>> productCommodityAllMap = commodityList.stream().collect(Collectors.groupingBy(Commodity::getProduct));
        // 过滤未创建套餐的产品
        productAllMap.keySet().removeIf(key -> !productCommodityAllMap.containsKey(key));

        // 所有套餐ID
        List<Integer> commodityIdList = commodityList.stream().map(Commodity::getId).distinct().collect(Collectors.toList());

        // 获取上下文
        SearchDataSyncContextBO searchDataSyncContext = this.getSearchDataSyncContext(productAllMap, commodityList, productCommodityAllMap);

        // 需过滤的产品ID
        List<Integer> searchFilterProductIds = this.getSearchFilterProductIds();

        // 需过滤的套餐ID
        List<Integer> searchFilterCommodityIds = this.getSearchFilterCommodityIds();

        List<StoreSearchDataBO> storeSearchDataList = new ArrayList<>(commodityIdList.size());
        for (Map.Entry<Integer, List<Commodity>> entry : productCommodityAllMap.entrySet()) {
            if (searchFilterProductIds.contains(entry.getKey())) {
                continue;
            }
            LOGGER.info("[商城搜索数据同步]开始处理产品ID:{}, 套餐数量:{}", entry.getKey(), entry.getValue().size());
            List<Commodity> commodities = entry.getValue().stream().filter(commodity -> !searchFilterCommodityIds.contains(commodity.getId())).collect(Collectors.toList());
            List<StoreSearchDataBO> searchDataList = this.getSearchContentBO(commodities, searchDataSyncContext);
            if (CollectionUtils.isNotEmpty(searchDataList)) {
                storeSearchDataList.addAll(searchDataList);
            }
        }

        return storeSearchDataList;
    }

    /**
     * 准备搜索上下文
     *
     * @param productAllMap
     * @param commodityList
     * @param productCommodityAllMap 产品套餐映射
     * @return
     */
    protected SearchDataSyncContextBO getSearchDataSyncContext(Map<Integer, Product> productAllMap, List<Commodity> commodityList, Map<Integer, List<Commodity>> productCommodityAllMap) {
        // 产品ID列表
        List<Integer> productIds = Lists.newArrayList(productAllMap.keySet());
        // 产品列表
        List<Product> productList = Lists.newArrayList(productAllMap.values());
        // 套餐ID列表
        List<Integer> commodityIds = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
        // 套餐map
        Map<Integer, Commodity> commodityAllMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, commodity -> commodity));

        // 产品多语言信息
        Map<Integer, Map<InstaLanguage, ProductInfo>> productInfoAllMap = this.getProductInfoMap(productIds);

        // 产品适配机型信息 key-productId
        Map<Integer, Map<InstaLanguage, List<String>>> productAdapterTypeAllMap = this.getProductAdapterTypeMap(productIds);

        // 套餐多语言信息
        Map<Integer, Map<InstaLanguage, CommodityInfo>> commodityInfoAllMap = this.getCommodityInfoMap(commodityIds);

        // 套餐销售状态信息
        Map<Integer, Map<InstaCountry, CommoditySaleState>> commoditySaleStateAllMap = this.getCommoditySaleStateMap(commodityIds);

        // 套餐主图信息
        Map<Integer, CommodityDisplay> commodityDisplayAllMap = this.getCommodityDisplayMap(commodityIds);

        // 套餐功能描述多语言信息
        Map<Integer, Map<InstaLanguage, CommodityFunctionDescription>> commodityFunctionDescriptionAllMap = this.getCommodityFunctionDescriptionMap(commodityIds);

        // 产品新品tag
        Map<Integer, Map<InstaLanguage, Boolean>> productTagMap = this.getCommodityNewTagMap(commodityIds);

        // 产品评论星级
        Map<Integer, Float> productReviewRateCountAllMap = this.getProductReviewRateCountMap(productIds);

        // 产品一级类目 只映射相机
        Map<Integer, ProductCategoryMainType> productCategoryMainTypeMap = this.getProductCategoryMainTypeMap(productIds);

        // 所有产品的一级类目
        Map<Integer, ProductCategoryMainType> productCategoryMainAllMap = this.getProductCategoryMainAllMap(productList);

        // 套餐配件类目分类
        ArrayListMultimap<Integer, Map<InstaCountry, Map<InstaLanguage, String>>> productCategoryImageTextInfoMap = this.getProductCategoryImageTextInfoMap(productList, productCommodityAllMap);

        // 一级导航栏
        Map<Integer, List<NavigationBarCategoryInfo>> searchNavigationBarInfoMap = this.getSearchNavigationBarInfoMap(commodityIds);

        SearchDataSyncContextBO searchDataSyncContext = new SearchDataSyncContextBO();
        searchDataSyncContext.setProductInfoAllMap(productInfoAllMap);
        searchDataSyncContext.setCommodityInfoAllMap(commodityInfoAllMap);
        searchDataSyncContext.setProductAdapterTypeAllMap(productAdapterTypeAllMap);
        searchDataSyncContext.setProductTagAllMap(productTagMap);
        searchDataSyncContext.setCommoditySaleStateAllMap(commoditySaleStateAllMap);
        searchDataSyncContext.setCommodityDisplayAllMap(commodityDisplayAllMap);
        searchDataSyncContext.setProductAllMap(productAllMap);
        searchDataSyncContext.setCommodityFunctionDescriptionAllMap(commodityFunctionDescriptionAllMap);
        searchDataSyncContext.setProductReviewRateCountAllMap(productReviewRateCountAllMap);
        searchDataSyncContext.setCommodityAllMap(commodityAllMap);
        searchDataSyncContext.setProductCategoryImageTextInfoMap(productCategoryImageTextInfoMap);
        searchDataSyncContext.setProductCategoryMainTypeMap(productCategoryMainTypeMap);
        searchDataSyncContext.setProductCategoryMainAllMap(productCategoryMainAllMap);
        searchDataSyncContext.setSearchNavigationBarInfoMap(searchNavigationBarInfoMap);

        return searchDataSyncContext;
    }

    /**
     * 获取产品一级类目
     * @param productList
     * @return
     */
    private Map<Integer, ProductCategoryMainType> getProductCategoryMainAllMap(List<Product> productList) {
        List<String> bindCategoryKeyList = productList.stream().map(Product::getCategoryKey).collect(Collectors.toList());
        List<ProductCategorySubset> productCategorySubsetList = productCategorySubsetService.listByCategorySubsetKeys(bindCategoryKeyList);
        Map<String, String> categoryMap = productCategorySubsetList.stream().collect(Collectors.toMap(ProductCategorySubset::getCategorySubsetKey, ProductCategorySubset::getCategoryMainKey, (v1, v2) -> v1));

        // key 产品ID，value 一级类目
        Map<Integer, ProductCategoryMainType> categoryMallAllMap = new HashMap<>();
        for (Product product : productList) {
            Integer productId = product.getId();
            String categoryKey = product.getCategoryKey();

            // 先直接解析一级类目，如果解析为null说明不是一级类目，则从map的映射中取
            ProductCategoryMainType productCategoryMainType = Optional.ofNullable(ProductCategoryMainType.parse(categoryKey))
                    .orElse(ProductCategoryMainType.parse(categoryMap.get(categoryKey)));
            categoryMallAllMap.put(productId, productCategoryMainType);
        }
        return categoryMallAllMap;
    }

    @Override
    public List<Integer> getSearchFilterProductIds() {
        List<Product> productList = productService.listByCategoryKeyIgnoreEnable(ProductCategoryMainType.CM_INTERIOR.name());
        List<Integer> filterProductIds = productList.stream().map(Product::getId).collect(Collectors.toList());

        String configValue = storeConfigService.getConfigValue(StoreConfigKey.search_filter_product);
        if (StringUtils.isNotBlank(configValue)) {
            List<Integer> filterConfigProductIds = JSONArray.parseArray(configValue, Integer.class);
            filterProductIds.addAll(filterConfigProductIds);
        }

        return filterProductIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Integer> getSearchFilterCommodityIds() {
        // 获取商店配置中关于搜索过滤商品的配置值
        String configValue = storeConfigService.getConfigValue(StoreConfigKey.search_filter_commodity);

        // 如果配置值为空，则返回一个空的列表，否则解析数据
        return StringUtils.isBlank(configValue) ? new ArrayList<>(0) : JSON.parseArray(configValue, Integer.class);

    }

    /**
     * 获取搜索内容
     *
     * @param commodities
     * @param searchDataSyncContext
     * @return
     */
    protected List<StoreSearchDataBO> getSearchContentBO(List<Commodity> commodities, SearchDataSyncContextBO searchDataSyncContext) {
        List<Integer> commodityIds = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        // 套餐价格Map
        Map<InstaCountry, Map<Integer, CommodityPrice>> commodityGlobalPriceMap = Optional.ofNullable(commodityPriceService.getPrices(commodityIds)).filter(CollectionUtils::isNotEmpty).map(commodityPrices -> commodityPrices.stream().collect(Collectors.groupingBy(CommodityPrice::country, Collectors.toMap(CommodityPrice::getCommodityId, commodityPrice -> commodityPrice)))).orElse(Collections.emptyMap());

        // 套餐库存Map
        Map<InstaCountry, Map<Integer, Integer>> commodityGlobalStockMap = commodityBatchHelper.globalStockMapCommodityIds(commodityIds);

        // 套餐销售库存
        Map<Integer, CommodityTradeRule> commodityTradeRuleMap = this.getCommodityTradeRules(commodityIds);
        Map<Integer, Product> productAllMap = searchDataSyncContext.getProductAllMap();
        Map<Integer, Commodity> commodityAllMap = searchDataSyncContext.getCommodityAllMap();
        Map<Integer, Map<InstaLanguage, CommodityInfo>> commodityInfoAllMap = searchDataSyncContext.getCommodityInfoAllMap();
        Map<Integer, Map<InstaLanguage, ProductInfo>> productInfoAllMap = searchDataSyncContext.getProductInfoAllMap();
        Map<Integer, Map<InstaLanguage, CommodityFunctionDescription>> commodityFunctionDescriptionAllMap = searchDataSyncContext.getCommodityFunctionDescriptionAllMap();
        Map<Integer, Float> productReviewRateCountAllMap = searchDataSyncContext.getProductReviewRateCountAllMap();
        Map<Integer, CommodityDisplay> commodityDisplayAllMap = searchDataSyncContext.getCommodityDisplayAllMap();
        Map<Integer, Map<InstaCountry, CommoditySaleState>> commoditySaleStateAllMap = searchDataSyncContext.getCommoditySaleStateAllMap();
        Map<Integer, Map<InstaLanguage, List<String>>> productAdapterTypeAllMap = searchDataSyncContext.getProductAdapterTypeAllMap();
        Map<Integer, Map<InstaLanguage, Boolean>> productTagAllMap = searchDataSyncContext.getProductTagAllMap();
        ArrayListMultimap<Integer, Map<InstaCountry, Map<InstaLanguage, String>>> productCategoryImageTextInfoMap = searchDataSyncContext.getProductCategoryImageTextInfoMap();
        Map<Integer, ProductCategoryMainType> productCategoryMainTypeMap = searchDataSyncContext.getProductCategoryMainTypeMap();
        Map<Integer, ProductCategoryMainType> productCategoryMainAllMap = searchDataSyncContext.getProductCategoryMainAllMap();
        Map<Integer, List<NavigationBarCategoryInfo>> searchNavigationBarInfoMap = searchDataSyncContext.getSearchNavigationBarInfoMap();
        NavigationCategoryBO navigationCategoryBo = searchDataSyncContext.getNavigationCategoryBo();

        List<StoreSearchDataBO> searchInitDataList = new ArrayList<>(commodityIds.size() * SearchConstant.COUNTIES.size());
        for (SearchCountiesBO searchCountiesBo : SearchConstant.COUNTIES) {
            // 当前地区语言
            InstaLanguage language = searchCountiesBo.getLanguage();
            // 当前地区
            InstaCountry country = searchCountiesBo.getCountry();
            // 获取当前地区的套餐价格
            Map<Integer, CommodityPrice> commodityPriceMap = commodityGlobalPriceMap.get(searchCountiesBo.getCountry());

            // 获取当前地区的套餐库存总数
            Map<Integer, Integer> commodityStockMap = commodityGlobalStockMap.get(country);

            // 过滤没有配置多语言名称的产品、套餐、
            // 过滤销售状态= ‘下架’的套餐
            // 过滤没有配置销售价格的套餐
            // 过滤状态‘禁用’ || 标记为‘是新品’ || 标记未‘非正常销售’的产品、套餐
            List<Integer> enableCommodityIdList = commodities.stream().filter(commodity -> this.checkCommodity(commodity, productAllMap)).filter(commodity -> this.checkCommodityInfo(commodity, commodityInfoAllMap, productInfoAllMap, language)).filter(commodity -> this.checkCommoditySaleState(commodity, commoditySaleStateAllMap, commodityPriceMap, country)).map(Commodity::getId).collect(Collectors.toList());

            // 当前spu下是否存在多个有效sku
            boolean isMultipleSku = enableCommodityIdList.size() > 1;
            // 获取最低价格的套餐
            CommodityPrice fromLowestCommodityPrice = null;
            if (isMultipleSku) {
                fromLowestCommodityPrice = this.getFromLowestCommodityPrice(enableCommodityIdList, Lists.newArrayList(commodityPriceMap.values()));
            }
            List<CommodityPrice> allCommodityPriceList = Optional.ofNullable(commodityPriceMap).map(Map::values).map(Lists::newArrayList).orElse(new ArrayList<>());
            List<CommodityPrice> enabledCommodityPriceList = allCommodityPriceList.stream().filter(commodityPrice -> enableCommodityIdList.contains(commodityPrice.getCommodityId())).collect(Collectors.toList());

            for (Integer commodityId : commodityIds) {
                // 套餐基础信息
                Commodity commodity = commodityAllMap.get(commodityId);
                // 套餐多语言
                CommodityInfo commodityInfo = null;
                Map<InstaLanguage, CommodityInfo> commodityInfoMap = commodityInfoAllMap.get(commodityId);
                if (MapUtils.isNotEmpty(commodityInfoMap) && commodityInfoMap.containsKey(language)) {
                    commodityInfo = commodityInfoMap.get(language);
                }

                // 产品多语言
                ProductInfo productInfo = null;
                Product product = productAllMap.get(commodity.getProduct());
                Map<InstaLanguage, ProductInfo> productInfoMap = productInfoAllMap.get(product.getId());
                if (MapUtils.isNotEmpty(productInfoMap) && productInfoMap.containsKey(language)) {
                    productInfo = productInfoMap.get(language);
                }

                // 产品评论星级
                Float reviewRateCount = Objects.nonNull(productReviewRateCountAllMap.get(product.getId())) ? productReviewRateCountAllMap.get(product.getId()) : 0f;
                // 套餐销售状态
                CommoditySaleState commoditySaleState = MapUtils.isEmpty(commoditySaleStateAllMap.get(commodityId)) ? null : commoditySaleStateAllMap.get(commodityId).get(country);
                // 套餐价格
                CommodityPrice commodityPrice = MapUtils.isNotEmpty(commodityPriceMap) ? commodityPriceMap.get(commodityId) : null;
                // 套餐首图
                CommodityDisplay commodityDisplay = commodityDisplayAllMap.get(commodityId);
                // 套餐销售规则
                CommodityTradeRule commodityTradeRule = commodityTradeRuleMap.get(commodityId);
                // 套餐销售库存
                Integer skuStockCount = CommonUtil.isNotEmpty(commodityStockMap) ? commodityStockMap.get(commodityId): 0;

                // 套餐多功能描述
                CommodityFunctionDescription commodityFunctionDescription = null;
                Map<InstaLanguage, CommodityFunctionDescription> commodityFunctionDescriptionMap = commodityFunctionDescriptionAllMap.get(commodityId);
                if (MapUtils.isNotEmpty(commodityFunctionDescriptionMap) && commodityFunctionDescriptionMap.containsKey(language)) {
                    commodityFunctionDescription = commodityFunctionDescriptionMap.get(language);
                }

                // 产品一级类目
                ProductCategoryMainType productCategoryMainType = productCategoryMainTypeMap.get(commodity.getProduct());

                // 待封装的数据
                StoreSearchDataBO storeSearchData = new StoreSearchDataBO();
                SearchContentBO searchContent = new SearchContentBO();
                // 开启数据封装
                this.doPackProductData(storeSearchData, searchContent, product, productInfo, reviewRateCount, productCategoryMainAllMap.get(commodity.getProduct()));
                this.doPackCommodityData(storeSearchData, searchContent, commodity, commoditySaleState, commodityTradeRule, skuStockCount, commodityInfo, commodityFunctionDescription, commodityDisplay);
                // 缺货标签要在套餐价格前
                this.doPackTag(storeSearchData, searchContent, commodity, product, searchCountiesBo, enableCommodityIdList, productTagAllMap, commodityStockMap, commoditySaleStateAllMap);
                this.doPackCommodityPriceData(storeSearchData, searchContent, commodityPrice, fromLowestCommodityPrice, enableCommodityIdList, enabledCommodityPriceList);
                this.doPackFilterInfoData(storeSearchData, searchContent, commodity, searchCountiesBo, productAdapterTypeAllMap, productCategoryImageTextInfoMap);
                this.doPackNavigationInfo(storeSearchData, searchContent, commodity, country, language, searchNavigationBarInfoMap, navigationCategoryBo);
                this.doPackOtherData(storeSearchData, searchContent, country, language, productCategoryMainType);
                searchInitDataList.add(storeSearchData);
            }
        }

        return searchInitDataList;
    }

    /**
     * 获取产品多语言信息
     *
     * @param productIds
     * @return
     */
    protected Map<Integer, Map<InstaLanguage, ProductInfo>> getProductInfoMap(List<Integer> productIds) {
        return Optional.ofNullable(productInfoService.listInfoByProductIds(productIds)).filter(CollectionUtils::isNotEmpty).map(productInfos -> productInfos.stream().collect(Collectors.groupingBy(ProductInfo::getProduct, Collectors.toMap(ProductInfo::language, productInfo -> productInfo)))).orElse(new HashMap<>());
    }

    /**
     * 根据产品ID列表获取产品适配器类型信息映射
     * 此方法旨在通过给定的产品ID列表，返回一个包含各产品适配器类型信息的映射
     * 信息包括不同语言下的适配器类型名称列表
     *
     * @param productIds 产品ID列表，用于查询特定产品的适配器类型
     * @return 返回一个映射，键为产品ID，值为另一个映射，该映射以语言为键，以适配器类型名称列表为值
     */
    protected Map<Integer, Map<InstaLanguage, List<String>>> getProductAdapterTypeMap(List<Integer> productIds) {
        // 获取 ProductAdapterType 列表
        List<ProductAdapterType> adapterTypes = productAdapterTypeService.listByProductIds(productIds);

        // 如果 adapterTypes 为空，直接返回空的 Map
        if (CollectionUtils.isEmpty(adapterTypes)) {
            return Collections.emptyMap();
        }

        // 根据 ProductId 分组
        Map<Integer, List<Integer>> groupedAdapterTypeMap = adapterTypes.stream().collect(Collectors.groupingBy(ProductAdapterType::getProductId, Collectors.mapping(ProductAdapterType::getAdapterTypeId, Collectors.toList())));
        // 全部适配机型ID
        List<Integer> adapterTypeAllIdList = groupedAdapterTypeMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        // 当前产品列表下的所有适配机型
        List<AdapterTypeMain> adapterTypeMainList = adapterTypeMainService.listEnableAdapterTypeByIds(adapterTypeAllIdList);
        if (CollectionUtils.isEmpty(adapterTypeMainList)) {
            return Collections.emptyMap();
        }

        List<Integer> adapterTypeEnableIdList = adapterTypeMainList.stream().map(AdapterTypeMain::getId).collect(Collectors.toList());

        // 创建最终的 productAdapterTypeMap
        Map<Integer, Map<InstaLanguage, List<String>>> productAdapterTypeMap = new HashMap<>(groupedAdapterTypeMap.size());

        groupedAdapterTypeMap.forEach((productId, adapterTypeIds) -> {
            // 过滤被‘禁用’的适配机型
            List<Integer> ids = adapterTypeIds.stream().filter(id -> adapterTypeEnableIdList.contains(id)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ids)) {
                return;
            }
            // 获取 AdapterTypeInfo 列表
            List<AdapterTypeInfo> adapterTypeInfos = adapterTypeInfoService.listEnableByAdapterTypeIds(ids);
            if (CollectionUtils.isNotEmpty(adapterTypeInfos)) {
                // 根据语言分组并映射信息名称
                Map<InstaLanguage, List<String>> languageMap = adapterTypeInfos.stream().filter(adapterTypeInfo -> StringUtils.isNotBlank(adapterTypeInfo.getInfoName())).collect(Collectors.groupingBy(AdapterTypeInfo::language, Collectors.mapping(AdapterTypeInfo::getInfoName, Collectors.toList())));
                if (MapUtils.isNotEmpty(languageMap)) {
                    productAdapterTypeMap.put(productId, languageMap);
                }
            }
        });

        return productAdapterTypeMap;
    }

    /**
     * 获取套餐多语言信息
     *
     * @param commodityIdList
     * @return
     */
    protected Map<Integer, Map<InstaLanguage, CommodityInfo>> getCommodityInfoMap(List<Integer> commodityIdList) {
        // 套餐多语言信息
        return Optional.ofNullable(commodityInfoService.listByIds(commodityIdList)).filter(CollectionUtils::isNotEmpty).map(commodityInfos -> commodityInfos.stream().collect(Collectors.groupingBy(CommodityInfo::getCommodity, Collectors.toMap(CommodityInfo::language, commodityInfo -> commodityInfo)))).orElse(new HashMap<>());
    }

    /**
     * 获取套餐销售状态
     *
     * @param commodityIdList
     * @return
     */
    protected Map<Integer, Map<InstaCountry, CommoditySaleState>> getCommoditySaleStateMap(List<Integer> commodityIdList) {
        return Optional.ofNullable(commoditySaleStateService.listSaleStates(commodityIdList)).filter(CollectionUtils::isNotEmpty).map(commoditySaleStates -> commoditySaleStates.stream().collect(Collectors.groupingBy(CommoditySaleState::getCommodityId, Collectors.toMap(CommoditySaleState::country, commoditySaleState -> commoditySaleState)))).orElse(new HashMap<>());
    }

    /**
     * 获取套餐主图信息
     *
     * @param commodityIdList
     * @return
     */
    protected Map<Integer, CommodityDisplay> getCommodityDisplayMap(List<Integer> commodityIdList) {
        return commodityBatchHelper.listCommodityDisplayMap(commodityIdList);
    }

    /**
     * 获取套餐功能描述
     *
     * @param commodityIds
     * @return
     */
    protected Map<Integer, Map<InstaLanguage, CommodityFunctionDescription>> getCommodityFunctionDescriptionMap(List<Integer> commodityIds) {
        return Optional.ofNullable(commodityFunctionDescriptionService.listFunctionDescriptionByCommodityIds(commodityIds)).filter(CollectionUtils::isNotEmpty).map(commodityFunctionDescriptions -> commodityFunctionDescriptions.stream().collect(Collectors.groupingBy(CommodityFunctionDescription::getCommodityId, Collectors.toMap(CommodityFunctionDescription::language, commodityFunctionDescription -> commodityFunctionDescription)))).orElse(new HashMap<>());
    }

    /**
     * 根据商品ID列表获取商品标签映射
     * 此方法查询与给定商品相关的标签，并组织成多级映射，首先按商品分组，然后按语言分组，最后收集所有标签内容
     *
     * @param commodityIds 套餐ID列表（sku）
     * @return 返回一个映射，结构为 { 套餐ID -> 是否含有新品标签 }
     */
    protected Map<Integer, Map<InstaLanguage, Boolean>> getCommodityNewTagMap(List<Integer> commodityIds) {
        // 获取套餐绑定的tag
        List<CommodityTagBind> commodityTagBinds = commodityTagBindService.listByCommodityIds(commodityIds);
        if (CollectionUtils.isEmpty(commodityTagBinds)) {
            return Collections.emptyMap();
        }

        // 套餐tag分组
        Map<Integer, List<Integer>> commodityTagBindMap = commodityTagBinds.stream().collect(Collectors.groupingBy(CommodityTagBind::getTagId, Collectors.mapping(CommodityTagBind::getCommodityId, Collectors.toList())));

        // 查询新品标签
        List<CommodityTagGroup> commodityTagGroupList = commodityTagGroupService.listNewTag(Lists.newArrayList(commodityTagBindMap.keySet()), CommodityTagType.NEW.getType());
        if (CollectionUtils.isEmpty(commodityTagGroupList)) {
            return Collections.emptyMap();
        }

        // 标签多语言文案
        List<Integer> groupIds = commodityTagGroupList.stream().map(commodityTagGroup -> commodityTagGroup.getId()).distinct().collect(Collectors.toList());
        List<CommodityTagInfo> commodityTagInfoList = commodityTagInfoService.listByGroupIds(groupIds);
        if (CollectionUtils.isEmpty(commodityTagInfoList)) {
            return Collections.emptyMap();
        }

        Map<Integer, Map<InstaLanguage, Boolean>> commodityTagInfoMap = commodityTagInfoList.stream().collect(Collectors.groupingBy(CommodityTagInfo::getTagGroupId, Collectors.toMap(CommodityTagInfo::language, c -> Boolean.TRUE, (oldValue, newValue) -> oldValue)));

        // 构建最终的映射
        Map<Integer, Map<InstaLanguage, Boolean>> commodityNewTagMap = new HashMap<>();
        for (Map.Entry<Integer, List<Integer>> entry : commodityTagBindMap.entrySet()) {
            if (!commodityTagInfoMap.containsKey(entry.getKey())) {
                continue;
            }

            Map<InstaLanguage, Boolean> languageMap = commodityTagInfoMap.get(entry.getKey());
            entry.getValue().forEach(commodityId -> {
                if (commodityNewTagMap.containsKey(commodityId)) {
                    Map<InstaLanguage, Boolean> map = commodityNewTagMap.get(commodityId);
                    map.putAll(languageMap);
                } else {
                    commodityNewTagMap.put(commodityId, languageMap);
                }
            });
        }

        List<Integer> newTagCommodityIdList = new ArrayList<>(commodityNewTagMap.keySet());
        List<Commodity> commodityList = commodityService.listByCommodityIdIgnoreEnable(newTagCommodityIdList);
        Map<Integer, Integer> commodityProductMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getProduct));
        Map<Integer, Map<InstaLanguage, Boolean>> productNewTagMap = new HashMap<>();

        commodityNewTagMap.forEach((commodityId, value) -> {
            Integer productId = commodityProductMap.get(commodityId);
            productNewTagMap.put(productId, value);
        });

        return productNewTagMap;
    }

    /**
     * 根据产品ID列表获取产品评论评分
     * 该方法旨在通过产品ID列表检索每个产品的评论评分计数，如果产品是加权的，则返回加权的评论评分
     *
     * @param productIds 产品ID列表，用于检索对应的评论评分计数
     * @return 返回一个Map，其中键是产品ID，值是该产品的评论评分加权平均值
     */
    protected Map<Integer, Float> getProductReviewRateCountMap(List<Integer> productIds) {
        // 根据产品ID列表获取评论评分计数列表，并将其转换为以产品ID为键的Map
        Map<Integer, ReviewRateCount> reviewRateCountMap = reviewRateCountService.listByProductIds(productIds).stream().collect(Collectors.toMap(ReviewRateCount::getProductId, r -> r));

        // 如果Map为空，则返回一个空的Map
        if (MapUtils.isEmpty(reviewRateCountMap)) {
            return Collections.emptyMap();
        }
        // 获取加权产品配置，用于确定哪些产品使用加权评分
        String weightProductStr = storeConfigService.getConfigValue(StoreConfigKey.review_weight_products);
        // 解析配置字符串为产品ID列表
        List<Integer> weightProducts = StringUtils.isNotBlank(weightProductStr) ? JSONArray.parseArray(weightProductStr).stream().map(productStr -> (Integer) productStr).collect(Collectors.toList()) : new ArrayList<>(0);

        // 返回最终的评论评分Map
        return getReviewMap(reviewRateCountMap, weightProducts);
    }

    /**
     * 根据产品ID列表获取产品一级类目映射（只映射‘相机’类目）
     *
     * @param productIds 产品ID列表
     * @return 返回一个Map，其中键是产品ID，值是该产品的主类型
     */
    private Map<Integer, ProductCategoryMainType> getProductCategoryMainTypeMap(List<Integer> productIds) {
        List<Product> productList = productService.getProducts(productIds);
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.emptyMap();
        }

        Map<Integer, ProductCategoryMainType> productCategoryMainTypeMap = new HashMap<>();
        for (Product product : productList) {
            ProductCategoryMainType categoryMainType = productCategoryHelper.getCategoryMainByKey(product.getCategoryKey());
            if (Objects.nonNull(categoryMainType) && ProductCategoryMainType.CAMERA_MAIN_TYPES.contains(categoryMainType)) {
                productCategoryMainTypeMap.putIfAbsent(product.getId(), ProductCategoryMainType.CM_CAMERA);
            }
        }
        return productCategoryMainTypeMap;
    }

    /**
     * 获取配件类目页分类
     *
     * @param productList
     * @param productCommodityAllMap
     * @return
     */
    protected ArrayListMultimap<Integer, Map<InstaCountry, Map<InstaLanguage, String>>> getProductCategoryImageTextInfoMap(List<Product> productList, Map<Integer, List<Commodity>> productCommodityAllMap) {
        List<ProductCategoryImageTextSelector> imageTextSelectorList = productCategoryImageTextSelectorService.listEnabledByCategoryKey(ProductCategoryMainType.CM_ACCESSORY.name());
        if (CollectionUtils.isEmpty(imageTextSelectorList)) {
            return ArrayListMultimap.create();
        }

        // 移除‘all’
        List<Integer> imageTextSelectorIds = imageTextSelectorList.stream().filter(imageTextSelector -> !imageTextSelector.getInsideName().equals("all")).map(ProductCategoryImageTextSelector::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(imageTextSelectorIds)) {
            return ArrayListMultimap.create();
        }

        // 配件类目页多语言
        Map<Integer, Map<InstaCountry, Map<InstaLanguage, String>>> imageTextSelectorInfoMap = Optional.ofNullable(productCategoryImageTextInfoService.listBySelectorIds(imageTextSelectorIds)).filter(CollectionUtils::isNotEmpty).map(list -> list.stream().collect(Collectors.groupingBy(
                // 按 `selectorId` 分组
                ProductCategoryImageTextInfo::getSelectorId, Collectors.toMap(
                        // key 为 `country`
                        ProductCategoryImageTextInfo::country,
                        // 初始化为单个语言和文本的 Map
                        info -> Collections.singletonMap(info.language(), info.getImageTextName()),
                        // 处理键冲突时合并 `language` -> `imageTextName`
                        (existingMap, newMap) -> {
                            Map<InstaLanguage, String> mergedMap = new HashMap<>(existingMap);
                            newMap.forEach((key, value) ->
                                    // 合并时直接用新值覆盖旧值
                                    mergedMap.merge(key, value, (existingValue, newValue) -> newValue));
                            return mergedMap;
                        })))).orElse(new HashMap<>());

        List<ProductCategoryImageTextCommodity> imageTextCommodityList = productCategoryImageTextCommodityService.listBySelectorIds(imageTextSelectorIds);
        if (CollectionUtils.isEmpty(imageTextCommodityList)) {
            return ArrayListMultimap.create();
        }

        ArrayListMultimap<Integer, Map<InstaCountry, Map<InstaLanguage, String>>> commodityImageTextSelectorInfoMap = ArrayListMultimap.create();
        for (Product product : productList) {
            // 二级类目
            String categoryKey = product.getCategoryKey();
            // 产品下的所有套餐
            List<Commodity> commodityList = productCommodityAllMap.get(product.getId());
            // 套餐ID
            List<Integer> commodityIds = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
            for (ProductCategoryImageTextCommodity imageTextCommodity : imageTextCommodityList) {
                // 获取多语言
                Map<InstaCountry, Map<InstaLanguage, String>> selectorInfoMap = imageTextSelectorInfoMap.get(imageTextCommodity.getSelectorId());
                if (MapUtils.isEmpty(selectorInfoMap)) {
                    continue;
                }
                if (StringUtils.isNotBlank(imageTextCommodity.getCategoryKey()) && imageTextCommodity.getCategoryKey().equals(categoryKey)) {
                    commodityIds.forEach(commodityId -> commodityImageTextSelectorInfoMap.put(commodityId, selectorInfoMap));
                    continue;
                }
                if (commodityIds.contains(imageTextCommodity.getCommodityId())) {
                    commodityImageTextSelectorInfoMap.put(imageTextCommodity.getCommodityId(), selectorInfoMap);
                }
            }
        }

        return commodityImageTextSelectorInfoMap;
    }

    /**
     * 获取搜索导航信息
     *
     * @param commodityIds
     * @return
     */
    private Map<Integer, List<NavigationBarCategoryInfo>> getSearchNavigationBarInfoMap(List<Integer> commodityIds) {
        if (CollectionUtils.isEmpty(commodityIds)){
            return new HashMap<>(0);
        }
        commodityIds = commodityIds.stream().distinct().collect(Collectors.toList());
        // 根据sku 获取所有 spu的套餐信息 skuId, navigationList
        Map<Integer, List<NavigationBarCategoryInfo>> navigationBarInfoMap = navigationBarCategoryHelper.packSearchNavigationBarInfoMap(commodityIds);
        List<Integer> commodityIdList = new ArrayList<>(navigationBarInfoMap.keySet());

        List<Commodity> commodityList = commodityService.listByCommodityIdIgnoreEnable(commodityIdList);
        List<Integer> productIds = commodityList.stream().map(Commodity::getProduct).collect(Collectors.toList());

        List<Commodity> allCommodityList = commodityService.listByProductIds(productIds);
        Map<Integer, Integer> commodityProductIdMap = allCommodityList.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getProduct));
        Map<Integer, List<Integer>> commodityProductGroupMap = allCommodityList.stream().collect(Collectors.groupingBy(Commodity::getProduct, Collectors.mapping(Commodity::getId, Collectors.toList())));

        // 合并sku导航栏信息 组装spu维度  key：productId  value ： List<NavigationBarCategoryInfo>>
        Map<Integer, Set<NavigationBarCategoryInfo>> productNavigationMap = new HashMap<>(allCommodityList.size());
        for (Map.Entry<Integer, List<NavigationBarCategoryInfo>> entry : navigationBarInfoMap.entrySet()) {
            Integer commodityId = entry.getKey();
            Integer productId = commodityProductIdMap.get(commodityId);
            if (productNavigationMap.containsKey(productId)) {
                Set<NavigationBarCategoryInfo> navigationBarCategoryInfos = productNavigationMap.get(productId);
                navigationBarCategoryInfos.addAll(entry.getValue());
                productNavigationMap.put(productId, navigationBarCategoryInfos);
            } else {
                List<NavigationBarCategoryInfo> navigationBarCategoryInfos = navigationBarInfoMap.get(commodityId);
                productNavigationMap.put(productId, new HashSet<>(navigationBarCategoryInfos));
            }

        }

        // 将spu维度  分为sku维度 key：commodityId  value ： List<NavigationBarCategoryInfo>>
        Map<Integer, List<NavigationBarCategoryInfo>> allNavigationBarCategoryMap = new HashMap<>(allCommodityList.size());
        for (Map.Entry<Integer, Set<NavigationBarCategoryInfo>> entry : productNavigationMap.entrySet()) {
            Integer productId = entry.getKey();
            List<NavigationBarCategoryInfo> navigationBarCategoryInfos = new ArrayList<>(entry.getValue());
            List<Integer> commodityIdGroups = commodityProductGroupMap.get(productId);
            if (CollectionUtils.isEmpty(commodityIdGroups)) {
                continue;
            }
            LOGGER.info("[getSearchNavigationBarInfoMap] commodityId:{}, productId:{}, commodityIdGroups:{}", productId, productId, commodityIdGroups);
            for (Integer commodityIdGroup : commodityIdGroups) {
                allNavigationBarCategoryMap.put(commodityIdGroup, navigationBarCategoryInfos);
            }
        }
        return allNavigationBarCategoryMap;

    }

    /**
     * 获取套餐库存规则
     *
     * @param commodityIdList
     * @return
     */
    protected Map<Integer, CommodityTradeRule> getCommodityTradeRules(List<Integer> commodityIdList) {
        return commodityBatchHelper.tradeRuleMapCommodityIds(commodityIdList);
    }

    /**
     * 检查套餐及对应产品状态、是否正常销售等
     *
     * @param commodity
     * @param productMap
     * @return
     */
    private Boolean checkCommodity(Commodity commodity, Map<Integer, Product> productMap) {
        if (Objects.isNull(commodity) || !commodity.getEnabled() || !commodity.getNormalSale()) {
            return false;
        }

        // 获取套餐对应的产品
        Product product = productMap.get(commodity.getProduct());
        return Objects.nonNull(product) && product.getEnabled();
    }

    /**
     * 过滤套餐&产品多语言文案 不予展示
     *
     * @param commodity
     * @param commodityInfoAllMap
     * @param productInfoAllMap
     * @param language
     * @return
     */
    private boolean checkCommodityInfo(Commodity commodity, Map<Integer, Map<InstaLanguage, CommodityInfo>> commodityInfoAllMap, Map<Integer, Map<InstaLanguage, ProductInfo>> productInfoAllMap, InstaLanguage language) {
//        // 判断多语言
//        Map<InstaLanguage, CommodityInfo> commodityInfoMap = commodityInfoAllMap.get(commodity.getId());
//        if (MapUtils.isEmpty(commodityInfoMap) || !commodityInfoMap.containsKey(language)) {
//            return false;
//        }

        Map<InstaLanguage, ProductInfo> productInfoMap = productInfoAllMap.get(commodity.getProduct());
        return MapUtils.isNotEmpty(productInfoMap) && Objects.nonNull(productInfoMap.get(language)) && StringUtils.isNotBlank(productInfoMap.get(language).getName());
    }

    /**
     * 套餐下架 && 没有价格 不予展示
     * 销售状态不存在
     *
     * @param commodity
     * @param commoditySaleStateAllMap
     * @param priceMap
     * @param country
     * @return
     */
    private Boolean checkCommoditySaleState(Commodity commodity, Map<Integer, Map<InstaCountry, CommoditySaleState>> commoditySaleStateAllMap, Map<Integer, CommodityPrice> priceMap, InstaCountry country) {
        Integer commodityId = commodity.getId();
        Map<InstaCountry, CommoditySaleState> commoditySaleStateMap = commoditySaleStateAllMap.get(commodityId);
        if (MapUtils.isEmpty(commoditySaleStateMap) || !commoditySaleStateMap.containsKey(country)) {
            LOGGER.info("[搜索数据套餐同步]套餐销售状态配置信息缺失。套餐Id:{}, 地区:{}", commodityId, country.name());
            return false;
        }
        if (MapUtils.isEmpty(priceMap) || !priceMap.containsKey(commodityId)) {
            LOGGER.info("[搜索数据套餐同步]套餐价格配置信息缺失。套餐Id:{}, 地区:{} ", commodityId, country.name());
            return false;
        }
        // 套餐销售状态
        CommoditySaleState commoditySaleState = commoditySaleStateMap.get(country);
        // 过滤掉下架的套餐
        CommodityPrice commodityPrice = priceMap.get(commodityId);
        if (Objects.isNull(commoditySaleState) || SaleState.remove.equals(commoditySaleState.parseSaleState())) {
            return false;
        }

        // 过滤掉没有配置价格或者价格未启用的套餐
        if (Objects.isNull(commodityPrice) || !commodityPrice.getEnabled() || Objects.isNull(commodityPrice.getAmount()) || Objects.isNull(commodityPrice.getOriginAmount())) {
            return false;
        }

        return true;
    }

    /**
     * 获取最低价格
     *
     * @param enableCommodityIdList
     * @param commodityPriceList
     * @return
     */
    protected CommodityPrice getFromLowestCommodityPrice(List<Integer> enableCommodityIdList, List<CommodityPrice> commodityPriceList) {
        return commodityPriceList.stream().filter(commodityPrice -> enableCommodityIdList.contains(commodityPrice.getCommodityId())).min(Comparator.comparing(CommodityPrice::getAmount)).get();
    }


    /**
     * 封装产品数据
     *
     * @param storeSearchData
     * @param searchContent
     * @param product
     * @param productInfo
     * @param reviewRateCount
     * @param productCategoryMainType
     */
    protected void doPackProductData(StoreSearchDataBO storeSearchData, SearchContentBO searchContent, Product product, ProductInfo productInfo, Float reviewRateCount, ProductCategoryMainType productCategoryMainType) {
        String urlKey = StringUtils.isNotBlank(product.getUrlKey()) ? product.getUrlKey() : StringUtils.EMPTY;
        String productName = Objects.nonNull(productInfo) && StringUtils.isNotBlank(productInfo.getName()) ? productInfo.getName() : StringUtils.EMPTY;

        // 主记录
        storeSearchData.setProductId(product.getId());
        storeSearchData.setUrlKey(urlKey);
        storeSearchData.setProductName(productName);

        // 产品记录
        ProductDataBO productDataBo = new ProductDataBO();
        productDataBo.setProductId(product.getId());
        productDataBo.setProductName(productName);
        productDataBo.setUrlKey(urlKey);
        productDataBo.setReviewRateAvg(reviewRateCount);
        productDataBo.setBindCategory(product.getCategoryKey());
        productDataBo.setCategoryMain(Optional.ofNullable(productCategoryMainType).map(ProductCategoryMainType::name).orElse(StringUtils.EMPTY));

        searchContent.setProduct(productDataBo);
    }

    /**
     * 封装套餐数据
     *
     * @param storeSearchData
     * @param searchContent
     * @param commodity
     * @param commoditySaleState
     * @param commodityTradeRule
     * @param commodityInfo
     * @param commodityFunctionDescription
     * @param commodityDisplay
     */
    protected void doPackCommodityData(StoreSearchDataBO storeSearchData, SearchContentBO searchContent, Commodity commodity, CommoditySaleState commoditySaleState, CommodityTradeRule commodityTradeRule, Integer skuStockCount, CommodityInfo commodityInfo, CommodityFunctionDescription commodityFunctionDescription, CommodityDisplay commodityDisplay) {
        // 套餐首图
        String imageUrl = StringUtils.EMPTY;
        if (Objects.nonNull(commodityDisplay) && StringUtils.isNotBlank(commodityDisplay.getUrl())) {
            imageUrl = commodityDisplay.getUrl();
        }

        // 套餐功能描述
        String functionDescription = StringUtils.EMPTY;
        if (Objects.nonNull(commodityFunctionDescription)) {
            functionDescription = StringUtils.isNotBlank(commodityFunctionDescription.getFunctionDescription()) ? commodityFunctionDescription.getFunctionDescription() : StringUtils.EMPTY;
        }

        // 套餐名称
        String commodityName = StringUtils.EMPTY;
        if (Objects.nonNull(commodityInfo)) {
            commodityName = StringUtils.isNotBlank(commodityInfo.getName()) ? commodityInfo.getName() : StringUtils.EMPTY;
        }

        // 套餐销售状态
        int saleState = Objects.isNull(commoditySaleState) ? SearchConstant.DEFAULT_SALE_STATE : commoditySaleState.getSaleState();
        // 套餐限购数量
        Integer orderBuyLimit = Objects.isNull(commodityTradeRule) ? null : commodityTradeRule.getOrderBuyLimit();

        // 封装套餐数据
        CommodityDataBO commodityData = new CommodityDataBO();
        commodityData.setCommodityId(commodity.getId());
        commodityData.setCreateTime(commodity.getCreateTime());
        commodityData.setImageUrl(imageUrl);
        commodityData.setCommodityName(commodityName);
        commodityData.setFunctionDescription(functionDescription);
        commodityData.setSaleState(saleState);
        commodityData.setStockCount(Objects.nonNull(skuStockCount) ? skuStockCount : 0);
        commodityData.setOrderBuyLimit(orderBuyLimit);
        searchContent.setCommodity(commodityData);

        storeSearchData.setCommodityId(commodity.getId());
        storeSearchData.setCommodityName(commodityName);
        storeSearchData.setCommoditySaleState(saleState);
        storeSearchData.setCommodityCreateTime(commodity.getCreateTime());
    }

    /**
     * 封装标签数据
     *
     * @param storeSearchData
     * @param searchContent
     * @param commodity
     * @param product
     * @param searchCountiesBo
     * @param enableCommodityIdList
     * @param commodityTagAllMap
     * @param commoditySaleStateAllMap
     */
    protected void doPackTag(StoreSearchDataBO storeSearchData, SearchContentBO searchContent, Commodity commodity, Product product, SearchCountiesBO searchCountiesBo, List<Integer> enableCommodityIdList, Map<Integer, Map<InstaLanguage, Boolean>> commodityTagAllMap, Map<Integer, Integer> commodityStockMap, Map<Integer, Map<InstaCountry, CommoditySaleState>> commoditySaleStateAllMap) {
        // 当前套餐
        Integer commodityId = commodity.getId();
        // 当前套餐的产品
        Integer productId = product.getId();
        // 国家
        InstaCountry country = searchCountiesBo.getCountry();
        // 语言
        InstaLanguage language = searchCountiesBo.getLanguage();

        // 是否被标记‘新品标签’
        Boolean newTagInfo = MapUtils.isNotEmpty(commodityTagAllMap.get(productId)) ? Objects.nonNull(commodityTagAllMap.get(productId).get(language)) : Boolean.FALSE;

        // spu维度‘缺货标签’
        boolean outOfStockTag = true;
        if (CollectionUtils.isNotEmpty(enableCommodityIdList)) {
            for (Integer id : enableCommodityIdList) {
                // 销售状态‘缺货‘维度
                CommoditySaleState commoditySaleState = commoditySaleStateAllMap.get(id).get(country);
                boolean saleStateMark = SaleState.outOfStockStates().contains(commoditySaleState.parseSaleState());

                // 套餐库存维度
                Integer skuStockCount = CommonUtil.isNotEmpty(commodityStockMap) ? commodityStockMap.get(id) : 0;
                boolean skuStockMark = Objects.isNull(skuStockCount) || skuStockCount <= 0;

                if (!saleStateMark && !skuStockMark) {
                    outOfStockTag = false;
                    break;
                }
            }
        }

        // 基础标签（表示可被搜索）
        boolean aggregateTag = CollectionUtils.isNotEmpty(enableCommodityIdList) && enableCommodityIdList.contains(commodityId);

        // 预发布新品标签
        boolean preReleaseProduct = commodity.getNewCommodity() || product.getNewProduct();

        searchContent.setOutOfStockTag(outOfStockTag);
        searchContent.setNewTagInfo(newTagInfo);
        storeSearchData.setAggregateTags(String.valueOf(aggregateTag));
        storeSearchData.setPreReleaseProduct(preReleaseProduct);
    }

    /**
     * 封装套餐价格数据
     *
     * @param storeSearchData
     * @param searchContent
     * @param commodityPrice
     * @param fromLowestCommodityPrice
     * @param enableCommodityIdList
     */
    protected void doPackCommodityPriceData(StoreSearchDataBO storeSearchData, SearchContentBO searchContent, CommodityPrice commodityPrice, CommodityPrice fromLowestCommodityPrice, List<Integer> enableCommodityIdList, List<CommodityPrice> commodityPriceList) {
        // 当前套餐是否存在套餐价格
        if (Objects.isNull(commodityPrice)) {
            return;
        }

        // 如果存在最低套餐现价，则使用该套餐的原价与最低价
        Price fromLowestPrice = Optional.ofNullable(fromLowestCommodityPrice).map(CommodityPrice::price).orElse(null);
        // 获取当前套餐的价格和原价
        Price originedPrice = commodityPrice.originPrice();
        // 如果现价为空 || 现价 > 原价 则取套餐的原价
        Price currentPrice = Objects.isNull(commodityPrice.getAmount()) || commodityPrice.getAmount() > commodityPrice.getOriginAmount() ? originedPrice : commodityPrice.price();
        // 是否存在促销
        boolean discountMark = currentPrice.getAmount() < originedPrice.getAmount();

        // 缺货取原价，不缺货取现价
        Boolean outOfStockTag = searchContent.getOutOfStockTag();
        Set<Float> priceSet = commodityPriceList.stream().map(price -> outOfStockTag ? price.getOriginAmount() : price.getAmount()).collect(Collectors.toSet());
        Boolean priceAllEqual = priceSet.size() == 1;

        // 获取套餐列表中，缺货取原价 未缺货取现价 均取最低价
        CommodityPrice lowestPrice = commodityPriceList.stream().filter(Objects::nonNull).min(Comparator.comparing(price -> outOfStockTag ? price.getOriginAmount() : price.getAmount())).orElse(commodityPrice);

        // 创建套餐价格数据对象
        CommodityPriceDataBO commodityPriceData = new CommodityPriceDataBO();

        // 设置当前套餐价格的原价和现价
        commodityPriceData.setOriginPrice(commodityPrice.originPrice());
        commodityPriceData.setPrice(commodityPrice.price());

        // 设置当前套餐spu维度下在售sku的最低价格
        commodityPriceData.setLowestOriginPrice(lowestPrice.originPrice());
        commodityPriceData.setLowestPrice(lowestPrice.price());

        // 设置当前套餐spu维度下在售sku的数量
        commodityPriceData.setEnabledSkuNum(enableCommodityIdList.size());
        commodityPriceData.setPriceAllEqual(priceAllEqual);

        searchContent.setCommodityPrice(commodityPriceData);
        storeSearchData.setCommodityPrice(Objects.isNull(fromLowestPrice) ? currentPrice.getAmount() : fromLowestPrice.getAmount());
        storeSearchData.setDiscountMark(discountMark);
    }

    /**
     * 封装过滤条件
     *
     * @param storeSearchData
     * @param searchContent
     * @param commodity
     * @param searchCountiesBo
     * @param productAdapterTypeAllMap
     * @param productCategoryImageTextInfoMap
     */
    protected void doPackFilterInfoData(StoreSearchDataBO storeSearchData, SearchContentBO searchContent, Commodity commodity, SearchCountiesBO searchCountiesBo, Map<Integer, Map<InstaLanguage, List<String>>> productAdapterTypeAllMap, ArrayListMultimap<Integer, Map<InstaCountry, Map<InstaLanguage, String>>> productCategoryImageTextInfoMap) {
        // 适配机型
        List<String> adapterTypes = null;
        if (MapUtils.isNotEmpty(productAdapterTypeAllMap) && productAdapterTypeAllMap.containsKey(commodity.getProduct())) {
            Map<InstaLanguage, List<String>> productAdapterTypeMap = productAdapterTypeAllMap.get(commodity.getProduct());
            adapterTypes = productAdapterTypeMap.get(searchCountiesBo.getLanguage());
        }

        // 获取配件套餐对应的配件类目图文导航多语言
        List<String> categoryImageTextInfoList = this.getCategoryImageTextInfo(commodity.getId(), productCategoryImageTextInfoMap, searchCountiesBo);

        FilterInfoDataBO filterInfoData = new FilterInfoDataBO();
        filterInfoData.setAdapterTypes(adapterTypes);
        filterInfoData.setAccessoriesCategory(categoryImageTextInfoList);
        filterInfoData.setDiscountMark(Objects.nonNull(storeSearchData.getDiscountMark()) && storeSearchData.getDiscountMark() ? SearchConstant.ENABLE : SearchConstant.DISABLE);
        searchContent.setFilterInfo(filterInfoData);

        String adapterTypeStr = StringUtils.EMPTY;
        String adapterTypeFilter = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(adapterTypes)) {
            adapterTypeFilter = StringUtils.join(adapterTypes.toArray(), SearchConstant.DELIMITER);
            adapterTypeStr = StringUtils.join(adapterTypes.toArray(), " ");
        }

        String accessoriesCategoryStr = StringUtils.EMPTY;
        String accessoriesCategoryFilter = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(categoryImageTextInfoList)) {
            accessoriesCategoryFilter = StringUtils.join(categoryImageTextInfoList.toArray(), SearchConstant.DELIMITER);
            accessoriesCategoryStr = StringUtils.join(categoryImageTextInfoList.toArray(), " ");
        }

        storeSearchData.setAdapterType(adapterTypeStr);
        storeSearchData.setAdapterTypeFilter(adapterTypeFilter);
        storeSearchData.setAccessoriesCategory(accessoriesCategoryStr);
        storeSearchData.setAccessoriesCategoryFilter(accessoriesCategoryFilter);
    }

    /**
     * do封装导航信息
     *
     * @param storeSearchData              商城search数据
     * @param searchContent                搜索内容
     * @param commodity                    套餐
     * @param country                      国家
     * @param language                     语言
     * @param navigationBarCategoryInfoMap navigation酒吧类别info映射
     * @param navigationCategoryBo         导航类别Bo
     */
    protected void doPackNavigationInfo(StoreSearchDataBO storeSearchData, SearchContentBO searchContent, Commodity commodity, InstaCountry country, InstaLanguage language, Map<Integer, List<NavigationBarCategoryInfo>> navigationBarCategoryInfoMap, NavigationCategoryBO navigationCategoryBo) {
        storeSearchData.setNavigationBar(StringUtils.EMPTY);
        storeSearchData.setNavigationBarFilter(StringUtils.EMPTY);
        if (MapUtils.isEmpty(navigationBarCategoryInfoMap)) {
            return;
        }
        List<NavigationBarCategoryInfo> navigationBarCategoryInfoList = navigationBarCategoryInfoMap.get(commodity.getId());
        if (CollectionUtils.isEmpty(navigationBarCategoryInfoList)) {
            return;
        }

        // 根据国家地区分类
        Map<InstaCountry, List<NavigationBarCategoryInfo>> countryNavigationBarInfoListMap = navigationBarCategoryInfoList.stream().collect(Collectors.groupingBy(NavigationBarCategoryInfo::country));
        List<NavigationBarCategoryInfo> navigationBarCategoryInfos = countryNavigationBarInfoListMap.get(country);
        if (CollectionUtils.isEmpty(navigationBarCategoryInfos)) {
            return;
        }

        // 根据语言信息分类
        Map<InstaLanguage, List<NavigationBarCategoryInfo>> languageNavigationBarInfoListMap = navigationBarCategoryInfos.stream().collect(Collectors.groupingBy(NavigationBarCategoryInfo::language));
        List<NavigationBarCategoryInfo> navigationInfoList = languageNavigationBarInfoListMap.get(language);
        if (CollectionUtils.isEmpty(navigationInfoList)) {
            return;
        }

        // 将导航栏信息 封装到搜索内容中
        List<String> categoryNameList = navigationInfoList.stream().map(NavigationBarCategoryInfo::getCategoryName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryNameList)) {
            return;
        }

        String navigationBarFilter = StringUtils.join(categoryNameList, SearchConstant.DELIMITER);
        String navigationBar = StringUtils.join(categoryNameList, StringUtils.SPACE);

        storeSearchData.setNavigationBar(navigationBar);
        storeSearchData.setNavigationBarFilter(navigationBarFilter);

        FilterInfoDataBO filterInfoDataBo = new FilterInfoDataBO();
        filterInfoDataBo.setNavigationBar(categoryNameList);
        searchContent.setFilterInfo(filterInfoDataBo);

    }

    /**
     * 封装其他数据
     *
     * @param storeSearchData
     * @param searchContent
     * @param country
     * @param language
     */
    protected void doPackOtherData(StoreSearchDataBO storeSearchData, SearchContentBO searchContent, InstaCountry country, InstaLanguage language, ProductCategoryMainType productCategoryMainType) {
        storeSearchData.setLanguage(language.name());
        storeSearchData.setCountry(country.name());
        storeSearchData.setSearchResult(JSON.toJSONString(searchContent));
        storeSearchData.setCategoryMainKey(Objects.nonNull(productCategoryMainType) ? productCategoryMainType.name() : StringUtils.EMPTY);
    }

    /**
     * 根据产品评论评分统计和产品权重列表生成产品评论评分映射
     * 该方法主要用于计算每个产品的评论评分，考虑是否应用权重调整评分
     *
     * @param reviewRateCountMap 产品评论评分统计的Map，键为产品ID，值为ReviewRateCount对象
     * @param weightProducts     加权产品列表，用于确定哪些产品应使用加权评分
     * @return 返回一个Map，包含每个产品ID及其对应的评论评分
     */
    private Map<Integer, Float> getReviewMap(Map<Integer, ReviewRateCount> reviewRateCountMap, List<Integer> weightProducts) {
        // 初始化产品评论评分Map，预设大小为评论统计Map的大小
        Map<Integer, Float> reviewMap = new HashMap<>(reviewRateCountMap.size());
        // 遍历评论统计Map，处理每个产品的评论评分
        for (Map.Entry<Integer, ReviewRateCount> entry : reviewRateCountMap.entrySet()) {
            // 获取当前产品的ID
            Integer productId = entry.getKey();
            // 获取当前产品的评论统计信息
            ReviewRateCount reviewRateCount = entry.getValue();
            // 初始评分：使用产品的常规评论平均分
            Float reviewRate = reviewRateCount.getReviewTateAvg();
            // 如果当前产品在加权产品列表中，则使用加权平均分代替常规平均分
            if (weightProducts.contains(entry.getKey())) {
                reviewRate = reviewRateCount.getReviewRateWeightAvg();
            }
            // 将产品ID与计算后的评论评分作为键值对存入Map中
            reviewMap.put(productId, reviewRate);
        }
        // 返回最终构建的产品评论评分Map
        return reviewMap;
    }

    /**
     * 获取配件类目分类多语言
     *
     * @param commodityId
     * @param productCategoryImageTextInfoMap
     * @param searchCounties
     * @return
     */
    protected List<String> getCategoryImageTextInfo(Integer commodityId, ArrayListMultimap<Integer, Map<InstaCountry, Map<InstaLanguage, String>>> productCategoryImageTextInfoMap, SearchCountiesBO searchCounties) {

        // 获取商品对应的图文信息列表
        List<Map<InstaCountry, Map<InstaLanguage, String>>> imageTextInfoList = productCategoryImageTextInfoMap.get(commodityId);

        // 如果列表为空，返回一个空的 ArrayList
        if (CollectionUtils.isEmpty(imageTextInfoList)) {
            return new ArrayList<>(0);
        }

        // 通过流式操作处理图文信息
        return imageTextInfoList.stream().map(map -> {
            // 根据国家获取语言和图文信息的映射
            Map<InstaLanguage, String> languageMap = map.get(searchCounties.getCountry());

            // 如果语言映射为空，返回 null
            if (MapUtils.isEmpty(languageMap)) {
                return null;
            }

            // 返回对应语言的图文信息
            return languageMap.get(searchCounties.getLanguage());
        }).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }
}
