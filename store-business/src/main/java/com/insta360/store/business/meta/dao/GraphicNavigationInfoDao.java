package com.insta360.store.business.meta.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.meta.model.GraphicNavigationInfo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-03-08
 * @Description:
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface GraphicNavigationInfoDao extends BaseDao<GraphicNavigationInfo> {

    /**
     * 批量创建图文导航info
     *
     * @param graphicNavigationInfoList
     */
    void addGraphicNavigationMainInfo(@Param("graphicNavigationInfoList") List<GraphicNavigationInfo> graphicNavigationInfoList);
}
