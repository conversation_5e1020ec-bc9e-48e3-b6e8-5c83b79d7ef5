package com.insta360.store.business.outgoing.mq.subscribe.sender;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.subscribe.dto.EmailSubscribeDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @description: 手机号上报mq
 * @author: py
 * @create: 2023-11-03 15:30
 */
@Component
public class AttentiveSubscribePhoneMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(AttentiveSubscribePhoneMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_attentive_subscribe, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * attentive上报手机号
     *
     * @param emailSubscribeParam
     */
    public void sendAttentiveSubscribePhoneMessage(EmailSubscribeDTO emailSubscribeParam) {
        Integer subscribeId = emailSubscribeParam.getSubscribeId();
        if (subscribeId == null) {
            LOGGER.error("[attentive]电话订阅上报失败。subscribeId缺失。request:{}", subscribeId);
            return;
        }
        LOGGER.info("[attentive]电话订阅上报准备中：subscribeId:{}", subscribeId);
        String messageId = rocketTcpMessageSender.sendMessage(JSONObject.toJSONString(emailSubscribeParam));
        MqUtils.isBlankMessageIdHandle(messageId, this, emailSubscribeParam);
        if (StringUtils.isBlank(messageId)) {
            LOGGER.info("[attentive]电话订阅上报failed：subscribeId:{}", subscribeId);
            FeiShuMessageUtil.storeGeneralMessage("[attentive]电话订阅上报同步消息失败：subscribeId:{}" + subscribeId, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return;
        }
    }
}
