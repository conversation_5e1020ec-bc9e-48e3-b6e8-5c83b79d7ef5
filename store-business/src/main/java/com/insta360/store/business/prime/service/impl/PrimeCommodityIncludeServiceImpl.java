package com.insta360.store.business.prime.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.prime.dao.PrimeCommodityIncludeDao;
import com.insta360.store.business.prime.model.PrimeCommodityInclude;
import com.insta360.store.business.prime.service.PrimeCommodityIncludeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-06-04
 * @Description:
 */
@Service
public class PrimeCommodityIncludeServiceImpl extends BaseServiceImpl<PrimeCommodityIncludeDao, PrimeCommodityInclude> implements PrimeCommodityIncludeService {

    @Override
    public List<PrimeCommodityInclude> listByPrimeCommodityId(Long primeCommodityId) {
        QueryWrapper<PrimeCommodityInclude> qw = new QueryWrapper<>();
        qw.eq("prime_commodity_id", primeCommodityId);
        return baseMapper.selectList(qw);
    }
}
