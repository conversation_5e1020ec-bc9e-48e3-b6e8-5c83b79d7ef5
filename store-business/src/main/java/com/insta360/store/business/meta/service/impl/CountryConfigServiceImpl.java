package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.configuration.check.annotation.ParameterChecker;
import com.insta360.store.business.meta.dao.CountryConfigDao;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.model.CountryGroupItem;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.meta.service.CountryGroupItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/1/24
 * @Description:
 */
@Service
public class CountryConfigServiceImpl extends BaseServiceImpl<CountryConfigDao, CountryConfig> implements CountryConfigService {

    @Autowired
    CountryGroupItemService countryGroupItemService;

    @Override
    public CountryConfig getByCountry(InstaCountry country) {
        if (country == null) {
            return null;
        }

        QueryWrapper<CountryConfig> qw = new QueryWrapper<>();
        qw.eq("country_code", country.name());
        qw.last("limit 1");
        return baseMapper.selectOne(qw);
    }

    @Override
    public CountryConfig getByCountryText(String text) {
        QueryWrapper<CountryConfig> qw = new QueryWrapper<>();
        qw.eq("country_text", text);
        qw.last("limit 1");
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<CountryConfig> listByGroup(String groupKey) {
        List<CountryGroupItem> groupItems = countryGroupItemService.getGroupItems(groupKey);

        return groupItems
                .stream()
                .map(gi -> {
                    QueryWrapper<CountryConfig> qw = new QueryWrapper<>();
                    qw.eq("country_code", gi.getCountryCode());
                    qw.eq("`language`", gi.getLanguage());
                    qw.eq("disabled", false);
                    return baseMapper.selectOne(qw);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<CountryConfig> listConfig() {
        QueryWrapper<CountryConfig> qw = new QueryWrapper<>();
        qw.eq("disabled", false);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public InstaLanguage getCountryLanguage(InstaCountry country) {
        CountryConfig countryConfig = this.getByCountry(country);
        if (countryConfig == null) {
            return InstaLanguage.en_US;
        }
        return countryConfig.language();
    }

    @Override
    public Currency getCountryCurrency(InstaCountry country) {
        CountryConfig countryConfig = this.getByCountry(country);
        if (countryConfig == null) {
            return null;
        }
        return countryConfig.currency();
    }

    @Override
    public List<String> listCountry() {
        return listConfig()
                .stream()
                .map(CountryConfig::getCountryCode)
                .collect(Collectors.toList());
    }

    @Override
    @ParameterChecker(checkNull = true, checkMax = 100.0, checkMin = 1.0)
    public List<CountryConfig> listByCountries(List<String> countyCodeList) {
        QueryWrapper<CountryConfig> qw = new QueryWrapper<>();
        qw.in("country_code", countyCodeList);
        return baseMapper.selectList(qw);
    }
}
