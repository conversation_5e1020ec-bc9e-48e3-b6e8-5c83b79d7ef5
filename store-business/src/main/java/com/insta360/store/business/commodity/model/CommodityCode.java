package com.insta360.store.business.commodity.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
@TableName("product_commodity_code")
public class CommodityCode extends BaseModel<CommodityCode> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "commodity")
    private Integer commodity;

    @TableId(value = "area")
    private String area;

    @TableField("`code`")
    private String code;

    public Integer getCommodity() {
        return commodity;
    }

    public void setCommodity(Integer commodity) {
        this.commodity = commodity;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "CommodityCode{" +
        "commodity=" + commodity +
        ", area=" + area +
        ", code=" + code +
        "}";
    }
}
