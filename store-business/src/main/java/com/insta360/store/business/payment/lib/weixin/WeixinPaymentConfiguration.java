package com.insta360.store.business.payment.lib.weixin;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: hyc
 * @Date: 2019/1/21
 * @Description:
 */
@Configuration
public class WeixinPaymentConfiguration implements WeixinConfig{

    @Value("${payment.weixin.mch_id}")
    protected String mchId;

    @Value("${payment.weixin.mch_key}")
    protected String mchKey;

    @Value("${payment.weixin.app_id}")
    protected String appId;

    @Value("${payment.weixin.app_secret}")
    protected String appSecret;

    @Value("${payment.weixin.base_url}")
    protected String baseUrl;

    @Override
    public String getMchId() {
        return mchId;
    }

    @Override
    public String getMchKey() {
        return mchKey;
    }

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public String getAppSecret() {
        return appSecret;
    }

    @Override
    public String getBaseUrl() {
        return baseUrl;
    }
}
