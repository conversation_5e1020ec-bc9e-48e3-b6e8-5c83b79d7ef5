package com.insta360.store.business.cloud.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-09-05
 * @Description:
 */
@TableName("cloud_subscribe_auth_result")
public class CloudSubscribeAuthResult extends BaseModel<CloudSubscribeAuthResult> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * user id
     */
    private Integer instaAccount;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 支付服务商
     */
    private String psp;

    /**
     * 本次记录的触发行为
     */
    private String actionType;

    /**
     * 支付渠道
     */
    private String payMethod;

    /**
     * 验证类型
     */
    private String authType;

    /**
     * 响应码
     */
    private String authCode;

    /**
     * 描述
     */
    private String authText;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInstaAccount() {
        return instaAccount;
    }

    public void setInstaAccount(Integer instaAccount) {
        this.instaAccount = instaAccount;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPsp() {
        return psp;
    }

    public void setPsp(String psp) {
        this.psp = psp;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getAuthText() {
        return authText;
    }

    public void setAuthText(String authText) {
        this.authText = authText;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CloudSubscribeAuthResult{" +
                "id=" + id +
                ", instaAccount=" + instaAccount +
                ", email='" + email + '\'' +
                ", psp='" + psp + '\'' +
                ", actionType='" + actionType + '\'' +
                ", payMethod='" + payMethod + '\'' +
                ", authType='" + authType + '\'' +
                ", authCode='" + authCode + '\'' +
                ", authText='" + authText + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "} " + super.toString();
    }
}