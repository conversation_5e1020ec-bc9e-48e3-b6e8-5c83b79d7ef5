package com.insta360.store.business.cloud.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.cloud.enums.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-05-13
 * @Description: 云储存商城权益表
 */
public class CloudStorageStoreBenefit extends BaseModel<CloudStorageStoreBenefit> {

    private static final long serialVersionUID = 1L;

    /**
     * 权益主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID(官网用户账户ID)
     */
    private Integer userId;

    /**
     * sku_id
     */
    private String skuId;

    /**
     * 权益到期时间
     */
    private Long expirationTime;

    /**
     * sku对应的平台来源
     * @see com.insta360.store.business.cloud.enums.BenefitPlatform
     */
    private String platformSource;

    /**
     * 期数
     */
    private Integer periodNumber;

    /**
     * 订阅类型
     * @see SkuSubscribeType
     */
    private String subscribeType;

    /**
     * 容量
     */
    private Double capacity;

    /**
     * 权益状态
     */
    private String status;

    /**
     * 是否已回收
     */
    private Boolean recycled;

    /**
     * 用户权益地区
     */
    private String region;

    /**
     * 是否存在下一台相机优惠
     */
    private Boolean nextOneCameraDiscount;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 获取sku对应订阅类型
     *
     * @return
     */
    public SkuSubscribeType parseSkuSubscribeType() {
        return SkuSubscribeType.matchType(this.subscribeType);
    }

    /**
     * 获取sku对应容量类型
     *
     * @return
     */
    public BenefitCapacityType parseCapacityType() {
        return BenefitCapacityType.matchType(this.capacity);
    }

    /**
     * 是否过期
     *
     * @return
     */
    @JSONField(serialize = false)
    public Boolean isExpired() {
        return this.expirationTime <= System.currentTimeMillis() || BenefitStatus.EXPIRED.getName().equals(this.status);
    }

    /**
     * 获取国家/地区二字码
     *
     * @return
     */
    public String parseRegion() {
        if (BenefitPlatform.STORE.getName().equals(platformSource)) {
            InstaCountry instaCountry = InstaCountry.parse(region);
            return Objects.nonNull(instaCountry) ? instaCountry.name() : null;
        } else {
            AppChannelCountry appChannelCountry = AppChannelCountry.matchCode(region);
            return Objects.nonNull(appChannelCountry) ? appChannelCountry.name() : null;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Long expirationTime) {
        this.expirationTime = expirationTime;
    }

    public String getPlatformSource() {
        return platformSource;
    }

    public void setPlatformSource(String platformSource) {
        this.platformSource = platformSource;
    }

    public Integer getPeriodNumber() {
        return periodNumber;
    }

    public void setPeriodNumber(Integer periodNumber) {
        this.periodNumber = periodNumber;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getSubscribeType() {
        return subscribeType;
    }

    public void setSubscribeType(String subscribeType) {
        this.subscribeType = subscribeType;
    }

    public Double getCapacity() {
        return capacity;
    }

    public void setCapacity(Double capacity) {
        this.capacity = capacity;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Boolean getRecycled() {
        return recycled;
    }

    public void setRecycled(Boolean recycled) {
        this.recycled = recycled;
    }

    public Boolean getNextOneCameraDiscount() {
        return nextOneCameraDiscount;
    }

    public void setNextOneCameraDiscount(Boolean nextOneCameraDiscount) {
        this.nextOneCameraDiscount = nextOneCameraDiscount;
    }

    @Override
    public String toString() {
        return "CloudStorageStoreBenefit{" +
                "id=" + id +
                ", userId=" + userId +
                ", skuId='" + skuId + '\'' +
                ", expirationTime=" + expirationTime +
                ", platformSource='" + platformSource + '\'' +
                ", periodNumber=" + periodNumber +
                ", subscribeType='" + subscribeType + '\'' +
                ", capacity=" + capacity +
                ", status='" + status + '\'' +
                ", recycled=" + recycled +
                ", region='" + region + '\'' +
                ", nextOneCameraDiscount=" + nextOneCameraDiscount +
                ", modifyTime=" + modifyTime +
                ", createTime=" + createTime +
                '}';
    }
}