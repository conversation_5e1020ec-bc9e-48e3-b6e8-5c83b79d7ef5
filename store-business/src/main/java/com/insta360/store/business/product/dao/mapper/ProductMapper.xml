<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.product.dao.ProductDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.product.dao.ProductDao"/>

    <!-- 批量插入 -->
    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into product (name, url_key, category_key, tax_code_info_id, tax_rate)
        values
        <foreach collection="list" item="product" separator=",">
            (
            #{product.name},
            #{product.urlKey},
            #{product.categoryKey},
            #{product.taxCodeInfoId},
            #{product.taxRate}
            )
        </foreach>
    </insert>

    <insert id="importSave"  keyProperty="id">
        INSERT INTO product (
        <if test="id != null">id,</if>  <!-- 非自增时保留 -->
        name,
        url_key,
        `key`,
        enabled,
        accessories,
        accessories_commodity,
        is_camera,
        is_repair_service,
        type,
        category_key,
        tax_rate,
        tax_code_info_id,
        new_product,
        create_time,
        modify_time
        )
        VALUES (
        <if test="id != null">#{id},</if>
        #{name},
        #{urlKey},
        #{key},
        #{enabled},
        #{accessories},
        #{accessoriesCommodity},
        #{isCamera},
        #{isRepairService},
        #{type},
        #{categoryKey},
        #{taxRate},
        #{taxCodeInfoId},
        #{newProduct},
        NOW(),
        NOW()
        )
    </insert>

    <select id="listProductsByKey" resultType="com.insta360.store.business.product.model.Product">
        select product.*
        from product
                 join product_category_subset pcs
                      on (pcs.category_main_key = product.category_key
                          or pcs.category_subset_key =
                             product.category_key)
        where product.enabled = 1
          and pcs.category_main_key = #{categoryKey}
    </select>

    <select id="listAccessoryCategoryProducts" resultType="com.insta360.store.business.product.model.Product">
        select product.*
        from product
                 join product_category_subset pcs
                      on (pcs.category_main_key = product.category_key
                          or pcs.category_subset_key =
                             product.category_key)
        where product.enabled = 1
            and pcs.category_main_key = "CM_ACCESSORY"
           or pcs.category_subset_key = "CF_TP_ACCESSORY"
    </select>

</mapper>