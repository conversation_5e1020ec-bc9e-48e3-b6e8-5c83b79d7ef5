package com.insta360.store.business.cloud.service;

import com.insta360.store.business.cloud.bo.CloudBenefitBindResultBO;
import com.insta360.store.business.cloud.bo.CloudStorageBenefitBO;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitBindContext;
import com.insta360.store.business.commodity.enums.ServiceType;

/**
 * @description:
 * @author: py
 * @create: 2024-05-17 15:24
 */
public interface StoreBenefitBindService {

    /**
     * 校验序列号激活
     *
     * @param cloudStorageBenefitParam
     * @param storeBenefitBindContext
     * @return
     */
    void checkActivation(CloudStorageBenefitBO cloudStorageBenefitParam, StoreBenefitBindContext storeBenefitBindContext);

    /**
     * 业务处理
     *
     * @param storeBenefitBindContext
     * @return
     */
    CloudBenefitBindResultBO handle(StoreBenefitBindContext storeBenefitBindContext);

    /**
     * 获取服务类型
     *
     * @param storeBenefitBindContext
     * @param bindType
     * @return
     */
    ServiceType getServiceType(StoreBenefitBindContext storeBenefitBindContext, String bindType);

    /**
     * 获取绑定类型
     *
     * @return
     */
    BenefitType getBindType();
}