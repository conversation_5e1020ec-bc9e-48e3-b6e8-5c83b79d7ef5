package com.insta360.store.business.trade.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.trade.model.CreditCardPayRule;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/11/16
 * @Description:
 */
public class CreditCardRuleDTO {

    private Integer id;

    /**
     * id集合列表
     */
    private List<Integer> ids;

    /**
     * 卡类型
     */
    private Integer cardTypeId;

    /**
     * checkout支付通道
     */
    private Integer checkoutPayChannelId;

    /**
     * 钱海支付通道
     */
    private Integer oceanPayChannelId;

    /**
     * 地区
     */
    private String country;

    /**
     * 钱海支付权重
     */
    private Integer oceanWeight;

    /**
     * cko支付权重
     */
    private Integer checkoutWeight;

    /**
     * 是否启用（0：禁用；1：启用）
     */
    private Boolean enabled;

    public CreditCardPayRule getPojoObject() {
        CreditCardPayRule creditCardPayRule = new CreditCardPayRule();
        BeanUtil.copyProperties(this, creditCardPayRule);
        return creditCardPayRule;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public Integer getCardTypeId() {
        return cardTypeId;
    }

    public void setCardTypeId(Integer cardTypeId) {
        this.cardTypeId = cardTypeId;
    }

    public Integer getCheckoutPayChannelId() {
        return checkoutPayChannelId;
    }

    public void setCheckoutPayChannelId(Integer checkoutPayChannelId) {
        this.checkoutPayChannelId = checkoutPayChannelId;
    }

    public Integer getOceanPayChannelId() {
        return oceanPayChannelId;
    }

    public void setOceanPayChannelId(Integer oceanPayChannelId) {
        this.oceanPayChannelId = oceanPayChannelId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getOceanWeight() {
        return oceanWeight;
    }

    public void setOceanWeight(Integer oceanWeight) {
        this.oceanWeight = oceanWeight;
    }

    public Integer getCheckoutWeight() {
        return checkoutWeight;
    }

    public void setCheckoutWeight(Integer checkoutWeight) {
        this.checkoutWeight = checkoutWeight;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    @Override
    public String toString() {
        return "CreditCardRuleDTO{" +
                "id=" + id +
                ", ids=" + ids +
                ", cardTypeId=" + cardTypeId +
                ", checkoutPayChannelId=" + checkoutPayChannelId +
                ", oceanPayChannelId=" + oceanPayChannelId +
                ", country='" + country + '\'' +
                ", oceanWeight=" + oceanWeight +
                ", checkoutWeight=" + checkoutWeight +
                ", enabled=" + enabled +
                '}';
    }
}
