package com.insta360.store.business.meta.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.meta.model.AvalaraTaxInfo;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-06-29
 * @Description:
 */
public interface AvalaraTaxInfoService extends BaseService<AvalaraTaxInfo> {

    /**
     * 根据业务类型、业务单据号查询
     * @param businessType
     * @param businessCode
     * @return
     */
    AvalaraTaxInfo getOrderAvalaraTaxInfo(String businessType, String businessCode);

    /**
     * 根据订单号获取交易计税信息（取第一条）
     *
     * @param orderNumber
     * @return
     */
    AvalaraTaxInfo getOrderAvalaraTaxInfo(String orderNumber);
}
