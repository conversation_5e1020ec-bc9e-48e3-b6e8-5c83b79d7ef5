package com.insta360.store.business.payment.service.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.store.business.common.constants.CommonConstant;
import com.insta360.store.business.configuration.utils.SpringContextLocator;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.payment.bo.OceanPaymentTransactionExcelBO;
import com.insta360.store.business.payment.config.OceanPaymentInfoConfiguration;
import com.insta360.store.business.payment.constants.OceanConstant;
import com.insta360.store.business.payment.dao.OceanPaymentTransactionRecordDao;
import com.insta360.store.business.payment.model.OceanPaymentTransactionRecord;
import com.insta360.store.business.payment.service.OceanPaymentTransactionRecordService;
import com.insta360.store.business.payment.service.impl.handler.OceanPaymentExcelListener;
import com.insta360.store.business.payment.service.impl.handler.SftpHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-09-27
 * @Description:
 */
@Service
public class OceanPaymentTransactionRecordServiceImpl extends BaseServiceImpl<OceanPaymentTransactionRecordDao, OceanPaymentTransactionRecord> implements OceanPaymentTransactionRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OceanPaymentTransactionRecordServiceImpl.class);

    @Autowired
    OceanPaymentInfoConfiguration oceanPaymentInfoConfiguration;

    @Override
    public List<OceanPaymentTransactionRecord> listByPaymentId(List<String> paymentIds) {
        if (CollectionUtils.isEmpty(paymentIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<OceanPaymentTransactionRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("payment_id", paymentIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void batchSaveOceanPaymentTransactionRecord(List<OceanPaymentTransactionRecord> oceanPaymentTransactionRecordList) {
        if (CollectionUtils.isEmpty(oceanPaymentTransactionRecordList)) {
            return;
        }
        baseMapper.batchSaveOceanPaymentTransactionRecord(oceanPaymentTransactionRecordList);
    }

    @Override
    public void pullOceanPaymentTransactionData(String timeStr) {
        // 计算当前日期，如果有指定日期则使用指定日期
        String toDay = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern(CommonConstant.DATE_PATTERN));
        String dateStr = StringUtils.defaultIfBlank(timeStr, toDay).trim();
        List<OceanPaymentInfoConfiguration.OceanAccount> oceanAccountList = oceanPaymentInfoConfiguration.getOceanAccounts();
        for (OceanPaymentInfoConfiguration.OceanAccount oceanAccount : oceanAccountList) {
            SftpHandler sftpHandler = new SftpHandler(oceanAccount.getHost(), oceanAccount.getPort(), oceanAccount.getUsername(), oceanAccount.getPassword());
            String remoteFilePath = String.format(OceanConstant.FILE_PATH, dateStr);
            String localPath = oceanPaymentInfoConfiguration.getLocalPathPrefix() + OceanConstant.DIR + File.separator;
            String localFilePath = localPath + UUIDUtils.generateUuid() + ExcelTypeEnum.CSV.getValue();
            FileUtil.mkdir(localPath);

            try {
                sftpHandler.downloadFile(remoteFilePath, localFilePath);
            } catch (Exception e) {
                String message = String.format("账号:%s 文件:%s sftp文件下载失败  error:%s", oceanAccount.getUsername(), remoteFilePath, e.getMessage());
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
                LOGGER.error(message, e);
                continue;
            }

            OceanPaymentExcelListener listener = SpringContextLocator.getBean(OceanPaymentExcelListener.class);

            // 读取Excel
            EasyExcel.read(localFilePath, OceanPaymentTransactionExcelBO.class, listener)
                    .autoTrim(true)
                    .ignoreEmptyRow(true)
                    .excelType(ExcelTypeEnum.CSV)
                    .sheet()
                    .doRead();

            FileUtil.del(localFilePath);
        }

    }
}
