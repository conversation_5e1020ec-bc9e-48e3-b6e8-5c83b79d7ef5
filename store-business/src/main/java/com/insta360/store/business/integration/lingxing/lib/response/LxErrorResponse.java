package com.insta360.store.business.integration.lingxing.lib.response;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20
 */
public class LxErrorResponse implements LxResponse {

    @JSONField(name = "code")
    private Integer code;

    @JSONField(name = "message")
    private String message;

    @JSONField(name = "error_details")
    private List<?> errorDetails;

    @JSONField(name = "request_id")
    private String requestId;

    @JSONField(name = "response_time")
    private String responseTime;

    @JSONField(name = "data")
    private List<?> data;

    @JSONField(name = "total")
    private Integer total;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<?> getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(List<?> errorDetails) {
        this.errorDetails = errorDetails;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(String responseTime) {
        this.responseTime = responseTime;
    }

    public List<?> getData() {
        return data;
    }

    public void setData(List<?> data) {
        this.data = data;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "LxErrorResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", errorDetails=" + errorDetails +
                ", requestId='" + requestId + '\'' +
                ", responseTime='" + responseTime + '\'' +
                ", data=" + data +
                ", total=" + total +
                '}';
    }

    @Override
    public Boolean isSuccess() {
        return false;
    }
}
