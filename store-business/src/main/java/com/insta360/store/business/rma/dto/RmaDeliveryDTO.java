package com.insta360.store.business.rma.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2020/11/19
 * @Description:
 */
public class RmaDeliveryDTO implements Serializable {

    @J<PERSON><PERSON>ield(name = "rma_id")
    private Integer rmaId;

    @JSONField(name = "express_from_company")
    private String expressFromCompany;

    @JSONField(name = "express_from_number")
    private String expressFromNumber;

    @JSONField(name = "express_to_company")
    private String expressToCompany;

    @JSONField(name = "express_to_number")
    private String expressToNumber;

    public Integer getRmaId() {
        return rmaId;
    }

    public void setRmaId(Integer rmaId) {
        this.rmaId = rmaId;
    }

    public String getExpressFromCompany() {
        return expressFromCompany;
    }

    public void setExpressFromCompany(String expressFromCompany) {
        this.expressFromCompany = expressFromCompany;
    }

    public String getExpressFromNumber() {
        return expressFromNumber;
    }

    public void setExpressFromNumber(String expressFromNumber) {
        this.expressFromNumber = expressFromNumber;
    }

    public String getExpressToCompany() {
        return expressToCompany;
    }

    public void setExpressToCompany(String expressToCompany) {
        this.expressToCompany = expressToCompany;
    }

    public String getExpressToNumber() {
        return expressToNumber;
    }

    public void setExpressToNumber(String expressToNumber) {
        this.expressToNumber = expressToNumber;
    }
}
