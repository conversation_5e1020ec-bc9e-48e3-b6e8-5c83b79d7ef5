package com.insta360.store.business.tradeup.dto;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @Author: wbt
 * @Date: 2020/11/19
 * @Description:
 */
public class TradeupOrderQueryDTO implements Serializable {

    @JSONField(name = "page_number")
    private Integer pageNumber;

    @JSONField(name = "page_size")
    private Integer pageSize;

    private JSONArray states;

    private String email;

    private String phone;

    @J<PERSON><PERSON>ield(name = "from_time")
    private Timestamp fromTime;

    @J<PERSON><PERSON>ield(name = "end_time")
    private Timestamp endTime;

    @JSONField(name = "trade_up_order_number")
    private String tradeUpOrderNumber;

    @JSONField(name = "country_code")
    private String countryCode;

    @J<PERSON><PERSON>ield(name = "device_company")
    private String deviceCompany;

    private String remark;

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public JSONArray getStates() {
        return states;
    }

    public void setStates(JSONArray states) {
        this.states = states;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Timestamp getFromTime() {
        return fromTime;
    }

    public void setFromTime(Timestamp fromTime) {
        this.fromTime = fromTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public String getTradeUpOrderNumber() {
        return tradeUpOrderNumber;
    }

    public void setTradeUpOrderNumber(String tradeUpOrderNumber) {
        this.tradeUpOrderNumber = tradeUpOrderNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getDeviceCompany() {
        return deviceCompany;
    }

    public void setDeviceCompany(String deviceCompany) {
        this.deviceCompany = deviceCompany;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TradeupOrderQueryDTO{" +
                "pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", states=" + states +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", fromTime=" + fromTime +
                ", endTime=" + endTime +
                ", tradeUpOrderNumber='" + tradeUpOrderNumber + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", deviceCompany='" + deviceCompany + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
