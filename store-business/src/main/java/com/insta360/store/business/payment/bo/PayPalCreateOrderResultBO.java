package com.insta360.store.business.payment.bo;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2024/10/24
 * @Description:
 */
public class PayPalCreateOrderResultBO implements Serializable {

    /**
     * paypal order token
     */
    private String paypalOrderToken;

    /**
     * paypal 订单授权url
     */
    private String orderApprovalUrl;

    public String getPaypalOrderToken() {
        return paypalOrderToken;
    }

    public void setPaypalOrderToken(String paypalOrderToken) {
        this.paypalOrderToken = paypalOrderToken;
    }

    public String getOrderApprovalUrl() {
        return orderApprovalUrl;
    }

    public void setOrderApprovalUrl(String orderApprovalUrl) {
        this.orderApprovalUrl = orderApprovalUrl;
    }

    @Override
    public String toString() {
        return "PayPalCreateOrderResultBO{" +
                "paypalOrderToken='" + paypalOrderToken + '\'' +
                ", orderApprovalUrl='" + orderApprovalUrl + '\'' +
                '}';
    }
}
