package com.insta360.store.business.configuration.grafana.bo;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2022/03/09
 * @Description:
 */
public class GrafanaValueBO implements Serializable {

    /**
     * 目标方法的结果值
     */
    private Object proceed;

    /**
     * 目标方法的执行时间
     */
    private Integer timeValue;

    /**
     * 执行标识
     */
    private Boolean flag = false;

    public Object getProceed() {
        return proceed;
    }

    public void setProceed(Object proceed) {
        this.proceed = proceed;
    }

    public Integer getTimeValue() {
        return timeValue;
    }

    public void setTimeValue(Integer timeValue) {
        this.timeValue = timeValue;
    }

    public Boolean isFlag() {
        return flag;
    }

    public void setFlag(Boolean flag) {
        this.flag = flag;
    }

    @Override
    public String toString() {
        return "GrafanaValueBO{" +
                "proceed=" + proceed +
                ", timeValue=" + timeValue +
                ", flag=" + flag +
                '}';
    }
}
