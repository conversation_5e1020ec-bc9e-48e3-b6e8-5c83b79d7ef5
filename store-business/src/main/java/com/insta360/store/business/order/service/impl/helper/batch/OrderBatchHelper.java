package com.insta360.store.business.order.service.impl.helper.batch;

import com.insta360.store.business.order.service.OrderDeliveryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 订单批量查询处理助手类
 * @Date 2023/5/22
 */
@Component
public class OrderBatchHelper {

    @Autowired
    private OrderDeliveryService orderDeliveryService;

    /**
     * 根据物流单号查询对应订单物流信息Map
     * @param expressCodes
     * @return
     */
//    public Map<String, Order> orderMapByExpressCodes(List<String> expressCodes) {
//        Map<String, OrderDelivery> orderDeliveryMap = orderDeliveryService.listByExpressCodes(expressCodes).stream().collect(Collectors.toMap(OrderDelivery::getExpressCode, o -> o, (v1, v2) -> v1));
//
//    }
}
