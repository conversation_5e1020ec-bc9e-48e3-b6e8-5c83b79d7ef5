package com.insta360.store.business.prime.lib.variables;

/**
 * 更新Amazon Prime产品的GraphQL变量类
 * <p>
 * 此类用于封装向Amazon Prime服务发送更新产品请求时所需的所有参数。
 * 支持更新产品的基本信息（如标题、图片、URL等）以及购买组成员信息。
 * 作为GraphQL请求变量传递给Prime API进行产品更新操作。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/9
 */
public class ProductVariables implements PrimeVariables {

    /**
     * 产品标识符
     * 用于唯一标识待更新的产品
     */
    private Identifier identifier;

    /**
     * 获取产品标识符
     *
     * @return 产品标识符对象
     */
    public Identifier getIdentifier() {
        return identifier;
    }

    /**
     * 设置产品标识符
     *
     * @param identifier 产品标识符对象
     */
    public void setIdentifier(Identifier identifier) {
        this.identifier = identifier;
    }

    /**
     * 产品标识符类
     * <p>
     * 用于在Amazon Prime系统中唯一标识待更新的产品
     * 使用SKU作为主要标识字段
     * </p>
     */
    public static class Identifier {

        /**
         * 产品SKU
         * 在Prime系统中唯一标识产品的代码
         */
        private String externalId;

        /**
         * 默认构造函数
         */
        public Identifier() {
        }

        /**
         * 通过SKU创建标识符
         *
         * @param externalId 产品SKU
         */
        public Identifier(String externalId) {
            this.externalId = externalId;
        }

        /**
         * 获取产品SKU
         *
         * @return SKU字符串
         */
        public String getExternalId() {
            return externalId;
        }

        /**
         * 设置产品SKU
         *
         * @param externalId SKU字符串
         */
        public void setExternalId(String externalId) {
            this.externalId = externalId;
        }
    }

}
