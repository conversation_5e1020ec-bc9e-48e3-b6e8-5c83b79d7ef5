package com.insta360.store.business.discount.dto;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 优惠模型查询请求参数实体
 * @Date 2022/6/16
 */
public class DiscountModelQueryDTO implements Serializable {

    /**
     * 交易券code
     */
    @NotEmpty(message = "交易券code不能为空")
    private List<String> tradeCodes;

    public List<String> getTradeCodes() {
        return tradeCodes;
    }

    public void setTradeCodes(List<String> tradeCodes) {
        this.tradeCodes = tradeCodes;
    }
}
