package com.insta360.store.business.rma.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-02-26
 * @Description: RMA订单操作记录
 */
@TableName("rma_order_operator_record")
public class RmaOrderOperatorRecord extends BaseModel<RmaOrderOperatorRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 售后订单id
     */
    private Integer rmaOrderId;

    /**
     * 操作员工姓名
     */
    private String username;

    /**
     * 操作员工邮箱
     */
    private String jobEmail;

    /**
     * 编辑标题
     */
    private String operatorTitle;

    /**
     * 编辑类型
     */
    private String operatorType;

    /**
     * 修改前的数据
     */
    private String fromData;

    /**
     * 修改后的数据
     */
    private String toData;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRmaOrderId() {
        return rmaOrderId;
    }

    public void setRmaOrderId(Integer rmaOrderId) {
        this.rmaOrderId = rmaOrderId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getJobEmail() {
        return jobEmail;
    }

    public void setJobEmail(String jobEmail) {
        this.jobEmail = jobEmail;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType;
    }

    public String getFromData() {
        return fromData;
    }

    public void setFromData(String fromData) {
        this.fromData = fromData;
    }

    public String getToData() {
        return toData;
    }

    public void setToData(String toData) {
        this.toData = toData;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperatorTitle() {
        return operatorTitle;
    }

    public void setOperatorTitle(String operatorTitle) {
        this.operatorTitle = operatorTitle;
    }

    @Override
    public String toString() {
        return "RmaOrderOperatorRecord{" +
                "id=" + id +
                ", rmaOrderId=" + rmaOrderId +
                ", username='" + username + '\'' +
                ", jobEmail='" + jobEmail + '\'' +
                ", operatorTitle='" + operatorTitle + '\'' +
                ", operatorType='" + operatorType + '\'' +
                ", fromData='" + fromData + '\'' +
                ", toData='" + toData + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}