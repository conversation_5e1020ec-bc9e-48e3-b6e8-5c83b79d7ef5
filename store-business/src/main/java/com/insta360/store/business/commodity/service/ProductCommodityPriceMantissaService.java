package com.insta360.store.business.commodity.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.commodity.dto.price.AdMantissaRuleDTO;
import com.insta360.store.business.commodity.model.ProductCommodityPriceMantissa;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-05-14
 * @Description:
 */
public interface ProductCommodityPriceMantissaService extends BaseService<ProductCommodityPriceMantissa> {

    /**
     * 创建规则
     *
     * @param adMantissaRuleDto admin尾数规则dto
     */
    void createRule(AdMantissaRuleDTO adMantissaRuleDto);

    /**
     * 更新规则
     *
     * @param adMantissaRuleDto admin尾数规则dto
     */
    void updateRule(AdMantissaRuleDTO adMantissaRuleDto);

    /**
     * 移除规则
     *
     * @param ruleId 规则id
     */
    void removeRule(Integer ruleId);
}
