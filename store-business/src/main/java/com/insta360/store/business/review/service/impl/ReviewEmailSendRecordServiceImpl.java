package com.insta360.store.business.review.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.review.dao.ReviewEmailSendRecordDao;
import com.insta360.store.business.review.email.ReviewEmail;
import com.insta360.store.business.review.model.ReviewEmailSendRecord;
import com.insta360.store.business.review.service.ReviewEmailSendRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-04
 * @Description:
 */
@Service
public class ReviewEmailSendRecordServiceImpl extends BaseServiceImpl<ReviewEmailSendRecordDao, ReviewEmailSendRecord> implements ReviewEmailSendRecordService {

    @Override
    public ReviewEmailSendRecord getReviewEmailSendRecord(String orderNumber, String email) {
        QueryWrapper<ReviewEmailSendRecord> qw = new QueryWrapper<>();
        qw.eq("order_number", orderNumber);
        qw.eq("email", email);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<ReviewEmailSendRecord> listByBetweenCreateTime(String startTime, String endTime) {
        QueryWrapper<ReviewEmailSendRecord> qw = new QueryWrapper<>();
        qw.eq("email_key", ReviewEmail.REVIEW_EMAIL_TEMPLATE_NAME);
        qw.between("send_time", startTime, endTime);
        return baseMapper.selectList(qw);
    }
}
