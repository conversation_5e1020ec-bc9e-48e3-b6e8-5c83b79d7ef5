package com.insta360.store.business.user.constant;

import com.insta360.compass.core.enums.InstaCountry;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: py
 * @Date: 2023/07/03
 * @Description:
 */
public class UserConstant {

    /**
     * 提供远程调用的邮箱订阅的服务 需要的订阅页面
     */
    public static final String RPC_SUBSCRIBE_NAME = "country_subscribe";

    /**
     * 不支持province字段的国家地区
     */
    public static final List<InstaCountry> NOT_SUPPORT_PROVINCE_COUNTRY = Arrays.asList(InstaCountry.HK, InstaCountry.MO, InstaCountry.TW, InstaCountry.SG);

    /**
     * user_toke缓存前缀
     */
    public static final String USER_TOKEN_KEY = "user:cahce:";
}
