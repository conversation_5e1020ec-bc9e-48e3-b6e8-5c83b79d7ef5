package com.insta360.store.business.integration.wto.oms.lib.requrest.admin;

import com.insta360.store.business.integration.wto.oms.bo.OmsGiftBO;
import com.insta360.store.business.integration.wto.oms.configuration.OmsConfiguration;
import com.insta360.store.business.integration.wto.oms.lib.requrest.BaseOmsRequest;
import org.springframework.beans.BeanUtils;

/**
 * 更新订单收货地址请求
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
public class DeleteGiftRequest extends BaseOmsRequest {

    private static final String METHOD = "insta360.deleteGift";

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 平台订单号
     */
    private String orderNumber;

    /**
     * 子订单号
     */
    private String orderItemId;

    /**
     * 赠品SKU
     */
    private String skuCode;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 创建一个新的 GiftItem 实例。
     *
     * @param shopCode    店铺编码
     * @param orderNumber 平台订单号
     * @param orderItemId 子订单号
     * @param skuCode     赠品SKU
     * @param num         数量
     */
    public DeleteGiftRequest(String shopCode, String orderNumber, String orderItemId, String skuCode, Integer num, OmsConfiguration omsConfiguration) {
        super(METHOD, omsConfiguration);
        this.shopCode = shopCode;
        this.orderNumber = orderNumber;
        this.orderItemId = orderItemId;
        this.skuCode = skuCode;
        this.num = num;
    }

    public DeleteGiftRequest(OmsConfiguration omsConfiguration) {
        super(METHOD, omsConfiguration);
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Override
    public String toString() {
        return "UpdateGiftOderItemRequest{" +
                "shopCode='" + shopCode + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", orderItemId='" + orderItemId + '\'' +
                ", skuCode='" + skuCode + '\'' +
                ", num='" + num + '\'' +
                '}';
    }

    /**
     * 构建参数
     *
     * @param omsGiftBo
     */
    public void buildParams(OmsGiftBO omsGiftBo) {
        if (omsGiftBo == null) {
            return;
        }
        BeanUtils.copyProperties(omsGiftBo, this);
    }
}
