package com.insta360.store.business.outgoing.rpc.base.bo;

import com.insta360.store.business.search.enums.SearchSortType;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/7
 */
public class SearchSortBO implements Serializable {

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序类型（1：升序；0：降序）
     */
    private Boolean sortType;

    public SearchSortBO() {
    }

    public SearchSortBO(SearchSortType searchSortType) {
        this.sortField = searchSortType.getField();
        this.sortType = searchSortType.getSortType();
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public Boolean getSortType() {
        return sortType;
    }

    public void setSortType(Boolean sortType) {
        this.sortType = sortType;
    }

    @Override
    public String toString() {
        return "SearchSortBO{" +
                "sortField='" + sortField + '\'' +
                ", sortType=" + sortType +
                '}';
    }
}
