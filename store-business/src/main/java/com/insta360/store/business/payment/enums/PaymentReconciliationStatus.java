package com.insta360.store.business.payment.enums;

/**
 * <AUTHOR>
 * @Description 支付对账状态枚举类
 * @Date 2024/4/11
 */
public enum PaymentReconciliationStatus {
    /**
     * 待对账
     */
    PENDING(0, "pending", "待对账"),

    /**
     * 已对账
     */
    RECONCILED(1, "reconciled", "已对账"),

    /**
     * 对账失败
     */
    ERROR(2, "error", "对账失败");

    private final Integer code;

    private final String value;

    private final String desc;

    PaymentReconciliationStatus(Integer code, String value, String desc) {
        this.code = code;
        this.value = value;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static PaymentReconciliationStatus parse(Integer code) {
        for (PaymentReconciliationStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
