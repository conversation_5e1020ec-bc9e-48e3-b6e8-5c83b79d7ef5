package com.insta360.store.business.user.service.impl.helper;

import com.insta360.compass.core.util.JwtUtil;
import io.jsonwebtoken.JwtException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: hyc
 * @Date: 2019/1/16
 * @Description:
 */
@Component
public class StoreTokenHelper {

    private static final String STORE_SECRET = "insta360jwt";
    private static final String PAYLOAD_KEY = "store_account";

    public String generate(Integer accountId) {
        Map<String, Object> payload = new HashMap<>();
        payload.put(PAYLOAD_KEY, accountId);
        return JwtUtil.sign(STORE_SECRET, payload, null);
    }

    public Integer verify(String token)  {
        try {
            Map<String, Object> payload = JwtUtil.verify(STORE_SECRET, token);
            return (Integer) payload.get(PAYLOAD_KEY);
        }catch (JwtException e){
            return null;
        }
    }

    public Object verify(String token, String payloadKey)  {
        try {
            Map<String, Object> payload = JwtUtil.verify(STORE_SECRET, token);
            return payload.get(payloadKey);
        }catch (JwtException e){
            return null;
        }
    }

    public static void main(String[] args) {

        System.out.println(new StoreTokenHelper().generate(154756));

        System.out.println(new StoreTokenHelper().verify("eyJhbGciOiJIUzI1NiJ9.eyJzdG9yZV9hY2NvdW50Ijo0MDEwMDd9.24iOa91raQCeBvKLQnvIkVeS4ykGXuHteJXTLJMFJ2o"));
    }
}