package com.insta360.store.business.configuration.common;

import com.insta360.compass.core.common.aop.AvoidRepeatableCommitAspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description 防重复申请幂等注解配置
 * @Date 2023/3/28
 */
@Configuration
public class AvoidRepeatableConfiguration {

    @Bean({"avoidRepeatableCommitAspect_"})
    public AvoidRepeatableCommitAspect avoidRepeatableCommitAspect() {
        return new AvoidRepeatableCommitAspect();
    }
}
