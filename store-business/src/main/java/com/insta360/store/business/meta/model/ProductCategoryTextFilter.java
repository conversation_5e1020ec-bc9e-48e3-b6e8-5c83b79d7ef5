package com.insta360.store.business.meta.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-10-11
 * @Description:
 */
@TableName("product_category_text_filter")
public class ProductCategoryTextFilter extends BaseModel<ProductCategoryTextFilter> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 文字筛选器id
     */
    private Integer textFilterMainId;

    /**
     * 筛选器id
     */
    private Integer adapterTypeMainId;

    /**
     * 内部名称
     */
    private String insideName;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTextFilterMainId() {
        return textFilterMainId;
    }

    public void setTextFilterMainId(Integer textFilterMainId) {
        this.textFilterMainId = textFilterMainId;
    }

    public Integer getAdapterTypeMainId() {
        return adapterTypeMainId;
    }

    public void setAdapterTypeMainId(Integer adapterTypeMainId) {
        this.adapterTypeMainId = adapterTypeMainId;
    }

    public String getInsideName() {
        return insideName;
    }

    public void setInsideName(String insideName) {
        this.insideName = insideName;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ProductCategoryTextFilter{" +
                "id=" + id +
                ", textFilterMainId=" + textFilterMainId +
                ", adapterTypeMainId=" + adapterTypeMainId +
                ", insideName='" + insideName + '\'' +
                ", orderIndex=" + orderIndex +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}