package com.insta360.store.business.admin.order.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 单一窗口批量申报DTO
 * @Date 2023/5/22
 */
public class BatchDeclarationDTO implements Serializable {

    /**
     * 运单号集合
     */
    @NotEmpty(message = "运单号不能为空")
    private List<String> waybillNumbers;

    /**
     * 申报口岸
     *
     * @see com.insta360.store.business.admin.order.enums.DeclarationPortType
     */
    @NotNull(message = "申报口岸不能为空")
    private Integer declarationPort;

    /**
     * 订单来源
     *
     * @see com.insta360.store.business.admin.order.enums.OrderSource
     */
    @NotNull(message = "订单来源不能为空")
    private Integer orderSource;

    /**
     * 报关币种
     *
     * @see com.insta360.store.business.admin.order.enums.DeclareCurrencyType
     */
    @NotNull(message = "报关币种不能为空")
    private Integer declareCurrency;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;


    public List<String> getWaybillNumbers() {
        return waybillNumbers;
    }

    public void setWaybillNumbers(List<String> waybillNumbers) {
        this.waybillNumbers = waybillNumbers;
    }

    public Integer getDeclarationPort() {
        return declarationPort;
    }

    public void setDeclarationPort(Integer declarationPort) {
        this.declarationPort = declarationPort;
    }

    public Integer getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Integer orderSource) {
        this.orderSource = orderSource;
    }

    public Integer getDeclareCurrency() {
        return declareCurrency;
    }

    public void setDeclareCurrency(Integer declareCurrency) {
        this.declareCurrency = declareCurrency;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}
