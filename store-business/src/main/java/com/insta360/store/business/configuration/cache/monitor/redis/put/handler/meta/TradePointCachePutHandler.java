package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CacheCountiesBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.outgoing.rpc.store.job.TradePointCachePutService;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Author: wbt
 * @Date: 2023/10/11
 * @Description:
 */
@Component
public class TradePointCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradePointCachePutHandler.class);

    @Autowired
    TradePointCachePutService tradePointCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) {
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}", this.getCachePutType(), this.isAsyncTaskable()));
        for (CacheCountiesBO cacheCounties : CacheConstant.COUNTIES) {
            tradePointCachePutService.listTradePointInfos(cacheCounties.getCountry(), cacheCounties.getLanguage());
        }

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.TRADE_POINT;
    }

    @Override
    public CachePutKeyParameterBO cacheParamParse(JoinPoint joinPoint) {
        return null;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.TRADE_POINT);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {
    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.FALSE;
    }
}
