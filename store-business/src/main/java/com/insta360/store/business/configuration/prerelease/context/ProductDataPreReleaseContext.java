package com.insta360.store.business.configuration.prerelease.context;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2022/04/24
 * @Description:
 */
@Scope("prototype")
@Component
public class ProductDataPreReleaseContext {

    /**
     * 新品数据上下文
     */
    private static final ThreadLocal<ProductDataPreReleaseContext> PRODUCT_DATA_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 是否展示新品数据标识（true：展示；false：不展示）
     */
    private Boolean isDisplay;

    public static void set(ProductDataPreReleaseContext productDataPreReleaseContext) {
        PRODUCT_DATA_THREAD_LOCAL.set(productDataPreReleaseContext);
    }

    public static ProductDataPreReleaseContext get() {
        return PRODUCT_DATA_THREAD_LOCAL.get();
    }

    public static void remove() {
        PRODUCT_DATA_THREAD_LOCAL.remove();
    }

    public Boolean getDisplay() {
        return isDisplay;
    }

    public void setDisplay(Boolean display) {
        isDisplay = display;
    }

    @Override
    public String toString() {
        return "ProductDataPreReleaseContext{" +
                "isDisplay=" + isDisplay +
                '}';
    }
}
