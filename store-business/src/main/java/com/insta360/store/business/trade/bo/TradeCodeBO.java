package com.insta360.store.business.trade.bo;

import com.insta360.store.business.meta.bo.TradeCode;

/**
 * 由于分销码可以绑定优惠码，在现有流程中很多通常在判断一个code的时候是通过查表来操作，若分销码绑定了优惠券会将分销码重置为优惠券，这会导致丢失了分销码的信息。
 * 在某段历史逻辑中，丢失分销码信息并没有问题，因为分销码未绑定优惠券是无法在结账页使用的，但是现在能在结账页使用所以需要将分销码信息保留，所以需要一个BO来保存分销码信息。
 *
 * 具体判断如下，如果 resellerCode 不为空这该券码必定是分销码，分销码是否绑定优惠券可以通过 bindCoupon 判断。
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19
 */
public class TradeCodeBO {

    /**
     * 交易代码
     */
    private TradeCode tradeCode;

    /**
     * 分销代码
     */
    private String resellerCode;

    /**
     * 绑定优惠券
     */
    private Boolean bindCoupon;

    public TradeCode getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(TradeCode tradeCode) {
        this.tradeCode = tradeCode;
    }

    public String getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(String resellerCode) {
        this.resellerCode = resellerCode;
    }

    public Boolean getBindCoupon() {
        return bindCoupon;
    }

    public void setBindCoupon(Boolean bindCoupon) {
        this.bindCoupon = bindCoupon;
    }
}
