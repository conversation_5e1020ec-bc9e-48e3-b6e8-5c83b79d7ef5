package com.insta360.store.business.meta.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-06-29
 * @Description: 离线税率
 */
@TableName("avalara_tax_rate_offline")
public class AvalaraTaxRateOffline extends BaseModel<AvalaraTaxRateOffline> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 国家二字码
     */
    private String countryCode;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 州地址
     */
    private String provinceCode;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 国家维度销售税率
     */
    private BigDecimal countrySalesTax;

    /**
     * 国家维度使用税率
     */
    private BigDecimal countryUseTax;

    /**
     * 州地址维度销售税
     */
    private BigDecimal stateSalesTax;

    /**
     * 州地址维度使用税
     */
    private BigDecimal stateUseTax;

    /**
     * 城市维度销售税
     */
    private BigDecimal citySalesTax;

    /**
     * 城市维度使用税
     */
    private BigDecimal cityUseTax;

    /**
     * 总销售税率
     */
    private BigDecimal totalSalesTax;

    /**
     * 总使用税率
     */
    private BigDecimal totalUseTax;

    /**
     * 是否包含运输税（0：不包含；1：包含）
     */
    private Boolean taxShippingAlone;

    /**
     * 是否包含打包和运费税（0：不包含；1：包含）
     */
    private Boolean taxShippingAndHandlingTogether;

    /**
     * 税率下载日期（yyyy-MM-dd）
     */
    private String downloadDate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public BigDecimal getCountrySalesTax() {
        return countrySalesTax;
    }

    public void setCountrySalesTax(BigDecimal countrySalesTax) {
        this.countrySalesTax = countrySalesTax;
    }

    public BigDecimal getCountryUseTax() {
        return countryUseTax;
    }

    public void setCountryUseTax(BigDecimal countryUseTax) {
        this.countryUseTax = countryUseTax;
    }

    public BigDecimal getStateSalesTax() {
        return stateSalesTax;
    }

    public void setStateSalesTax(BigDecimal stateSalesTax) {
        this.stateSalesTax = stateSalesTax;
    }

    public BigDecimal getStateUseTax() {
        return stateUseTax;
    }

    public void setStateUseTax(BigDecimal stateUseTax) {
        this.stateUseTax = stateUseTax;
    }

    public BigDecimal getCitySalesTax() {
        return citySalesTax;
    }

    public void setCitySalesTax(BigDecimal citySalesTax) {
        this.citySalesTax = citySalesTax;
    }

    public BigDecimal getCityUseTax() {
        return cityUseTax;
    }

    public void setCityUseTax(BigDecimal cityUseTax) {
        this.cityUseTax = cityUseTax;
    }

    public BigDecimal getTotalSalesTax() {
        return totalSalesTax;
    }

    public void setTotalSalesTax(BigDecimal totalSalesTax) {
        this.totalSalesTax = totalSalesTax;
    }

    public BigDecimal getTotalUseTax() {
        return totalUseTax;
    }

    public void setTotalUseTax(BigDecimal totalUseTax) {
        this.totalUseTax = totalUseTax;
    }

    public Boolean getTaxShippingAlone() {
        return taxShippingAlone;
    }

    public void setTaxShippingAlone(Boolean taxShippingAlone) {
        this.taxShippingAlone = taxShippingAlone;
    }

    public Boolean getTaxShippingAndHandlingTogether() {
        return taxShippingAndHandlingTogether;
    }

    public void setTaxShippingAndHandlingTogether(Boolean taxShippingAndHandlingTogether) {
        this.taxShippingAndHandlingTogether = taxShippingAndHandlingTogether;
    }

    public String getDownloadDate() {
        return downloadDate;
    }

    public void setDownloadDate(String downloadDate) {
        this.downloadDate = downloadDate;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "AvalaraTaxRateOffline{" +
                "id=" + id +
                ", countryCode=" + countryCode +
                ", zipCode=" + zipCode +
                ", provinceCode=" + provinceCode +
                ", countryName=" + countryName +
                ", cityName=" + cityName +
                ", countrySalesTax=" + countrySalesTax +
                ", countryUseTax=" + countryUseTax +
                ", stateSalesTax=" + stateSalesTax +
                ", stateUseTax=" + stateUseTax +
                ", citySalesTax=" + citySalesTax +
                ", cityUseTax=" + cityUseTax +
                ", totalSalesTax=" + totalSalesTax +
                ", totalUseTax=" + totalUseTax +
                ", taxShippingAlone=" + taxShippingAlone +
                ", taxShippingAndHandlingTogether=" + taxShippingAndHandlingTogether +
                ", downloadDate=" + downloadDate +
                ", createTime=" + createTime +
                "}";
    }
}