package com.insta360.store.business.meta.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.model.ProductCategoryImageTextInfo;
import com.insta360.store.business.meta.model.SearchRecommendItemText;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
public interface SearchRecommendItemTextService extends BaseService<SearchRecommendItemText> {

    /**
     * 删除指定子项的多语言文案
     * @param itemId
     */
    void deleteByItemId(Integer itemId);

    /**
     * 新增子项多语言文案
     *
     * @param searchRecommendItemTexts
     */
    void addSearchRecommendItemTexts(List<SearchRecommendItemText> searchRecommendItemTexts);

    /**
     * 根据子项id查找多语言文案
     * @param searchRecommendItemId
     */
    List<SearchRecommendItemText> listByItemId(Integer searchRecommendItemId);

    /**
     * 根据子项id集合查找多语言文案
     * @param itemIds
     */
    List<SearchRecommendItemText> listByItemIds(List<Integer> itemIds);

    /**
     * 根据子项id集合查找多语言文案(指定语言)
     * @param itemIds
     * @param language
     * @return
     */
    List<SearchRecommendItemText> listByItemIds(List<Integer> itemIds, InstaLanguage language);

    /**
     * 根据子项id集合查找多语言文案(指定国家语言)
     * @param itemIds
     * @param language
     * @param country
     * @return
     */
    List<SearchRecommendItemText> listByItemIds(List<Integer> itemIds, InstaLanguage language, InstaCountry country);
}
