package com.insta360.store.business.payment.service.impl.handler;


import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.JsonUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.ThreadUtil;
import com.insta360.store.business.cloud.constant.CloudSubscribeTextConstant;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.enums.SkuSubscribeType;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitService;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.constants.OrderBizMarkConstant;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.bo.PaymentHandleResult;
import com.insta360.store.business.payment.bo.PaymentReconciliationCheckBO;
import com.insta360.store.business.payment.bo.PaymentReconciliationCheckResultBO;
import com.insta360.store.business.payment.constants.OceanConstant;
import com.insta360.store.business.payment.enums.OceanPaymentMethod;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.ocean.config.OceanPaymentConfig;
import com.insta360.store.business.payment.lib.ocean.request.BaseOceanPaymentRequest;
import com.insta360.store.business.payment.lib.ocean.request.store.BaseOceanSubscribeRequest;
import com.insta360.store.business.payment.lib.ocean.request.store.CreateIDEncryptionPaymentRequest;
import com.insta360.store.business.payment.lib.ocean.response.CreateIdEncryptionPaymentResponse;
import com.insta360.store.business.payment.lib.ocean.response.CreateSubscribePaymentResponse;
import com.insta360.store.business.payment.service.impl.helper.OceanPaymentHelper;
import com.insta360.store.business.trade.enums.CreditCardPaymentActionEnum;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.UserPayInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2024/05/20
 * @Description:
 */
public abstract class BaseOceanPaymentSubscribeHandler extends BaseOceanPaymentHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseOceanPaymentSubscribeHandler.class);

    // 定义首次订阅和续费订阅（区分不同终端配置）
    protected PaymentSubscribeType subscribeType = PaymentSubscribeType.FIRST_SUBSCRIBE;

    // 是否是扣款重试
    protected Boolean isRenewDeductionRetry = Boolean.FALSE;

    @Autowired
    OceanPaymentHelper oceanPaymentHelper;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    OrderHelper orderHelper;

    @Override
    protected <T> T _payOrder(BasePaymentHandler.PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        // 初始化订阅信息
        this.initPaymentSubscribeType(order);
        this.isRenewDeductionRetry = paymentExtra.getIsRenewDeductionRetry();
        // 保存续费订单payment info
        this.saveOrUpdatePaymentInfo(order);
        return super._payOrder(paymentInfo, paymentExtra);
    }

    /**
     * 初始化订阅支付类型
     *
     * @param order
     */
    public void initPaymentSubscribeType(Order order) {
        subscribeType = order.paymentSubscribeType();
        this.order = order;
    }

    @Override
    protected <T> T preHandlePaymentResult(Order order, BaseOceanPaymentRequest paymentRequest) {
        BaseOceanSubscribeRequest request = (BaseOceanSubscribeRequest) paymentRequest;
        // 快捷支付id为空，不发起支付请求
        if (StringUtil.isBlank(request.getQuickpay_id())) {
            LOGGER.info(String.format(" 快捷支付id为空。。{%s}", request));
            return null;
        }
        String responseStr = request.executePost();
        JSONObject resultJson = JsonUtil.xmlStrToJson(responseStr);
        if (Objects.isNull(resultJson)) {
            LOGGER.info(String.format("前置支付结果解析失败！订单号 {%s} 支付响应{%s}", order.getOrderNumber(), responseStr));
            // 发送飞书消息通知异常
            FeiShuMessageUtil.storeGeneralMessage(String.format("前置支付结果解析失败！订单号 {%s} 支付响应{%s}", order.getOrderNumber(), responseStr)
                    , FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return null;
        }
        JSONObject responseJson = resultJson.getJSONObject("response");
        CreateSubscribePaymentResponse response = new CreateSubscribePaymentResponse(responseJson);
        LOGGER.info("Credit card payment response info : {}, order_number : {} ", resultJson.toJSONString(), request.getOrder_number());

        // 签名判断
        boolean isSignatureValid = response.isSignatureValid(getOceanPaymentConfig().getSecureCode());
        if (!isSignatureValid) {
            FeiShuMessageUtil.storeGeneralMessage("前置支付响应验签失败", FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 发送钱海支付失败通知
            FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.OCEAN_SUBSCRIBE_PAYMENT_FAIL_TEXT, order.getOrderNumber(), response.getPaymentStatus(), response.getPaymentDetails()),
                    FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.LCX);
            return null;
        }

        // 状态返回值判断 && 3D验证链接判断
        if (response.getPaymentStatus() == -1 && response.getPayUrl() != null) {
            LOGGER.info("Is 3d payment request order. pay_url:" + response.getPayUrl() + ", requestParam:" + request);
            return (T) response.getPayUrl();
        }

        // 续费前置支付结果处理
        if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(subscribeType) && !order.isMark(OrderBizMarkConstant.cloud_subscribe_renew_order_pay)) {
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            this.doHandlePaymentResult(response, responseJson, orderPayment, responseStr, resultJson);
            // 支付成功返回标识
            if (OceanConstant.OCEAN_TRANSACTION.equals(response.getNoticeType()) && 1 == response.getPaymentStatus()) {
                return (T) Boolean.TRUE;
            }

            // 交易支付失败处理
            if (OceanConstant.OCEAN_TRANSACTION.equals(response.getNoticeType()) && 0 == response.getPaymentStatus()) {
                // 处理是否需重试
                oceanPaymentHelper.handleRetryLogic(order, response.getPaymentDetails());
            }

            // 发送续费订单扣款失败通知
            FeiShuMessageUtil.storeGeneralMessage(String.format(CloudSubscribeTextConstant.RENEW_ORDER_PAYMENT_EXCEPTION_TEXT,
                    order.getOrderNumber(), this.getPaymentChannel().name(), response.getPaymentStatus(), response.getPaymentDetails()),
                    FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.LCX);
            return (T) Boolean.FALSE;
        }

        return null;
    }

    @Override
    public void _handlePaymentResult(Object paymentResult, PaymentHandleResult handleResult) {
        LOGGER.info("钱海支付结果回传通知。paymentResult:{}", paymentResult);
        JSONObject resultJson = JsonUtil.xmlStrToJson((String) paymentResult);
        JSONObject responseJson = resultJson.getJSONObject("response");
        CreateSubscribePaymentResponse paymentResponse = new CreateSubscribePaymentResponse(responseJson);

        OrderPayment orderPayment = orderPaymentService.getByOrderNumber(paymentResponse.getOrderNumber());
        if (orderPayment == null) {
            LOGGER.error("ocean capture fail. message: order payment not found. paymentResult:{}", paymentResult);
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        Order order = orderService.getByOrderNumber(paymentResponse.getOrderNumber());
        if (order == null) {
            LOGGER.error("ocean capture fail. message: order not found. paymentResult:{}", paymentResult);
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }
        // 初始化支付订阅类型
        this.initPaymentSubscribeType(order);

        // 首订才需要更新支付信息
        // 续费失败订单支付也更新支付信息
        if (PaymentSubscribeType.FIRST_SUBSCRIBE.equals(order.paymentSubscribeType()) || order.isMark(OrderBizMarkConstant.cloud_subscribe_renew_order_pay)) {
            // 处理支付结果
            this.doHandlePaymentResult(paymentResponse, responseJson, orderPayment, paymentResult, resultJson);

            // 更新支付信息
            this.updateOceanPayIdInfo(paymentResponse);
        }
    }

    @Override
    public PaymentReconciliationCheckResultBO handleTransactionReconciliation(PaymentReconciliationCheckBO paymentReconciliationCheckParam) {
        Order order = paymentReconciliationCheckParam.getOrder();
        if (Objects.isNull(order)) {
            LOGGER.error("[支付后交易对账]Ocean 延迟交易对账失败。 订单不存在。param:{}", paymentReconciliationCheckParam);
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        this.initPaymentSubscribeType(order);
        return super.handleTransactionReconciliation(paymentReconciliationCheckParam);
    }

    /**
     * 更新支付信息
     *
     * @param paymentResponse
     */
    protected void updateOceanPayIdInfo(CreateSubscribePaymentResponse paymentResponse) {
        // 支付成功更新
        if (OceanConstant.OCEAN_TRANSACTION.equals(paymentResponse.getNoticeType())
                && paymentResponse.getPaymentStatus() == 1) {
            if (StringUtil.isBlank(paymentResponse.getQuickPayId()) || StringUtil.isBlank(paymentResponse.getCardType())) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("ocean 支付成功没回传pay id/card type！订单号{%s}", paymentResponse.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return;
            }
            oceanPaymentHelper.updateOceanPaymentPayIdAndCardInfo(paymentResponse.getOrderNumber(), paymentResponse.getQuickPayId()
                    , paymentResponse.getCardType(), paymentResponse.getCardCountry(), paymentResponse.getCardNumber());
        }
    }

    @Override
    protected BaseOceanPaymentRequest buildPaymentRequest(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        // 构造订阅支付请求
        BaseOceanSubscribeRequest buildPaymentRequest = (BaseOceanSubscribeRequest) super.buildPaymentRequest(paymentInfo, paymentExtra);
        buildPaymentRequest.setQuickpay_id(getQuickPayId(paymentInfo, paymentExtra));
        buildPaymentRequest.setService_mode(getServiceMode());
        buildPaymentRequest.setOrder_notes(OceanConstant.FIRST_ORDER_NOTES);
        // 续费订单参数构建，并且非续费失败订单
        if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(subscribeType) && !order.isMark(OrderBizMarkConstant.cloud_subscribe_renew_order_pay)) {
            packRenewOrderRequest(paymentExtra.getStoreAccount(), buildPaymentRequest);
        }
        return buildPaymentRequest;
    }

    /**
     * 续费订单参数构建
     *
     * @param storeAccount
     * @param buildPaymentRequest
     */
    protected void packRenewOrderRequest(StoreAccount storeAccount, BaseOceanSubscribeRequest buildPaymentRequest) {
        if (Objects.isNull(storeAccount)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(storeAccount.getInstaAccount());
        if (Objects.isNull(userPayInfo) || StringUtil.isBlank(userPayInfo.getPayId())) {
            // 发送飞书消息通知异常
            FeiShuMessageUtil.storeGeneralMessage(String.format("钱海支付续费订单账户订阅状态不符合续费条件. 原始订单号: [%s]，instaAccount: {%s}",
                    buildPaymentRequest.getOrder_number(), storeAccount.getInstaAccount()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        CloudStorageStoreBenefit cloudStorageStoreBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(storeAccount.getInstaAccount());
        if (Objects.isNull(cloudStorageStoreBenefit)) {
            // 发送飞书消息通知异常
            FeiShuMessageUtil.storeGeneralMessage(String.format("钱海支付续费订单账户权益不符合续费条件. 原始订单号: [%s]，instaAccount: {%s}",
                    buildPaymentRequest.getOrder_number(), storeAccount.getInstaAccount()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }
        buildPaymentRequest.setQuickpay_id(AESUtil.decode(AESUtil.PAY_TOKEN_KEY, userPayInfo.getPayId()));
        buildPaymentRequest.setService_mode(getRenewOrderServiceMode(cloudStorageStoreBenefit));
        buildPaymentRequest.setOrder_notes(OceanConstant.RENEW_ORDER_NOTES + cloudStorageStoreBenefit.getPeriodNumber());
        Order order = orderService.getByOrderNumber(buildPaymentRequest.getOrder_number());
        if (Objects.isNull(order)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }
        buildPaymentRequest.setBilling_ip(StringUtil.isNotBlank(order.getIp()) ? order.getIp() : OceanConstant.DEFAULT_IP);
        buildPaymentRequest.setMethods(OceanPaymentMethod.credit_card.getMethodParaName());
    }

    /**
     * 获取续费订单订阅期数
     *
     * @param cloudStorageStoreBenefit
     * @return
     */
    protected String getRenewOrderServiceMode(CloudStorageStoreBenefit cloudStorageStoreBenefit) {
        SkuSubscribeType skuSubscribeType = orderHelper.getSkuSubscribeType(this.order);
        if (SkuSubscribeType.MONTHLY.equals(skuSubscribeType)) {
            return cloudStorageStoreBenefit.getPeriodNumber() == 1 ? OceanConstant.SERVICE_MODE_M2 : OceanConstant.SERVICE_MODE_M3;
        }
        return cloudStorageStoreBenefit.getPeriodNumber() == 1 ? OceanConstant.SERVICE_MODE_Y2 : OceanConstant.SERVICE_MODE_Y3;
    }

    /**
     * 获取订阅期数
     *
     * @return
     */
    protected String getServiceMode() {
        SkuSubscribeType skuSubscribeType = orderHelper.getSkuSubscribeType(this.order);
        return SkuSubscribeType.MONTHLY.equals(skuSubscribeType) ? OceanConstant.SERVICE_MODE_M1 : OceanConstant.SERVICE_MODE_Y1;
    }

    /**
     * 获取快捷支付id
     *
     * @return
     */
    protected String getQuickPayId(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        // 处理首次订阅
        // 钱海续费失败订单允许用户走首次订阅流程
        if (!PaymentSubscribeType.FIRST_SUBSCRIBE.equals(this.order.paymentSubscribeType())
                && !(PaymentSubscribeType.RENEW_SUBSCRIBE.equals(this.order.paymentSubscribeType())
                && order.isMark(OrderBizMarkConstant.cloud_subscribe_renew_order_pay))) {
            return StringUtils.EMPTY;
        }
        // 首购
        OceanPaymentConfig oceanPaymentConfig = getOceanPaymentConfig();
        CreateIDEncryptionPaymentRequest encryptionPaymentRequest = new CreateIDEncryptionPaymentRequest(oceanPaymentConfig);
        // 构建请求参数
        buildCreateIDEncryptionParam(encryptionPaymentRequest, paymentInfo, paymentExtra);

        encryptionPaymentRequest.sign();

        JSONObject resultJson = JsonUtil.xmlStrToJson(encryptionPaymentRequest.executePost());
        CreateIdEncryptionPaymentResponse response = new CreateIdEncryptionPaymentResponse(resultJson.getJSONObject("response"));
        LOGGER.info("Credit card payment response info : {}, order_number : {} ", resultJson.toJSONString(), paymentInfo.orderNumber);
        boolean signatureValid = response.isSignatureValid(oceanPaymentConfig.getSecureCode());
        if (!signatureValid) {
            FeiShuMessageUtil.storeGeneralMessage("ocean 快捷支付id响应验签失败", FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(PaymentErrorCode.InvalidQuickPayIdException);
        }

        // 非升级订单才提前保存pay id
        // 钱海续费失败订单支付前置不记录pay id
        if (!ServiceScenesType.UPGRADE.equals(ServiceScenesType.parse(order.getSubscribeScenesType())) && !(PaymentSubscribeType.RENEW_SUBSCRIBE.equals(this.order.paymentSubscribeType())
                && order.isMark(OrderBizMarkConstant.cloud_subscribe_renew_order_pay))) {
            // 保存快捷支付id和卡信息
            oceanPaymentHelper.updateOceanPaymentPayIdAndCardInfo(paymentInfo.orderNumber, response.getQuickPayId(),
                    response.getCardType(), response.getCardCountry(), response.getCardNumber());
        }

        // 保存get pay id result
        ThreadUtil.execute(() -> this.saveAuthPaymentResult(CreditCardPaymentActionEnum.OCEAN_GET_PAYMENT_ID, order.getOrderNumber(),
                CreditCardPaymentActionEnum.OCEAN_GET_PAYMENT_ID.getAuthType(), response.getQuickPayStatus(), response.getQuickPayDetails()));
        return response.getQuickPayId();
    }

    /**
     * 构建创建快捷支付id参数
     *
     * @param encryptionPaymentRequest
     */
    protected void buildCreateIDEncryptionParam(CreateIDEncryptionPaymentRequest encryptionPaymentRequest, PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        encryptionPaymentRequest.setCard_data(paymentExtra.getCardData());
        encryptionPaymentRequest.setCustomer_id(String.valueOf(paymentInfo.storeAccountId));
        encryptionPaymentRequest.setMethods(OceanPaymentMethod.credit_card.getMethodParaName());
        encryptionPaymentRequest.setOrder_number(paymentInfo.orderNumber);

        // 账单地址
        encryptionPaymentRequest.setBilling_firstName(paymentInfo.billing_firstName);
        encryptionPaymentRequest.setBilling_lastName(paymentInfo.billing_lastName);
        encryptionPaymentRequest.setBilling_email(paymentInfo.email);
        encryptionPaymentRequest.setBilling_phone(paymentInfo.billing_phone);
        encryptionPaymentRequest.setBilling_city(paymentInfo.billing_city);
        encryptionPaymentRequest.setBilling_zip(paymentInfo.billing_zipCode);
        encryptionPaymentRequest.setBilling_address(paymentInfo.billing_address);
        encryptionPaymentRequest.setBilling_state(paymentInfo.billing_oceanCode);
        encryptionPaymentRequest.setBilling_country(paymentInfo.instaCountry.name());
    }

    /**
     * 保存payment info
     *
     * @param order
     */
    protected void saveOrUpdatePaymentInfo(Order order) {
        // 记录续费订单
        // 续费失败订单会前置记录
        if (!PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType()) || order.isMark(OrderBizMarkConstant.cloud_subscribe_renew_order_pay)) {
            return;
        }

        // 需拿订阅中订单PayChannelId
        Integer userId = order.getUserId();

        CloudStorageSubscribe storageSubscribe = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(userId);
        if (Objects.isNull(storageSubscribe)) {
            throw new InstaException(PaymentErrorCode.PaymentSubscribeInfoMissingException);
        }

        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(userId);
        if (Objects.isNull(userPayInfo)) {
            throw new InstaException(PaymentErrorCode.PaymentSubscribeInfoMissingException);
        }

        CreditCardPaymentInfo oldCreditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(storageSubscribe.getOrderNumber());
        if (Objects.isNull(oldCreditCardPaymentInfo)) {
            LOGGER.error(String.format("订单{%s} cardPaymentInfo无效 用户ID:{%s}", storageSubscribe.getOrderNumber(), userId));
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单{%s} cardPaymentInfo无效 用户ID:{%s}", storageSubscribe.getOrderNumber(), userId),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return;
        }

        Integer oldPaymentChannelId = oldCreditCardPaymentInfo.getPayChannelId();
        CreditCardPaymentInfo creditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(order.getOrderNumber());
        // 可能存在其他支付方式记录
        if (Objects.isNull(creditCardPaymentInfo)) {
            creditCardPaymentInfo = new CreditCardPaymentInfo();
            creditCardPaymentInfo.setCreateTime(LocalDateTime.now());
        }
        creditCardPaymentInfo.setOrderNumber(order.getOrderNumber());
        creditCardPaymentInfo.setPayMethod(StorePaymentMethodEnum.OCEAN_PAYMENT.getName());
        creditCardPaymentInfo.setPayChannelId(Objects.isNull(oldPaymentChannelId) || oldPaymentChannelId == -1 ? OceanConstant.DEFAULT_OCEAN_CHANNEL : oldPaymentChannelId);
        creditCardPaymentInfo.setCreditCardType(userPayInfo.getCardType());
        creditCardPaymentInfo.setCardBin(oldCreditCardPaymentInfo.getCardBin());
        creditCardPaymentInfo.setOrderCountry(order.getArea());
        creditCardPaymentInfo.setOrderCreateTime(order.getCreateTime());
        creditCardPaymentInfoService.saveOrUpdate(creditCardPaymentInfo);
    }
}
