package com.insta360.store.business.integration.easypost.service.impl.helper;

import com.easypost.exception.EasyPostException;
import com.easypost.model.Rate;
import com.easypost.model.Shipment;
import com.easypost.service.EasyPostClient;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.utils.ProfileUtil;
import com.insta360.store.business.integration.easypost.EasyPostConfiguration;
import com.insta360.store.business.integration.easypost.EasyPostSingletonClient;
import com.insta360.store.business.integration.easypost.bo.CreateShipmentContextBO;
import com.insta360.store.business.integration.easypost.bo.ShipmentResponseBO;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.rma.exception.RmaErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 运输标签生成帮助类
 * @Date 2024/4/24
 */
@Component
public class EasyPostHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(EasyPostHelper.class);

    @Autowired
    EasyPostConfiguration easyPostConfiguration;

    @Autowired
    OrderDeliveryService orderDeliveryService;


    /**
     * 根据订单信息获取配送标签。
     *
     * @param createShipmentContext 包含订单及其配送信息的上下文对象
     * @return ShipmentResponseBO 包含配送标签的响应对象
     * @throws InstaException 如果订单信息不完整或找不到对应订单，抛出异常
     */
    public ShipmentResponseBO getShippingLabel(CreateShipmentContextBO createShipmentContext) {
        // 校验传入的上下文对象是否为 null
        if (Objects.isNull(createShipmentContext)) {
            throw new InstaException(RmaErrorCode.ConditionNotMetException);
        }

        // 获取并校验订单对象是否为 null
        Order order = createShipmentContext.getOrder();
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 获取订单配送地址并校验其是否为空
        OrderDelivery orderDelivery = createShipmentContext.getOrderDelivery();
        if (Objects.isNull(orderDelivery)) {
            throw new InstaException(OrderErrorCode.OrderDeliveryMissingException);
        }

        // 校验订单和配送地址的匹配性
        if (!order.getId().equals(orderDelivery.getOrder())) {
            throw new InstaException(OrderErrorCode.OrderDeliveryMissingException);
        }

        // 生成并返回配送标签
        return this.generateShippingLabel(order, orderDelivery, createShipmentContext.getCountry());
    }


    /**
     * 生成运输标签。
     *
     * @param order         订单信息，包含订单的详细信息。
     * @param orderDelivery 订单交付信息，包含交付地址等。
     * @param country       目标国家，用于确定承运商和服务。
     * @return ShipmentResponseBO 运输响应业务对象，包含创建的运输标签的信息。
     * @throws InstaException 如果创建运输标签过程中发生错误。
     */
    private ShipmentResponseBO generateShippingLabel(Order order, OrderDelivery orderDelivery, InstaCountry country) {
        // 整合订单、交付信息和目标国家为运输信息的Map
        Map<String, Object> shipmentMap = this.getShipmentMap(order, orderDelivery, country);
        // 获取指定国家支持的承运商和服务
        Map<String, List<String>> carriersMap = this.getBuyCarriersMap(country);
        // 提取购买承运商列表
        List<String> buyCarriers = carriersMap.get("buyCarriers");
        // 提取购买服务列表
        List<String> buyServices = carriersMap.get("buyServices");

        ShipmentResponseBO shipmentResponse;
        try {
            // 使用EasyPost客户端实例创建运输和购买运输服务
            EasyPostClient client = EasyPostSingletonClient.INSTANCE.getClient(easyPostConfiguration.getApiKeyNew());
            // 根据提供的信息创建货物运输
            Shipment shipmentCreated = client.shipment.create(shipmentMap);
            // 购买选定的运输服务
            Rate rate = shipmentCreated.lowestRate(buyCarriers, buyServices);
            Shipment shipmentBuy = client.shipment.buy(shipmentCreated.getId(), rate);
            // 将购买的运输服务信息转换为业务对象
            shipmentResponse = ShipmentResponseBO.parse(shipmentBuy);
            LOGGER.info(String.format("[shippingLabel开具]shipmentResponse:%s", shipmentResponse));
        } catch (EasyPostException e) {
            // 记录运输标签创建失败，并抛出运输标签创建异常
            LOGGER.error(String.format("调用EasyPost开具ShippingLabel失败.orderId:%s", order.getId()), e);
            throw new InstaException(-1, String.format("ShippingLabel创建失败,原因:{%s},请检查或手动生成Label", e.getMessage()));
        }

        return shipmentResponse;
    }

    /**
     * 获取运输信息Map
     *
     * @param order
     * @param orderDelivery
     * @param country
     * @return
     */
    private Map<String, Object> getShipmentMap(Order order, OrderDelivery orderDelivery, InstaCountry country) {
        // 获取发货地址
        Map<String, Object> fromAddressMap = this.getFromAddressMap(orderDelivery, order);
        // 获取收货地址
        Map<String, Object> defaultToAddressMap = this.getDefaultToAddressMap(country);
        // 获取包裹信息
        Map<String, Object> parcelMap = this.getParcelMap();
        // 获取其他选项信息
        Map<String, Object> optionsMap = this.getOptions(order, orderDelivery.getCountryCode(), country);
        // 获取承运商账号ID列表
        List<String> carriersAccountIds = this.getCarriersAccountIds(country);

        Map<String, Object> shipmentMap = new HashMap<>(4);
        shipmentMap.put("from_address", fromAddressMap);
        shipmentMap.put("to_address", defaultToAddressMap);
        shipmentMap.put("parcel", parcelMap);
        shipmentMap.put("options", optionsMap);
        shipmentMap.put("carrier_accounts", carriersAccountIds);
        // fix "The country associated with Shippers ShipperNumber must be the same as the shipments Shippers country"
        if (InstaCountry.EU.equals(country)) {
            shipmentMap.put("return_address", InstaCountry.DE.equals(order.country()) ? fromAddressMap : defaultToAddressMap);
        }
        return shipmentMap;
    }

    /**
     * 获取发货地址
     *
     * @param orderDelivery
     * @param order
     * @return
     */
    private Map<String, Object> getFromAddressMap(OrderDelivery orderDelivery, Order order) {
        Map<String, Object> fromAddressMap = new HashMap<>(9);
        if (StringUtils.isNotBlank(orderDelivery.getFirstName()) && StringUtils.isNotBlank(orderDelivery.getLastName())) {
            fromAddressMap.put("name", ProfileUtil.getFullName(orderDelivery.getFirstName(), orderDelivery.getLastName()));
        }
        if (StringUtils.isNotBlank(orderDelivery.getAddress())) {
            fromAddressMap.put("street1", orderDelivery.getAddress());
        }
        if (StringUtils.isNotBlank(orderDelivery.getSubAddress())) {
            fromAddressMap.put("street2", orderDelivery.getSubAddress());
        }
        if (StringUtils.isNotBlank(orderDelivery.getCity())) {
            fromAddressMap.put("city", orderDelivery.getCity());
        }
        if (StringUtils.isNotBlank(orderDelivery.getProvince())) {
            fromAddressMap.put("state", orderDelivery.getProvince());
        }
        if (StringUtils.isNotBlank(orderDelivery.getZipCode())) {
            fromAddressMap.put("zip", orderDelivery.getZipCode());
        }
        if (StringUtils.isNotBlank(orderDelivery.getCountryCode())) {
            fromAddressMap.put("country", orderDelivery.getCountryCode());
        }
        if (StringUtils.isNotBlank(orderDelivery.getPhone())) {
            fromAddressMap.put("phone", ProfileUtil.getFullPhone(orderDelivery.getPhoneCode(), orderDelivery.getPhone()));
        }
        if (StringUtils.isNotBlank(order.getContactEmail())) {
            fromAddressMap.put("email", order.getContactEmail());
        }
        // 校验地址
        fromAddressMap.put("verify", true);

        return fromAddressMap;
    }

    /**
     * 获取目的地地址信息
     *
     * @param country
     * @return
     */
    private Map<String, Object> getDefaultToAddressMap(InstaCountry country) {
        if (InstaCountry.US.equals(country)) {
            return this.getUsToAddressMap();
        }

        if (InstaCountry.EU.equals(country)) {
            return this.getEuToAddressMap();
        }

        throw new InstaException(-1, String.format("暂不支持该地区[%s]", country.name()));
    }

    /**
     * 获取购买运营商信息
     *
     * @return
     */
    private Map<String, List<String>> getBuyCarriersMap(InstaCountry country) {
        Map<String, List<String>> carriersMap = new HashMap<>(2);
        if (InstaCountry.US.equals(country)) {
            carriersMap.put("buyCarriers", Lists.newArrayList("FedEx"));
            carriersMap.put("buyServices", Lists.newArrayList("FEDEX_GROUND"));
        } else if (InstaCountry.EU.equals(country)) {
            carriersMap.put("buyCarriers", Lists.newArrayList("UPS"));
            carriersMap.put("buyServices", Lists.newArrayList("UPSStandard"));
        }

        return carriersMap;
    }

    /**
     * 获取包裹信息
     *
     * @return
     */
    private Map<String, Object> getParcelMap() {
        Map<String, Object> parcelMap = new HashMap<String, Object>(1);
        parcelMap.put("weight", 1);
        return parcelMap;
    }

    /**
     * 获取其他选项信息
     *
     * @return
     */
    private Map<String, Object> getOptions(Order order, String fromCountry, InstaCountry toCountry) {
        Map<String, Object> optionsMap = new HashMap<String, Object>(1);
        optionsMap.put("print_custom_1", order.getOrderNumber());

        if (InstaCountry.EU.equals(toCountry)) {
            toCountry = InstaCountry.DE;
        }
        // fix: "The country associated with Shippers ShipperNumber must be the same as the shipments Shippers country"
        if (!toCountry.name().equals(fromCountry)) {
            optionsMap.put("import_control", "PRINT");
            optionsMap.put("import_control_description", "Return Shipment");
        }
        return optionsMap;
    }

    /**
     * 获取美国的目的地地址信息
     *
     * @return
     */
    private Map<String, Object> getUsToAddressMap() {
        Map<String, Object> toAddressMap = new HashMap<String, Object>(8);
        toAddressMap.put("company", "Insta360 Ecommerce Return");
        toAddressMap.put("street1", "2323 Main St");
        toAddressMap.put("street2", "Unit 16");
        toAddressMap.put("city", "Irvine");
        toAddressMap.put("state", "CA");
        toAddressMap.put("zip", "92614");
        toAddressMap.put("phone", "8006920360");
        toAddressMap.put("country", InstaCountry.US.name());
        return toAddressMap;
    }

    /**
     * 获取欧洲地区的目的地地址信息。
     * <p>
     * 该方法不接受任何参数，返回一个包含地址详细信息的Map对象。
     * 地址信息包括公司名称、街道1、街道2、城市、邮政编码和电话号码。
     *
     * @return 返回一个Map对象，其中包含欧洲目的地地址的详细信息。
     */
    private Map<String, Object> getEuToAddressMap() {
        Map<String, Object> toAddressMap = new HashMap<String, Object>(7);
        toAddressMap.put("company", "Vitech GmbH-Official Store");
        toAddressMap.put("street1", "Dr.-Ruben-Rausing Str. 2");
        toAddressMap.put("street2", "Lager Halle 8A");
        toAddressMap.put("city", "Hochheim am Main.");
        toAddressMap.put("state", "");
        toAddressMap.put("zip", "65239");
        toAddressMap.put("phone", "+49 **********");
        toAddressMap.put("country", InstaCountry.DE.name());
        return toAddressMap;
    }

    /**
     * 获取可用的运输商账号ID列表
     *
     * @return
     */
    private List<String> getCarriersAccountIds(InstaCountry country) {
        switch (country) {
            case US:
                return Lists.newArrayList(easyPostConfiguration.getFedexAccountId());
            case EU:
                return Lists.newArrayList(easyPostConfiguration.getUpsAccountId());
            default:
                throw new InstaException(-1, String.format("暂不支持该地区[%s]", country.name()));
        }
    }
}
