package com.insta360.store.business.trade.dto.condition;

import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.reseller.dto.condition.ResellerCode;

/**
 * @Author: hyc
 * @Date: 2019/4/13
 * @Description:
 */
public class TradePriceParam {

    private CommodityPrice commodityPrice;


    private ResellerCode resellerCode;

    public CommodityPrice getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(CommodityPrice commodityPrice) {
        this.commodityPrice = commodityPrice;
    }


    public ResellerCode getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(ResellerCode resellerCode) {
        this.resellerCode = resellerCode;
    }
}
