package com.insta360.store.business.prime.lib.response;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public class ProductsResponse implements PrimeResponse {
    private Products products;

    public Products getProducts() {
        return products;
    }

    public void setProducts(Products products) {
        this.products = products;
    }

    public static class Products {
        private PageInfo pageInfo;
        private List<Edge> edges;

        public PageInfo getPageInfo() {
            return pageInfo;
        }

        public void setPageInfo(PageInfo pageInfo) {
            this.pageInfo = pageInfo;
        }

        public List<Edge> getEdges() {
            return edges;
        }

        public void setEdges(List<Edge> edges) {
            this.edges = edges;
        }
    }

    public static class PageInfo {
        private Boolean hasNextPage;
        private Boolean hasPreviousPage;
        private String startCursor;
        private String endCursor;

        public Boolean getHasNextPage() {
            return hasNextPage;
        }

        public void setHasNextPage(Boolean hasNextPage) {
            this.hasNextPage = hasNextPage;
        }

        public Boolean getHasPreviousPage() {
            return hasPreviousPage;
        }

        public void setHasPreviousPage(Boolean hasPreviousPage) {
            this.hasPreviousPage = hasPreviousPage;
        }

        public String getStartCursor() {
            return startCursor;
        }

        public void setStartCursor(String startCursor) {
            this.startCursor = startCursor;
        }

        public String getEndCursor() {
            return endCursor;
        }

        public void setEndCursor(String endCursor) {
            this.endCursor = endCursor;
        }
    }

    public static class Edge {
        private String cursor;
        private Node node;

        public String getCursor() {
            return cursor;
        }

        public void setCursor(String cursor) {
            this.cursor = cursor;
        }

        public Node getNode() {
            return node;
        }

        public void setNode(Node node) {
            this.node = node;
        }
    }

    public static class Node {
        private String id;
        private ExternalId externalId;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public ExternalId getExternalId() {
            return externalId;
        }

        public void setExternalId(ExternalId externalId) {
            this.externalId = externalId;
        }
    }

    public static class ExternalId {
        private String value;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}