package com.insta360.store.business.prime.bo;

import com.insta360.store.business.prime.model.PrimeCommodity;
import com.insta360.store.business.prime.model.PrimeCommodityInclude;

import java.io.Serializable;
import java.util.List;

/**
 * Prime商品领域业务对象
 * <p>
 * 该类封装了Prime商品及其包含的商品列表信息，用于在业务逻辑和展示层之间传递数据。
 * 作为领域模型的一部分，它代表了一个完整的Prime商品，包括主商品信息和其包含的子商品信息。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/9
 */
public class PrimeCommodityDomainBO implements Serializable {
    /**
     * Prime商品基本信息
     * 包含商品ID、名称、类型、Prime产品ID等核心信息
     */
    private PrimeCommodity primeCommodity;

    /**
     * Prime商品包含的子商品列表
     * 对于Bundle类型的商品，此字段包含该套餐中包含的所有子商品信息
     */
    private List<PrimeCommodityInclude> primeCommodityIncludes;

    /**
     * 默认构造函数
     */
    public PrimeCommodityDomainBO() {
    }

    /**
     * 全参数构造函数
     *
     * @param primeCommodity Prime商品基本信息
     * @param primeCommodityIncludes Prime商品包含的子商品列表
     */
    public PrimeCommodityDomainBO(PrimeCommodity primeCommodity, List<PrimeCommodityInclude> primeCommodityIncludes) {
        this.primeCommodity = primeCommodity;
        this.primeCommodityIncludes = primeCommodityIncludes;
    }

    /**
     * 获取Prime商品基本信息
     *
     * @return Prime商品基本信息对象
     */
    public PrimeCommodity getPrimeCommodity() {
        return primeCommodity;
    }

    /**
     * 设置Prime商品基本信息
     *
     * @param primeCommodity Prime商品基本信息对象
     */
    public void setPrimeCommodity(PrimeCommodity primeCommodity) {
        this.primeCommodity = primeCommodity;
    }

    /**
     * 获取Prime商品包含的子商品列表
     *
     * @return 子商品列表
     */
    public List<PrimeCommodityInclude> getPrimeCommodityIncludes() {
        return primeCommodityIncludes;
    }

    /**
     * 设置Prime商品包含的子商品列表
     *
     * @param primeCommodityIncludes 子商品列表
     */
    public void setPrimeCommodityIncludes(List<PrimeCommodityInclude> primeCommodityIncludes) {
        this.primeCommodityIncludes = primeCommodityIncludes;
    }
}
