package com.insta360.store.business.review.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-27
 * @Description:
 */
@TableName("review_state_record")
public class ReviewStateRecord extends BaseModel<ReviewStateRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 评论id
     */
    private Integer reviewId;

    /**
     * 原始状态
     */
    private Integer stateFrom;

    /**
     * 目标状态
     */
    private Integer stateTo;

    /**
     * 操作人工号
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReviewId() {
        return reviewId;
    }

    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }

    public Integer getStateFrom() {
        return stateFrom;
    }

    public void setStateFrom(Integer stateFrom) {
        this.stateFrom = stateFrom;
    }

    public Integer getStateTo() {
        return stateTo;
    }

    public void setStateTo(Integer stateTo) {
        this.stateTo = stateTo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ReviewStateRecord{" +
                "id=" + id +
                ", reviewId=" + reviewId +
                ", stateFrom=" + stateFrom +
                ", stateTo=" + stateTo +
                ", operator=" + operator +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}