package com.insta360.store.business.meta.bo;

import com.insta360.store.business.meta.model.ActivityDynamicParam;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public class ActivityDynamicParamBO implements Serializable {

    /**
     * 活动配置ID
     */
    private Integer id;

    /**
     * 组件信息
     */
    @Valid
    @NotNull
    private List<ActivityDynamicParamInfoBO> dynamicParamInfos;

    /**
     * 构建动态参数信息
     *
     * @return
     */
    public List<ActivityDynamicParam> buildActivityDynamicParams() {
        if (CollectionUtils.isEmpty(dynamicParamInfos)) {
            return new ArrayList<>(0);
        }
        // 使用Stream API遍历componentInfos集合
        return dynamicParamInfos.stream().map(dynamicParamInfoBo -> {
            ActivityDynamicParam dynamicParam = new ActivityDynamicParam();
            dynamicParam.setActivityId(this.id);
            dynamicParam.setParamType(dynamicParamInfoBo.getParamType().name());
            dynamicParam.setComponentId(dynamicParamInfoBo.getComponentId());
            dynamicParam.setParamId(dynamicParamInfoBo.getParamId());
            return dynamicParam;
        }).collect(Collectors.toList());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public List<ActivityDynamicParamInfoBO> getDynamicParamInfos() {
        return dynamicParamInfos;
    }

    public void setDynamicParamInfos(List<ActivityDynamicParamInfoBO> dynamicParamInfos) {
        this.dynamicParamInfos = dynamicParamInfos;
    }

    @Override
    public String toString() {
        return "ActivityDynamicParamBO{" +
                "activityId=" + id +
                ", dynamicParamInfos=" + dynamicParamInfos +
                '}';
    }
}


