package com.insta360.store.business.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.integration.google.bo.GoogleAdapterTypeBO;
import com.insta360.store.business.meta.exception.MetaErrorCode;
import com.insta360.store.business.meta.model.AdapterTypeMain;
import com.insta360.store.business.meta.service.AdapterTypeMainService;
import com.insta360.store.business.product.dao.ProductAdapterTypeDao;
import com.insta360.store.business.product.dto.ProductAdapterTypeDTO;
import com.insta360.store.business.product.model.ProductAdapterType;
import com.insta360.store.business.product.service.ProductAdapterTypeService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-09-13
 * @Description:
 */
@Service
public class ProductAdapterTypeServiceImpl extends BaseServiceImpl<ProductAdapterTypeDao, ProductAdapterType> implements ProductAdapterTypeService {

    @Autowired
    AdapterTypeMainService adapterTypeMainService;

    @Override
    public List<ProductAdapterType> listByProduct(Integer productId) {
        QueryWrapper<ProductAdapterType> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ProductAdapterType> listEnabledAdapterType(Integer productId) {
        List<ProductAdapterType> productAdapterTypes = listByProduct(productId);
        return productAdapterTypes
                .stream()
                // 过滤掉未启用的适配类型
                .filter(productAdapterType -> !adapterTypeMainService.getById(productAdapterType.getAdapterTypeId()).getDisabled())
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductAdapterType> listByAdapterType(Integer adapterTypeId) {
        QueryWrapper<ProductAdapterType> qw = new QueryWrapper<>();
        qw.eq("adapter_type_id", adapterTypeId);
        return this.list(qw);
    }

    @Override
    public List<ProductAdapterType> listByAdapterTypeIds(List<Integer> adapterTypeIds) {
        if (CollectionUtils.isEmpty(adapterTypeIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<ProductAdapterType> qw = new QueryWrapper<>();
        qw.in("adapter_type_id", adapterTypeIds);
        return this.list(qw);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void createProductAdapterType(ProductAdapterTypeDTO productAdapterTypeParam) {
        List<Integer> adapterTypeIds = productAdapterTypeParam.getAdapterTypeIds();
        if (CollectionUtils.isEmpty(adapterTypeIds)) {
            // 删除产品id下全部的适配机型
            this.deleteByProductId(productAdapterTypeParam.getProductId());
            return;
        }
        Collection<AdapterTypeMain> adapterTypeMains = adapterTypeMainService.listAdapterTypeByIds(adapterTypeIds);
        // 适配类型需要真实有效
        if (adapterTypeMains.size() != adapterTypeIds.size()) {
            throw new InstaException(MetaErrorCode.AdapterTypeNotFoundException);
        }

        // 删除产品id下全部的适配机型
        this.deleteByProductId(productAdapterTypeParam.getProductId());

        // 封装集合数量批量新增
        int index = 0;
        List<ProductAdapterType> productAdapterTypes = new ArrayList<>(adapterTypeIds.size());
        for (Integer adapterTypeId : adapterTypeIds) {
            ProductAdapterType productAdapterType = new ProductAdapterType();
            productAdapterType.setProductId(productAdapterTypeParam.getProductId());
            productAdapterType.setAdapterTypeId(adapterTypeId);
            productAdapterType.setOrderIndex(++index);
            productAdapterType.setCreateTime(LocalDateTime.now());
            productAdapterType.setUpdateTime(LocalDateTime.now());
            productAdapterTypes.add(productAdapterType);
        }
        baseMapper.saveAdapterType(productAdapterTypes);
    }

    @Override
    public List<ProductAdapterType> listByProductIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<ProductAdapterType> qw = new QueryWrapper<>();
        qw.in("product_id", productIds);
        return baseMapper.selectList(qw);

    }

    @Override
    public Map<Integer, List<Integer>> listAccessoryCompatibility() {
        List<GoogleAdapterTypeBO> googleAdapterTypeBOList = baseMapper.listAccessoryCompatibility();
        // 配件套餐没有也会查出一个null
        return googleAdapterTypeBOList
                .stream()
                .filter(googleAdapterTypeBO -> googleAdapterTypeBO.getAccessoryCompatibilityId() != null)
                .collect(Collectors.groupingBy(
                        GoogleAdapterTypeBO::getCommodityId,
                        Collectors.mapping(GoogleAdapterTypeBO::getAccessoryCompatibilityId, Collectors.toList())));

    }

    @Override
    public void deleteByProductId(Integer productId) {
        QueryWrapper<ProductAdapterType> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        baseMapper.delete(qw);
    }

    @Override
    public List<ProductAdapterType> listByProductOrderByIdDesc(Integer productId) {
        QueryWrapper<ProductAdapterType> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        qw.orderByDesc("id");
        return baseMapper.selectList(qw);
    }

    @Override
    public void updateOrderIndexByIds(ArrayList<ProductAdapterType> adapterTypes) {
        if (CollectionUtils.isEmpty(adapterTypes)) {
            return;
        }
        baseMapper.updateOrderIndexByIds(adapterTypes);
    }
}
