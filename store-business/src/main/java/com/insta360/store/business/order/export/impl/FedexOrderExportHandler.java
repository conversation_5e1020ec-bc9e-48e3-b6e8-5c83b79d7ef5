package com.insta360.store.business.order.export.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.store.business.admin.order.export.FedexOrderExportData;
import com.insta360.store.business.admin.order.service.OrderExportService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.order.dto.OrderExportDTO;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.user.model.StoreAccount;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24
 */
@Component
public class FedexOrderExportHandler extends BaseOrderExportHandler {

    @Autowired
    OrderExportService orderExportService;

    @Override
    public QueryWrapper<Order> buildExportWrapper(OrderExportDTO orderExportParam) {
        Long fromTimeLong = orderExportParam.getFromTime();
        Long endTimeLong = orderExportParam.getEndTime();
        List<Integer> states = orderExportParam.getStates();

        LocalDateTime fromTime = fromTimeLong != null ? TimeUtil.parseLocalDateTime(fromTimeLong) : null;
        LocalDateTime endTime = endTimeLong != null ? TimeUtil.parseLocalDateTime(endTimeLong) : null;

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.gt(fromTime != null, "create_time", fromTime);
        qw.lt(endTime != null, "create_time", endTime);
        qw.in(CollectionUtils.isNotEmpty(states), "state", states);
        qw.eq("is_repair", false);
        qw.orderByDesc("id");
        return qw;
    }

    @Override
    public Integer getOrderCount(OrderExportDTO orderExportParam) {
        return orderService.countExportOrders(buildExportWrapper(orderExportParam));
    }

    @Override
    public <T> List<T> pageOrderData(OrderExportDTO orderExportParam, Page<Order> page) {
        List<Order> orders = orderService.pageExportOrders(page, buildExportWrapper(orderExportParam)).getRecords();
        List<FedexOrderExportData> fedexOrderExportData = orderExportService.exportFedexOrderData(orders);
        // noinspection unchecked
        return (List<T>) fedexOrderExportData;
    }

    @Override
    protected FeiShuAtUser[] getFeishuAtUser() {
        return new FeiShuAtUser[]{FeiShuAtUser.TW};
    }
}
