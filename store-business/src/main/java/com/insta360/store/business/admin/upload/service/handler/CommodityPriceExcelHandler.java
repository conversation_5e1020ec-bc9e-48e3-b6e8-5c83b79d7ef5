package com.insta360.store.business.admin.upload.service.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.google.common.collect.Lists;
import com.insta360.store.business.admin.upload.bo.UploadExcelDataBO;
import com.insta360.store.business.admin.upload.common.UploadConstant;
import com.insta360.store.business.admin.upload.enums.UploadBusinessType;
import com.insta360.store.business.admin.upload.service.data.CommodityPriceExcelData;
import com.insta360.store.business.admin.upload.service.data.ErrorCommonExcelData;
import com.insta360.store.business.admin.upload.service.listener.CommodityPriceListener;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityHelper;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 套餐价格Excel处理类
 * @Date 2023/10/26
 */
public abstract class CommodityPriceExcelHandler extends BaseUploadExcelHandler<CommodityPriceExcelData, CommodityPriceListener> {

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Autowired
    CommodityHelper commodityHelper;


    @Override
    protected CommodityPriceListener excelParse(String ossUrl, String taskId) {
        // 获取文件输入流
        InputStream in = super.getFileInputStream(ossUrl);
        // 读取Excel
        CommodityPriceListener listener = new CommodityPriceListener(this.getBusinessType(),taskId);
        EasyExcel.read(in, CommodityPriceExcelData.class, listener)
                .autoTrim(true)
                .ignoreEmptyRow(true)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet()
                .doRead();

        return listener;
    }

    /**
     * Excel数据格式检查
     *
     * @param commodityPriceExcelData
     * @param businessType
     * @return
     */
    public static ErrorCommonExcelData dataFormatCheck(CommodityPriceExcelData commodityPriceExcelData, UploadBusinessType businessType, Integer rowNumber) {
        StringBuffer sb = new StringBuffer();
        // 套餐ID校验
        commodityIdRegularCheck(commodityPriceExcelData, sb);

        // 国家二字码校验
        areaRegularCheck(commodityPriceExcelData, sb);

        // 币种校验
        currencyRegularCheck(commodityPriceExcelData, sb);

        // 现价校验
        currentPriceRegularCheck(commodityPriceExcelData, sb);

        // 税费校验
        taxRegularCheck(commodityPriceExcelData, sb);

        // 活动名称校验
        campaignRegularCheck(commodityPriceExcelData, sb);

        // 原价校验
        originalPriceRegularCheck(commodityPriceExcelData, businessType, sb);

        ErrorCommonExcelData errorCommonExcelData = null;
        if (Objects.nonNull(sb) && sb.length() > 0) {
            errorCommonExcelData = new ErrorCommonExcelData(rowNumber, sb.toString());
        }

        return errorCommonExcelData;
    }

    /**
     * 原价检查
     *
     * @param commodityPriceExcelData
     * @param businessType
     * @param sb
     */
    private static void originalPriceRegularCheck(CommodityPriceExcelData commodityPriceExcelData, UploadBusinessType businessType, StringBuffer sb) {
        switch (businessType) {
            case original_price:
            case new_product_original_price:
                originAmountRegularCheck(commodityPriceExcelData, sb);
                break;
            default:
                break;
        }
    }

    /**
     * 获取地区货币配置Map
     *
     * @return
     */
    protected Map<String, String> getCountryCurrencyMap() {
        return countryConfigService.listConfig()
                .stream()
                .collect(Collectors.toMap(CountryConfig::getCountryCode, CountryConfig::getCurrency, (c1, c2) -> c1));
    }

    /**
     * 获取合法的套餐ID列表
     *
     * @param excelDataList
     * @return
     */
    protected List<Integer> getLegalCommodityIds(List<UploadExcelDataBO<CommodityPriceExcelData>> excelDataList) {
        // Excel文件中所有的套餐ID列表
        List<Integer> commodityIds = excelDataList.stream()
                .filter(u -> Objects.nonNull(u.getData()
                        .getCommodityId()))
                .map(u -> u.getData()
                        .getCommodityId())
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(commodityIds)) {
            return Lists.newArrayList();
        }

        return Optional.ofNullable(commodityService.listCommodities(commodityIds))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .map(Commodity::getId)
                        .collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
    }

    /**
     * 获取套餐-产品类目配置Map
     *
     * @param excelDataList
     * @return
     */
    protected Map<Integer, ProductCategoryMainType> getLegalCommodityMap(List<UploadExcelDataBO<CommodityPriceExcelData>> excelDataList) {
        // Excel文件中所有的套餐ID列表
        List<Integer> commodityIds = excelDataList.stream()
                .filter(u -> Objects.nonNull(u.getData()
                        .getCommodityId()))
                .map(u -> u.getData()
                        .getCommodityId())
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(commodityIds)) {
            return new HashMap<>();
        }

        List<Commodity> commodityList = commodityService.listCommodities(commodityIds);
        List<Integer> productIdList = commodityList.stream()
                .map(Commodity::getProduct)
                .collect(Collectors.toList());

        // 产品ID-产品类目配置Map
        Map<Integer, ProductCategoryMainType> productCategoryMainTypeMap = Optional.ofNullable(productService.getProducts(productIdList))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .collect(Collectors.toMap(Product::getId, p -> productCategoryHelper.getCategoryMainByKey(p.getCategoryKey()), (v1, v2) -> v2)))
                .orElse(new HashMap<>());

        return commodityList.stream()
                .collect(Collectors.toMap(Commodity::getId, c -> productCategoryMainTypeMap.get(c.getProduct()), (v1, v2) -> v2));
    }


    /**
     * 活动名称正则检查
     *
     * @param data
     * @param sb
     */
    private static void campaignRegularCheck(CommodityPriceExcelData data, StringBuffer sb) {
        String campaign = data.getCampaign();
        if (StringUtils.isBlank(campaign)) {
            sb.append("活动名称必填！");
        }
    }

    /**
     * 税费名称正则检查
     *
     * @param data
     * @param sb
     */
    private static void taxRegularCheck(CommodityPriceExcelData data, StringBuffer sb) {
        String tax = data.getTax();
        if (StringUtils.isNotBlank(tax) && !UploadConstant.DOUBLE_POINT_PATTERN.matcher(tax).matches()) {
            sb.append("税费必须为非负小数或非负正整数！");
        }
    }

    /**
     * 现价正则检查
     *
     * @param data
     * @param sb
     */
    private static void currentPriceRegularCheck(CommodityPriceExcelData data, StringBuffer sb) {
        String amount = data.getAmount();
        if (StringUtils.isBlank(amount)) {
            sb.append("现价必填！");
        } else {
            if (!UploadConstant.DOUBLE_POINT_PATTERN.matcher(amount).matches()) {
                sb.append("现价必须为非负小数或非负正整数！");
            }
        }
    }

    /**
     * 币种三字码正则检查
     *
     * @param data
     * @param sb
     */
    private static void currencyRegularCheck(CommodityPriceExcelData data, StringBuffer sb) {
        String currency = data.getCurrency();
        if (StringUtils.isBlank(currency)) {
            sb.append("币种三字码必填！");
        } else {
            if (!UploadConstant.CURRENCY_PATTERN.matcher(currency).matches()) {
                sb.append("币种三字码格式错误,标准格式必须由三位大写字母构成！");
            }
        }
    }

    /**
     * 国家地区二字码正则检查
     *
     * @param data
     * @param sb
     */
    private static void areaRegularCheck(CommodityPriceExcelData data, StringBuffer sb) {
        String area = data.getArea();
        if (StringUtils.isBlank(area)) {
            sb.append("国家/地区二字码必填！");
        } else {
            if (!UploadConstant.AREA_PATTERN.matcher(area).matches()) {
                sb.append("国家/地区二字码格式错误,标准格式必须由两位大写字母构成！");
            }
        }
    }

    /**
     * 套餐ID正则检查
     *
     * @param data
     * @param sb
     */
    private static void commodityIdRegularCheck(CommodityPriceExcelData data, StringBuffer sb) {
        Integer commodityId = data.getCommodityId();
        if (Objects.isNull(commodityId)) {
            sb.append("套餐ID必填！");
        }
    }

    /**
     * 原价正则检查
     *
     * @param data
     * @param sb
     */
    private static void originAmountRegularCheck(CommodityPriceExcelData data, StringBuffer sb) {
        String originAmount = data.getOriginAmount();
        if (StringUtils.isBlank(originAmount)) {
            sb.append("原价必填！");
        } else {
            if (!UploadConstant.DOUBLE_POINT_PATTERN.matcher(originAmount).matches()) {
                sb.append("原价必须为非负小数或非负正整数！");
            }
        }
    }


}
