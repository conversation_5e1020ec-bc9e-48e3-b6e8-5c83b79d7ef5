package com.insta360.store.business.cloud.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.cloud.enums.BenefitType;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-06-03
 * @Description: 云存储商城权益补偿相关明细表
 */
public class CloudStorageCompensateDetail extends BaseModel<CloudStorageCompensateDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户账户id
     */
    private Integer userId;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 代金券code
     */
    private String giftCode;

    /**
     * 补偿类型
     * @see com.insta360.store.business.cloud.enums.BenefitType
     */
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    public CloudStorageCompensateDetail() {
    }

    public CloudStorageCompensateDetail(Integer userId, String serialNumber, String giftCode, BenefitType benefitType) {
        this.userId = userId;
        this.serialNumber = serialNumber;
        this.giftCode = giftCode;
        this.type = benefitType.getType();
    }

    public CloudStorageCompensateDetail(Integer userId, String giftCode, BenefitType benefitType) {
        this.userId = userId;
        this.giftCode = giftCode;
        this.type = benefitType.getType();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getGiftCode() {
        return giftCode;
    }

    public void setGiftCode(String giftCode) {
        this.giftCode = giftCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "CloudStorageCompensateDetail{" +
                "id=" + id +
                ", userId=" + userId +
                ", serialNumber='" + serialNumber + '\'' +
                ", giftCode='" + giftCode + '\'' +
                ", type=" + type +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}