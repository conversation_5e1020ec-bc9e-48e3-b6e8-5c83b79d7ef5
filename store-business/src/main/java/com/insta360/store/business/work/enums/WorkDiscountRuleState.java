package com.insta360.store.business.work.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/12/1
 */
public enum WorkDiscountRuleState {
    DISABLE(0,"禁用"),
    ENABLE(1,"启用");


    private final int code;

    private final String value;

    WorkDiscountRuleState(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static WorkDiscountRuleState parse(Integer code) {
        if(code == null) {
            return null;
        }
        for (WorkDiscountRuleState state : WorkDiscountRuleState.values()) {
            if(state.getCode() == code) {
                return state;
            }
        }
        return null;
    }
}
