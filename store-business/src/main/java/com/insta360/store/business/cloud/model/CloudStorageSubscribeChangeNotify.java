package com.insta360.store.business.cloud.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/25
 */
public class CloudStorageSubscribeChangeNotify extends BaseModel<CloudStorageSubscribeChangeNotify> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Insta360账户ID
     */
    private Integer instaAccountId;

    /**
     * 商城云服务订阅ID
     */
    private Integer subscribeId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 用户账户token
     */
    private String userAccountToken;

    /**
     * 通知类型
     */
    private String notificationType;

    /**
     * 支付流水号
     */
    private String transactionId;

    /**
     * 订阅过期时间
     */
    private Long expireTime;

    /**
     * 原因
     */
    private String reason;

    /**
     * 原始交易ID（商城生成）
     */
    private String originTransactionId;

    /**
     * 确认标记
     */
    private Boolean ackMark;

    /**
     * 云服务实付金额
     */
    private Double price;

    /**
     * 云服务实退金额
     */
    private Double refundPrice;

    /**
     * 支付渠道
     * @see com.insta360.store.business.meta.enums.PaymentChannel
     */
    private String paymentChannel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSubscribeId() {
        return subscribeId;
    }

    public void setSubscribeId(Integer subscribeId) {
        this.subscribeId = subscribeId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getUserAccountToken() {
        return userAccountToken;
    }

    public void setUserAccountToken(String userAccountToken) {
        this.userAccountToken = userAccountToken;
    }

    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getOriginTransactionId() {
        return originTransactionId;
    }

    public void setOriginTransactionId(String originTransactionId) {
        this.originTransactionId = originTransactionId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Boolean getAckMark() {
        return ackMark;
    }

    public void setAckMark(Boolean ackMark) {
        this.ackMark = ackMark;
    }

    public Integer getInstaAccountId() {
        return instaAccountId;
    }

    public void setInstaAccountId(Integer instaAccountId) {
        this.instaAccountId = instaAccountId;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getRefundPrice() {
        return refundPrice;
    }

    public void setRefundPrice(Double refundPrice) {
        this.refundPrice = refundPrice;
    }

    public String getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(String paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    @Override
    public String toString() {
        return "CloudStorageSubscribeChangeNotify{" +
                "id=" + id +
                ", instaAccountId=" + instaAccountId +
                ", subscribeId=" + subscribeId +
                ", skuId='" + skuId + '\'' +
                ", userAccountToken='" + userAccountToken + '\'' +
                ", notificationType='" + notificationType + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", expireTime=" + expireTime +
                ", reason='" + reason + '\'' +
                ", originTransactionId='" + originTransactionId + '\'' +
                ", ackMark=" + ackMark +
                ", price=" + price +
                ", refundPrice=" + refundPrice +
                ", paymentChannel='" + paymentChannel + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
