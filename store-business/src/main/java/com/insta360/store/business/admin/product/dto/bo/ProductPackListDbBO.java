package com.insta360.store.business.admin.product.dto.bo;

import com.insta360.store.business.product.model.ProductPackListText;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/9/20
 */
public class ProductPackListDbBO implements Serializable {

    /**
     * 新增
     */
    private List<ProductPackListText> addProductPackListTextList;

    /**
     * 更新
     */
    private List<ProductPackListText> updateProductPackListTextList;

    public ProductPackListDbBO() {
    }

    public ProductPackListDbBO(List<ProductPackListText> addProductPackListTextList, List<ProductPackListText> updateProductPackListTextList) {
        this.addProductPackListTextList = addProductPackListTextList;
        this.updateProductPackListTextList = updateProductPackListTextList;
    }

    public List<ProductPackListText> getAddProductPackListTextList() {
        return addProductPackListTextList;
    }

    public void setAddProductPackListTextList(List<ProductPackListText> addProductPackListTextList) {
        this.addProductPackListTextList = addProductPackListTextList;
    }

    public List<ProductPackListText> getUpdateProductPackListTextList() {
        return updateProductPackListTextList;
    }

    public void setUpdateProductPackListTextList(List<ProductPackListText> updateProductPackListTextList) {
        this.updateProductPackListTextList = updateProductPackListTextList;
    }
}
