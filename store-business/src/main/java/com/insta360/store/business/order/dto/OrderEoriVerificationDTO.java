package com.insta360.store.business.order.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 存放对订单的eori校验的结果
 * @Date 2024/8/27 上午10:12
 */
public class OrderEoriVerificationDTO implements Serializable {

    /**
     * 是否匹配到eori关键词
     */
    private Boolean pickEoriKeyWord;

    /**
     * 命中了哪个关键词
     */
    private String keyWord;

    /**
     * 哪一项
     */
    private String item;

    /**
     * 该项具体值
     */
    private String value;

    public OrderEoriVerificationDTO() {
    }

    public OrderEoriVerificationDTO(Boolean pickEoriKeyWord) {
        this(pickEoriKeyWord, null, null, null);
    }


    public OrderEoriVerificationDTO(Boolean pickEoriKeyWord, String keyWord, String item, String value) {
        this.pickEoriKeyWord = pickEoriKeyWord;
        this.keyWord = keyWord;
        this.item = item;
        this.value = value;
    }

    public Boolean getPickEoriKeyWord() {
        return pickEoriKeyWord;
    }

    public void setPickEoriKeyWord(Boolean pickEoriKeyWord) {
        this.pickEoriKeyWord = pickEoriKeyWord;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
