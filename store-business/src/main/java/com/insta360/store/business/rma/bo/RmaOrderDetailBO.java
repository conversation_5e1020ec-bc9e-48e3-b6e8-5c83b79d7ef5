package com.insta360.store.business.rma.bo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 售后单详情BO
 * @Date 2023/6/27
 */
public class RmaOrderDetailBO implements Serializable {

    /**
     * 售后明细ID
     */
    private Integer rmaOrderDetailId;

    /**
     * 售后单ID
     */
    private Integer rmaOrderId;

    /**
     * 售后商品ID
     */
    private Integer orderItemId;

    /**
     * 退税费
     */
    private BigDecimal returnTaxAmount;

    /**
     * 退运费
     */
    private BigDecimal returnFreight;

    /**
     * 优惠扣减金额
     */
    private BigDecimal discountAmount;

    /**
     * 商品应退金额（未扣减优惠金额）
     */
    private BigDecimal itemRefundableAmount;

    /**
     * 商品实退金额 （已减去优惠金额）
     */
    private BigDecimal itemActualRefundAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 售后数量
     */
    private Integer returnNum;

    public Integer getRmaOrderDetailId() {
        return rmaOrderDetailId;
    }

    public void setRmaOrderDetailId(Integer rmaOrderDetailId) {
        this.rmaOrderDetailId = rmaOrderDetailId;
    }

    public Integer getRmaOrderId() {
        return rmaOrderId;
    }

    public void setRmaOrderId(Integer rmaOrderId) {
        this.rmaOrderId = rmaOrderId;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public BigDecimal getReturnTaxAmount() {
        return returnTaxAmount;
    }

    public void setReturnTaxAmount(BigDecimal returnTaxAmount) {
        this.returnTaxAmount = returnTaxAmount;
    }

    public BigDecimal getReturnFreight() {
        return returnFreight;
    }

    public void setReturnFreight(BigDecimal returnFreight) {
        this.returnFreight = returnFreight;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getItemRefundableAmount() {
        return itemRefundableAmount;
    }

    public void setItemRefundableAmount(BigDecimal itemRefundableAmount) {
        this.itemRefundableAmount = itemRefundableAmount;
    }

    public BigDecimal getItemActualRefundAmount() {
        return itemActualRefundAmount;
    }

    public void setItemActualRefundAmount(BigDecimal itemActualRefundAmount) {
        this.itemActualRefundAmount = itemActualRefundAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Integer getReturnNum() {
        return returnNum;
    }

    public void setReturnNum(Integer returnNum) {
        this.returnNum = returnNum;
    }
}
