package com.insta360.store.business.payment.lib.paypal.enums.param;

/**
 * @Author: wbt
 * @Date: 2022/08/17
 * @Description:
 */
public enum PayPalUserAction {

    /**
     * 将客户重定向到PayPal支付页面后，会出现一个Continue按钮。当启动结帐流时不知道最终金额，并且希望在不处理付款的情况下将客户重定向到商家页面时，请使用此选项。
     */
    CONTINUE("continue", "跳过支付"),

    /**
     * 当你将客户重定向到PayPal支付页面后，一个“立即支付”按钮就会出现。当开始结帐时知道最终金额，并且希望在客户单击“立即支付”时立即处理付款时，请使用此选项。
     */
    PAY_NOW("pay_now", "立即支付");

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述
     */
    private final String detail;

    PayPalUserAction(String name, String detail) {
        this.name = name;
        this.detail = detail;
    }

    public String getName() {
        return name;
    }

    public String getDetail() {
        return detail;
    }
}
