package com.insta360.store.business.discount.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/4/8
 */
public enum DiscountType {

    un_know(0,"未知"),

    // 指定商品减价优惠
    specific_commodity_discount(1,"指定商品减价优惠"),

    // 满减优惠
    price_achieve_discount(2,"满减优惠"),

    // 指定商品免单（精确匹配什么套餐多少个）
    free_commodity_discount(3,"免单"),

    // 可叠加优惠（套餐维度）
    superimposed_discount(4,"可叠加优惠"),

    // 取最低优惠（套餐维度）
    min_discount(5,"取最低优惠"),

    // 订单维度不可叠加
    order_min_discount(6,"不可叠加优惠");

    public final int code;

    public final String value;

    DiscountType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static DiscountType matchCode(Integer code) {
        if(Objects.isNull(code)) {
            return un_know;
        }
        for (DiscountType discountType : values()) {
            if(Integer.valueOf(discountType.code).equals(code)) {
                return discountType;
            }
        }
        return un_know;
    }
}
