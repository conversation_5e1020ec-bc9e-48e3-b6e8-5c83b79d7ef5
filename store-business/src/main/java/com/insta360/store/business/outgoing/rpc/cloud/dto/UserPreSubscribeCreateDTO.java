package com.insta360.store.business.outgoing.rpc.cloud.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/21
 */
public class UserPreSubscribeCreateDTO implements Serializable {

    /**
     * skuId
     */
    private String skuId;

    /**
     * 下单地区
     */
    private String region;

    /**
     * 协议版本号
     */
    private Integer agreementVersion;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 价格
     */
    private Double amount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 优惠码
     */
    private String offerId;

    /**
     * 订单号
     */
    private String orderNumber;

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Integer getAgreementVersion() {
        return agreementVersion;
    }

    public void setAgreementVersion(Integer agreementVersion) {
        this.agreementVersion = agreementVersion;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getOfferId() {
        return offerId;
    }

    public void setOfferId(String offerId) {
        this.offerId = offerId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    @Override
    public String toString() {
        return "UserPreSubscribeCreateDTO{" +
                "skuId='" + skuId + '\'' +
                ", region='" + region + '\'' +
                ", agreementVersion=" + agreementVersion +
                ", userId=" + userId +
                ", platform='" + platform + '\'' +
                ", amount=" + amount +
                ", currency='" + currency + '\'' +
                ", offerId='" + offerId + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                '}';
    }
}
