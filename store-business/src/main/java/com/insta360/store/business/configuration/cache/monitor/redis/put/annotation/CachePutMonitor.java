package com.insta360.store.business.configuration.cache.monitor.redis.put.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: wbt
 * @Date: 2023/08/21
 * @Description:
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface CachePutMonitor {

    /**
     * 缓存模块
     *
     * @return
     */
    String cacheableType();

}
