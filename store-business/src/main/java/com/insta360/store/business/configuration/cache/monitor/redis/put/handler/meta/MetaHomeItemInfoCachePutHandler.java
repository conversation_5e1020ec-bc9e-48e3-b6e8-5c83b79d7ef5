package com.insta360.store.business.configuration.cache.monitor.redis.put.handler.meta;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.cache.contancts.CacheConstant;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CacheCountiesBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.BaseCachePutHandler;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CacheErrorCode;
import com.insta360.store.business.outgoing.rpc.store.job.HomeItemCachePutService;
import com.insta360.store.business.outgoing.rpc.store.job.HomepageCachePutService;
import feign.RetryableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/11/16
 * @Description:
 */
@Component
public class MetaHomeItemInfoCachePutHandler extends BaseCachePutHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(MetaHomeItemInfoCachePutHandler.class);

    @Autowired
    HomeItemCachePutService homeItemCachePutService;

    @Autowired
    HomepageCachePutService homepageCachePutService;

    @Override
    protected StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws InterruptedException {
        InstaLanguage language = cachePutKeyParameter.getLanguage();
        if (Objects.isNull(language)) {
            LOGGER.error(String.format("触发 {%s} 缓存更新流程。语言为空，不予处理。", this.getCachePutType()));
            throw new InstaException(CacheErrorCode.CachePutParamMissException);
        }

        // 参数封装
        List<CacheCountiesBO> filterCounties = CacheConstant.COUNTIES
                .stream()
                .filter(cacheCounties -> cacheCounties.getLanguage().equals(language))
                .collect(Collectors.toList());
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("homeItemIds", cachePutKeyParameter.getHomeItemIds());
        paramMap.put("filterCounties", filterCounties);

        // 任务异步化
        LOGGER.info(String.format("触发 {%s} 缓存更新流程。是否开启异步更新:{%s}", this.getCachePutType(), this.isAsyncTaskable()));
        CountDownLatch countDownLatch = this.getCountDownLatch();
        cachePutThreadPool.execute(() -> this.task1(filterCounties, countDownLatch));
        cachePutThreadPool.execute(() -> this.task2(paramMap, countDownLatch));
        countDownLatch.await();

        // 构造前端缓存更新参数
        return isWebSocketNotify() ? new StoreCacheDataChangeEventBO() : null;
    }

    @Override
    protected String getCachePutType() {
        return CachePutType.HOME_ITEM_INFO;
    }

    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.TRUE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return Arrays.asList(CacheableType.HOME_PAGE_KEY, CacheableType.HOME_ITEM_KEY);
    }

    @Override
    public void task1(Object param, CountDownLatch countDownLatch) {
        try {
            // 更新首页
            List<CacheCountiesBO> filterCounties = (List<CacheCountiesBO>) param;
            filterCounties.forEach(cacheCounties -> homepageCachePutService.listHomepage(cacheCounties.getCountry(), cacheCounties.getLanguage()));
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务1完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task1}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task2(Object param, CountDownLatch countDownLatch) {
        try {
            Map<String, Object> paramMap = (Map<String, Object>) param;
            List<Integer> homeItemIds = (List<Integer>) paramMap.get("homeItemIds");
            List<CacheCountiesBO> filterCounties = (List<CacheCountiesBO>) paramMap.get("filterCounties");

            // 更新类目页
            filterCounties.forEach(cacheCounties -> homeItemIds.forEach(homeItemId -> homeItemCachePutService.listHomeItemInfoByHomeItemType(homeItemId,
                    cacheCounties.getCountry(), cacheCounties.getLanguage())));
            LOGGER.info(String.format("触发 {%s} 缓存更新流程。任务2完成。", this.getCachePutType()));
        } catch (Exception e) {
            if (e instanceof RetryableException || e.getCause() instanceof SocketTimeoutException) {
                if (Boolean.FALSE.equals(this.getretryable())) {
                    this.setRetryable(Boolean.TRUE);
                }
            }
            LOGGER.error(String.format("触发{%s}缓存更新流程{task2}任务异常。原因:{%s}", this.getCachePutType(), e.getMessage()), e);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    public void task3(Object param, CountDownLatch countDownLatch) {

    }

    @Override
    public Boolean isAsyncTaskable() {
        return Boolean.TRUE;
    }

    @Override
    public Integer getTaskNumber() {
        return super.getTaskNumber() - 1;
    }
}
