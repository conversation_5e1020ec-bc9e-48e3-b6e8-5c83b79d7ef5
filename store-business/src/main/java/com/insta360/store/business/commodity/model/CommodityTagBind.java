package com.insta360.store.business.commodity.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-10-22
 * @Description:
 */
@TableName("product_commodity_tag_bind")
public class CommodityTagBind extends BaseModel<CommodityTagBind> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 关联的tag_id
     */
    private Integer tagId;

    /**
     * 关联的活动id
     */
    private Integer activityId;

    /**
     * 关联的活动赠品id
     */
    private Integer activityGiftId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getTagId() {
        return tagId;
    }

    public void setTagId(Integer tagId) {
        this.tagId = tagId;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public Integer getActivityGiftId() {
        return activityGiftId;
    }

    public void setActivityGiftId(Integer activityGiftId) {
        this.activityGiftId = activityGiftId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CommodityTagBind{" +
                "id=" + id +
                ", productId=" + productId +
                ", commodityId=" + commodityId +
                ", tagId=" + tagId +
                ", activityId=" + activityId +
                ", activityGiftId=" + activityGiftId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }

    /**
     * 是否是赠品tag
     *
     * @return
     */
    @JSONField(serialize = false)
    public Boolean isActivityGiftTag() {
        return activityGiftId != 0;
    }
}