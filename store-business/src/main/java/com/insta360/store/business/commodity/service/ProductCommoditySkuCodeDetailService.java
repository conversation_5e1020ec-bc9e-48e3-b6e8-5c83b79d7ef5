package com.insta360.store.business.commodity.service;

import com.insta360.store.business.commodity.model.ProductCommoditySkuCodeDetail;
import com.insta360.compass.core.common.BaseService;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-05-12
 * @Description:
 */
public interface ProductCommoditySkuCodeDetailService extends BaseService<ProductCommoditySkuCodeDetail> {

    /**
     * 根据父skuId获取子skuCode列表
     *
     * @param parentSkuId
     * @return
     */
    List<ProductCommoditySkuCodeDetail> listSkuCodeDetailByParentSkuId(Long parentSkuId);

    /**
     * 根据父skuId列表获取子skuCode列表
     *
     * @param parentSkuIds
     * @return
     */
    List<ProductCommoditySkuCodeDetail> listSkuCodeDetailByParentSkuIds(List<Long> parentSkuIds);

    /**
     * 根据子skuId列表获取子skuCode列表
     *
     * @param childSkuIds
     * @return
     */
    List<ProductCommoditySkuCodeDetail> listSkuCodeDetailByChildSkuIds(List<Long> childSkuIds);

    /**
     * 根据父skuId删除子skuCode列表
     *
     * @param parentSkuIds
     * @return
     */
    int deleteByParentSkuIds(List<Long> parentSkuIds);

    /**
     * 批量保存子skuCode列表
     *
     * @param productCommoditySkuCodeDetails
     * @return
     */
    int batchSaveSkuCodeDetail(List<ProductCommoditySkuCodeDetail> productCommoditySkuCodeDetails);
}
