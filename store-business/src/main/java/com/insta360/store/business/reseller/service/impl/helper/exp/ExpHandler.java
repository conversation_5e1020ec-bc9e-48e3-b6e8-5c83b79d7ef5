package com.insta360.store.business.reseller.service.impl.helper.exp;

import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerExpRecord;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.model.ResellerOrderItem;
import com.insta360.store.business.reseller.service.ResellerExpService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

/**
 * @Author: hyc
 * @Date: 2019/3/25
 * @Description:
 */
public abstract class ExpHandler {

    @Autowired
    ResellerExpService expRecordService;

    protected Reseller reseller;
    protected ResellerOrder order;

    protected void init(Reseller reseller, ResellerOrder resellerOrder) {
        this.reseller = reseller;
        this.order = resellerOrder;
    }

    public abstract void handle(ResellerOrderItem orderItem, Integer configExp);

    protected void saveExp(Integer productId, Integer expToSave){
        ResellerExpRecord resellerExpRecord = new ResellerExpRecord();
        resellerExpRecord.setResellerAutoId(reseller.getId());
        resellerExpRecord.setPromoCode(reseller.getPromoCode());
        resellerExpRecord.setResellerOrder(order.getId());
        resellerExpRecord.setOrderNumber(order.getOrderNumber());
        resellerExpRecord.setExp(expToSave);
        resellerExpRecord.setProduct(productId);
        resellerExpRecord.setCreateTime(LocalDateTime.now());
        expRecordService.save(resellerExpRecord);
    }
}
