package com.insta360.store.business.admin.commodity.price.handle.discount;

import com.insta360.store.business.admin.commodity.price.CommodityPriceCalculateHelper;
import com.insta360.store.business.admin.commodity.price.bo.CommodityPriceCalculateBO;
import com.insta360.store.business.commodity.model.ProductCommodityPriceCalculateDiscount;
import com.insta360.store.business.discount.utils.MathUtil;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/17
 */
@Component
public class LongTermDiscountHandler extends BaseDiscountHandler {

    public static final Logger LOGGER = LoggerFactory.getLogger(CommodityPriceCalculateHelper.class);

    @Override
    protected void doCalculate(List<ProductCommodityPriceCalculateDiscount> discountList, CommodityPriceCalculateBO commodityPriceDiscountBo,BigDecimal accessoryDiscountRate) {
        ProductCategoryMainType commodityCategoryManType = commodityPriceDiscountBo.getCommodityCategoryManType();

        // 套餐折扣率
        BigDecimal commodityDiscountRate = commodityPriceDiscountBo.getDiscountRate();

        // 主机类型
        boolean cameraType = ProductCategoryMainType.isCameraType(commodityCategoryManType);

        // 非标准套餐
        boolean notStandard = (! commodityPriceDiscountBo.getStandard());

        // 标准数量大于等于1
        boolean standardNumMoreThan = commodityPriceDiscountBo.getStandardCommodityNum() >= 1;

        BigDecimal discountAfterPrice = BigDecimal.ZERO;
        if (cameraType && notStandard && standardNumMoreThan) {
            discountAfterPrice = super.calculateGroupPrice(commodityPriceDiscountBo, commodityDiscountRate, accessoryDiscountRate);
        } else {
            // 原价 *（1-主机折扣OFF）
            Float originAmountFloat = commodityPriceDiscountBo.getCommodityPrice().getOriginAmount();
            BigDecimal originAmount = MathUtil.getBigDecimal(originAmountFloat);
            discountAfterPrice = BigDecimal.ZERO.add(originAmount.multiply(BigDecimal.ONE.subtract(commodityDiscountRate)));
        }
        discountAfterPrice = discountAfterPrice.setScale(2, RoundingMode.DOWN);
        commodityPriceDiscountBo.setDiscountAfterPrice(discountAfterPrice);
        commodityPriceDiscountBo.setCalculateFinishPrice(discountAfterPrice);
    }

}
