package com.insta360.store.business.insurance.service.impl.handler;

import com.insta360.store.business.insurance.bo.InsuranceBO;

/**
 * @description:
 * @author: py
 * @create: 2024-05-16 17:40
 */
public interface BusinessServiceInterface extends InsuranceInterface {

    /**
     * 使用云服务权益绑定增值服务
     *
     * @param insuranceParam
     * @return
     */
    void businessBindService(InsuranceBO insuranceParam);

    /**
     * 使用云服务权益绑定增值服务
     *
     * @param serial
     * @return
     */
    void cloudCancelService(String serial);
}
