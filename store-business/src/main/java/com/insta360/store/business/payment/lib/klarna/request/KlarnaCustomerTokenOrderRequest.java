package com.insta360.store.business.payment.lib.klarna.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.store.business.payment.lib.klarna.config.KlarnaPaymentConfiguration;
import com.insta360.store.business.payment.lib.klarna.module.KlarnaBillingAddress;
import com.insta360.store.business.payment.lib.klarna.module.KlarnaOrderLine;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2024/09/13
 * @Description:
 */
public class KlarnaCustomerTokenOrderRequest extends BaseKlarnaPaymentRequest {

    /**
     * 接口路径
     */
    private static final String METHOD = "/customer-token/v1/tokens/%s/order";

    /**
     * 自动capture order
     */
    @JSONField(name = "auto_capture")
    private Boolean autoCapture;

    /**
     * 订单总价
     */
    @JSONField(name = "order_amount")
    private Integer orderAmount;

    /**
     * 订单税费
     */
    @JSONField(name = "order_tax_amount")
    private Integer orderTaxAmount;

    /**
     * 订单子项信息
     */
    @JSONField(name = "order_lines")
    private List<KlarnaOrderLine> orderLines;

    /**
     * 账单地址
     */
    @JSONField(name = "billing_address")
    private KlarnaBillingAddress billingAddress;

    /**
     * 国家
     */
    @JSONField(name = "purchase_country")
    private String purchaseCountry;

    /**
     * 货币
     */
    @JSONField(name = "purchase_currency")
    private String purchaseCurrency;

    /**
     * 订单号
     */
    @JSONField(name = "merchant_reference1")
    private String merchantReference1;

    public KlarnaCustomerTokenOrderRequest(String payId, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        super(String.format(METHOD, payId), klarnaPaymentConfiguration);
    }

    @Override
    public Boolean checker() {
        return orderAmount == orderLines.stream().mapToInt(KlarnaOrderLine::getTotal_amount).sum();
    }

    public Boolean getAutoCapture() {
        return autoCapture;
    }

    public void setAutoCapture(Boolean autoCapture) {
        this.autoCapture = autoCapture;
    }

    public Integer getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(Integer orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Integer getOrderTaxAmount() {
        return orderTaxAmount;
    }

    public void setOrderTaxAmount(Integer orderTaxAmount) {
        this.orderTaxAmount = orderTaxAmount;
    }

    public List<KlarnaOrderLine> getOrderLines() {
        return orderLines;
    }

    public void setOrderLines(List<KlarnaOrderLine> orderLines) {
        this.orderLines = orderLines;
    }

    public KlarnaBillingAddress getBillingAddress() {
        return billingAddress;
    }

    public void setBillingAddress(KlarnaBillingAddress billingAddress) {
        this.billingAddress = billingAddress;
    }

    public String getPurchaseCountry() {
        return purchaseCountry;
    }

    public void setPurchaseCountry(String purchaseCountry) {
        this.purchaseCountry = purchaseCountry;
    }

    public String getPurchaseCurrency() {
        return purchaseCurrency;
    }

    public void setPurchaseCurrency(String purchaseCurrency) {
        this.purchaseCurrency = purchaseCurrency;
    }

    public String getMerchantReference1() {
        return merchantReference1;
    }

    public void setMerchantReference1(String merchantReference1) {
        this.merchantReference1 = merchantReference1;
    }

    @Override
    public String toString() {
        return "KlarnaCustomerTokenOrderRequest{" +
                "autoCapture=" + autoCapture +
                ", orderAmount=" + orderAmount +
                ", orderTaxAmount=" + orderTaxAmount +
                ", orderLines=" + orderLines +
                ", billingAddress=" + billingAddress +
                ", purchaseCountry='" + purchaseCountry + '\'' +
                ", purchaseCurrency='" + purchaseCurrency + '\'' +
                ", merchantReference1='" + merchantReference1 + '\'' +
                "} " + super.toString();
    }
}
