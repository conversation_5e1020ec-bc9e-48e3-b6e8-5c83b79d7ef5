package com.insta360.store.business.payment.lib.paypal.request.api;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * @Author: wkx
 * @Date: 2024/09/09
 * @Description:
 */
public class CreatePayPalPaymentRefundRequest extends BasePayPalRequest {

    private static final String METHOD = "/v2/payments/captures/%s/refund";

    /**
     * 订单号
     */
    @JSONField(name = "invoice_id")
    String invoiceId;

    public CreatePayPalPaymentRefundRequest(String paymentId) {
        super(String.format(METHOD, paymentId));
    }

    public String getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }

    @Override
    public String toString() {
        return "CreatePayPalPaymentRefundRequest{" +
                "invoiceId='" + invoiceId + '\'' +
                "} " + super.toString();
    }
}
