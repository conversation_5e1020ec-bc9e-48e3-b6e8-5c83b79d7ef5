package com.insta360.store.business.discount.enums;

import java.util.Objects;

public enum DiscountCreateType {
    un_know(0,"未知"),
    specific(1,"券"),
    template(2,"模版");

    public final int type;

    public final String value;

    DiscountCreateType(int type, String value) {
        this.type = type;
        this.value = value;
    }

    public static DiscountCreateType matchCode(Integer type) {
        if(Objects.isNull(type)) {
            return un_know;
        }
        for (DiscountCreateType createType : values()) {
            if(Integer.valueOf(createType.type).equals(type)) {
                return createType;
            }
        }
        return un_know;
    }
}
