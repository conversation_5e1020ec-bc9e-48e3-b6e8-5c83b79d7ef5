package com.insta360.store.business.configuration.grafana.support.processor;

import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;

/**
 * @Author: wbt
 * @Date: 2022/03/09
 * @Description: bean加载处理器（避免重复配置导致切面的目标方法执行多次）
 */
@Component
public class GrafanaDataStatsPostProcessor implements InstantiationAwareBeanPostProcessor {

    @Override
    public Object postProcessBeforeInstantiation(Class<?> beanClass, String beanName) throws BeansException {
        for (Method method : beanClass.getDeclaredMethods()) {
            GrafanaDataStats annotation = method.getDeclaredAnnotation(GrafanaDataStats.class);
            if (annotation == null) {
                continue;
            }

            // 避免配置了两个一样的statisticsType
            if (new HashSet<>(Arrays.asList(annotation.statisticsType())).size() != annotation.statisticsType().length) {
                throw new IllegalArgumentException("classPath:" + beanClass.getName() + ".This statisticsType duplicate configuration！check it please");
            }
        }
        return null;
    }
}
