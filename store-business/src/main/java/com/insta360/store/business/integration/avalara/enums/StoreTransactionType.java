package com.insta360.store.business.integration.avalara.enums;

/**
 * @Author: wbt
 * @Date: 2023/06/19
 * @Description:
 */
public enum StoreTransactionType {

    /**
     * 结账页计税
     */
    CHECKOUT_TRANSACTION("checkout_transaction", "结账页计税", false),

    /**
     * 订单创建计税
     */
    ORDER_CREATE_TRANSACTION("order_create_transaction", "订单创建计税", false),

    /**
     * 订单支付离线税率计税
     */
    ORDER_PAYED_OFFLINE_TRANSACTION("order_payed_offline_transaction", "订单支付离线税率计税", true),

    /**
     * 订单支付计税
     */
    ORDER_PAYED_TRANSACTION("order_payed_transaction", "订单支付计税", true),

    /**
     * 订单售后计税
     */
    ORDER_REFUND_TRANSACTION("order_refund_transaction", "订单售后计税", true),

    /**
     * 下载离线税率
     */
    DOWNLOAD_TAX_RATE("download_tax_rate", "下载离线税率", true),

    /**
     * 地址解析
     */
    RESOLVE_ADDRESS("resolve_address", "地址解析", true),

    /**
     * 订单二次支付计税
     */
    ORDER_PAYED_AGAIN_TRANSACTION("order_payed_again_transaction", "订单二次支付计税", false),

    ;

    /**
     * 名称
     */
    private final String nameEn;

    /**
     * 描述
     */
    private final String detail;

    /**
     * 是否必须走Avalara实现
     */
    private final Boolean isAvalara;

    StoreTransactionType(String nameEn, String detail, Boolean isAvalara) {
        this.nameEn = nameEn;
        this.detail = detail;
        this.isAvalara = isAvalara;
    }

    public String getNameEn() {
        return nameEn;
    }

    public String getDetail() {
        return detail;
    }

    public Boolean getAvalara() {
        return isAvalara;
    }
}
