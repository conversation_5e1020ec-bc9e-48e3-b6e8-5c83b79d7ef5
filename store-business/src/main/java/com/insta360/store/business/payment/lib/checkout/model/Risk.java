package com.insta360.store.business.payment.lib.checkout.model;

/**
 * @Author: wbt
 * @Date: 2021/11/15
 * @Description:
 */
public class Risk {

    /**
     * 是否开启风险评估
     */
    private Boolean enabled;


    /*****************************************   以下字段存在于Response   *****************************************/

    /**
     * 是否进行了风险检查
     */
    private Boolean flagged;

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getFlagged() {
        return flagged;
    }

    public void setFlagged(Boolean flagged) {
        this.flagged = flagged;
    }

    @Override
    public String toString() {
        return "Risk{" +
                "enabled=" + enabled +
                ", flagged=" + flagged +
                '}';
    }
}
