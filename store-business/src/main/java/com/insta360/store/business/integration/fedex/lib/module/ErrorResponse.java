package com.insta360.store.business.integration.fedex.lib.module;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/20
 */
public class ErrorResponse implements Serializable {

    /**
     * 错误码
     * Example: NOT.FOUND.ERROR
     */
    private String code;

    /**
     * 错误提示消息
     */
    private String message;

    /**
     * 对应错误key-value提示
     */
    private List<ErrorParameter> parameterList;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<ErrorParameter> getParameterList() {
        return parameterList;
    }

    public void setParameterList(List<ErrorParameter> parameterList) {
        this.parameterList = parameterList;
    }

    @Override
    public String toString() {
        return "ErrorResponse{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", parameterList=" + parameterList +
                '}';
    }
}
