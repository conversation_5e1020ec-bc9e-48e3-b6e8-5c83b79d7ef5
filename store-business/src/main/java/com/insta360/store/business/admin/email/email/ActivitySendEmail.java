package com.insta360.store.business.admin.email.email;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.admin.email.model.EmailActivitySendRecord;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import com.insta360.store.business.meta.email.BaseStoreEmail;
import com.insta360.store.business.outgoing.mq.email.helper.EmailSendMessageHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26
 */
@Component
@Scope("prototype")
public class ActivitySendEmail extends BaseStoreEmail {

    private static final Logger LOGGER = LoggerFactory.getLogger(ActivitySendEmail.class);

    @Autowired
    EmailSendMessageHelper emailSendMessageHelper;

    /**
     * 语言
     */
    private InstaLanguage language;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 优惠券
     */
    private String giftCode;

    /**
     * 优惠券
     */
    private String productName;

    /**
     * 时间区间
     */
    private String timeRange;

    /**
     * 初始化参数
     *
     * @param templateName
     * @param emailActivitySendRecord
     */
    public void init(String templateName, EmailActivitySendRecord emailActivitySendRecord) {
        this.templateName = templateName;
        this.language = emailActivitySendRecord.language();
        this.giftCode = emailActivitySendRecord.getGiftCode();
        this.productName = emailActivitySendRecord.getProductName();
        this.timeRange = emailActivitySendRecord.getTimeRange();
    }

    /**
     * 邮件发送
     *
     * @param toAddress
     */
    @Async
    @Override
    public void doSend(String toAddress) {
        try {
            EmailTemplateParams templateParams = buildEmailTemplateParams(toAddress);
            // 邮件推送
            emailSendMessageHelper.sendBatchEmailMessage(templateParams);
        } catch (Exception e) {
            LOGGER.error("send email error. message:" + e.getMessage(), e);
        }
    }

    @Override
    protected Integer getSleepTime() {
        return 0;
    }

    @Override
    protected InstaLanguage getLanguage() {
        return language;
    }

    @Override
    public String getTemplateName() {
        return templateName;
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        if (StringUtils.isNotBlank(giftCode)) {
            templateParams.addBodyParam("gift_code", giftCode);
        }
        if (StringUtils.isNotBlank(productName)) {
            templateParams.addBodyParam("product_name", productName);
        }
        if (StringUtils.isNotBlank(timeRange)) {
            templateParams.addBodyParam("time_range", timeRange);
        }
    }

    @Override
    protected Long getDelayTimeConfig() {
        return DEFAULT_DELAY_TIME;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public void setLanguage(InstaLanguage language) {
        this.language = language;
    }

    public EmailSendMessageHelper getEmailSendMessageHelper() {
        return emailSendMessageHelper;
    }

    public void setEmailSendMessageHelper(EmailSendMessageHelper emailSendMessageHelper) {
        this.emailSendMessageHelper = emailSendMessageHelper;
    }

    public String getGiftCode() {
        return giftCode;
    }

    public void setGiftCode(String giftCode) {
        this.giftCode = giftCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getTimeRange() {
        return timeRange;
    }

    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }

    @Override
    public String toString() {
        return "ActivitySendEmail{" +
                "language=" + language +
                ", templateName='" + templateName + '\'' +
                ", giftCode='" + giftCode + '\'' +
                '}';
    }
}