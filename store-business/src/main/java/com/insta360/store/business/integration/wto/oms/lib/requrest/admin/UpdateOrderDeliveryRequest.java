package com.insta360.store.business.integration.wto.oms.lib.requrest.admin;

import com.insta360.store.business.configuration.utils.ProfileUtil;
import com.insta360.store.business.integration.wto.oms.bo.OmsOrderDeliveryBO;
import com.insta360.store.business.integration.wto.oms.configuration.OmsConfiguration;
import com.insta360.store.business.integration.wto.oms.lib.requrest.BaseOmsRequest;

/**
 * 更新订单收货地址请求
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
public class UpdateOrderDeliveryRequest extends BaseOmsRequest {

    private static final String METHOD = "oms.modify.address";

    /**
     * 修改后的地址。
     */
    private ModifiedAddress modifiedAddress;

    /**
     * 交易号，例如：INS098686897897897X
     */
    private String tradeId;

    /**
     * 店铺码
     */
    private String shopCode;

    public UpdateOrderDeliveryRequest(OmsConfiguration omsConfiguration) {
        super(METHOD, omsConfiguration);
    }

    public UpdateOrderDeliveryRequest(String method, OmsConfiguration omsConfiguration) {
        super(method, omsConfiguration);
    }

    public ModifiedAddress getModifiedAddress() {
        return modifiedAddress;
    }

    public void setModifiedAddress(ModifiedAddress modifiedAddress) {
        this.modifiedAddress = modifiedAddress;
    }

    public String getTradeId() {
        return tradeId;
    }

    public void setTradeId(String tradeId) {
        this.tradeId = tradeId;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    @Override
    public String toString() {
        return "UpdateOrderDeliveryRequest{" +
                "modifiedAddress=" + modifiedAddress +
                ", tradeId='" + tradeId + '\'' +
                ", shopCode='" + shopCode + '\'' +
                '}';
    }

    /**
     * 构建参数
     *
     * @param orderNumber
     * @param orderDelivery
     */
    public void buildParams(String orderNumber, OmsOrderDeliveryBO orderDelivery) {

        this.tradeId = orderNumber;
        this.modifiedAddress = new ModifiedAddress();
        this.modifiedAddress.setAddressDetail(ProfileUtil.parseAddressDetails(orderDelivery));
        this.modifiedAddress.setCountryCode(orderDelivery.getCountryCode());
        this.modifiedAddress.setArea(orderDelivery.getDistrict());
        this.modifiedAddress.setCity(orderDelivery.getCity());
        this.modifiedAddress.setName(ProfileUtil.getFullName(orderDelivery.getFirstName(), orderDelivery.getLastName()));
        this.modifiedAddress.setPhone(ProfileUtil.getFullPhone(orderDelivery.getPhoneCode(), orderDelivery.getPhone()));
        this.modifiedAddress.setPostCode(orderDelivery.getZipCode());
        this.modifiedAddress.setProvince(ProfileUtil.parseOmsProvince(orderDelivery.country(), orderDelivery.getProvince()));
    }

    /**
     * 表示修改后的地址。
     */
    public static class ModifiedAddress {

        /**
         * 详细地址，例如："11号"。
         */
        private String addressDetail;

        /**
         * 区域，例如："南山区"。
         */
        private String area;

        /**
         * 国家二字码
         */
        private String countryCode;

        /**
         * 城市，例如："深圳市"。
         */
        private String city;

        /**
         * 收货人姓名，例如："张三"。
         */
        private String name;

        /**
         * 手机号码，例如：16688888888。
         */
        private String phone;

        /**
         * 邮政编码。
         */
        private String postCode;

        /**
         * 省份，例如："广东省"。
         */
        private String province;

        public String getAddressDetail() {
            return addressDetail;
        }

        public void setAddressDetail(String addressDetail) {
            this.addressDetail = addressDetail;
        }

        public String getArea() {
            return area;
        }

        public void setArea(String area) {
            this.area = area;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getPostCode() {
            return postCode;
        }

        public void setPostCode(String postCode) {
            this.postCode = postCode;
        }

        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        @Override
        public String toString() {
            return "ModifiedAddress{" +
                    "addressDetail='" + addressDetail + '\'' +
                    ", area='" + area + '\'' +
                    ", countryCode='" + countryCode + '\'' +
                    ", city='" + city + '\'' +
                    ", name='" + name + '\'' +
                    ", phone='" + phone + '\'' +
                    ", postCode='" + postCode + '\'' +
                    ", province='" + province + '\'' +
                    '}';
        }
    }
}
