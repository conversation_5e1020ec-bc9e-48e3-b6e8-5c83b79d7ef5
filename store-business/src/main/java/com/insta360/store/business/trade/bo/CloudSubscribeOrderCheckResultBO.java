package com.insta360.store.business.trade.bo;

import com.insta360.store.business.cloud.model.CloudStorageSku;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/9
 */
public class CloudSubscribeOrderCheckResultBO implements Serializable {

    /**
     * 是否为云订阅订单
     */
    private Boolean cloudSubscribeOrderMark;

    /**
     * 云订阅商品信息
     */
    private CloudStorageSku cloudStorageSku;

    public CloudSubscribeOrderCheckResultBO() {
    }

    public CloudSubscribeOrderCheckResultBO(Boolean cloudSubscribeOrderMark, CloudStorageSku cloudStorageSku) {
        this.cloudSubscribeOrderMark = cloudSubscribeOrderMark;
        this.cloudStorageSku = cloudStorageSku;
    }

    public Boolean getCloudSubscribeOrderMark() {
        return cloudSubscribeOrderMark;
    }

    public void setCloudSubscribeOrderMark(Boolean cloudSubscribeOrderMark) {
        this.cloudSubscribeOrderMark = cloudSubscribeOrderMark;
    }

    public CloudStorageSku getCloudStorageSku() {
        return cloudStorageSku;
    }

    public void setCloudStorageSku(CloudStorageSku cloudStorageSku) {
        this.cloudStorageSku = cloudStorageSku;
    }

    @Override
    public String toString() {
        return "CloudSubscribeOrderCheckResultBO{" +
                "cloudSubscribeOrderMark=" + cloudSubscribeOrderMark +
                ", cloudStorageSku=" + cloudStorageSku +
                '}';
    }
}
