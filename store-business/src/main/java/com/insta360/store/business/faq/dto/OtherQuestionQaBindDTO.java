package com.insta360.store.business.faq.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqOtherQuestionQaBind;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/4/29
 * @Description:
 */
public class OtherQuestionQaBindDTO implements Serializable {

    private Integer id;

    /**
     * 其他页面绑定id
     */
    @NotNull(message = "页面绑定id不允许为空")
    private Integer otherQuestionBindId;

    /**
     * 内部分类问题id
     */
    @NotNull(message = "内部分类问题id不允许为空")
    private Integer questionId;

    /**
     * 排序
     */
    @NotNull(message = "排序不允许为空")
    private Integer orderIndex;

    public FaqOtherQuestionQaBind getPojoObject() {
        FaqOtherQuestionQaBind questionQaBind = new FaqOtherQuestionQaBind();
        BeanUtil.copyProperties(this, questionQaBind);
        return questionQaBind;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOtherQuestionBindId() {
        return otherQuestionBindId;
    }

    public void setOtherQuestionBindId(Integer otherQuestionBindId) {
        this.otherQuestionBindId = otherQuestionBindId;
    }

    public Integer getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }
}
