package com.insta360.store.business.admin.order.print.spliter;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.order.model.OrderItem;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * @Author: hyc
 * @Date: 2019-05-14
 * @Description:
 */
@Component
public class StoreOrderSpliter extends OrderSpliter {

    public List<OrderItem> run(List<OrderItem> orderItems, InstaCountry country) {

        Integer orderId = null;

        // 将拆分后的item与拆分前的item强关联
        Map<OrderItem, Map<Commodity, Integer>> resultMap = new HashMap<>(orderItems.size());

        for (OrderItem orderItem : orderItems) {
            Map<Commodity, Integer> toCombineCommodityMap = new HashMap<>(orderItems.size());
            if (orderId == null) {
                orderId = orderItem.getOrder();
            }

            Commodity commodity = commodityService.getById(orderItem.getCommodity());
            Map<Commodity, Integer> splitMap = this.getSplitCommodityMap(commodity);

            // 套餐不可拆分，放入待合并结果
            if (splitMap == null || splitMap.isEmpty()) {
                toCombineCommodityMap.merge(commodity, orderItem.getNumber(), Integer::sum);
            } else {
                // 将拆分结果放入待合并结果，若遇到已存在套餐则数量相加
                Set<Commodity> commodities = splitMap.keySet();
                commodities.forEach(c -> {
                    Integer addItemNumber = splitMap.get(c) * orderItem.getNumber();
                    toCombineCommodityMap.merge(c, addItemNumber, Integer::sum);
                });
            }
            resultMap.put(orderItem, toCombineCommodityMap);
        }

        return this.doCombine(resultMap, orderId, country);
    }

    /**
     * 合并相同的套餐
     *
     * @param resultMap
     * @param orderId
     * @param country
     * @return
     */
    private List<OrderItem> doCombine(Map<OrderItem, Map<Commodity, Integer>> resultMap, Integer orderId, InstaCountry country) {
        List<OrderItem> result = new ArrayList<>();
        Set<OrderItem> keySet = resultMap.keySet();
        for (OrderItem orderItem : keySet) {
            Map<Commodity, Integer> splitCommodityMap = resultMap.get(orderItem);
            Set<Commodity> commodities = splitCommodityMap.keySet();

            for (Commodity splitCommodity : commodities) {
                CommodityPrice commodityPrice = commodityPriceService.getPrice(splitCommodity.getId(), country);
                if (commodityPrice == null) {
                    throw new InstaException(-1, String.format("套餐ID:%s,国家:%s价格不存在", splitCommodity.getId(), country.name()));
                }
                Price price = commodityPrice.price();
                if (price == null) {
                    throw new InstaException(-1, String.format("套餐ID:%s,国家:%s价格为空", splitCommodity.getId(), country.name()));
                }
                float amount = price.getAmount();
                int count = splitCommodityMap.get(splitCommodity);

                OrderItem newItem = new OrderItem();
                newItem.setPrice(amount);
                newItem.setCurrency(price.getCurrency().name());
                newItem.setCommodity(splitCommodity.getId());
                newItem.setNumber(count);
                newItem.setIsGift(false);
                newItem.setOrder(orderId);
                newItem.setState(orderItem.getState());
                newItem.setDeliveryState(orderItem.getDeliveryState());

                result.add(newItem);
            }
        }

        Map<Integer, OrderItem> orderItemMap = result.stream().collect(Collectors.toMap(OrderItem::getCommodity, c -> c, (oldValue, newValue) -> newValue));
        Map<Integer, Integer> itemNumMap = result.stream().collect(Collectors.groupingBy(OrderItem::getCommodity, Collectors.summingInt(OrderItem::getNumber)));

        orderItemMap.forEach((key, value) -> {
            Integer itemNum = itemNumMap.get(key);
            if (Objects.nonNull(itemNum)) {
                value.setNumber(itemNum);
            }
        });

        return Lists.newArrayList(orderItemMap.values());
    }
}
