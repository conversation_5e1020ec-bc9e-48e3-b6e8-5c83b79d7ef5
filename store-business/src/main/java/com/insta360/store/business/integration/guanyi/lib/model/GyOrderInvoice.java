package com.insta360.store.business.integration.guanyi.lib.model;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description:
 */
public class GyOrderInvoice {
    String invoice_type_name;//	发票类型
    String invoice_title;//发票抬头
    String invoice_title_type;// 发票抬头类型
    String invoice_content;//	发票内容
    String invoice_amount;//	发票金额
    Integer invoice_type;//	Number 	1-普通发票；2-增值发票
    String invoice_tex_payer_number;//纳税人识别号
    String invoice_bank_name;//开户行
    String invoice_bank_account;//账号
    String invoice_address;//地址
    String invoice_phone;//电话


    public String getInvoice_tex_payer_number() {
        return invoice_tex_payer_number;
    }

    public void setInvoice_tex_payer_number(String invoice_tex_payer_number) {
        this.invoice_tex_payer_number = invoice_tex_payer_number;
    }

    public String getInvoice_bank_name() {
        return invoice_bank_name;
    }

    public void setInvoice_bank_name(String invoice_bank_name) {
        this.invoice_bank_name = invoice_bank_name;
    }

    public String getInvoice_bank_account() {
        return invoice_bank_account;
    }

    public void setInvoice_bank_account(String invoice_bank_account) {
        this.invoice_bank_account = invoice_bank_account;
    }

    public String getInvoice_address() {
        return invoice_address;
    }

    public void setInvoice_address(String invoice_address) {
        this.invoice_address = invoice_address;
    }

    public String getInvoice_phone() {
        return invoice_phone;
    }

    public void setInvoice_phone(String invoice_phone) {
        this.invoice_phone = invoice_phone;
    }

    public String getInvoice_type_name() {
        return invoice_type_name;
    }

    public void setInvoice_type_name(String invoice_type_name) {
        this.invoice_type_name = invoice_type_name;
    }

    public String getInvoice_title() {
        return invoice_title;
    }

    public void setInvoice_title(String invoice_title) {
        this.invoice_title = invoice_title;
    }

    public String getInvoice_content() {
        return invoice_content;
    }

    public void setInvoice_content(String invoice_content) {
        this.invoice_content = invoice_content;
    }

    public String getInvoice_amount() {
        return invoice_amount;
    }

    public void setInvoice_amount(String invoice_amount) {
        this.invoice_amount = invoice_amount;
    }

    public Integer getInvoice_type() {
        return invoice_type;
    }

    public void setInvoice_type(Integer invoice_type) {
        this.invoice_type = invoice_type;
    }

    public String getInvoice_title_type() {
        return invoice_title_type;
    }

    public void setInvoice_title_type(String invoice_title_type) {
        this.invoice_title_type = invoice_title_type;
    }
}
