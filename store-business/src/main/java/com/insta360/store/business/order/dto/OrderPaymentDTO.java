package com.insta360.store.business.order.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2020/11/20
 * @Description:
 */
public class OrderPaymentDTO implements Serializable {

    private Float amount;

    private String currency;

    @JSONField(name = "order_number")
    private String orderNumber;

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }
}
