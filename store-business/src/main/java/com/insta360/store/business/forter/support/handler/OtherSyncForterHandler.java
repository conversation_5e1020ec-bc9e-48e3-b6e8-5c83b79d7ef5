package com.insta360.store.business.forter.support.handler;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.forter.bo.ForterOrderStatusBO;
import com.insta360.store.business.forter.enums.ForterSyncOrderStatusType;
import com.insta360.store.business.forter.request.CreateOrderStatusRequest;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.bo.OrderSyncForterBO;
import com.insta360.store.business.payment.enums.forter.status.ForterOrderStatusType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2022/11/02
 * @Description: 用于处理其它支付渠道的同步case
 */
@Component
public class OtherSyncForterHandler extends BaseForterSyncServiceHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(OtherSyncForterHandler.class);

    /**
     * 防止调用了forter预授权接口，但是没有同步订单状态给forter的情况
     *
     * @param orderSyncForterParam
     * @return
     */
    @Override
    protected Boolean syncPayFailed(OrderSyncForterBO orderSyncForterParam) {
        Order order = orderSyncForterParam.getOrder();
        if (order == null) {
            LOGGER.error("钱海拒付订单同步forter异常。订单信息不存在。");
            return true;
        }

        // 如果不存在forter预授权认证，则不予同步
        if (!isSyncForterAuthorizaiton(order.getOrderNumber())) {
            return true;
        }

        try {
            ForterOrderStatusBO forterOrderStatus = new ForterOrderStatusBO();
            forterOrderStatus.setOrder(order);
            forterOrderStatus.setForterSyncOrderStatusType(ForterSyncOrderStatusType.ORDER_CANCEL);
            forterOrderStatus.setForterOrderStatusType(ForterOrderStatusType.CANCELED_BY_MERCHANT);

            CreateOrderStatusRequest request = orderStatusHandler.initRequest(forterOrderStatus);
            orderStatusHandler.handle(request);
            return true;
        } catch (Exception e) {
            LOGGER.error(String.format("订单支付失败同步至forter异常！order_number:{%s}, error_message:{%s}",
                    order.getOrderNumber(), e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单支付失败同步至forter异常！order_number:{%s}, error_message:{%s}",
                    order.getOrderNumber(), e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 内部异常不再继续重试
            return e instanceof InstaException;
        }
    }

    /**
     * 防止调用了forter预授权接口，但是没有同步订单状态给forter的情况
     *
     * @param orderSyncForterParam
     * @return
     */
    @Override
    protected Boolean syncPayedSuccess(OrderSyncForterBO orderSyncForterParam) {
        Order order = orderSyncForterParam.getOrder();
        if (order == null) {
            LOGGER.error("钱海拒付订单同步forter异常。订单信息不存在。");
            return true;
        }

        // 如果不存在forter预授权认证，则不予同步
        if (!isSyncForterAuthorizaiton(order.getOrderNumber())) {
            return true;
        }

        try {
            ForterOrderStatusBO forterOrderStatus = new ForterOrderStatusBO();
            forterOrderStatus.setOrder(order);
            forterOrderStatus.setForterSyncOrderStatusType(ForterSyncOrderStatusType.ORDER_CANCEL);
            forterOrderStatus.setForterOrderStatusType(ForterOrderStatusType.CANCELED_BY_MERCHANT);

            CreateOrderStatusRequest request = orderStatusHandler.initRequest(forterOrderStatus);
            orderStatusHandler.handle(request);
            return true;
        } catch (Exception e) {
            LOGGER.error(String.format("订单支付成功同步至forter异常！order_number:{%s}, error_message:{%s}",
                    order.getOrderNumber(), e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单支付成功同步至forter异常！order_number:{%s}, error_message:{%s}",
                    order.getOrderNumber(), e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 内部异常不再继续重试
            return e instanceof InstaException;
        }
    }

    @Override
    protected Boolean syncChargeBackState(OrderSyncForterBO orderSyncForterParam) {
        return true;
    }
}
