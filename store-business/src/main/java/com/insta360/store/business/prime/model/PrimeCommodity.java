package com.insta360.store.business.prime.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-06-04
 * @Description: Prime商品表
 */
public class PrimeCommodity extends BaseModel<PrimeCommodity> {

    private static final long serialVersionUID = 1L;

    /**
     * 键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long commodityId;

    /**
     * prime商品的唯一ID（创建后prime返回的）
     */
    private String primeProductId;

    /**
     * Amazon SKU
     */
    private String amazonSku;

    /**
     * 是否提供Prime服务：0-否，1-是
     */
    private Boolean offerPrime;

    /**
     * 是否可购买
     */
    private Boolean buyable;

    /**
     * Prime商品类型：Individual-单个商品，Bundle-组合商品套餐
     */
    private String primeCommodityType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public String getPrimeProductId() {
        return primeProductId;
    }

    public void setPrimeProductId(String primeProductId) {
        this.primeProductId = primeProductId;
    }

    public String getAmazonSku() {
        return amazonSku;
    }

    public void setAmazonSku(String amazonSku) {
        this.amazonSku = amazonSku;
    }

    public Boolean getOfferPrime() {
        return offerPrime;
    }

    public void setOfferPrime(Boolean offerPrime) {
        this.offerPrime = offerPrime;
    }

    public Boolean getBuyable() {
        return buyable;
    }

    public void setBuyable(Boolean buyable) {
        this.buyable = buyable;
    }

    public String getPrimeCommodityType() {
        return primeCommodityType;
    }

    public void setPrimeCommodityType(String primeCommodityType) {
        this.primeCommodityType = primeCommodityType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PrimeCommodity{" +
        "id=" + id +
        ", commodityId=" + commodityId +
        ", primeProductId=" + primeProductId +
        ", amazonSku=" + amazonSku +
        ", offerPrime=" + offerPrime +
        ", buyable=" + buyable +
        ", primeCommodityType=" + primeCommodityType +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}