package com.insta360.store.business.commodity.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.commodity.enums.DifferenceContentType;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-12-26
 * @Description:
 */
@TableName("product_commodity_difference")
public class CommodityDifference extends BaseModel<CommodityDifference> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @JSONField(name = "commodity_id")
    private Integer commodityId;

    @JSONField(name = "point_id")
    private Integer pointId;

    private String content;

    @JSONField(name = "content_type")
    private String contentType;

    @JSONField(name = "order_index")
    private Integer orderIndex;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Integer getPointId() {
        return pointId;
    }

    public void setPointId(Integer pointId) {
        this.pointId = pointId;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    @Override
    public String toString() {
        return "CommodityDifference{" +
                "id=" + id +
                ", commodityId=" + commodityId +
                ", pointId=" + pointId +
                ", content='" + content + '\'' +
                ", contentType='" + contentType + '\'' +
                ", orderIndex=" + orderIndex +
                '}';
    }

    public DifferenceContentType contentType() {
        return DifferenceContentType.parse(contentType);
    }
}