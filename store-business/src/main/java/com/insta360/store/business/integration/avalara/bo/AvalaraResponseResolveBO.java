package com.insta360.store.business.integration.avalara.bo;

import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.rma.model.RmaOrder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description Avalara 响应解析落库BO
 * @Date 2023/6/29
 */
public class AvalaraResponseResolveBO implements Serializable {

    /**
     * Avalara 响应JSON
     */
    private String responseJson;

    /**
     * 售后单
     */
    private RmaOrder rmaOrder;

    /**
     * 订单
     */
    private Order order;

    public String getResponseJson() {
        return responseJson;
    }

    public void setResponseJson(String responseJson) {
        this.responseJson = responseJson;
    }

    public RmaOrder getRmaOrder() {
        return rmaOrder;
    }

    public void setRmaOrder(RmaOrder rmaOrder) {
        this.rmaOrder = rmaOrder;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    @Override
    public String toString() {
        return "AvalaraResponseResolveBO{" +
                "responseJson='" + responseJson + '\'' +
                ", rmaOrder=" + rmaOrder +
                ", order=" + order +
                '}';
    }
}
