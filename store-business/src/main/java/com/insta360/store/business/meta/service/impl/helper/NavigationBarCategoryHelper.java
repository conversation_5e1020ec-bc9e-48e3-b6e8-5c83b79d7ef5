package com.insta360.store.business.meta.service.impl.helper;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.search.bo.NavigationCategoryBO;
import com.insta360.store.business.configuration.search.bo.SearchNavigationBarInfo;
import com.insta360.store.business.meta.enums.NavigationBarTypeEnum;
import com.insta360.store.business.meta.model.NavigationBarCategoryCommodity;
import com.insta360.store.business.meta.model.NavigationBarCategoryInfo;
import com.insta360.store.business.meta.model.NavigationBarCategoryInside;
import com.insta360.store.business.meta.model.NavigationBarCategorySubsetInside;
import com.insta360.store.business.meta.service.NavigationBarCategoryCommodityService;
import com.insta360.store.business.meta.service.NavigationBarCategoryInfoService;
import com.insta360.store.business.meta.service.NavigationBarCategoryInsideService;
import com.insta360.store.business.meta.service.NavigationBarCategorySubsetInsideService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductCategorySubset;
import com.insta360.store.business.product.service.ProductCategorySubsetService;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/14
 */
@Component
public class NavigationBarCategoryHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(NavigationBarCategoryHelper.class);

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductCategorySubsetService productCategorySubsetService;

    @Autowired
    NavigationBarCategoryInfoService navigationBarCategoryInfoService;

    @Autowired
    NavigationBarCategoryInsideService navigationBarCategoryInsideService;

    @Autowired
    NavigationBarCategoryCommodityService navigationBarCategoryCommodityService;

    @Autowired
    NavigationBarCategorySubsetInsideService navigationBarCategorySubsetInsideService;

    /**
     * 封装搜索信息映射
     *
     * @param commodityIds 套餐ID
     * @return {@link Map }<{@link Integer }, {@link SearchNavigationBarInfo }>
     */
    public Map<Integer, List<NavigationBarCategoryInfo>> packSearchNavigationBarInfoMap(List<Integer> commodityIds) {
        // 所有配件套餐ID
        List<Integer> accessoryCommodityIds = listAccessoryCommodityIds();
        // 导航栏更新触发 涉及的配件套餐ID
        List<Integer> navigationAccessoryCommodityIds = commodityIds.stream().filter(accessoryCommodityIds::contains).collect(Collectors.toList());
        // 导航栏更新触发 涉及的非配件套餐ID
        List<Integer> navigationNotAccessoryCommodityIds = commodityIds.stream().filter(commodityId -> !accessoryCommodityIds.contains(commodityId)).collect(Collectors.toList());

        // 配件套餐 导航栏信息映射
        Map<Integer, List<NavigationBarCategoryInfo>> accessoryNavigationBarInfoMap = this.packAccessoryNavigationBarInfoMap(navigationAccessoryCommodityIds);
        // 非配件套餐 导航栏信息映射
        Map<Integer, List<NavigationBarCategoryInfo>> notAccesoryNavigationBarInfoMap = this.packSecondLevelNavigationBarInfoMap(navigationNotAccessoryCommodityIds);

        accessoryNavigationBarInfoMap.putAll(notAccesoryNavigationBarInfoMap);
        return accessoryNavigationBarInfoMap;

    }

    /**
     * 获取所有的配件套餐
     *
     * @return
     */
    private List<Integer> listAccessoryCommodityIds() {
        List<ProductCategorySubset> productCategorySubsetList = productCategorySubsetService.listByCategoryMainKey(ProductCategoryMainType.CM_ACCESSORY.name());
        List<String> categoryKeyList = productCategorySubsetList.stream().map(ProductCategorySubset::getCategorySubsetKey).collect(Collectors.toList());
        categoryKeyList.add(ProductCategoryMainType.CM_ACCESSORY.name());
        List<Product> productList = productService.listByCategoryKeys(categoryKeyList);
        List<Integer> productIds = productList.stream().map(Product::getId).collect(Collectors.toList());
        List<Commodity> commodityList = commodityService.listByProductIds(productIds);
        return commodityList.stream().map(Commodity::getId).collect(Collectors.toList());

    }

    /**
     * 封装配件信息映射
     *
     * @return {@link Map }<{@link Integer }, {@link List }<{@link NavigationBarCategoryInfo }>>
     */
    private Map<Integer, List<NavigationBarCategoryInfo>> packAccessoryNavigationBarInfoMap(List<Integer> commodityIds) {
        Map<Integer, List<NavigationBarCategoryInfo>> map = new HashMap<>(commodityIds.size());
        List<NavigationBarCategoryInside> navigationBarCategoryInsideList = navigationBarCategoryInsideService.listByType(NavigationBarTypeEnum.ACCESSORY_NEW);
        List<Integer> enabledInsideIds = navigationBarCategoryInsideList.stream().filter(NavigationBarCategoryInside::getEnabled).map(NavigationBarCategoryInside::getId).collect(Collectors.toList());
        List<NavigationBarCategoryInfo> navigationBarCategoryInfos = navigationBarCategoryInfoService.listByInsideIds(enabledInsideIds);
        for (Integer commodityId : commodityIds) {
            map.put(commodityId, navigationBarCategoryInfos);
        }
        return map;
    }

    /**
     * 封装二级导航信息
     *
     * @param commodityIds 套餐ID
     * @return {@link HashMap }<{@link Integer }, {@link List }<{@link NavigationBarCategoryInfo }>>
     */
    private Map<Integer, List<NavigationBarCategoryInfo>> packSecondLevelNavigationBarInfoMap(List<Integer> commodityIds) {
        List<NavigationBarCategoryCommodity> navigationBarCategoryCommodityList = navigationBarCategoryCommodityService.listByCommodityIds(commodityIds);
        if (CollectionUtils.isEmpty(navigationBarCategoryCommodityList)) {
            return new HashMap<>(0);
        }

        // key -> commodityId, value -> subsetInsideId[]
        Map<Integer, List<Integer>> commodityIdSubsetIdListMap = navigationBarCategoryCommodityList.stream()
                .collect(Collectors.groupingBy(NavigationBarCategoryCommodity::getCommodityId, Collectors.mapping(NavigationBarCategoryCommodity::getSubsetInsideId, Collectors.toList())));

        // 二级导航栏ID
        List<Integer> subsetInsideIds = navigationBarCategoryCommodityList.stream().map(NavigationBarCategoryCommodity::getSubsetInsideId).distinct().collect(Collectors.toList());
        List<NavigationBarCategorySubsetInside> navigationBarCategorySubsetInsideList = navigationBarCategorySubsetInsideService.listIds(subsetInsideIds);
        if (CollectionUtils.isEmpty(navigationBarCategorySubsetInsideList)) {
            return new HashMap<>(0);
        }

        // key -> subsetInsideId, value -> categoryInsideId
        Map<Integer, Integer> subsetIdInsideIdMap = navigationBarCategorySubsetInsideList.stream().collect(Collectors.toMap(NavigationBarCategorySubsetInside::getId, NavigationBarCategorySubsetInside::getCategoryInsideId));
        List<Integer> insideIds = navigationBarCategorySubsetInsideList.stream().map(NavigationBarCategorySubsetInside::getCategoryInsideId).collect(Collectors.toList());

        // 获取启用的一级导航栏
        List<NavigationBarCategoryInside> navigationBarCategoryInsideList = navigationBarCategoryInsideService.listByIdsTypes(insideIds, NavigationBarTypeEnum.searchSyncTypeListString());
        List<Integer> enabledInsideIds = navigationBarCategoryInsideList.stream().filter(NavigationBarCategoryInside::getEnabled).map(NavigationBarCategoryInside::getId).collect(Collectors.toList());
        List<NavigationBarCategoryInfo> navigationBarCategoryInfoList = navigationBarCategoryInfoService.listByInsideIds(enabledInsideIds);
        if (CollectionUtils.isEmpty(navigationBarCategoryInfoList)) {
            return new HashMap<>(0);
        }

        // key -> insideId, value -> navigationBarCategoryInfoList[]
        Map<Integer, List<NavigationBarCategoryInfo>> navigationBarCategoryInfoListMap = navigationBarCategoryInfoList.stream().collect(Collectors.groupingBy(NavigationBarCategoryInfo::getCategoryInsideId));

        HashMap<Integer, List<NavigationBarCategoryInfo>> map = new HashMap<>(commodityIds.size());
        for (Integer commodityId : commodityIds) {

            List<Integer> subsetIds = commodityIdSubsetIdListMap.get(commodityId);
            if (CollectionUtils.isEmpty(subsetIds)) {
                continue;
            }
            List<Integer> bindInsideIds = subsetIds.stream().map(subsetIdInsideIdMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(bindInsideIds)) {
                continue;
            }

            LOGGER.info("套餐ID:{} 存在的二级导航栏ID:{}, 一级导航栏ID:{}", commodityId, subsetIds, bindInsideIds);
            List<NavigationBarCategoryInfo> bindNavigationList = bindInsideIds.stream().map(navigationBarCategoryInfoListMap::get)
                    .filter(Objects::nonNull).flatMap(List::stream).filter(Objects::nonNull).collect(Collectors.toList());

            LOGGER.info("套餐ID:{} 存在的二级导航栏:{}", commodityId, bindNavigationList);
            if (CollectionUtils.isEmpty(bindNavigationList)) {
                continue;
            }
            map.put(commodityId, bindNavigationList);
        }

        return map;

    }

    /**
     * 根据导航栏ID 获取搜索相关绑定的套餐ID
     *
     * @param insideId 导航类别 ID
     * @return {@link List }<{@link Integer }>
     */
    public List<Integer> listNavigationSearchCommodityIds(Integer insideId) {
        if (Objects.isNull(insideId)) {
            return new ArrayList<>(0);
        }

        NavigationBarCategoryInside navigationBarCategoryInside = navigationBarCategoryInsideService.getById(insideId);
        if (Objects.isNull(navigationBarCategoryInside)) {
            return new ArrayList<>(0);
        }

        NavigationBarTypeEnum navigationBarTypeEnum = navigationBarCategoryInside.parseType();

        if (!navigationBarTypeEnum.isSearchSyncType()) {
            throw new InstaException(-1, "不是搜索类型");
        }

        // 根据导航栏类型 获取套餐数据
        switch (navigationBarTypeEnum) {
            case ACCESSORY_NEW:
                return listAccessoryCommodityIds();
            case SERVICE_NEW:
            case CAMERA:
                List<NavigationBarCategorySubsetInside> navigationBarCategorySubsetInsideList = navigationBarCategorySubsetInsideService.listByCategoryInsideId(insideId);
                List<Integer> subsetInsideIds = navigationBarCategorySubsetInsideList.stream().map(NavigationBarCategorySubsetInside::getId).collect(Collectors.toList());
                List<NavigationBarCategoryCommodity> navigationBarCategoryCommodityList = navigationBarCategoryCommodityService.listBySubsetInsideIds(subsetInsideIds);
                List<Integer> commodityIds = navigationBarCategoryCommodityList.stream().map(NavigationBarCategoryCommodity::getCommodityId).collect(Collectors.toList());
                List<Commodity> commodities = commodityService.listByCommodityIdIgnoreEnable(commodityIds);
                List<Integer> productIdList = commodities.stream().map(Commodity::getProduct).collect(Collectors.toList());
                List<Commodity> allCommodities = commodityService.listByProductIds(productIdList);
                return allCommodities.stream().map(Commodity::getId).collect(Collectors.toList());
            default:
                return new ArrayList<>(0);

        }

    }

    /**
     * 根据类型获取商品列表
     *
     * @param commodityIds 商品ID列表，用于指定需要查询的商品
     * @return 返回一个整型列表，包含根据商品ID查询到的所有商品的ID
     */
    public List<Integer> listNavigationCommodityIds(List<Integer> commodityIds) {
        // 根据商品ID列表获取商品信息，忽略启用状态
        List<Commodity> commodities = commodityService.listByCommodityIdIgnoreEnable(commodityIds);
        // 提取商品信息中的产品ID，用于后续查询
        List<Integer> productIdList = commodities.stream().map(Commodity::getProduct).collect(Collectors.toList());
        // 根据产品ID列表获取所有相关商品信息
        List<Commodity> allCommodities = commodityService.listByProductIds(productIdList);
        // 提取所有相关商品的ID
        List<Integer> allCommodityIds = allCommodities.stream().map(Commodity::getId).collect(Collectors.toList());

        // 获取配件商品ID列表
        List<Integer> accessoryCommodityIds = listAccessoryCommodityIds();
        // 计算配件商品ID列表与传入商品ID列表的交集
        List<Integer> intersection = ListUtils.intersection(accessoryCommodityIds, commodityIds);
        // 判断是否有交集，即是否存在配件商品
        boolean isHaveAccessory = !intersection.isEmpty();
        // 如果存在配件商品，则将所有商品ID与配件商品ID合并
        if (isHaveAccessory) {
            allCommodityIds.addAll(accessoryCommodityIds);
        }
        // 返回所有商品ID列表
        return allCommodityIds;
    }

    /**
     * 封装导航信息Bo
     *
     * @param categoryInsideId ID 内类别
     * @return {@link NavigationCategoryBO }
     */
    public NavigationCategoryBO packNavigationBo(Integer categoryInsideId) {
        NavigationBarCategoryInside navigationBarCategoryInside = navigationBarCategoryInsideService.getById(categoryInsideId);
        return new NavigationCategoryBO(categoryInsideId, navigationBarCategoryInside.parseType());
    }

    /**
     * 根据二级导航栏ID 封装导航信息Bo
     *
     * @param subsetInsideId
     * @return {@link NavigationCategoryBO }
     */
    public NavigationCategoryBO packNavigationBoBySubsetInsideId(Integer subsetInsideId) {
        NavigationBarCategorySubsetInside navigationBarCategorySubsetInside = navigationBarCategorySubsetInsideService.getById(subsetInsideId);
        if (navigationBarCategorySubsetInside == null) {
            throw new InstaException(-1, "二级导航栏信息不存在");
        }
        Integer categoryInsideId = navigationBarCategorySubsetInside.getCategoryInsideId();
        NavigationBarCategoryInside navigationBarCategoryInside = navigationBarCategoryInsideService.getById(categoryInsideId);
        if (navigationBarCategoryInside == null) {
            throw new InstaException(-1, "一级导航栏信息不存在");
        }
        return new NavigationCategoryBO(categoryInsideId, navigationBarCategoryInside.parseType());
    }

}
