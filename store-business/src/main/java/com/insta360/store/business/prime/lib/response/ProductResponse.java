package com.insta360.store.business.prime.lib.response;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public class ProductResponse implements PrimeResponse {

    private Product product;

    public Product getProduct() {
        return product;
    }

    public void setProduct(Product product) {
        this.product = product;
    }

    public static class Product {

        private String id;

        private ExternalId externalId;

        private String sku;

        private String amazonSku;

        private Boolean offerPrime;

        private String productDetailPageUrl;

        private String purchaseGroupMemberships;

        private Image image;

        private Buyability buyability;

        private RepresentativeOfPurchaseGroup representativeOfPurchaseGroup;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public ExternalId getExternalId() {
            return externalId;
        }

        public void setExternalId(ExternalId externalId) {
            this.externalId = externalId;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getAmazonSku() {
            return amazonSku;
        }

        public void setAmazonSku(String amazonSku) {
            this.amazonSku = amazonSku;
        }

        public Boolean getOfferPrime() {
            return offerPrime;
        }

        public void setOfferPrime(Boolean offerPrime) {
            this.offerPrime = offerPrime;
        }

        public String getProductDetailPageUrl() {
            return productDetailPageUrl;
        }

        public void setProductDetailPageUrl(String productDetailPageUrl) {
            this.productDetailPageUrl = productDetailPageUrl;
        }

        public String getPurchaseGroupMemberships() {
            return purchaseGroupMemberships;
        }

        public void setPurchaseGroupMemberships(String purchaseGroupMemberships) {
            this.purchaseGroupMemberships = purchaseGroupMemberships;
        }

        public Image getImage() {
            return image;
        }

        public void setImage(Image image) {
            this.image = image;
        }

        public Buyability getBuyability() {
            return buyability;
        }

        public void setBuyability(Buyability buyability) {
            this.buyability = buyability;
        }

        public RepresentativeOfPurchaseGroup getRepresentativeOfPurchaseGroup() {
            return representativeOfPurchaseGroup;
        }

        public void setRepresentativeOfPurchaseGroup(RepresentativeOfPurchaseGroup representativeOfPurchaseGroup) {
            this.representativeOfPurchaseGroup = representativeOfPurchaseGroup;
        }
    }

    public static class ExternalId {

        private String value;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    public static class Image {

        private String displayReadyUrl;

        private String sourceUrl;

        public String getDisplayReadyUrl() {
            return displayReadyUrl;
        }

        public void setDisplayReadyUrl(String displayReadyUrl) {
            this.displayReadyUrl = displayReadyUrl;
        }

        public String getSourceUrl() {
            return sourceUrl;
        }

        public void setSourceUrl(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }
    }

    public static class Buyability {

        private String status;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }
    }
    
    public static class RepresentativeOfPurchaseGroup {
        private Members members;

        public Members getMembers() {
            return members;
        }

        public void setMembers(Members members) {
            this.members = members;
        }
    }

    public static class Members {
        private List<Edge> edges;

        public List<Edge> getEdges() {
            return edges;
        }

        public void setEdges(List<Edge> edges) {
            this.edges = edges;
        }
    }

    public static class Edge {
        private String cursor;
        private Node node;

        public String getCursor() {
            return cursor;
        }

        public void setCursor(String cursor) {
            this.cursor = cursor;
        }

        public Node getNode() {
            return node;
        }

        public void setNode(Node node) {
            this.node = node;
        }
    }

    public static class Node {
        private MemberAmount memberAmount;
        private NodeProduct product;
        private ProductGroup productGroup;

        public MemberAmount getMemberAmount() {
            return memberAmount;
        }

        public void setMemberAmount(MemberAmount memberAmount) {
            this.memberAmount = memberAmount;
        }

        public NodeProduct getProduct() {
            return product;
        }

        public void setProduct(NodeProduct product) {
            this.product = product;
        }

        public ProductGroup getProductGroup() {
            return productGroup;
        }

        public void setProductGroup(ProductGroup productGroup) {
            this.productGroup = productGroup;
        }
    }

    public static class MemberAmount {
        private String unit;
        private Integer value;

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }
    }

    public static class NodeProduct {
        private ExternalId externalId;

        public ExternalId getExternalId() {
            return externalId;
        }

        public void setExternalId(ExternalId externalId) {
            this.externalId = externalId;
        }
    }

    public static class ProductGroup {
        private Object members;

        public Object getMembers() {
            return members;
        }

        public void setMembers(Object members) {
            this.members = members;
        }
    }

}