package com.insta360.store.business.product.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.product.model.ProductVideo;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2020/11/20
 * @Description:
 */
public class ProductVideoDTO implements Serializable {

    /**
     * id
     */
    private Integer id;

    /**
     * 产品id
     */
    private Integer product;

    /**
     * 语言
     */
    private String language;

    /**
     * cover
     */
    private String cover;

    /**
     * url
     */
    private String url;

    /**
     * label
     */
    private String label;

    /**
     * 类型
     */
    private String type;

    /**
     * 序号
     */
    private Integer orderIndex;

    public ProductVideo getPojoObject() {
        ProductVideo productVideo = new ProductVideo();
        BeanUtil.copyProperties(this, productVideo);
        return productVideo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    @Override
    public String toString() {
        return "ProductVideoDTO{" +
                "id=" + id +
                ", product=" + product +
                ", language='" + language + '\'' +
                ", cover='" + cover + '\'' +
                ", url='" + url + '\'' +
                ", label='" + label + '\'' +
                ", type='" + type + '\'' +
                ", orderIndex=" + orderIndex +
                '}';
    }
}
