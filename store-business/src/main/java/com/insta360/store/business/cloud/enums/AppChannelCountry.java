package com.insta360.store.business.cloud.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Description App渠道订阅支持的国家/地区 （Apple Store /Google Store）
 * @Date 2024/5/21
 */
public enum AppChannelCountry {

    CN("CN", "CHN", "中国"),

    HK("HK", "HKG", "香港"),

    MO("MO", "MAC", "澳门"),

    TW("TW", "TWN", "台湾"),

    US("US", "USA", "美国"),

    DE("DE", "DEU", "德国"),

    IT("IT", "ITA", "意大利"),

    ES("ES", "ESP", "西班牙"),

    MY("MY", "MYS", "马来西亚"),

    FR("FR", "FRA", "法国"),

    ID("ID", "IDN", "印度尼西亚"),

    AU("AU", "AUS", "澳大利亚"),

    TH("TH", "THA", "泰国"),

    SG("SG", "SGP", "新加坡"),

    PH("PH", "PHL", "菲律宾"),

    CH("CH", "CHE", "瑞士"),

    NL("NL", "NLD", "荷兰"),

    CA("CA", "CAN", "加拿大"),

    KR("KR", "KOR", "韩国"),

    AL("AL", "ALB", "阿尔巴尼亚"),

    IE("IE", "IRL", "爱尔兰"),

    EE("EE", "EST", "爱沙尼亚"),

    AT("AT", "AUT", "奥地利"),

    BY("BY", "BLR", "白俄罗斯"),

    BG("BG", "BGR", "保加利亚"),

    MK("MK", "MKD", "北马其顿"),

    BE("BE", "BEL", "比利时"),

    IS("IS", "ISL", "冰岛"),

    PL("PL", "POL", "波兰"),

    BA("BA", "BIH", "波斯尼亚和黑塞哥维那"),

    DK("DK", "DNK", "丹麦"),

    RU("RU", "RUS", "俄罗斯"),

    FI("FI", "FIN", "芬兰"),

    ME("ME", "MNE", "黑山"),

    CZ("CZ", "CZE", "捷克"),

    XK("XK", "XKX", "科索沃"),

    HR("HR", "HRV", "克罗地亚"),

    LV("LV", "LVA", "拉脱维亚"),

    LT("LT", "LTU", "立陶宛"),

    LU("LU", "LUX", "卢森堡"),

    RO("RO", "ROU", "罗马尼亚"),

    MT("MT", "MLT", "马耳他"),

    MD("MD", "MDA", "摩尔多瓦"),

    NO("NO", "NOR", "挪威"),

    PT("PT", "PRT", "葡萄牙"),

    SE("SE", "SWE", "瑞典"),

    RS("RS", "SRB", "塞尔维亚"),

    CY("CY", "CYP", "塞浦路斯"),

    SK("SK", "SVK", "斯洛伐克"),

    SI("SI", "SVN", "斯洛文尼亚"),

    TR("TR", "TUR", "土耳其"),

    UA("UA", "UKR", "乌克兰"),

    GR("GR", "GRC", "希腊"),

    HU("HU", "HUN", "匈牙利"),

    GB("GB", "GBR", "英国"),

    DZ("DZ", "DZA", "阿尔及利亚"),

    AF("AF", "AFG", "阿富汗"),

    AE("AE", "ARE", "阿拉伯联合酋长国"),

    OM("OM", "OMN", "阿曼"),

    AZ("AZ", "AZE", "阿塞拜疆"),

    EG("EG", "EGY", "埃及"),

    AO("AO", "AGO", "安哥拉"),

    BH("BH", "BHR", "巴林"),

    BJ("BJ", "BEN", "贝宁"),

    BW("BW", "BWA", "博茨瓦纳"),

    BF("BF", "BFA", "布基纳法索"),

    CV("CV", "CPV", "佛得角"),

    GM("GM", "GMB", "冈比亚"),

    CG("CG", "COG", "刚果共和国"),

    CD("CD", "COD", "刚果民主共和国"),

    GE("GE", "GEO", "格鲁吉亚"),

    GW("GW", "GNB", "几内亚比绍"),

    GH("GH", "GHA", "加纳"),

    GA("GA", "GAB", "加蓬"),

    ZW("ZW", "ZWE", "津巴布韦"),

    CM("CM", "CMR", "喀麦隆"),

    QA("QA", "QAT", "卡塔尔"),

    CI("CI", "CIV", "科特迪瓦"),

    KW("KW", "KWT", "科威特"),

    KE("KE", "KEN", "肯尼亚"),

    LB("LB", "LBN", "黎巴嫩"),

    LR("LR", "LBR", "利比里亚"),

    LY("LY", "LBY", "利比亚"),

    RW("RW", "RWA", "卢旺达"),

    MG("MG", "MDG", "马达加斯加"),

    MW("MW", "MWI", "马拉维"),

    ML("ML", "MLI", "马里"),

    MU("MU", "MUS", "毛里求斯"),

    MR("MR", "MRT", "毛里塔尼亚"),

    MA("MA", "MAR", "摩洛哥"),

    MZ("MZ", "MOZ", "莫桑比克"),

    NA("NA", "NAM", "纳米比亚"),

    ZA("ZA", "ZAF", "南非"),

    NE("NE", "NER", "尼日尔"),

    NG("NG", "NGA", "尼日利亚"),

    SL("SL", "SLE", "塞拉利昂"),

    SN("SN", "SEN", "塞内加尔"),

    SC("SC", "SYC", "塞舌尔"),

    SA("SA", "SAU", "沙特阿拉伯"),

    ST("ST", "STP", "圣多美和普林西比"),

    SZ("SZ", "SWZ", "斯威士兰"),

    TZ("TZ", "TZA", "坦桑尼亚"),

    TN("TN", "TUN", "突尼斯"),

    UG("UG", "UGA", "乌干达"),

    AM("AM", "ARM", "亚美尼亚"),

    YE("YE", "YEM", "也门"),

    IQ("IQ", "IRQ", "伊拉克"),

    IL("IL", "ISR", "以色列"),

    JO("JO", "JOR", "约旦"),

    ZM("ZM", "ZMB", "赞比亚"),

    TD("TD", "TCD", "乍得"),

    AR("AR", "ARG", "阿根廷"),

    AI("AI", "AIA", "安圭拉"),

    AG("AG", "ATG", "安提瓜和巴布达"),

    BB("BB", "BRB", "巴巴多斯"),

    BS("BS", "BHS", "巴哈马"),

    PY("PY", "PRY", "巴拉圭"),

    PA("PA", "PAN", "巴拿马"),

    BR("BR", "BRA", "巴西"),

    BM("BM", "BMU", "百慕大"),

    BO("BO", "BOL", "玻利维亚"),

    BZ("BZ", "BLZ", "伯利兹"),

    DO("DO", "DOM", "多米尼加共和国"),

    DM("DM", "DMA", "多米尼克"),

    EC("EC", "ECU", "厄瓜多尔"),

    CO("CO", "COL", "哥伦比亚"),

    CR("CR", "CRI", "哥斯达黎加"),

    GD("GD", "GRD", "格林纳达"),

    GY("GY", "GUY", "圭亚那"),

    HN("HN", "HND", "洪都拉斯"),

    KY("KY", "CYM", "开曼群岛"),

    MS("MS", "MSR", "蒙特塞拉特"),

    PE("PE", "PER", "秘鲁"),

    MX("MX", "MEX", "墨西哥"),

    NI("NI", "NIC", "尼加拉瓜"),

    SV("SV", "SLV", "萨尔瓦多"),

    KN("KN", "KNA", "圣基茨和尼维斯"),

    LC("LC", "LCA", "圣卢西亚"),

    VC("VC", "VCT", "圣文森特和格林纳丁斯"),

    SR("SR", "SUR", "苏里南"),

    TC("TC", "TCA", "特克斯和凯科斯群岛"),

    TT("TT", "TTO", "特立尼达和多巴哥"),

    GT("GT", "GTM", "危地马拉"),

    VE("VE", "VEN", "委内瑞拉"),

    UY("UY", "URY", "乌拉圭"),

    JM("JM", "JAM", "牙买加"),

    VG("VG", "VGB", "英属维尔京群岛"),

    CL("CL", "CHL", "智利"),

    JP("JP", "JPN", "日本"),

    PG("PG", "PNG", "巴布亚新几内亚"),

    PK("PK", "PAK", "巴基斯坦"),

    BT("BT", "BTN", "不丹"),

    FJ("FJ", "FJI", "斐济"),

    KZ("KZ", "KAZ", "哈萨克斯坦"),

    KG("KG", "KGZ", "吉尔吉斯斯坦"),

    KH("KH", "KHM", "柬埔寨"),

    LA("LA", "LAO", "老挝"),

    MV("MV", "MDV", "马尔代夫"),

    MN("MN", "MNG", "蒙古"),

    FM("FM", "FSM", "密克罗尼西亚"),

    MM("MM", "MMR", "缅甸"),

    NR("NR", "NRU", "瑙鲁"),

    NP("NP", "NPL", "尼泊尔"),

    PW("PW", "PLW", "帕劳"),

    LK("LK", "LKA", "斯里兰卡"),

    SB("SB", "SLB", "所罗门群岛"),

    TJ("TJ", "TJK", "塔吉克斯坦"),

    TO("TO", "TON", "汤加"),

    TM("TM", "TKM", "土库曼斯坦"),

    VU("VU", "VUT", "瓦努阿图"),

    BN("BN", "BRN", "文莱"),

    UZ("UZ", "UZB", "乌兹别克斯坦"),

    NZ("NZ", "NZL", "新西兰"),

    VN("VN", "VNM", "越南"),

    CF("CF", "CAF", "中非共和国"),

    GN("GN", "GIN", "几内亚"),

    LI("LI", "LIE", "列支敦士登"),

    ER("ER", "ERI", "厄立特里亚"),

    DJ("DJ", "DJI", "吉布提"),

    SM("SM", "SMR", "圣马力诺"),

    TG("TG", "TGO", "多哥"),

    BD("BD", "BGD", "孟加拉国"),

    MC("MC", "MCO", "摩纳哥"),

    VA("VA", "VAT", "梵蒂冈"),

    HT("HT", "HTI", "海地"),

    GI("GI", "GIB", "直布罗陀"),

    KM("KM", "COM", "科摩罗"),

    SO("SO", "SOM", "索马里"),

    WS("WS", "WSM", "萨摩亚"),

    AW("AW", "ABW", "阿鲁巴"),

    IN("IN", "IND", "印度");

    private final String googleCode;

    private final String appleCode;

    private final String name;

    AppChannelCountry(String googleCode, String appleCode, String name) {
        this.googleCode = googleCode;
        this.appleCode = appleCode;
        this.name = name;
    }

    /**
     * 根据谷歌或苹果等渠道中的地区代码（二字码、三字码）获取到对应枚举
     *
     * @param countryCode 国家代码，字符串形式。
     * @return 如果找到匹配的国家对象，则返回该对象；如果未找到或输入代码为空，则返回null。
     */
    public static AppChannelCountry matchCode(String countryCode) {
        // 检查国家代码是否为空
        if (StringUtils.isBlank(countryCode)) {
            return null;
        }
        // 遍历所有可用的国家代码，查找匹配的国家代码
        for (AppChannelCountry appChannelCountry : AppChannelCountry.values()) {
            if (appChannelCountry.getGoogleCode().equals(countryCode) || appChannelCountry.getAppleCode().equals(countryCode)) {
                return appChannelCountry;
            }
        }
        return null;
    }


    public String getGoogleCode() {
        return googleCode;
    }

    public String getAppleCode() {
        return appleCode;
    }

    public String getName() {
        return name;
    }
}
