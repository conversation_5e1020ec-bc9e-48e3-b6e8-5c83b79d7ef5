package com.insta360.store.business.discount.service;

import com.insta360.store.business.discount.dto.ao.BatchCreateDiscountCommonAO;
import com.insta360.store.business.discount.dto.ao.CreateCommonDiscountAO;
import com.insta360.store.business.discount.dto.ro.BatchCreateCommonDiscountRO;
import com.insta360.store.business.discount.dto.ro.CreateCommonDiscountRo;

public interface DiscountWritService {

    /**
     * 创建折扣券（优惠券、代金券）
     * @param createDiscountReq
     * @return
     */
    CreateCommonDiscountRo create(CreateCommonDiscountAO createDiscountReq);

    /**
     * 更新折扣券（优惠券、代金券）
     * @param createDiscountReq
     * @return
     */
    CreateCommonDiscountRo update(CreateCommonDiscountAO createDiscountReq);

    /**
     * 批量创建
     * @param batchCreateDiscountCommonReq
     * @return
     */
    BatchCreateCommonDiscountRO batchCreate(BatchCreateDiscountCommonAO batchCreateDiscountCommonReq);
}
