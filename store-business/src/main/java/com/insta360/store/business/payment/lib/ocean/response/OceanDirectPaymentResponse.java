package com.insta360.store.business.payment.lib.ocean.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.store.business.payment.enums.OceanPaymentStatus;

import java.math.BigDecimal;

/**
 * 0元验卡操作
 *
 * <AUTHOR>
 * @date 2025/04/29
 */
public class OceanDirectPaymentResponse {

    /**
     * 账户号（必填）
     */
    private String account;

    /**
     * 3D支付跳转地址（关键业务字段）
     * <p>当payUrl不为空时必须进行页面重定向</p>
     */
    @JSONField(name = "pay_url")
    private String payUrl;

    /**
     * 支付认证类型枚举（新增类型说明）
     * 0:该交易类型为‘Sale’一般交易
     * 1:该交易类型为‘预授权且非3D’交易
     * 2:该交易类型为‘3D且非预授权’交易
     * 3:该交易类型为‘3D且预授权’交易
     */
    @JSONField(name = "payment_authType")
    private String paymentAuthType;

    /**
     * 支付状态枚举（扩展状态定义）
     * -1:待处理（预授权才会返回）
     * 0:支付失败
     * 1:支付成功
     */
    @JSONField(name = "payment_status")
    private String paymentStatus;

    // 原有字段区保持结构（新增注释说明）
    @JSONField(name = "notice_type")
    private String noticeType;

    @JSONField(name = "push_dateTime", format = "yyyy-MM-dd HH:mm:ss")
    private String pushDateTime;

    @JSONField(name = "terminal")
    private String terminal;

    /**
     * 签名值
     */
    @JSONField(name = "signValue")
    private String signValue;

    /**
     * 支付方式
     */
    private String methods;

    /**
     * 订单编号
     */
    @JSONField(name = "order_number")
    private String orderNumber;

    /**
     * 卡所属国家
     */
    @JSONField(name = "card_country")
    private String cardCountry;

    /**
     * 订单币种
     */
    @JSONField(name = "order_currency")
    private String orderCurrency;

    /**
     * 订单金额（使用BigDecimal处理金额）
     */
    @JSONField(name = "order_amount")
    private String orderAmount;

    /**
     * 订单备注（允许空值）
     */
    @JSONField(name = "order_notes")
    private String orderNotes = "";  // 默认空字符串

    /**
     * 卡号（脱敏）
     */
    @JSONField(name = "card_number")
    private String cardNumber;

    /**
     * 卡类型
     */
    @JSONField(name = "card_type")
    private String cardType;

    /**
     * 支付国家
     */
    @JSONField(name = "payment_country")
    private String paymentCountry;

    /**
     * 支付流水号
     */
    @JSONField(name = "payment_id")
    private String paymentId;

    /**
     * 支付详情
     */
    @JSONField(name = "payment_details")
    private String paymentDetails;

    /**
     * 支付解决方案
     */
    @JSONField(name = "payment_solutions")
    private String paymentSolutions;

    /**
     * 支付风险（允许空值）
     */
    @JSONField(name = "payment_risk")
    private String paymentRisk;

    /**
     * 实际支付金额（允许空值）
     */
    @JSONField(name = "payment_amount")
    private BigDecimal paymentAmount;

    /**
     * 汇率（允许空值）
     */
    @JSONField(name = "payment_exchangeRate")
    private String paymentExchangeRate;

    /**
     * 认证原因（允许空值）
     */
    @JSONField(name = "auth_reason")
    private String authReason;

    /**
     * 认证码（允许空值）
     */
    @JSONField(name = "auth_code")
    private String authCode;

    /**
     * 支付用户ID（允许空值）
     */
    @JSONField(name = "pay_userId")
    private String payUserId;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public String getPaymentAuthType() {
        return paymentAuthType;
    }

    public void setPaymentAuthType(String paymentAuthType) {
        this.paymentAuthType = paymentAuthType;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getPushDateTime() {
        return pushDateTime;
    }

    public void setPushDateTime(String pushDateTime) {
        this.pushDateTime = pushDateTime;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getSignValue() {
        return signValue;
    }

    public void setSignValue(String signValue) {
        this.signValue = signValue;
    }

    public String getMethods() {
        return methods;
    }

    public void setMethods(String methods) {
        this.methods = methods;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getCardCountry() {
        return cardCountry;
    }

    public void setCardCountry(String cardCountry) {
        this.cardCountry = cardCountry;
    }

    public String getOrderCurrency() {
        return orderCurrency;
    }

    public void setOrderCurrency(String orderCurrency) {
        this.orderCurrency = orderCurrency;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getOrderNotes() {
        return orderNotes;
    }

    public void setOrderNotes(String orderNotes) {
        this.orderNotes = orderNotes;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getPaymentCountry() {
        return paymentCountry;
    }

    public void setPaymentCountry(String paymentCountry) {
        this.paymentCountry = paymentCountry;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getPaymentDetails() {
        return paymentDetails;
    }

    public void setPaymentDetails(String paymentDetails) {
        this.paymentDetails = paymentDetails;
    }

    public String getPaymentSolutions() {
        return paymentSolutions;
    }

    public void setPaymentSolutions(String paymentSolutions) {
        this.paymentSolutions = paymentSolutions;
    }

    public String getPaymentRisk() {
        return paymentRisk;
    }

    public void setPaymentRisk(String paymentRisk) {
        this.paymentRisk = paymentRisk;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getPaymentExchangeRate() {
        return paymentExchangeRate;
    }

    public void setPaymentExchangeRate(String paymentExchangeRate) {
        this.paymentExchangeRate = paymentExchangeRate;
    }

    public String getAuthReason() {
        return authReason;
    }

    public void setAuthReason(String authReason) {
        this.authReason = authReason;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getPayUserId() {
        return payUserId;
    }

    public void setPayUserId(String payUserId) {
        this.payUserId = payUserId;
    }

    @Override
    public String toString() {
        return "DirectPaymentResponse{" +
                "account='" + account + '\'' +
                ", payUrl='" + payUrl + '\'' +
                ", paymentAuthType='" + paymentAuthType + '\'' +
                ", paymentStatus='" + paymentStatus + '\'' +
                ", noticeType='" + noticeType + '\'' +
                ", pushDateTime='" + pushDateTime + '\'' +
                ", terminal='" + terminal + '\'' +
                ", signValue='" + signValue + '\'' +
                ", methods='" + methods + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", cardCountry='" + cardCountry + '\'' +
                ", orderCurrency='" + orderCurrency + '\'' +
                ", orderAmount='" + orderAmount + '\'' +
                ", orderNotes='" + orderNotes + '\'' +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardType='" + cardType + '\'' +
                ", paymentCountry='" + paymentCountry + '\'' +
                ", paymentId='" + paymentId + '\'' +
                ", paymentDetails='" + paymentDetails + '\'' +
                ", paymentSolutions='" + paymentSolutions + '\'' +
                ", paymentRisk='" + paymentRisk + '\'' +
                ", paymentAmount=" + paymentAmount +
                ", paymentExchangeRate='" + paymentExchangeRate + '\'' +
                ", authReason='" + authReason + '\'' +
                ", authCode='" + authCode + '\'' +
                ", payUserId='" + payUserId + '\'' +
                '}';
    }

    public OceanPaymentStatus paymentStatus() {
        return OceanPaymentStatus.matchCode(paymentStatus);
    }

    public Boolean isFailed() {
        return OceanPaymentStatus.FAILED.equals(paymentStatus());
    }

    public Boolean isPending() {
        return OceanPaymentStatus.PENDING.equals(paymentStatus());
    }
}
