package com.insta360.store.business.meta.bo;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.store.business.meta.model.ActivityLocalesConfig;
import com.insta360.store.business.meta.model.ActivityPage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public class ActivityPageBO implements Serializable {

    /**
     * 活动ID 如果是修改则不能为空
     */
    private Integer id;

    /**
     * 活动URL路径
     */
    @NotBlank
    private String urlKey;

    /**
     * 活动 配置key 根据key获取配置
     */
    @NotBlank
    private String activityKey;

    /**
     * 活动名
     */
    @NotBlank
    private String activityName;

    /**
     * 活动埋点名
     */
    @NotBlank
    private String campaignName;

    /**
     * 活动国家配置信息
     */
    @NotNull
    @Valid
    private List<ActivityLocalesConfigInfoBO> localesConfigs;

    /**
     * 创建活动落地页配置
     *
     * @return
     */
    public ActivityPage buildActivityPage() {
        ActivityPage activityPage = new ActivityPage();
        BeanUtils.copyProperties(this, activityPage);
        return activityPage;
    }

    /**
     * 构建活动本地化配置列表
     * <p>
     * 如果localesConfigs为空，则返回一个空的ArrayList，否则遍历localesConfigs中的每个配置，
     * 为每个国家生成一个唯一的UUID作为组键，并设置活动ID、国家、开始时间和结束时间
     *
     * @return 活动本地化配置列表
     */
    public List<ActivityLocalesConfig> buildActivityLocalesConfigs(Integer activityId) {
        // 检查localesConfigs是否为空，如果为空则返回一个空的ArrayList
        if (CollectionUtils.isEmpty(localesConfigs)) {
            return new ArrayList<>(0);
        }
        // 遍历localesConfigs，为每个配置生成对应的ActivityLocalesConfig对象
        return localesConfigs.stream().map(localesConfig -> {
            List<InstaCountry> countryList = localesConfig.getCountryList();
            String uuid = UUIDUtils.generateUuid();

            // 为每个国家生成ActivityLocalesConfig对象，并设置相关属性
            return countryList.stream().map(country -> {
                ActivityLocalesConfig activityLocalesConfig = new ActivityLocalesConfig();
                activityLocalesConfig.setGroupKey(uuid);
                activityLocalesConfig.setActivityId(activityId);
                activityLocalesConfig.setCountry(country.name());
                activityLocalesConfig.setStartTime(localesConfig.getStartTime());
                activityLocalesConfig.setEndTime(localesConfig.getEndTime());
                return activityLocalesConfig;
            }).collect(Collectors.toList());
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUrlKey() {
        return urlKey;
    }

    public void setUrlKey(String urlKey) {
        this.urlKey = urlKey;
    }

    public String getActivityKey() {
        return activityKey;
    }

    public void setActivityKey(String activityKey) {
        this.activityKey = activityKey;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public List<ActivityLocalesConfigInfoBO> getLocalesConfigs() {
        return localesConfigs;
    }

    public void setLocalesConfigs(List<ActivityLocalesConfigInfoBO> localesConfigs) {
        this.localesConfigs = localesConfigs;
    }
}
