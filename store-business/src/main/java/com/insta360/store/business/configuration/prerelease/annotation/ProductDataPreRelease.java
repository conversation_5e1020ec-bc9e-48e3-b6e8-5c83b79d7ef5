package com.insta360.store.business.configuration.prerelease.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: wbt
 * @Date: 2022/04/19
 * @Description: 产品数据预发布
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ProductDataPreRelease {

    /**
     * 是否开启
     *
     * @return
     */
    boolean isEnabled() default true;
}
