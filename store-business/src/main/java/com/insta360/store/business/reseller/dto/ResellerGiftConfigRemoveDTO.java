package com.insta360.store.business.reseller.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 分销赠品活动配置-删除实体
 * @Date 2022/8/11
 */
public class ResellerGiftConfigRemoveDTO {

    /**
     * 规则ID
     */
    @NotEmpty(message = "请指定规则ID")
    private List<Integer> configRuleIdList;

    /**
     * 模块类型
     * @see com.insta360.store.business.reseller.enums.ResellerGiftModuleType
     */
    @NotNull(message = "请指定模块类型")
    private Integer moduleType;

    public List<Integer> getConfigRuleIdList() {
        return configRuleIdList;
    }

    public void setConfigRuleIdList(List<Integer> configRuleIdList) {
        this.configRuleIdList = configRuleIdList;
    }

    public Integer getModuleType() {
        return moduleType;
    }

    public void setModuleType(Integer moduleType) {
        this.moduleType = moduleType;
    }
}
