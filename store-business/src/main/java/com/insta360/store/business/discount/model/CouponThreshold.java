package com.insta360.store.business.discount.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.common.Marker;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 优惠券门槛DB实体
 * @Date 2022/3/21
 */
@TableName("coupon_threshold")
public class CouponThreshold extends BaseModel<CouponThreshold> implements Marker {

    /**
     * 门槛ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 优惠券编号 or 优惠券模版编号
     */
    private String couponCode;

    /**
     * 门槛类型Mark值，二进制计算   缺省值：0
     * @see com.insta360.store.business.discount.constant.ThresholdMarkType
     */
    private long thresholdTypeMark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public long getThresholdTypeMark() {
        return thresholdTypeMark;
    }

    public void setThresholdTypeMark(long thresholdTypeMark) {
        this.thresholdTypeMark = thresholdTypeMark;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public Long getMark() {
        return thresholdTypeMark;
    }

    @Override
    public void setMark(Long mark) {
        this.thresholdTypeMark = mark;
    }
}
