package com.insta360.store.business.commodity.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.commodity.model.CommoditySaleState;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-08-03
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface CommoditySaleStateDao extends BaseDao<CommoditySaleState> {

    /**
     * 批量更新销售状态
     *
     * @param commoditySaleStates
     */
    void updateBatchCommoditySaleState(@Param("commoditySaleStates") List<CommoditySaleState> commoditySaleStates);
}
