package com.insta360.store.business.integration.google.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.integration.google.model.GoogleCommodity;
import com.insta360.store.business.integration.google.model.GoogleUpShelfCommodity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/10/27
 */
public interface GoogleCommodityDao extends BaseDao<GoogleCommodity> {

    List<GoogleCommodity> selectGoogleCommoditySaleState(@Param("state") Integer state);

    List<GoogleCommodity> selectGoogleCommoditySalePrice(@Param("state") Integer state);

    /**
     * 查询出套餐对应的地区、原价、现价、币种、销售状态
     *
     * @param commodityIds
     * @return
     */
    List<GoogleCommodity> getCommodityPriceById(@Param("commodityIds") List<Integer> commodityIds);

    /**
     * 查询出套餐适配语言、名称，对应产品名称、类型、分类
     *
     * @param commodityIds
     * @return
     */
    List<GoogleCommodity> getGoogleCommodityInfoById(@Param("commodityIds") List<Integer> commodityIds);

    List<GoogleCommodity> getCommodityDetailById(@Param("commodityIds") List<Integer> commodityIds);

    /**
     * 获取谷歌上下架信息
     *
     * @param enabled
     * @return
     */
    List<GoogleUpShelfCommodity> selectUpShelfCommodity(@Param("enabled") Integer enabled);
}
