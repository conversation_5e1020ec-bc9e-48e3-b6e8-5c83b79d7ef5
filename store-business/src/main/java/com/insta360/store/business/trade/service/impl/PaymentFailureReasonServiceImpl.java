package com.insta360.store.business.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.trade.dao.PaymentFailureReasonDao;
import com.insta360.store.business.trade.model.PaymentFailureReason;
import com.insta360.store.business.trade.service.PaymentFailureReasonService;
import org.springframework.stereotype.Service;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-12-17
 * @Description:
 */
@Service
public class PaymentFailureReasonServiceImpl extends BaseServiceImpl<PaymentFailureReasonDao, PaymentFailureReason> implements PaymentFailureReasonService {

    @Override
    public PaymentFailureReason getByResponseCode(String payMethod, String responseCode) {
        QueryWrapper<PaymentFailureReason> qw = new QueryWrapper<>();
        qw.eq("pay_method", payMethod);
        qw.eq("response_code", responseCode);
        return baseMapper.selectOne(qw);
    }
}
