package com.insta360.store.business.integration.avalara.service.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.store.business.integration.avalara.enums.StoreTransactionType;
import com.insta360.store.business.integration.avalara.service.hadler.TaxService;
import com.insta360.store.business.integration.avalara.service.hadler.avalara.*;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2023/06/19
 * @Description:
 */
@Component
public class AvalaraTaxHandlerFactory implements TaxFactory {

    /**
     * 获取 Avalara 计税处理器
     *
     * @param storeAvalaraType
     * @return
     */
    @Override
    public TaxService getTaxService(StoreTransactionType storeAvalaraType) {
        if (storeAvalaraType == null) {
            return null;
        }

        // spring 上下文
        ApplicationContext applicationContext = ApplicationContextHolder.getApplicationContext();

        // 获取对应的处理器
        switch (storeAvalaraType) {
            case CHECKOUT_TRANSACTION:
                return applicationContext.getBean(AvalaraCheckoutTransactionHandler.class);
            case ORDER_CREATE_TRANSACTION:
                return applicationContext.getBean(AvalaraOrderCreateTransactionHandler.class);
            case ORDER_PAYED_TRANSACTION:
                return applicationContext.getBean(AvalaraOrderPayedCommitTransactionHandler.class);
            case ORDER_PAYED_OFFLINE_TRANSACTION:
                return applicationContext.getBean(AvalaraOfflineCreateTransactionHandler.class);
            case ORDER_REFUND_TRANSACTION:
                return applicationContext.getBean(AvalaraOrderRefundTransactionHandler.class);
            case DOWNLOAD_TAX_RATE:
                return applicationContext.getBean(AvalaraDownloadTaxRateHandler.class);
            case RESOLVE_ADDRESS:
                return applicationContext.getBean(AvalaraResolveAddressHandler.class);
            case ORDER_PAYED_AGAIN_TRANSACTION:
                return applicationContext.getBean(AvalaraOrderPayedAgainTransactionHandler.class);
            default:
                return null;
        }
    }
}
