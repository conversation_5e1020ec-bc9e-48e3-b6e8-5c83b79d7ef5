package com.insta360.store.business.product.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.product.model.ProductCommodityOverviewContent;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-09-21
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface ProductCommodityOverviewContentDao extends BaseDao<ProductCommodityOverviewContent> {

    /**
     * 批量保存
     *
     * @param productCommodityOverviewContents
     */
    void saveContentBatch(@Param("productCommodityOverviewContents") List<ProductCommodityOverviewContent> productCommodityOverviewContents);

    /**
     * 批量更新
     *
     * @param updateList
     */
    void updateContentBatch(@Param("updateList") List<ProductCommodityOverviewContent> updateList);
}
