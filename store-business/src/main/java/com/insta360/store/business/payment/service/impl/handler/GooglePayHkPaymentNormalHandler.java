package com.insta360.store.business.payment.service.impl.handler;

import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.payment.lib.checkout.CheckoutConfig;
import com.insta360.store.business.payment.lib.checkout.CheckoutHkConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/29
 */
@Scope("prototype")
@Component
public class GooglePayHkPaymentNormalHandler extends BaseGooglePayPaymentNormalHandler {

    @Autowired
    CheckoutHkConfiguration checkoutHkConfiguration;

    @Override
    protected CheckoutConfig getCheckoutConfigInfo() {
        return checkoutHkConfiguration;
    }

    @Override
    public PaymentChannel getPaymentChannel() {
        return PaymentChannel.google_pay_cko;
    }
}
