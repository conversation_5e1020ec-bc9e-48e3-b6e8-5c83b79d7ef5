package com.insta360.store.business.order.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.order.model.OrderItemBind;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-08-22
 * @Description:
 */
public interface OrderItemBindService extends BaseService<OrderItemBind> {

    /**
     * 获取订单绑定项
     *
     * @param orderId
     * @param orderItemId
     * @return
     */
    OrderItemBind getByOrderItem(Integer orderId, Integer orderItemId);

    /**
     * 获取一个订单所有的绑定项
     *
     * @param orderId
     * @return
     */
    List<OrderItemBind> getOrderBindItems(Integer orderId);

    /**
     * 查询相机是否有绑定服务
     *
     * @param orderItemId
     */
    List<OrderItemBind> getByBelongItemId(Integer orderItemId);

    /**
     * 根据ids查询
     *
     * @param orderIds
     * @return
     */
    List<OrderItemBind> listByOrderIds(List<Integer> orderIds);

}
