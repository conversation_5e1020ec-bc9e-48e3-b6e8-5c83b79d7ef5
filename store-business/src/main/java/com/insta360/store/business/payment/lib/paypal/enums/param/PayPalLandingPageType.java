package com.insta360.store.business.payment.lib.paypal.enums.param;

/**
 * @Author: wbt
 * @Date: 2022/08/17
 * @Description:
 */
public enum PayPalLandingPageType {

    /**
     * 客户会被重定向到一个页面，以登录PayPal并批准支付。
     */
    LOGIN("login", "登录"),

    /**
     * 客户会被重定向到一个页面，输入信用卡或借记卡以及完成购买所需的其他相关账单信息。
     */
    BILLING("billing", "无需登录"),

    /**
     * 根据用户上一次支付类型进行选择。（LOGIN 或者 BILLING）
     */
    NO_PREFERENCE("no_preference", "不做处理");

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述
     */
    private final String detail;

    PayPalLandingPageType(String name, String detail) {
        this.name = name;
        this.detail = detail;
    }

    public String getName() {
        return name;
    }

    public String getDetail() {
        return detail;
    }
}
