package com.insta360.store.business.integration.guanyi.service.impl;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.common.constants.CommonConstant;
import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.integration.guanyi.bo.GuanyiOrderInterceptContextBO;
import com.insta360.store.business.integration.guanyi.constant.GyConstantPool;
import com.insta360.store.business.integration.guanyi.enums.GyOrderInterceptState;
import com.insta360.store.business.integration.guanyi.enums.GyOrderInterceptTypeState;
import com.insta360.store.business.integration.guanyi.enums.GyOrderRefundState;
import com.insta360.store.business.integration.guanyi.lib.GuanyiConfiguration;
import com.insta360.store.business.integration.guanyi.lib.model.GyOrder;
import com.insta360.store.business.integration.guanyi.lib.model.GyOrderDelivery;
import com.insta360.store.business.integration.guanyi.lib.request.*;
import com.insta360.store.business.integration.guanyi.lib.response.GyTradeAddResponse;
import com.insta360.store.business.integration.guanyi.lib.response.GyTradeGetResponse;
import com.insta360.store.business.integration.guanyi.lib.response.GyTradeRefundUpdateResponse;
import com.insta360.store.business.integration.guanyi.lib.response.GyTraderInterceptOrderResponse;
import com.insta360.store.business.integration.guanyi.service.GuanyiSyncService;
import com.insta360.store.business.integration.guanyi.service.impl.helper.GyInterceptOrderStateUpdater;
import com.insta360.store.business.integration.guanyi.service.impl.helper.OrderTransferFactory;
import com.insta360.store.business.integration.jingdong.lib.model.JdOrder;
import com.insta360.store.business.integration.jingdong.lib.model.JdRmaOrder;
import com.insta360.store.business.meta.bo.ToGuanyiOrder;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.model.StoreSdkCallRecord;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.enums.PushPlatformType;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description:
 */
@Service
public class GuanyiSyncServiceImpl implements GuanyiSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(GuanyiSyncServiceImpl.class);

    @Autowired
    GuanyiConfiguration configuration;

    @Autowired
    OrderTransferFactory orderTransferFactory;

    @Autowired
    StoreSdkCallRecordService storeSdkCallRecordService;

    @Autowired
    OrderService orderService;

    /**
     * 同步订单至管易
     *
     * @param order
     * @return
     */
    @Override
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_GY
    )
    public Boolean syncToGuanyi(ToGuanyiOrder order) {
        GyOrder gyOrder = orderTransferFactory.getTransfer(order).transfer();
        GyTradeAddResponse gyTradeAddResponse = addTrade(gyOrder);
        // 处理管易响应
        gyResponseHandler(gyOrder,order, gyTradeAddResponse);
        return gyTradeAddResponse.getSuccess();
    }

    /**
     * 添加推送管易订单
     *
     * @param gyOrder gy订单
     * @return {@link GyTradeAddResponse}
     */
    private GyTradeAddResponse addTrade(GyOrder gyOrder) {
        // 构建管易推送请求
        GyTradeAddRequest request = buildGyTradeAddRequest(gyOrder);

        if (gyOrder.getTradeup_order()) {
            request.setTag_code(GyConstantPool.TAG_CODE);
        }

        String result = null;
        try {
            LOGGER.info("[管易平台API调用]同步商城订单至管易 start... orderNumber:{},request:{}", gyOrder.getPlatform_code(), JSON.toJSONString(request));
            result = request.executePost(configuration);
            LOGGER.info("[管易平台API调用]同步商城订单至管易 end... orderNumber:{},response:{}", gyOrder.getPlatform_code(), result);
        } finally {
            callRecordHandle(StoreSdkCallBusinessType.GUAN_YI_ERP.getType(), StoreSdkCallApiType.GUAN_YI_ERP_ORDER_SYNC.getType(), JSON.toJSONString(request), result, gyOrder.getPlatform_code());
        }
        GyTradeAddResponse response = GyTradeAddResponse.parse(result);
        LOGGER.info("GyTradeAddResponse:" + gyOrder.getPlatform_code() + ",result:" + result);


        return response;
    }

    /**
     * gy响应处理
     *
     * @param gyOrder       gy订单
     * @param response      响应
     * @param toGuanyiOrder 管易统一转换订单
     */
    private void gyResponseHandler(GyOrder gyOrder, ToGuanyiOrder toGuanyiOrder, GyTradeAddResponse response) {
        String orderNumber = gyOrder.getPlatform_code();
        String errorDesc = response.getErrorDesc();

        if (response.getSuccess()) {
            if (orderNumber.startsWith(CommonConstant.ORDER_PREFIX)) {
                this.updatePushPlatform(orderNumber);
            }
            return;
        }

        if (orderNumber.startsWith(CommonConstant.ORDER_PREFIX)) {
            String message = String.format("商城订单:[%s]，推送管易失败,错误原因:%s",orderNumber, errorDesc);
            FeiShuAtUser[] feiShuAtUsers = errorDesc.contains(GyConstantPool.ORDER_TEXT) ? null : FeiShuAtUser.OPERATION_NOTICE;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, feiShuAtUsers);
            return;
        }

        if (toGuanyiOrder instanceof JdRmaOrder){
            FeiShuMessageUtil.storeGeneralMessage(String.format("京东退货单号【%s】，推送管易失败，原因：%s",orderNumber,errorDesc), FeiShuGroupRobot.JdSelfSupport);
            return;
        }
        if (toGuanyiOrder instanceof JdOrder){
            FeiShuMessageUtil.storeGeneralMessage(String.format("京东采购单号【%s】，推送管易失败，原因：%s",orderNumber,errorDesc), FeiShuGroupRobot.JdSelfSupport, FeiShuAtUser.GSL);
            return;
        }
        FeiShuMessageUtil.storeGeneralMessage(String.format("订单【%s】推送管易失败，原因：%s",orderNumber,errorDesc), FeiShuGroupRobot.FmbOrder);

    }

    /**
     * 构建交易添加请求
     *
     * @param gyOrder gy订单
     * @return {@link GyTradeAddRequest}
     */
    private GyTradeAddRequest buildGyTradeAddRequest(GyOrder gyOrder) {
        GyTradeAddRequest request = new GyTradeAddRequest();
        request.setPlatform_code(gyOrder.getPlatform_code());
        request.setShop_code(gyOrder.getShop_code());
        request.setExpress_code(gyOrder.getExpress_code());
        request.setWarehouse_code(gyOrder.getWarehouse_code());
        request.setCurrency_code(gyOrder.getCurrency_code());
        request.setVip_code(gyOrder.getVip_code());
        request.setVip_name(gyOrder.getVip_name());
        request.setVipIdCard(gyOrder.getVipIdCard());
        request.setDeal_datetime(gyOrder.getDealtime());
        request.setReceiver_mobile(gyOrder.getReceiver_mobile());
        request.setReceiver_phone(gyOrder.getReceiver_phone());
        request.setReceiver_name(gyOrder.getReceiver_name());
        request.setReceiver_address(gyOrder.getReceiver_address());
        request.setDetails(gyOrder.getDetails());
        request.setPayments(gyOrder.getPayments());
        request.setInvoices(gyOrder.getInvoices());
        request.setCod_fee(gyOrder.getCod_fee());
        request.setDiscount_fee(gyOrder.getDiscount_fee());
        request.setPost_fee(gyOrder.getPost_fee());
        request.setVipRealName(gyOrder.getVipRealName());
        request.setVipEmail(gyOrder.getVipEmail());
        request.setBuyer_memo(gyOrder.getBuyer_memo());
        request.setSeller_memo(gyOrder.getSeller_memo());
        request.setExtend_memo(gyOrder.getExtend_memo());
        request.setReceiver_zip(gyOrder.getReceiver_zip());
        request.setSeller_memo_late(gyOrder.getSeller_memo_late());
        request.setReceiver_province(gyOrder.getReceiver_province());
        request.setReceiver_district(gyOrder.getReceiver_district());
        request.setReceiver_city(gyOrder.getReceiver_city());
        request.setTag_code(gyOrder.getTag_code());
        return request;
    }

    /**
     * 拉取管易订单发货正向物流
     *
     * @param order
     * @return
     */
    @Override
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_GY
    )
    public GyOrderDelivery syncGuanyiOrderDelivery(ToGuanyiOrder order) {
        GyTradeGetRequest request = new GyTradeGetRequest();
        request.setPlatform_code(order.orderIdInGuanyi());

        LOGGER.info("[管易平台API调用]拉取管易订单发货正向物流 start... orderNumber:{},request:{}", order.orderIdInGuanyi(), JSON.toJSONString(request));
        String result = request.executePost(configuration);
        LOGGER.info("[管易平台API调用]拉取管易订单发货正向物流 end... orderNumber:{},response:{}", order.orderIdInGuanyi(), result);

        GyTradeGetResponse response = GyTradeGetResponse.parse(result);
        if (Objects.nonNull(response) && response.getSuccess()) {
            List<GyOrder> orders = response.getOrders();
            for (GyOrder gyOrder : orders) {
                if (gyOrder.getDelivery_state() == GyOrderDelivery.DELIVERY_NONE) {
                    continue;
                }

                List<GyOrderDelivery> deliveries = gyOrder.getDeliverys();
                if (deliveries != null && deliveries.size() > 0) {
                    GyOrderDelivery gyOrderDelivery = null;
                    for (GyOrderDelivery delivery : deliveries) {
                        if (StringUtil.isNotBlank(delivery.getMail_no())) {
                            gyOrderDelivery = delivery;
                            break;
                        }
                    }
                    return gyOrderDelivery;
                }
            }
        }
        return null;
    }

    /**
     * 拉取管易订单信息
     *
     * @param order
     * @return
     */
    @Override
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_GY
    )
    public List<GyOrder> syncGuanyiPartOrderDelivery(ToGuanyiOrder order) {
        GyTradeGetRequest request = new GyTradeGetRequest();
        request.setPlatform_code(order.orderIdInGuanyi());

        GyTradeGetResponse response = null;
        try {
            LOGGER.info("[管易平台API调用]拉取管易订单发货信息 start... orderNumber:{},request:{}", order.orderIdInGuanyi(), JSON.toJSONString(request));
            String result = request.executePost(configuration);
            LOGGER.info("[管易平台API调用]拉取管易订单发货信息 end... orderNumber:{},response:{}", order.orderIdInGuanyi(), result);
            response = GyTradeGetResponse.parse(result);
        } catch (Exception e) {
            LOGGER.error(String.format("[管易平台API调用]拉取管易订单发货信息异常,orderNumber:%s", order.orderIdInGuanyi()), e);
        }

        if (Objects.nonNull(response) && response.getSuccess()) {
            return response.getOrders();
        }
        return null;
    }

    /**
     * 根据物流单号查询管易订单物流信息
     *
     * @param mailNo
     * @return
     */
    @Override
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_GY
    )
    public String syncGuanyiOrderDeliverys(String mailNo) {
        GyTradeGetDeliverysRequest request = new GyTradeGetDeliverysRequest();
        request.setDelivery(GyConstantPool.ON_DELIVERY);
        request.setMail_no(mailNo);

        String response = null;
        try {
            LOGGER.info("[管易平台API调用]根据物流单号查询管易订单物流信息 start... mailNo:{},request:{}", mailNo, JSON.toJSONString(request));
            response = request.executePost(configuration);
            LOGGER.info("[管易平台API调用]根据物流单号查询管易订单物流信息 end... mailNo:{},response:{}", mailNo, response);
        } catch (Exception e) {
            LOGGER.error(String.format("[管易平台API调用]拉取订单商品物流信息异常,mailNo:%s", mailNo), e);
        }
        return response;
    }

    @Override
    public String syncGuanyiOrderDeliverys(Integer pageNo) {
        GyTradeGetDeliverysRequest request = new GyTradeGetDeliverysRequest();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime stareDate = LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MIN);
        LocalDateTime endDate = LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MAX);
        request.setStart_delivery_date(stareDate.format(formatter));
        request.setEnd_delivery_date(endDate.format(formatter));
        request.setDelivery(GyConstantPool.ON_DELIVERY);
        request.setPage_no(pageNo);
        request.setPage_size(GyConstantPool.PAGE_SIZE);
        return request.executePost(configuration);
    }

    /**
     * 通知管易订单进行货物拦截或取消拦截
     *
     * @param order
     * @param reason
     * @param rmaState
     * @return
     */
    @Override
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_GY
    )
    public boolean syncInterceptGuanyiOrder(Order order, String reason, Integer rmaState) {
        GyTradeInterceptOrderRequest request = new GyTradeInterceptOrderRequest();

        request.setPlatform_code(order.orderIdInGuanyi());

        // 根据订单状态判断是否是拦截
        rmaState = rmaState == 0 ? GyOrderInterceptState.intercept.getState() : GyOrderInterceptState.cancel_intercept.getState();

        request.setOperate_type(rmaState);
        request.setTrade_hold_code(GyOrderInterceptTypeState.refund.getState());
        request.setTrade_hold_reason(reason);

        String result = null;
        try {
            LOGGER.info("[管易平台API调用]通知管易订单进行货物拦截或取消拦截 start... orderNumber:{},request:{}", order.getOrderNumber(), JSON.toJSONString(request));
            result = request.executePost(configuration);
            LOGGER.info("[管易平台API调用]通知管易订单进行货物拦截或取消拦截 end... orderNumber:{},response:{}", order.getOrderNumber(), result);
        } finally {
            callRecordHandle(StoreSdkCallBusinessType.GUAN_YI_ERP.getType(), StoreSdkCallApiType.GUAN_YI_ERP_REFUND_CARGO_INTERCEPTION.getType(), JSON.toJSONString(request), result, order.orderIdInGuanyi());
        }

        return StringUtils.isNotBlank(result) && GyTraderInterceptOrderResponse.parse(result).getSuccess();
    }

    @Override
    public Boolean syncInterceptGuanyiOrder(GuanyiOrderInterceptContextBO contextParam) {
        if (Objects.isNull(contextParam)) {
            return false;
        }
        GyTradeInterceptOrderRequest request = new GyTradeInterceptOrderRequest();
        request.setPlatform_code(contextParam.getOrderNumber());
        request.setOperate_type(contextParam.getInterceptState());
        request.setTrade_hold_code(contextParam.getInterceptType());
        request.setTrade_hold_reason(contextParam.getInterceptReason());

        String result = null;
        try {
            LOGGER.info("[管易平台API调用]通知管易订单进行货物拦截或取消拦截 start... orderNumber:{},request:{}", contextParam.getOrderNumber(), JSON.toJSONString(request));
            result = request.executePost(configuration);
            LOGGER.info("[管易平台API调用]通知管易订单进行货物拦截或取消拦截 end... orderNumber:{},response:{}", contextParam.getOrderNumber(), result);
        } finally {
            callRecordHandle(StoreSdkCallBusinessType.GUAN_YI_ERP.getType(), StoreSdkCallApiType.GUAN_YI_ERP_REFUND_CARGO_INTERCEPTION.getType(), JSON.toJSONString(request), result, contextParam.getOrderNumber());
        }

        return StringUtils.isNotBlank(result) && GyTraderInterceptOrderResponse.parse(result).getSuccess();
    }

    /**
     * 拒付订单通知管易进行货物拦截
     *
     * @param order
     */
    @Override
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_GY
    )
    public void syncInterceptGuanyiOrder(Order order) {
        GyTradeInterceptOrderRequest request = new GyTradeInterceptOrderRequest();

        request.setPlatform_code(order.orderIdInGuanyi());
        request.setOperate_type(GyOrderInterceptState.intercept.getState());
        request.setTrade_hold_code(GyOrderInterceptTypeState.charge_back.getState());
        request.setTrade_hold_reason(GyConstantPool.REASON);

        LOGGER.info("[管易平台API调用]拒付订单通知管易进行货物拦截 start... orderNumber:{},request:{}", order.getOrderNumber(), JSON.toJSONString(request));
        String result = request.executePost(configuration);
        LOGGER.info("[管易平台API调用]拒付订单通知管易进行货物拦截 end... orderNumber:{},response:{}", order.getOrderNumber(), result);

        if (StringUtils.isBlank(result) || !GyTraderInterceptOrderResponse.parse(result).getSuccess()) {
            String message = "拒付订单拦截失败。订单号：" + order.getOrderNumber() + "，订单状态：" + order.orderState().getNameZh() + "，请及时拦截处理！";
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.LZJ);
        }
    }

    /**
     * 通知管易修改订单商品售后状态
     *
     * @param order
     * @param orderItem
     * @param rmaState
     */
    @Override
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_GY
    )
    public void syncRefundUpdateGuanyiOrder(Order order, OrderItem orderItem, Integer rmaState) {
        // 子项为已发货状态则不同步
        if (OrderItemState.on_delivery.equals(OrderItemState.parse(orderItem.getDeliveryState()))) {
            return;
        }
        GyTradeRefundUpdateRequest request = new GyTradeRefundUpdateRequest();
        request.setTid(order.orderIdInGuanyi());
        request.setOid(orderItem.getId().toString());
        request.setRefund_state(GyInterceptOrderStateUpdater.getInterceptState(rmaState));

        String result = null;
        try {
            LOGGER.info("[管易平台API调用]通知管易修改订单商品售后状态 start... orderNumber:{},request:{}", order.getOrderNumber(), JSON.toJSONString(request));
            result = request.executePost(configuration);
            LOGGER.info("[管易平台API调用]通知管易修改订单商品售后状态 end... orderNumber:{},response:{}", order.getOrderNumber(), result);
        } finally {
            callRecordHandle(StoreSdkCallBusinessType.GUAN_YI_ERP.getType(), StoreSdkCallApiType.GUAN_YI_ERP_REFUND.getType(), JSON.toJSONString(request), result, order.orderIdInGuanyi());
        }

        GyTradeRefundUpdateResponse response = GyTradeRefundUpdateResponse.parse(result);
        if (Objects.isNull(response) || !response.getSuccess()) {
            String message = "订单：" + order.orderIdInGuanyi() + "，状态修改失败（管易），" +
                    response.getErrorDesc() + "，修改状态：" + GyOrderRefundState.param(request.getRefund_state()).getName();
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.OPERATION_NOTICE);
        }
    }

    @Override
    public GyTradeGetResponse checkGyOrder(String orderNumber) {
        GyTradeGetRequest tradeGetRequest = new GyTradeGetRequest();
        tradeGetRequest.setPlatform_code(orderNumber);
        String result = tradeGetRequest.executePost(configuration);
        return GyTradeGetResponse.parse(result);
    }

    /**
     * 商城-管易调用记录处理
     *
     * @param businessType
     * @param apiType
     * @param requestJson
     * @param responseJson
     * @param orderNumber
     */
    private void callRecordHandle(Integer businessType, Integer apiType, String requestJson, String responseJson, String orderNumber) {
        if (businessType == null || apiType == null || StringUtils.isBlank(orderNumber)) {
            return;
        }
        StoreSdkCallRecord record = new StoreSdkCallRecord();
        record.setBusinessType(businessType);
        record.setApiType(apiType);
        record.setRequestJson(requestJson);
        record.setResponseJson(responseJson);
        record.setCreateTime(LocalDateTime.now());
        record.setModifyTime(LocalDateTime.now());
        record.setOrderNumber(orderNumber);
        record.setSubKey(orderNumber);

        storeSdkCallRecordService.save(record);
    }

    private void updatePushPlatform(String orderNumber) {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            return;
        }

        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setPushPlatform(PushPlatformType.GUAN_YI.getType());
        orderService.updateById(order);
    }
}
