package com.insta360.store.business.commodity.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-05-12
 * @Description: 
 */
@TableName("product_commodity_sku_code_detail")
public class ProductCommoditySkuCodeDetail extends BaseModel<ProductCommoditySkuCodeDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 组合料号sku_id
     */
    private Long parentSkuId;

    /**
     * 组合商品料号套餐ID
     */
    private Integer parentCommodityId;

    /**
     * 子料号sku_id
     */
    private Long childSkuId;

    /**
     * 子料号套餐ID
     */
    private Integer childCommodityId;

    /**
     * 标准件子料号数量
     */
    private Integer quantity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    public ProductCommoditySkuCodeDetail() {
    }

    public ProductCommoditySkuCodeDetail(Long parentSkuId, Integer parentCommodityId, Long childSkuId, Integer childCommodityId, Integer quantity) {
        this.parentSkuId = parentSkuId;
        this.parentCommodityId = parentCommodityId;
        this.childSkuId = childSkuId;
        this.childCommodityId = childCommodityId;
        this.quantity = quantity;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentSkuId() {
        return parentSkuId;
    }

    public void setParentSkuId(Long parentSkuId) {
        this.parentSkuId = parentSkuId;
    }

    public Integer getParentCommodityId() {
        return parentCommodityId;
    }

    public void setParentCommodityId(Integer parentCommodityId) {
        this.parentCommodityId = parentCommodityId;
    }

    public Long getChildSkuId() {
        return childSkuId;
    }

    public void setChildSkuId(Long childSkuId) {
        this.childSkuId = childSkuId;
    }

    public Integer getChildCommodityId() {
        return childCommodityId;
    }

    public void setChildCommodityId(Integer childCommodityId) {
        this.childCommodityId = childCommodityId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "ProductCommoditySkuCodeDetail{" +
        "id=" + id +
        ", parentSkuId=" + parentSkuId +
        ", parentCommodityId=" + parentCommodityId +
        ", childSkuId=" + childSkuId +
        ", childCommodityId=" + childCommodityId +
        ", quantity=" + quantity +
        ", createTime=" + createTime +
        ", modifyTime=" + modifyTime +
        "}";
    }
}