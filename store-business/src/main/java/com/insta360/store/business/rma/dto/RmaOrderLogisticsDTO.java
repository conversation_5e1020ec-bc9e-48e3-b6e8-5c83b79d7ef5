package com.insta360.store.business.rma.dto;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 售后单物流DTO
 * @Date 2024/2/28
 */
public class RmaOrderLogisticsDTO implements Serializable {

    /**
     * 售后单号
     */
    @NotBlank(message = "rmaNumber not null")
    private String rmaNumber;

    /**
     * 客户寄回快递物流公司
     */
    @NotBlank(message = "expressCompany not null")
    private String customerReturnExpressCompany;

    /**
     * 客户寄回快递物流单号
     */
    @NotBlank(message = "expressNumber not null")
    private String customerReturnExpressNumber;

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public String getCustomerReturnExpressCompany() {
        return customerReturnExpressCompany;
    }

    public void setCustomerReturnExpressCompany(String customerReturnExpressCompany) {
        this.customerReturnExpressCompany = customerReturnExpressCompany;
    }

    public String getCustomerReturnExpressNumber() {
        return customerReturnExpressNumber;
    }

    public void setCustomerReturnExpressNumber(String customerReturnExpressNumber) {
        this.customerReturnExpressNumber = customerReturnExpressNumber;
    }

    @Override
    public String toString() {
        return "RmaOrderLogisticsDTO{" +
                "rmaNumber='" + rmaNumber + '\'' +
                ", customerReturnExpressCompany='" + customerReturnExpressCompany + '\'' +
                ", customerReturnExpressNumber='" + customerReturnExpressNumber + '\'' +
                '}';
    }
}
