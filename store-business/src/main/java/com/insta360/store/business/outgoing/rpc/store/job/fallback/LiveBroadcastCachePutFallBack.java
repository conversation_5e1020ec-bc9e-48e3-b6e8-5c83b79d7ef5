package com.insta360.store.business.outgoing.rpc.store.job.fallback;

import com.insta360.store.business.outgoing.rpc.store.job.LiveBroadcastCachePutService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/15
 */
@Component
public class LiveBroadcast<PERSON>achePutFallBack implements LiveBroadcastCachePutService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LiveBroadcastCachePutFallBack.class);

    @Override
    public void listLiveBroadcastPage() {
        LOGGER.error(String.format("store-service调用失败。路径：/rpc/store/service/cacheput/meta/lb/listLiveBroadcastPage。"));
    }
}
