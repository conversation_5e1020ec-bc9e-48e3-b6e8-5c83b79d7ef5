package com.insta360.store.business.outgoing.mq.cloud.sender;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.cloud.dto.CloudSubscribeRenewMqDTO;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2024/08/13
 * @Description: 发送续费订单扣款消息
 */
@Component
public class StoreCloudRenewOrderPaymentMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudProductSubscribeCreateMq.class);

    // 默认延迟时间
    private static final long defaultDelayTime = 0;

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_renew_order_payment, messageType = MessageSenderType.time)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * 发送续费订单扣款mq
     *
     * @param order
     * @param delayTime
     */
    public void sendRenewOrderPaymentMessage(Order order, Long delayTime) {
        LOGGER.info(String.format("续费扣款订单发送开始。。。。 订单信息{%s}，delay time{%s}", order, delayTime));
        if (Objects.isNull(order)) {
            return;
        }

        // 订单信息check
        if (order.isGuestOrder() || order.isRepairOrder() || !order.getCloudSubscribeMark()
                || !PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            LOGGER.error(String.format("续费扣款订单Mq发送异常，订单信息{%s}", order));
            FeiShuMessageUtil.storeGeneralMessage(String.format("续费扣款订单Mq发送异常，订单号{%s}", order.getOrderNumber())
                    , FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return;
        }

        CloudSubscribeRenewMqDTO subscribeRenewMqDto = new CloudSubscribeRenewMqDTO();
        subscribeRenewMqDto.setOrder(order);

        String messageId = rocketTcpMessageSender.sendDelayMessage(JSON.toJSONString(subscribeRenewMqDto), Objects.isNull(delayTime) ? defaultDelayTime : delayTime);
        // 日志记录消息发送结束
        LOGGER.info("续订订单扣款延迟消息发送结束. order: {}, delayTime: {}, messageId: {}", JSON.toJSON(order), delayTime, messageId);

        // 检查消息ID是否为空
        MqUtils.isBlankMessageIdHandle(messageId, this, subscribeRenewMqDto);
        if (StringUtils.isBlank(messageId)) {
            // 如果消息发送失败，则通过FeiShu发送报警消息
            FeiShuMessageUtil.storeGeneralMessage(String.format("续订订单扣款延迟消息发送失败... 订单号{%s}", order.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
    }
}
