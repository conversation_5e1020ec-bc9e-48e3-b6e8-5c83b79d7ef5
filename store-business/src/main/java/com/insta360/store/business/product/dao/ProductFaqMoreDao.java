package com.insta360.store.business.product.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.product.model.ProductFaqMore;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-08-07
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface ProductFaqMoreDao extends BaseDao<ProductFaqMore> {

}
