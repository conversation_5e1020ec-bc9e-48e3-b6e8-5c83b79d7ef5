package com.insta360.store.business.integration.google.helper;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.compass.libs.aliyun.oss.enums.EndpointEnum;
import com.insta360.compass.libs.aliyun.oss.enums.ModuleEnum;
import com.insta360.store.business.admin.order.enums.OrderExportType;
import com.insta360.store.business.admin.order.model.OrderExportInfo;
import com.insta360.store.business.admin.order.service.OrderExportInfoService;
import com.insta360.store.business.admin.order.service.impl.handler.constant.OrderExportConstant;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.enums.StateEnum;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.commodity.service.CommodityMetaService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.gateway.GatewayConfiguration;
import com.insta360.store.business.discount.utils.MathUtil;
import com.insta360.store.business.integration.google.dto.GoogleCommodityInsertQueryDTO;
import com.insta360.store.business.integration.google.enums.GoogleAvailablityType;
import com.insta360.store.business.integration.google.enums.GoogleExportType;
import com.insta360.store.business.integration.google.model.*;
import com.insta360.store.business.integration.google.service.GoogleCommodityService;
import com.insta360.store.business.meta.model.MetaShippingCost;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.MetaShippingCostService;
import com.insta360.store.business.order.dto.OrderExportDTO;
import com.insta360.store.business.product.enums.ProductCategoryFinalType;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.service.ProductAdapterTypeService;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import com.insta360.store.business.trade.model.ShippingFeeConfig;
import com.insta360.store.business.trade.service.ShippingFeeConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/10/29
 */
@Component
public class GoogleCommodityHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(GoogleCommodityHelper.class);

    @Autowired
    OSSService ossService;

    @Autowired
    GatewayConfiguration gatewayConfiguration;

    @Autowired
    CommodityMetaService commodityMetaService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Autowired
    ShippingFeeConfigService shippingFeeConfigService;

    @Autowired
    MetaShippingCostService metaShippingCostService;

    @Autowired
    ProductAdapterTypeService productAdapterTypeService;

    @Autowired
    private CommodityService commodityService;

    @Autowired
    private GoogleCommodityService googleCommodityService;

    @Autowired
    private OrderExportInfoService orderExportInfoService;

    /**
     * 查询商品套餐销售状态数据
     *
     * @return
     */
    public List<GoogleCommoditySaleStateExportData> listCommoditySaleStateExportData(Integer state) {
        List<GoogleCommodity> commodityList = googleCommodityService.getGoogleCommoditySaleState(state);
        if (CollectionUtils.isEmpty(commodityList)) {
            return null;
        }

        return commodityList.stream().map(googleCommodity -> {
            GoogleCommoditySaleStateExportData exportData = new GoogleCommoditySaleStateExportData();
            BeanUtils.copyProperties(googleCommodity, exportData);
            exportData.setCommodityIdAndCountry(googleCommodity.getCommodityId() + googleCommodity.getCountry());
            if (googleCommodity.getSaleState() != null) {
                if (googleCommodity.getSaleState().intValue() == SaleState.normal.getCode()) {
                    exportData.setAvailablity(GoogleAvailablityType.normal.getValue());
                }
                if (googleCommodity.getSaleState().intValue() == SaleState.out_of_stock.getCode()) {
                    exportData.setAvailablity(GoogleAvailablityType.out_of_stock.getValue());
                }
            }
            return exportData;
        }).collect(Collectors.toList());
    }

    /**
     * 查询商品套餐销售价格变化数据
     *
     * @return
     */
    public List<GoogleCommoditySalePriceExportData> listCommoditySalePriceExportData(Integer state) {
        List<GoogleCommodity> commodityList = googleCommodityService.getGoogleCommoditySalePrice(state);
        if (CollectionUtils.isEmpty(commodityList)) {
            return null;
        }

        return commodityList.stream().map(googleCommodity -> {
            GoogleCommoditySalePriceExportData exportData = new GoogleCommoditySalePriceExportData();
            BeanUtils.copyProperties(googleCommodity, exportData);
            exportData.setCommodityIdAndCountry(googleCommodity.getCommodityId() + googleCommodity.getCountry());
            exportData.setSalePriceAndCurrency(googleCommodity.getAmount() + " " + googleCommodity.getCurrency());
            return exportData;
        }).collect(Collectors.toList());
    }

    /**
     * 数据导出excel 并上传OSS
     *
     * @param state
     * @param account
     * @param exportType
     */
    public void doExportCommodityData(Integer state, String account, OrderExportType exportType) {
        OrderExportInfo exportInfo = createOrderExportInfo(account, exportType);
        try {
            String ossUrl = createOSSFile(state, exportType);
            if (StringUtils.isBlank(ossUrl)) {
                throw new InstaException(-1, "OSS文件url为空！");
            }
            exportInfo.setUrl(ossUrl);
        } catch (Exception e) {
            LOGGER.error("Google商品套餐数据导出异常！exportType:{}", exportType.getExportType(), e);
            exportInfo.setState(false);
            FeiShuMessageUtil.storeGeneralMessage("Google商品套餐数据导出失败。原因：" + e.getMessage() + "，类型：" + exportType.name() + "，导出人：" + account, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW);
        } finally {
            orderExportInfoService.save(exportInfo);
        }
    }

    /**
     * 获取Google商品套餐-新增场景数据
     *
     * @param googleCommodityInsertQueryDTO
     * @return
     */
    public List<GoogleCommodityInsertData> getGoogleCommodityInsertData(GoogleCommodityInsertQueryDTO googleCommodityInsertQueryDTO) {
        if (Objects.isNull(googleCommodityInsertQueryDTO)) {
            return null;
        }

        List<Commodity> commodityList = commodityService.listCommodityByTime(googleCommodityInsertQueryDTO);
        if (CollectionUtils.isEmpty(commodityList)) {
            throw new InstaException(-1, "无启用的套餐");
        }

        //获取新增的参套ID
        List<Integer> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());

        //查询出套餐对应的地区、原价、现价、币种、销售状态
        List<GoogleCommodity> commodityPriceList = googleCommodityService.getCommodityPriceById(commodityIdList);
        //查询出套餐适配语言、名称，对应产品名称、类型、分类
        List<GoogleCommodity> commodityInfoList = googleCommodityService.getGoogleCommodityInfoById(commodityIdList);
        //查询出套餐的最大运输时间、库存、图片链接、海关编码
        List<GoogleCommodity> commodityDetailList = googleCommodityService.getCommodityDetailById(commodityIdList);

        List<MetaShippingCost> metaShippingCostList = metaShippingCostService.listAll();
        Map<InstaCountry, MetaShippingCost> metaShippingCostMap = metaShippingCostList.stream().collect(Collectors.toMap(MetaShippingCost::country, Function.identity()));

        // 获取套餐报关信息
        List<CommodityMeta> commodityMetaList = commodityMetaService.listCommodityMetaByCommodityIds(commodityIdList);
        Map<Integer, CommodityMeta> commodityMetaMap = commodityMetaList.stream().collect(Collectors.toMap(CommodityMeta::getCommodityId, Function.identity()));

        if (CollectionUtils.isEmpty(commodityPriceList) || CollectionUtils.isEmpty(commodityInfoList) || CollectionUtils.isEmpty(commodityDetailList)) {
            throw new InstaException(-1, "套餐数据不完整");
        }

        Map<Integer, List<GoogleCommodity>> commodityPriceMap = commodityPriceList.stream().collect(
                Collectors.toMap(GoogleCommodity::getCommodityId, Lists::newArrayList,
                        (List<GoogleCommodity> newValueList, List<GoogleCommodity> oldValueList) -> {
                            oldValueList.addAll(newValueList);
                            return oldValueList;
                        }));

        Map<Integer, GoogleCommodity> commodityDetailMap = commodityDetailList.stream().collect(Collectors.toMap(GoogleCommodity::getCommodityId, g -> g));

        Map<String, GoogleCommodity> commodityInfoMap = new HashMap<>();
        for (GoogleCommodity googleCommodity : commodityInfoList) {
            String key = googleCommodity.getCommodityId() + googleCommodity.getLanguage();
            commodityInfoMap.putIfAbsent(key, googleCommodity);
        }

        //excel数据
        List<GoogleCommodityInsertData> excelDataList = Lists.newArrayList();
        for (Integer key : commodityPriceMap.keySet()) {
            //包含最大运输时间、库存、图片链接、海关编码
            GoogleCommodity commodity = commodityDetailMap.get(key);
            if (commodity == null) {
                continue;
            }
            String gtin = "";
            if (StringUtils.isNotBlank(commodity.getCustomCode())) {
                JSONObject jsonObject = JSONObject.parseObject(commodity.getCustomCode());
                gtin = jsonObject.containsKey("US") ? jsonObject.getString("US") : null;
            }

            List<GoogleCommodity> googleCommodityList = commodityPriceMap.get(key);
            for (GoogleCommodity googleCommodity : googleCommodityList) {
                //忽略hk-英文
                if ("HK".equals(googleCommodity.getCountry()) && InstaLanguage.en_US.name().equals(googleCommodity.getLanguage())) {
                    continue;
                }

                //套餐明细
                String tempKey = googleCommodity.getCommodityId() + googleCommodity.getLanguage();
                GoogleCommodity commodityInfo = commodityInfoMap.get(tempKey);
                if (commodityInfo == null) {
                    continue;
                }
                String urlKey = commodityInfo.getUrlKey().trim();

                GoogleCommodityInsertData insertData = new GoogleCommodityInsertData();
                insertData.setCommodityId(googleCommodity.getCommodityId());
                insertData.setAvailability(saleStateConversion(googleCommodity.getSaleState()));
                //设置套餐价格
                if (googleCommodity.getAmount() < googleCommodity.getOriginAmount()) {
                    insertData.setSalePrice(googleCommodity.getAmount() + " " + googleCommodity.getCurrency());
                    insertData.setPriceAndCurrency(googleCommodity.getOriginAmount() + " " + googleCommodity.getCurrency());
                } else {
                    insertData.setPriceAndCurrency(googleCommodity.getOriginAmount() + " " + googleCommodity.getCurrency());
                }
                //设置英文固定产品名
                if (!InstaLanguage.en_US.name().equals(googleCommodity.getLanguage())) {
                    tempKey = googleCommodity.getCommodityId() + InstaLanguage.en_US.name();
                    GoogleCommodity commodityEnUs = commodityInfoMap.get(tempKey);
                    if (commodityEnUs != null) {
                        insertData.setNameId(commodityEnUs.getProductName());
                    }
                } else {
                    insertData.setNameId(commodityInfo.getProductName());
                }

                insertData.setCountry(googleCommodity.getCountry());
                insertData.setUid(googleCommodity.getCommodityId() + googleCommodity.getCountry());
                insertData.setGtin(gtin);
                insertData.setMaxHandlingTime(commodity.getEstimateDays());

                // 具体运费 根据按重计费中运算
                CommodityMeta commodityMeta = commodityMetaMap.get(googleCommodity.getCommodityId());
                if (commodityMeta != null) {
                    BigDecimal weight = MathUtil.getBigDecimal(commodityMeta.getWeight());
                    InstaCountry country = InstaCountry.parse(googleCommodity.getCountry());
                    ShippingFeeConfig shippingFeeByWeight = shippingFeeConfigService.getShippingFeeByWeight(weight, country);
                    BigDecimal shipping = Optional.ofNullable(shippingFeeByWeight).map(ShippingFeeConfig::getShipping).orElse(BigDecimal.ZERO);

                    // 获取比对价格
                    BigDecimal amount = MathUtil.getBigDecimal(Optional.ofNullable(googleCommodity.getAmount()).orElse(googleCommodity.getOriginAmount()));
                    MetaShippingCost metaShippingCost = metaShippingCostMap.get(country);
                    BigDecimal freeLimit = Optional.ofNullable(metaShippingCost).map(MetaShippingCost::getFreeLimit).map(MathUtil::getBigDecimal).orElse(BigDecimal.ZERO);
                    if (amount.compareTo(freeLimit) >= 0) {
                        shipping = BigDecimal.ZERO;
                    }
                    insertData.setShipping(googleCommodity.getCountry() + ":::" + shipping + " " + googleCommodity.getCurrency());
                }
                //设置图片链接
                insertData.setImageLink(commodity.getUrl());
                if (StringUtils.isNotBlank(commodity.getUrlS())) {
                    insertData.setAdditionalImageLink1(commodity.getUrlS());
                }

                //设置title、desc等基础信息
                String productName = commodityInfo.getProductName();
                String commodityName = commodityInfo.getCommodityName();
                if (StringUtils.isNotBlank(productName) && StringUtils.isNotBlank(commodityName)) {
                    if (productName.trim().equals(commodityName.trim())) {
                        insertData.setTitle(productName);
                    } else {
                        insertData.setTitle(productName + " " + commodityName);
                    }
                }

                insertData.setDescription(getText(commodityInfo.getProductIntroduction()) + getText(commodityInfo.getIntroduction()) + getText(commodityInfo.getProductDesc()) + getText(commodityInfo.getDescription()));

                String categoryKey = commodityInfo.getCategoryKey();
                ProductCategoryMainType categoryMainType = productCategoryHelper.getCategoryMainByKey(categoryKey);
                ProductCategoryFinalType categoryFinalType = ProductCategoryFinalType.parse(categoryKey);

                if (ProductCategoryFinalType.CF_360_3D_CAMERA.equals(categoryFinalType)) {
                    insertData.setProductType("360 Camera");
                }

                if (ProductCategoryFinalType.CF_ACTION_CAMERA.equals(categoryFinalType)) {
                    insertData.setProductType("Action Camera");
                }

                if (ProductCategoryFinalType.CF_PROFESSION_CAMERA.equals(categoryFinalType)) {
                    insertData.setProductType("Profession Camera");
                }

                if (ProductCategoryMainType.CM_ACCESSORY.equals(categoryMainType)) {
                    insertData.setProductType("Camera Accessory");
                }

                if (ProductCategoryMainType.isCameraType(categoryMainType)) {
                    insertData.setGoogleProductCategory("Cameras & Optics > Cameras");
                    insertData.setShippingLabel("cameras");
                    if (ProductCategoryFinalType.isProfessionCamera(categoryFinalType)) {
                        insertData.setShippingHeight("20 cm");
                        insertData.setShippingWeight("5.75 kg");
                        insertData.setShippingWidth("30 cm");
                        insertData.setShippingLength("20 cm");
                    } else {
                        insertData.setShippingHeight("15 cm");
                        insertData.setShippingWeight("1 kg");
                        insertData.setShippingWidth("18 cm");
                        insertData.setShippingLength("15 cm");
                    }

                    //标签
                    if ("go".equals(urlKey)) {
                        insertData.setCustomLabel1("go");
                    }
                    if ("go_2".equals(urlKey)) {
                        insertData.setCustomLabel1("go2");
                    }
                    if ("one_r".equals(urlKey)) {
                        insertData.setCustomLabel1("oner");
                    }
                    if ("one_x".equals(urlKey)) {
                        insertData.setCustomLabel1("onex");
                    }
                    if ("one_x2".equals(urlKey)) {
                        insertData.setCustomLabel1("onex2");
                    }
                    if ("Pro_2".equals(urlKey)) {
                        insertData.setCustomLabel1("pro2");
                    }
                    if ("titan".equals(urlKey)) {
                        insertData.setCustomLabel1("titan");
                    }
                }
                if (ProductCategoryMainType.CM_ACCESSORY.equals(categoryMainType)) {
                    insertData.setGoogleProductCategory("Cameras & Optics > Camera & Optic Accessories > Camera Parts & Accessories");
                    insertData.setShippingLabel("accessories");

                    insertData.setShippingHeight("10 cm");
                    insertData.setShippingWeight("0.6 kg");
                    insertData.setShippingWidth("10 cm");
                    insertData.setShippingLength("10 cm");

                    insertData.setCustomLabel1("accessory");
                }

                String link = gatewayConfiguration.getStoreUrl() + "/" + googleCommodity.getCountry().toLowerCase() + "/product/" + urlKey + "?c=" + googleCommodity.getCommodityId() + "&freelist=gmc";
                insertData.setLink(link);
                insertData.setMobileLink(link);
                insertData.setMpn(googleCommodity.getCode());

                excelDataList.add(insertData);


            }
        }

        return excelDataList;
    }

    /**
     * 获取Google商品套餐各地区上下架信息
     *
     * @param orderExportParam
     * @return
     */
    public List<GoogleUpShelfCommodityData> getGoogleUpShelfCommodityData(OrderExportDTO orderExportParam) {
        Integer state = orderExportParam.getTripleState();
        List<GoogleUpShelfCommodity> commodityList = googleCommodityService.getUpShelfCommodity(state);
        if (CollectionUtils.isEmpty(commodityList)) {
            return Lists.newArrayList();
        }

        // commodityPriceMap key:commodityId value:price
        List<Integer> commodityIdList = commodityList.stream().map(GoogleUpShelfCommodity::getCommodityId).collect(Collectors.toList());
        Map<Integer, Float> commodityPriceMap = commodityPriceService.listPriceByIds(commodityIdList);

        // 配件套餐适配机型map
        Map<Integer, List<Integer>> accessoryCompatibilityMap = productAdapterTypeService.listAccessoryCompatibility();
        List<Integer> compatibilityList = orderExportParam.getAccessoryCompatibilityList();

        List<GoogleUpShelfCommodityData> googleUpShelfCommodityDataList = Lists.newArrayList();
        for (GoogleUpShelfCommodity commodity : commodityList) {
            GoogleUpShelfCommodityData shelfCommodityData = new GoogleUpShelfCommodityData();

            // 是否全为旧适配机型
            Integer commodityId = commodity.getCommodityId();
            Boolean accessoryCompatibility = getAccessoryCompatibility(accessoryCompatibilityMap, compatibilityList, shelfCommodityData, commodityId);

            buildGoogleUpShelfCommodityData(commodity, shelfCommodityData, accessoryCompatibility);

            // set price
            setPrice(commodityPriceMap, shelfCommodityData, commodityId);
            googleUpShelfCommodityDataList.add(shelfCommodityData);
        }

        return googleUpShelfCommodityDataList;
    }

    /**
     * set price
     *
     * @param commodityPriceMap
     * @param shelfCommodityData
     * @param commodityId
     */
    private void setPrice(Map<Integer, Float> commodityPriceMap, GoogleUpShelfCommodityData shelfCommodityData, Integer commodityId) {
        Float price = commodityPriceMap.get(commodityId);
        String priceString = null;
        if (price != null) {
            priceString = price.toString();
        }
        shelfCommodityData.setCommodityPrice(priceString);
    }

    /**
     * 新旧适配机型判断
     *
     * @param accessoryCompatibilityMap
     * @param compatibilityList
     * @param shelfCommodityData
     * @param commodityId
     */
    private Boolean getAccessoryCompatibility(Map<Integer, List<Integer>> accessoryCompatibilityMap, List<Integer> compatibilityList, GoogleUpShelfCommodityData shelfCommodityData, Integer commodityId) {
        // 配件实际的适配机型
        List<Integer> dbCompatibilityList = accessoryCompatibilityMap.get(commodityId);

        if (CollectionUtils.isEmpty(dbCompatibilityList)) {
            return true;
        }
        // 如果页面配置的旧机型是空的直接返回
        if (CollectionUtils.isEmpty(compatibilityList)) {
            return true;
        }
        // 旧机型
        if (compatibilityList.containsAll(dbCompatibilityList)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 构建Google商品套餐各地区上下架信息
     *
     * @param commodity
     * @param shelfCommodityData
     */
    private void buildGoogleUpShelfCommodityData(GoogleUpShelfCommodity commodity, GoogleUpShelfCommodityData shelfCommodityData, Boolean accessoryCompatibility) {
        shelfCommodityData.setCommodityId(commodity.getCommodityId());
        shelfCommodityData.setCommodityName(commodity.getCommodityName());
        shelfCommodityData.setProductName(commodity.getProductName());
        // 设置国家地区销售状态 产品/套餐启用设置为1
        if (commodity.getProductState() == StateEnum.ENABLE.getCode() && commodity.getCommodityState() == StateEnum.ENABLE.getCode() && accessoryCompatibility) {
            String areaStr = commodity.getArea();
            if (StringUtils.isNotBlank(areaStr)) {
                String[] areaList = areaStr.split(",");
                for (String area : areaList) {
                    buildCountry(shelfCommodityData, area);
                }
            }
        }
    }

    /**
     * 匹配国家
     *
     * @param shelfCommodityData
     * @param area
     */
    private void buildCountry(GoogleUpShelfCommodityData shelfCommodityData, String area) {
        InstaCountry country = InstaCountry.parse(area);
        switch (country) {
            case AD:
                shelfCommodityData.setAd(StateEnum.ENABLE.getCode());
                break;
            case AE:
                shelfCommodityData.setAe(StateEnum.ENABLE.getCode());
                break;
            case AT:
                shelfCommodityData.setAt(StateEnum.ENABLE.getCode());
                break;
            case AU:
                shelfCommodityData.setAu(StateEnum.ENABLE.getCode());
                break;
            case BE:
                shelfCommodityData.setBe(StateEnum.ENABLE.getCode());
                break;
            case BG:
                shelfCommodityData.setBg(StateEnum.ENABLE.getCode());
                break;
            case BN:
                shelfCommodityData.setBn(StateEnum.ENABLE.getCode());
                break;
            case BR:
                shelfCommodityData.setBr(StateEnum.ENABLE.getCode());
                break;
            case CA:
                shelfCommodityData.setCa(StateEnum.ENABLE.getCode());
                break;
            case CH:
                shelfCommodityData.setCh(StateEnum.ENABLE.getCode());
                break;
            case CN:
                shelfCommodityData.setCn(StateEnum.ENABLE.getCode());
                break;
            case CY:
                shelfCommodityData.setCy(StateEnum.ENABLE.getCode());
                break;
            case CZ:
                shelfCommodityData.setCz(StateEnum.ENABLE.getCode());
                break;
            case DE:
                shelfCommodityData.setDe(StateEnum.ENABLE.getCode());
                break;
            case DK:
                shelfCommodityData.setDk(StateEnum.ENABLE.getCode());
                break;
            case EE:
                shelfCommodityData.setEe(StateEnum.ENABLE.getCode());
                break;
            case ES:
                shelfCommodityData.setEs(StateEnum.ENABLE.getCode());
                break;
            case FI:
                shelfCommodityData.setFi(StateEnum.ENABLE.getCode());
                break;
            case FR:
                shelfCommodityData.setFr(StateEnum.ENABLE.getCode());
                break;
            case GB:
                shelfCommodityData.setGb(StateEnum.ENABLE.getCode());
                break;
            case GR:
                shelfCommodityData.setGr(StateEnum.ENABLE.getCode());
                break;
            case HK:
                shelfCommodityData.setHk(StateEnum.ENABLE.getCode());
                break;
            case HR:
                shelfCommodityData.setHr(StateEnum.ENABLE.getCode());
                break;
            case HU:
                shelfCommodityData.setHu(StateEnum.ENABLE.getCode());
                break;
            case ID:
                shelfCommodityData.setId(StateEnum.ENABLE.getCode());
                break;
            case IE:
                shelfCommodityData.setIe(StateEnum.ENABLE.getCode());
                break;
            case IL:
                shelfCommodityData.setIl(StateEnum.ENABLE.getCode());
                break;
            case IN:
                shelfCommodityData.setIn(StateEnum.ENABLE.getCode());
                break;
            case IS:
                shelfCommodityData.setIs(StateEnum.ENABLE.getCode());
                break;
            case IT:
                shelfCommodityData.setIt(StateEnum.ENABLE.getCode());
                break;
            case JP:
                shelfCommodityData.setJp(StateEnum.ENABLE.getCode());
                break;
            case KR:
                shelfCommodityData.setKr(StateEnum.ENABLE.getCode());
                break;
            case KW:
                shelfCommodityData.setKw(StateEnum.ENABLE.getCode());
                break;
            case LI:
                shelfCommodityData.setLi(StateEnum.ENABLE.getCode());
                break;
            case LT:
                shelfCommodityData.setLt(StateEnum.ENABLE.getCode());
                break;
            case LV:
                shelfCommodityData.setLv(StateEnum.ENABLE.getCode());
                break;
            case LU:
                shelfCommodityData.setLu(StateEnum.ENABLE.getCode());
                break;
            case MC:
                shelfCommodityData.setMc(StateEnum.ENABLE.getCode());
                break;
            case MF:
                shelfCommodityData.setMf(StateEnum.ENABLE.getCode());
                break;
            case MO:
                shelfCommodityData.setMo(StateEnum.ENABLE.getCode());
                break;
            case MT:
                shelfCommodityData.setMt(StateEnum.ENABLE.getCode());
                break;
            case MX:
                shelfCommodityData.setMx(StateEnum.ENABLE.getCode());
                break;
            case MY:
                shelfCommodityData.setMy(StateEnum.ENABLE.getCode());
                break;
            case NL:
                shelfCommodityData.setNl(StateEnum.ENABLE.getCode());
                break;
            case NO:
                shelfCommodityData.setNo(StateEnum.ENABLE.getCode());
                break;
            case NZ:
                shelfCommodityData.setNz(StateEnum.ENABLE.getCode());
                break;
            case PH:
                shelfCommodityData.setPh(StateEnum.ENABLE.getCode());
                break;
            case PL:
                shelfCommodityData.setPl(StateEnum.ENABLE.getCode());
                break;
            case PT:
                shelfCommodityData.setPt(StateEnum.ENABLE.getCode());
                break;
            case RO:
                shelfCommodityData.setRo(StateEnum.ENABLE.getCode());
                break;
            case RU:
                shelfCommodityData.setRu(StateEnum.ENABLE.getCode());
                break;
            case SA:
                shelfCommodityData.setSa(StateEnum.ENABLE.getCode());
                break;
            case SE:
                shelfCommodityData.setSe(StateEnum.ENABLE.getCode());
                break;
            case SG:
                shelfCommodityData.setSg(StateEnum.ENABLE.getCode());
                break;
            case SI:
                shelfCommodityData.setSi(StateEnum.ENABLE.getCode());
                break;
            case SK:
                shelfCommodityData.setSk(StateEnum.ENABLE.getCode());
                break;
            case TH:
                shelfCommodityData.setTh(StateEnum.ENABLE.getCode());
                break;
            case TR:
                shelfCommodityData.setTr(StateEnum.ENABLE.getCode());
                break;
            case TW:
                shelfCommodityData.setTw(StateEnum.ENABLE.getCode());
                break;
            case US:
                shelfCommodityData.setUs(StateEnum.ENABLE.getCode());
                break;
            case VN:
                shelfCommodityData.setVn(StateEnum.ENABLE.getCode());
                break;
            default:
                break;
        }
    }

    private String saleStateConversion(Integer saleState) {
        String availability = "";
        if (saleState == null) {
            return availability;
        }
        if (saleState.intValue() == SaleState.normal.getCode()) {
            availability = GoogleAvailablityType.normal.getValue();
        }
        if (saleState.intValue() == SaleState.out_of_stock.getCode()) {
            availability = GoogleAvailablityType.out_of_stock.getValue();
        }

        return availability;
    }

    /**
     * 创建OSS文件
     *
     * @param orderExportType
     * @return
     * @throws Exception
     */
    private String createOSSFile(Integer state, OrderExportType orderExportType) throws Exception {
        String fileName = URLEncoder.encode(orderExportType.name() + "_" + System.currentTimeMillis(), "UTF-8");
        if (!FileUtil.exist(OrderExportConstant.PATH)) {
            FileUtil.mkdir(OrderExportConstant.PATH);
        }

        File targetFile = new File(OrderExportConstant.PATH + fileName + ".xlsx");
        this.writerExcel(state, targetFile);

        // 上传oss
        String ossUrl = ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, targetFile);
        if (FileUtil.exist(targetFile)) {
            FileUtil.del(targetFile);
        }
        return ossUrl;
    }

    /**
     * 写入Excel文件
     *
     * @param targetFile
     * @throws FileNotFoundException
     */
    private void writerExcel(Integer state, File targetFile) throws FileNotFoundException {
        ExcelWriter writer = null;
        try {
            writer = EasyExcel.write(new FileOutputStream(targetFile)).build();

            List<GoogleCommoditySaleStateExportData> saleStateExportDataList = listCommoditySaleStateExportData(state);
            if (CollectionUtils.isNotEmpty(saleStateExportDataList)) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0, GoogleExportType.sale_state.getSheetName()).head(GoogleExportType.sale_state.getDataModuleClass()).build();
                writer.write(saleStateExportDataList, writeSheet);
            }
            List<GoogleCommoditySalePriceExportData> priceExportDataList = listCommoditySalePriceExportData(state);
            if (CollectionUtils.isNotEmpty(priceExportDataList)) {
                WriteSheet writeSheet = EasyExcel.writerSheet(1, GoogleExportType.sale_price.getSheetName()).head(GoogleExportType.sale_price.getDataModuleClass()).build();
                writer.write(priceExportDataList, writeSheet);
            }
        } finally {
            writer.finish();
        }
    }

    /**
     * 抽取订单写入信息
     *
     * @param account
     * @param exportType
     * @return
     */
    private OrderExportInfo createOrderExportInfo(String account, OrderExportType exportType) {
        OrderExportInfo exportInfo = new OrderExportInfo();
        exportInfo.setAccount(account);
        exportInfo.setCreateTime(LocalDateTime.now());
        exportInfo.setType(exportType.name());
        exportInfo.setState(true);
        return exportInfo;
    }

    public String getText(String content) {
        if (StringUtils.isEmpty(content)) {
            return "";
        }
        String txtcontent = content.replaceAll("</?[^>]+>", ""); //剔出<html>的标签
        txtcontent = txtcontent.replaceAll("<a>\\s*|\t|\r|\n|</a>", "");//去除字符串中的空格,回车,换行符,制表符
        txtcontent = txtcontent.replaceAll("&nbsp;", "");
        return txtcontent;
    }
}
