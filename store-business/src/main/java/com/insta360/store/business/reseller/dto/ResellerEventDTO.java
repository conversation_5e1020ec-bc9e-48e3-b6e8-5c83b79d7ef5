package com.insta360.store.business.reseller.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2020/11/19
 * @Description:
 */
public class ResellerEventDTO implements Serializable {

    private String link;

    private Boolean disable;

    private JSONObject infos;

    @<PERSON><PERSON><PERSON><PERSON>(name = "event_id")
    private Integer eventId;

    @JSONField(name = "update_time")
    private Long updateTime;

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Boolean getDisable() {
        return disable;
    }

    public void setDisable(Boolean disable) {
        this.disable = disable;
    }

    public JSONObject getInfos() {
        return infos;
    }

    public void setInfos(JSONObject infos) {
        this.infos = infos;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
