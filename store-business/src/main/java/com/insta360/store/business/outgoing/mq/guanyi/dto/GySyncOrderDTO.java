package com.insta360.store.business.outgoing.mq.guanyi.dto;

import com.insta360.store.business.order.enums.OrderStockSourceType;
import com.insta360.store.business.rma.model.RmaOrder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 多渠道订单信息同步至管易 mq消息体
 * @Date 2021/10/14
 */
public class GySyncOrderDTO implements Serializable {

    /**
     * 区分类标签
     */
    private String tag;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 推单来源
     * @see com.insta360.store.business.order.enums.OrderStockSourceType
     */
    private String source;

    /**
     * 订单子项id
     */
    private Integer orderItemId;

    /**
     * 售后单
     */
    private RmaOrder rmaOrder;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public RmaOrder getRmaOrder() {
        return rmaOrder;
    }

    public void setRmaOrder(RmaOrder rmaOrder) {
        this.rmaOrder = rmaOrder;
    }

    @Override
    public String toString() {
        return "GySyncOrderDTO{" +
                "tag='" + tag + '\'' +
                ", orderId=" + orderId +
                ", source='" + source + '\'' +
                ", orderItemId=" + orderItemId +
                ", rmaOrder=" + rmaOrder +
                '}';
    }
}
