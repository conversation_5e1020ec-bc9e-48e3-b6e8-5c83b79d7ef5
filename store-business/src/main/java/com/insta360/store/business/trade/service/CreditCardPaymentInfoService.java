package com.insta360.store.business.trade.service;

import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.trade.bo.CreditCardOrderPaymentInfoQueryBO;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-12-03
 * @Description:
 */
public interface CreditCardPaymentInfoService extends BaseService<CreditCardPaymentInfo> {

    /**
     * 根据订单号获取支付记录信息
     *
     * @param orderNumber
     * @return
     */
    CreditCardPaymentInfo getByOrderNumber(String orderNumber);

    /**
     * 信用卡支付记录查询
     *
     * @param creditCardOrderPaymentInfoQuery
     * @return
     */
    List<CreditCardPaymentInfo> listPaymentInfo(CreditCardOrderPaymentInfoQueryBO creditCardOrderPaymentInfoQuery);

    /**
     * 所有的信用卡支付记录查询
     *
     * @return
     */
    List<CreditCardPaymentInfo> listAllPaymentInfo();

    /**
     * 复杂查询
     *
     * @param creditCardOrderPaymentInfoQuery
     * @param pageQuery
     * @return
     */
    PageResult<CreditCardPaymentInfo> queryPaymentInfo(CreditCardOrderPaymentInfoQueryBO creditCardOrderPaymentInfoQuery, PageQuery pageQuery);

    /**
     * 获取未capture成功的支付信息
     *
     * @param pageQuery
     * @return
     */
    PageResult<CreditCardPaymentInfo> listCapturePaymentInfo(PageQuery pageQuery);

}
