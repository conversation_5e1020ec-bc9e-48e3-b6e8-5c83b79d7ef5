package com.insta360.store.business.meta.enums;

/**
 * @description:
 * @author: py
 * @create: 2025-03-11 18:15
 */
public enum CategorySceneryImageType {

    left( "左图"),

    right("右图"),

    single("单图"),

    ;

    /**
     * 描述
     */
    private final String desc;

    CategorySceneryImageType(String desc) {
        this.desc = desc;
    }

    /**
     * 类型解析
     *
     * @param value
     * @return
     */
    public static CategorySceneryImageType parse(String value) {
        for (CategorySceneryImageType categorySceneryImageType : values()) {
            if (categorySceneryImageType.name().equals(value)) {
                return categorySceneryImageType;
            }
        }

        return null;
    }

    public String getDesc() {
        return desc;
    }
}
