package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.meta.dao.ActivityDynamicParamDao;
import com.insta360.store.business.meta.model.ActivityDynamicParam;
import com.insta360.store.business.meta.service.ActivityDynamicParamService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-01-13
 * @Description:
 */
@Service
public class ActivityDynamicParamServiceImpl extends BaseServiceImpl<ActivityDynamicParamDao, ActivityDynamicParam> implements ActivityDynamicParamService {

    @Override
    public void removeByActivityId(Integer activityId) {
        QueryWrapper<ActivityDynamicParam> qw = new QueryWrapper<>();
        qw.eq("activity_id", activityId);
        baseMapper.delete(qw);
    }

    @Override
    public List<ActivityDynamicParam> listActivityId(Integer activityId) {
        QueryWrapper<ActivityDynamicParam> qw = new QueryWrapper<>();
        qw.eq("activity_id", activityId);
        return baseMapper.selectList(qw);
    }
}
