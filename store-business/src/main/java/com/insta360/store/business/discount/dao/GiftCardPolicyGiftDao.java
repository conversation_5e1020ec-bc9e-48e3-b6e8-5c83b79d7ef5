package com.insta360.store.business.discount.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.discount.model.GiftCardPolicyGift;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface GiftCardPolicyGiftDao extends BaseDao<GiftCardPolicyGift> {

    /**
     * 批量插入
     *
     * @param policyGiftList
     */
    void batchInsert(@Param("policyGiftList") List<GiftCardPolicyGift> policyGiftList);
}
