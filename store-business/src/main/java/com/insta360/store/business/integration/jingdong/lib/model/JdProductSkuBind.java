package com.insta360.store.business.integration.jingdong.lib.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: wkx
 * @Date: 2020/12/29
 * @Description:
 */
@TableName("jd_product_sku_bind")
public class JdProductSkuBind extends BaseModel<JdProductSkuBind> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @JSONField(name = "ware_id")
    private String wareId;  // 商品编号

    private String name;    // 商品名称

    private String upc;     // upc编码

    @JSONField(name = "item_code")
    private String itemCode;   // 商品代码

    public String getWareId() {
        return wareId;
    }

    public void setWareId(String wareId) {
        this.wareId = wareId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUpc() {
        return upc;
    }

    public void setUpc(String upc) {
        this.upc = upc;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "JdProductSkuBind{" +
                "id=" + id +
                ", wareId='" + wareId + '\'' +
                ", name='" + name + '\'' +
                ", upc='" + upc + '\'' +
                ", itemCode='" + itemCode + '\'' +
                '}';
    }
}
