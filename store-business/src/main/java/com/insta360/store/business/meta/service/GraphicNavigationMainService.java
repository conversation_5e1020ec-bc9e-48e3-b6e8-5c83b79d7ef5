package com.insta360.store.business.meta.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.meta.dto.GraphicNavigationDTO;
import com.insta360.store.business.meta.model.GraphicNavigationMain;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-03-08
 * @Description:
 */
public interface GraphicNavigationMainService extends BaseService<GraphicNavigationMain> {

    /**
     * save gn main
     *
     * @param graphicNavigationId
     * @param graphicNavigationParam
     */
    void addGraphicNavigationMain(Integer graphicNavigationId, GraphicNavigationDTO graphicNavigationParam);

    /**
     * del by id
     *
     * @param mainId
     */
    void delGraphicNavigationMain(Integer mainId);

    /**
     * enable gn
     *
     * @param navigationMain
     */
    void enableGraphicNavigation(GraphicNavigationMain navigationMain);

    /**
     * disable gn
     *
     * @param navigationMain
     */
    void disableGraphicNavigation(GraphicNavigationMain navigationMain);

    /**
     * 根据gn ids查询
     *
     * @param graphicNavigationIds
     */
    List<GraphicNavigationMain> listByGraphicNavigationIds(List<Integer> graphicNavigationIds);

    /**
     * 根据gn ids查询
     *
     * @param graphicNavigationIds
     */
    List<GraphicNavigationMain> listByGraphicNavigationIdsEnabled(List<Integer> graphicNavigationIds);

    /**
     * 更新排序
     *
     * @param graphicNavigationMains
     * @param fromIndex
     * @param toIndex
     */
    void updateGnOrderIndex(List<GraphicNavigationMain> graphicNavigationMains, Integer fromIndex, Integer toIndex);
}
