package com.insta360.store.business.commodity.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.model.CommodityTagInfo;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2021/09/15
 * @Description:
 */
public class CommodityTagInfoDTO implements Serializable {

    private Integer id;

    /**
     * 所属分类
     */
    private Integer tagGroupId;

    /**
     * 语言
     */
    private String language;

    /**
     * 文案描述
     */
    private String tagContent;

    public CommodityTagInfo getPojoObject() {
        CommodityTagInfo commodityTagInfo = new CommodityTagInfo();
        BeanUtil.copyProperties(this, commodityTagInfo);
        return commodityTagInfo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTagGroupId() {
        return tagGroupId;
    }

    public void setTagGroupId(Integer tagGroupId) {
        this.tagGroupId = tagGroupId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTagContent() {
        return tagContent;
    }

    public void setTagContent(String tagContent) {
        this.tagContent = tagContent;
    }

    @Override
    public String toString() {
        return "CommodityTagInfoDTO{" +
                "id=" + id +
                ", tagGroupId=" + tagGroupId +
                ", language='" + language + '\'' +
                ", tagContent='" + tagContent + '\'' +
                '}';
    }
}
