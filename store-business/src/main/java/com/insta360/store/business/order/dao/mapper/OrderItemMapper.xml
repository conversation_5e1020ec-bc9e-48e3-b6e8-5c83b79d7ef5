<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.order.dao.OrderItemDao">

    <!-- 根据多条件查询订单ID集合 -->
    <select id="getOrderIdByProductAndOrder" resultType="java.lang.Integer" >
        select `order` from order_item where
            product in
        <foreach collection="productIdList" item="productId" open="(" close=")" separator=",">
            #{productId}
        </foreach>
        and `order` in
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <!-- 获取指定套餐在指定时间段、地区的总预占数 -->
    <select id="listItemLockStockByCommodityIds" resultType="com.insta360.store.business.order.bo.OrderItemStockBO">
        select oi.commodity as commodityId,sum(oi.number) as lockQuantity
        from `order` o
        join order_item oi on oi.`order` = o.id
        <where>
            <if test="commodityIds != null and commodityIds.size() > 0">
                AND oi.commodity IN
                <foreach collection="commodityIds" item="commodityId" open="(" close=")" separator=",">
                    #{commodityId}
                </foreach>
            </if>
            <if test="fromTime != null and endTime != null">
                AND o.create_time BETWEEN #{fromTime} AND #{endTime}
            </if>
            <if test="orderStates != null and orderStates.size() > 0">
                AND o.state IN
                <foreach collection="orderStates" item="state" open="(" close=")" separator=",">
                    #{state}
                </foreach>
            </if>
            <if test="country != null and country != ''">
                AND o.area = #{country}
            </if>
            <if test="isRepair != null">
                AND o.is_repair = #{isRepair}
            </if>
        </where>
        group by oi.commodity
    </select>
</mapper>
