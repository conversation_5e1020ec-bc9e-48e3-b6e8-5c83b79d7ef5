package com.insta360.store.business.prime.enums;

import com.insta360.store.business.prime.constants.PrimeQueryConstants;
import com.insta360.store.business.prime.lib.response.*;
import com.insta360.store.business.prime.lib.variables.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4
 * @description Amazon Prime GraphQL操作枚举
 * 该枚举定义了与Amazon Prime API交互的各种GraphQL操作，包括查询和变更
 */
public enum PrimeGraphqlOperation {

    /**
     * 创建产品操作
     * 将产品信息提交给Amazon Prime服务，创建新的产品记录
     * 返回创建的产品ID
     */
    CreateProduct("CreateProduct", CreateProductVariables.class, CreateProductResponse.class, PrimeQueryConstants.CreateProduct),

    /**
     * 创建购买组操作
     * 将多个产品组合成一个购买组，支持主产品和附属产品的绑定关系
     * 用于在Amazon Prime中创建产品组合或套装
     * 返回创建的购买组ID
     */
    CreatePurchaseGroup("createPurchaseGroup", CreatePurchaseGroupVariables.class, CreatePurchaseGroupResponse.class, PrimeQueryConstants.CreatePurchaseGroup),

    UpdateProduct("UpdateProduct", UpdateProductVariables.class, UpdateProductResponse.class, PrimeQueryConstants.UpdateProduct),

    DeleteProduct("deleteProduct", DeleteProductVariables.class, DeleteProductResponse.class, PrimeQueryConstants.DeleteProduct),

    Product("product", ProductVariables.class, ProductResponse.class, PrimeQueryConstants.Product),

    Products("products", ProductsVariables.class, ProductsResponse.class, PrimeQueryConstants.Products),
    ;

    /**
     * 操作名称，用于在GraphQL请求中标识操作
     */
    private final String operationName;

    private final Class<? extends PrimeVariables> variables;

    private final Class<? extends PrimeResponse> response;

    /**
     * GraphQL查询字符串，定义了操作的具体内容
     */
    private final String query;

    /**
     * 构造函数
     *
     * @param operationName 操作名称
     * @param query         GraphQL查询字符串
     */
    PrimeGraphqlOperation(String operationName, String query) {
        this.operationName = operationName;
        this.query = query;
        this.variables = null;
        this.response = null;
    }

    PrimeGraphqlOperation(String operationName, Class<? extends PrimeVariables> variables, Class<? extends PrimeResponse> response, String query) {
        this.operationName = operationName;
        this.variables = variables;
        this.response = response;
        this.query = query;
    }

    /**
     * 获取GraphQL查询字符串
     *
     * @return 查询字符串
     */
    public String getQuery() {
        return query;
    }

    /**
     * 获取操作名称
     *
     * @return 操作名称
     */
    public String getOperationName() {
        return operationName;
    }

    public Class<? extends PrimeVariables> getVariables() {
        return variables;
    }

    public Class<? extends PrimeResponse> getResponse() {
        return response;
    }
}
