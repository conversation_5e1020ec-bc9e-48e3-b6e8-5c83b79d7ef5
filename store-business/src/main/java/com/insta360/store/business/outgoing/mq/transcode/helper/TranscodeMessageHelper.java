package com.insta360.store.business.outgoing.mq.transcode.helper;

import com.insta360.store.business.commodity.dto.CommodityDisplayDTO;
import com.insta360.store.business.temp.dto.CommodityDisplayTempDTO;
import com.insta360.store.business.temp.model.CommodityDisplayTemp;
import com.insta360.store.business.transcode.constant.TranscodeDelayTime;
import com.insta360.store.business.transcode.enums.BusinessType;
import com.insta360.store.business.outgoing.mq.review.bo.ReviewResourceTranscodeBO;
import com.insta360.store.business.outgoing.mq.review.helper.ReviewMessageHelper;
import com.insta360.store.business.outgoing.mq.transcode.dto.TranscodeMessageDTO;
import com.insta360.store.business.outgoing.mq.transcode.sender.StoreResourceTranscodeMq;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2022/07/07
 * @Description:
 */
@Component
public class TranscodeMessageHelper {

    private final static Logger LOGGER = LoggerFactory.getLogger(ReviewMessageHelper.class);

    @Autowired
    StoreResourceTranscodeMq storeResourceTranscodeMq;

    /**
     * 发送评论资源转码事件
     *
     * @param reviewResourceTranscodeBoList 评论资源转码BO列表
     */
    public void sendReviewResourceTranscode(List<ReviewResourceTranscodeBO> reviewResourceTranscodeBoList) {
        if (CollectionUtils.isEmpty(reviewResourceTranscodeBoList)) {
            LOGGER.error("review transcode data isEmpty {}", reviewResourceTranscodeBoList);
            return;
        }
        TranscodeMessageDTO transcodeMessageParam = new TranscodeMessageDTO();
        transcodeMessageParam.setBusinessType(BusinessType.REVIEW.name());
        transcodeMessageParam.setReviewResources(reviewResourceTranscodeBoList);
        transcodeMessageParam.setDelayTime(TranscodeDelayTime.REVIEW_DELAY_TIME);
        storeResourceTranscodeMq.sendResourceTranscode(transcodeMessageParam);
    }


    /**
     * 发送套餐主图资源转码事件
     *
     * @param commodityDisplayParam
     */
    public void sendDisplayResourceTranscode(CommodityDisplayDTO commodityDisplayParam) {
        if (Objects.isNull(commodityDisplayParam)) {
            LOGGER.error(String.format("[主图转码]发送主图资源转码失败,数据为空:%s", commodityDisplayParam));
            return;
        }

        TranscodeMessageDTO transcodeMessageParam = new TranscodeMessageDTO();
        transcodeMessageParam.setBusinessType(BusinessType.DISPLAY.name());
        transcodeMessageParam.setCommodityDisplayParam(commodityDisplayParam);
        transcodeMessageParam.setDelayTime(TranscodeDelayTime.DISPLAY_DELAY_TIME);
        storeResourceTranscodeMq.sendResourceTranscode(transcodeMessageParam);
    }

    /**
     * display历史数据转码
     *
     * @param commodityDisplayTemps
     */
    public void sendDisplayTempMessage(List<CommodityDisplayTempDTO> commodityDisplayTemps) {
        if (CollectionUtils.isEmpty(commodityDisplayTemps)) {
            LOGGER.error(String.format("[display历史数据同步]发送主图资源转码失败,数据为空:%s", commodityDisplayTemps));
            return;
        }

        TranscodeMessageDTO transcodeMessageParam = new TranscodeMessageDTO();
        transcodeMessageParam.setBusinessType(BusinessType.DISPLAY_TEMP.name());
        transcodeMessageParam.setCommodityDisplayTempList(commodityDisplayTemps);
        transcodeMessageParam.setDelayTime(TranscodeDelayTime.DISPLAY_DELAY_TIME);
        storeResourceTranscodeMq.sendResourceTranscode(transcodeMessageParam);
    }
}
