package com.insta360.store.business.admin.order.service.impl.handler;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.io.resource.ClassPathResource;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.io.ByteStreams;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.admin.order.dto.BatchVatOrderDTO;
import com.insta360.store.business.admin.order.service.impl.handler.bo.DhlVatItemBO;
import com.insta360.store.business.admin.order.service.impl.handler.bo.DhlVatOrderContextBO;
import com.insta360.store.business.admin.order.service.impl.handler.bo.VatOrderContextBO;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.commodity.service.CommodityMetaService;
import com.insta360.store.business.integration.dhl.lib.enums.ValueAddedServiceCodeEnum;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.model.CountryCustomsVat;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.utils.MathUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description DHL运单创建商城内部处理类
 * @Date 2023/3/3
 */
@Component
public class DhlCreateShipmentHandler extends BaseVatOrderExportHandler<List<DhlVatOrderContextBO>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(DhlCreateShipmentHandler.class);

    @Autowired
    private CommodityMetaService commodityMetaService;

    @Autowired
    private CountryConfigService countryConfigService;

    @Override
    public List<DhlVatOrderContextBO> exportVatOrderData(BatchVatOrderDTO batchVatOrderDto) {
        List<DhlVatOrderContextBO> dhlVatOrderContextList = null;
        // 当前执行的订单
        AtomicReference<String> currentOrder = new AtomicReference<>();
        try {
            // 1、基础参数检查
            this.baseParamCheck(batchVatOrderDto);
            // 2、获取当前批次上传的所有订单下单地区
            List<Order> orderList = batchVatOrderDto.getOrderList();
            Map<String, CountryCustomsVat> countryCustomsVatMap = this.getCountryCustomsVatMap(orderList);
            // 3、获取订单支付信息
            List<Integer> orderIdList = orderList.stream().map(Order::getId).collect(Collectors.toList());
            Map<Integer, OrderPayment> orderPaymentMap = getOrderPayment(orderIdList);
            // 4、当前批次订单是否带有电池标识
            ValueAddedServiceCodeEnum valueAddedServiceCode = this.getValueAddedServiceCode(batchVatOrderDto);
            // 5、解析logo、电子签名等base64编码流
            String logoBase64 = getImageBase64("insta360_logo.png");
            String signatureBase64 = getImageBase64("signatureImage.png");
            dhlVatOrderContextList = orderList
                    .stream()
                    .map(
                            order -> {
                                DhlVatOrderContextBO dhlVatOrderContext = null;
                                currentOrder.set(order.getOrderNumber());
                                try {
                                    dhlVatOrderContext = Optional
                                            .ofNullable(selectAmountRatioCalculation(order, orderPaymentMap.get(order.getId()), countryCustomsVatMap))
                                            .map(
                                                    vatOrderContextBo ->
                                                            doPackDhlVatOrderContext(vatOrderContextBo, valueAddedServiceCode, logoBase64, signatureBase64))
                                            .orElse(null);
                                } catch (Exception e) {
                                    String msg = String.format("订单号:%s  ,API调用前置业务处理失败,失败原因:%s", currentOrder.get(), ExceptionUtils.getStackTrace(e));
                                    FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.ShippingDhl, FeiShuAtUser.TW, FeiShuAtUser.LJC, FeiShuAtUser.ZJW, FeiShuAtUser.LB);
                                    LOGGER.error(String.format("[DHL_运单创建]API调用前置业务处理异常 orderNo:%s", currentOrder.get()), e);
                                }
                                return dhlVatOrderContext;
                            })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            String message = String.format("API调用前置业务附属信息处理失败,失败原因:%s", ExceptionUtils.getStackTrace(e));
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.ShippingDhl, FeiShuAtUser.TW, FeiShuAtUser.LJC, FeiShuAtUser.ZJW, FeiShuAtUser.LB);
            LOGGER.error("[DHL_运单创建]API调用前置业务附属信息处理异常", e);
        }

        return dhlVatOrderContextList;
    }

    /**
     * 获取特殊服务-电池标识
     *
     * @param batchVatOrderDto
     */
    private ValueAddedServiceCodeEnum getValueAddedServiceCode(BatchVatOrderDTO batchVatOrderDto) {
        String batteryCode = batchVatOrderDto.getBatteryCode();
        ValueAddedServiceCodeEnum valueAddedServiceCode = ValueAddedServiceCodeEnum.WY;
        if (StringUtils.isNotBlank(batteryCode)) {
            if (ValueAddedServiceCodeEnum.HD.getLabel().equals(batteryCode)) {
                valueAddedServiceCode = ValueAddedServiceCodeEnum.HD;
            }
            if (ValueAddedServiceCodeEnum.HV.getLabel().equals(batteryCode)) {
                valueAddedServiceCode = ValueAddedServiceCodeEnum.HV;
            }
        }
        return valueAddedServiceCode;
    }

    /**
     * 封装DHL 报关运单所需数据
     *
     * @param vatOrderContext
     * @param valueAddedServiceCode
     * @param logoBase64
     * @param signatureBase64
     * @return
     */
    private DhlVatOrderContextBO doPackDhlVatOrderContext(VatOrderContextBO vatOrderContext, ValueAddedServiceCodeEnum valueAddedServiceCode, String logoBase64, String signatureBase64) {
        // 订单
        Order order = vatOrderContext.getOrder();
        // 订单客户信息
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        // 订单报关商品
        List<DhlVatItemBO> dhlVatItemList = this.doPackDhlVatItem(vatOrderContext.getOrderItemList(), order.country());

        DhlVatOrderContextBO dhlVatOrderContext = new DhlVatOrderContextBO();
        dhlVatOrderContext.setOrder(order);
        dhlVatOrderContext.setOrderPayment(orderPaymentService.getByOrder(order.getId()));
        dhlVatOrderContext.setOrderDelivery(orderDelivery);
        dhlVatOrderContext.setValueAddedServiceCode(valueAddedServiceCode);
        dhlVatOrderContext.setCifValue(getCifAmount(vatOrderContext.getTotalItemAmount(), vatOrderContext.getFreight()));
        dhlVatOrderContext.setTax(vatOrderContext.getTax());
        dhlVatOrderContext.setFreight(vatOrderContext.getFreight());
        dhlVatOrderContext.setCountryCustomsVat(vatOrderContext.getCountryCustomsVat());
        dhlVatOrderContext.setDhlVatItemList(dhlVatItemList);
        dhlVatOrderContext.setMaxPriceItemEnglishName(dhlVatItemList.get(0).getItemEnglishName());
        dhlVatOrderContext.setLogoImageBase64(logoBase64);
        dhlVatOrderContext.setSignatureBase64(signatureBase64);
        dhlVatOrderContext.setOrderFmgSpliceNumber(orderHelper.orderCustomSerialSplice(order));

        return dhlVatOrderContext;
    }

    /**
     * 获取CIF金额 （商品分摊总价 + 运费）
     *
     * @param totalItemAmount
     * @param freight
     * @return
     */
    private Double getCifAmount(Double totalItemAmount, Double freight) {
        return MathUtil.add(totalItemAmount, freight);
    }

    /**
     * 封装DHL VAT格式的订单报关商品
     *
     * @param orderItemList
     * @param country
     * @return
     */
    private List<DhlVatItemBO> doPackDhlVatItem(List<OrderItem> orderItemList, InstaCountry country) {
        // 订单商品单价重排序（降序，单价最高第一位）
        orderItemList = orderItemPriceSort(orderItemList);
        // 商品套餐报关配置相关信息
        List<Integer> commodityIds = orderItemList.stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        List<CommodityMeta> commodityMetaList = commodityMetaService.listCommodityMetaByCommodityIds(commodityIds);
        Map<Integer, CommodityMeta> commodityMetaMap = commodityMetaList.stream().collect(Collectors.toMap(CommodityMeta::getCommodityId, c -> c));
        // 获取下单地区相关信息
        CountryConfig countryConfig = countryConfigService.getByCountry(country);
        // 进行自增的序号
        AtomicInteger itemId = new AtomicInteger(1);
        return orderItemList.stream()
                .map(
                        orderItem -> buildDhlOrderItem(commodityMetaMap, countryConfig, orderItem, itemId)
                )
                .collect(Collectors.toList());
    }

    /**
     * 订单商品单价降序
     *
     * @param orderItemList
     * @return
     */
    private List<OrderItem> orderItemPriceSort(List<OrderItem> orderItemList) {
        return orderItemList.stream().sorted(Comparator.comparing(OrderItem::getPrice).reversed()).collect(Collectors.toList());
    }

    /**
     * 构建DHL VAT格式的订单报关商品
     *
     * @param commodityMetaMap
     * @param countryConfig
     * @param orderItem
     * @return
     */
    private DhlVatItemBO buildDhlOrderItem(Map<Integer, CommodityMeta> commodityMetaMap, CountryConfig countryConfig, OrderItem orderItem, AtomicInteger itemId) {
        // 当前商品套餐报关配置
        CommodityMeta commodityMeta = commodityMetaMap.get(orderItem.getCommodity());
        DhlVatItemBO dhlVatItem = new DhlVatItemBO();
        dhlVatItem.setOrderItemId(itemId.getAndIncrement());
        dhlVatItem.setItemEnglishName(commodityMeta.getOfficialNameEn());
        dhlVatItem.setItemPrice(Double.valueOf(String.valueOf(orderItem.getPrice())));
        dhlVatItem.setQuantity(orderItem.getNumber());
        dhlVatItem.setOutboundCode(getCustomCodeByCountry(countryConfig, commodityMeta.getCustomCode(), false));
        dhlVatItem.setInboundCode(getCustomCodeByCountry(countryConfig, commodityMeta.getCustomCode(), true));
        dhlVatItem.setCommodityMeta(commodityMeta);
        return dhlVatItem;
    }

    /**
     * 获取海关编码
     *
     * @param countryConfig
     * @param customCode
     * @return
     */
    private String getCustomCodeByCountry(CountryConfig countryConfig, String customCode, Boolean isMatch) {
        if (Objects.isNull(countryConfig) || StringUtils.isBlank(customCode) || Objects.isNull(isMatch)) {
            return null;
        }
        String hsCode = null;
        JSONObject customCodeJson = JSON.parseObject(customCode);
        // 不需匹配欧盟&美国地区的则直接取默认的海关编码
        if (!isMatch) {
            return customCodeJson.getString("default");
        }

        if ("EU".equals(countryConfig.getGroup())) {
            if (!InstaCountry.NO.equals(countryConfig.country())) {
                hsCode = customCodeJson.getString("EU");
            } else {
                // 商城支持的「挪威」默认取「海关编码」值
                return customCodeJson.getString("default");
            }
        }

        if (InstaCountry.US.equals(countryConfig.country())) {
            hsCode = customCodeJson.getString("US");
        }

        if (StringUtils.isBlank(hsCode)) {
            return customCodeJson.getString("default");
        }

        return hsCode;
    }

    /**
     * 基础参数检查
     *
     * @param batchVatOrderDto
     */
    private void baseParamCheck(BatchVatOrderDTO batchVatOrderDto) {
        if (Objects.isNull(batchVatOrderDto)) {
            throw new InstaException(-1, "参数异常");
        }
        if (CollectionUtils.isEmpty(batchVatOrderDto.getOrderList())) {
            throw new InstaException(-1, "当前批次任务订单缺失");
        }
    }

    /**
     * 获取海关报关税率Map (key:countryCode value:CountryCustomsVat)
     *
     * @param orders
     * @return
     */
    private Map<String, CountryCustomsVat> getCountryCustomsVatMap(List<Order> orders) {
        // 获取所有订单的下单地区
        Set<String> countryCodes = orders.stream().map(Order::getArea).collect(Collectors.toSet());
        return Optional.ofNullable(countryCustomsVatService.listCustomsVatByCountryCodes(countryCodes))
                .filter(CollectionUtils::isNotEmpty)
                .map(
                        list ->
                                list.stream().collect(Collectors.toMap(CountryCustomsVat::getCountryCode, c -> c, (oldValue, newValue) -> newValue)))
                .orElse(null);
    }

    /**
     * 获取图片base64编码流
     *
     * @param fileName
     * @return
     */
    private static String getImageBase64(String fileName) throws IOException {
        ClassPathResource resource = new ClassPathResource("picture/" + fileName);
        InputStream stream = resource.getStream();
        LOGGER.info("获取图片base64编码流,resource:{}", resource);
        return Base64Encoder.encode(ByteStreams.toByteArray(stream));
    }
}
