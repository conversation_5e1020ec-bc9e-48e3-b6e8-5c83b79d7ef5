package com.insta360.store.business.configuration.verification.support.helper;

import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.configuration.verification.enums.ParameterBusinessType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 商城接口请求拦截上报
 * @Date 2022/11/3
 */
@Component
public class GrafanaInterfaceParamVerificationHelper {

    private static final Logger logger = LoggerFactory.getLogger(GrafanaInterfaceParamVerificationHelper.class);

    /**
     * 接口参数请求拦截上报
     * @param businessType
     */
    @GrafanaDataStats(statisticsType = {GrafanaStatisticsType.GAUGE,GrafanaStatisticsType.COUNTER},businessType = GrafanaBusinessType.STORE_SERVICE_MONITOR,keyType = GrafanaKeyType.MONITOR_INTERFACE_REQUEST)
    public void interfaceParamRequestReport(ParameterBusinessType businessType) {
        logger.info("非法请求次数上报成功... businessType:{}",businessType);
    }
}
