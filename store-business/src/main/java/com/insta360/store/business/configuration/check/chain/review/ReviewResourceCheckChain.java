package com.insta360.store.business.configuration.check.chain.review;

import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.review.enums.ReviewResourceTypeEnum;
import com.insta360.store.business.review.model.ReviewResource;
import com.insta360.store.business.review.service.ReviewResourceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/22
 */
@Component
public class ReviewResourceCheckChain extends BaseReviewCheckChain {

    @Autowired
    ReviewResourceService reviewResourceService;

    /**
     * 是纯色图片
     *
     * @param imageUrl 图像url
     * @return boolean
     */
    private static boolean isSolidColor(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return false;
        }
        try {
            URL url = new URL(imageUrl);
            BufferedImage image = ImageIO.read(url);
            int width = image.getWidth();
            int height = image.getHeight();

            // 确保图像足够大以取九个点
            if (width < 3 || height < 3) {
                return false;
            }

            // 取图像九宫格中的九个点
            int[][] points = {
                    {0, 0}, {width / 2, 0}, {width - 1, 0},
                    {0, height / 2}, {width / 2, height / 2}, {width - 1, height / 2},
                    {0, height - 1}, {width / 2, height - 1}, {width - 1, height - 1}
            };

            int firstPixel = image.getRGB(points[0][0], points[0][1]);

            // 检查所有点的颜色是否一致
            for (int i = 1; i < points.length; i++) {
                if (image.getRGB(points[i][0], points[i][1]) != firstPixel) {
                    return false;
                }
            }

            return true;
        } catch (IOException e) {
            return false;
        }
    }

    @Override
    public Boolean doCheck(DoubleCheckBO doubleCheckBo) {
        List<ReviewResource> reviewResourceList = reviewResourceService.listResourceByReviewId(doubleCheckBo.getBusinessId())
                .stream().filter(reviewResource -> reviewResource.getResourceTypeEnum().equals(ReviewResourceTypeEnum.image)).collect(Collectors.toList());
        for (ReviewResource reviewResource : reviewResourceList) {
            if (isSolidColor(reviewResource.getResource())) {
                sendReviewMessage(reviewResource.getId(), "原图 resource", reviewResource.getResource());
                return false;
            }
            if (isSolidColor(reviewResource.getCompressResource())) {
                sendReviewMessage(reviewResource.getId(), "转码后图片 compress_resource", reviewResource.getResource());
                return false;
            }
            if (isSolidColor(reviewResource.getVideoCover())) {
                sendReviewMessage(reviewResource.getId(), "视频封面 video_cover", reviewResource.getResource());
                return false;
            }
            if (isSolidColor(reviewResource.getCompressVideoCover())) {
                sendReviewMessage(reviewResource.getId(), "转码后视频封面 compress_video_cover", reviewResource.getResource());
                return false;
            }
            if (isSolidColor(reviewResource.getSquareImage())) {
                sendReviewMessage(reviewResource.getId(), "方形预览图 square_image", reviewResource.getResource());
                return false;
            }
        }
        return true;
    }

    /**
     * 发送评论信息
     */
    private void sendReviewMessage(Integer reviewResourceId, String message, String url) {
        FeiShuMessageUtil.storeGeneralMessage(String.format("资源ID%s,%s 可能是纯色图（不一定采用九宫格检测法）,原图连接为:[%s]", reviewResourceId, message, url), FeiShuGroupRobot.InternalWarning);
    }

    @Override
    public String getName() {
        return "评论图片资源校验";
    }

}
