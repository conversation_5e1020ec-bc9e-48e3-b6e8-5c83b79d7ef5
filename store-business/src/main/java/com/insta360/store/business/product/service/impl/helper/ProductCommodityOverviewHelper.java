package com.insta360.store.business.product.service.impl.helper;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.admin.product.service.impl.handler.OverviewHandler;
import com.insta360.store.business.product.constant.ProductOverviewConstant;
import com.insta360.store.business.product.dto.ProductOverviewDTO;
import com.insta360.store.business.product.dto.ProductTemplateDTO;
import com.insta360.store.business.product.enums.OverviewParameterType;
import com.insta360.store.business.product.exception.ProductErrorCode;
import com.insta360.store.business.product.model.OverviewTemplateDisableCountry;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductCommodityOverview;
import com.insta360.store.business.product.model.ProductCommodityOverviewContent;
import com.insta360.store.business.product.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 模板的新增/更新小助手
 * @author: py
 * @create: 2022-09-23 10:56
 */
@Component
public class ProductCommodityOverviewHelper {

    @Autowired
    ProductCommodityOverviewService productCommodityOverviewService;

    @Autowired
    ProductCommodityOverviewContentService productCommodityOverviewContentService;

    @Autowired
    OverviewTemplateDisableCountryService overviewTemplateDisableCountryService;

    @Autowired
    ProductService productService;

    @Autowired
    OverviewHandler overviewHandler;

    @Autowired
    OverviewParameterService overviewParameterService;

    /**
     * 模板的新增、更新
     *
     * @param productOverviewParam
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void changeOverviews(ProductTemplateDTO productOverviewParam) {
        Integer productId = productOverviewParam.getProductId();

        Product product = productService.getById(productId);
        if (product == null) {
            throw new InstaException(ProductErrorCode.ProductNotFoundException);
        }

        //获取模板key id
        Map<String, Integer> templateMap = overviewHandler.getTemplateMap();
        //获取元素key id
        Map<String, Integer> parameterMap = overviewHandler.getParameterMap();

        //更新/新增的list
        List<ProductOverviewDTO> productOverviewDtoList = productOverviewParam.getProductOverviews();
        Map<Integer, List<ProductOverviewDTO>> overviewDtoListMap = productOverviewDtoList.stream().collect(Collectors.groupingBy(o -> {
            if (o.getId() == null) {
                return ProductOverviewConstant.ADD_INDEX;
            }
            return ProductOverviewConstant.UPDATE_INDEX;
        }));

        updateOverviewAndParameters(overviewDtoListMap.get(ProductOverviewConstant.UPDATE_INDEX), productOverviewParam, templateMap, parameterMap);
        addOverviewAndParameters(overviewDtoListMap.get(ProductOverviewConstant.ADD_INDEX), productOverviewParam, templateMap, parameterMap);
    }

    /**
     * 更新模板/元素内容/禁用国家等
     *
     * @param updateOverviews
     * @param productOverviewParam
     * @param templateMap
     * @param parameterMap
     */
    private void updateOverviewAndParameters(List<ProductOverviewDTO> updateOverviews, ProductTemplateDTO productOverviewParam, Map<String, Integer> templateMap, Map<String, Integer> parameterMap) {
        if (CollectionUtils.isEmpty(updateOverviews)) {
            return;
        }
        Integer productId = productOverviewParam.getProductId();
        String language = productOverviewParam.getLanguage();
        Integer identify = productOverviewParam.getIdentify();
        Boolean isMobile = productOverviewParam.getIsMobile();

        //更新模板
        List<ProductCommodityOverview> productCommodityOverviews = updateOverviews.stream().map(o -> {
            Integer overviewId = o.getId();
            //元素的更新
            productCommodityOverviewContentService.deleteByLanguage(overviewId, language);
            List<ProductCommodityOverviewContent> productCommodityOverviewContents = o.getOverviewContentObjectList(language, parameterMap, null);
            productCommodityOverviewContentService.saveContentBatch(productCommodityOverviewContents);

            // 模版不是logo -> 进行图片特殊处理
            if (!ProductOverviewConstant.TEMPLATE_LOGO.equals(o.getTemplateKey())) {
                handlePicture(productCommodityOverviewContents);
            }

            //禁用国家的更新
            List<OverviewTemplateDisableCountry> disableCountries = o.getDisabledCountryObjectList(null);
            overviewTemplateDisableCountryService.deleteByOverviewId(overviewId);
            if (o.getDisabledCountry()) {
                overviewTemplateDisableCountryService.saveDisableCountryBatch(disableCountries);
            }

            //更新模板
            ProductCommodityOverview productCommodityOverview = o.getOverviewObject(templateMap);
            productCommodityOverview.setProductId(productId);
            productCommodityOverview.setIdentify(identify);
            productCommodityOverview.setIsMobile(isMobile);
            return productCommodityOverview;
        }).collect(Collectors.toList());
        productCommodityOverviewService.updateByIdBatch(productCommodityOverviews);
    }

    /**
     * 对图片的特殊处理
     *
     * @param productCommodityOverviewContents
     */
    private void handlePicture(List<ProductCommodityOverviewContent> productCommodityOverviewContents) {
        // 区分图片参数
        List<ProductCommodityOverviewContent> contents = getProductCommodityOverviewContentList(productCommodityOverviewContents);
        // 没有图片直接返回
        if (CollectionUtils.isEmpty(contents)) {
            return;
        }
        List<InstaLanguage> instaLanguages = Arrays.asList(InstaLanguage.zh_CN, InstaLanguage.zh_TW, InstaLanguage.ja_JP,
                InstaLanguage.de_DE, InstaLanguage.ko_KR, InstaLanguage.es_ES, InstaLanguage.it_IT, InstaLanguage.fr_FR, InstaLanguage.en_US);
        // 处理图片
        contents.forEach(content -> {
            productCommodityOverviewContentService.deleteByOverviewIdAndParameterId(content);
            List<ProductCommodityOverviewContent> contentList = executeLanguage(instaLanguages, content);
            productCommodityOverviewContentService.saveContentBatch(contentList);
        });
    }

    /**
     * 区分图片content
     *
     * @param productCommodityOverviewContents
     * @return
     */
    private List<ProductCommodityOverviewContent> getProductCommodityOverviewContentList(List<ProductCommodityOverviewContent> productCommodityOverviewContents) {
        List<Integer> pictureIds = overviewParameterService.listByParameterType(OverviewParameterType.picture.getType());
        int picture = 1;
        int noPic = 2;
        Map<Integer, List<ProductCommodityOverviewContent>> map = productCommodityOverviewContents.stream().collect(Collectors.groupingBy(o -> {
            Integer parameterId = o.getOverviewParameterId();
            if (pictureIds.contains(parameterId)) {
                return picture;
            }
            return noPic;
        }));
        // 保存多语言图片信息
        return map.get(picture);
    }

    /**
     * 处理多语言信息的保存
     *
     * @param instaLanguages
     * @param content
     */
    private List<ProductCommodityOverviewContent> executeLanguage(List<InstaLanguage> instaLanguages, ProductCommodityOverviewContent content) {
        return instaLanguages.stream().map(language -> {
            ProductCommodityOverviewContent overviewContent = new ProductCommodityOverviewContent();
            overviewContent.init(content, language.name());
            return overviewContent;
        }).collect(Collectors.toList());
    }

    /**
     * 新增模板/元素内容/禁用国家等
     *
     * @param addOverviews
     * @param productOverviewParam
     * @param templateMap
     * @param parameterMap
     */
    private void addOverviewAndParameters(List<ProductOverviewDTO> addOverviews, ProductTemplateDTO productOverviewParam, Map<String, Integer> templateMap, Map<String, Integer> parameterMap) {
        if (CollectionUtils.isEmpty(addOverviews)) {
            return;
        }
        Integer productId = productOverviewParam.getProductId();
        String language = productOverviewParam.getLanguage();
        Integer identify = productOverviewParam.getIdentify();
        Boolean isMobile = productOverviewParam.getIsMobile();
        // 新增模板
        List<ProductCommodityOverview> productCommodityOverviews = addOverviews.stream().map(o -> {
            // 封装模板内容
            ProductCommodityOverview overview = o.getOverviewObject(templateMap);
            overview.setProductId(productId);
            overview.setIdentify(identify);
            overview.setIsMobile(isMobile);
            overview.setCreateTime(LocalDateTime.now());
            return overview;
        }).collect(Collectors.toList());
        productCommodityOverviewService.saveOverviewBatch(productCommodityOverviews);

        // key:模板顺序 value:模版本身
        Map<Integer, ProductCommodityOverview> overviewMap = productCommodityOverviews.stream().
                collect(Collectors.toMap(ProductCommodityOverview::getTemplateOrderIndex, o -> o));

        addOverviews.forEach(o -> {
            Integer overviewId = overviewMap.get(o.getTemplateOrderIndex()).getId();
            // 新增元素内容
            List<ProductCommodityOverviewContent> productCommodityOverviewContents = o.getOverviewContentObjectList(language, parameterMap, overviewId);
            productCommodityOverviewContentService.saveContentBatch(productCommodityOverviewContents);

            // 模版不是logo -> 进行图片特殊处理
            if (!ProductOverviewConstant.TEMPLATE_LOGO.equals(o.getTemplateKey())) {
                handlePicture(productCommodityOverviewContents);
            }

            // 新增禁用国家的内容
            if (o.getDisabledCountry()) {
                List<OverviewTemplateDisableCountry> disableCountries = o.getDisabledCountryObjectList(overviewId);
                overviewTemplateDisableCountryService.saveDisableCountryBatch(disableCountries);
            }

        });
    }

    /**
     * 删除模板以及这个模板关联的所有元素和禁用国家等
     *
     * @param idList
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteOverviews(List<Integer> idList) {
        //删除元素
        productCommodityOverviewContentService.deleteByOverviewIdList(idList);
        //删除禁用国家
        overviewTemplateDisableCountryService.deleteByOverviewIdList(idList);
        //删除模板
        productCommodityOverviewService.removeByIds(idList);
    }
}
