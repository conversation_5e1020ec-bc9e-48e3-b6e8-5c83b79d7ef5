package com.insta360.store.business.commodity.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;

import java.text.DecimalFormat;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-10-25
 * @Description:
 */
@TableName("product_commodity_price")
public class CommodityPrice extends BaseModel<CommodityPrice> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @JSONField(name = "commodity_id")
    private Integer commodityId;

    private String area;

    private String currency;

    @JSONField(name = "shipping_cost")
    private Float shippingCost;

    private Float tax;

    @JSONField(name = "tax_rate")
    private Float taxRate;

    @JSONField(name = "origin_amount")
    private Float originAmount;

    private Float amount;

    @JSONField(name = "reseller_customer_amount")
    private Float resellerCustomerAmount;

    @JSONField(name = "reseller_flash_promo_amount")
    private Float resellerFlashPromoAmount;

    private Boolean enabled;

    private String campaign;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Float getShippingCost() {
        return shippingCost;
    }

    public void setShippingCost(Float shippingCost) {
        this.shippingCost = shippingCost;
    }

    public Float getTax() {
        return tax;
    }

    public void setTax(Float tax) {
        this.tax = tax;
    }

    public Float getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(Float taxRate) {
        this.taxRate = taxRate;
    }

    public Float getOriginAmount() {
        return originAmount;
    }

    public void setOriginAmount(Float originAmount) {
        this.originAmount = originAmount;
    }

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public Float getResellerCustomerAmount() {
        return resellerCustomerAmount;
    }

    public void setResellerCustomerAmount(Float resellerCustomerAmount) {
        this.resellerCustomerAmount = resellerCustomerAmount;
    }

    public Float getResellerFlashPromoAmount() {
        return resellerFlashPromoAmount;
    }

    public void setResellerFlashPromoAmount(Float resellerFlashPromoAmount) {
        this.resellerFlashPromoAmount = resellerFlashPromoAmount;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    @Override
    public String toString() {
        return "CommodityPrice{" +
                "id=" + id +
                ", commodityId=" + commodityId +
                ", area='" + area + '\'' +
                ", currency='" + currency + '\'' +
                ", shippingCost=" + shippingCost +
                ", tax=" + tax +
                ", taxRate=" + taxRate +
                ", originAmount=" + originAmount +
                ", amount=" + amount +
                ", resellerCustomerAmount=" + resellerCustomerAmount +
                ", resellerFlashPromoAmount=" + resellerFlashPromoAmount +
                ", enabled=" + enabled +
                ", campaign='" + campaign + '\'' +
                '}';
    }

    /**
     * 现价
     *
     * @return
     */
    public Price price() {
        return new Price(currency(), amount);
    }

    /**
     * 原价
     *
     * @return
     */
    public Price originPrice() {
        return originAmount != null ? new Price(currency(), originAmount) : null;
    }

    public Price resellerCustomerPrice() {
        Float amount = getResellerCustomerAmount();
        return amount == null ? null : new Price(currency(), amount);
    }

    public Price resellerFlashPromoPrice() {
        Float amount = getResellerFlashPromoAmount();
        return amount == null ? null : new Price(currency(), amount);
    }

    /**
     * 货币转换
     *
     * @return
     */
    public Currency currency() {
        return Currency.valueOf(currency);
    }

    /**
     * 国家地区
     *
     * @return {@link InstaCountry}
     */
    public InstaCountry country()  {
        return InstaCountry.parse(area);
    }

    /**
     * 税费
     *
     * @return
     */
    public Price taxPrice() {
        if (taxRate != null && taxRate != 0) {
            tax = amount * taxRate;
        }

        tax = tax == null ? 0f : Float.parseFloat(new DecimalFormat("#.##").format(tax));
        return new Price(currency(), tax);
    }

    /**
     * 运费
     *
     * @return
     */
    public Price shippingFeePrice() {
        return new Price(currency(), shippingCost);
    }
}