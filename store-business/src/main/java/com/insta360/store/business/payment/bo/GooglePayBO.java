package com.insta360.store.business.payment.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 3/12/24
 * @Description:
 */
public class GooglePayBO implements Serializable {

    /**
     * 签名
     */
    @NotNull(message = "签名不允许为null")
    @NotBlank(message = "签名不允许为空")
    private String signature;

    /**
     * 算法版本
     */
    @NotNull(message = "算法版本不允许为null")
    @NotBlank(message = "算法版本不允许为空")
    private String protocolVersion;

    /**
     * 签名详情
     */
    @NotNull(message = "签名详情不允许为null")
    @NotBlank(message = "签名详情不允许为空")
    private String signedMessage;

    /**
     * 用户行为token
     */
    @NotNull(message = "行为token不允许为null")
    @NotBlank(message = "行为token不允许为空")
    private String forterTokenCookie;

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getProtocolVersion() {
        return protocolVersion;
    }

    public void setProtocolVersion(String protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    public String getSignedMessage() {
        return signedMessage;
    }

    public void setSignedMessage(String signedMessage) {
        this.signedMessage = signedMessage;
    }

    public String getForterTokenCookie() {
        return forterTokenCookie;
    }

    public void setForterTokenCookie(String forterTokenCookie) {
        this.forterTokenCookie = forterTokenCookie;
    }

    @Override
    public String toString() {
        return "GooglePayBO{" +
                "signature='" + signature + '\'' +
                ", protocolVersion='" + protocolVersion + '\'' +
                ", signedMessage='" + signedMessage + '\'' +
                ", forterTokenCookie='" + forterTokenCookie + '\'' +
                '}';
    }
}
