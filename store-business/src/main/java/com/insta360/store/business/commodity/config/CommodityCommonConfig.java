package com.insta360.store.business.commodity.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @description: 套餐模块配置
 * @author: py
 * @create: 2022-12-05 16:25
 */
@RefreshScope
@Configuration
public class CommodityCommonConfig {

    /**
     * 分批启用套餐的每次的数量
     */
    @Value("${app.commodity.common.enablePriceBatchNum}")
    private Integer enablePriceBatchNum;

    /**
     * 美国仓库存告警指定的套餐id集合
     */
    @Value("${app.commodity.common.usAlertCommodityIds}")
    private List<Integer> usStockNoticeCommodityIds;

    /**
     * 美国仓安全库存阈值
     */
    @Value("${app.commodity.common.usSafeStock:100}")
    private Integer usSafeStock;

    public Integer getEnablePriceBatchNum() {
        return enablePriceBatchNum;
    }

    public List<Integer> getUsStockNoticeCommodityIds() {
        return usStockNoticeCommodityIds;
    }

    public Integer getUsSafeStock() {
        return usSafeStock;
    }
}
