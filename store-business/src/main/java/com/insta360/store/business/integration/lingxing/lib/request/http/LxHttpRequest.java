package com.insta360.store.business.integration.lingxing.lib.request.http;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.store.business.exception.RetryHandlerException;
import com.insta360.store.business.integration.lingxing.lib.request.BaseLxRequest;
import com.insta360.store.business.integration.lingxing.lib.response.LxErrorResponse;
import com.insta360.store.business.integration.lingxing.lib.response.LxResponse;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15
 */
@Component
@Scope("prototype")
public class LxHttpRequest {

    public static final Logger LOGGER = LoggerFactory.getLogger(BaseLxRequest.class);

    /**
     * 限流代码
     */
    private static final Integer LIMIT_CODE = 103;

    /**
     * 执行请求
     *
     * @return {@link LxResponse}
     */
    @Retryable(value = RetryHandlerException.class, include = RetryHandlerException.class, maxAttempts = 3, backoff = @Backoff(value = 10000L, multiplier = 1.1))
    public LxResponse execute(HttpRequest request, Class<? extends LxResponse> responseClass) {
        try (HttpResponse execute = request.execute()) {
            return doExecuteHandler(execute, responseClass);
        } catch (Exception e) {
            String uuid = UUIDUtils.generateUuid();
            String message = String.format("领星请求发生异常,uuid:{%s},errorMessage:{%s}", uuid, e.getMessage());
            LOGGER.error(String.format("领星请求发生异常,uuid:{%s},请求参数为:{%s},errorMessage:{%s}", uuid, request, e.getMessage()), e);
            if (e instanceof HttpException) {
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning);
                throw new RetryHandlerException(e.getMessage());
            }
            FeiShuAtUser atUser = e instanceof InstaException ? null : FeiShuAtUser.WXQ;
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, atUser);
            throw e;
        }
    }


    /**
     * 执行处理程序
     *
     * @param execute 执行
     * @return {@link LxResponse}
     */
    private LxResponse doExecuteHandler(HttpResponse execute, Class<? extends LxResponse> responseClass) {
        String responseBody = execute.body();
        if (StringUtil.isBlank(responseBody)) {
            throw new InstaException(-1, "请求响应为空");
        }

        // 解析拉取数据响应
        LxResponse lxResponse = parseResponse(responseBody, responseClass);
        if (Objects.nonNull(lxResponse) && lxResponse.isSuccess()) {
            return lxResponse;
        }

        // 错误数据解析
        LxErrorResponse lxErrorResponse = parseResponse(responseBody, LxErrorResponse.class);
        if (Objects.isNull(lxErrorResponse)) {
            throw new InstaException(-1, "请求错误解析失败");
        }
        String message = String.format("领星请求失败,message:%s,requestId:%s,响应为:%s", lxErrorResponse.getMessage(), lxErrorResponse.getRequestId(), responseBody);
        LOGGER.error(message);
        throw new InstaException(-1, message);
    }

    /**
     * 解析响应
     *
     * @param responseBody  响应正文
     * @param responseClass 响应类
     * @return {@link LxResponse}
     */
    public <T> T parseResponse(String responseBody, Class<T> responseClass) {
        try {
            LOGGER.info("请求响应：{}", responseBody);
            return JSON.parseObject(responseBody, responseClass);
        } catch (Exception e) {
            LOGGER.error("JSON解析为：类[{}]错误，JSON原文:{}", responseClass, responseBody);
            return null;
        }
    }
}
