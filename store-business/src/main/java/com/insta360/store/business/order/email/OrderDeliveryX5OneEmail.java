package com.insta360.store.business.order.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: py
 * @Date: 2025/04/08
 * @Description: 发货之后4天发送教育邮件第1封
 */
@Scope("prototype")
@Component
public class OrderDeliveryX5OneEmail extends BaseOrderEmail {

    @Override
    public String getTemplateName() {
        return "store_order_on_delivery_x5_1";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        // 订单号
        templateParams.addBodyParam("order_number", this.getOrderNumber());

        // 用户名称
        templateParams.addBodyParam("user_name", this.getUserName());
    }

    @Override
    protected InstaLanguage getLanguage() {
        if (InstaCountry.HK.equals(getOrder().country())) {
            return InstaLanguage.zh_TW;
        }

        return super.getLanguage();
    }

    @Override
    protected Long getDelayTimeConfig() {
        return DELAY_TIME;
    }
}
