package com.insta360.store.business.meta.service.impl.helper;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.model.TaxCodeInfo;
import com.insta360.store.business.meta.service.TaxCodeInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/7/6
 */
@Component
public class TaxCodeInfoHelper {

    @Autowired
    TaxCodeInfoService taxCodeInfoService;

    /**
     * 获取产品税号Map
     *
     * @param country
     * @return
     */
    public Map<Integer, String> getProductCategoryTaxCodeMap(InstaCountry country) {
        List<TaxCodeInfo> taxCodeInfoList = taxCodeInfoService.listByCountry(country);
        if (CollectionUtils.isEmpty(taxCodeInfoList)) {
            return new HashMap<>();
        }
        return taxCodeInfoList.stream().collect(Collectors.toMap(TaxCodeInfo::getId, TaxCodeInfo::getTaxCode));
    }
}
