package com.insta360.store.business.integration.wto.oms.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description 巨沃wms公共配置类
 * @Date 2025/01/22
 */
@RefreshScope
@Configuration
public class WmsConfiguration {

    @Value("${app.wms.appKey}")
    private String appKey;

    @Value("${app.wms.secret}")
    private String secret;

    @Value("${app.wms.apiUrl}")
    private String apiUrl;

    public String getAppKey() {
        return appKey;
    }

    public String getSecret() {
        return secret;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    @Override
    public String toString() {
        return "WmsConfiguration{" +
                "appKey='" + appKey + '\'' +
                ", secret='" + secret + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                '}';
    }
}
