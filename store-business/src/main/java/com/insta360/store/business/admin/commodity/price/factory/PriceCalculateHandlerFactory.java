package com.insta360.store.business.admin.commodity.price.factory;

import com.insta360.store.business.admin.commodity.price.handle.discount.DiscountHandler;
import com.insta360.store.business.admin.commodity.price.handle.mantissa.MantissaHandler;
import com.insta360.store.business.admin.commodity.price.handle.pack.DataPackHandler;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/17
 */
public interface PriceCalculateHandlerFactory {

    /**
     * 创建数据封装策略
     *
     * @return {@link DataPackHandler}
     */
    DataPackHandler createDataPackStrategy();

    /**
     * 创建折扣策略
     *
     * @return {@link DiscountHandler}
     */
    DiscountHandler createDiscountStrategy();

    /**
     * 创建尾数策略
     *
     * @return {@link MantissaHandler}
     */
    MantissaHandler createMantissaStrategy();

}
