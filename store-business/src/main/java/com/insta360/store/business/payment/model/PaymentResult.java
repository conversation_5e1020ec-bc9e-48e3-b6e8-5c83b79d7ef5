package com.insta360.store.business.payment.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: hyc
 * @Date: 2019/2/24
 * @Description:
 */
@TableName("payment_result")
public class PaymentResult extends BaseModel<PaymentResult> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 支付渠道
     */
    private String channel;

    /**
     * 回传类型
     */
    @TableField("`type`")
    private String type;

    /**
     * 回传数据
     */
    private String data;

    @JSONField(name = "create_time")
    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "PaymentResult{" +
                "id=" + id +
                ", orderNumber='" + orderNumber + '\'' +
                ", channel='" + channel + '\'' +
                ", type='" + type + '\'' +
                ", data='" + data + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
