package com.insta360.store.business.discount.service.impl.factory;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.discount.enums.AmountBusinessType;
import com.insta360.store.business.discount.enums.PolicyDiscountType;
import com.insta360.store.business.discount.enums.PolicyRuleType;
import com.insta360.store.business.discount.exception.DiscountErrorCode;
import com.insta360.store.business.discount.model.*;
import com.insta360.store.business.discount.service.*;
import com.insta360.store.business.discount.service.impl.handler.context.CouponContext;
import com.insta360.store.business.discount.service.impl.handler.context.GiftCardContext;
import com.insta360.store.business.discount.service.impl.handler.context.TradeDiscountContext;
import com.insta360.store.business.meta.bo.TradeCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/4/20
 */
@Component
public class TradeDiscountContextFactory {

    @Autowired
    private CouponThresholdItemService couponThresholdItemService;

    @Autowired
    private CouponPolicyService couponPolicyService;

    @Autowired
    private CouponPolicyItemService couponPolicyItemService;

    @Autowired
    private CouponRuleAmountService couponRuleAmountService;

    @Autowired
    private CouponThresholdService couponThresholdService;

    @Autowired
    private CouponPolicyGiftService couponPolicyGiftService;

    @Autowired
    private GiftCardThresholdService giftCardThresholdService;

    @Autowired
    private GiftCardThresholdItemService giftCardThresholdItemService;

    @Autowired
    private GiftCardRuleAmountService giftCardRuleAmountService;

    @Autowired
    private GiftCardPolicyService giftCardPolicyService;

    @Autowired
    private GiftCardPolicyItemService giftCardPolicyItemService;

    @Autowired
    private GiftCardPolicyGiftService giftCardPolicyGiftService;

    /**
     * 设置优惠券核心计算校验上下文
     * @param tradeCode
     * @param <T>
     * @return
     */
    public <T extends TradeDiscountContext> T getTradeDiscountContext(TradeCode tradeCode) {
        if(Objects.isNull(tradeCode) || StringUtils.isBlank(tradeCode.getCode())) {
            throw new InstaException(DiscountErrorCode.InvalidCouponException);
        }

        if(tradeCode instanceof Coupon) {
            Coupon coupon = (Coupon)tradeCode;
            return (T) getCouponContext(coupon);
        }else if(tradeCode instanceof GiftCard) {
            GiftCard giftCard = (GiftCard)tradeCode;
            return (T) getGiftCardContext(giftCard);
        }

        return null;
    }


    /**
     * 获取优惠券核心计算上下文
     * @param coupon
     * @return
     */
    private CouponContext getCouponContext(Coupon coupon) {
        CouponContext couponContext = new CouponContext();
        BeanUtils.copyProperties(coupon,couponContext);
        couponContext.setCouponId(coupon.getId());
        couponContext.setTradeCode(coupon);

        //1、设置优惠券门槛
        CouponContext.CouponThresholdBean couponThresholdBean = getCouponThresholdBean(coupon);

        //2、设置优惠券政策
        List<CouponContext.CouponPolicyBean> couponPolicyBeanList = getCouponPolicyBeanList(coupon);

        couponContext.setThreshold(couponThresholdBean);
        couponContext.setPolicyList(couponPolicyBeanList);
        return couponContext;
    }

    /**
     * 获取代金券核心计算上下文
     * @param giftCard
     * @return
     */
    private GiftCardContext getGiftCardContext(GiftCard giftCard) {
        GiftCardContext giftCardContext = new GiftCardContext();
        BeanUtils.copyProperties(giftCard,giftCardContext);
        giftCardContext.setGiftCardId(giftCard.getId());
        giftCardContext.setTradeCode(giftCard);

        //1、设置代金券门槛
        GiftCardContext.GiftCardThresholdBean giftCardThresholdBean = getGiftCardThresholdBean(giftCard);

        //2、设置代金券政策
        List<GiftCardContext.GiftCardPolicyBean> couponPolicyBeanList = getGiftCardPolicyBeanList(giftCard);

        giftCardContext.setThreshold(giftCardThresholdBean);
        giftCardContext.setPolicyList(couponPolicyBeanList);
        return giftCardContext;
    }

    /**
     * 获取代金券政策
     * @param giftCard
     * @return
     */
    private List<GiftCardContext.GiftCardPolicyBean> getGiftCardPolicyBeanList(GiftCard giftCard) {
        List<GiftCardPolicy> giftCardPolicyList = giftCardPolicyService.listByGiftCardCode(giftCard.getGiftCardCode());
        if(CollectionUtils.isEmpty(giftCardPolicyList) && !giftCard.isFreeType()) {
            throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
        }

        //2、设置代金券政策
        return giftCardPolicyList
                    .stream()
                    .filter(giftCardPolicy -> {
                        //如有指定产品or套餐，则校验下政策指定商品数据是否存在，如不存在则过滤此条政策
                        if (!Integer.valueOf(PolicyRuleType.NO_RULES.code).equals(giftCardPolicy.getPolicyRuleType())) {
                            List<GiftCardPolicyItem> giftCardPolicyItemList = giftCardPolicyItemService.listByPolicyId(giftCardPolicy.getId());
                            return CollectionUtils.isNotEmpty(giftCardPolicyItemList) ? true : false;
                        }
                        return true;
                    })
                    .map(giftCardPolicy -> {
                        GiftCardContext.GiftCardPolicyBean giftCardPolicyBean = new GiftCardContext.GiftCardPolicyBean();
                        BeanUtils.copyProperties(giftCardPolicy, giftCardPolicyBean);
                        //是否有指定商品，如有则进行查询
                        if (!Integer.valueOf(PolicyRuleType.NO_RULES.code).equals(giftCardPolicy.getPolicyRuleType())) {
                            List<GiftCardPolicyItem> giftCardPolicyItemList = giftCardPolicyItemService.listByPolicyId(giftCardPolicy.getId());
                            giftCardPolicyBean.setGiftCardPolicyItemList(giftCardPolicyItemList);
                        }
                        //是否有设置固定减价
                        if (Integer.valueOf(PolicyDiscountType.AMOUNT_FIXED.code).equals(giftCardPolicy.getPolicyDiscountType())) {
                            List<GiftCardRuleAmount> giftCardPolicyRuleAmountList = giftCardRuleAmountService.listByBusinessIdAndType(giftCardPolicy.getId(), AmountBusinessType.POLICY_AMOUNT.type);
                            giftCardPolicyBean.setGiftCardRuleAmountList(giftCardPolicyRuleAmountList);
                        }
                        //是否有设置赠品
                        List<GiftCardPolicyGift> giftCardPolicyGiftList = giftCardPolicyGiftService.listByPolicyId(giftCardPolicy.getId());
                        giftCardPolicyBean.setGiftCardPolicyGiftList(giftCardPolicyGiftList);
                        return giftCardPolicyBean;
                    })
                    .collect(Collectors.toList());
    }


    /**
     * 获取代金券门槛
     * @param giftCard
     * @return
     */
    private GiftCardContext.GiftCardThresholdBean getGiftCardThresholdBean(GiftCard giftCard) {
        GiftCardContext.GiftCardThresholdBean giftCardThresholdBean = null;
        GiftCardThreshold giftCardThreshold = giftCardThresholdService.getThresholdByGiftCardCode(giftCard.getGiftCardCode());
        if(Objects.nonNull(giftCardThreshold) || giftCardThreshold.getThresholdTypeMark() != 0) {
            giftCardThresholdBean = new GiftCardContext.GiftCardThresholdBean();
            BeanUtils.copyProperties(giftCardThreshold,giftCardThresholdBean);
            //查询门槛金额配置
            List<GiftCardRuleAmount> giftCardThresholdRuleAmountList = giftCardRuleAmountService.listByBusinessIdAndType(giftCardThreshold.getId(), AmountBusinessType.THRESHOLD_AMOUNT.type);
            giftCardThresholdBean.setGiftCardThresholdAmountList(giftCardThresholdRuleAmountList);
            //查询门槛指定商品
            List<GiftCardThresholdItem> giftCardThresholdItemList = giftCardThresholdItemService.listByThresholdId(giftCardThreshold.getId());
            giftCardThresholdBean.setGiftCardThresholdItemList(giftCardThresholdItemList);
        }
        return giftCardThresholdBean;
    }

    /**
     * 获取优惠券门槛
     * @param coupon
     * @return
     */
    private CouponContext.CouponThresholdBean getCouponThresholdBean(Coupon coupon) {
        CouponContext.CouponThresholdBean couponThresholdBean = null;
        CouponThreshold couponThreshold = couponThresholdService.getThresholdByCouponCode(coupon.getCouponCode());
        if(Objects.nonNull(couponThreshold) || couponThreshold.getThresholdTypeMark() != 0) {
            couponThresholdBean = new CouponContext.CouponThresholdBean();
            BeanUtils.copyProperties(couponThreshold,couponThresholdBean);
            //查询门槛金额配置
            List<CouponRuleAmount> couponThresholdRuleAmountList = couponRuleAmountService.listByBusinessIdAndType(couponThreshold.getId(), AmountBusinessType.THRESHOLD_AMOUNT.type);
            couponThresholdBean.setCouponThresholdAmountList(couponThresholdRuleAmountList);
            //查询门槛指定商品
            List<CouponThresholdItem> couponThresholdItemList = couponThresholdItemService.listByThresholdId(couponThreshold.getId());
            couponThresholdBean.setCouponThresholdItemList(couponThresholdItemList);
        }
        return couponThresholdBean;
    }

    /**
     * 获取优惠券政策
     * @param coupon
     * @return
     */
    private List<CouponContext.CouponPolicyBean> getCouponPolicyBeanList(Coupon coupon) {
        List<CouponPolicy> couponPolicyList = couponPolicyService.listByCouponCode(coupon.getCouponCode());
        if(CollectionUtils.isEmpty(couponPolicyList)) {
            throw new InstaException(DiscountErrorCode.InvalidCouponException);
        }

        return couponPolicyList
                .stream()
                .filter(couponPolicy -> {
                    //如有指定产品or套餐，则校验下政策指定商品数据是否存在，如不存在则过滤此条政策
                    if (!Integer.valueOf(PolicyRuleType.NO_RULES.code).equals(couponPolicy.getPolicyRuleType())) {
                        List<CouponPolicyItem> couponPolicyItemList = couponPolicyItemService.listByPolicyId(couponPolicy.getPolicyId());
                        return CollectionUtils.isNotEmpty(couponPolicyItemList) ? true : false;
                    }
                    return true;
                })
                .map(couponPolicy -> {
                    CouponContext.CouponPolicyBean couponPolicyBean = new CouponContext.CouponPolicyBean();
                    BeanUtils.copyProperties(couponPolicy, couponPolicyBean);
                    //是否有指定商品，如有则进行查询
                    if (!Integer.valueOf(PolicyRuleType.NO_RULES.code).equals(couponPolicy.getPolicyRuleType())) {
                        List<CouponPolicyItem> couponPolicyItemList = couponPolicyItemService.listByPolicyId(couponPolicy.getPolicyId());
                        couponPolicyBean.setCouponPolicyItemList(couponPolicyItemList);
                    }
                    //是否有设置固定减价
                    if (Integer.valueOf(PolicyDiscountType.AMOUNT_FIXED.code).equals(couponPolicy.getPolicyDiscountType())) {
                        List<CouponRuleAmount> couponPolicyRuleAmountList = couponRuleAmountService.listByBusinessIdAndType(couponPolicy.getPolicyId(), AmountBusinessType.POLICY_AMOUNT.type);
                        couponPolicyBean.setCouponRuleAmountList(couponPolicyRuleAmountList);
                    }
                    //是否有设置赠品
                    List<CouponPolicyGift> couponPolicyGiftList = couponPolicyGiftService.listByPolicyId(couponPolicy.getPolicyId());
                    couponPolicyBean.setCouponPolicyGiftList(couponPolicyGiftList);
                    return couponPolicyBean;
                })
                .collect(Collectors.toList());
    }
}
