package com.insta360.store.business.payment.lib.ocean.config.normal;

import com.insta360.store.business.payment.lib.ocean.config.BaseOceanPaymentConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wbt
 * @Date: 2024/07/04
 * @Description:
 */
@Configuration
public class OceanUsRepairmentCreditCardConfig extends BaseOceanPaymentConfig {

    @Value("${payment.repairment.ocean_payment.credit_card.us.account}")
    protected String creditCardAccount;

    @Value("${payment.repairment.ocean_payment.credit_card.us.terminal}")
    protected String creditCardTerminal;

    @Value("${payment.repairment.ocean_payment.credit_card.us.secure_code}")
    protected String creditCardSecureCode;

    @Value("${payment.repairment.ocean_payment.credit_card.us.public_key}")
    protected String creditCardPublicKey;

    @Override
    public String getAccount() {
        return creditCardAccount;
    }

    @Override
    public String getTerminal() {
        return creditCardTerminal;
    }

    @Override
    public String getSecureCode() {
        return creditCardSecureCode;
    }

    @Override
    public String getPublicKey() {
        return creditCardPublicKey;
    }
}
