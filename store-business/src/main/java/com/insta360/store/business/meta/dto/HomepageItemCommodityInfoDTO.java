package com.insta360.store.business.meta.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.HomepageItemCommodityInfo;

/**
 * @Author: wbt
 * @Date: 2021/09/11
 * @Description:
 */
public class HomepageItemCommodityInfoDTO {

    private Integer id;

    /**
     * 关联的主页类型
     */
    private Integer commodityGroupId;

    /**
     * 语言
     */
    private String language;

    /**
     * 产品名称标注（为空字符串则取产品名）
     */
    private String displayName;

    /**
     * 视频
     */
    private String video;

    /**
     * 视频背景
     */
    private String videoImage;

    /**
     * 小的场景触碰图
     */
    private String hoverImageS;

    /**
     * 大的场景触碰图
     */
    private String hoverImageM;

    /**
     * 小的场景触碰图（移动端）
     */
    private String mobileHoverImageS;

    /**
     * 大的场景触碰图（移动端）
     */
    private String mobileHoverImageM;

    public HomepageItemCommodityInfo getPojoObject() {
        HomepageItemCommodityInfo homepageItemInfo = new HomepageItemCommodityInfo();
        BeanUtil.copyProperties(this, homepageItemInfo);
        return homepageItemInfo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCommodityGroupId() {
        return commodityGroupId;
    }

    public void setCommodityGroupId(Integer commodityGroupId) {
        this.commodityGroupId = commodityGroupId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getVideo() {
        return video;
    }

    public void setVideo(String video) {
        this.video = video;
    }

    public String getVideoImage() {
        return videoImage;
    }

    public void setVideoImage(String videoImage) {
        this.videoImage = videoImage;
    }

    public String getHoverImageS() {
        return hoverImageS;
    }

    public void setHoverImageS(String hoverImageS) {
        this.hoverImageS = hoverImageS;
    }

    public String getHoverImageM() {
        return hoverImageM;
    }

    public void setHoverImageM(String hoverImageM) {
        this.hoverImageM = hoverImageM;
    }

    public String getMobileHoverImageS() {
        return mobileHoverImageS;
    }

    public void setMobileHoverImageS(String mobileHoverImageS) {
        this.mobileHoverImageS = mobileHoverImageS;
    }

    public String getMobileHoverImageM() {
        return mobileHoverImageM;
    }

    public void setMobileHoverImageM(String mobileHoverImageM) {
        this.mobileHoverImageM = mobileHoverImageM;
    }

    @Override
    public String toString() {
        return "HomepageItemCommodityInfoDTO{" +
                "id=" + id +
                ", commodityGroupId=" + commodityGroupId +
                ", language='" + language + '\'' +
                ", displayName='" + displayName + '\'' +
                ", video='" + video + '\'' +
                ", videoImage='" + videoImage + '\'' +
                ", hoverImageS='" + hoverImageS + '\'' +
                ", hoverImageM='" + hoverImageM + '\'' +
                ", mobileHoverImageS='" + mobileHoverImageS + '\'' +
                ", mobileHoverImageM='" + mobileHoverImageM + '\'' +
                '}';
    }
}
