package com.insta360.store.business.configuration.redisson.enums;

/**
 * @Author: wbt
 * @Date: 2021/07/22
 * @Description:
 */
public enum RmaEmailEnum {

    /**
     * 售后订单申请邮件
     */
    RMA_ORDER_APPLY_EMAIL("rma_order_apply_email", "根据不同的仅退款/退货退款原因发送退货退款挽留邮件");

    /**
     * 邮件类型
     */
    private final String type;

    /**
     * 说明
     */
    private final String detail;

    RmaEmailEnum(String type, String detail) {
        this.type = type;
        this.detail = detail;
    }

    public String getType() {
        return type;
    }

    public String getDetail() {
        return detail;
    }
}
