package com.insta360.store.business.payment.lib.klarna.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.store.business.payment.lib.klarna.config.KlarnaPaymentConfiguration;
import com.insta360.store.business.payment.lib.klarna.request.http.KlarnaPaymentHttpRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @Author: wbt
 * @Date: 2023/04/10
 * @Description:
 */
public abstract class BaseKlarnaPaymentRequest {

    protected static final Logger LOGGER = LoggerFactory.getLogger(BaseKlarnaPaymentRequest.class);

    /**
     * 接口路径
     */
    protected String method;

    /**
     * 请求路径
     */
    protected String apiUrl;

    /**
     * 账户名
     */
    protected String username;

    /**
     * 账户密码
     */
    protected String password;

    public BaseKlarnaPaymentRequest(String method, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        this.method = method;
        this.apiUrl = klarnaPaymentConfiguration.getApiUrl();
        this.username = klarnaPaymentConfiguration.getUsername();
        this.password = klarnaPaymentConfiguration.getPassword();
    }

    /**
     * 参数检测
     */
    public Boolean checker() {
        return true;
    }

    /**
     * get 请求
     *
     * @return
     */
    public String executeGet() {
        KlarnaPaymentHttpRequest httpRequest = ApplicationContextHolder.getApplicationContext().getBean(KlarnaPaymentHttpRequest.class);
        return httpRequest.executeGet(this.getRequestUrl(), this.getAuthorization());
    }

    /**
     * post 请求
     *
     * @return
     */
    public String executePost(String orderNumber) {
        KlarnaPaymentHttpRequest httpRequest = ApplicationContextHolder.getApplicationContext().getBean(KlarnaPaymentHttpRequest.class);
        return httpRequest.executePost(orderNumber, this.getRequestUrl(), this.getAuthorization(), this.getBody());
    }

    /**
     * delete 请求
     *
     * @return
     */
    public String executeDelete() {
        KlarnaPaymentHttpRequest httpRequest = ApplicationContextHolder.getApplicationContext().getBean(KlarnaPaymentHttpRequest.class);
        return httpRequest.executeDelete(this.getRequestUrl(), this.getAuthorization());
    }

    /**
     * patch 请求
     *
     * @return
     */
    public String executePatch() {
        KlarnaPaymentHttpRequest httpRequest = ApplicationContextHolder.getApplicationContext().getBean(KlarnaPaymentHttpRequest.class);
        return httpRequest.executePatch(this.getRequestUrl(), this.getAuthorization(), this.getBody());
    }

    /**
     * 请求路径
     *
     * @return
     */
    protected String getRequestUrl() {
        return this.apiUrl + this.method;
    }

    /**
     * 构造授权值
     *
     * @return
     */
    protected String getAuthorization() {
        String authText = this.getUsername() + ":" + this.getPassword();
        return "Basic " + new String(Base64.getEncoder().encode(authText.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 构造请求体
     *
     * @return
     */
    protected String getBody() {
        JSONObject requestBodyJson = JSONObject.parseObject(JSON.toJSONString(this));
        requestBodyJson.remove("method");
        requestBodyJson.remove("username");
        requestBodyJson.remove("password");
        requestBodyJson.remove("apiUrl");
        requestBodyJson.remove("requestUrl");
        return requestBodyJson.toJSONString();
    }

    public String getMethod() {
        return method;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    @Override
    public String toString() {
        return "BaseKlarnaPaymentRequest{" +
                "method='" + method + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                '}';
    }
}
