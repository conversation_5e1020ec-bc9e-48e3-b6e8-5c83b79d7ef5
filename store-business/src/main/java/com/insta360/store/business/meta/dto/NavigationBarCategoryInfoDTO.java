package com.insta360.store.business.meta.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategoryInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/5/24
 * @Description:
 */
public class NavigationBarCategoryInfoDTO implements Serializable {

    private Integer id;

    /**
     * 一级内部分类id
     */
    @NotNull(message = "一级内部分类id不允许为空")
    private Integer categoryInsideId;

    /**
     * 一级分类名称
     */
    @NotBlank(message = "一级分类名称不允许为空")
    private String categoryName;

    /**
     * 语言
     */
    @NotBlank(message = "语言不允许为空")
    private String language;

    /**
     * 地区
     */
    @NotBlank(message = "地区不允许为空")
    private String country;

    public NavigationBarCategoryInfo getPojoObject() {
        NavigationBarCategoryInfo categoryInfo = new NavigationBarCategoryInfo();
        BeanUtil.copyProperties(this, categoryInfo);
        return categoryInfo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        return "AdNavigationBarCategoryInfoVO{" +
                "id=" + id +
                ", categoryInsideId=" + categoryInsideId +
                ", categoryName='" + categoryName + '\'' +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                '}';
    }
}
