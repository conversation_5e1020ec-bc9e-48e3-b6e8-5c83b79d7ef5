package com.insta360.store.business.discount.dto.ro;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 门槛通用RO
 * @Date 2022/4/1
 */
public class ThresholdCommonRO implements Serializable {

    /**
     * 门槛ID
     */
    private Integer thresholdId;

    /**
     * 优惠券业务编号 or 代金券业务编号 or 优惠券模版业务编号 or 代金券模版业务编号
     */
    private String commonCode;

    /**
     * 门槛指定商品类型
     * @see com.insta360.store.business.discount.enums.PolicyRuleType
     */
    private Integer thresholdItemType;

    /**
     * 门槛绑定商品
     */
    private List<BindItemCommonRO> thresholdItemList;

    /**
     * 门槛指定满足金额
     */
    private List<RuleAmountCommonRO> thresholdAmountList;

    public Integer getThresholdId() {
        return thresholdId;
    }

    public void setThresholdId(Integer thresholdId) {
        this.thresholdId = thresholdId;
    }

    public String getCommonCode() {
        return commonCode;
    }

    public void setCommonCode(String commonCode) {
        this.commonCode = commonCode;
    }

    public List<BindItemCommonRO> getThresholdItemList() {
        return thresholdItemList;
    }

    public void setThresholdItemList(List<BindItemCommonRO> thresholdItemList) {
        this.thresholdItemList = thresholdItemList;
    }

    public List<RuleAmountCommonRO> getThresholdAmountList() {
        return thresholdAmountList;
    }

    public void setThresholdAmountList(List<RuleAmountCommonRO> thresholdAmountList) {
        this.thresholdAmountList = thresholdAmountList;
    }

    public Integer getThresholdItemType() {
        return thresholdItemType;
    }

    public void setThresholdItemType(Integer thresholdItemType) {
        this.thresholdItemType = thresholdItemType;
    }
}
