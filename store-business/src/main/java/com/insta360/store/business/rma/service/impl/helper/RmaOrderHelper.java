package com.insta360.store.business.rma.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.cloud.service.impl.helper.PspEquityHelper;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.discount.enums.DiscountSource;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.integration.wto.oms.service.helper.StoreOrderDeliveryHelper;
import com.insta360.store.business.integration.yipiaoyun.service.YiPiaoYunAutoInvoiceService;
import com.insta360.store.business.order.constants.OrderBizMarkConstant;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemCustomsTaxRateService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.avalara.helper.AvalaraMessageSendHelper;
import com.insta360.store.business.outgoing.mq.rma.helper.RmaOrderMessageSendHelper;
import com.insta360.store.business.outgoing.mq.wto.helper.StoreDataSyncOmsMessageSendHelper;
import com.insta360.store.business.outgoing.rpc.user.dto.OaUser;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductBatchHelper;
import com.insta360.store.business.rma.bo.*;
import com.insta360.store.business.rma.constant.RedisKeyConstant;
import com.insta360.store.business.rma.constant.RmaConstant;
import com.insta360.store.business.rma.dto.*;
import com.insta360.store.business.rma.email.*;
import com.insta360.store.business.rma.enums.RmaPlatformSource;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.exception.RmaErrorCode;
import com.insta360.store.business.rma.model.*;
import com.insta360.store.business.rma.service.*;
import com.insta360.store.business.rma.service.impl.handler.RmaOrderSpecialSceneHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2020/02/10
 * @Description:
 */
@Component
public class RmaOrderHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(RmaOrderHelper.class);

    /**
     * 售后原因
     */
    private static final String RMA_REASON = "other_reasons";


    @Autowired
    OrderService orderService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    RmaEmailFactory rmaEmailFactory;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    RmaDeliveryService rmaDeliveryService;

    @Autowired
    RmaItemStockHelper rmaItemStockHelper;

    @Autowired
    RmaNumberGenerator rmaNumberGenerator;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    RmaItemStockService rmaItemStockService;

    @Autowired
    RmaOrderDetailService rmaOrderDetailService;

    @Autowired
    RmaReasonOptionService rmaReasonOptionService;

    @Autowired
    RmaInsuranceOrderHelper rmaInsuranceOrderHelper;

    @Autowired
    AvalaraMessageSendHelper avalaraMessageSendHelper;

    @Autowired
    RmaOrderMessageSendHelper rmaOrderMessageSendHelper;

    @Autowired
    YiPiaoYunAutoInvoiceService yiPiaoYunAutoInvoiceService;

    @Autowired
    RmaCoreAmountCalculationHelper rmaCoreAmountCalculationHelper;

    @Autowired
    RmaSuccessHelper rmaSuccessHelper;

    @Autowired
    ProductBatchHelper productBatchHelper;

    @Autowired
    ProductService productService;

    @Autowired
    RmaDownstreamProcessingHelper rmaDownstreamProcessingHelper;

    @Autowired
    StoreDataSyncOmsMessageSendHelper storeDataSyncOmsMessageSendHelper;

    @Autowired
    StoreOrderDeliveryHelper storeOrderDeliveryHelper;

    @Autowired
    OrderItemCustomsTaxRateService orderItemCustomsTaxRateService;

    @Autowired
    PspEquityHelper pspEquityHelper;

    /**
     * 申请售后 （用户）
     *
     * @param refundApplyWebBo
     * @return
     */
    public RmaOrder create(RefundApplyWebBO refundApplyWebBo) {
        if (Objects.isNull(refundApplyWebBo)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        // 获取订单
        Order order = refundApplyWebBo.getOrder();
        // 获取售后商品
        OrderItem orderItem = refundApplyWebBo.getOrderItem();
        // 售后商品对应的产品
        Product product = productService.getById(orderItem.getProduct());
        // 售后单基础信息
        RmaOrder rmaOrderInfo = refundApplyWebBo.getRmaOrder();

        // 订单信息校验
        this.checkRmaOrderInfo(order, orderItem, rmaOrderInfo);
        // 构建售后单明细
        RmaOrderDetail rmaOrderDetail = this.getRmaOrderDetail(rmaOrderInfo.rmaType(), orderItem, orderItem.getNumber());
        if (RmaType.rma_change.equals(rmaOrderInfo.rmaType())) {
            rmaOrderDetail = new RmaOrderDetail();
            rmaOrderDetail.setReturnNum(orderItem.getNumber());
            rmaOrderDetail.setRefundAmount(BigDecimal.ZERO);
            rmaOrderDetail.setOrderItemId(orderItem.getId());
        }
        // 构建售后单信息
        if (product.isCloudSubscribeItem()) {
            rmaOrderInfo.setCloseAutoSubscribe(Boolean.TRUE);
        }
        if (DiscountSource.CLOUD_SUBSCRIBE.name().equals(orderItem.getDiscountSource())) {
            rmaOrderInfo.setReturnQuota(Boolean.TRUE);
        }
        rmaOrderInfo.setRmaNumber(rmaNumberGenerator.generate());
        rmaOrderInfo.setContactEmail(order.getContactEmail());
        rmaOrderInfo.setState(RmaState.init.getCode());
        rmaOrderInfo.setCreateTime(LocalDateTime.now());
        rmaOrderInfo.setModifyTime(LocalDateTime.now());
        rmaOrderInfo.setQuantity(orderItem.getNumber());
        rmaOrderInfo.setRefundCurrency(orderItem.getCurrency());
        rmaOrderInfo.setRefundAmount(rmaOrderDetail.getRefundAmount().floatValue());

        // 1、创建售后单
        RmaOrder rmaOrder = rmaOrderService.create(rmaOrderInfo, rmaOrderDetail, orderItem);
        // 2、售后单创建完成后置处理
        rmaCreatePostHandle(rmaOrder, rmaOrderDetail, order);

        return rmaOrder;
    }

    /**
     * 用户端申请校验
     *
     * @param order
     * @param orderItem
     * @param rmaOrderInfo
     */
    public void checkRmaOrderInfo(Order order, OrderItem orderItem, RmaOrder rmaOrderInfo) {
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        if (Objects.isNull(orderItem)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 支付后的状态才可申请RMA
        if (!OrderState.afterPayedState().contains(order.orderState())) {
            throw new InstaException(RmaErrorCode.ConditionNotMetException);
        }

        // 退款原因长度限制
        String extraReason = rmaOrderInfo.getExtraReason();
        if (StringUtil.isNotBlank(extraReason) && extraReason.length() > 1000) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 判断是否重复申请
        RmaOrder rmaOrder = rmaOrderService.getByOrderItem(rmaOrderInfo.getOrderItemId());
        if (Objects.nonNull(rmaOrder)) {
            throw new InstaException(RmaErrorCode.DuplicateApplyException);
        }

        // 云服务商品退款申请校验
        RmaOrderSpecialSceneHandler rmaOrderSpecialSceneHandler = ApplicationContextHolder.getApplicationContext().getBean(RmaOrderSpecialSceneHandler.class);
        rmaOrderSpecialSceneHandler.cloudSubscribeRefundOrderCheck(orderItem);

        // 校验可退库存
        rmaItemStockHelper.checkRefundItemStock(order.getId(), orderItem.getId(), orderItem.getNumber());

        // 绑定服务校验
        rmaInsuranceOrderHelper.bindItemService(order, orderItem, rmaOrderInfo);
    }

    /**
     * 获取售后单明细
     *
     * @param rmaType   售后类型
     * @param orderItem 售后商品
     * @param returnNum 售后数量
     * @return
     */
    public RmaOrderDetail getRmaOrderDetail(RmaType rmaType, OrderItem orderItem, Integer returnNum) {
        // 获取售后退款金额明细
        RefundItemPriceBO refundItemPrice = rmaCoreAmountCalculationHelper.getRefundMoneyDetail(rmaType, orderItem, returnNum);
        // 退税费
        BigDecimal returnTaxAmount = refundItemPrice.getRefundableTax();
        // 退关税
        BigDecimal refundableCustomsTax = refundItemPrice.getRefundableCustomsTax();
        // 退运费
        BigDecimal returnFreight = BigDecimal.ZERO;
        // 优惠扣减金额
        BigDecimal discountAmount = refundItemPrice.getDiscountDeductionAmount();
        // 应退金额（未扣减优惠金额）
        BigDecimal itemRefundableAmount = new BigDecimal(String.valueOf(orderItem.getPrice())).multiply(new BigDecimal(String.valueOf(returnNum)));
        // 实退金额（减去扣减金额）
        BigDecimal itemActualRefundAmount = refundItemPrice.getItemActualRefundAmount();
        // 售后单退款金额 = 实退金额 + 退税费
        BigDecimal refundAmount = itemActualRefundAmount.add(returnTaxAmount).add(refundableCustomsTax);

        return new RmaOrderDetail(orderItem.getId(), returnTaxAmount, returnFreight, discountAmount, itemRefundableAmount, itemActualRefundAmount, refundAmount, returnNum, refundableCustomsTax);
    }

    /**
     * 售后单创建事件-后置处理
     *
     * @param rmaOrder 售后订单
     * @param order    订单
     */
    public void rmaCreatePostHandle(RmaOrder rmaOrder, RmaOrderDetail rmaOrderDetail, Order order) {
        LOGGER.info("售后单创建后置处理开始-rmaOrder:{}", JSON.toJSONString(rmaOrder));
        // 1、同步OMS
        storeDataSyncOmsMessageSendHelper.sendRmaOrderChangeSyncOmsMessage(rmaOrder, rmaOrderDetail, Boolean.FALSE);

        // 2、售后状态变更通知
        rmaOrderMessageSendHelper.sendRefundStateChangeMessage(rmaOrder);

        // 3、发送邮件到zendesk系统
        sendRmaEmail2Zendesk(rmaOrder);
    }

    /**
     * 批次创建售后订单
     *
     * @param batchCreateRmaDTO 批次创建售后dto
     * @param oaUserInfo
     * @return {@link List}<{@link RmaOrder}>
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public List<RmaOrder> batchCreateRmaOrder(BatchCreateRmaDTO batchCreateRmaDTO, OaUser oaUserInfo) {
        List<ReturnItemDTO> refundItemList = batchCreateRmaDTO.getRefundItemList();
        if (CollectionUtils.isEmpty(refundItemList)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        RmaType rmaType = RmaType.parse(batchCreateRmaDTO.getRmaType());
        if (Objects.isNull(rmaType)) {
            throw new InstaException(-1, "非法的售后类型");
        }

        // 需要售后的订单商品id
        List<Integer> rmaItemIds = refundItemList.stream().map(ReturnItemDTO::getOrderItemId).collect(Collectors.toList());
        // 查询商品列表
        List<OrderItem> orderItemList = (List<OrderItem>) orderItemService.listByIds(rmaItemIds);

        // 获取订单商品id - 产品 映射Map
        Map<Integer, Product> orderItemProductMap = getOrderItemProductMap(orderItemList);
        // 获取订单商品映射Map
        Map<Integer, OrderItem> orderItemMap = getOrderItemMap(orderItemList);
        return refundItemList.stream().flatMap(rmaItem -> {
            RmaOrder rmaOrder = new RmaOrder();
            rmaOrder.setOrderId(batchCreateRmaDTO.getOrderId());
            rmaOrder.setRmaType(rmaType.name());
            rmaOrder.setRmaMainDuty(batchCreateRmaDTO.getRmaMainDuty());
            rmaOrder.setAdminReason(batchCreateRmaDTO.getAdminReason());
            rmaOrder.setAdminRemark(batchCreateRmaDTO.getAdminRemark());
            rmaOrder.setNeedReturn(batchCreateRmaDTO.getNeedReturn());
            rmaOrder.setOrderItemId(rmaItem.getOrderItemId());
            rmaOrder.setQuantity(rmaItem.getReturnNum());
            rmaOrder.setRefundAmount(rmaItem.getRefundAmount().floatValue());
            rmaOrder.setPlatformSource(batchCreateRmaDTO.getPlatformSource());

            // 判定是否云服务商品
            Product product = orderItemProductMap.get(rmaItem.getOrderItemId());
            if (Objects.nonNull(product) && product.isCloudSubscribeItem() && Objects.nonNull(batchCreateRmaDTO.getCloseAutoSubscribe())) {
                rmaOrder.setCloseAutoSubscribe(batchCreateRmaDTO.getCloseAutoSubscribe());
            }

            // 判定是否享受云服务订阅折扣的商品
            OrderItem orderItem = orderItemMap.get(rmaItem.getOrderItemId());
            if (Objects.nonNull(orderItem) && DiscountSource.CLOUD_SUBSCRIBE.name().equals(orderItem.getDiscountSource()) && Objects.nonNull(batchCreateRmaDTO.getReturnQuota())) {
                rmaOrder.setReturnQuota(batchCreateRmaDTO.getReturnQuota());
            }

            // psp 权益终止标识
            rmaOrder.setTerminationEquity(batchCreateRmaDTO.getTerminationEquity());

            // 使用flatMap将列表平铺成单个集合
            return this.create(rmaOrder, rmaItem.getRefundedTax(), rmaItem.getRefundCustomsTax(), oaUserInfo).stream();
        }).collect(Collectors.toList());
    }

    /**
     * 申请售后 （运营平台）
     *
     * @param rmaOrderInfo 售后订单信息
     * @param refundedTax  已退税款
     * @param oaUserInfo
     * @return {@link RmaOrder}
     */
    public List<RmaOrder> create(RmaOrder rmaOrderInfo, BigDecimal refundedTax, BigDecimal refundCustomsTax, OaUser oaUserInfo) {
        if (Objects.isNull(rmaOrderInfo) || Objects.isNull(refundedTax) || Objects.isNull(refundCustomsTax)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        Order order = orderService.getById(rmaOrderInfo.getOrderId());
        OrderItem orderItem = orderItemService.getById(rmaOrderInfo.getOrderItemId());
        // 售后单创建前置校验
        List<RmaOrder> rmaOrders = this.checkAdRmaOrderInfo(order, orderItem, rmaOrderInfo, refundedTax, refundCustomsTax, oaUserInfo);
        // 获取售后单明细
        RmaOrderDetail rmaOrderDetail = getRmaOrderDetail(rmaOrderInfo.rmaType(), orderItem, rmaOrderInfo.getQuantity());
        // 售后单实退金额 (系统计算)
        BigDecimal refundAmount = rmaOrderDetail.getRefundAmount();
        // 客服修改后的售后单实退金额 （客服在系统计算的基础上修改）
        BigDecimal modifyRefundAmount = new BigDecimal(String.valueOf(rmaOrderInfo.getRefundAmount()));

        // 若售后单实退金额被客服修改则进行退款金额重置
        if (refundAmount.compareTo(modifyRefundAmount) != 0) {
            rmaOrderDetail.setRefundAmount(modifyRefundAmount);
            rmaOrderDetail.setReturnTaxAmount(refundedTax);
            rmaOrderDetail.setRefundCustomsTaxAmount(refundCustomsTax);
            rmaOrderDetail.setItemActualRefundAmount(modifyRefundAmount.subtract(refundedTax).subtract(refundCustomsTax));
        } else {
            rmaOrderInfo.setRefundAmount(refundAmount.floatValue());
        }
        rmaOrderInfo.setRmaNumber(rmaNumberGenerator.generate());
        rmaOrderInfo.setState(RmaState.init.getCode());
        rmaOrderInfo.setCreateTime(LocalDateTime.now());
        rmaOrderInfo.setModifyTime(LocalDateTime.now());
        rmaOrderInfo.setRefundCurrency(orderItem.getCurrency());
        rmaOrderInfo.setContactEmail(order.getContactEmail());
        rmaOrderInfo.setRecoverUser(oaUserInfo.getName());

        // 1、创建售后单
        RmaOrder rmaOrder = rmaOrderService.create(rmaOrderInfo, rmaOrderDetail, orderItem);
        rmaOrders.add(rmaOrder);
        // 更新订单下所有售后单挽回人
        this.updateRmaOperatorByOrderId(oaUserInfo.getName(), rmaOrder.getOrderId());
        // 2、售后单创建完成后置处理
        rmaCreatePostHandle(rmaOrder, rmaOrderDetail, order);

        return rmaOrders;
    }

    /**
     * 运营后台申请校验
     *
     * @param order
     * @param orderItem
     * @param rmaOrderInfo
     * @param oaUserInfo
     */
    public List<RmaOrder> checkAdRmaOrderInfo(Order order, OrderItem orderItem, RmaOrder rmaOrderInfo, BigDecimal refundedTax, BigDecimal refundCustomsTax, OaUser oaUserInfo) {
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        if (Objects.isNull(orderItem)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        if (!order.getId().equals(orderItem.getOrder())) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        if (OrderState.prepared.equals(order.orderState()) && !RmaType.rma_refund.equals(rmaOrderInfo.rmaType())) {
            throw new InstaException(-1, "已配货订单只允许申请'仅退款'");
        }

        // 退款原因长度限制
        String extraReason = rmaOrderInfo.getExtraReason();
        if (StringUtil.isNotBlank(extraReason) && extraReason.length() > 300) {
            throw new InstaException(-1, "客服备注说明长度不可超过300字符");
        }

        // 判断是否重复申请
        RmaOrder rmaOrder = rmaOrderService.getByOrderItem(rmaOrderInfo.getOrderItemId());
        if (Objects.nonNull(rmaOrder)) {
            throw new InstaException(RmaErrorCode.DuplicateApplyException);
        }

        // 云服务商品退款申请校验
        RmaOrderSpecialSceneHandler rmaOrderSpecialSceneHandler = ApplicationContextHolder.getApplicationContext().getBean(RmaOrderSpecialSceneHandler.class);
        rmaOrderSpecialSceneHandler.cloudSubscribeRefundOrderCheck(orderItem);

        // 售后单实退金额
        BigDecimal refundAmount = new BigDecimal(String.valueOf(rmaOrderInfo.getRefundAmount()));
        // 订单支付总金额
        OrderPayment payment = orderPaymentService.getByOrder(rmaOrderInfo.getOrderId());
        BigDecimal orderPayAmount = new BigDecimal(String.valueOf(payment.getTotalPayPrice().getAmount()));


        // 售后价格不能超过订单的支付金额
        if (refundAmount.compareTo(orderPayAmount) > 0) {
            throw new InstaException(-1, "售后单实退金额不能超过订单支付总金额");
        }

        // 退税费校验
        BigDecimal totalTax = orderItem.getTotalTax();
        if (refundedTax.compareTo(totalTax) > 0) {
            throw new InstaException(-1, "退税费不可大于商品实收总税费");
        }

        // 售后商品最大可退关税
        BigDecimal refundableMaxCustomsTax = Optional.ofNullable(orderItemCustomsTaxRateService.getCustomsTaxByOrderItemId(orderItem.getId())).map(orderItemCustomsTaxRate -> new BigDecimal(String.valueOf(orderItemCustomsTaxRate.getTotalTax()))).orElse(BigDecimal.ZERO);
        if (refundCustomsTax.compareTo(refundableMaxCustomsTax) > 0) {
            throw new InstaException(-1, "退关税不可大于商品实收总关税");
        }

        // 订单总退款金额校验
        if (!RmaType.rma_change.name().equals(rmaOrderInfo.getRmaType())) {
            // 查询出当前订单关联的所有售后单
            List<RmaOrder> orderList = rmaOrderService.listByOrderId(order.getId());
            // 有效售后单
            List<RmaOrder> efficientRmaOrderList = orderList.stream().filter(returnOrder -> Lists.newArrayList(RmaType.rma_refund, RmaType.rma_return).contains(returnOrder.rmaType()) && !Lists.newArrayList(RmaState.cancelled, RmaState.rejected).contains(returnOrder.rmaState())).collect(Collectors.toList());
            // 订单已退款金额
            BigDecimal orderRefundedAmount = efficientRmaOrderList.stream().map(r -> new BigDecimal(String.valueOf(r.getRefundAmount()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 订单已退总金额
            BigDecimal orderRefundedTotalAmount = orderRefundedAmount.add(refundAmount);

            if (orderPayAmount.compareTo(orderRefundedTotalAmount) < 0) {
                throw new InstaException(-1, "订单总退款金额不能超过订单总支付金额");
            }
        }

        // 是否同步客服原因给用户
        RmaReasonOption rmaReasonOption = rmaReasonOptionService.getByRmaTypeAndKey(RmaType.parse(rmaOrderInfo.getRmaType()), rmaOrderInfo.getAdminReason(), rmaOrderInfo.getRmaMainDuty());
        if (rmaReasonOption == null) {
            throw new InstaException(-1, "客服原因key不存在，请联系开发人员确认。" + "客服原因：" + rmaOrderInfo.getAdminReason()
                    + "售后类型:" + rmaOrderInfo.getRmaType() + "主责归因:" + rmaOrderInfo.getRmaMainDuty());
        }
        if (rmaReasonOption.getCustomerEnable()) {
            rmaOrderInfo.setReason(rmaOrderInfo.getAdminReason());
        } else {
            rmaOrderInfo.setReason(RMA_REASON);
        }

        // 校验可退库存
        rmaItemStockHelper.checkRefundItemStock(order.getId(), orderItem.getId(), rmaOrderInfo.getQuantity());

        // 后台绑定服务校验
        return rmaInsuranceOrderHelper.bindAdItemService(order, orderItem, rmaOrderInfo, oaUserInfo);
    }

    /**
     * 更新订单下所有售后单挽回人
     *
     * @param name    姓名
     * @param orderId 订单id
     */
    private void updateRmaOperatorByOrderId(String name, Integer orderId) {
        List<RmaOrder> rmaOrders = rmaOrderService.listByOrderId(orderId);
        List<RmaOrder> rmaOrderList = rmaOrders.stream().peek(rmaOrder -> rmaOrder.setRecoverUser(name)).collect(Collectors.toList());
        rmaOrderService.batchUpdateRmaOrderById(rmaOrderList);
    }

    /**
     * 售后订单修改
     *
     * @param rmaOrderModifyDto 售后订单修改dto
     * @param oaUserInfo        oa用户信息
     */
    public void rmaOrderModify(RmaOrderModifyDTO rmaOrderModifyDto, OaUser oaUserInfo) {
        RmaOrder rmaOrder = rmaOrderService.getById(rmaOrderModifyDto.getRmaId());
        String adminReason = rmaOrder.getAdminReason();
        if (Objects.isNull(rmaOrder)) {
            throw new InstaException(-1, "售后单不存在");
        }
        OrderItem orderItem = orderItemService.getById(rmaOrder.getOrderItemId());
        if (Objects.isNull(orderItem)) {
            throw new InstaException(-1, "售后商品不存在");
        }
        // 对应产品
        Product product = productService.getById(orderItem.getProduct());
        // 是否云服务产品 && 售后单处于售后中状态
        if (product.isCloudSubscribeItem() && RmaState.isAfterSaleState(rmaOrder.rmaState())) {
            rmaOrder.setCloseAutoSubscribe(rmaOrderModifyDto.getCloseAutoSubscribe());
        }

        // 获取售后类型
        RmaType rmaType = RmaType.parse(rmaOrderModifyDto.getRmaType());
        // 售后订单编辑数据校验
        this.rmaOrderModifyCheck(rmaOrderModifyDto, rmaOrder, orderItem);
        // 获取售后商品可退库存数
        int refundStockNum = rmaItemStockService.getRefundStockNum(rmaOrder.getOrderId(), rmaOrder.getOrderItemId());
        // 获取售后退款金额明细
        RefundItemPriceBO modifyRefundMoneyDetail = rmaCoreAmountCalculationHelper.getModifyRefundMoneyDetail(rmaType, orderItem, rmaOrderModifyDto.getReturnNum());
        // 售后单详情
        RmaOrderDetail rmaOrderDetail = rmaOrderDetailService.getRmaOrderDetailByRmaId(rmaOrder.getId());
        if (Objects.isNull(rmaOrderDetail)) {
            rmaOrderDetail = new RmaOrderDetail();
            rmaOrderDetail.setRmaOrderId(rmaOrder.getId());
            rmaOrderDetail.setOrderItemId(rmaOrder.getOrderItemId());
        }

        // 程序计算得出的售后单退款金额
        BigDecimal refundAmount = modifyRefundMoneyDetail.getItemActualRefundAmount().add(modifyRefundMoneyDetail.getRefundableTax()).add(modifyRefundMoneyDetail.getRefundableCustomsTax());
        // 客服编辑后的售后单退款金额
        BigDecimal modifyRefundAmount = rmaOrderModifyDto.getRefundAmount();
        // 前端传递进来的退税费
        BigDecimal refundedTax = rmaOrderModifyDto.getRefundedTax().min(orderItem.getTotalTax());
        // 前端传递进来的退关税
        BigDecimal refundedCustomsTax = rmaOrderModifyDto.getRefundedCustomsTax();

        if (RmaType.rma_change.name().equals(rmaOrderModifyDto.getRmaType())) {
            BigDecimal itemRefundAmount = modifyRefundAmount.subtract(refundedCustomsTax);
            itemRefundAmount = itemRefundAmount.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : itemRefundAmount;

            rmaOrderDetail.setReturnNum(rmaOrderModifyDto.getReturnNum());
            rmaOrderDetail.setRefundAmount(modifyRefundAmount);
            rmaOrderDetail.setItemActualRefundAmount(itemRefundAmount);
            rmaOrderDetail.setItemRefundableAmount(itemRefundAmount);
            rmaOrderDetail.setReturnTaxAmount(BigDecimal.ZERO);
            rmaOrderDetail.setDiscountAmount(BigDecimal.ZERO);
            rmaOrderDetail.setRefundCustomsTaxAmount(refundedCustomsTax);
            rmaOrder.setRefundAmount(modifyRefundAmount.floatValue());
        } else {
            if (modifyRefundAmount.compareTo(refundAmount) != 0) {
                // 商品实退金额
                BigDecimal itemActualRefundAmount = modifyRefundAmount.subtract(refundedTax).subtract(refundedCustomsTax);
                rmaOrderDetail.setReturnTaxAmount(refundedTax);
                rmaOrderDetail.setRefundCustomsTaxAmount(refundedCustomsTax);
                rmaOrderDetail.setDiscountAmount(modifyRefundMoneyDetail.getDiscountDeductionAmount());
                rmaOrderDetail.setItemRefundableAmount(new BigDecimal(String.valueOf(orderItem.getPrice())).multiply(new BigDecimal(String.valueOf(rmaOrderModifyDto.getReturnNum()))));
                rmaOrderDetail.setItemActualRefundAmount(itemActualRefundAmount);
                rmaOrderDetail.setReturnNum(rmaOrderModifyDto.getReturnNum());
                rmaOrderDetail.setRefundAmount(modifyRefundAmount);
                rmaOrder.setRefundAmount(modifyRefundAmount.floatValue());
            } else {
                rmaOrderDetail.setReturnTaxAmount(modifyRefundMoneyDetail.getRefundableTax());
                rmaOrderDetail.setRefundCustomsTaxAmount(modifyRefundMoneyDetail.getRefundableCustomsTax());
                rmaOrderDetail.setDiscountAmount(modifyRefundMoneyDetail.getDiscountDeductionAmount());
                rmaOrderDetail.setItemRefundableAmount(new BigDecimal(String.valueOf(orderItem.getPrice())).multiply(new BigDecimal(String.valueOf(rmaOrderModifyDto.getReturnNum()))));
                rmaOrderDetail.setItemActualRefundAmount(modifyRefundMoneyDetail.getItemActualRefundAmount());
                rmaOrderDetail.setReturnNum(rmaOrderModifyDto.getReturnNum());
                rmaOrderDetail.setRefundAmount(refundAmount);
                rmaOrder.setRefundAmount(refundAmount.floatValue());
            }
        }

        // 重置售后单
        rmaOrder.setRmaType(rmaOrderModifyDto.getRmaType());
        rmaOrder.setQuantity(rmaOrderModifyDto.getReturnNum());
        rmaOrder.setRmaMainDuty(rmaOrderModifyDto.getRmaMainDuty());
        rmaOrder.setAdminReason(rmaOrderModifyDto.getAdminReason());
        rmaOrder.setAdminRemark(rmaOrderModifyDto.getAdminRemark());
        rmaOrder.setNeedReturn(rmaOrderModifyDto.getNeedReturn());
        rmaOrder.setModifyTime(LocalDateTime.now());

        // 重置售后库存
        RmaItemStock rmaItemStock = rmaItemStockService.getByOrderIdAndItemId(rmaOrder.getOrderId(), rmaOrder.getOrderItemId());
        int remainCount;
        int returnCount;
        if (rmaOrder.isClose()) {
            remainCount = rmaOrderModifyDto.getReturnNum() + rmaItemStock.getRemainCount();
            remainCount = Math.min(remainCount, rmaItemStock.getBuyCount());

            returnCount = rmaItemStock.getReturnCount() - rmaOrderModifyDto.getReturnNum();
            returnCount = Math.max(returnCount, 0);
        } else {
            returnCount = rmaOrderModifyDto.getReturnNum().equals(rmaItemStock.getReturnCount()) ? rmaItemStock.getReturnCount() : rmaOrderModifyDto.getReturnNum();
            remainCount = rmaItemStock.getBuyCount() - returnCount;
        }

        rmaItemStock.setReturnCount(returnCount);
        rmaItemStock.setRemainCount(remainCount);

        // psp 终止权益处理
        handlePspEquity(rmaOrderModifyDto, rmaOrder);

        rmaOrderService.update(rmaOrder, rmaOrderDetail, rmaItemStock);

        // 仅修改adminReason需要修改挽回人
        if (!StringUtils.equals(rmaOrderModifyDto.getAdminReason(), adminReason)) {
            // 更新订单下所有售后单挽回人
            this.updateRmaOperatorByOrderId(oaUserInfo.getName(), rmaOrder.getOrderId());
        }
        // 推送售后变更信息
        rmaOrderMessageSendHelper.sendRefundStateChangeMessage(rmaOrder);

        // 同步售后单数据至oms
        storeDataSyncOmsMessageSendHelper.sendRmaOrderChangeSyncOmsMessage(rmaOrder, rmaOrderDetail, Boolean.FALSE);
    }

    /**
     * psp权益订单售后处理
     *
     * @param rmaOrderModifyDto
     * @param rmaOrder
     */
    private void handlePspEquity(RmaOrderModifyDTO rmaOrderModifyDto, RmaOrder rmaOrder) {
        Order order = orderService.getById(rmaOrder.getOrderId());
        if (!order.isMark(OrderBizMarkConstant.psp_order_mark)) {
            return;
        }
        // psp 终止权益标识
        rmaOrder.setTerminationEquity(rmaOrderModifyDto.getTerminationEquity());
        // 二次编辑终止权益
        if (RmaState.success.getCode() == rmaOrder.getState() && Boolean.TRUE.equals(rmaOrderModifyDto.getTerminationEquity())) {
            pspEquityHelper.terminationEquity(rmaOrder);
        }
    }

    /**
     * 售后订单编辑检查
     *
     * @param rmaOrderModifyDto
     * @param rmaOrder
     */
    private void rmaOrderModifyCheck(RmaOrderModifyDTO rmaOrderModifyDto, RmaOrder rmaOrder, OrderItem orderItem) {
        // 校验售后类型
        RmaType rmaType = RmaType.parse(rmaOrderModifyDto.getRmaType());
        if (Objects.isNull(rmaType)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 客服备注长度校验
        String adminRemark = rmaOrderModifyDto.getAdminRemark();
        if (StringUtil.isNotBlank(adminRemark) && adminRemark.length() > 300) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        Integer returnNum = rmaOrderModifyDto.getReturnNum();
        Integer buyNum = orderItem.getNumber();
        if (returnNum > buyNum) {
            throw new InstaException(-1, "售后数量不能超过商品购买数量");
        }

        // 订单支付金额校验
        OrderPayment orderPayment = orderPaymentService.getByOrder(rmaOrder.getOrderId());
        BigDecimal refundAmount = rmaOrderModifyDto.getRefundAmount();
        BigDecimal orderPayAmount = new BigDecimal(String.valueOf(orderPayment.getTotalPayPrice().getAmount()));
        if (orderPayAmount.compareTo(refundAmount) < 0) {
            throw new InstaException(-1, "售后退款金额不能超过订单总支付金额");
        }

        // 订单总退款金额校验
        if (!RmaType.rma_change.name().equals(rmaOrderModifyDto.getRmaType())) {
            List<RmaOrder> orderList = rmaOrderService.listByOrderId(rmaOrder.getOrderId());
            // 有效售后单
            List<RmaOrder> efficientRmaOrderList = orderList.stream().filter(returnOrder -> !rmaOrder.getId().equals(returnOrder.getId()) && Lists.newArrayList(RmaType.rma_refund, RmaType.rma_return).contains(returnOrder.rmaType()) && !Lists.newArrayList(RmaState.cancelled, RmaState.rejected).contains(returnOrder.rmaState())).collect(Collectors.toList());
            // 订单已退款金额
            BigDecimal orderRefundedAmount = efficientRmaOrderList.stream().map(r -> new BigDecimal(String.valueOf(r.getRefundAmount()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 订单已退总金额
            BigDecimal orderRefundedTotalAmount = orderRefundedAmount.add(refundAmount);
            if (orderPayAmount.compareTo(orderRefundedTotalAmount) < 0) {
                throw new InstaException(-1, "订单总退款金额不能超过订单总支付金额");
            }
        }

        // 应退税费校验
        BigDecimal refundedTax = rmaOrderModifyDto.getRefundedTax();
        BigDecimal totalTax = orderItem.getTotalTax();
        if (Objects.nonNull(refundedTax) && refundedTax.compareTo(totalTax) > 0) {
            throw new InstaException(-1, "退税费不可大于售后商品实收总税费");
        }

        // 退关税校验
        BigDecimal refundedCustomsTax = rmaOrderModifyDto.getRefundedCustomsTax();
        // 商品最大可退关税
        BigDecimal refundableMaxCustomsTax = Optional.ofNullable(orderItemCustomsTaxRateService.getCustomsTaxByOrderItemId(orderItem.getId())).map(orderItemCustomsTaxRate -> new BigDecimal(String.valueOf(orderItemCustomsTaxRate.getTotalTax()))).orElse(BigDecimal.ZERO);
        if (Objects.nonNull(refundedCustomsTax) && refundedCustomsTax.compareTo(refundableMaxCustomsTax) > 0) {
            throw new InstaException(-1, "退关税不可大于售后商品实收总关税");
        }
    }

    /**
     * 获取售后申请前置页所需数据
     *
     * @param rmaApplyPreViewDTO
     * @return
     */
    public RefundPreViewRespBo toRefundCommonPreView(RmaApplyPreViewDTO rmaApplyPreViewDTO) {
        Order order = orderService.getByOrderNumber(rmaApplyPreViewDTO.getOrderNo());
        OrderItem orderItem = orderItemService.getById(rmaApplyPreViewDTO.getOrderItemId());
        Integer orderItemState = orderItem.getState();
        // 基础参数校验
        if (Objects.isNull(order) || Objects.isNull(orderItem) || !Integer.valueOf(OrderItemState.normal.getCode()).equals(orderItemState)) {
            throw new InstaException(RmaErrorCode.ConditionNotMetException);
        }
        // 售后类型校验
        List<String> rmaTypes = getRmaType(order, orderItem);
        if (CollectionUtils.isEmpty(rmaTypes)) {
            throw new InstaException(RmaErrorCode.ConditionNotMetException);
        }
        // 查询售后商品对应的产品
        Product product = productService.getById(orderItem.getProduct());

        RefundPreViewRespBo preViewRespBo = new RefundPreViewRespBo();
        preViewRespBo.setOrderNo(order.getOrderNumber());
        preViewRespBo.setOrderItemId(orderItem.getId());
        List<RefundTypeCheckBo> refundTypeCheckBos = Lists.newArrayList();
        rmaTypes.stream().forEach(rmaType -> {
            RefundTypeCheckBo refundTypeCheckBo = new RefundTypeCheckBo();
            refundTypeCheckBo.setKey(rmaType);
            // 售后原因
            List<RmaReasonOption> reasonOptionList = rmaReasonOptionService.getByType(RmaType.parse(rmaType));
            reasonOptionList = reasonOptionList
                    .stream()
                    .filter(o -> !o.getDisabled())
                    .filter(RmaReasonOption::getCustomerEnable)
                    .collect(Collectors.toList());
            refundTypeCheckBo.setRefundReasonList(reasonOptionList);

            // 仅退款-协商选项按钮
            if (RmaType.rma_refund.name().equals(rmaType)) {
                refundTypeCheckBo.setButtonList(RefundButtonBO.buildRefundNegotiationOptions(InstaLanguage.parse(rmaApplyPreViewDTO.getLanguage())));
            }
            refundTypeCheckBos.add(refundTypeCheckBo);
        });
        preViewRespBo.setFrontRefundTypeList(refundTypeCheckBos);
        preViewRespBo.setCloudSubscribeItem(product.isCloudSubscribeItem());

        return preViewRespBo;
    }

    /**
     * 获取售后类型
     *
     * @param order
     * @param orderItem
     * @return
     */
    public List<String> getRmaType(Order order, OrderItem orderItem) {
        // 售后仅退款支持的订单状态
        List<OrderState> rmaRefundOrderStateList = Lists.newArrayList(OrderState.payed, OrderState.prepared);
        // 售后退货退款or换货支持的订单状态
        List<OrderState> rmaReturnOrderStateList = Lists.newArrayList(OrderState.on_delivery, OrderState.success);
        OrderState orderState = order.orderState();
        Integer deliveryState = orderItem.getDeliveryState();

        // 售后类型
        List<String> rmaTypes = Lists.newArrayList();
        if (Product.INSURANCE_SERVICE_PRODUCT.contains(orderItem.getProduct())
                || Product.cloudProductId.equals(orderItem.getProduct())
                || Product.PSP_CLOUD_ID.equals(orderItem.getProduct())
        ) {
            rmaTypes.add(RmaType.rma_refund.name());
        } else if (orderState.equals(OrderState.part_delivery)) {
            rmaTypes = deliveryState != null && OrderItemState.on_delivery.getCode() == deliveryState ? Lists.newArrayList(RmaType.rma_return.name(), RmaType.rma_change.name()) : Lists.newArrayList(RmaType.rma_refund.name());
        } else if (rmaRefundOrderStateList.contains(orderState)) {
            rmaTypes.add(RmaType.rma_refund.name());
        } else if (rmaReturnOrderStateList.contains(orderState)) {
            rmaTypes.add(RmaType.rma_return.name());
            rmaTypes.add(RmaType.rma_change.name());
        }
        return rmaTypes;
    }

    /**
     * 待用户寄回
     *
     * @param rmaOrderId
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void waitForCustomerToShip(Integer rmaOrderId) {
        RmaOrder rmaOrder = rmaOrderService.getById(rmaOrderId);
        if (Objects.isNull(rmaOrder)) {
            throw new InstaException(RmaErrorCode.RmaNotFoundException);
        }
        rmaOrder.setState(RmaState.pending_for_customer_send.getCode());
        rmaOrder.setModifyTime(LocalDateTime.now());
        rmaOrder.setNeedReturn(true);
        if (rmaOrderService.updateById(rmaOrder)) {
            rmaOrderMessageSendHelper.sendRefundStateChangeMessage(rmaOrder);
            // 同步售后单数据至oms
            storeDataSyncOmsMessageSendHelper.sendRmaOrderChangeSyncOmsMessage(rmaOrder, Boolean.FALSE);
        }
    }

    /**
     * 待商家确认收货
     *
     * @param rmaOrderId
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void waitForMerchantToConfirm(Integer rmaOrderId) {
        RmaOrder rmaOrder = rmaOrderService.getById(rmaOrderId);
        if (Objects.isNull(rmaOrder)) {
            throw new InstaException(RmaErrorCode.RmaNotFoundException);
        }

        rmaOrder.setState(RmaState.pending_for_receive.getCode());
        rmaOrder.setModifyTime(LocalDateTime.now());
        if (rmaOrderService.updateById(rmaOrder)) {
            rmaOrderMessageSendHelper.sendRefundStateChangeMessage(rmaOrder);
            // 同步售后单数据至oms
            storeDataSyncOmsMessageSendHelper.sendRmaOrderChangeSyncOmsMessage(rmaOrder, Boolean.FALSE);
        }
    }

    /**
     * 同步售后单
     *
     * @param rmaInfoDto
     */
    public void syncRmaOrders(RmaInfoDTO rmaInfoDto) {
        List<Integer> rmaIds = rmaInfoDto.getRmaIds();
        // 获取需被同步的售后单列表
        List<RmaOrder> rmaOrderList = rmaOrderService.listByRmaIds(rmaIds);
        if (CollectionUtils.isEmpty(rmaOrderList)) {
            throw new InstaException(-1, "需同步的售后单不存在");
        }

        // 判断是否为不同类型的售后单
        String rmaType = rmaInfoDto.getRmaType();
        List<String> rmaTypes = rmaOrderList.stream().map(RmaOrder::getRmaType).distinct().collect(Collectors.toList());
        rmaTypes.remove(rmaType);
        if (CollectionUtils.isNotEmpty(rmaTypes)) {
            throw new InstaException(-1, "不同售后类型的售后单不允许同步！");
        }
        // 售后类型
        RmaType returnType = RmaType.parse(rmaType);

        // 需同步的售后状态
        RmaState rmaState = RmaState.parse(rmaInfoDto.getState());

        // 被同步的售后单状态检查
        List<String> rmaOrderEndList = rmaOrderList.stream()
                .filter(rmaOrder -> RmaState.isCloseNode(rmaOrder.rmaState()) && RmaState.isCloseNode(rmaState))
                .map(RmaOrder::getRmaNumber)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rmaOrderEndList)) {
            throw new InstaException(-1, String.format("%s 需同步的售后单都已处于[取消或拒绝]状态，不可再重复流转状态.", StringUtils.join(rmaOrderEndList)));
        }

        // 获取需被同步的售后物流信息
        List<RmaDelivery> rmaDeliveryList = rmaDeliveryService.listByRmaOrderIds(rmaIds);

        Map<Integer, RmaDelivery> rmaDeliveryMap = Optional.ofNullable(rmaDeliveryList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .collect(Collectors.toMap(RmaDelivery::getRmaId, rmaDelivery -> rmaDelivery)))
                .orElse(new HashMap<>());

        // 需更新的售后物流列表
        List<RmaDelivery> updateRmaDeliveryList = Lists.newArrayList();
        // 需要新增的售后物流列表
        List<RmaDelivery> insertRmaDeliveryList = Lists.newArrayList();
        // 是否售后单完结节点
        boolean isOrderEndNode = RmaState.isOrderEndState(rmaState);
        rmaOrderList.stream()
                .forEach(rmaOrder -> {
                    // 设置售后单基础信息
                    if (StringUtils.isNotBlank(rmaInfoDto.getAdminReason())) {
                        rmaOrder.setAdminReason(rmaInfoDto.getAdminReason());
                    }
                    if (StringUtils.isNotBlank(rmaInfoDto.getAdminRemark())) {
                        rmaOrder.setAdminRemark(rmaInfoDto.getAdminRemark());
                    }
                    if (Objects.nonNull(rmaInfoDto.getNeedReturn())) {
                        rmaOrder.setNeedReturn(rmaInfoDto.getNeedReturn());
                    }
                    if (Objects.nonNull(rmaInfoDto.getRmaMainDuty())) {
                        rmaOrder.setRmaMainDuty(rmaInfoDto.getRmaMainDuty());
                    }

                    if (!isOrderEndNode) {
                        rmaOrder.setState(rmaInfoDto.getState());
                    }

                    if (RmaType.isNeedLogisticsType(returnType)) {
                        rmaDeliverySetUp(rmaInfoDto, rmaDeliveryMap, updateRmaDeliveryList, insertRmaDeliveryList, rmaOrder);
                    }
                    rmaOrder.setModifyTime(LocalDateTime.now());
                });
        // 批量同步
        this.batchSyncRmaOrders(rmaOrderList, updateRmaDeliveryList, insertRmaDeliveryList, rmaInfoDto, isOrderEndNode);
    }

    /**
     * 设置售后发货信息
     *
     * @param rmaInfoDto
     * @param rmaDeliveryMap
     * @param updateRmaDeliveryList
     * @param insertRmaDeliveryList
     * @param rmaOrder
     */
    private void rmaDeliverySetUp(RmaInfoDTO rmaInfoDto, Map<Integer, RmaDelivery> rmaDeliveryMap, List<RmaDelivery> updateRmaDeliveryList, List<RmaDelivery> insertRmaDeliveryList, RmaOrder rmaOrder) {
        // 设置售后单退货物流信息
        RmaDelivery rmaDelivery = null;
        if (rmaDeliveryMap.containsKey(rmaOrder.getId())) {
            if (StringUtils.isNotBlank(rmaInfoDto.getExpressFromCompany())
                    || StringUtils.isNotBlank(rmaInfoDto.getExpressFromNumber())
                    || StringUtils.isNotBlank(rmaInfoDto.getExpressToCompany())
                    || StringUtils.isNotBlank(rmaInfoDto.getExpressToNumber())
            ) {
                rmaDelivery = rmaDeliveryMap.get(rmaOrder.getId());
                rmaDelivery.setExpressFromCompany(rmaInfoDto.getExpressFromCompany());
                rmaDelivery.setExpressFromNumber(rmaInfoDto.getExpressFromNumber());
                rmaDelivery.setExpressToCompany(rmaInfoDto.getExpressToCompany());
                rmaDelivery.setExpressToNumber(rmaInfoDto.getExpressToNumber());
                updateRmaDeliveryList.add(rmaDelivery);
            }
        } else {
            if (StringUtils.isNotBlank(rmaInfoDto.getExpressFromCompany())
                    || StringUtils.isNotBlank(rmaInfoDto.getExpressFromNumber())
                    || StringUtils.isNotBlank(rmaInfoDto.getExpressToCompany())
                    || StringUtils.isNotBlank(rmaInfoDto.getExpressToNumber())
            ) {
                rmaDelivery = new RmaDelivery();
                rmaDelivery.setRmaId(rmaOrder.getId());
                rmaDelivery.setExpressFromCompany(rmaInfoDto.getExpressFromCompany());
                rmaDelivery.setExpressFromNumber(rmaInfoDto.getExpressFromNumber());
                rmaDelivery.setExpressToCompany(rmaInfoDto.getExpressToCompany());
                rmaDelivery.setExpressToNumber(rmaInfoDto.getExpressToNumber());
                insertRmaDeliveryList.add(rmaDelivery);
            }
        }
    }

    /**
     * 批量同步售后单
     *
     * @param rmaOrderList
     * @param rmaDeliveryUpdateList
     * @param rmaDeliveryInsertList
     * @param rmaInfoDto
     * @param isOrderEndNode
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchSyncRmaOrders(List<RmaOrder> rmaOrderList, List<RmaDelivery> rmaDeliveryUpdateList, List<RmaDelivery> rmaDeliveryInsertList, RmaInfoDTO rmaInfoDto, Boolean isOrderEndNode) {
        if (CollectionUtils.isEmpty(rmaOrderList)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(rmaDeliveryInsertList)) {
            // 批量插入售后单退货物流信息
            rmaDeliveryService.saveBatch(rmaDeliveryInsertList);
        }

        if (CollectionUtils.isNotEmpty(rmaDeliveryUpdateList)) {
            // 批量更新售后单退货物流信息
            rmaDeliveryService.updateBatchById(rmaDeliveryUpdateList);
        }

        if (CollectionUtils.isNotEmpty(rmaOrderList)) {
            // 批量更新售后单基础信息
            rmaOrderService.updateBatchById(rmaOrderList);
        }

        if (isOrderEndNode) {
            // 批量同步退款信息至管易
            batchSyncRefundToGuanYi(rmaOrderList, rmaInfoDto);
        } else {
            // 同步非完结状态的售后单状态变更情况
            rmaOrderList.stream()
                    .forEach(rmaOrder -> {
                        rmaOrderMessageSendHelper.sendRefundStateChangeMessage(rmaOrder);
                        // 同步售后单数据至oms
                        storeDataSyncOmsMessageSendHelper.sendRmaOrderChangeSyncOmsMessage(rmaOrder, Boolean.FALSE);
                    });
        }
    }

    /**
     * 批量同步退款信息至管易
     *
     * @param rmaOrders
     * @param rmaInfoDTO
     */
    private void batchSyncRefundToGuanYi(Collection<RmaOrder> rmaOrders, RmaInfoDTO rmaInfoDTO) {
        RmaState rmaState = RmaState.parse(rmaInfoDTO.getState());
        rmaOrders.stream().forEach(rmaOrder -> {
            switch (rmaState) {
                case rejected:
                    this.reject(rmaOrder.getId());
                    break;

                case cancelled:
                    this.cancel(rmaOrder.getId());
                    break;

                case success:
                    this.complete(rmaOrder.getId());
                    break;

                default:
                    break;
            }
        });
    }

    /**
     * 售后拒绝
     *
     * @param rmaId
     */
    public void reject(Integer rmaId) {
        if (Objects.isNull(rmaId)) {
            throw new InstaException(-1, "售后单ID不能为空！");
        }
        // 1、售后拒绝
        RmaOrder rmaOrder = rmaOrderService.reject(rmaId);
        // 2、售后完结后处理
        refundFinishPreHandle(rmaOrder);
    }

    /**
     * 售后取消
     *
     * @param rmaId
     */
    public void cancel(Integer rmaId) {
        if (Objects.isNull(rmaId)) {
            throw new InstaException(-1, "售后单ID不能为空！");
        }
        // 1、取消售后单
        RmaOrder rmaOrder = rmaOrderService.cancel(rmaId);
        // 2、售后完结后处理
        refundFinishPreHandle(rmaOrder);
    }

    /**
     * 售后成功
     *
     * @param rmaId
     */
    public void complete(Integer rmaId) {
        if (Objects.isNull(rmaId)) {
            throw new InstaException(-1, "售后单ID不能为空！");
        }
        // 1、售后成功
        RmaOrder rmaOrder = rmaOrderService.setSuccess(rmaId);
        // 2、售后完结后处理
        refundFinishPreHandle(rmaOrder);
    }

    /**
     * 售后单完结事件-前置处理
     *
     * @param rmaOrder
     */
    private void refundFinishPreHandle(RmaOrder rmaOrder) {
        // 1、同步OMS
        storeDataSyncOmsMessageSendHelper.sendRmaOrderChangeSyncOmsMessage(rmaOrder, Boolean.FALSE);
        // 2、售后退款成功后置处理
        if (RmaState.success.equals(rmaOrder.rmaState())) {
            // 回收权益
            rmaDownstreamProcessingHelper.handleCloudServiceAfterSales(rmaOrder);
            // 终止psp权益
            pspEquityHelper.terminationEquity(rmaOrder);
            // 2.1、同步forter
            rmaOrderMessageSendHelper.sendRmaOrderSuccessSyncForterMessage(rmaOrder);
            // 2.2、发送售后成功邮件
            BaseRmaEmail rmaEmail = rmaEmailFactory.getSuccessEmail(rmaOrder);
            rmaEmail.doSend(rmaOrder.getContactEmail());
            // 2.3、售后退款完成提醒财务发票冲红
            yiPiaoYunAutoInvoiceService.refundHandleNotify(rmaOrder);
            // 2.4、售后单退款同步Avalara
            avalaraMessageSendHelper.sendStoreOrderRefundMessage(rmaOrder);
            // 2.5、送增云存售后单发送飞书通知告知客服
            rmaSuccessHelper.sendCloudNotify(rmaOrder);
            // 2.6、处理部分发货订单状态流转
            storeOrderDeliveryHelper.handleOrderDeliveryAll(rmaOrder);
        }

        // 3、售后状态变更通知
        rmaOrderMessageSendHelper.sendRefundStateChangeMessage(rmaOrder);
    }

    /**
     * 获取订单商品-产品映射Map
     *
     * @param orderItemList
     * @return
     */
    private Map<Integer, Product> getOrderItemProductMap(List<OrderItem> orderItemList) {
        // 获取商品产品列表
        List<Integer> productIds = orderItemList.stream().map(OrderItem::getProduct).distinct().collect(Collectors.toList());
        // 获取产品列表
        Map<Integer, Product> productMap = productBatchHelper.productUnLimitEnableMapProductIds(productIds);

        return orderItemList.stream().collect(Collectors.toMap(OrderItem::getId, orderItem -> productMap.get(orderItem.getProduct())));
    }

    /**
     * 获取订单商品映射Map
     *
     * @param orderItemList
     * @return
     */
    private Map<Integer, OrderItem> getOrderItemMap(List<OrderItem> orderItemList) {
        return orderItemList.stream().collect(Collectors.toMap(OrderItem::getId, orderItem -> orderItem));
    }

    /**
     * 发送邮件到zendesk系统
     *
     * @param rmaOrder
     */
    private void sendRmaEmail2Zendesk(RmaOrder rmaOrder) {
        // 退换货不再发送提示和确认邮件
        // 发给zendesk系统（<EMAIL>）让客服与用户沟通
        String contactEmail = RmaConstant.RMA_ZENDESK_EMAIL;
        RmaPlatformSource platformSource = RmaPlatformSource.matchCode(rmaOrder.getPlatformSource());
        RmaType rmaType = rmaOrder.rmaType();
        String redisKeyPrefix = getRedisKeyPrefixByType(rmaType);
        Integer orderId = rmaOrder.getOrderId();

        // 用户发起，一定时间内，同一订单，不会再发送同样的协商邮件
        if (!rmaRedisKeyNotExpired(redisKeyPrefix, orderId) && RmaPlatformSource.store_pc.equals(platformSource)) {
            return;
        }

        // 缓存过期时间
        long expireTime = 0L;
        // 用户发起
        if (RmaPlatformSource.store_pc.equals(platformSource)) {
            switch (rmaType) {
                case rma_refund:
                    sendRmaNegotiateEmail(rmaOrder, contactEmail, RmaRefundApplyNegotiateEmail.class);
                    expireTime = RedisKeyConstant.RMA_REFUND_EXPIRE;
                    break;
                case rma_return:
                    sendRmaNegotiateEmail(rmaOrder, contactEmail, RmaReturnApplyNegotiateEmail.class);
                    expireTime = RedisKeyConstant.RMA_RETURN_EXPIRE;
                    break;
                case rma_change:
                    sendRmaNegotiateEmail(rmaOrder, contactEmail, RmaReplacementApplyNegotiateEmail.class);
                    expireTime = RedisKeyConstant.RMA_REPLACEMENT_EXPIRE;
                    break;
                default:
                    break;
            }
            // 设置新的缓存过期时间
            RedisTemplateUtil.setKeyValue(redisKeyPrefix + orderId, orderId, expireTime, TimeUnit.HOURS);
        }

        // 系统云服务退款
        if (RmaPlatformSource.system.equals(platformSource) && RmaType.rma_refund.equals(rmaType)) {
            sendRmaNegotiateEmail(rmaOrder, contactEmail, RmaRefundApplyNegotiateEmail.class);
        }
    }

    /**
     * 发送指定类型的RMA协商邮件
     *
     * @param rmaOrder     售后单
     * @param contactEmail 邮箱
     * @param clazz        邮件类类型
     * @return 对应的缓存过期时间
     */
    private void sendRmaNegotiateEmail(RmaOrder rmaOrder, String contactEmail, Class<? extends BaseRmaEmail> clazz) {
        BaseRmaEmail email = ApplicationContextHolder.getApplicationContext().getBean(clazz);
        email.setRmaOrder(rmaOrder);
        email.doSend(contactEmail);
    }

    /**
     * 检查RMA订单对应的Redis Key是否已过期
     *
     * @param redisKeyPrefix
     * @param orderId
     * @return true表示Key已过期/不存在，可以继续处理；
     * false表示Key未过期 或者 售后类型异常，不继续发送邮件
     */
    private boolean rmaRedisKeyNotExpired(String redisKeyPrefix, Integer orderId) {
        if (StringUtils.isBlank(redisKeyPrefix)) {
            return false;
        }

        // 检查Key是否存在
        String fullKey = redisKeyPrefix + orderId;
        return Objects.isNull(RedisTemplateUtil.getValue(fullKey));
    }

    /**
     * 根据RMA类型获取对应的Redis Key前缀
     */
    private String getRedisKeyPrefixByType(RmaType rmaType) {
        if (rmaType == null) {
            return null;
        }

        switch (rmaType) {
            case rma_refund:
                return RedisKeyConstant.RMA_REFUND_PRE_FIX;
            case rma_return:
                return RedisKeyConstant.RMA_RETURN_PRE_FIX;
            case rma_change:
                return RedisKeyConstant.RMA_REPLACEMENT_PRE_FIX;
            default:
                return null;
        }
    }
}