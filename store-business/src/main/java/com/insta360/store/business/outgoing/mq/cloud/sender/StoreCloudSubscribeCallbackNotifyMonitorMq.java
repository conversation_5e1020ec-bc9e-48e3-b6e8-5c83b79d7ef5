package com.insta360.store.business.outgoing.mq.cloud.sender;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.cloud.dto.CallbackNotifyMonitorDTO;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/27
 */
@Component
public class StoreCloudSubscribeCallbackNotifyMonitorMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudSubscribeCallbackNotifyMonitorMq.class);

    /**
     * 延迟时间
     */
    private static final long DELAY_TIME = 10 * 60 * 1000;

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_callback_monitor, messageType = MessageSenderType.time)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * 发送商城云服务订阅权益下发云存储回调结果通知监控消息
     *
     * @param notifyId    回调通知ID
     * @param subscribeId 订阅ID
     */
    public void sendCallbackNotifyMonitorMessage(Long notifyId, Integer subscribeId) {
        if (Objects.isNull(notifyId) || Objects.isNull(subscribeId)) {
            LOGGER.info("[商城云服务订阅权益下发云存储回调结果通知监控]消息发送失败,参数错误... 订阅ID:{}, 通知ID:{}", subscribeId, notifyId);
            return;
        }

        CallbackNotifyMonitorDTO callbackNotifyMonitorDto = new CallbackNotifyMonitorDTO();
        callbackNotifyMonitorDto.setNotifyId(notifyId);
        callbackNotifyMonitorDto.setSubscribeId(subscribeId);
        LOGGER.info("[商城云服务订阅权益下发云存储回调结果通知监控]消息发送处理... callbackNotifyMonitorDto:{}", JSON.toJSONString(callbackNotifyMonitorDto));
        String messageId = rocketTcpMessageSender.sendDelayMessage(JSON.toJSONString(callbackNotifyMonitorDto), DELAY_TIME);
        LOGGER.info("[商城云服务订阅权益下发云存储回调结果通知监控]消息发送成功... callbackNotifyMonitorDto:{},messageId:{}", JSON.toJSONString(callbackNotifyMonitorDto), messageId);
        MqUtils.isBlankMessageIdHandle(messageId, this, callbackNotifyMonitorDto);
        if (StringUtils.isBlank(messageId)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅权益下发云存储回调结果通知监控]消息发送失败...  订阅ID:{%s}, 通知ID:{%s}", subscribeId, notifyId), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
    }
}
