package com.insta360.store.business.payment.service.impl.handler;

import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.lib.ocean.config.OceanPaymentConfig;
import com.insta360.store.business.payment.lib.ocean.config.normal.OceanUsRepairmentCreditCardConfig;
import com.insta360.store.business.payment.lib.ocean.request.BaseOceanPaymentRequest;
import com.insta360.store.business.payment.lib.ocean.request.repairment.CreateRepairmentIndirectUSPaymentRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2024/03/07
 * @Description:
 */
@Scope("prototype")
@Component
public class RepairmentOPIndirectUSPaymentHandler extends BaseOceanPaymentHandler {

    @Autowired
    OceanUsRepairmentCreditCardConfig oceanUsRepairmentCreditCardConfig;

    @Override
    public PaymentChannel getPaymentChannel() {
        return PaymentChannel.repairment_credit_card_indirect_us;
    }

    @Override
    protected BaseOceanPaymentRequest newPaymentRequest(PaymentExtra paymentExtra) {
        return new CreateRepairmentIndirectUSPaymentRequest(getOceanPaymentConfig());
    }

    @Override
    public OceanPaymentConfig getOceanPaymentConfig() {
        return oceanUsRepairmentCreditCardConfig;
    }

}
