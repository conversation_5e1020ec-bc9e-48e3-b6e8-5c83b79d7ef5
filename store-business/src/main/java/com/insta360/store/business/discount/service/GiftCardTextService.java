package com.insta360.store.business.discount.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.discount.dto.GiftCardTextResult;
import com.insta360.store.business.discount.enums.old.ConditionType;
import com.insta360.store.business.discount.enums.old.DiscountTextType;
import com.insta360.store.business.discount.enums.old.DiscountWay;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.model.GiftCardText;

/**
 * @Author: hyc
 * @Date: 2019/2/25
 * @Description: 代金券文案
 */
public interface GiftCardTextService extends BaseService<GiftCardText> {

    /**
     * 根据文案类型和优惠方式获取
     *
     * @param textType
     * @param discountWay
     * @param language
     * @return
     */
    GiftCardText getText(DiscountTextType textType, DiscountWay discountWay, InstaLanguage language);

    /**
     * 根据生效条件类型获取
     *
     * @param conditionType
     * @param language
     * @return
     */
    GiftCardText getText(ConditionType conditionType, InstaLanguage language);

    /**
     * 获取代金券文案
     *
     * @param giftCard
     * @param language
     * @return
     */
    GiftCardTextResult getGiftCardText(GiftCard giftCard, InstaLanguage language);
}
