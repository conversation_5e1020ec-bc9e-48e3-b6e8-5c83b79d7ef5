package com.insta360.store.business.product.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaLanguage;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description: 
 */
@TableName("product_info")
public class ProductInfo extends BaseModel<ProductInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "product")
    private Integer product;

    @TableId(value = "language")
    @TableField(value = "`language`")
    private String language;

    @TableField(value = "`name`")
    private String name;

    private String description;

    private String adaption;

    private String overview;

    @JSONField(name = "packing_list")
    private String packingList;

    @JSONField(name = "technical_parameters")
    private String technicalParameters;

    private String introduction;

    @JSONField(name = "overview_mobile")
    private String overviewMobile;

    @JSONField(name = "packing_list_mobile")
    private String packingListMobile;

    @JSONField(name = "technical_parameters_mobile")
    private String technicalParametersMobile;

    @JSONField(name = "introduction_mobile")
    private String introductionMobile;

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAdaption() {
        return adaption;
    }

    public void setAdaption(String adaption) {
        this.adaption = adaption;
    }

    public String getOverview() {
        return overview;
    }

    public void setOverview(String overview) {
        this.overview = overview;
    }

    public String getPackingList() {
        return packingList;
    }

    public void setPackingList(String packingList) {
        this.packingList = packingList;
    }

    public String getTechnicalParameters() {
        return technicalParameters;
    }

    public void setTechnicalParameters(String technicalParameters) {
        this.technicalParameters = technicalParameters;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getOverviewMobile() {
        return overviewMobile;
    }

    public void setOverviewMobile(String overviewMobile) {
        this.overviewMobile = overviewMobile;
    }

    public String getPackingListMobile() {
        return packingListMobile;
    }

    public void setPackingListMobile(String packingListMobile) {
        this.packingListMobile = packingListMobile;
    }

    public String getTechnicalParametersMobile() {
        return technicalParametersMobile;
    }

    public void setTechnicalParametersMobile(String technicalParametersMobile) {
        this.technicalParametersMobile = technicalParametersMobile;
    }

    public String getIntroductionMobile() {
        return introductionMobile;
    }

    public void setIntroductionMobile(String introductionMobile) {
        this.introductionMobile = introductionMobile;
    }

    @Override
    public String toString() {
        return "ProductInfo{" +
        "product=" + product +
        ", language=" + language +
        ", name=" + name +
        ", description=" + description +
        ", adaption=" + adaption +
        ", overview=" + overview +
        ", packingList=" + packingList +
        ", technicalParameters=" + technicalParameters +
        ", introduction=" + introduction +
        ", overviewMobile=" + overviewMobile +
        ", packingListMobile=" + packingListMobile +
        ", technicalParametersMobile=" + technicalParametersMobile +
        ", introductionMobile=" + introductionMobile +
        "}";
    }

    public InstaLanguage language(){
        return InstaLanguage.parse(language);
    }
}
