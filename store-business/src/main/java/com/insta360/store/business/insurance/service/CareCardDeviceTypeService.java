package com.insta360.store.business.insurance.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.insurance.model.CareCardDeviceType;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-04-25
 * @Description:
 */
public interface CareCardDeviceTypeService extends BaseService<CareCardDeviceType> {

    /**
     * 获取所有的可选产品型号
     *
     * @return
     */
    List<CareCardDeviceType> listDevice();

    /**
     * 通过卡类型查询可选产品型号
     *
     * @param cardType
     * @return
     */
    List<CareCardDeviceType> listDeviceByType(String cardType);
}
