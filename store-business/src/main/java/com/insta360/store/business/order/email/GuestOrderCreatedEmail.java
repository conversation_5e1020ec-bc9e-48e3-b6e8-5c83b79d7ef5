package com.insta360.store.business.order.email;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: hyc
 * @Date: 2019/3/7
 * @Description:
 */
@Scope("prototype")
@Component
public class GuestOrderCreatedEmail extends BaseOrderEmail {

    @Override
    public InstaLanguage getLanguage() {
        return super.getLanguage();
    }

    @Override
    public String getTemplateName() {
        return "store_guest_order_created";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        // 订单号
        templateParams.addBodyParam("order_number", this.getOrderNumber());

        // 订单时间
        templateParams.addBodyParam("order_time", this.getOrderCreateTime());

        // 订单内容
        templateParams.addBodyParam("order_items", this.getOrderItems());

        // 支付信息
        templateParams.addBodyParam("order_payment", this.getOrderPayment());

        // 配送信息
        templateParams.addBodyParam("order_delivery", this.getOrderDelivery());

        // 订单号（RSA加密）
        templateParams.addBodyParam("order_id", this.getOrderNumberByRSA());
    }
}
