package com.insta360.store.business.payment.service;

import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.payment.bo.EntPayInfo;
import com.insta360.store.business.payment.bo.PaymentExtra;

/**
 * @Author: hyc
 * @Date: 2019/2/16
 * @Description:
 */
public interface PaymentService {

    /**
     * 支付订单
     *
     * @param orderId
     * @param payChannel
     * @param paymentExtra
     * @param <T>
     * @return
     */
    <T> T payOrder(Integer orderId, PaymentChannel payChannel, PaymentExtra paymentExtra);

    /**
     * 支付到用户的微信零钱
     *
     * @param entPayInfo
     */
    void payToUserWechatAccount(EntPayInfo entPayInfo);
}
