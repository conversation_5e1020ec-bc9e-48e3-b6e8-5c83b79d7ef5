package com.insta360.store.business.meta.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.meta.model.StoreEmailFailRecord;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * @Author: wbt
 * @Date: 2020/03/03
 * @Description:
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface StoreEmailFailRecordDao extends BaseDao<StoreEmailFailRecord> {

}
