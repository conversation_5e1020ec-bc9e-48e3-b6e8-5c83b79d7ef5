package com.insta360.store.business.discount.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.discount.dao.GiftCardInfoDetailDao;
import com.insta360.store.business.discount.model.GiftCardInfoDetail;
import com.insta360.store.business.discount.service.GiftCardInfoDetailService;
import org.springframework.stereotype.Service;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-09-27
 * @Description:
 */
@Service
public class GiftCardInfoDetailServiceImpl extends BaseServiceImpl<GiftCardInfoDetailDao, GiftCardInfoDetail> implements GiftCardInfoDetailService {

    @Override
    public GiftCardInfoDetail getInfo(String infoTag, InstaLanguage language) {
        QueryWrapper<GiftCardInfoDetail> qw = new QueryWrapper<>();
        qw.eq("info_tag", infoTag);
        qw.eq("`language`", language);
        return baseMapper.selectOne(qw);
    }

    @Override
    public GiftCardInfoDetail getInfoDefaultEnglish(String giftTag, InstaLanguage language) {
        GiftCardInfoDetail infoDetail = getInfo(giftTag, language);
        if (infoDetail == null || infoDetail.getContentDetail() == null) {
            infoDetail = getInfo(giftTag, InstaLanguage.en_US);
        }

        return infoDetail;
    }
}
