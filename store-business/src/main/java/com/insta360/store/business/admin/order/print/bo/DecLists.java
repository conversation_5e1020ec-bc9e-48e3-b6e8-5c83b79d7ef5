
package com.insta360.store.business.admin.order.print.bo;

import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element ref="{http://www.chinaport.gov.cn/dec}DecList" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "decList"
})
@XmlRootElement(name = "DecLists", namespace = "http://www.chinaport.gov.cn/dec")
public class DecLists {

    @XmlElement(name = "DecList", namespace = "http://www.chinaport.gov.cn/dec")
    protected List<DecList> decList;

    /**
     * Gets the value of the decList property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the decList property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDecList().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DecList }
     * 
     * 
     */
    public List<DecList> getDecList() {
        if (decList == null) {
            decList = new ArrayList<DecList>();
        }
        return this.decList;
    }

    public void setDecList(List<DecList> decList) {
        this.decList = decList;
    }
}
