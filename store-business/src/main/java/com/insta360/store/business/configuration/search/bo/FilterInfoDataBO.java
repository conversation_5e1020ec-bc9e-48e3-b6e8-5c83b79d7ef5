package com.insta360.store.business.configuration.search.bo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/28
 */
public class FilterInfoDataBO {

    /**
     * adapterTypes
     */
    private List<String> adapterTypes;

    /**
     * accessoriesCategory
     */
    private List<String> accessoriesCategory;

    /**
     * navigationBar
     */
    private List<String> navigationBar;

    /**
     * discountMark
     */
    private Integer discountMark;

    public List<String> getAdapterTypes() {
        return adapterTypes;
    }

    public void setAdapterTypes(List<String> adapterTypes) {
        this.adapterTypes = adapterTypes;
    }

    public List<String> getAccessoriesCategory() {
        return accessoriesCategory;
    }

    public void setAccessoriesCategory(List<String> accessoriesCategory) {
        this.accessoriesCategory = accessoriesCategory;
    }

    public Integer getDiscountMark() {
        return discountMark;
    }

    public void setDiscountMark(Integer discountMark) {
        this.discountMark = discountMark;
    }

    public List<String> getNavigationBar() {
        return navigationBar;
    }

    public void setNavigationBar(List<String> navigationBar) {
        this.navigationBar = navigationBar;
    }

    @Override
    public String toString() {
        return "FilterInfoDataBO{" +
                "adapterTypes=" + adapterTypes +
                ", accessoriesCategory=" + accessoriesCategory +
                ", navigationBar=" + navigationBar +
                ", discountMark=" + discountMark +
                '}';
    }
}
