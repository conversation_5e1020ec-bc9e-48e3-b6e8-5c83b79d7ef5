package com.insta360.store.business.product.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.product.model.ProductCategorySubset;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-08-31
 * @Description:
 */
public interface ProductCategorySubsetService extends BaseService<ProductCategorySubset> {

    /**
     * 获取二级类目排序最新index
     *
     * @return
     */
    ProductCategorySubset getCategorySubsetOrderIndex();

    /**
     * 更新二级类目排序
     *
     * @param categorySubsetList
     */
    void updateCategoryIndex(List<ProductCategorySubset> categorySubsetList);

    /**
     * 根据类目key查询
     *
     * @param categoryKey
     * @return
     */
    ProductCategorySubset getByCategorySubsetKey(String categoryKey);

    /**
     * 根据一级类目ids查询
     *
     * @param categoryMainKeys
     * @return
     */
    List<ProductCategorySubset> listByCategoryMainKeys(List<String> categoryMainKeys);

    /**
     * 根据一级类目key查询对应二级类目信息
     *
     * @param categoryMainKey
     * @return
     */
    List<ProductCategorySubset> listByCategoryMainKey(String categoryMainKey);

    /**
     * 根据二级类目key查询对应一级类目信息
     *
     * @param categorySubList 类别sub集合
     * @return {@link List}<{@link ProductCategorySubset}>
     */
    List<ProductCategorySubset> listByCategorySubsetKeys(List<String> categorySubList);
}
