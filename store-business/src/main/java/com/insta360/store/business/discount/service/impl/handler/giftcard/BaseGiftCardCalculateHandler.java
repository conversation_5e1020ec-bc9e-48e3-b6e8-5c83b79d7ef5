package com.insta360.store.business.discount.service.impl.handler.giftcard;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.bo.GiftItem;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.discount.constant.DiscountCommonConstant;
import com.insta360.store.business.discount.constant.ThresholdMarkType;
import com.insta360.store.business.discount.dto.bo.DiscountCheckResult;
import com.insta360.store.business.discount.enums.PolicyDiscountType;
import com.insta360.store.business.discount.enums.PolicyRuleType;
import com.insta360.store.business.discount.enums.old.DiscountType;
import com.insta360.store.business.discount.exception.DiscountErrorCode;
import com.insta360.store.business.discount.exception.StoreDiscountErrorCode;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.model.GiftCardPolicyItem;
import com.insta360.store.business.discount.model.GiftCardRuleAmount;
import com.insta360.store.business.discount.model.GiftCardThresholdItem;
import com.insta360.store.business.discount.service.impl.factory.TradeDiscountContextFactory;
import com.insta360.store.business.discount.service.impl.handler.BaseTradeDiscountCalculateHandler;
import com.insta360.store.business.discount.service.impl.handler.context.GiftCardContext;
import com.insta360.store.business.discount.service.impl.handler.dto.DiscountCalculationDTO;
import com.insta360.store.business.discount.service.impl.handler.dto.TradeCalculateDTO;
import com.insta360.store.business.discount.service.impl.handler.dto.TradeDiscountGiftItemQueryDTO;
import com.insta360.store.business.discount.service.impl.handler.dto.TradeOrderItemPolicyDTO;
import com.insta360.store.business.discount.service.impl.helper.TradeDiscountCalculateHelper;
import com.insta360.store.business.discount.service.impl.helper.monitor.TradeCodeRuleEarlyWarningHelper;
import com.insta360.store.business.order.model.OrderItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 代金券优惠规则、计算核心处理类
 * @Date 2022/4/20
 */
public abstract class BaseGiftCardCalculateHandler extends BaseTradeDiscountCalculateHandler<GiftCardContext> {

    private static final Logger logger = LoggerFactory.getLogger(BaseGiftCardCalculateHandler.class);

    @Autowired
    private TradeDiscountContextFactory tradeDiscountContextFactory;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    TradeDiscountCalculateHelper tradeDiscountCalculateHelper;

    @Autowired
    TradeCodeRuleEarlyWarningHelper tradeCodeRuleEarlyWarningHelper;

    /**
     * 初始化代金券核心计算校验上下文
     *
     * @param giftCard
     */
    public void init(GiftCard giftCard) {
        GiftCardContext giftCardContext = tradeDiscountContextFactory.getTradeDiscountContext(giftCard);
        super.init(giftCardContext);
    }

    /**
     * 订单维度优惠 订单购买套餐政策匹配
     *
     * @param orderItemList
     * @param instaCountry
     * @return
     */
    protected TradeCalculateDTO matchOrderDimensionPolicy(List<OrderItem> orderItemList, InstaCountry instaCountry) {
        //取当前代金券的政策池中的第一条
        GiftCardContext.GiftCardPolicyBean giftCardPolicyBean = discountContext.getPolicyList().stream().findFirst().orElseThrow(() -> new InstaException(DiscountErrorCode.InvalidGiftCardException));
        PolicyDiscountType policyDiscountType = PolicyDiscountType.matchCode(giftCardPolicyBean.getPolicyDiscountType());
        Map<String, Float> fixedDiscountAmountMap = Maps.newHashMap();
        switch (policyDiscountType) {
            case AMOUNT_RATIO:
                if (Objects.isNull(giftCardPolicyBean.getAmountProportion()) || giftCardPolicyBean.getAmountProportion() < DiscountCommonConstant.zero) {
                    throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
                }
                break;
            case AMOUNT_FIXED:
                if (CollectionUtils.isEmpty(giftCardPolicyBean.getGiftCardRuleAmountList())) {
                    throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
                }
                //当前政策减价金额Map
                fixedDiscountAmountMap = Optional.ofNullable(giftCardPolicyBean.getGiftCardRuleAmountList())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(giftCardRuleAmount ->
                                Objects.nonNull(giftCardRuleAmount.getCurrency())
                                        && Objects.nonNull(giftCardRuleAmount.getAmount())
                                        && giftCardRuleAmount.getAmount() > DiscountCommonConstant.zero)
                        .collect(
                                Collectors.toMap(GiftCardRuleAmount::getCurrency, GiftCardRuleAmount::getAmount)
                        );

                //支付币种
                String paymentCurrency = orderItemList.get(0).getCurrency();
                if (MapUtils.isNotEmpty(fixedDiscountAmountMap) && !fixedDiscountAmountMap.containsKey(paymentCurrency)) {
                    throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
                }
                break;
            default:
                throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
        }

        //套餐价格配置（符合代金券政策的订单商品所属套餐）
        List<Integer> orderCommodityIdList = orderItemList.stream().map(OrderItem::getCommodity).collect(Collectors.toList());
        List<CommodityPrice> orderCommodityPriceList = commodityPriceService.getPriceByCommodityIds(orderCommodityIdList, instaCountry);
        Map<Integer, CommodityPrice> orderCommodityPriceMap = orderCommodityPriceList
                .stream()
                .collect(Collectors.toMap(CommodityPrice::getCommodityId, commodityPrice -> commodityPrice));

        Map<String, Float> finalFixedDiscountAmountMap = fixedDiscountAmountMap;
        List<TradeOrderItemPolicyDTO> tradeOrderItemPolicyDTOList = orderItemList.stream().map(orderItem -> {
            TradeOrderItemPolicyDTO orderItemPolicyDTO = new TradeOrderItemPolicyDTO();
            orderItemPolicyDTO.setPolicyId(giftCardPolicyBean.getId());
            orderItemPolicyDTO.setOrderItem(orderItem);
            orderItemPolicyDTO.setDiscountRate(giftCardPolicyBean.getAmountProportion());
            orderItemPolicyDTO.setPolicyDiscountType(PolicyDiscountType.matchCode(giftCardPolicyBean.getPolicyDiscountType()));
            orderItemPolicyDTO.setFixedDiscountAmountMap(finalFixedDiscountAmountMap);
            orderItemPolicyDTO.setCommodityPrice(orderCommodityPriceMap.get(orderItem.getCommodity()));
            orderItemPolicyDTO.setMaxDiscountNum(giftCardPolicyBean.getDiscountMaxNumber());
            return orderItemPolicyDTO;
        }).collect(Collectors.toList());


        TradeCalculateDTO tradeCalculateDTO = new TradeCalculateDTO();
        tradeCalculateDTO.setTradeDiscountType(DiscountType.parse(discountContext.getDiscountType()));
        tradeCalculateDTO.setTradeOrderItemPolicyList(tradeOrderItemPolicyDTOList);
        tradeCalculateDTO.setPlaceOrderCountry(instaCountry);
        tradeCalculateDTO.setTradeCode(discountContext.getTradeCode());

        //政策优惠条件风险检查
        tradeCodeRuleEarlyWarningHelper.policyDataEarlyWarning(tradeCalculateDTO);
        return tradeCalculateDTO;
    }

    /**
     * 套餐维度优惠 订单购买套餐政策匹配
     *
     * @param orderItemList
     * @param instaCountry
     * @return
     */
    protected TradeCalculateDTO matchCommodityDimensionPolicy(List<OrderItem> orderItemList, InstaCountry instaCountry) {
        List<GiftCardContext.GiftCardPolicyBean> giftCardPolicyBeanList = discountContext.getPolicyList();
        //每个下单套餐只匹配一条政策，若存在多条政策符合条件则进行覆盖
        Map<Integer, GiftCardContext.GiftCardPolicyBean> commodityPolicyMap = Maps.newHashMap();
        for (OrderItem orderItem : orderItemList) {
            for (GiftCardContext.GiftCardPolicyBean giftCardPolicyBean : giftCardPolicyBeanList) {
                //若政策没指定优惠产品 or 套餐 则忽略该政策
                if (Integer.valueOf(PolicyRuleType.NO_RULES.code).equals(giftCardPolicyBean.getPolicyRuleType())) {
                    continue;
                }

                PolicyDiscountType policyDiscountType = PolicyDiscountType.matchCode(giftCardPolicyBean.getPolicyDiscountType());
                if (policyDiscountType.equals(PolicyDiscountType.AMOUNT_RATIO)
                        && (Objects.isNull(giftCardPolicyBean.getAmountProportion()) || giftCardPolicyBean.getAmountProportion() < DiscountCommonConstant.zero)
                ) {
                    continue;
                }
                if (policyDiscountType.equals(PolicyDiscountType.AMOUNT_FIXED) && CollectionUtils.isEmpty(giftCardPolicyBean.getGiftCardRuleAmountList())) {
                    continue;
                }

                //指定优惠套餐 or 优惠产品
                List<Integer> productOrCommodityIdList = Optional.ofNullable(giftCardPolicyBean.getGiftCardPolicyItemList())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(GiftCardPolicyItem::getProductCommodityId).collect(Collectors.toList());
                PolicyRuleType policyRuleType = PolicyRuleType.matchCode(giftCardPolicyBean.getPolicyRuleType());
                switch (policyRuleType) {
                    case ASSIGN_EFFECT_COMMODITY:
                        if (productOrCommodityIdList.contains(orderItem.getCommodity())) {
                            commodityPolicyMap.put(orderItem.getCommodity(), giftCardPolicyBean);
                        }
                        break;
                    case ASSIGN_EFFECT_PRODUCT:
                        if (productOrCommodityIdList.contains(orderItem.getProduct())) {
                            commodityPolicyMap.put(orderItem.getCommodity(), giftCardPolicyBean);
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        if (MapUtils.isEmpty(commodityPolicyMap)) {
            throw new InstaException(StoreDiscountErrorCode.InvalidTradeCodeException);
        }

        //套餐价格配置（符合代金券政策的订单商品所属套餐）
        List<Integer> orderCommodityIdList = Lists.newArrayList(commodityPolicyMap.keySet());
        List<CommodityPrice> orderCommodityPriceList = commodityPriceService.getPriceByCommodityIds(orderCommodityIdList, instaCountry);
        Map<Integer, CommodityPrice> orderCommodityPriceMap = orderCommodityPriceList
                .stream()
                .collect(Collectors.toMap(CommodityPrice::getCommodityId, commodityPrice -> commodityPrice));

        List<TradeOrderItemPolicyDTO> tradeOrderItemPolicyList = Lists.newArrayList();
        orderItemList.forEach(orderItem -> {
            if (commodityPolicyMap.containsKey(orderItem.getCommodity())) {
                //1、当前商品符合的政策
                GiftCardContext.GiftCardPolicyBean giftCardPolicyBean = commodityPolicyMap.get(orderItem.getCommodity());
                //2、政策减价Map (币种-减价金额)
                Map<String, Float> fixedDiscountAmountMap = Optional.ofNullable(giftCardPolicyBean.getGiftCardRuleAmountList())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(giftCardRuleAmount ->
                                Objects.nonNull(giftCardRuleAmount.getCurrency())
                                        && Objects.nonNull(giftCardRuleAmount.getAmount())
                                        && giftCardRuleAmount.getAmount() > DiscountCommonConstant.zero)
                        .collect(Collectors.toMap(GiftCardRuleAmount::getCurrency, GiftCardRuleAmount::getAmount));

                TradeOrderItemPolicyDTO orderItemPolicyDTO = null;
                PolicyDiscountType policyDiscountType = PolicyDiscountType.matchCode(giftCardPolicyBean.getPolicyDiscountType());
                switch (policyDiscountType) {
                    case AMOUNT_RATIO:
                        //3、政策金额比例
                        orderItemPolicyDTO = getTradeOrderItemPolicyDTO(orderCommodityPriceMap, orderItem, giftCardPolicyBean, fixedDiscountAmountMap);
                        break;

                    case AMOUNT_FIXED:
                        //4、固定减价
                        //优惠政策中所设置的减价币种是否包含订单支付币种
                        String paymentCurrency = orderItem.getCurrency();
                        if (MapUtils.isNotEmpty(fixedDiscountAmountMap) && fixedDiscountAmountMap.containsKey(paymentCurrency)) {
                            orderItemPolicyDTO = getTradeOrderItemPolicyDTO(orderCommodityPriceMap, orderItem, giftCardPolicyBean, fixedDiscountAmountMap);
                        }
                        break;

                    default:
                        break;
                }
                if (Objects.nonNull(orderItemPolicyDTO)) {
                    tradeOrderItemPolicyList.add(orderItemPolicyDTO);
                }
            }
        });

        TradeCalculateDTO tradeCalculateDTO = new TradeCalculateDTO();
        tradeCalculateDTO.setTradeDiscountType(DiscountType.parse(discountContext.getDiscountType()));
        tradeCalculateDTO.setTradeOrderItemPolicyList(tradeOrderItemPolicyList);
        tradeCalculateDTO.setPlaceOrderCountry(instaCountry);
        tradeCalculateDTO.setTradeCode(discountContext.getTradeCode());

        //政策优惠条件风险检查
        tradeCodeRuleEarlyWarningHelper.policyDataEarlyWarning(tradeCalculateDTO);
        return tradeCalculateDTO;
    }

    /**
     * 订单维度代金券门槛规则检查
     *
     * @param discountCalculationDto
     */
    protected void orderDimensionRuleCheck(DiscountCalculationDTO discountCalculationDto) {
        GiftCardContext.GiftCardThresholdBean giftCardThresholdBean = discountContext.getThreshold();
        List<OrderItem> orderItemList = discountCalculationDto.getOrderItems();
        if (Objects.isNull(giftCardThresholdBean)) {
            logger.info("[代金券使用]门槛校验流程-门槛信息缺失!giftCardCode:{},discountType:{}", discountContext.getGiftCardCode(), discountContext.getDiscountType());
            throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
        }

        // 无门槛限制
        if (giftCardThresholdBean.getThresholdTypeMark() == 0) {
            tradeCodeRuleEarlyWarningHelper.giftCardNoThresholdCheck(discountContext);
            return;
        }

        List<GiftCardThresholdItem> giftCardThresholdItemList = giftCardThresholdBean.getGiftCardThresholdItemList();
        List<GiftCardRuleAmount> giftCardThresholdRuleAmountList = giftCardThresholdBean.getGiftCardThresholdAmountList();
        if (CollectionUtils.isEmpty(giftCardThresholdItemList) && CollectionUtils.isEmpty(giftCardThresholdRuleAmountList)) {
            throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
        }

        // 指定优惠订单商品
        List<OrderItem> minConsumptionItems = orderItemList;
        if (CollectionUtils.isNotEmpty(giftCardThresholdItemList)) {
            // 门槛套餐ID集合
            List<Integer> commodityIdList = null;
            List<Integer> thresholdProductOrCommodityIdList = giftCardThresholdItemList.stream().map(GiftCardThresholdItem::getProductCommodityId).collect(Collectors.toList());
            boolean existNotEffect = false;
            // 1、不生效产品验证
            if (giftCardThresholdBean.isMark(ThresholdMarkType.BIND_NOT_EFFECT_PRODUCT)) {
                commodityIdList = commodityService.getCommodityIds(thresholdProductOrCommodityIdList);
                existNotEffect = true;
            }
            // 2、不生效套餐验证
            else if (giftCardThresholdBean.isMark(ThresholdMarkType.BIND_NOT_EFFECT_COMMODITY)) {
                commodityIdList = thresholdProductOrCommodityIdList;
                existNotEffect = true;
            }
            //3 、生效产品
            else if (giftCardThresholdBean.isMark(ThresholdMarkType.BIND_EFFECT_PRODUCT)) {
                commodityIdList = commodityService.getCommodityIds(thresholdProductOrCommodityIdList);
            }
            // 4、生效套餐
            else if (giftCardThresholdBean.isMark(ThresholdMarkType.BIND_EFFECT_COMMODITY)) {
                commodityIdList = thresholdProductOrCommodityIdList;
            }

            List<Integer> finalCommodityIdList = commodityIdList;
            if (existNotEffect) {
                orderItemList.forEach(orderItem -> {
                    if (finalCommodityIdList.contains(orderItem.getCommodity())) {
                        throw new InstaException(StoreDiscountErrorCode.InvalidTradeCodeException);
                    }
                });
            } else {
                minConsumptionItems = orderItemList
                        .stream()
                        .filter(orderItem ->
                                finalCommodityIdList.contains(orderItem.getCommodity()))
                        .collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(minConsumptionItems)) {
                throw new InstaException(StoreDiscountErrorCode.InvalidTradeCodeException);
            }
        }

        if (giftCardThresholdBean.isMark(ThresholdMarkType.BIND_THRESHOLD_AMOUNT)) {
            if (CollectionUtils.isEmpty(giftCardThresholdRuleAmountList)) {
                throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
            }

            Map<String, Float> thresholdAmountMap = giftCardThresholdRuleAmountList
                    .stream()
                    .collect(Collectors.toMap(GiftCardRuleAmount::getCurrency, GiftCardRuleAmount::getAmount));

            String currency = minConsumptionItems.get(0).getCurrency();
            Float itemTotalAmount = tradeDiscountCalculateHelper.itemTotalAmount(minConsumptionItems);
            Float limitAmount = thresholdAmountMap.get(currency);
            if (Objects.isNull(limitAmount) || itemTotalAmount < limitAmount) {
                throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
            }
        }
    }

    /**
     * 套餐维度优惠门槛检查
     *
     * @param discountCalculationDto
     */
    protected void commodityDimensionRuleCheck(DiscountCalculationDTO discountCalculationDto) {
        GiftCardContext.GiftCardThresholdBean giftCardThreshold = discountContext.getThreshold();
        List<OrderItem> orderItemList = discountCalculationDto.getOrderItems();
        if (Objects.isNull(giftCardThreshold)) {
            logger.info("[代金券使用]最低优惠流程-缺少门槛信息!giftCardCode:{}", discountContext.getGiftCardCode());
            throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
        }
        // 门槛无限制
        if (giftCardThreshold.getThresholdTypeMark() == 0) {
            tradeCodeRuleEarlyWarningHelper.giftCardNoThresholdCheck(discountContext);
            return;
        }
        List<GiftCardThresholdItem> giftCardThresholdItemList = giftCardThreshold.getGiftCardThresholdItemList();
        if (CollectionUtils.isEmpty(giftCardThresholdItemList)) {
            throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
        }

        // 门槛套餐ID集合
        List<Integer> commodityIdList = null;
        List<Integer> thresholdProductOrCommodityIdList = giftCardThresholdItemList.stream().map(GiftCardThresholdItem::getProductCommodityId).collect(Collectors.toList());
        boolean existNotEffect = false;
        // 1、不生效产品验证
        if (giftCardThreshold.isMark(ThresholdMarkType.BIND_NOT_EFFECT_PRODUCT)) {
            commodityIdList = commodityService.getCommodityIds(thresholdProductOrCommodityIdList);
            existNotEffect = true;
        }
        // 2、不生效套餐验证
        else if (giftCardThreshold.isMark(ThresholdMarkType.BIND_NOT_EFFECT_COMMODITY)) {
            commodityIdList = thresholdProductOrCommodityIdList;
            existNotEffect = true;
        }
        // 3、生效产品
        else if (giftCardThreshold.isMark(ThresholdMarkType.BIND_EFFECT_PRODUCT)) {
            commodityIdList = commodityService.getCommodityIds(thresholdProductOrCommodityIdList);
        }
        // 4、生效套餐
        else if (giftCardThreshold.isMark(ThresholdMarkType.BIND_EFFECT_COMMODITY)) {
            commodityIdList = thresholdProductOrCommodityIdList;
        }

        if (CollectionUtils.isEmpty(commodityIdList)) {
            throw new InstaException(DiscountErrorCode.InvalidGiftCardException);
        }
        List<Integer> finalNotEffectCommodityIdList = commodityIdList;
        if (existNotEffect) {
            orderItemList.forEach(orderItem -> {
                if (finalNotEffectCommodityIdList.contains(orderItem.getCommodity())) {
                    throw new InstaException(StoreDiscountErrorCode.InvalidTradeCodeException);
                }
            });
        } else {
            orderItemList
                    .stream()
                    .filter(orderItem -> finalNotEffectCommodityIdList.contains(orderItem.getCommodity()))
                    .findFirst()
                    .orElseThrow(() -> new InstaException(StoreDiscountErrorCode.InvalidTradeCodeException));
        }
    }

    /**
     * 获取代金券政策
     *
     * @param orderCommodityPriceMap
     * @param orderItem
     * @param giftCardPolicyBean
     * @param fixedDiscountAmountMap
     * @return
     */
    private TradeOrderItemPolicyDTO getTradeOrderItemPolicyDTO(Map<Integer, CommodityPrice> orderCommodityPriceMap, OrderItem orderItem, GiftCardContext.GiftCardPolicyBean giftCardPolicyBean, Map<String, Float> fixedDiscountAmountMap) {
        TradeOrderItemPolicyDTO orderItemPolicyDTO = new TradeOrderItemPolicyDTO();
        orderItemPolicyDTO.setPolicyId(giftCardPolicyBean.getId());
        orderItemPolicyDTO.setOrderItem(orderItem);
        orderItemPolicyDTO.setPolicyDiscountType(PolicyDiscountType.matchCode(giftCardPolicyBean.getPolicyDiscountType()));
        orderItemPolicyDTO.setFixedDiscountAmountMap(fixedDiscountAmountMap);
        orderItemPolicyDTO.setCommodityPrice(orderCommodityPriceMap.get(orderItem.getCommodity()));
        orderItemPolicyDTO.setMaxDiscountNum(giftCardPolicyBean.getDiscountMaxNumber());
        orderItemPolicyDTO.setDiscountRate(giftCardPolicyBean.getAmountProportion());
        return orderItemPolicyDTO;
    }

    /**
     * 获取折扣金额
     *
     * @param tradeCalculateDTO
     * @return
     */
    public abstract DiscountCheckResult getDiscountAmount(TradeCalculateDTO tradeCalculateDTO);

    /**
     * 门槛检查
     *
     * @param discountCalculationDto
     */
    public abstract void thresholdCheck(DiscountCalculationDTO discountCalculationDto);

    /**
     * 政策检查
     *
     * @param discountCalculationDto
     */
    public abstract TradeCalculateDTO policyCheck(DiscountCalculationDTO discountCalculationDto);

    /**
     * 获取政策折扣赠品
     *
     * @param tradeDiscountGiftItemQueryDTO
     * @return
     */
    public abstract List<GiftItem> getDiscountGiftItem(TradeDiscountGiftItemQueryDTO tradeDiscountGiftItemQueryDTO);
}
