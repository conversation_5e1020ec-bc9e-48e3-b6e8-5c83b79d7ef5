package com.insta360.store.business.prime.lib.response;

import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 * @description 创建购买组的响应
 * 包含从Amazon Prime API返回的创建购买组结果
 */
public class CreatePurchaseGroupResponse implements PrimeResponse {

    /**
     * 创建购买组的响应数据
     */
    private CreatePurchaseGroup createPurchaseGroup;

    /**
     * 获取创建购买组的响应数据
     *
     * @return 创建购买组的响应数据对象
     */
    public CreatePurchaseGroup getCreatePurchaseGroup() {
        return createPurchaseGroup;
    }

    /**
     * 设置创建购买组的响应数据
     *
     * @param createPurchaseGroup 创建购买组的响应数据对象
     */
    public void setCreatePurchaseGroup(CreatePurchaseGroup createPurchaseGroup) {
        this.createPurchaseGroup = createPurchaseGroup;
    }

    /**
     * 获取购买组ID
     * 如果创建成功，则返回购买组ID；否则返回空字符串
     *
     * @return 购买组ID字符串
     */
    public String purchaseGroupId() {
        return Optional.ofNullable(createPurchaseGroup)
                .map(CreatePurchaseGroup::getId)
                .orElse(StringUtils.EMPTY);
    }

    /**
     * 创建购买组的内部响应数据类
     */
    public static class CreatePurchaseGroup {
        /**
         * 购买组ID
         */
        private String id;

        /**
         * 获取购买组ID
         *
         * @return 购买组ID字符串
         */
        public String getId() {
            return id;
        }

        /**
         * 设置购买组ID
         *
         * @param id 购买组ID字符串
         */
        public void setId(String id) {
            this.id = id;
        }
    }
}
