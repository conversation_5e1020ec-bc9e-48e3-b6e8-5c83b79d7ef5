package com.insta360.store.business.configuration.monitor.support.handler;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSONArray;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.configuration.monitor.bo.StoreBusinessMonitorBO;
import com.insta360.store.business.configuration.monitor.enums.MonitorKeyType;
import com.insta360.store.business.configuration.monitor.model.GiftPriceThreshold;
import com.insta360.store.business.configuration.monitor.service.GiftPriceThresholdService;
import com.insta360.store.business.configuration.monitor.support.helper.OrderRuleMonitorHelper;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.dto.OrderStateDTO;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.product.enums.ProductCategoryFinalType;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductCategoryService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/8/8
 * @Description:
 */
@Component
public class OrderMonitorHandler extends BaseMonitorHandler {

    private final static Logger LOGGER = LoggerFactory.getLogger(OrderMonitorHandler.class);

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    ProductService productService;

    @Autowired
    GiftPriceThresholdService giftPriceThresholdService;

    @Autowired
    OrderRuleMonitorHelper orderRuleMonitorHelper;

    @Autowired
    ProductCategoryService productCategoryService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Override
    public void handler(StoreBusinessMonitorBO monitorBO) {

        // 休眠3秒，防止此处查询订单因数据库特性问题查询不到
        ThreadUtil.sleep(3, TimeUnit.SECONDS);

        // 参数处理
        Order order = parseMonitorBO(monitorBO);
        if (Objects.isNull(order)) {
            LOGGER.error("订单参数有误：" + monitorBO);
            return;
        }

        // 订单前置校验
        if (!orderPreCheck(order, monitorBO.getPaymentChannel(), monitorBO.getTradeCode())) {
            return;
        }

        // 监控逻辑处理
        doHandler(order);
    }

    /**
     * 订单前置校验
     *
     * @param order
     * @return
     */
    private boolean orderPreCheck(Order order, PaymentChannel paymentChannel, String tradeCode) {
        // 过滤工单
        if (order.isRepairOrder()) {
            return false;
        }

        // 兼容后台修改订单
        if (Objects.isNull(paymentChannel)) {
            OrderPayment payment = orderPaymentService.getByOrder(order.getId());
            if (!Objects.isNull(payment)) {
                paymentChannel = payment.paymentChannel();
            }
        }

        // 过滤市场&研发领用订单（免单不做处理）
        if (PaymentChannel.isInternalPayFilterChannel(paymentChannel)) {
            // 防止code拿不到情况
            tradeCode = tradeCode != null
                    ? tradeCode : StringUtil.isNotBlank(order.getGiftCardCode())
                    ? order.getGiftCardCode() : order.getCouponCode();
            if (StringUtil.isNotBlank(tradeCode)) {
                // 免支付订单文案
                orderRuleMonitorHelper.packFreeDiscountText(order, tradeCode);
            }
            return false;
        }
        return true;
    }

    /**
     * 监控逻辑处理入口
     *
     * @param order
     */
    private void doHandler(Order order) {
        String monitorIgnoreCommodity = storeConfigService.getConfigValue(StoreConfigKey.monitor_ignore_commodity);
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        List<OrderItem> giftItems = new ArrayList<>(orderItemList.size());
        // 记录商品支付总价
        float itemTotalPayAmount = 0;
        // 记录商品原价总和
        float itemTotalAmount = 0;
        for (OrderItem orderItem : orderItemList) {
            // 过滤没有原价订单
            if (Objects.isNull(orderItem.getOriginAmount())) {
                return;
            }

            if (StringUtil.isNotBlank(monitorIgnoreCommodity)) {
                JSONArray array = JSONArray.parseArray(monitorIgnoreCommodity);
                List<Integer> ignoreCommodityIds = array.stream().map(o -> (Integer) o).collect(Collectors.toList());
                // 过滤不监控套餐
                if (ignoreCommodityIds.contains(orderItem.getCommodity())) {
                    continue;
                }
            }

            // 记录赠品
            if (orderItem.getIsGift()) {
                giftItems.add(orderItem);
                continue;
            }
            itemTotalPayAmount += (orderItem.getPrice() - orderItem.getDiscountFee()) * orderItem.getNumber();
            itemTotalAmount += orderItem.getOriginAmount() * orderItem.getNumber();
        }
        Map<String, Object> orderRuleCheckMap = new HashMap<>();
        orderRuleCheckMap.put("order", order);
        orderRuleCheckMap.put("currency", orderItemList.get(0).currency());
        // 订单商品折扣校验(都是赠品)
        if (itemTotalAmount != 0) {
            orderDiscountCheck(orderRuleCheckMap, formatFloat(itemTotalPayAmount), formatFloat(itemTotalAmount));
        }
        // 赠品规则校验
        giftRuleCheck(orderRuleCheckMap, orderItemList, giftItems);
        // 校验&发送飞书通知
        orderRuleMonitorHelper.doOrderRuleCheck(orderRuleCheckMap);
    }

    /**
     * 赠品规则校验
     *
     * @param orderRuleCheckMap
     * @param orderItemList
     * @param giftItems
     * @return
     */
    private void giftRuleCheck(Map<String, Object> orderRuleCheckMap, List<OrderItem> orderItemList, List<OrderItem> giftItems) {
        // 订单不存在赠品
        if (CollectionUtils.isEmpty(giftItems)) {
            return;
        }

        // 记录产品数量
        Map<Integer, Integer> productMap = new HashMap<>();
        Set<Integer> productIds = orderItemList.stream().map(orderItem -> {
            Integer productId = orderItem.getProduct();
            if (productMap.containsKey(productId)) {
                Integer number = productMap.get(productId) + orderItem.getNumber();
                productMap.put(productId, number);
            } else {
                productMap.put(productId, orderItem.getNumber());
            }
            return productId;
        }).collect(Collectors.toSet());

        Collection<Product> products = productService.listByIds(productIds);
        List<Product> cameraProducts = products.stream().filter(Product::whetherCamera).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cameraProducts)) {
            // 不含相机订单文案封装
            orderRuleMonitorHelper.packNotCameraText(orderRuleCheckMap);
            return;
        }
        // 相机规则校验
        cameraRuleCheck(orderRuleCheckMap, productMap, giftItems, cameraProducts);
    }

    /**
     * 相机规则校验
     *
     * @param productMap
     * @param giftItems
     * @param cameraProducts
     */
    private void cameraRuleCheck(Map<String, Object> orderRuleCheckMap, Map<Integer, Integer> productMap, List<OrderItem> giftItems, List<Product> cameraProducts) {
        // 货币阈值
        Currency currency = giftItems.get(0).currency();
        GiftPriceThreshold priceThreshold = giftPriceThresholdService.getByCurrency(currency);
        if (Objects.isNull(priceThreshold)) {
            // 飞书通知
            FeiShuMessageUtil.storeGeneralMessage("货币阈值没有配置，请及时处理：" + currency, FeiShuGroupRobot.PriceChange, FeiShuAtUser.TW);
            return;
        }

        // 处理相机赠品规则
        Integer consumeCameraNumber = 0;
        Integer professionCameraNumber = 0;
        for (Product cameraProduct : cameraProducts) {
            ProductCategoryMainType categoryMainType = productCategoryHelper.getCategoryMainByKey(cameraProduct.getCategoryKey());
            ProductCategoryFinalType categoryFinalType = ProductCategoryFinalType.parse(cameraProduct.getCategoryKey());
            Integer productNumber = productMap.get(cameraProduct.getId());

            if (ProductCategoryMainType.isSpecialCameraType(categoryMainType)
                    || ProductCategoryFinalType.isConsumeCamera(categoryFinalType)) {
                consumeCameraNumber += productNumber;
            }
            if (ProductCategoryFinalType.isProfessionCamera(categoryFinalType)) {
                professionCameraNumber += productNumber;
            }
        }

        // 相机赠品规则校验
        doCameraRuleCheck(orderRuleCheckMap, consumeCameraNumber, professionCameraNumber, giftItems, priceThreshold);
    }

    /**
     * 相机赠品规则校验
     *
     * @param orderRuleCheckMap
     * @param consumeCameraNumber
     * @param professionCameraNumber
     * @param giftItems
     * @param priceThreshold
     */
    private void doCameraRuleCheck(Map<String, Object> orderRuleCheckMap, Integer consumeCameraNumber, Integer professionCameraNumber, List<OrderItem> giftItems, GiftPriceThreshold priceThreshold) {
        float totalGiftAmount = giftItems.stream().map(giftItem -> giftItem.getOriginAmount() * giftItem.getNumber()).reduce(Float::sum).get();
        // 消费级
        if (consumeCameraNumber != 0) {
            Float consumeThreshold = priceThreshold.getConsumeThreshold();
            // 赠品总价值
            if (totalGiftAmount > consumeThreshold * consumeCameraNumber) {
                // 总价值文案
                orderRuleMonitorHelper.packConsumeGiftTotalValueText(orderRuleCheckMap, consumeThreshold, consumeCameraNumber);
            }
            List<OrderItem> giftItemList = giftItems.stream().filter(giftItem -> giftItem.getOriginAmount() >= consumeThreshold).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(giftItemList)) {
                // 单赠品文案
                orderRuleMonitorHelper.packConsumeGiftText(orderRuleCheckMap, giftItemList, consumeThreshold);
            }
        }
        // 专业级
        if (professionCameraNumber != 0) {
            Float professionThreshold = priceThreshold.getProfessionThreshold();
            // 赠品总价值
            if (totalGiftAmount > professionThreshold * professionCameraNumber) {
                // 总价值文案
                orderRuleMonitorHelper.packProfessionGiftTotalValueText(orderRuleCheckMap, professionThreshold, professionCameraNumber);
            }
            List<OrderItem> giftItemList = giftItems.stream().filter(giftItem -> giftItem.getOriginAmount() >= professionThreshold).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(giftItemList)) {
                // 单赠品文案
                orderRuleMonitorHelper.packProfessionGiftText(orderRuleCheckMap, giftItemList, professionThreshold);
            }
        }
    }

    /**
     * 订单商品折扣校验
     *
     * @param orderRuleCheckMap
     * @param itemTotalPayAmount
     * @param itemTotalAmount
     * @return
     */
    private void orderDiscountCheck(Map<String, Object> orderRuleCheckMap, float itemTotalPayAmount, float itemTotalAmount) {
        String discountRatio = storeConfigService.getConfigValue(StoreConfigKey.order_discount_ratio);
        float orderDiscountRatio = discountRatio != null ? 1 - Float.parseFloat(discountRatio) : 0.6f;

        // 订单商品实付价总和 ≤ 75% * 订单商品原价总和
        if (itemTotalPayAmount <= itemTotalAmount * orderDiscountRatio) {
            // 文案封装
            orderRuleMonitorHelper.packOrderRuleText(orderRuleCheckMap, itemTotalPayAmount, itemTotalAmount);
        }
    }

    /**
     * 业务参数封装
     *
     * @param keyType
     * @param point
     * @return
     */
    @Override
    public StoreBusinessMonitorBO parseKeyPoint(MonitorKeyType keyType, JoinPoint point) {
        StoreBusinessMonitorBO monitorBO = new StoreBusinessMonitorBO();
        monitorBO.setKeyType(keyType);

        // 获取业务参数
        Object[] args = point.getArgs();
        if (args == null) {
            // 飞书通知
            return monitorBO;
        }

        switch (keyType) {
            case ORDER_PAY_BY_ORDER_NUMBER:
                if (args[0] instanceof String) {
                    monitorBO.setOrderNumber((String) args[0]);
                }
                if (args[1] instanceof PaymentChannel) {
                    monitorBO.setPaymentChannel((PaymentChannel) args[1]);
                }
                if (args[2] instanceof String) {
                    monitorBO.setTradeCode((String) args[2]);
                }
                return monitorBO;
            case ORDER_PAY_BY_ORDER_ID:
                if (args[0] instanceof OrderStateDTO) {
                    OrderStateDTO orderStateDTO = (OrderStateDTO) args[0];
                    monitorBO.setOrderId(orderStateDTO.getOrder());
                }
                return monitorBO;
            default:
                return monitorBO;
        }
    }

    /**
     * 解析BO参数
     *
     * @param monitorBO
     */
    private Order parseMonitorBO(StoreBusinessMonitorBO monitorBO) {
        switch (monitorBO.getKeyType()) {
            case ORDER_PAY_BY_ORDER_NUMBER:
                return orderService.getByOrderNumber(monitorBO.getOrderNumber());
            case ORDER_PAY_BY_ORDER_ID:
                return orderService.getById(monitorBO.getOrderId());
            default:
                return null;
        }
    }

    /**
     * 格式化
     *
     * @param price
     * @return
     */
    private Float formatFloat(float price) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return Float.valueOf(decimalFormat.format(price));
    }
}
