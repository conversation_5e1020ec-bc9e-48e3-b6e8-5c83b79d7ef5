package com.insta360.store.business.integration.avalara.service.hadler.avalara;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.integration.avalara.bo.AvalaraResponseResolveBO;
import com.insta360.store.business.integration.avalara.bo.ParseLineItemBO;
import com.insta360.store.business.integration.avalara.enums.AvalaraTransactionType;
import com.insta360.store.business.integration.avalara.enums.StoreTransactionType;
import com.insta360.store.business.integration.avalara.lib.module.AvalaraCreateTransaction;
import com.insta360.store.business.integration.avalara.lib.module.AvalaraLineItem;
import com.insta360.store.business.integration.avalara.lib.module.AvalaraResponseLineItem;
import com.insta360.store.business.integration.avalara.lib.request.AvalaraCreateTransactionRequest;
import com.insta360.store.business.integration.avalara.lib.request.BaseAvalaraRequest;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.trade.bo.CalculateTaxBO;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2023/06/19
 * @Description: 结账页环节
 */
@Scope("prototype")
@Component
public class AvalaraCheckoutTransactionHandler extends BaseAvalaraOrderTransactionHandler {

    @Override
    public StoreTransactionType getStoreTransactionType() {
        return StoreTransactionType.CHECKOUT_TRANSACTION;
    }

    @Override
    protected void init(CalculateTaxBO calculateTax) {
        this.order = new Order();
        this.orderDelivery = this.getOrderDelivery(calculateTax);
        this.orderPayment = this.getOrderPayment(calculateTax);
        this.inited = true;
    }

    @Override
    protected Boolean initable() {
        return true;
    }

    @Override
    protected Boolean commitable() {
        return false;
    }

    @Override
    protected BaseAvalaraRequest getAvalaraRequest(CalculateTaxBO calculateTax) {
        // 未指定初始化无法使用该流程
        if (!this.initable()) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 交易类型必须存在
        AvalaraTransactionType avalaraTransactionType = calculateTax.getAvalaraTransactionType();
        if (avalaraTransactionType == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 构建交易详情
        AvalaraCreateTransaction transaction = this.getAvalaraCreateTransaction(calculateTax);
        transaction.setType(avalaraTransactionType.getType());
        transaction.setPurchaseOrderNo(this.getPurchaseOrderNo());

        // 参数封装
        AvalaraCreateTransactionRequest request = new AvalaraCreateTransactionRequest(avalaraConfiguration);
        request.setCreateTransaction(transaction);
        return request;
    }

    /**
     * 结帐页不解析该项（这时还没有order item id）
     *
     * @param lineItems
     * @return
     */
    @Override
    protected Map<Integer, BigDecimal> parseItemTax(List<AvalaraResponseLineItem> lineItems) {
        return null;
    }

    /**
     * 结帐页不解析该项（这时还没有order item id）
     *
     * @param lineItems
     * @param totalTaxRate
     * @return
     */
    @Override
    protected Map<Integer, BigDecimal> parseItemTax(List<AvalaraLineItem> lineItems, BigDecimal totalTaxRate) {
        return null;
    }

    @Override
    protected LocalDateTime getTransactionDate() {
        return LocalDateTime.now();
    }

    @Override
    protected String getPurchaseOrderNo() {
        return null;
    }

    @Override
    protected String getCurrencyCode() {
        return orderPayment.getCurrency();
    }

    @Override
    protected List<AvalaraLineItem> listLineItems(CalculateTaxBO calculateTax) {
        // 构建 order items
        ParseLineItemBO avalaraParseLineItem = new ParseLineItemBO();
        avalaraParseLineItem.setTradeCode(calculateTax.getTradeCode());
        avalaraParseLineItem.setContactEmail(calculateTax.getContactEmail());
        avalaraParseLineItem.setPrePaymentChannel(calculateTax.getPrePaymentChannel());
        avalaraParseLineItem.setCountry(InstaCountry.parse(calculateTax.getCountryCode()));
        avalaraParseLineItem.setSheetItems(cartItemsParser.parse(calculateTax.getItemArrays()));
        avalaraParseLineItem.setStoreAccount(calculateTax.getStoreAccount());

        // 构建 avalara lines
        return avalaraLineResolveHelper.parseAvalaraLines(avalaraParseLineItem)
                .stream()
                .map(avalaraLine -> {
                    AvalaraLineItem avalaraLineItem = new AvalaraLineItem();
                    avalaraLineItem.setDiscounted(false);
                    avalaraLineItem.setTaxIncluded(false);
                    avalaraLineItem.setTaxCode(avalaraLine.getTaxCode());
                    avalaraLineItem.setQuantity(avalaraLine.getQuantity());
                    avalaraLineItem.setAmount(avalaraLine.getItemTotalAmount().doubleValue());
                    avalaraLineItem.setRef1(Objects.isNull(avalaraLine.getOrderItemId()) ? "" : String.valueOf(avalaraLine.getOrderItemId()));
                    avalaraLineItem.setRef2(String.valueOf(avalaraLine.getChildCommodityId()));
                    avalaraLineItem.setNumber(avalaraLine.getLineSerialNumber());
                    return avalaraLineItem;
                }).collect(Collectors.toList());
    }

    /**
     * 封装地址信息
     *
     * @param calculateTax
     * @return
     */
    private OrderDelivery getOrderDelivery(CalculateTaxBO calculateTax) {
        OrderDelivery orderDelivery = new OrderDelivery();
        orderDelivery.setCountryCode(calculateTax.getCountryCode());
        orderDelivery.setProvinceCode(calculateTax.getProvinceCode());
        orderDelivery.setCity(calculateTax.getCity());
        orderDelivery.setAddress(calculateTax.getAddress());
        orderDelivery.setSubAddress(calculateTax.getSubAddress());
        orderDelivery.setZipCode(calculateTax.getZipCode());
        return orderDelivery;
    }

    /**
     * 封装支付信息
     *
     * @param calculateTax
     * @return
     */
    private OrderPayment getOrderPayment(CalculateTaxBO calculateTax) {
        OrderPayment orderPayment = new OrderPayment();
        orderPayment.setCurrency(calculateTax.getCurrency());
        return orderPayment;
    }

    @Override
    public void resolveTransaction(AvalaraResponseResolveBO avalaraResponseResolve) {
    }
}
