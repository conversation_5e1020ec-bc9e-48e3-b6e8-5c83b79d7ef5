package com.insta360.store.business.order.email;

import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2020/10/14
 * @Description:
 */
@Scope("prototype")
@Component
public class OrderUnPayedPromotionEmail extends BaseOrderEmail {

    @Override
    public String getTemplateName() {
        return "store_order_unpay_promotion";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {

        // 订单号
        templateParams.addBodyParam("order_number", this.getOrderNumber());

        // 创建订单时间
        templateParams.addBodyParam("order_time", this.getOrderCreateTime());

        // 订单子项
        templateParams.addBodyParam("order_items", this.getOrderItems());

        // 支付信息
        templateParams.addBodyParam("order_payment", this.getOrderPayment());

        // 配送信息
        templateParams.addBodyParam("order_delivery", this.getOrderDelivery());

        // 订单号（RSA加密）
        templateParams.addBodyParam("order_id", this.getOrderNumberByRSA());
    }
}
