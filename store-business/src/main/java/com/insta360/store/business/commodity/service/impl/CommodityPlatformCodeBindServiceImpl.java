package com.insta360.store.business.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.dao.CommodityPlatformCodeBindDao;
import com.insta360.store.business.commodity.dto.CommodityPlatformCodeBindDTO;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.model.CommodityPlatformCodeBind;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.commodity.service.CommodityPlatformCodeBindService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-04-11
 * @Description:
 */
@Service
public class CommodityPlatformCodeBindServiceImpl extends BaseServiceImpl<CommodityPlatformCodeBindDao, CommodityPlatformCodeBind> implements CommodityPlatformCodeBindService {

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    CommodityPlatformCodeBindService platformCodeBindService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductService productService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void createPlatformCodeBind(CommodityPlatformCodeBindDTO platformCodeBindParam) {
        List<String> skuCodes = handleSkuCode(platformCodeBindParam.getSkuCodes());
        List<CommodityCode> commodityCodes = commodityCodeService.listBySkuCodes(skuCodes);
        // 已存在商城的料号不允许再绑定
        if (CollectionUtils.isNotEmpty(commodityCodes)) {
            HashMap<String, Integer> commodityCodesMap = new HashMap<>();
            for (CommodityCode commodityCode : commodityCodes) {
                commodityCodesMap.put(commodityCode.getCode(), commodityCode.getCommodity());
            }
            throw new InstaException(CommodityErrorCode.CommodityPlatformCodeAlreadyCreatedException,
                    packExceptionInfo(commodityCodesMap, CommodityErrorCode.CommodityPlatformCodeAlreadyCreatedException.getMsg()));
        }

        // 已存在绑定关系不允许再绑定
        List<CommodityPlatformCodeBind> commodityPlatformCodeBinds = platformCodeBindService.listBySkuCodes(skuCodes);
        if (CollectionUtils.isNotEmpty(commodityPlatformCodeBinds)) {
            Map<String, Integer> platformCodesMap = commodityPlatformCodeBinds
                    .stream().collect(Collectors.toMap(CommodityPlatformCodeBind::getSkuCode, CommodityPlatformCodeBind::getCommodityId));
            throw new InstaException(CommodityErrorCode.CommodityPlatformCodeAlreadyBindCreatedException,
                    packExceptionInfo(platformCodesMap, CommodityErrorCode.CommodityPlatformCodeAlreadyBindCreatedException.getMsg()));
        }

        List<CommodityPlatformCodeBind> platformCodeBinds = skuCodes.stream().map(skuCode -> {
            CommodityPlatformCodeBind platformCodeBind = new CommodityPlatformCodeBind();
            platformCodeBind.setCommodityId(platformCodeBindParam.getCommodityId());
            platformCodeBind.setSkuCode(skuCode);
            platformCodeBind.setCreateTime(LocalDateTime.now());
            platformCodeBind.setUpdateTime(LocalDateTime.now());
            return platformCodeBind;
        }).collect(Collectors.toList());

        // 保存
        this.saveBatch(platformCodeBinds);
    }

    @Override
    public List<CommodityPlatformCodeBind> listBySkuCodes(List<String> skuCodes) {
        QueryWrapper<CommodityPlatformCodeBind> qw = new QueryWrapper<>();
        qw.in("sku_code", skuCodes);
        return baseMapper.selectList(qw);
    }

    @Override
    public CommodityPlatformCodeBind getBySkuCode(String skuCode) {
        QueryWrapper<CommodityPlatformCodeBind> qw = new QueryWrapper<>();
        qw.eq("sku_code", skuCode);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<CommodityPlatformCodeBind> listByCommodityId(Integer commodityId) {
        QueryWrapper<CommodityPlatformCodeBind> qw = new QueryWrapper<>();
        qw.in("commodity_id", commodityId);
        return baseMapper.selectList(qw);
    }

    @Override
    public void deleteByCommodityId(Integer commodityId) {
        List<CommodityPlatformCodeBind> platformCodeBinds = listByCommodityId(commodityId);
        if (platformCodeBinds.isEmpty()) {
            throw new InstaException(CommodityErrorCode.CommodityPlatformCodeBindNotFoundException);
        }
        List<Integer> platformCodeBindsIds = platformCodeBinds.stream().map(CommodityPlatformCodeBind::getId).collect(Collectors.toList());
        baseMapper.deleteBatchIds(platformCodeBindsIds);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateByCommodityId(CommodityPlatformCodeBindDTO platformCodeBindParam) {
        deleteByCommodityId(platformCodeBindParam.getCommodityId());
        createPlatformCodeBind(platformCodeBindParam);
    }

    /**
     * 封装异常信息
     *
     * @param skuCodesMap
     * @param exceptionMsg
     * @return
     */
    private String packExceptionInfo(Map<String, Integer> skuCodesMap, String exceptionMsg) {
        StringBuilder exceptionInfo = new StringBuilder();
        exceptionInfo.append(exceptionMsg);
        // 查询并封装产品套餐名称
        List<Commodity> commodities = commodityService.listCommodities(new ArrayList<>(skuCodesMap.values()));
        Map<Integer, String> commodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getName));
        Map<Integer, Integer> productCommodityMap = commodities.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getProduct));
        List<Integer> productIds = commodities.stream().map(Commodity::getProduct).collect(Collectors.toList());
        Map<Integer, String> productMap = productService.getProducts(productIds).stream().collect(Collectors.toMap(Product::getId, Product::getName));

        Set<String> keySet = skuCodesMap.keySet();
        for (String skuCode : keySet) {
            exceptionInfo.append("<br/>");
            exceptionInfo.append(skuCode);
            exceptionInfo.append(": ");
            exceptionInfo.append(productMap.get(productCommodityMap.get(skuCodesMap.get(skuCode))))
                    .append("【").append(commodityMap.get(skuCodesMap.get(skuCode))).append("】");
        }
        return exceptionInfo.toString();
    }

    /**
     * 过滤字符串空格并且去重
     *
     * @param skuCodes
     * @return
     */
    private List<String> handleSkuCode(String skuCodes) {
        List<String> codes = new ArrayList<>();
        String[] skuCodeArr = skuCodes.split(",");
        for (String code : skuCodeArr) {
            String skuCode = code.trim();
            if (!codes.contains(code)) {
                codes.add(skuCode);
            }
        }
        return new ArrayList<>(codes);
    }
}
