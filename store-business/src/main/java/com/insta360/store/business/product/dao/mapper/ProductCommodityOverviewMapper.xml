<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.product.dao.ProductCommodityOverviewDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.product.dao.ProductCommodityOverviewDao"/>
    <insert id="saveOverviewBatch" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.insta360.store.business.product.model.ProductCommodityOverview">
        insert into product_commodity_overview
        (product_id,commodity_id,overview_template_id,template_order_index,identify,is_mobile,disabled_country,create_time,update_time)
        values
        <foreach collection="list" item="overview" separator=",">
            (#{overview.productId},#{overview.commodityId},#{overview.overviewTemplateId},
            #{overview.templateOrderIndex},#{overview.identify},#{overview.isMobile},#{overview.disabledCountry},#{overview.createTime},#{overview.updateTime})
        </foreach>
    </insert>

    <update id="updateByIdBatch">
        <foreach collection="productCommodityOverviews" item="overview" separator=";">
            update product_commodity_overview
            <set>
                product_id = #{overview.productId},
                commodity_id = #{overview.commodityId},
                overview_template_id = #{overview.overviewTemplateId},
                template_order_index = #{overview.templateOrderIndex},
                identify = #{overview.identify},
                disabled_country = #{overview.disabledCountry},
                update_time = #{overview.updateTime}
            </set>
            <where>
                id = #{overview.id}
            </where>
        </foreach>
    </update>
</mapper>



