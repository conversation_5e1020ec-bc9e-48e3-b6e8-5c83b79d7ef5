package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.dao.ProductCategoryTextFilterInfoDao;
import com.insta360.store.business.meta.model.ProductCategoryTextFilterInfo;
import com.insta360.store.business.meta.service.ProductCategoryTextFilterInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-10-11
 * @Description:
 */
@Service
public class ProductCategoryTextFilterInfoServiceImpl extends BaseServiceImpl<ProductCategoryTextFilterInfoDao, ProductCategoryTextFilterInfo> implements ProductCategoryTextFilterInfoService {

    @Override
    public void updateTextFilterInfo(List<ProductCategoryTextFilterInfo> filterInfoList) {
        baseMapper.updateTextFilterInfo(filterInfoList);
    }

    @Override
    public List<ProductCategoryTextFilterInfo> listBySelectorId(Integer textFilterId) {
        QueryWrapper<ProductCategoryTextFilterInfo> qw = new QueryWrapper<>();
        qw.eq("text_filter_main_id", textFilterId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ProductCategoryTextFilterInfo> listByMainIds(List<Integer> textFilterMainIds, InstaCountry country, InstaLanguage language) {
        QueryWrapper<ProductCategoryTextFilterInfo> qw = new QueryWrapper<>();
        qw.eq("country", country.name());
        qw.eq("language", language.name());
        qw.in("text_filter_main_id", textFilterMainIds);
        return baseMapper.selectList(qw);
    }
}
