package com.insta360.store.business.outgoing.mq.cache.helper;

import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.outgoing.mq.cache.sender.CachePutMonitorMq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2023/08/31
 * @Description:
 */
@Component
public class CachePutMonitorSendHelper {

    @Autowired
    CachePutMonitorMq cachePutMonitorMq;

    /**
     * 缓存更新事件通知
     */
    public void sendCachePutMonitorMessage(String cacheableType, CachePutKeyParameterBO cachePutKeyParameter) {
        cachePutMonitorMq.sendCachePutMonitorMessage(cacheableType, cachePutKeyParameter);
    }
}
