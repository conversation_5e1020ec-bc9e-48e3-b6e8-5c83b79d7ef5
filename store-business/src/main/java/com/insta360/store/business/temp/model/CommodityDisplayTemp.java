package com.insta360.store.business.temp.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-06-01
 * @Description: 
 */
public class CommodityDisplayTemp extends BaseModel<CommodityDisplayTemp> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer commodityId;

    private Integer orderIndex;

    private String ossUrl;

    private String urlM;

    private String urlS;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getOssUrl() {
        return ossUrl;
    }

    public void setOssUrl(String ossUrl) {
        this.ossUrl = ossUrl;
    }

    public String getUrlM() {
        return urlM;
    }

    public void setUrlM(String urlM) {
        this.urlM = urlM;
    }

    public String getUrlS() {
        return urlS;
    }

    public void setUrlS(String urlS) {
        this.urlS = urlS;
    }

    @Override
    public String toString() {
        return "CommodityDisplayTemp{" +
        "id=" + id +
        ", commodityId=" + commodityId +
        ", orderIndex=" + orderIndex +
        ", ossUrl=" + ossUrl +
        ", urlM=" + urlM +
        ", urlS=" + urlS +
        "}";
    }
}