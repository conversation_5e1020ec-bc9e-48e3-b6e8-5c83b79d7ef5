package com.insta360.store.business.discount.service.impl.handler.dto;

import com.insta360.store.business.order.bo.OrderSheet;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 交易券赠品查询DTO
 * @Date 2022/4/27
 */
public class TradeDiscountGiftItemQueryDTO implements Serializable {

    /**
     * 下单商品
     */
    private List<OrderSheet.SheetItem> sheetItems;

    public List<OrderSheet.SheetItem> getSheetItems() {
        return sheetItems;
    }

    public void setSheetItems(List<OrderSheet.SheetItem> sheetItems) {
        this.sheetItems = sheetItems;
    }
}
