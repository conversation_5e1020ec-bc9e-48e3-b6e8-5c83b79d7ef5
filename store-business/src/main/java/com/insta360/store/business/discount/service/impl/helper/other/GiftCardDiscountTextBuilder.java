package com.insta360.store.business.discount.service.impl.helper.other;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.discount.constant.TradeCodeConstantPool;
import com.insta360.store.business.discount.enums.AmountBusinessType;
import com.insta360.store.business.discount.enums.PolicyDiscountType;
import com.insta360.store.business.discount.enums.PolicyRuleType;
import com.insta360.store.business.discount.enums.old.DiscountTextType;
import com.insta360.store.business.discount.enums.old.DiscountWay;
import com.insta360.store.business.discount.model.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019-08-21
 * @Description:
 */
@Component
public class GiftCardDiscountTextBuilder extends GiftCardTextBuilder {

    public List<String> build(GiftCard giftCard, InstaLanguage language) {
        List<String> discountTextList = new ArrayList<>();

        // 从优惠力度的配置确定优惠力度文案
        List<GiftCardPolicy> giftCardPolicyList = giftCardPolicyService.listByGiftCardCode(giftCard.getGiftCardCode());

        if (CollectionUtils.isEmpty(giftCardPolicyList)) {
            return discountTextList;
        }

        //根据优惠政策ID分组
        List<Integer> policyIdList = giftCardPolicyList.stream().map(GiftCardPolicy::getId).collect(Collectors.toList());

        //1、政策指定优惠商品
        List<GiftCardPolicyItem> giftCardPolicyItemList = giftCardPolicyItemService.listByPolicyId(policyIdList);
        Map<Integer, List<Integer>> policyItemMap = Optional.ofNullable(giftCardPolicyItemList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(
                        Collectors.groupingBy(GiftCardPolicyItem::getPolicyId, Collectors.mapping(GiftCardPolicyItem::getProductCommodityId, Collectors.toList()))
                );

        //2、政策减价
        List<GiftCardRuleAmount> giftCardRuleAmountList = giftCardRuleAmountService.listByBusinessIdAndType(policyIdList, AmountBusinessType.POLICY_AMOUNT.type);
        Map<String, Map<String, Float>> policyRuleAmountMap = Optional.ofNullable(giftCardRuleAmountList)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(
                        Collectors.groupingBy(giftCardRuleAmount ->
                                giftCardRuleAmount.getBusinessId() + "#" + giftCardRuleAmount.getAmountBusinessType(),
                                Collectors.toMap(GiftCardRuleAmount::getCurrency, GiftCardRuleAmount::getAmount))
                );


        for (GiftCardPolicy giftCardPolicy : giftCardPolicyList) {
            //折扣比例
            Float rate = giftCardPolicy.getAmountProportion();
            PolicyDiscountType policyDiscountType = PolicyDiscountType.matchCode(giftCardPolicy.getPolicyDiscountType());

            //政策规则类型
            PolicyRuleType policyRuleType = PolicyRuleType.matchCode(giftCardPolicy.getPolicyRuleType());

            if (!rate.equals(TradeCodeConstantPool.DEFAULT_FLOT) && Objects.equals(policyDiscountType,PolicyDiscountType.AMOUNT_RATIO)) {
                // 仅限某些商品（套餐）来打折
                if (Objects.equals(policyRuleType,PolicyRuleType.ASSIGN_EFFECT_PRODUCT)) {
                    //获取指定产品
                    List<Integer> productIdList = policyItemMap.get(giftCardPolicy.getId());
                    if(CollectionUtils.isEmpty(productIdList)) {
                        continue;
                    }
                    GiftCardText discountProductText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.rate_product, language);
                    if (discountProductText == null) {
                        discountProductText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.rate_product, InstaLanguage.en_US);
                    }

                    discountTextList.add(String.format(discountProductText.getContent(), getProductsName(productIdList, language)));
                } else if (Objects.equals(policyRuleType,PolicyRuleType.ASSIGN_EFFECT_COMMODITY)) {
                    //获取指定套餐
                    List<Integer> commodityIdList = policyItemMap.get(giftCardPolicy.getId());
                    if(CollectionUtils.isEmpty(commodityIdList)) {
                        continue;
                    }
                    GiftCardText discountCommodityText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.rate_commodity, language);
                    if (discountCommodityText == null) {
                        discountCommodityText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.rate_commodity, InstaLanguage.en_US);
                    }

                    discountTextList.add(String.format(discountCommodityText.getContent(), getCommoditiesName(commodityIdList, language)));
                }

                // 优惠方式
                GiftCardText discountText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.rate, language);
                if (discountText == null) {
                    discountText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.rate, InstaLanguage.en_US);
                }

                String rateText = getRateText(rate, language);
                discountTextList.add(String.format(discountText.getContent(), rateText));

            } else if (Objects.equals(policyDiscountType,PolicyDiscountType.AMOUNT_FIXED)) {
                GiftCardText discountText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.fixed, language);
                if (discountText == null) {
                    discountText = giftCardTextService.getText(DiscountTextType.discount, DiscountWay.fixed, InstaLanguage.en_US);
                }
                String key = giftCardPolicy.getId() + "#" + AmountBusinessType.POLICY_AMOUNT.type;
                if(Objects.isNull(policyRuleAmountMap) || !policyRuleAmountMap.containsKey(key)) {
                    continue;
                }
                discountTextList.add(String.format(discountText.getContent(), getPriceStr(policyRuleAmountMap.get(key))));
            }
        }

        return discountTextList;
    }
}
