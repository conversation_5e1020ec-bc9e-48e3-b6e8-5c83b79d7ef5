package com.insta360.store.business.meta.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.meta.model.LiveBroadcastPage;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-10-18
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface LiveBroadcastPageDao extends BaseDao<LiveBroadcastPage> {

    /**
     * 展示页面
     *
     * @param liveBroadcastPages
     */
    void addLiveBroadcastPage(@Param("liveBroadcastPages") List<LiveBroadcastPage> liveBroadcastPages);
}
