package com.insta360.store.business.commodity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaLanguage;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-09-13
 * @Description: 
 */
@TableName("product_commodity_function_description")
public class CommodityFunctionDescription extends BaseModel<CommodityFunctionDescription> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 语言
     */
    private String language;

    /**
     * 功能描述
     */
    private String functionDescription;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public InstaLanguage language() {
        return InstaLanguage.parse(language);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getFunctionDescription() {
        return functionDescription;
    }

    public void setFunctionDescription(String functionDescription) {
        this.functionDescription = functionDescription;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ProductCommodityFunctionDescription{" +
        "id=" + id +
        ", commodityId=" + commodityId +
        ", language=" + language +
        ", functionDescription=" + functionDescription +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}