package com.insta360.store.business.integration.guanyi.lib.request;

/**
 * @Author: hyc
 * @Date: 2018/8/24
 * @Description:
 */
public class GyTradeGetRequest extends GyRequest {

    public static final String METHOD = "gy.erp.trade.get";

    Integer page_no; //页码
    Integer page_size; //每页大小
    String start_date; //开始时间
    String end_date; //结束时间
    Integer date_type; //时间类型 [默认为0, 0、创建时间 1、拍单时间 2、付款时间]
    Integer order_state; //订单类型 [默认为0, 0、全部 1、未审核 2、已审核]
    String warehouse_code; //仓库代码
    String shop_code; //店铺代码
    String vip_name; //会员名称
    String platform_code; //平台单号
    String receiver_mobile; //收件手机

    public GyTradeGetRequest() {
        super(METHOD);
    }

    public Integer getPage_no() {
        return page_no;
    }

    public void setPage_no(Integer page_no) {
        this.page_no = page_no;
    }

    public Integer getPage_size() {
        return page_size;
    }

    public void setPage_size(Integer page_size) {
        this.page_size = page_size;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public Integer getDate_type() {
        return date_type;
    }

    public void setDate_type(Integer date_type) {
        this.date_type = date_type;
    }

    public Integer getOrder_state() {
        return order_state;
    }

    public void setOrder_state(Integer order_state) {
        this.order_state = order_state;
    }

    public String getWarehouse_code() {
        return warehouse_code;
    }

    public void setWarehouse_code(String warehouse_code) {
        this.warehouse_code = warehouse_code;
    }

    public String getShop_code() {
        return shop_code;
    }

    public void setShop_code(String shop_code) {
        this.shop_code = shop_code;
    }

    public String getVip_name() {
        return vip_name;
    }

    public void setVip_name(String vip_name) {
        this.vip_name = vip_name;
    }

    public String getPlatform_code() {
        return platform_code;
    }

    public void setPlatform_code(String platform_code) {
        this.platform_code = platform_code;
    }

    public String getReceiver_mobile() {
        return receiver_mobile;
    }

    public void setReceiver_mobile(String receiver_mobile) {
        this.receiver_mobile = receiver_mobile;
    }
}
