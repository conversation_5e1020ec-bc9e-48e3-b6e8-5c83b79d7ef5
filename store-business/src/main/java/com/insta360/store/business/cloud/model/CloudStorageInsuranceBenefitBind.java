package com.insta360.store.business.cloud.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-05-13
 * @Description: 云存储商城权益增值服务绑定表
 */
public class CloudStorageInsuranceBenefitBind extends BaseModel<CloudStorageInsuranceBenefitBind> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商城权益明细id
     */
    private Long benefitDetailId;

    /**
     * 权益类型 1：配件折扣 2：care 3：延保
     */
    private Integer benefitType;

    /**
     * 保险类型
     */
    private String insuranceType;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBenefitDetailId() {
        return benefitDetailId;
    }

    public void setBenefitDetailId(Long benefitDetailId) {
        this.benefitDetailId = benefitDetailId;
    }

    public Integer getBenefitType() {
        return benefitType;
    }

    public void setBenefitType(Integer benefitType) {
        this.benefitType = benefitType;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "CloudStorageInsuranceBenefitBind{" +
                "id=" + id +
                ", benefitDetailId=" + benefitDetailId +
                ", benefitType=" + benefitType +
                ", insuranceType='" + insuranceType + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}