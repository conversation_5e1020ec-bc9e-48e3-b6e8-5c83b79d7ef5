package com.insta360.store.business.discount.enums;

/**
 * <AUTHOR>
 * @Description 折扣券生效状态
 * @Date 2023/4/20
 */
public enum DiscountEffectState {
    disabled(0,"已禁用"),
    expired(1,"已过期"),
    effect(2,"生效中"),
    future_effect(3,"未来生效"),
    ;

    private final int code;

    private final String value;

    DiscountEffectState(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
