package com.insta360.store.business.payment.service.impl.helper;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.gateway.GatewayConfiguration;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.integration.checkout.lib.response.CreateGetCkoOrderDetailResponse;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.aop.annotation.LogOrderStateChange;
import com.insta360.store.business.payment.bo.PaymentSubscribeBO;
import com.insta360.store.business.payment.bo.PaymentSubscribeResultBO;
import com.insta360.store.business.payment.constants.CkoPaymentConstant;
import com.insta360.store.business.payment.dto.CheckoutDTO;
import com.insta360.store.business.payment.enums.CkoSubPaymentMethod;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.checkout.ApplePayConfiguration;
import com.insta360.store.business.payment.lib.checkout.CheckoutHkConfiguration;
import com.insta360.store.business.payment.lib.checkout.CheckoutUsConfiguration;
import com.insta360.store.business.payment.lib.checkout.model.BillingAddress;
import com.insta360.store.business.payment.lib.checkout.model.Source;
import com.insta360.store.business.payment.lib.checkout.request.payment.CreateCkoPaymentSessionRequest;
import com.insta360.store.business.payment.service.impl.handler.BaseCkoPaymentHandler;
import com.insta360.store.business.payment.service.impl.handler.BasePaymentHandler;
import com.insta360.store.business.payment.service.impl.handler.PaymentHandlerFactory;
import com.insta360.store.business.trade.enums.CreditCardPaymentActionEnum;
import com.insta360.store.business.trade.model.CreditCardPayChannel;
import com.insta360.store.business.trade.model.CreditCardPaymentAuthResult;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.service.CreditCardPayChannelService;
import com.insta360.store.business.trade.service.CreditCardPaymentAuthResultService;
import com.insta360.store.business.trade.service.CreditCardPaymentInfoService;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.utils.SSLUtil;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wkx
 * @Date: 12/29/23
 * @Description:
 */
@Component
public class CkoPaymentHelper {

    private final static Logger LOGGER = LoggerFactory.getLogger(CkoPaymentHelper.class);

    @Autowired
    ApplePayConfiguration applePayConfiguration;

    @Autowired
    PaymentHandlerFactory paymentHandlerFactory;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    StoreSdkCallRecordService storeSdkCallRecordService;

    @Autowired
    CreditCardPaymentAuthResultService creditCardPaymentAuthResultService;

    @Autowired
    OrderService orderService;

    @Autowired
    CreditCardPaymentInfoService creditCardPaymentInfoService;

    @Autowired
    GatewayConfiguration gatewayConfiguration;

    @Autowired
    CheckoutHkConfiguration checkoutHkConfiguration;

    @Autowired
    CheckoutUsConfiguration checkoutUsConfiguration;

    @Autowired
    CreditCardPayChannelService creditCardPayChannelService;

    /**
     * 校验Apple Pay session
     *
     * @param appleUrl
     * @param displayName
     * @param domainName
     * @return
     */
    public String validateApplePaySession(String appleUrl, String displayName, String domainName) {
        Map<String, String> reqParamMap = new HashMap<>(3);
        reqParamMap.put("merchantIdentifier", applePayConfiguration.getMerchantIdentifier());
        reqParamMap.put("domainName", domainName);
        reqParamMap.put("displayName", displayName);
        String bodyParam = JSON.toJSONString(reqParamMap);

        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .writeTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .sslSocketFactory(SSLUtil.getSocketFactoryPEM(applePayConfiguration.getPem(),
                            applePayConfiguration.getPemKey()), SSLUtil.getX509TrustManager())
                    .build();

            okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
            okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(mediaType, bodyParam);
            Request request = new Request.Builder().url(appleUrl).post(requestBody).build();

            LOGGER.info(String.format("apple pay session request param {%s}", request));
            Response response = ApplicationContextHolder.getApplicationContext()
                    .getBean(CkoPaymentHelper.class).executeRequest(client, request);
            LOGGER.info(String.format("apple pay session response {%s}", response));

            ResponseBody responseBody = response.body();
            if (Objects.isNull(responseBody)) {
                throw new InstaException(CommonErrorCode.InvalidParameterException, "apple pay body null exception");
            }
            String responseBodyStr = responseBody.string();
            LOGGER.info(String.format("apple pay session response body {%s}", responseBodyStr));

            if (!response.isSuccessful()) {
                throw new InstaException(CommonErrorCode.InvalidParameterException, "apple pay request unsuccessful exception");
            }

            if (!JSONUtil.isJson(responseBodyStr)) {
                throw new InstaException(CommonErrorCode.InvalidParameterException, "apple pay response body no json exception");
            }
            return responseBodyStr;
        } catch (Exception e) {
            String msg = "apple pay session request fail ";
            LOGGER.error(msg, e);
            FeiShuMessageUtil.storeGeneralMessage(msg + e.getMessage(), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
    }

    /**
     * ok http执行器
     *
     * @param client
     * @param request
     * @return
     * @throws IOException
     */
    @Retryable(value = {IOException.class}, include = {IOException.class}, maxAttempts = 3, backoff = @Backoff(value = 100L, multiplier = 1))
    public Response executeRequest(OkHttpClient client, Request request) throws IOException {
        return client.newCall(request).execute();
    }

    /**
     * CKO支付结果校验处理
     *
     * @param paymentDetailResponse
     * @param orderPayment
     */
    @LogOrderStateChange
    @Transactional(rollbackFor = RuntimeException.class)
    public void handleCkoPaymentResultCheck(CreateGetCkoOrderDetailResponse paymentDetailResponse, OrderPayment orderPayment, PaymentChannel paymentChannel) {
        BaseCkoPaymentHandler baseCkoPaymentHandler = (BaseCkoPaymentHandler) paymentHandlerFactory.getPaymentHandler(paymentChannel);
        baseCkoPaymentHandler.doHandlePaymentResultCheck(paymentDetailResponse, orderPayment);
    }

    /**
     * CKO更新订阅信息
     *
     * @param paymentSubscribeParam
     * @param orderNumber
     * @return
     */
    public PaymentSubscribeResultBO updateSubscribeInformation(PaymentSubscribeBO paymentSubscribeParam, String orderNumber) {
        // 订单信息
        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }
        paymentSubscribeParam.setOrder(order);

        // 支付信息
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (orderPayment == null) {
            throw new InstaException(OrderErrorCode.OrderPaymentNotFoundException);
        }
        paymentSubscribeParam.setOrderPayment(orderPayment);

        // 支付渠道
        PaymentChannel paymentChannel = orderPayment.paymentChannel();
        if (paymentChannel == null) {
            throw new InstaException(PaymentErrorCode.InvalidPaymentChannelException);
        }

        // 获取订阅中订单支付通道
        CreditCardPaymentInfo oldOrderPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(orderNumber);
        paymentSubscribeParam.setPaymentChannelId(Objects.isNull(oldOrderPaymentInfo) || oldOrderPaymentInfo.getPayChannelId() == -1
                // 正常流程不会出现pay channel不存在，兜底香港通道
                ? CkoPaymentConstant.DEFAULT_CKO_CHANNEL : oldOrderPaymentInfo.getPayChannelId());

        BasePaymentHandler paymentHandler = paymentHandlerFactory.getPaymentHandler(getCkoSubscribeChannel(order));
        return paymentHandler.updatePaymentSubscribeInfo(paymentSubscribeParam);
    }

    /**
     * 保存cko扣款信息
     *
     * @param orderNumber
     * @param paymentChannel
     * @param ckoOrderStateResponse
     */
    @Async
    public void saveUserPayInfo(String orderNumber, PaymentChannel paymentChannel, CreateGetCkoOrderDetailResponse ckoOrderStateResponse) {
        UserPayInfo userPayInfo = paymentHelper.getUserPayInfo(orderNumber);
        Source source = ckoOrderStateResponse.getSource();
        userPayInfo.setPayId(AESUtil.encode(AESUtil.PAY_TOKEN_KEY, source.getId()));
        userPayInfo.setPaymentTradeId(ckoOrderStateResponse.getId());
        userPayInfo.setPaymentMethod(StorePaymentMethodEnum.CKO_PAYMENT.getName());
        if (PaymentChannel.isCkoSubscribeChannel(paymentChannel)) {
            userPayInfo.setCardType(source.getScheme());
            userPayInfo.setCardCountry(source.getIssuerCountry());
            userPayInfo.setDeductCategory(source.getBin() + "***" + source.getLast4());
            userPayInfo.setPaymentSubMethod(CkoSubPaymentMethod.CREDIT_CARD.getMethodParaName());
        }

        switch (paymentChannel) {
            case google_pay_cko_subscribe:
            case google_pay_cko_us_subscribe:
                userPayInfo.setPaymentSubMethod(CkoSubPaymentMethod.GOOGLE_PAY.getMethodParaName());
                break;
            case apple_pay_cko_subscribe:
            case apple_pay_cko_us_subscribe:
                userPayInfo.setPaymentSubMethod(CkoSubPaymentMethod.APPLE_PAY.getMethodParaName());
                break;
            default:
                break;
        }
        paymentHelper.saveOrUpdatePayInfo(userPayInfo);
        this.saveAuthResultPaymentInfo(ckoOrderStateResponse);
    }

    /**
     * 记录cko 订阅调用记录
     *
     * @param orderNumber
     * @param requestBody
     * @param responseBody
     */
    @Async
    public void saveCkoSubscribeCallRecord(String orderNumber, Object requestBody, Object responseBody) {
        storeSdkCallRecordService.saveSdkCallRecord(orderNumber, orderNumber, JSONObject.toJSONString(requestBody), JSONObject.toJSONString(responseBody),
                StoreSdkCallBusinessType.SUBSCRIBE_MANAGEMENT, StoreSdkCallApiType.CKO_SUBSCRIBE_API);
    }

    /**
     * 处理重试扣款逻辑
     *
     * @param order
     * @param responseCode
     * @param isRenewDeductionRetry
     */
    public void handleRetryLogic(Order order, String responseCode, Boolean isRenewDeductionRetry) {
        LOGGER.info(String.format("进入cko重试扣款逻辑。。订单信息{%s} responseCode{%s} isRenewDeductionRetry {%s} ", order, responseCode, isRenewDeductionRetry));
        // 对首次扣款失败进行重试
        if (StringUtils.isBlank(responseCode) || Boolean.TRUE.equals(isRenewDeductionRetry)) {
            paymentHelper.processSubscribeRenewOrderFailed(order);
            return;
        }

        // 重试扣款
        if (responseCode.startsWith(CkoPaymentConstant.RESPONSE_SOFT_DECLINE_PRE_CODE) || responseCode.startsWith(CkoPaymentConstant.RESPONSE_CKO_DECLINE_PRE_CODE)) {
            paymentHelper.doRenewOrderPayRetryLogic(order);
            return;
        }
        paymentHelper.processSubscribeRenewOrderFailed(order);
    }

    /**
     * 保存auth payment info
     *
     * @param paymentResponse
     */
    private void saveAuthResultPaymentInfo(CreateGetCkoOrderDetailResponse paymentResponse) {
        // 单独保存订单的多条auth信息
        CreditCardPaymentAuthResult creditCardPaymentAuthResult = new CreditCardPaymentAuthResult();
        creditCardPaymentAuthResult.setOrderNumber(paymentResponse.getReference());
        creditCardPaymentAuthResult.setPayMethod(StorePaymentMethodEnum.CKO_PAYMENT.getName());
        creditCardPaymentAuthResult.setActionType(CreditCardPaymentActionEnum.CKO_GET_PAYMENT_ID.getActionName());
        creditCardPaymentAuthResult.setAuthType(CreditCardPaymentActionEnum.CKO_GET_PAYMENT_ID.getAuthType());
        creditCardPaymentAuthResult.setAuthCode(CkoPaymentConstant.SUCCESS_CODE);
        creditCardPaymentAuthResult.setAuthText(CkoPaymentConstant.SUCCESS_CODE);
        creditCardPaymentAuthResult.setCreateTime(LocalDateTime.now());
        creditCardPaymentAuthResult.setUpdateTime(LocalDateTime.now());
        creditCardPaymentAuthResultService.save(creditCardPaymentAuthResult);

    }

    /**
     * 获取Google Pay支付渠道
     *
     * @param order
     * @return
     */
    public PaymentChannel getCkoGpChannel(Order order) {
        return InstaCountry.US.equals(order.country()) ? PaymentChannel.google_pay_cko_us : PaymentChannel.google_pay_cko;
    }

    /**
     * 获取Apple Pay支付渠道
     *
     * @param order
     * @return
     */
    public PaymentChannel getCkoApChannel(Order order) {
        return InstaCountry.US.equals(order.country()) ? PaymentChannel.apple_pay_cko_us : PaymentChannel.apple_pay_cko;
    }

    /**
     * 获取普通卡支付渠道
     *
     * @param order
     * @return
     */
    public PaymentChannel getCkoNormalChannel(Order order) {
        return InstaCountry.US.equals(order.country()) ? PaymentChannel.cko_credit_card_us : PaymentChannel.cko_credit_card;
    }

    /**
     * 获取订阅卡支付渠道
     *
     * @param order
     * @return
     */
    public PaymentChannel getCkoSubscribeChannel(Order order) {
        return InstaCountry.US.equals(order.country()) ? PaymentChannel.cko_credit_card_us_subscribe : PaymentChannel.cko_credit_card_subscribe;
    }

    /**
     * 获取Google Pay订阅支付渠道
     *
     * @param order
     * @return
     */
    public PaymentChannel getCkoGpSubscribeChannel(Order order) {
        return InstaCountry.US.equals(order.country()) ? PaymentChannel.google_pay_cko_us_subscribe : PaymentChannel.google_pay_cko_subscribe;
    }

    /**
     * 获取Apple Pay订阅支付渠道
     *
     * @param order
     * @return
     */
    public PaymentChannel getCkoApSubscribeChannel(Order order) {
        return InstaCountry.US.equals(order.country()) ? PaymentChannel.apple_pay_cko_us_subscribe : PaymentChannel.apple_pay_cko_subscribe;
    }

    /**
     * 创建cko payment session
     *
     * @param paymentParam
     * @param apiCountry
     * @return
     */
    public JSONObject createFlowPaymentSession(CheckoutDTO paymentParam, InstaCountry apiCountry) {
        BillingAddress billing = new BillingAddress();
        billing.setCountry(apiCountry.name());
        // 统一走美国通道（解决接口参数必传，不影响payments api支付通道值）
        CreditCardPayChannel cardPayChannel = creditCardPayChannelService.getById(2);

        CreateCkoPaymentSessionRequest request = new CreateCkoPaymentSessionRequest();
        request.setProcessingChannelId(cardPayChannel.getProcessingId());
        request.setAmount(Integer.valueOf(paymentParam.getAmount()));
        request.setCurrency(paymentParam.getCurrency());
        request.setBilling(billing);
        request.setSuccessUrl(gatewayConfiguration.getStoreUrl());
        request.setFailureUrl(gatewayConfiguration.getStoreUrl());
        String result = request.executePost(InstaCountry.US.equals(apiCountry) ? checkoutUsConfiguration : checkoutHkConfiguration);
        return JSONObject.parseObject(result);
    }
}
