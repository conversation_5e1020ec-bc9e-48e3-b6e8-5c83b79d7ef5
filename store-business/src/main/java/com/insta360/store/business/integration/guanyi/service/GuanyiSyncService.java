package com.insta360.store.business.integration.guanyi.service;

import com.insta360.store.business.integration.guanyi.bo.GuanyiOrderInterceptContextBO;
import com.insta360.store.business.integration.guanyi.lib.model.GyOrder;
import com.insta360.store.business.integration.guanyi.lib.model.GyOrderDelivery;
import com.insta360.store.business.integration.guanyi.lib.response.GyTradeGetResponse;
import com.insta360.store.business.meta.bo.ToGuanyiOrder;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description:
 */
public interface GuanyiSyncService {

    /**
     * 同步订单到管易（商城、马帮、Shopify、amazon、领星）
     *
     * @param order
     */
    Boolean syncToGuanyi(ToGuanyiOrder order);

    /**
     * 从管易获取订单发货信息（Shopify订单使用）
     *
     * @param order
     * @return
     */
    GyOrderDelivery syncGuanyiOrderDelivery(ToGuanyiOrder order);

    /**
     * 从管易获取订单的部分发货信息
     *
     * @param order
     * @return
     */
    List<GyOrder> syncGuanyiPartOrderDelivery(ToGuanyiOrder order);

    /**
     * 从管易获取发货订单的信息（根据物流号）
     *
     * @param mailNo
     * @return
     */
    String syncGuanyiOrderDeliverys(String mailNo);

    /**
     * 从管易获取发货订单的信息（获取全部的分页信息）
     *
     * @param pageNo
     * @return
     */
    String syncGuanyiOrderDeliverys(Integer pageNo);

    /**
     * 从管易拦截退款订单
     *
     * @param order
     * @param reason
     * @return
     */
    boolean syncInterceptGuanyiOrder(Order order, String reason, Integer rmaState);

    /**
     * 同步管易拦截订单
     *
     * @param contextParam
     * @return
     */
    Boolean syncInterceptGuanyiOrder(GuanyiOrderInterceptContextBO contextParam);

    /**
     * 从管易拦截拒付订单
     *
     * @param order
     * @return
     */
    void syncInterceptGuanyiOrder(Order order);

    /**
     * 管易订单退款状态修改
     *
     * @param order
     * @param orderItem
     * @param rmaState
     * @return
     */
    void syncRefundUpdateGuanyiOrder(Order order, OrderItem orderItem, Integer rmaState);

    /**
     * 校验订单是否推送成功
     *
     * @param orderNumber
     * @return
     */
    GyTradeGetResponse checkGyOrder(String orderNumber);
}
