package com.insta360.store.business.trade.bo;

import com.insta360.store.business.integration.avalara.bo.AvalaraAddressBO;
import com.insta360.store.business.integration.avalara.enums.StoreTaxType;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2023/06/23
 * @Description:
 */
public class CalculateTaxResultBO {

    /**
     * 总税费
     */
    private BigDecimal totalTax;

    /**
     * 与地址税率关联的唯一标识
     */
    private String uniqueKey;

    /**
     * 组合套餐纬度的税费关联关系
     * <p>
     * key:orderItemId
     * value:tax
     */
    private Map<Integer, BigDecimal> itemTax;

    /**
     * 接口响应的json字符串
     */
    private String responseJson;

    /**
     * 计税方式
     */
    private StoreTaxType storeTaxType;

    /**
     * 地址校验结果
     */
    private AvalaraAddressBO address;

    public CalculateTaxResultBO() {
    }

    public CalculateTaxResultBO(BigDecimal totalTax, StoreTaxType storeTaxType) {
        this.totalTax = totalTax;
        this.storeTaxType = storeTaxType;
    }

    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    public String getUniqueKey() {
        return uniqueKey;
    }

    public void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    public Map<Integer, BigDecimal> getItemTax() {
        return itemTax;
    }

    public void setItemTax(Map<Integer, BigDecimal> itemTax) {
        this.itemTax = itemTax;
    }

    public String getResponseJson() {
        return responseJson;
    }

    public void setResponseJson(String responseJson) {
        this.responseJson = responseJson;
    }

    public StoreTaxType getStoreTaxType() {
        return storeTaxType;
    }

    public void setStoreTaxType(StoreTaxType storeTaxType) {
        this.storeTaxType = storeTaxType;
    }

    public AvalaraAddressBO getAddress() {
        return address;
    }

    public void setAddress(AvalaraAddressBO address) {
        this.address = address;
    }

    @Override
    public String toString() {
        return "CalculateTaxResultBO{" +
                "totalTax=" + totalTax +
                ", uniqueKey='" + uniqueKey + '\'' +
                ", itemTax=" + itemTax +
                ", responseJson='" + responseJson + '\'' +
                ", storeTaxType=" + storeTaxType +
                ", address=" + address +
                '}';
    }
}
