package com.insta360.store.business.integration.avalara.lib.response;

import com.alibaba.fastjson.JSONObject;
import com.insta360.store.business.integration.avalara.lib.module.AvalaraAddressLocationInfo;
import com.insta360.store.business.integration.avalara.lib.module.AvalaraResponseLineItem;
import com.insta360.store.business.integration.avalara.lib.module.AvalaraTaxSummary;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/06/06
 * @Description:
 */
public class AvalaraAdjustTransactionResponse extends BaseAvalaraResponse {

    /**
     * 本次交易调用ID
     */
    private Long id;

    /**
     * AvaTax 唯一编号
     */
    private String code;

    /**
     * 计税日期
     */
    private String date;

    /**
     * 计税类型
     *
     * @see com.insta360.store.business.integration.avalara.enums.AvalaraTransactionType
     */
    private String type;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 汇率币种
     */
    private String exchangeRateCurrencyCode;

    /**
     * 商城订单号
     */
    private String purchaseOrderNo;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单总税费
     */
    private BigDecimal totalTax;

    /**
     * 订单应税总额
     */
    private BigDecimal totalTaxable;

    /**
     * AvaTax 为交易计算的税额
     */
    private BigDecimal totalTaxCalculated;

    /**
     * 调整原因
     *
     * @see com.insta360.store.business.integration.avalara.enums.AvalaraAdjustmentReasonType
     */
    private String adjustmentReason;

    /**
     * 国家
     */
    private String country;

    /**
     * 地区
     */
    private String region;

    /**
     * 交易商品集合
     */
    private List<AvalaraResponseLineItem> lines;

    /**
     * 地址列表
     */
    private List<AvalaraAddressLocationInfo> addresses;

    /**
     * 各阶梯税率概括
     */
    private List<AvalaraTaxSummary> summary;

    /**
     * 参数解析
     *
     * @param result
     * @return
     */
    public static AvalaraAdjustTransactionResponse parse(String result) {
        return JSONObject.parseObject(result, AvalaraAdjustTransactionResponse.class);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getExchangeRateCurrencyCode() {
        return exchangeRateCurrencyCode;
    }

    public void setExchangeRateCurrencyCode(String exchangeRateCurrencyCode) {
        this.exchangeRateCurrencyCode = exchangeRateCurrencyCode;
    }

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    public BigDecimal getTotalTaxable() {
        return totalTaxable;
    }

    public void setTotalTaxable(BigDecimal totalTaxable) {
        this.totalTaxable = totalTaxable;
    }

    public BigDecimal getTotalTaxCalculated() {
        return totalTaxCalculated;
    }

    public void setTotalTaxCalculated(BigDecimal totalTaxCalculated) {
        this.totalTaxCalculated = totalTaxCalculated;
    }

    public String getAdjustmentReason() {
        return adjustmentReason;
    }

    public void setAdjustmentReason(String adjustmentReason) {
        this.adjustmentReason = adjustmentReason;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public List<AvalaraResponseLineItem> getLines() {
        return lines;
    }

    public void setLines(List<AvalaraResponseLineItem> lines) {
        this.lines = lines;
    }

    public List<AvalaraAddressLocationInfo> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<AvalaraAddressLocationInfo> addresses) {
        this.addresses = addresses;
    }

    public List<AvalaraTaxSummary> getSummary() {
        return summary;
    }

    public void setSummary(List<AvalaraTaxSummary> summary) {
        this.summary = summary;
    }

    @Override
    public String toString() {
        return "AvalaraAdjustTransactionResponse{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", date='" + date + '\'' +
                ", type='" + type + '\'' +
                ", currencyCode='" + currencyCode + '\'' +
                ", exchangeRateCurrencyCode='" + exchangeRateCurrencyCode + '\'' +
                ", purchaseOrderNo='" + purchaseOrderNo + '\'' +
                ", totalAmount=" + totalAmount +
                ", totalTax=" + totalTax +
                ", totalTaxable=" + totalTaxable +
                ", totalTaxCalculated=" + totalTaxCalculated +
                ", adjustmentReason='" + adjustmentReason + '\'' +
                ", country='" + country + '\'' +
                ", region='" + region + '\'' +
                ", lines=" + lines +
                ", addresses=" + addresses +
                ", summary=" + summary +
                '}';
    }
}
