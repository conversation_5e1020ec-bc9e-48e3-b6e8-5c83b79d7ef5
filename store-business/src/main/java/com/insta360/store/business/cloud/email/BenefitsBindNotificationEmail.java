package com.insta360.store.business.cloud.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @description: 权益发送通知邮件
 * @author: py
 * @create: 2024-05-21 10:35
 */
@Scope("prototype")
@Component
public class BenefitsBindNotificationEmail extends BaseCloudEmail {

    @Override
    public String getTemplateName() {
        return "insta360_subscription_benefits_tobind_notification";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        templateParams.addBodyParam("care_service", getBenefitTypeExistMap().get(BenefitType.CARE));
        templateParams.addBodyParam("extend_service", getBenefitTypeExistMap().get(BenefitType.EXTEND));
        templateParams.addBodyParam("bind_url", getBindLink());
    }

    @Override
    protected InstaLanguage getLanguage() {
        if (InstaCountry.HK.equals(parseCountry() != null ? parseCountry() : null)) {
            return InstaLanguage.zh_TW;
        }
        return super.getLanguage();
    }

    @Override
    protected InstaCountry parseCountry() {
        String countryCode = getCloudStorageStoreBenefit().parseRegion();
        return InstaCountry.parse(countryCode);
    }
}
