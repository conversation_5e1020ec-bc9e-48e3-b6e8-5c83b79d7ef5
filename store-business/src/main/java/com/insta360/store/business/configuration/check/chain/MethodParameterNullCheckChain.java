package com.insta360.store.business.configuration.check.chain;

import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.outgoing.mq.check.bo.MethodParamCheckBO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 方法参数null检查chain
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/3
 */
@Component
public class MethodParameterNullCheckChain extends BaseMethodParameterCheckChain {

    @Override
    public Boolean doCheck(DoubleCheckBO doubleCheckBO) {
        MethodParamCheckBO methodParamCheckBO = doubleCheckBO.getMethodParamCheckBo();

        // 判断是否要进行null检查¬
        Boolean checkNull = methodParamCheckBO.getCheckNull();
        if (!checkNull) {
            return true;
        }

        // 只要有一个为null就不通过
        List<Object> needCheckArgs = methodParamCheckBO.getNeedCheckArgs();
        for (Object needCheckArg : needCheckArgs) {
           if (Objects.isNull(needCheckArg)){
               return false;
           }
        }
        return true;
    }

    @Override
    public String getName() {
        return "参数是否为空";
    }
}
