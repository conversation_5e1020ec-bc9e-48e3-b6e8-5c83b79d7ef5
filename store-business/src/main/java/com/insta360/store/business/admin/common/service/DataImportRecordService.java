package com.insta360.store.business.admin.common.service;

import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.admin.common.model.DataImportRecord;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-05-10
 * @Description:
 */
public interface DataImportRecordService extends BaseService<DataImportRecord> {

    /**
     * 根据工号和类型查询
     *
     * @param account
     * @param type
     * @param pageQuery
     */
    PageResult<DataImportRecord> listByAccountAndType(String account, String type, PageQuery pageQuery);
}
