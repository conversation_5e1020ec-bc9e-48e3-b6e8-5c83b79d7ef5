package com.insta360.store.business.trade.dto;

import com.insta360.compass.core.util.BeanUtil;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/12/08
 * @Description: 信用卡支付订单查询dto
 */
public class CreditCardPaymentInfoQueryDTO {

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 卡种
     */
    private String creditCardType;

    /**
     * 支付机构（ocean || cko）
     */
    private String payMethod;

    /**
     * 支付通道
     */
    private Integer payChannelId;

    /**
     * 订单所属国家
     */
    private String orderCountry;

    /**
     * forter决策
     */
    private String forterDecision;

    /**
     * 是否3d
     */
    private Boolean isThreeDomainSecure;

    /**
     * 订单创建开始时间
     */
    private LocalDateTime orderCreateStartTime;

    /**
     * 订单创建结束时间
     */
    private LocalDateTime orderCreateEndTime;

    /**
     * 支付发起开始时间
     */
    private LocalDateTime orderPayRequestStartTime;

    /**
     * 支付发起结束时间
     */
    private LocalDateTime orderPayRequestEndTime;

    /**
     * 支付结果
     */
    private Boolean payResult;

    /**
     * page number
     */
    private Integer pageNumber;

    /**
     * page size
     */
    private Integer pageSize;

    /**
     * 数据引用转换
     *
     * @param tradeExportParam
     * @return
     */
    public static CreditCardPaymentInfoQueryDTO swapDataReference(TradeExportDTO tradeExportParam) {
        if (tradeExportParam != null) {
            CreditCardPaymentInfoQueryDTO paymentInfoQueryParam = new CreditCardPaymentInfoQueryDTO();
            BeanUtil.copyProperties(tradeExportParam, paymentInfoQueryParam);
            return paymentInfoQueryParam;
        }
        return null;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getCreditCardType() {
        return creditCardType;
    }

    public void setCreditCardType(String creditCardType) {
        this.creditCardType = creditCardType;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getPayChannelId() {
        return payChannelId;
    }

    public void setPayChannelId(Integer payChannelId) {
        this.payChannelId = payChannelId;
    }

    public String getOrderCountry() {
        return orderCountry;
    }

    public void setOrderCountry(String orderCountry) {
        this.orderCountry = orderCountry;
    }

    public String getForterDecision() {
        return forterDecision;
    }

    public void setForterDecision(String forterDecision) {
        this.forterDecision = forterDecision;
    }

    public Boolean getThreeDomainSecure() {
        return isThreeDomainSecure;
    }

    public void setThreeDomainSecure(Boolean threeDomainSecure) {
        isThreeDomainSecure = threeDomainSecure;
    }

    public LocalDateTime getOrderCreateStartTime() {
        return orderCreateStartTime;
    }

    public void setOrderCreateStartTime(LocalDateTime orderCreateStartTime) {
        this.orderCreateStartTime = orderCreateStartTime;
    }

    public LocalDateTime getOrderCreateEndTime() {
        return orderCreateEndTime;
    }

    public void setOrderCreateEndTime(LocalDateTime orderCreateEndTime) {
        this.orderCreateEndTime = orderCreateEndTime;
    }

    public LocalDateTime getOrderPayRequestStartTime() {
        return orderPayRequestStartTime;
    }

    public void setOrderPayRequestStartTime(LocalDateTime orderPayRequestStartTime) {
        this.orderPayRequestStartTime = orderPayRequestStartTime;
    }

    public LocalDateTime getOrderPayRequestEndTime() {
        return orderPayRequestEndTime;
    }

    public void setOrderPayRequestEndTime(LocalDateTime orderPayRequestEndTime) {
        this.orderPayRequestEndTime = orderPayRequestEndTime;
    }

    public Boolean getPayResult() {
        return payResult;
    }

    public void setPayResult(Boolean payResult) {
        this.payResult = payResult;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "CreditCardPaymentInfoQueryDTO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", creditCardType='" + creditCardType + '\'' +
                ", payMethod='" + payMethod + '\'' +
                ", payChannelId=" + payChannelId +
                ", orderCountry='" + orderCountry + '\'' +
                ", forterDecision='" + forterDecision + '\'' +
                ", isThreeDomainSecure=" + isThreeDomainSecure +
                ", orderCreateStartTime=" + orderCreateStartTime +
                ", orderCreateEndTime=" + orderCreateEndTime +
                ", orderPayRequestStartTime=" + orderPayRequestStartTime +
                ", orderPayRequestEndTime=" + orderPayRequestEndTime +
                ", payResult=" + payResult +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                '}';
    }
}
