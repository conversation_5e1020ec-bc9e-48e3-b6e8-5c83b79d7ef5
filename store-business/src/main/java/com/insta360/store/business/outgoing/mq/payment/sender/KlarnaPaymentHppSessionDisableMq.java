package com.insta360.store.business.outgoing.mq.payment.sender;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.outgoing.mq.payment.dto.PaymentMessageDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/05/05
 * @Description:
 */
@Component
public class KlarnaPaymentHppSessionDisableMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(KlarnaPaymentHppSessionDisableMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.klarna_hpp_session_disable, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * klarna hpp session 禁用事件
     *
     * @param orderNumber
     * @param hppSessions
     */
    public void sendKlarnaPaymentHppSessionDisableMessage(String orderNumber, List<String> hppSessions) {
        if (CollectionUtils.isEmpty(hppSessions)) {
            LOGGER.error("klarna hpp session 禁用事件发送失败。session list 为空。order_number:{}", orderNumber);
            return;
        }

        LOGGER.info("send klarna payment hpp session disable message. pending... hpp sessions:{}", hppSessions);

        PaymentMessageDTO paymentMessage = new PaymentMessageDTO();
        paymentMessage.setOrderNumber(orderNumber);
        paymentMessage.setKlarnaHppSessions(hppSessions);
        String messageId = rocketTcpMessageSender.sendMessage(JSONObject.toJSONString(paymentMessage));
        MqUtils.isBlankMessageIdHandle(messageId, this, paymentMessage);

        LOGGER.info("send klarna payment hpp session disable message. success... hpp sessions:{}", hppSessions);
    }
}
