package com.insta360.store.business.discount.dto.bo;

import com.insta360.store.business.order.model.OrderItem;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/7/13
 */
public class PaymentChannelDiscountResultBO implements Serializable {

    /**
     * 运费支付优惠金额
     */
    private BigDecimal shippingFeePayDiscount;

    /**
     * 固定税金支付优惠金额
     */
    private BigDecimal fixedTaxPayDiscount;

    /**
     * 支付优惠总金额（累积了商品、运费、固定税金的支付优惠金额）
     */
    private BigDecimal paymentChannelTotalDiscount;

    /**
     * 订单商品
     */
    private List<OrderItem> orderItemList;

    public PaymentChannelDiscountResultBO() {
    }

    public PaymentChannelDiscountResultBO(BigDecimal shippingFeePayDiscount, BigDecimal fixedTaxPayDiscount, BigDecimal paymentChannelTotalDiscount, List<OrderItem> orderItemList) {
        this.shippingFeePayDiscount = shippingFeePayDiscount;
        this.fixedTaxPayDiscount = fixedTaxPayDiscount;
        this.paymentChannelTotalDiscount = paymentChannelTotalDiscount;
        this.orderItemList = orderItemList;
    }

    public BigDecimal getShippingFeePayDiscount() {
        return shippingFeePayDiscount;
    }

    public void setShippingFeePayDiscount(BigDecimal shippingFeePayDiscount) {
        this.shippingFeePayDiscount = shippingFeePayDiscount;
    }

    public BigDecimal getFixedTaxPayDiscount() {
        return fixedTaxPayDiscount;
    }

    public void setFixedTaxPayDiscount(BigDecimal fixedTaxPayDiscount) {
        this.fixedTaxPayDiscount = fixedTaxPayDiscount;
    }

    public BigDecimal getPaymentChannelTotalDiscount() {
        return paymentChannelTotalDiscount;
    }

    public void setPaymentChannelTotalDiscount(BigDecimal paymentChannelTotalDiscount) {
        this.paymentChannelTotalDiscount = paymentChannelTotalDiscount;
    }

    public List<OrderItem> getOrderItemList() {
        return orderItemList;
    }

    public void setOrderItemList(List<OrderItem> orderItemList) {
        this.orderItemList = orderItemList;
    }

    @Override
    public String toString() {
        return "PaymentChannelDiscountResultBO{" +
                "shippingFeePayDiscount=" + shippingFeePayDiscount +
                ", fixedTaxPayDiscount=" + fixedTaxPayDiscount +
                ", paymentChannelTotalDiscount=" + paymentChannelTotalDiscount +
                ", orderItemList=" + orderItemList +
                '}';
    }
}
