package com.insta360.store.business.insurance.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/2/8 下午5:14
 */
public class SuitableProductDTO implements Serializable {

    /**
     * 适用的产品id
     */
    private Integer suitableProductId;

    /**
     * 适用的套餐id集合
     */
    private List<Integer> suitableCommodityIds;

    public Integer getSuitableProductId() {
        return suitableProductId;
    }

    public void setSuitableProductId(Integer suitableProductId) {
        this.suitableProductId = suitableProductId;
    }

    public List<Integer> getSuitableCommodityIds() {
        return suitableCommodityIds;
    }

    public void setSuitableCommodityIds(List<Integer> suitableCommodityIds) {
        this.suitableCommodityIds = suitableCommodityIds;
    }

    @Override
    public String toString() {
        return "SuitableProductDTO{" +
                "suitableProductId=" + suitableProductId +
                ", suitableCommodityIds=" + suitableCommodityIds +
                '}';
    }
}