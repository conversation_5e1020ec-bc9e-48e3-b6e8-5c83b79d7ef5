package com.insta360.store.business.discount.service.impl.helper;

import com.insta360.store.business.discount.dto.bo.DiscountAverageBO;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderItemAverage;
import com.insta360.store.business.order.service.impl.handler.OrderCoreCalculateHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 交易券折扣金额分摊处理类
 * @Date 2022/4/27
 */
@Component
public class TradeDiscountApportionmentHelper {

    @Autowired
    OrderCoreCalculateHandler orderCoreCalculateHandler;


    /**
     * 将订单项列表拆分为折扣平均值对象列表。
     *
     * @param orderItems 订单项列表，不可为null或空。每个订单项代表一个商品在订单中的详细信息，包括商品数量、价格等。
     * @return 返回一个折扣平均值对象列表，每个对象代表一个订单项的折扣信息。
     */
    public List<DiscountAverageBO> split(List<OrderItem> orderItems) {
        // 检查传入的订单项列表是否为空，如果为空则直接返回一个空列表
        if (CollectionUtils.isEmpty(orderItems)) {
            return new ArrayList<>();
        }

        // 使用Stream API对订单项列表进行映射操作，将每个订单项映射为它的折扣平均值对象，然后收集到一个列表中返回
        return orderItems.stream()
                .filter(orderItem -> Objects.nonNull(orderItem.getTotalDiscount()) && orderItem.getTotalDiscount().compareTo(BigDecimal.ZERO) > 0)
                .map(orderItem -> this.itemDiscountAmountSplit(orderItem))
                .collect(Collectors.toList());
    }


    /**
     * 计算订单项的折扣金额分割。
     * 该方法用于根据订单项的数量，将总折扣金额平均分配到每个订单项上，并返回每个订单项的平均折扣金额和其他相关信息。
     *
     * @param orderItem 订单项对象，包含订单项的总折扣金额和数量等信息。
     * @return DiscountAverageBO 折扣平均信息对象，包含订单项ID、平均折扣金额和平均计算的相关列表。
     */
    private DiscountAverageBO itemDiscountAmountSplit(OrderItem orderItem) {
        // 计算每个订单项的平均折扣金额
        BigDecimal totalDiscount = orderItem.getTotalDiscount();
        BigDecimal itemDiscountAverageAmount = totalDiscount.divide(new BigDecimal(String.valueOf(orderItem.getNumber())), 2, BigDecimal.ROUND_DOWN);

        // 调用核心计算处理器，进行平均计算，获取更详细的平均信息列表
        List<OrderItemAverage> orderItemAverageList = orderCoreCalculateHandler.averageCalculate(orderItem, totalDiscount);

        // 构造并返回折扣平均信息对象
        return new DiscountAverageBO(orderItem.getId(), itemDiscountAverageAmount, orderItemAverageList);
    }
}
