package com.insta360.store.business.search.service.impl.handler;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.search.dto.SearchQueryDTO;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.outgoing.rpc.base.bo.SearchRequestBO;
import com.insta360.store.business.outgoing.rpc.base.service.InfrBaseService;
import com.insta360.store.business.search.bo.OpenSearchQueryPageBO;
import com.insta360.store.business.search.dto.SearchSpecialQueryDTO;
import com.insta360.store.business.utils.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/5
 */
@Component
public class SearchQueryHandler extends BaseSearchConditionConvertHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchQueryHandler.class);

    @Autowired
    InfrBaseService infrBaseService;

    @Override
    public OpenSearchQueryPageBO search(SearchQueryDTO searchQueryParam) {
        LOGGER.info("[商城搜索]search request : {}", JSON.toJSONString(searchQueryParam));
        if (Objects.isNull(searchQueryParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 设置默认查询参数
        SearchRequestBO searchRequestBo = this.searchQueryParamConvert(searchQueryParam);
        LOGGER.info("[商城搜索]search searchRequestBo : {}", JSON.toJSONString(searchRequestBo));

        // 搜索
        OpenSearchQueryPageBO openSearchQueryPageResult = null;
        try {
            Response<OpenSearchQueryPageBO> response = infrBaseService.search(searchRequestBo);
            if (Objects.nonNull(response)) {
                openSearchQueryPageResult = response.getData();
            }
        } catch (Exception e) {
            LOGGER.error(String.format("[商城搜索]search error. request : %s", JSON.toJSONString(searchRequestBo)), e);
        }
        return openSearchQueryPageResult;
    }

    @Override
    public OpenSearchQueryPageBO suggest(SearchSpecialQueryDTO searchSpecialQueryParam) {
        LOGGER.info("[商城搜索]suggest request : {}", JSON.toJSONString(searchSpecialQueryParam));
        if (Objects.isNull(searchSpecialQueryParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 参数检查
        CommonUtil.validationObject(searchSpecialQueryParam);
        // 设置默认查询参数
        SearchRequestBO searchRequestBo = this.suggestQueryParamConvert(searchSpecialQueryParam, 5, 1);

        LOGGER.info("[商城搜索]suggest searchRequestBo : {}", JSON.toJSONString(searchRequestBo));

        // 搜索
        OpenSearchQueryPageBO openSearchQueryPageResult = null;
        try {
            Response<OpenSearchQueryPageBO> response = infrBaseService.search(searchRequestBo);
            if (Objects.nonNull(response)) {
                openSearchQueryPageResult = response.getData();
            }
            LOGGER.info("[商城搜索]suggest response : {}", JSON.toJSONString(response));
        } catch (Exception e) {
            LOGGER.error(String.format("[商城搜索]search suggest error. request : %s", JSON.toJSONString(searchRequestBo)), e);
        }
        return openSearchQueryPageResult;
    }

    @Override
    public OpenSearchQueryPageBO filterSuggest(SearchSpecialQueryDTO searchSpecialQueryParam) {
        LOGGER.info("[商城搜索]search filterSuggest request : {}", JSON.toJSONString(searchSpecialQueryParam));
        if (Objects.isNull(searchSpecialQueryParam)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 参数检查
        CommonUtil.validationObject(searchSpecialQueryParam);
        // 设置默认查询参数
        SearchRequestBO searchRequestBo = this.filterQueryParamConvert(searchSpecialQueryParam, 500, 1);

        LOGGER.info("[商城搜索]search filterSuggest searchRequestBo : {}", JSON.toJSONString(searchRequestBo));

        // 搜索
        OpenSearchQueryPageBO openSearchQueryPageResult = null;
        try {
            Response<OpenSearchQueryPageBO> response = infrBaseService.search(searchRequestBo);
            if (Objects.nonNull(response)) {
                openSearchQueryPageResult = response.getData();
            }
            LOGGER.info("[商城搜索]search filterSuggest response : {}", JSON.toJSONString(response));
        } catch (Exception e) {
            LOGGER.error(String.format("[商城搜索]search filterSuggest error. request: %s", JSON.toJSONString(searchRequestBo)), e);
        }
        return openSearchQueryPageResult;
    }
}
