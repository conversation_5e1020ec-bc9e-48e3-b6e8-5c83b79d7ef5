package com.insta360.store.business.trade.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-06-04
 * @Description:
 */
@TableName("credit_card_pre_pay_rule_info")
public class CreditCardPrePayRuleInfo extends BaseModel<CreditCardPrePayRuleInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * Snowflake ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 前置支付规则id
     */
    private Long prePayRuleId;

    /**
     * 地区
     */
    private String country;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPrePayRuleId() {
        return prePayRuleId;
    }

    public void setPrePayRuleId(Long prePayRuleId) {
        this.prePayRuleId = prePayRuleId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CreditCardPrePayRuleInfo{" +
                "id=" + id +
                ", prePayRuleId=" + prePayRuleId +
                ", country=" + country +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}