package com.insta360.store.business.trade.service;

import com.insta360.store.business.trade.dto.CreditCardPrePayRuleDTO;
import com.insta360.store.business.trade.model.CreditCardPrePayRule;
import com.insta360.compass.core.common.BaseService;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-06-04
 * @Description:
 */
public interface CreditCardPrePayRuleService extends BaseService<CreditCardPrePayRule> {

    /**
     * 新增前置支付路由规则
     *
     * @param creditCardPrePayRule
     * @param creditCardPrePayRuleParam
     */
    void addPreCreditCardPayRule(CreditCardPrePayRule creditCardPrePayRule, CreditCardPrePayRuleDTO creditCardPrePayRuleParam);

    /**
     * 更新前置支付路由规则
     *
     * @param creditCardPrePayRuleParam
     */
    void updatePreCreditCardPayRule(CreditCardPrePayRuleDTO creditCardPrePayRuleParam);

    /**
     * 批量删除前置支付路由规则
     *
     * @param cardPrePayRuleList
     */
    void deletePreCreditCardPayRules(List<CreditCardPrePayRule> cardPrePayRuleList);

    /**
     * 查询所有路由配置
     *
     * @return
     */
    List<CreditCardPrePayRule> listPreCreditCardPayRules();

}
