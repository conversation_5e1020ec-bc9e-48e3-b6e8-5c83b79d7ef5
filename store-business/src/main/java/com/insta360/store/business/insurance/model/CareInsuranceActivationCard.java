package com.insta360.store.business.insurance.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.insurance.constant.InsuranceCommonConstant;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-04-09
 * @Description:
 */
@TableName("care_insurance_activation_card")
public class CareInsuranceActivationCard extends BaseModel<CareInsuranceActivationCard> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String insuranceNumber;

    /**
     * 激活码
     */
    @JSONField(name = "activation_code")
    private String activationCode;

    /**
     * 序列号
     */
    @JSONField(name = "device_serial")
    private String deviceSerial;

    /**
     * 设备类型
     */
    @JSONField(name = "device_type")
    private String deviceType;

    /**
     * 保险类型
     */
    @JSONField(name = "insurance_type")
    private String insuranceType;

    /**
     * 国家
     */
    private String country;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * care绑定时间
     */
    @JSONField(name = "bind_time")
    private LocalDateTime bindTime;

    /**
     * 使用时间
     */
    @JSONField(name = "use_time")
    private LocalDateTime useTime;

    /**
     * care过期时间
     */
    @JSONField(name = "expire_time")
    private LocalDateTime expireTime;

    @JSONField(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 剩余使用次数[仅针对care+]
     * 默认为2 可使用两次 次数不能低于0
     */
    private Integer remainingUsageCount;

    /**
     * 禁用
     */
    private Boolean disabled;

    /**
     * 服务是否可用
     *
     * @return
     */
    public Boolean serviceStillAvailable() {
        // 是否在有效期内
        boolean isNotExpired = expireTime != null && expireTime.isAfter(LocalDateTime.now());
        // 是否还有可用次数
        boolean remainingCount = remainingUsageCount != null && remainingUsageCount > InsuranceCommonConstant.USAGE_ZERO;
        return isNotExpired && remainingCount;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public String getActivationCode() {
        return activationCode;
    }

    public void setActivationCode(String activationCode) {
        this.activationCode = activationCode;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDateTime getBindTime() {
        return bindTime;
    }

    public void setBindTime(LocalDateTime bindTime) {
        this.bindTime = bindTime;
    }

    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Integer getRemainingUsageCount() {
        return remainingUsageCount;
    }

    public void setRemainingUsageCount(Integer remainingUsageCount) {
        this.remainingUsageCount = remainingUsageCount;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public String toString() {
        return "CareInsuranceActivationCard{" +
                "id=" + id +
                ", insuranceNumber='" + insuranceNumber + '\'' +
                ", activationCode='" + activationCode + '\'' +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", insuranceType='" + insuranceType + '\'' +
                ", country='" + country + '\'' +
                ", name='" + name + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", bindTime=" + bindTime +
                ", useTime=" + useTime +
                ", expireTime=" + expireTime +
                ", createTime=" + createTime +
                ", remainingUsageCount=" + remainingUsageCount +
                ", disabled=" + disabled +
                '}';
    }
}