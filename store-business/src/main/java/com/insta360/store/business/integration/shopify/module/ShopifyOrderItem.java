package com.insta360.store.business.integration.shopify.module;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-05-09
 * @Description: 
 */
@TableName(value = "shopify_order_item")
public class ShopifyOrderItem extends BaseModel<ShopifyOrderItem> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "`order`")
    private String order;

    @TableId(value = "sku")
    private String sku;

    private Integer number;

    private String currency;

    @JSONField(name = "item_price")
    private Float itemPrice;

    private Float discount;

    private Float tax;

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Float getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(Float itemPrice) {
        this.itemPrice = itemPrice;
    }

    public Float getDiscount() {
        return discount;
    }

    public void setDiscount(Float discount) {
        this.discount = discount;
    }

    public Float getTax() {
        return tax;
    }

    public void setTax(Float tax) {
        this.tax = tax;
    }

    @Override
    public String toString() {
        return "ShopifyOrderItem{" +
        "order=" + order +
        ", sku=" + sku +
        ", number=" + number +
        ", currency=" + currency +
        ", itemPrice=" + itemPrice +
        ", discount=" + discount +
        ", tax=" + tax +
        "}";
    }
}