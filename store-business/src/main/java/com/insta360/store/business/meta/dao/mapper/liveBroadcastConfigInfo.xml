<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.meta.dao.LiveBroadcastConfigInfoDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.meta.dao.LiveBroadcastConfigInfoDao"/>

    <!-- 批量创建多语言信息 -->
    <insert id="addLiveBroadcastInfo" parameterType="java.util.List">
        INSERT INTO live_broadcast_config_info(live_broadcast_config_id, country, `language`,
        title, create_time, update_time)
        VALUES
        <foreach collection="liveBroadcastConfigInfoList" item="liveBroadcastConfigInfo" separator=",">
            (
            #{liveBroadcastConfigInfo.liveBroadcastConfigId}, #{liveBroadcastConfigInfo.country},
             #{liveBroadcastConfigInfo.language},#{liveBroadcastConfigInfo.title},
             #{liveBroadcastConfigInfo.createTime},#{liveBroadcastConfigInfo.updateTime}
            )
        </foreach>
    </insert>
</mapper>
