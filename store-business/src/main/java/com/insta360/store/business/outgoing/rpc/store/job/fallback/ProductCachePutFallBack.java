package com.insta360.store.business.outgoing.rpc.store.job.fallback;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.outgoing.rpc.store.job.ProductCachePutService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 产品页缓存更新RPC服务失败回调实现类
 * @Date 2023/11/2
 */
@Component
public class ProductCachePutFallBack implements ProductCachePutService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductCachePutFallBack.class);

    @Override
    public void getInfo(Integer productId, InstaCountry country, InstaLanguage language) {
        LOGGER.error(String.format("store-service调用失败。路径：/rpc/store/service/cacheput/product/getInfo。" +
                "productId:{%s},country:{%s},language:{%s}", productId, country, language));
    }

    @Override
    public void getProductAccessoryCompatibility(Integer productId, InstaLanguage language) {
        LOGGER.error(String.format("store-service调用失败。路径：/rpc/store/service/cacheput/product/getProductAccessoryCompatibility。" +
                "productId:{%s},language:{%s}", productId, language));
    }
}
