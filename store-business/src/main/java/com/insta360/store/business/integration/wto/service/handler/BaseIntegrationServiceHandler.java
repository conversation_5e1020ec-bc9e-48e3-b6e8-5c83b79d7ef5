package com.insta360.store.business.integration.wto.service.handler;

import com.insta360.store.business.integration.wto.service.factory.IntegrationSimpleFactory;
import com.insta360.store.business.outgoing.mq.wto.dto.StoreDataSyncOmsMqDTO;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description:
 * @author: py
 * @create: 2025-01-20 16:31
 */
public abstract class BaseIntegrationServiceHandler {

    @Autowired
    IntegrationSimpleFactory integrationSimpleFactory;

    /**
     * 消费消息
     *
     * @param storeDataSyncOmsMqDto
     */
    public abstract void executeMessage(StoreDataSyncOmsMqDTO storeDataSyncOmsMqDto);
}
