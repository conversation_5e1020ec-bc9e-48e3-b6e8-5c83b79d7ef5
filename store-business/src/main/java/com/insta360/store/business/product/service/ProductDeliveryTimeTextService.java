package com.insta360.store.business.product.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.product.model.ProductDeliveryTimeText;

import java.util.Map;

/**
 * @Author: hyc
 * @Date: 2019/1/24
 * @Description:
 */
public interface ProductDeliveryTimeTextService extends BaseService<ProductDeliveryTimeText> {

    /**
     * 获取产品某个语言的预计发货时间文案
     *
     * @param productId
     * @param language
     * @return
     */
    ProductDeliveryTimeText getDeliveryTimeText(Integer productId, InstaLanguage language);

    /**
     * 获取产品的预计发货时间的文案
     *
     * @param productId
     * @return
     */
    Map<InstaLanguage, String> getDeliveryTimeTextMaps(Integer productId);

    /**
     * 获取套餐的预计发货时间的文案
     *
     * @param productId
     * @return
     */
    Map<InstaLanguage, String> getDeliveryTimeTextMaps(Integer productId, Integer commodityId);
}
