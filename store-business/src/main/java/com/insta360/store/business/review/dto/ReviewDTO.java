package com.insta360.store.business.review.dto;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2022/07/05
 * @Description:
 */
public class ReviewDTO {

    /**
     * 评论号
     */
    private String reviewNumber;

    /**
     * 评论id
     */
    private Integer reviewId;

    /**
     * 排序类型
     */
    private String sortType;

    /**
     * 评论备注
     */
    private String reviewRemark;

    /**
     * 评论回复
     */
    private String reviewReply;

    /**
     * 置顶
     */
    private Boolean topTag;


    /**
     * 资源ID
     */
    private List<Integer> reviewResourceIdList;

    public Integer getReviewId() {
        return reviewId;
    }

    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    public String getReviewReply() {
        return reviewReply;
    }

    public void setReviewReply(String reviewReply) {
        this.reviewReply = reviewReply;
    }

    public Boolean getTopTag() {
        return topTag;
    }

    public void setTopTag(Boolean topTag) {
        this.topTag = topTag;
    }

    public String getReviewNumber() {
        return reviewNumber;
    }

    public void setReviewNumber(String reviewNumber) {
        this.reviewNumber = reviewNumber;
    }

    public List<Integer> getReviewResourceIdList() {
        return reviewResourceIdList;
    }

    public void setReviewResourceIdList(List<Integer> reviewResourceIdList) {
        this.reviewResourceIdList = reviewResourceIdList;
    }

    @Override
    public String toString() {
        return "ReviewDTO{" +
                "reviewNumber=" + reviewNumber +
                ", reviewId=" + reviewId +
                ", sortType='" + sortType + '\'' +
                ", reviewRemark='" + reviewRemark + '\'' +
                ", reviewReply='" + reviewReply + '\'' +
                ", topTag=" + topTag +
                ", reviewResourceIdList=" + reviewResourceIdList +
                '}';
    }
}
