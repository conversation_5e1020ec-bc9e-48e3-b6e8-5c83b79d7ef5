package com.insta360.store.business.meta.robot;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.util.HttpUtil;
import com.insta360.compass.libs.feishu.FeishuMessageSender;
import com.insta360.compass.libs.feishu.dto.FeishuMessageDTO;
import com.insta360.compass.libs.feishu.enums.MsgTypeEnum;
import com.insta360.store.business.configuration.utils.SpringContextLocator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * fei shu message utils
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/2
 */
public class FeiShuMessageUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FeiShuMessageUtil.class);

    /**
     * 是否是测试环境（dev/test）
     */
    private static final Boolean IS_TEST;

    /**
     * 是否是测试环境（dev）
     */
    private static final Boolean IS_DEV;

    /**
     * 消息前缀
     */
    private static final String MSG_PREFIX;

    /**
     * 群名：S.商城项目【测试环境】飞书通知
     */
    private static final String TEST_FEISHU_WEBHOOK = "https://open.feishu.cn/open-apis/bot/v2/hook/f94f8a2d-5441-4af8-b607-32e866a419f2";

    /**
     * 飞书信息发送
     */
    private static final FeishuMessageSender FEISHU_MESSAGE_SENDER;

    static {
        FEISHU_MESSAGE_SENDER = SpringContextLocator.getBean(FeishuMessageSender.class);
        FeiShuRobotConfiguration feiShuRobotConfiguration = SpringContextLocator.getBean(FeiShuRobotConfiguration.class);
        IS_TEST = feiShuRobotConfiguration.getTest();
        IS_DEV = feiShuRobotConfiguration.getDev();
        MSG_PREFIX = feiShuRobotConfiguration.getEnvironment();
    }

    /**
     * 发送商城通用飞书通知
     *
     * @param message    信息
     * @param groupRobot 群机器人
     * @param atUsers    at用户
     */
    public static void storeGeneralMessage(String message, FeiShuGroupRobot groupRobot, FeiShuAtUser... atUsers) {
        try {
            doSend(message, groupRobot, atUsers);
        } catch (Exception e) {
            LOGGER.error("飞书发送通知发生异常,message:{},group:{},atUsers:{}", message, groupRobot, atUsers);
            LOGGER.error("飞书发送异常信息:", e);
        }
    }

    /**
     * 发送商城通用飞书通知
     *
     * @param message    信息
     * @param groupRobot 群机器人
     * @param atUsers    at用户
     * @param id         通知ID
     */
    public static void storeGeneralMessage(String id, String message, FeiShuGroupRobot groupRobot, FeiShuAtUser... atUsers) {
        LOGGER.info("发送飞书通知，id为:{}", id);
        storeGeneralMessage(message, groupRobot, atUsers);
    }

    /**
     * 执行发送逻辑
     *
     * @param message    信息
     * @param groupRobot 群机器人
     * @param atUsers    at用户
     */
    private static void doSend(String message, FeiShuGroupRobot groupRobot, FeiShuAtUser... atUsers) {
        List<String> userCodeList = null;
        if (ArrayUtil.isNotEmpty(atUsers)) {
            userCodeList = Arrays.stream(atUsers).filter(Objects::nonNull).map(FeiShuAtUser::getCode).collect(Collectors.toList());
        }
        // 封装消息内容 环境 标题 消息体，
        String content = String.format("【%s】\n%s %s\n", MSG_PREFIX, groupRobot.getTitle(), message);

        FeishuMessageDTO feishuMessageParam = new FeishuMessageDTO();
        feishuMessageParam.setGroupCode(groupRobot.getCode());
        feishuMessageParam.setUserCodes(userCodeList);
        feishuMessageParam.setMsgType(MsgTypeEnum.text);
        feishuMessageParam.setContent(content);

        // 本地测试/测试环境不at用户 直接通过openapi发送，不at用户
        if (IS_DEV || IS_TEST) {
            doDevSend(feishuMessageParam);
            return;
        }

        FEISHU_MESSAGE_SENDER.send(feishuMessageParam);
    }

    /**
     * 执行发送逻辑
     *
     * @param message    信息
     * @param groupRobot 群机器人
     * @param atUsers    at用户
     */
    @Deprecated
    public static void doSendTestProd(String message, FeiShuGroupRobot groupRobot, FeiShuAtUser... atUsers) {
        List<String> userCodeList = null;
        if (ArrayUtil.isNotEmpty(atUsers)) {
            userCodeList = Arrays.stream(atUsers).map(FeiShuAtUser::getCode).collect(Collectors.toList());
        }

        // 封装消息内容 环境 标题 消息体
        String content = String.format("【%s】\n%s %s\n", MSG_PREFIX, groupRobot.getTitle(), message);
        FeishuMessageDTO feishuMessageParam = new FeishuMessageDTO();
        feishuMessageParam.setGroupCode(groupRobot.getCode());
        feishuMessageParam.setUserCodes(userCodeList);
        feishuMessageParam.setMsgType(MsgTypeEnum.text);
        feishuMessageParam.setContent(content);

        FEISHU_MESSAGE_SENDER.send(feishuMessageParam);
    }

    /**
     * 本地开发环境
     * 消息发送（飞书应用）
     *
     * @param feishuMessageParam 飞书信息param
     */
    private static void doDevSend(FeishuMessageDTO feishuMessageParam) {
        JSONObject text = new JSONObject();
        text.put("text", feishuMessageParam.getContent());
        JSONObject json = new JSONObject();
        json.put("msg_type", "text");
        json.put("content", text);

        HttpUtil.post(TEST_FEISHU_WEBHOOK, json.toJSONString());
    }
}
