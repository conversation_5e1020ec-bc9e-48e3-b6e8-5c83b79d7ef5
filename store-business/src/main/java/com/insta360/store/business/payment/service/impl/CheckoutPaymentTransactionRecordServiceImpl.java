package com.insta360.store.business.payment.service.impl;

import com.insta360.store.business.payment.model.CheckoutPaymentTransactionRecord;
import com.insta360.store.business.payment.dao.CheckoutPaymentTransactionRecordDao;
import com.insta360.store.business.payment.service.CheckoutPaymentTransactionRecordService;
import com.insta360.compass.core.common.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-11-28
 * @Description:
 */
@Service
public class CheckoutPaymentTransactionRecordServiceImpl extends BaseServiceImpl<CheckoutPaymentTransactionRecordDao, CheckoutPaymentTransactionRecord> implements CheckoutPaymentTransactionRecordService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveRecords(List<CheckoutPaymentTransactionRecord> paymentTransactionRecords) {
        if (CollectionUtils.isEmpty(paymentTransactionRecords)) {
            return;
        }
        this.saveBatch(paymentTransactionRecords);
    }
}
