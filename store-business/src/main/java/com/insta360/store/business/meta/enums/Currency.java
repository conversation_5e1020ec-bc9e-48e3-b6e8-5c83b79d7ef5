package com.insta360.store.business.meta.enums;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description: 新增货币需确认每一个支付方式的价格转换是否需要同步修改
 */
public enum Currency {

    /**
     * 人民币
     */
    CNY("￥", null),

    /**
     * 美元
     */
    USD("$", "US"),

    /**
     * 日元
     */
    JPY("￥", null),

    /**
     * 欧元
     */
    EUR("€", null),

    /**
     * 英镑
     */
    GBP("£", null),

    /**
     * 澳元
     */
    AUD("$", "AU"),

    /**
     * 加拿大元
     */
    CAD("$", "CA"),

    /**
     * 港元
     */
    HKD("$", "HK"),

    /**
     * 韩元
     */
    KRW("₩", null),

    /**
     * 台币
     */
    TWD("$", "NT"),

    /**
     * 纽西兰（临时）
     */
    NZD("$", null),

    /**
     * 新币
     */
    SGD("$", "S"),

    /**
     * 马来西亚令吉
     */
    MYR("$", null),

    /**
     * 泰铢
     */
    THB("฿", null),

    /**
     * 越南盾
     */
    VND("₫", null),

    /**
     * 菲律宾比索
     */
    PHP("₱", null),

    /**
     * 印尼卢比
     */
    IDR("$", null),

    /**
     * 墨西哥比索
     */
    MXN("$", null);

    public static Currency parse(String s) {
        for (Currency currency : values()) {
            if (currency.name().equalsIgnoreCase(s)) {
                return currency;
            }
        }
        return null;
    }

    /**
     * 货币符号
     */
    private final String signal;

    /**
     * 新对外展示的货币
     */
    private final String newCurrency;

    Currency(String signal, String newCurrency) {
        this.signal = signal;
        this.newCurrency = newCurrency;
    }

    /**
     * 日、韩、台三种货币支付价格不可有小数（PayPal）
     *
     * @param currency
     * @return
     */
    public static Boolean isTwdOrKrwOrJpy(Currency currency) {
        return TWD.equals(currency) || KRW.equals(currency) || JPY.equals(currency);
    }

    public String getSignal() {
        return signal;
    }

    public String getNewCurrency() {
        return newCurrency;
    }
}
