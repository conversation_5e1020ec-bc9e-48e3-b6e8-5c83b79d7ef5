package com.insta360.store.business.integration.attentive.lib.module;

/**
 * @description:
 * @author: py
 * @create: 2023-09-05 14:56
 */
public class AttentiveSubscribeLocale {

    /**
     * 语言
     */
    private String language;

    /**
     * 国家
     */
    private String country;

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        return "Locale{" +
                "language='" + language + '\'' +
                ", country='" + country + '\'' +
                '}';
    }
}
