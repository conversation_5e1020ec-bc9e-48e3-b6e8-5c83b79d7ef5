<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.reseller.dao.ResellerGiftConfigRuleDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.reseller.dao.ResellerGiftConfigRuleDao"/>


    <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into reseller_gift_config_rule
        (reseller_config_id,reseller_activity_id,start_time,end_time,freebies_mark,state,remark,operator_number,operator_name)
        values
        <foreach collection="list" item="configRule" separator=",">
            (#{configRule.resellerConfigId},#{configRule.resellerActivityId},#{configRule.startTime},#{configRule.endTime},#{configRule.freebiesMark},#{configRule.state},#{configRule.remark},#{configRule.operatorNumber},#{configRule.operatorName})
        </foreach>
    </insert>

    <update id="updateBatch" >
        <foreach collection="resellerGiftConfigRuleList" item="configRule" separator=";">
            update reseller_gift_config_rule
                <set>
                    <if test="configRule.startTime != null">
                        start_time = #{configRule.startTime},
                    </if>
                    <if test="configRule.endTime != null">
                        end_time = #{configRule.endTime},
                    </if>
                    <if test="configRule.remark != null">
                        remark = #{configRule.remark},
                    </if>
                    <if test="configRule.operatorNumber != null">
                        operator_number = #{configRule.operatorNumber},
                    </if>
                    <if test="configRule.operatorName != null">
                        operator_name = #{configRule.operatorName},
                    </if>
                    <if test="configRule.freebiesMark != null">
                        freebies_mark = #{configRule.freebiesMark},
                    </if>
                    <if test="configRule.state != null">
                        state = #{configRule.state},
                    </if>
                </set>
                where
                    id = #{configRule.id}
                </foreach>
    </update>

    <select id="listByResellerGiftConfigRule" resultType="com.insta360.store.business.reseller.model.ResellerGiftConfigRule" parameterType="com.insta360.store.business.reseller.dto.condition.ResellerGiftConfigRuleQueryCondition">
        select
            r2.id,
            r2.reseller_config_id,
            r2.reseller_activity_id,
            r2.start_time,
            r2.end_time,
            r2.freebies_mark,
            r2.state,
            r2.remark,
            r2.operator_number,
            r2.operator_name
                from reseller_gift_config_new r1
                join reseller_gift_config_rule r2 on r1.id = r2.reseller_config_id
        <where>
            <if test="productId != null" >
                and r1.product_id = #{productId}
            </if>
            <if test="promoCode != null">
                and r1.promo_code = #{promoCode}
            </if>
            <if test="commodityId != null">
                and r1.commodity_id = #{commodityId}
            </if>
            <if test="moduleType != null">
                and r1.default_gift_mark = #{moduleType}
            </if>
            <if test="state != null" >
                and r2.state = #{state}
            </if>
            <if test="remark != null" >
                and r2.remark like concat(#{remark},'%')
            </if>
            <if test="startTime != null" >
                and r2.start_time <![CDATA[>=]]> #{startTime}
            </if>
            <if test="endTime != null" >
                and r2.end_time <![CDATA[<=]]>  #{endTime}
            </if>
            <if test="timeLimit != null" >
                and r2.end_time <![CDATA[>=]]>  #{timeLimit}
            </if>
        </where>
        order by r2.start_time desc
    </select>
</mapper>
