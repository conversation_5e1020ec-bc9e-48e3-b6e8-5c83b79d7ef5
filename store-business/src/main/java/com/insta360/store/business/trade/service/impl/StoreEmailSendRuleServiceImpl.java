package com.insta360.store.business.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.trade.dao.StoreEmailSendRuleDao;
import com.insta360.store.business.trade.model.StoreEmailSendRule;
import com.insta360.store.business.trade.service.StoreEmailSendRuleService;
import org.springframework.stereotype.Service;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-05-15
 * @Description:
 */
@Service
public class StoreEmailSendRuleServiceImpl extends BaseServiceImpl<StoreEmailSendRuleDao, StoreEmailSendRule> implements StoreEmailSendRuleService {

    @Override
    public StoreEmailSendRule getTemplateSendRuleByOrderId(Integer orderId, String templateName) {
        QueryWrapper<StoreEmailSendRule> qw = new QueryWrapper<>();
        qw.eq("template_key", templateName);
        qw.eq("order_id", orderId);
        return baseMapper.selectOne(qw);
    }
}
