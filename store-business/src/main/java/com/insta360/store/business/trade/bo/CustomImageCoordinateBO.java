package com.insta360.store.business.trade.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/12/29 15:43
 * @Description:
 * @Version 1.0
 */
public class CustomImageCoordinateBO implements Serializable {

    /**
     * 自定义图像在 x 轴方向上的坐标。
     */
    private Integer customImageCoordinateX;

    /**
     * 自定义图像在 y 轴方向上的坐标
     */
    private Integer customImageCoordinateY;

    public CustomImageCoordinateBO() {
    }

    public CustomImageCoordinateBO(int customImageCoordinateX, int customImageCoordinateY) {
        this.customImageCoordinateX = customImageCoordinateX;
        this.customImageCoordinateY = customImageCoordinateY;
    }

    public int getCustomImageCoordinateX() {
        return customImageCoordinateX;
    }

    public void setCustomImageCoordinateX(int customImageCoordinateX) {
        this.customImageCoordinateX = customImageCoordinateX;
    }

    public int getCustomImageCoordinateY() {
        return customImageCoordinateY;
    }

    public void setCustomImageCoordinateY(int customImageCoordinateY) {
        this.customImageCoordinateY = customImageCoordinateY;
    }

    @Override
    public String toString() {
        return "CustomImageCoordinateBO{" +
                "customImageCoordinateX=" + customImageCoordinateX +
                ", customImageCoordinateY=" + customImageCoordinateY +
                '}';
    }
}
