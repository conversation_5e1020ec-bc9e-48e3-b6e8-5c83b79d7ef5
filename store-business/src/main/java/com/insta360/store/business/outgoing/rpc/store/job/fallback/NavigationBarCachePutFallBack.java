package com.insta360.store.business.outgoing.rpc.store.job.fallback;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.outgoing.rpc.store.job.NavigationBarCachePutService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2023/11/03
 * @Description:
 */
@Component
public class NavigationBarCachePutFallBack implements NavigationBarCachePutService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NavigationBarCachePutFallBack.class);

    @Override
    public void listNavigationBarCategoryInfos(InstaCountry country, InstaLanguage language) {
        LOGGER.error(String.format("store-service调用失败。路径：/rpc/store/service/cacheput/meta/nbc/listNavigationBarCategoryInfos。" +
                "country:{%s},language:{%s}", country, language));
    }
}
