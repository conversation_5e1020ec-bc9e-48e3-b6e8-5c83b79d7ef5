<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.product.dao.ProductAdapterTypeDao">


    <insert id="saveAdapterType" parameterType="java.util.List">
        insert into product_adapter_type
        (product_id,adapter_type_id,order_index,create_time,update_time)
        values
        <foreach collection="productAdapterTypes" item="type" separator=",">
            (
            #{type.productId},
            #{type.adapterTypeId},
            #{type.orderIndex},
            #{type.createTime},
            #{type.updateTime}
            )
        </foreach>
    </insert>

    <update id="updateOrderIndexByIds">
        <foreach collection="adapterTypes" item="type" separator=";">
        update product_adapter_type
            set order_index = #{type.orderIndex}
            where id = #{type.id}
        </foreach>
    </update>

    <select id="listAccessoryCompatibility"
            resultType="com.insta360.store.business.integration.google.bo.GoogleAdapterTypeBO">
        select pc.id               commodityId,
               pat.adapter_type_id accessoryCompatibilityId
        from product_commodity pc
                 join product p on p.id = pc.product
                 join product_category_subset pcs
                      on (pcs.category_main_key = p.category_key
                          or
                          pcs.category_subset_key = p.category_key)
                 left join product_adapter_type pat on p.id = pat.product_id
        where pcs.category_main_key = 'CM_ACCESSORY';
    </select>
</mapper>



