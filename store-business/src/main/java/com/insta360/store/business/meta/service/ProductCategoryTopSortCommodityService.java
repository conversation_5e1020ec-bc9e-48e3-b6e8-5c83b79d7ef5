package com.insta360.store.business.meta.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.meta.dto.ProductCategoryTopSortCommodityDTO;
import com.insta360.store.business.meta.model.ProductCategoryTopSortCommodity;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-10-11
 * @Description:
 */
public interface ProductCategoryTopSortCommodityService extends BaseService<ProductCategoryTopSortCommodity> {

    /**
     * 新增置顶排序
     *
     * @param sortCommodityList
     */
    void addTopSortCommodity(List<ProductCategoryTopSortCommodity> sortCommodityList);

    /**
     * 置顶排序
     *
     * @param productCategoryTopSortCommodityParam
     */
    void updateTopSortCommodityIndex(List<ProductCategoryTopSortCommodityDTO> productCategoryTopSortCommodityParam);

    /**
     * 根据指定排序id删除
     *
     * @param sortCommodityId
     */
    void deleteTopSortCommodity(Integer sortCommodityId);

    /**
     * 查询置顶排序
     *
     * @return
     */
    List<ProductCategoryTopSortCommodity> listTopSortCommodity();

    /**
     * 根据套餐ids查询
     *
     * @param commodityIds
     * @return
     */
    List<ProductCategoryTopSortCommodity> listByCommodityIds(List<Integer> commodityIds);

    /**
     * 通过分类key查询
     *
     * @param categoryKey
     * @return
     */
    List<ProductCategoryTopSortCommodity> listByCategoryKey(String categoryKey);
}
