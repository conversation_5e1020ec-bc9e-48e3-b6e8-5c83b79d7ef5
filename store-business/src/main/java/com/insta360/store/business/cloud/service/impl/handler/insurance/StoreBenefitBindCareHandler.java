package com.insta360.store.business.cloud.service.impl.handler.insurance;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.cloud.bo.CloudBenefitBindResultBO;
import com.insta360.store.business.cloud.bo.CloudStorageBenefitBO;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.exception.CloudStorageBenefitErrorCode;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitBindContext;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.bo.InsuranceBO;
import com.insta360.store.business.insurance.constant.InsuranceCommonConstant;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceActivationInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2024-05-17 15:24
 */
@Component
public class StoreBenefitBindCareHandler extends BaseStoreBenefitBindHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreBenefitBindCareHandler.class);

    @Override
    public BenefitType getBindType() {
        return BenefitType.CARE;
    }

    @Override
    public CloudBenefitBindResultBO handle(StoreBenefitBindContext storeBenefitBindContext) {
        CloudStorageBenefitBO cloudStorageBenefit = storeBenefitBindContext.getCloudStorageBenefit();
        this.checkActivation(cloudStorageBenefit, storeBenefitBindContext);
        LOGGER.info(String.format("[云服务权益]care激活,参数:%s", storeBenefitBindContext));

        // 具体的绑定操作
        ServiceType serviceType = this.getServiceType(storeBenefitBindContext, getBindType().getValue());

        // 需要激活的相机
        DeviceActivationInfo deviceActivationInfo = deviceInfoHelper.getDeviceActivationInfo(storeBenefitBindContext.getDeviceSerial());
        if (Objects.isNull(deviceActivationInfo)) {
            throw new InstaException(CloudStorageBenefitErrorCode.ProhibitedBindException);
        }

        // 激活时间
        if (!deviceActivationInfo.getCreateTime().plusDays(InsuranceCommonConstant.CLOUD_ACTIVATION_TIME_LIMIT).isAfter(LocalDateTime.now())) {
            // 相机激活超过15天
            InstaException instaException = new InstaException(CloudStorageBenefitErrorCode.CareOverTimeException);
            instaException.putErrorData("deviceType", storeBenefitBindContext.getDeviceType());
            instaException.putErrorData("insuranceType", serviceType.name());
            instaException.putErrorData("cloudType", storeBenefitBindContext.getBusinessType().name());
            throw instaException;
        }
        storeBenefitBindContext.setActivateTime(deviceActivationInfo.getCreateTime() == null ? LocalDateTime.now() : deviceActivationInfo.getCreateTime());

        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(serviceType.name());
        InsuranceBO insuranceParam = new InsuranceBO(storeBenefitBindContext);
        insuranceHandler.businessBindService(insuranceParam);

        // 权益绑定记录
        this.saveBindRecord(storeBenefitBindContext, serviceType, insuranceParam, cloudStorageBenefit.getId());

        // 返回结果
        return this.buildResult(storeBenefitBindContext, serviceType);
    }
}

