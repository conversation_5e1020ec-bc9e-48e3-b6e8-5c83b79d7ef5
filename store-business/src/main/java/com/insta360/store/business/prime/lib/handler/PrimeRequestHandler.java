package com.insta360.store.business.prime.lib.handler;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.common.OkHttpUtils;
import com.insta360.store.business.common.constants.RedisKeyConstant;
import com.insta360.store.business.configuration.graphql.GraphqlConfig;
import com.insta360.store.business.configuration.graphql.GraphqlRequest;
import com.insta360.store.business.configuration.graphql.GraphqlResponse;
import com.insta360.store.business.configuration.graphql.GraphqlUtils;
import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.configuration.trace.TraceLogContext;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.model.StoreSdkCallRecord;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.prime.config.PrimeConfig;
import com.insta360.store.business.prime.enums.PrimeGraphqlOperation;
import com.insta360.store.business.prime.error.PrimeErrorCode;
import com.insta360.store.business.prime.lib.request.BwpTokenRequest;
import com.insta360.store.business.prime.lib.response.BasePrimeResponse;
import com.insta360.store.business.prime.lib.response.BwpTokenResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4
 * @description Amazon Prime GraphQL请求处理器
 * 该类负责处理与Amazon Prime API的所有GraphQL通信，封装请求发送和响应处理的逻辑
 */
@Component
public class PrimeRequestHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrimeRequestHandler.class);

    /**
     * Prime配置信息
     */
    @Autowired
    PrimeConfig primeConfig;

    @Autowired
    StoreSdkCallRecordService storeSdkCallRecordService;

    @TraceLog(logPrefixSub = "获取bwp Token")
    public String getBwpToken() {
        BwpTokenRequest request = new BwpTokenRequest(primeConfig.getClientId(), primeConfig.getClientSecret(), primeConfig.getGrantType());
        String token = RedisTemplateUtil.getValueString(RedisKeyConstant.PRIME_BWP_TOKEN);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        OkHttpUtils.OkHttpResponse okHttpResponse = OkHttpUtils.postFormUrlEncoded(primeConfig.getBwpTokenUrl(), request.buildFormData(), null);
        BwpTokenResponse bwpTokenResponse = JSON.parseObject(okHttpResponse.getResponseBody(), BwpTokenResponse.class);
        RedisTemplateUtil.setKeyValue(RedisKeyConstant.PRIME_BWP_TOKEN, bwpTokenResponse.getAccessToken(), bwpTokenResponse.getExpiresIn() / 2, TimeUnit.SECONDS);
        return bwpTokenResponse.getAccessToken();
    }

    /**
     * 处理GraphQL请求并返回响应
     * 该方法使用注入的配置信息构建请求
     *
     * @param operation GraphQL操作枚举
     * @param variables 操作变量
     * @param <T>       变量类型
     * @return GraphQL响应对象
     */
    @TraceLog(logPrefixSub = "prime GraphQL请求")
    public <T> BasePrimeResponse graphqlReqeust(PrimeGraphqlOperation operation, T variables) {
        LOGGER.info("执行Prime GraphQL请求，操作: {}, 变量: {}", operation.getOperationName(), JSON.toJSONString(variables));

        // 构建GraphQL请求
        GraphqlRequest request = GraphqlRequest.builder()
                .query(operation.getQuery())
                .operationName(operation.getOperationName())
                .variables(variables)
                .build();
        LOGGER.info("构建GraphQL请求完成");

        String bwpToken = getBwpToken();
        primeConfig.setBwpToken(bwpToken);
        GraphqlConfig graphqlConfig = primeConfig.buildGraphqlConfig();

        // 执行请求并获取响应
        GraphqlResponse graphqlResponse = GraphqlUtils.execute(request, graphqlConfig);
        BasePrimeResponse response = new BasePrimeResponse(graphqlResponse.getHttpCode(), graphqlResponse.getResponseBody());

        this.saveCallRecord(request, response);
        if (response.ok()) {
            LOGGER.info("请求成功，HTTP状态码: {}", response.getHttpCode());
        } else {
            FeiShuMessageUtil.storeGeneralMessage(String.format("traceId:%s prime graphql 请求失败 错误原因:%s,", TraceLogContext.getTraceId(), response.errorMessage()), FeiShuGroupRobot.InternalWarning);
            LOGGER.error("请求返回错误，HTTP状态码: {}, 错误信息: {}", response.getHttpCode(), response.errorMessage());
            throw new InstaException(PrimeErrorCode.GRAPHQL_REQUEST_FAILED,response.errorMessage());
        }
        return response;

    }

    /**
     * 保存调用记录
     *
     * @param request  GraphQL请求对象
     * @param response GraphQL响应对象
     */
    private void saveCallRecord(GraphqlRequest request, BasePrimeResponse response) {
        String traceId = TraceLogContext.getTraceId();
        LOGGER.debug("开始保存GraphQL调用记录, 操作: {}, 追踪ID: {}", request.getOperationName(), traceId);

        try {
            StoreSdkCallRecord storeSdkCallRecord = new StoreSdkCallRecord();
            storeSdkCallRecord.setBusinessType(StoreSdkCallBusinessType.PRIME_API.getType());
            storeSdkCallRecord.setRequestJson(JSON.toJSONString(request));
            storeSdkCallRecord.setResponseJson(response.getResponseBody());
            storeSdkCallRecord.setOrderNumber("");
            storeSdkCallRecord.setTraceId(traceId);
            storeSdkCallRecord.setCreateTime(LocalDateTime.now());
            storeSdkCallRecord.setModifyTime(LocalDateTime.now());
            storeSdkCallRecord.setApiType(StoreSdkCallApiType.PRIME_GRAPHQL.getType());
            storeSdkCallRecord.setSubKey("");

            boolean saved = storeSdkCallRecordService.save(storeSdkCallRecord);
            LOGGER.info("GraphQL调用记录保存{}, 操作: {}, 追踪ID: {}",
                    saved ? "成功" : "失败", request.getOperationName(), traceId);
        } catch (Exception e) {
            LOGGER.error("保存GraphQL调用记录时发生异常, 操作: {}, 追踪ID: {}",
                    request.getOperationName(), traceId, e);
            // TODO 飞书通知
            // 此处不抛出异常，避免影响主业务流程
        }
    }

}
