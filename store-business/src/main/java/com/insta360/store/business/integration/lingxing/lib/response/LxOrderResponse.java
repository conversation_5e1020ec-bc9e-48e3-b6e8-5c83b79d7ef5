package com.insta360.store.business.integration.lingxing.lib.response;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/1
 */
public class LxOrderResponse implements LxResponse {

    @JSONField(name = "total")
    private Integer total;

    @JSONField(name = "code")
    private Integer code;

    @JSONField(name = "data")
    private List<DataDTO> data;

    @JSONField(name = "error_details")
    private List<?> errorDetails;

    @JSONField(name = "response_time")
    private String responseTime;

    @JSONField(name = "message")
    private String message;

    @JSONField(name = "request_id")
    private String requestId;

    @Override
    public Boolean isSuccess() {
        return code == 0;
    }

    public static class DataDTO {

        @JSONField(name = "shipment_date_local")
        private String shipmentDateLocal;

        @JSONField(name = "purchase_date_local_utc")
        private String purchaseDateLocalUtc;

        @JSONField(name = "last_update_date_utc")
        private String lastUpdateDateUtc;

        @JSONField(name = "amazon_order_id")
        private String amazonOrderId;

        @JSONField(name = "hide_time")
        private String hideTime;

        @JSONField(name = "gmt_modified_utc")
        private String gmtModifiedUtc;

        @JSONField(name = "buyer_name")
        private String buyerName;

        @JSONField(name = "is_assessed")
        private Integer isAssessed;

        @JSONField(name = "shipment_date")
        private String shipmentDate;

        @JSONField(name = "earliest_ship_date_utc")
        private String earliestShipDateUtc;

        @JSONField(name = "gmt_modified")
        private String gmtModified;

        @JSONField(name = "sid")
        private String sid;

        @JSONField(name = "order_status")
        private String orderStatus;

        @JSONField(name = "shipment_date_utc")
        private String shipmentDateUtc;

        @JSONField(name = "is_return")
        private String isReturn;

        @JSONField(name = "seller_name")
        private String sellerName;

        @JSONField(name = "fulfillment_channel")
        private String fulfillmentChannel;

        @JSONField(name = "item_list")
        private List<ItemListDTO> itemList;

        @JSONField(name = "purchase_date_local")
        private String purchaseDateLocal;

        @JSONField(name = "tracking_number")
        private String trackingNumber;

        @JSONField(name = "refund_amount")
        private String refundAmount;

        @JSONField(name = "earliest_ship_date")
        private String earliestShipDate;

        @JSONField(name = "last_update_date")
        private String lastUpdateDate;

        @JSONField(name = "order_total_amount")
        private String orderTotalAmount;

        @JSONField(name = "buyer_email")
        private String buyerEmail;

        @JSONField(name = "address")
        private String address;

        @JSONField(name = "is_replacement_order")
        private Integer isReplacementOrder;

        @JSONField(name = "posted_date_utc")
        private String postedDateUtc;

        @JSONField(name = "sales_channel")
        private String salesChannel;

        @JSONField(name = "purchase_date_utc")
        private String purchaseDateUtc;

        @JSONField(name = "is_mcf_order")
        private String isMcfOrder;

        @JSONField(name = "is_return_order")
        private Integer isReturnOrder;

        @JSONField(name = "is_replaced_order")
        private Integer isReplacedOrder;

        @JSONField(name = "phone")
        private String phone;

        @JSONField(name = "order_total_currency_code")
        private String orderTotalCurrencyCode;

        @JSONField(name = "name")
        private String name;

        /**
         * 购买日期
         */
        @JSONField(name = "purchase_date")
        private String purchaseDate;

        @JSONField(name = "postal_code")
        private String postalCode;

        @JSONField(name = "posted_date")
        private String postedDate;

        public String getShipmentDateLocal() {
            return shipmentDateLocal;
        }

        public void setShipmentDateLocal(String shipmentDateLocal) {
            this.shipmentDateLocal = shipmentDateLocal;
        }

        public String getPurchaseDateLocalUtc() {
            return purchaseDateLocalUtc;
        }

        public void setPurchaseDateLocalUtc(String purchaseDateLocalUtc) {
            this.purchaseDateLocalUtc = purchaseDateLocalUtc;
        }

        public String getLastUpdateDateUtc() {
            return lastUpdateDateUtc;
        }

        public void setLastUpdateDateUtc(String lastUpdateDateUtc) {
            this.lastUpdateDateUtc = lastUpdateDateUtc;
        }

        public String getAmazonOrderId() {
            return amazonOrderId;
        }

        public void setAmazonOrderId(String amazonOrderId) {
            this.amazonOrderId = amazonOrderId;
        }

        public String getHideTime() {
            return hideTime;
        }

        public void setHideTime(String hideTime) {
            this.hideTime = hideTime;
        }

        public String getGmtModifiedUtc() {
            return gmtModifiedUtc;
        }

        public void setGmtModifiedUtc(String gmtModifiedUtc) {
            this.gmtModifiedUtc = gmtModifiedUtc;
        }

        public String getBuyerName() {
            return buyerName;
        }

        public void setBuyerName(String buyerName) {
            this.buyerName = buyerName;
        }

        public Integer getIsAssessed() {
            return isAssessed;
        }

        public void setIsAssessed(Integer isAssessed) {
            this.isAssessed = isAssessed;
        }

        public String getShipmentDate() {
            return shipmentDate;
        }

        public void setShipmentDate(String shipmentDate) {
            this.shipmentDate = shipmentDate;
        }

        public String getEarliestShipDateUtc() {
            return earliestShipDateUtc;
        }

        public void setEarliestShipDateUtc(String earliestShipDateUtc) {
            this.earliestShipDateUtc = earliestShipDateUtc;
        }

        public String getGmtModified() {
            return gmtModified;
        }

        public void setGmtModified(String gmtModified) {
            this.gmtModified = gmtModified;
        }

        public String getSid() {
            return sid;
        }

        public void setSid(String sid) {
            this.sid = sid;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getShipmentDateUtc() {
            return shipmentDateUtc;
        }

        public void setShipmentDateUtc(String shipmentDateUtc) {
            this.shipmentDateUtc = shipmentDateUtc;
        }

        public String getIsReturn() {
            return isReturn;
        }

        public void setIsReturn(String isReturn) {
            this.isReturn = isReturn;
        }

        public String getSellerName() {
            return sellerName;
        }

        public void setSellerName(String sellerName) {
            this.sellerName = sellerName;
        }

        public String getFulfillmentChannel() {
            return fulfillmentChannel;
        }

        public void setFulfillmentChannel(String fulfillmentChannel) {
            this.fulfillmentChannel = fulfillmentChannel;
        }

        public List<ItemListDTO> getItemList() {
            return itemList;
        }

        public void setItemList(List<ItemListDTO> itemList) {
            this.itemList = itemList;
        }

        public String getPurchaseDateLocal() {
            return purchaseDateLocal;
        }

        public void setPurchaseDateLocal(String purchaseDateLocal) {
            this.purchaseDateLocal = purchaseDateLocal;
        }

        public String getTrackingNumber() {
            return trackingNumber;
        }

        public void setTrackingNumber(String trackingNumber) {
            this.trackingNumber = trackingNumber;
        }

        public String getRefundAmount() {
            return refundAmount;
        }

        public void setRefundAmount(String refundAmount) {
            this.refundAmount = refundAmount;
        }

        public String getEarliestShipDate() {
            return earliestShipDate;
        }

        public void setEarliestShipDate(String earliestShipDate) {
            this.earliestShipDate = earliestShipDate;
        }

        public String getLastUpdateDate() {
            return lastUpdateDate;
        }

        public void setLastUpdateDate(String lastUpdateDate) {
            this.lastUpdateDate = lastUpdateDate;
        }

        public String getOrderTotalAmount() {
            return orderTotalAmount;
        }

        public void setOrderTotalAmount(String orderTotalAmount) {
            this.orderTotalAmount = orderTotalAmount;
        }

        public String getBuyerEmail() {
            return buyerEmail;
        }

        public void setBuyerEmail(String buyerEmail) {
            this.buyerEmail = buyerEmail;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public Integer getIsReplacementOrder() {
            return isReplacementOrder;
        }

        public void setIsReplacementOrder(Integer isReplacementOrder) {
            this.isReplacementOrder = isReplacementOrder;
        }

        public String getPostedDateUtc() {
            return postedDateUtc;
        }

        public void setPostedDateUtc(String postedDateUtc) {
            this.postedDateUtc = postedDateUtc;
        }

        public String getSalesChannel() {
            return salesChannel;
        }

        public void setSalesChannel(String salesChannel) {
            this.salesChannel = salesChannel;
        }

        public String getPurchaseDateUtc() {
            return purchaseDateUtc;
        }

        public void setPurchaseDateUtc(String purchaseDateUtc) {
            this.purchaseDateUtc = purchaseDateUtc;
        }

        public String getIsMcfOrder() {
            return isMcfOrder;
        }

        public void setIsMcfOrder(String isMcfOrder) {
            this.isMcfOrder = isMcfOrder;
        }

        public Integer getIsReturnOrder() {
            return isReturnOrder;
        }

        public void setIsReturnOrder(Integer isReturnOrder) {
            this.isReturnOrder = isReturnOrder;
        }

        public Integer getIsReplacedOrder() {
            return isReplacedOrder;
        }

        public void setIsReplacedOrder(Integer isReplacedOrder) {
            this.isReplacedOrder = isReplacedOrder;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getOrderTotalCurrencyCode() {
            return orderTotalCurrencyCode;
        }

        public void setOrderTotalCurrencyCode(String orderTotalCurrencyCode) {
            this.orderTotalCurrencyCode = orderTotalCurrencyCode;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPurchaseDate() {
            return purchaseDate;
        }

        public void setPurchaseDate(String purchaseDate) {
            this.purchaseDate = purchaseDate;
        }

        public String getPostalCode() {
            return postalCode;
        }

        public void setPostalCode(String postalCode) {
            this.postalCode = postalCode;
        }

        public String getPostedDate() {
            return postedDate;
        }

        public void setPostedDate(String postedDate) {
            this.postedDate = postedDate;
        }

        @Override
        public String toString() {
            return "DataDTO{" +
                    "shipmentDateLocal='" + shipmentDateLocal + '\'' +
                    ", purchaseDateLocalUtc='" + purchaseDateLocalUtc + '\'' +
                    ", lastUpdateDateUtc='" + lastUpdateDateUtc + '\'' +
                    ", amazonOrderId='" + amazonOrderId + '\'' +
                    ", hideTime='" + hideTime + '\'' +
                    ", gmtModifiedUtc='" + gmtModifiedUtc + '\'' +
                    ", buyerName='" + buyerName + '\'' +
                    ", isAssessed=" + isAssessed +
                    ", shipmentDate='" + shipmentDate + '\'' +
                    ", earliestShipDateUtc='" + earliestShipDateUtc + '\'' +
                    ", gmtModified='" + gmtModified + '\'' +
                    ", sid='" + sid + '\'' +
                    ", orderStatus='" + orderStatus + '\'' +
                    ", shipmentDateUtc='" + shipmentDateUtc + '\'' +
                    ", isReturn='" + isReturn + '\'' +
                    ", sellerName='" + sellerName + '\'' +
                    ", fulfillmentChannel='" + fulfillmentChannel + '\'' +
                    ", itemList=" + itemList +
                    ", purchaseDateLocal='" + purchaseDateLocal + '\'' +
                    ", trackingNumber='" + trackingNumber + '\'' +
                    ", refundAmount=" + refundAmount +
                    ", earliestShipDate='" + earliestShipDate + '\'' +
                    ", lastUpdateDate='" + lastUpdateDate + '\'' +
                    ", orderTotalAmount='" + orderTotalAmount + '\'' +
                    ", buyerEmail='" + buyerEmail + '\'' +
                    ", address='" + address + '\'' +
                    ", isReplacementOrder=" + isReplacementOrder +
                    ", postedDateUtc='" + postedDateUtc + '\'' +
                    ", salesChannel='" + salesChannel + '\'' +
                    ", purchaseDateUtc='" + purchaseDateUtc + '\'' +
                    ", isMcfOrder='" + isMcfOrder + '\'' +
                    ", isReturnOrder=" + isReturnOrder +
                    ", isReplacedOrder=" + isReplacedOrder +
                    ", phone='" + phone + '\'' +
                    ", orderTotalCurrencyCode='" + orderTotalCurrencyCode + '\'' +
                    ", name='" + name + '\'' +
                    ", purchaseDate='" + purchaseDate + '\'' +
                    ", postalCode='" + postalCode + '\'' +
                    ", postedDate='" + postedDate + '\'' +
                    '}';
        }

        public static class ItemListDTO {

            @JSONField(name = "quantity_ordered")
            private String quantityOrdered;

            @JSONField(name = "seller_sku")
            private String sellerSku;

            @JSONField(name = "asin")
            private String asin;

            public String getQuantityOrdered() {
                return quantityOrdered;
            }

            public void setQuantityOrdered(String quantityOrdered) {
                this.quantityOrdered = quantityOrdered;
            }

            public String getSellerSku() {
                return sellerSku;
            }

            public void setSellerSku(String sellerSku) {
                this.sellerSku = sellerSku;
            }

            public String getAsin() {
                return asin;
            }

            public void setAsin(String asin) {
                this.asin = asin;
            }

            @Override
            public String toString() {
                return "ItemListDTO{" +
                        "quantityOrdered='" + quantityOrdered + '\'' +
                        ", sellerSku='" + sellerSku + '\'' +
                        ", asin='" + asin + '\'' +
                        '}';
            }
        }
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public List<DataDTO> getData() {
        return data;
    }

    public void setData(List<DataDTO> data) {
        this.data = data;
    }

    public List<?> getErrorDetails() {
        return errorDetails;
    }

    public void setErrorDetails(List<?> errorDetails) {
        this.errorDetails = errorDetails;
    }

    public String getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(String responseTime) {
        this.responseTime = responseTime;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public String toString() {
        return "LxOrderResponse{" +
                "total=" + total +
                ", code=" + code +
                ", data=" + data +
                ", errorDetails=" + errorDetails +
                ", responseTime='" + responseTime + '\'' +
                ", message='" + message + '\'' +
                ", requestId='" + requestId + '\'' +
                '}';
    }
}
