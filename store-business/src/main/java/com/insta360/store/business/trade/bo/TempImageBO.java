package com.insta360.store.business.trade.bo;

import java.awt.*;
import java.awt.image.BufferedImage;

/**
 * 临时图像Bo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/24
 */
public class TempImageBO {

    /**
     * 临时图像
     */
    BufferedImage tempImage;

    /**
     * url
     */
    String url;

    /**
     * 图形处理
     */
    Graphics2D graphics;

    public void dis(){
        graphics.dispose();
    }

    public TempImageBO() {
    }

    public TempImageBO(BufferedImage tempImage, String url, Graphics2D graphics) {
        this.tempImage = tempImage;
        this.url = url;
        this.graphics = graphics;
    }

    public BufferedImage getTempImage() {
        return tempImage;
    }

    public void setTempImage(BufferedImage tempImage) {
        this.tempImage = tempImage;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Graphics2D getGraphics() {
        return graphics;
    }

    public void setGraphics(Graphics2D graphics) {
        this.graphics = graphics;
    }
}
