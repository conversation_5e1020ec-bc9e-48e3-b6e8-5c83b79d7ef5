package com.insta360.store.business.discount.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.discount.model.GiftCardPolicy;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-03-22
 * @Description:
 */
public interface GiftCardPolicyService extends BaseService<GiftCardPolicy> {

    /**
     * 根据政策ID集合查询对应代金券政策
     * @param policyIdList
     * @return
     */
    List<GiftCardPolicy> listByPolicyId(List<Integer> policyIdList);

    /**
     * 根据代金券ID查询对应优惠政策
     * @param giftCardCode
     * @return
     */
    List<GiftCardPolicy> listByGiftCardCode(String giftCardCode);

    /**
     * 根据代金券ID删除对应优惠政策
     * @param giftCardCode
     */
    void deleteByGiftCardCode(String giftCardCode);

    /**
     * 根据代金券code集合查询对应优惠政策
     * @param giftCardCodes
     * @return
     */
    List<GiftCardPolicy> listByGiftCardCodes(List<String> giftCardCodes);
}
