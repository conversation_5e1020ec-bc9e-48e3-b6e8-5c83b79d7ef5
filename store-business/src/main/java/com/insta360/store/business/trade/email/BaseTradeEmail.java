package com.insta360.store.business.trade.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.gateway.GatewayConfiguration;
import com.insta360.store.business.meta.email.BaseStoreEmail;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.trade.bo.UserCartCommodityInfoBO;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/07/21
 * @Description:
 */
public abstract class BaseTradeEmail extends BaseStoreEmail {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseTradeEmail.class);

    /**
     * 需要前缀的国家地区
     */
    private static final List<InstaCountry> SUFFIX_COUNTRY_LIST = Arrays.asList(InstaCountry.DE, InstaCountry.ES, InstaCountry.PT, InstaCountry.FI, InstaCountry.FR, InstaCountry.IT, InstaCountry.LU, InstaCountry.MC, InstaCountry.SE, InstaCountry.NO, InstaCountry.JP);

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductService productService;

    @Autowired
    GatewayConfiguration gatewayConfiguration;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 当前语言
     */
    private InstaCountry country;

    /**
     * 币种
     */
    private Currency currency;

    /**
     * 当前语言
     */
    private InstaLanguage language;

    /**
     * 邮件类型
     */
    private UserCartEmailEnum userCartEmailEnum;

    /**
     * 购物车数据
     */
    private List<UserCartCommodityInfoBO> userCartCommodityInfoBos;

    public UserCartEmailEnum getUserCartEmailEnum() {
        return userCartEmailEnum;
    }

    public void setUserCartEmailEnum(UserCartEmailEnum userCartEmailEnum) {
        this.userCartEmailEnum = userCartEmailEnum;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public void setCountry(InstaCountry country) {
        this.country = country;
    }

    public List<UserCartCommodityInfoBO> getUserCartCommodityInfoBos() {
        return userCartCommodityInfoBos;
    }

    public void setUserCartCommodityInfoBos(List<UserCartCommodityInfoBO> userCartCommodityInfoBos) {
        this.userCartCommodityInfoBos = userCartCommodityInfoBos;
    }

    public Currency getCurrency() {
        return currency;
    }

    public void setCurrency(Currency currency) {
        this.currency = currency;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    protected Integer getSleepTime() {
        return SLEEP_TIME;
    }

    @Override
    protected InstaLanguage getLanguage() {
        return language;
    }

    public void setLanguage(InstaLanguage language) {
        this.language = language;
    }

    @Override
    protected Long getDelayTimeConfig() {
        return DEFAULT_DELAY_TIME;
    }

    /**
     * 解析封装购物车信息数据
     *
     * @return
     */
    protected List<UserCartCommodityInfoBO> packCartCommodityInfo() {
        return userCartCommodityInfoBos;
    }

    /**
     * 购物车信息的币种
     *
     * @return
     */
    protected String getCartSignal() {
        return this.getSignal(country, currency);
    }

    /**
     * 获取
     *
     * @return
     */
    protected Boolean getSuffix() {
        return SUFFIX_COUNTRY_LIST.contains(country);
    }

    /**
     * 获取价格
     *
     * @param productId
     * @return
     */
    protected Float getPrice(Integer productId) {
        Product product = productService.getById(productId);
        if (Objects.isNull(product) || !product.getEnabled()) {
            return null;
        }

        List<Commodity> commodities = commodityService.getCommodities(productId);
        List<Integer> commodityIdList = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        List<CommodityPrice> commodityPrices = commodityPriceService.getPriceByCommodityIds(commodityIdList, country);
        if (CollectionUtils.isEmpty(commodityPrices)) {
            LOGGER.error(String.format("[购物车邮件发送]套餐价格不存在,产品ID:%s,国家:%s", productId, country));
            FeiShuMessageUtil.storeGeneralMessage(String.format("[购物车邮件发送]套餐价格不存在,产品ID:%s,国家:%s", productId, country), FeiShuGroupRobot.InternalWarning);
            return null;
        }

        CommodityPrice minPrice = commodityPrices.stream().min(Comparator.comparing(CommodityPrice::getAmount)).orElse(null);
        if (Objects.isNull(minPrice)) {
            LOGGER.error(String.format("[购物车邮件发送]最低价格不存在,产品ID:%s,国家:%s", productId, country));
            FeiShuMessageUtil.storeGeneralMessage(String.format("[购物车邮件发送]最低价格不存在,产品ID:%s,国家:%s", productId, country), FeiShuGroupRobot.InternalWarning);
        }
        return Objects.isNull(minPrice) ? null : minPrice.getAmount();
    }

    /**
     * 购物车链接
     *
     * @return
     */
    protected String getCartLink(String utm) {
        return String.format("%s/cart?utm_source=store&utm_medium=%s", gatewayConfiguration.getStoreUrl(), utm);
    }

    /**
     * 产品页链接
     *
     * @return
     */
    protected String getProductLink() {
        return String.format("%s/product/x4?utm_source=store&utm_medium=%s", gatewayConfiguration.getStoreUrl(), UserCartEmailEnum.A3_SECOND_EMAIL.getLinkName());
    }
}
