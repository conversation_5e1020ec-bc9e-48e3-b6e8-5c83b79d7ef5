package com.insta360.store.business.order.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/19
 */
public class OrderExtraBO implements Serializable {

    /**
     * 云存储数据存储地区
     */
    private String storageRegion;

    /**
     * 云存储地区协议版本号
     */
    private Integer agreementVersion;

    public String getStorageRegion() {
        return storageRegion;
    }

    public void setStorageRegion(String storageRegion) {
        this.storageRegion = storageRegion;
    }

    public Integer getAgreementVersion() {
        return agreementVersion;
    }

    public void setAgreementVersion(Integer agreementVersion) {
        this.agreementVersion = agreementVersion;
    }

    @Override
    public String toString() {
        return "OrderExtraBO{" +
                "storageRegion='" + storageRegion + '\'' +
                ", agreementVersion=" + agreementVersion +
                '}';
    }
}
