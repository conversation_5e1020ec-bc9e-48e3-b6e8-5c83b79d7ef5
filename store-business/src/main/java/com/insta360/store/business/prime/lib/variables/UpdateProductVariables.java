package com.insta360.store.business.prime.lib.variables;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 更新Amazon Prime产品的GraphQL变量类
 * <p>
 * 此类用于封装向Amazon Prime服务发送更新产品请求时所需的所有参数。
 * 支持更新产品的基本信息（如标题、图片、URL等）以及购买组成员信息。
 * 作为GraphQL请求变量传递给Prime API进行产品更新操作。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/9
 */
public class UpdateProductVariables implements PrimeVariables {

    /**
     * 产品标识符
     * 用于唯一标识待更新的产品
     */
    private Identifier identifier;

    /**
     * 更新产品的输入参数
     * 包含所有需要更新的产品属性
     */
    private Input input;

    /**
     * 获取产品标识符
     *
     * @return 产品标识符对象
     */
    public Identifier getIdentifier() {
        return identifier;
    }

    /**
     * 设置产品标识符
     *
     * @param identifier 产品标识符对象
     */
    public void setIdentifier(Identifier identifier) {
        this.identifier = identifier;
    }

    /**
     * 获取更新产品的输入参数
     *
     * @return 输入参数对象
     */
    public Input getInput() {
        return input;
    }

    /**
     * 设置更新产品的输入参数
     *
     * @param input 输入参数对象
     */
    public void setInput(Input input) {
        this.input = input;
    }

    /**
     * 产品标识符类
     * <p>
     * 用于在Amazon Prime系统中唯一标识待更新的产品
     * 使用SKU作为主要标识字段
     * </p>
     */
    public static class Identifier {

        /**
         * 产品SKU
         * 在Prime系统中唯一标识产品的代码
         */
        private String externalId;

        /**
         * 默认构造函数
         */
        public Identifier() {
        }

        /**
         * 通过SKU创建标识符
         *
         * @param externalId 产品SKU
         */
        public Identifier(String externalId) {
            this.externalId = externalId;
        }

        /**
         * 获取产品SKU
         *
         * @return SKU字符串
         */
        public String getExternalId() {
            return externalId;
        }

        /**
         * 设置产品SKU
         *
         * @param externalId SKU字符串
         */
        public void setExternalId(String externalId) {
            this.externalId = externalId;
        }
    }

    /**
     * 更新产品输入参数类
     * <p>
     * 包含更新Amazon Prime产品时所需的所有字段信息
     * 支持更新产品基本信息和购买组成员信息
     * </p>
     */
    public static class Input {

        /**
         * 购买组成员关系
         * 用于组合产品（Bundle）的成员管理
         */
        private List<PurchaseGroupMemberships> purchaseGroupMemberships;

        /**
         * Amazon SKU
         * 用于在Amazon系统中唯一标识产品
         */
        private AmazonSku amazonSku;

        /**
         * 是否提供Prime服务
         * 指示该产品是否支持Amazon Prime服务
         */
        private Boolean offerPrime;

        /**
         * 产品详情页URL
         * 产品在商城中的详情页链接
         */
        private String productDetailPageUrl;

        /**
         * 产品图片
         * 用于在Amazon平台展示的产品图片
         */
        private Image image;

        /**
         * 默认构造函数
         */
        public Input() {
        }

        /**
         * 全参数构造函数
         * 创建完整的产品更新输入参数，包括图片信息
         *
         * @param externalId           外部系统产品ID
         * @param amazonSku            Amazon SKU
         * @param offerPrime           是否提供Prime服务
         * @param productDetailPageUrl 产品详情页URL
         * @param image                产品图片URL
         */
        public Input(String amazonSku, Boolean offerPrime, String productDetailPageUrl, String image) {
            this.offerPrime = offerPrime;
            this.productDetailPageUrl = productDetailPageUrl;
            if (StringUtils.isNotBlank(amazonSku)){
                this.amazonSku = new AmazonSku(amazonSku);
            }
            if (StringUtils.isNotBlank(image)){
                this.image = new Image(image);
            }
        }

        /**
         * 添加购买组成员
         * 用于Bundle类型产品添加包含的子商品
         *
         * @param value 子商品ID/数量
         * @param unit  单位（通常为"UNIT"）
         */
        public void addMember(String externalId, Integer value, String unit) {
            if (CollectionUtils.isEmpty(purchaseGroupMemberships)) {
                purchaseGroupMemberships = new ArrayList<>();
            }
            purchaseGroupMemberships.add(new PurchaseGroupMemberships(externalId, value, unit));
        }

        public AmazonSku getAmazonSku() {
            return amazonSku;
        }

        public void setAmazonSku(AmazonSku amazonSku) {
            this.amazonSku = amazonSku;
        }

        public boolean isOfferPrime() {
            return offerPrime;
        }

        public String getProductDetailPageUrl() {
            return productDetailPageUrl;
        }

        public void setProductDetailPageUrl(String productDetailPageUrl) {
            this.productDetailPageUrl = productDetailPageUrl;
        }

        public List<PurchaseGroupMemberships> getPurchaseGroupMemberships() {
            return purchaseGroupMemberships;
        }

        public void setPurchaseGroupMemberships(List<PurchaseGroupMemberships> purchaseGroupMemberships) {
            this.purchaseGroupMemberships = purchaseGroupMemberships;
        }

        public Boolean getOfferPrime() {
            return offerPrime;
        }

        public void setOfferPrime(boolean offerPrime) {
            this.offerPrime = offerPrime;
        }

        public void setOfferPrime(Boolean offerPrime) {
            this.offerPrime = offerPrime;
        }

        public Image getImage() {
            return image;
        }

        public void setImage(Image image) {
            this.image = image;
        }

    }

    /**
     * 购买组成员关系类
     * <p>
     * 用于管理组合产品（Bundle）中包含的子商品信息
     * 包括代表性产品ID和成员列表
     * </p>
     */
    public static class PurchaseGroupMemberships {

        /**
         * 成员列表
         * 组合产品中包含的所有子商品信息
         */
        private Member memberAmount;

        /**
         * 代表性产品ID
         * 组合产品的主要产品ID
         */
        private Product representativeProductId;

        /**
         * 默认构造函数
         */
        public PurchaseGroupMemberships() {
        }

        public PurchaseGroupMemberships(String externalId, Integer value, String unit) {
            this.memberAmount = new Member(value, unit);
            this.representativeProductId = new Product(externalId);
        }

        public Member getMemberAmount() {
            return memberAmount;
        }

        public void setMemberAmount(Member memberAmount) {
            this.memberAmount = memberAmount;
        }

        public Product getRepresentativeProductId() {
            return representativeProductId;
        }

        public void setRepresentativeProductId(Product representativeProductId) {
            this.representativeProductId = representativeProductId;
        }

        /**
         * 购买组成员类
         * <p>
         * 表示组合产品中的单个子商品
         * 包含数量和单位信息
         * </p>
         */
        public static class Member {

            /**
             * 数量值
             * 子商品的数量或ID
             */
            private Integer value;

            /**
             * 单位
             * 数量的单位，通常为"UNIT"
             */
            private String unit;

            /**
             * 默认构造函数
             */
            public Member() {
            }

            /**
             * 全参数构造函数
             *
             * @param value 数量值
             * @param unit  单位
             */
            public Member(Integer value, String unit) {
                this.value = value;
                this.unit = unit;
            }

            public String getUnit() {
                return unit;
            }

            public void setUnit(String unit) {
                this.unit = unit;
            }

            public Integer getValue() {
                return value;
            }

            public void setValue(Integer value) {
                this.value = value;
            }

        }

        /**
         * 产品标识类
         * <p>
         * 用于标识购买组中的产品
         * 使用外部ID作为主要标识符
         * </p>
         */
        public static class Product {

            /**
             * 外部系统产品ID
             * 用于在外部系统中标识产品
             */
            private String externalId;

            /**
             * 默认构造函数
             */
            public Product() {
            }

            /**
             * 通过外部ID创建产品标识
             *
             * @param externalId 外部系统产品ID
             */
            public Product(String externalId) {
                this.externalId = externalId;
            }

            public String getExternalId() {
                return externalId;
            }

            public void setExternalId(String externalId) {
                this.externalId = externalId;
            }
        }
    }

    /**
     * Amazon SKU类
     * <p>
     * 用于在Amazon系统中唯一标识产品的SKU
     * 包含单个value字段表示SKU值
     * </p>
     */
    public static class AmazonSku {

        /**
         * SKU值
         * Amazon系统中的SKU标识符
         */
        private String value;

        /**
         * 默认构造函数
         */
        public AmazonSku() {
        }

        /**
         * 通过SKU值创建Amazon SKU
         *
         * @param value SKU值
         */
        public AmazonSku(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    /**
     * 产品图片类
     * <p>
     * 用于存储产品在Amazon平台展示的图片URL
     * 包含图片源URL信息
     * </p>
     */
    public static class Image {

        /**
         * 图片源URL
         * 指向产品图片资源的完整URL
         */
        private String sourceUrl;

        /**
         * 默认构造函数
         */
        public Image() {
        }

        /**
         * 通过源URL创建图片对象
         *
         * @param sourceUrl 图片源URL
         */
        public Image(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }

        public String getSourceUrl() {
            return sourceUrl;
        }

        public void setSourceUrl(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }
    }

}
