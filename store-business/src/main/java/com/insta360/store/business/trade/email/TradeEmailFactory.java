package com.insta360.store.business.trade.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.utils.SpringContextLocator;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.trade.bo.UserCartCommodityInfoBO;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/07/21
 * @Description:
 */
@Component
public class TradeEmailFactory {

    /**
     * 获取购物车套餐挽回邮件
     *
     * @param uuid
     * @param language
     * @param country
     * @param currency
     * @param userCartEmailEnum
     * @param userCartCommodityInfoBos
     * @param tradeEmailClz
     * @return
     */
    public BaseTradeEmail getEmail(String uuid,
                                   InstaLanguage language,
                                   InstaCountry country,
                                   Currency currency,
                                   UserCartEmailEnum userCartEmailEnum,
                                   List<UserCartCommodityInfoBO> userCartCommodityInfoBos,
                                   Class<? extends BaseTradeEmail> tradeEmailClz) {
        BaseTradeEmail tradeEmail = SpringContextLocator.getBean(tradeEmailClz);
        tradeEmail.setLanguage(language);
        tradeEmail.setUuid(uuid);
        tradeEmail.setUserCartEmailEnum(userCartEmailEnum);
        tradeEmail.setCountry(country);
        tradeEmail.setCurrency(currency);
        tradeEmail.setUserCartCommodityInfoBos(userCartCommodityInfoBos);
        return tradeEmail;
    }
}
