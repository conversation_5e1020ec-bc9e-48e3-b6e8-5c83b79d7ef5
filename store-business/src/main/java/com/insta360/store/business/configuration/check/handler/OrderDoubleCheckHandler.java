package com.insta360.store.business.configuration.check.handler;

import com.insta360.store.business.configuration.check.bo.CheckResultBO;
import com.insta360.store.business.configuration.check.chain.BaseOrderCheckChain;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/12
 */
@Scope("prototype")
@Component
public class OrderDoubleCheckHandler extends BaseOrderDoubleCheckHandler {

    /**
     * 订单检查链条集合
     */
    @Autowired
    List<BaseOrderCheckChain> orderCheckChainList;

    @Override
    protected List<CheckResultBO> doDoubleCheck(DoubleCheckBO doubleCheckBO) {
        return orderCheckChainList.stream().map(baseOrderCheckChain -> {
            String name = baseOrderCheckChain.getName();
            Boolean checkPass = baseOrderCheckChain.doCheck(doubleCheckBO);
            Boolean sendMessage = baseOrderCheckChain.sendMessage();
            return new CheckResultBO(name, checkPass, sendMessage);
        }).collect(Collectors.toList());
    }
}
