package com.insta360.store.business.trade.service.impl.helper;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.order.constants.OrderCommonConstant;
import org.springframework.stereotype.Component;

/**
 * @<PERSON>
 * @Description
 * @Date 2025/4/29 上午11:58
 */
@Component
public class OrderShippingFeeTaxCounter {

    /**
     * 运费税费计算
     *
     * @param shippingFee
     * @return
     */
    public Price count(Price shippingFee, InstaCountry country) {
        // 非巴西地区置为0
        if (!InstaCountry.BR.equals(country)) {
            return new Price(shippingFee.getCurrency(), 0f);
        }
        return shippingFee.multiply(OrderCommonConstant.BR_FREIGHT_TAX_RATE);
    }
}
