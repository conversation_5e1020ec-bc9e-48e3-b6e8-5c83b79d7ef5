package com.insta360.store.business.integration.wto.oms.service.handler.admin;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.integration.wto.oms.lib.requrest.BaseOmsRequest;
import com.insta360.store.business.integration.wto.oms.lib.response.BaseOmsResponse;
import com.insta360.store.business.integration.wto.oms.service.handler.BaseOmsService;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;

/**
 * 基础商城管理服务
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
public abstract class BaseStoreAdminService extends BaseOmsService {

    @Override
    protected Boolean initializable() {
        return true;
    }

    /**
     * 请求数据记录
     * @param baseOmsRequest
     * @param baseOmsResponse
     */
    protected void requestContextHandler(BaseOmsRequest baseOmsRequest, BaseOmsResponse baseOmsResponse) {
        LOGGER.info("[OMS系统]官方商城订单信息同步失败, method: {}, 请求数据: {}, 响应数据: {}", baseOmsRequest.getMethod(), JSON.toJSONString(baseOmsRequest), JSON.toJSONString(baseOmsResponse));
        if (baseOmsResponse.isFail()) {
            String message = String.format("请求[%s] 错误:[%s]\n request:%s\n response:%s\n", baseOmsRequest.getMethod(), baseOmsResponse.getMessage(), JSON.toJSONString(baseOmsRequest), JSON.toJSONString(baseOmsResponse));
            LOGGER.error(message);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning);
        } else {
            LOGGER.info("请求[{}],响应[{}],响应状态[{}]", baseOmsRequest.getMethod(), baseOmsResponse.getMessage(), baseOmsResponse.getSuccess());
        }
    }

}
