package com.insta360.store.business.reseller.email;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: hyc
 * @Date: 2019/4/11
 * @Description:
 */
@Scope("prototype")
@Component
public class ResellerApplyFailEmail extends BaseResellerEmail {

    @Override
    public String getTemplateName() {
        return "mall_reseller_apply_fail";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
    }

    @Override
    protected InstaLanguage getLanguage() {
        return resellerApply.language();
    }
}
