package com.insta360.store.business.discount.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 代金券生效类型
 * @Date 2022/9/26
 */
public enum GiftCardEffectType {
    UNKNOWN(0,"未知"),
    FIXED_EFFECTIVE_DAYS(1,"固定生效天数"),
    FLEXIBLE_TIME_PERIOD(2,"灵活时间段");

    private final int type;

    private final String desc;

    GiftCardEffectType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static GiftCardEffectType matchType(Integer type) {
        if(Objects.isNull(type)) {
            return UNKNOWN;
        }
        for (GiftCardEffectType giftCardEffectType : GiftCardEffectType.values()) {
            if(giftCardEffectType.type == type.intValue()) {
                return giftCardEffectType;
            }
        }
        return UNKNOWN;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
