package com.insta360.store.business.payment.service.impl.channel;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.bo.PaymentChannelBO;
import com.insta360.store.business.payment.enums.OceanCreditCardType;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.trade.exception.TradeErrorCode;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2023/09/06
 * @Description:
 */
@Component
public class OceanPaymentChannelHandler extends BasePaymentChannelHandler {

    /**
     * 钱海支付机构
     *
     * @param paymentChannelParam
     * @return
     */
    @Override
    public PaymentChannel getPaymentChannel(PaymentChannelBO paymentChannelParam) {
        Order order = paymentChannelParam.getOrder();
        InstaCountry country = order.country();
        // 根据不同支付业务类型选择支付渠道
        switch (paymentChannelParam.getPaymentBusinessType()) {
            case NORMAL_PAY:
                return getNormalPayChannel(order, paymentChannelParam.getCardName(), country);
            case SUBSCRIBE_PAY:
                return getSubscribePayChannel(paymentChannelParam.getCardName(), country);
            default:
                throw new InstaException(PaymentErrorCode.InvalidPaymentMethodException);
        }
    }

    /**
     * 获取普通支付渠道
     *
     * @param cardName
     * @param country
     * @return
     */
    private PaymentChannel getNormalPayChannel(Order order, String cardName, InstaCountry country) {
        return order.isRepairOrder()
                ? this.repairmentOceanPayment(country, cardName)
                : this.storeOceanPayment(country, cardName);
    }

    /**
     * 商城 Ocean 支付
     *
     * @param country
     * @param cardName
     * @return
     */
    private PaymentChannel storeOceanPayment(InstaCountry country, String cardName) {
        // 中国地区信用卡：credit_card_indirect
        if (InstaCountry.CN.equals(country)) {
            // 中国地区信用卡卡种仅支持Visa、MasterCard以及Maestro
            if (!OceanCreditCardType.isVMM(cardName)) {
                throw new InstaException(TradeErrorCode.InvalidCardTypeException);
            }
            return PaymentChannel.credit_card_indirect;
        }

        // 美国主体
        if (InstaCountry.US.equals(country)) {
            if (!OceanCreditCardType.isVMAJDD(cardName)) {
                throw new InstaException(TradeErrorCode.InvalidCardTypeException);
            }
            return PaymentChannel.credit_card_indirect_us;
        }

        return OceanCreditCardType.isADJDC(cardName) ? PaymentChannel.ae_discover_jcb_cup : PaymentChannel.credit_card_indirect_hk;
    }

    /**
     * 获取订阅支付渠道
     *
     * @param cardName
     * @param country
     * @return
     */
    private PaymentChannel getSubscribePayChannel(String cardName, InstaCountry country) {
        // 中国地区信用卡：credit_card_indirect
        if (InstaCountry.CN.equals(country)) {
            // 中国地区信用卡卡种仅支持Visa、MasterCard以及Maestro
            if (!OceanCreditCardType.isVMM(cardName)) {
                throw new InstaException(TradeErrorCode.InvalidCardTypeException);
            }
            return PaymentChannel.credit_card_indirect_subscribe;
        }

        // 美国主体订阅
        if (InstaCountry.US.equals(country)) {
            if (!OceanCreditCardType.isVMAJDD(cardName)) {
                throw new InstaException(TradeErrorCode.InvalidCardTypeException);
            }
            return PaymentChannel.credit_card_indirect_us_subscribe;
        }

        // ADJC（AE、Discover、Jcb、Cup）：ae_discover_jcb_cup
        // 其他卡种走海外信用卡
        return (OceanCreditCardType.isADJDC(cardName)) ? PaymentChannel.ae_discover_jcb_cup_subscribe : PaymentChannel.credit_card_indirect_hk_subscribe;
    }

    /**
     * 工单 Ocean 支付
     *
     * @param country
     * @param cardName
     * @return
     */
    private PaymentChannel repairmentOceanPayment(InstaCountry country, String cardName) {
        // 工单钱海信用卡不支持中国地区
        if (InstaCountry.CN.equals(country)) {
            throw new InstaException(PaymentErrorCode.InvalidPaymentChannelException);
        }

        // 美国和加拿大的信用卡走美国收款主体
        boolean isUsPayEntity = InstaCountry.US.equals(country) || InstaCountry.CA.equals(country);

        // 小众卡种
        if (OceanCreditCardType.isDJDC(cardName)) {
            return isUsPayEntity ? PaymentChannel.repairment_ae_discover_jcb_cup_us : PaymentChannel.repairment_ae_discover_jcb_cup;
        }

        // 非美国AE卡种
        if (OceanCreditCardType.AE.getCardName().equals(cardName) && !InstaCountry.US.equals(country)) {
            return PaymentChannel.repairment_ae_discover_jcb_cup;
        }

        // TODO: 07/03/2024 美国通道AE卡种暂时分配到V/M系列
        return isUsPayEntity ? PaymentChannel.repairment_credit_card_indirect_us : PaymentChannel.repairment_credit_card_indirect_hk;
    }
}
