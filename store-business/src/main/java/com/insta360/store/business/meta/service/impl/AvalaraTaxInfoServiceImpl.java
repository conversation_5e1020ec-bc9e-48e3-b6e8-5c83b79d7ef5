package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.meta.dao.AvalaraTaxInfoDao;
import com.insta360.store.business.meta.model.AvalaraTaxInfo;
import com.insta360.store.business.meta.service.AvalaraTaxInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-06-29
 * @Description:
 */
@Service
public class AvalaraTaxInfoServiceImpl extends BaseServiceImpl<AvalaraTaxInfoDao, AvalaraTaxInfo> implements AvalaraTaxInfoService {

    @Override
    public AvalaraTaxInfo getOrderAvalaraTaxInfo(String businessType, String businessCode) {
        if(StringUtils.isBlank(businessType) || StringUtils.isBlank(businessCode)) {
            return null;
        }
        QueryWrapper<AvalaraTaxInfo> qw = new QueryWrapper<>();
        qw.eq("business_type",businessType);
        qw.eq("business_code",businessCode);
        return baseMapper.selectOne(qw);
    }

    @Override
    public AvalaraTaxInfo getOrderAvalaraTaxInfo(String orderNumber) {
        QueryWrapper<AvalaraTaxInfo> qw = new QueryWrapper<>();
        qw.eq("purchase_order_no", orderNumber);
        qw.orderByAsc("id");
        qw.last("limit 1");
        return baseMapper.selectOne(qw);
    }
}
