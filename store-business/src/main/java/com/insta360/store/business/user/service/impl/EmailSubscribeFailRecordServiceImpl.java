package com.insta360.store.business.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.user.dao.EmailSubscribeFailRecordDao;
import com.insta360.store.business.user.model.EmailSubscribeFailRecord;
import com.insta360.store.business.user.service.EmailSubscribeFailRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-10-10
 * @Description:
 */
@Service
public class EmailSubscribeFailRecordServiceImpl extends BaseServiceImpl<EmailSubscribeFailRecordDao, EmailSubscribeFailRecord> implements EmailSubscribeFailRecordService {

    @Override
    public List<EmailSubscribeFailRecord> getCompensateRecordByState(Integer state) {
        QueryWrapper<EmailSubscribeFailRecord> qw = new QueryWrapper<>();
        qw.eq("compensate_state",state);
        return baseMapper.selectList(qw);
    }

    @Override
    public EmailSubscribeFailRecord getCompensateRecordBySubscribeId(Integer subscribeId) {
        QueryWrapper<EmailSubscribeFailRecord> qw = new QueryWrapper<>();
        qw.eq("subscribe_id",subscribeId);
        return baseMapper.selectOne(qw);
    }
}
