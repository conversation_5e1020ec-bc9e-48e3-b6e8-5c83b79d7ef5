package com.insta360.store.business.integration.mabang.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.integration.mabang.dao.MbOrderPaymentDao;
import com.insta360.store.business.integration.mabang.model.MbOrderPayment;
import com.insta360.store.business.integration.mabang.service.MbOrderPaymentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-08-17
 * @Description:
 */
@Service
public class MbOrderPaymentServiceImpl extends BaseServiceImpl<MbOrderPaymentDao, MbOrderPayment> implements MbOrderPaymentService {

    @Override
    public MbOrderPayment getByMbOrder(Integer orderId) {
        QueryWrapper<MbOrderPayment> qw = new QueryWrapper<>();
        qw.eq("`order`", orderId);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<MbOrderPayment> listByOrderIds(List<Integer> orderIds) {
        QueryWrapper<MbOrderPayment> qw = new QueryWrapper<>();
        qw.in("`order`", orderIds);
        return baseMapper.selectList(qw);
    }
}
