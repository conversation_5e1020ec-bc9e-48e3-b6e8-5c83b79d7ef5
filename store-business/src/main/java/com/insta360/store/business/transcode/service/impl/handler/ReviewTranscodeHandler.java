package com.insta360.store.business.transcode.service.impl.handler;

import cn.hutool.core.util.StrUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.configuration.utils.UrlUtils;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.review.bo.ReviewResourceTranscodeResultBO;
import com.insta360.store.business.outgoing.mq.transcode.dto.TranscodeMessageDTO;
import com.insta360.store.business.review.enums.ReviewCompressStateEnum;
import com.insta360.store.business.review.enums.ReviewResourceTypeEnum;
import com.insta360.store.business.review.model.ReviewResource;
import com.insta360.store.business.review.service.ReviewResourceService;
import com.insta360.store.business.transcode.service.impl.BaseTranscodeHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2024-05-24 11:46
 */
@Component
public class ReviewTranscodeHandler extends BaseTranscodeHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReviewTranscodeHandler.class);

    @Autowired
    ReviewResourceService reviewResourceService;

    @Override
    public void saveTranscodeResult(TranscodeMessageDTO transcodeMessageParam) {
        List<ReviewResourceTranscodeResultBO> transcodeResultBoList = transcodeMessageParam.getReviewTranscodeList();
        if (CollectionUtils.isEmpty(transcodeResultBoList)) {
            LOGGER.error("转码数据格式化为空:{}", transcodeResultBoList);
            return;
        }

        // 找出视频资源id
        List<Integer> videoResultIdList = transcodeResultBoList.stream()
                .filter(transcodeResultBO -> ReviewResourceTypeEnum.video.getType().equals(transcodeResultBO.getReviewResourceType()))
                .map(ReviewResourceTranscodeResultBO::getReviewResourceId)
                .collect(Collectors.toList());

        // 根据资源ID匹配视频和视频封面 key:资源ID value:转码资源
        Map<Integer, List<ReviewResourceTranscodeResultBO>> transcodeResultBoMap = transcodeResultBoList.stream().collect(Collectors.groupingBy(ReviewResourceTranscodeResultBO::getReviewResourceId));
        List<ReviewResource> reviewResources = new ArrayList<>(transcodeResultBoList.size());

        // 将转码集合 转为 javaBean
        transcodeResultBoMap.forEach((resourceId, resourceTranscodeResultBos) -> transcodeDataToReviewResource(videoResultIdList, reviewResources, resourceId, resourceTranscodeResultBos));

        reviewResourceService.updateReviewResourceBatchById(reviewResources);
        LOGGER.info("评论资源转码数据更新成功：{}", reviewResources);
    }

    /**
     * 转码数据转评论资源
     *
     * @param videoResultIdList          视频id
     * @param reviewResources            评论资源
     * @param resourceId                 资源id
     * @param resourceTranscodeResultBos 资源转码结果
     */
    private void transcodeDataToReviewResource(List<Integer> videoResultIdList, List<ReviewResource> reviewResources, Integer resourceId, List<ReviewResourceTranscodeResultBO> resourceTranscodeResultBos) {
        ReviewResource reviewResource = new ReviewResource();
        reviewResource.setId(resourceId);
        reviewResource.setUpdateTime(LocalDateTime.now());

        for (ReviewResourceTranscodeResultBO resourceTranscodeResultBo : resourceTranscodeResultBos) {
            String transcodeUrl = resourceTranscodeResultBo.getReviewTranscodeResourceUrl();
            ReviewResourceTypeEnum resourceTypeEnum = ReviewResourceTypeEnum.parse(resourceTranscodeResultBo.getReviewResourceType());

            // 前置校验
            if (StringUtil.isBlank(transcodeUrl)) {
                String msg = StrUtil.format("评论资源转码后url为空 ,id:{},type:{},url:{}", resourceTranscodeResultBo.getReviewResourceId(), resourceTranscodeResultBo.getReviewResourceType(), resourceTranscodeResultBo.getReviewTranscodeResourceUrl());
                LOGGER.error(msg);
                FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.MainNotice, FeiShuAtUser.WXQ);
                reviewResource.setCompressState(ReviewCompressStateEnum.fail.getCode());
            } else {
                transcodeUrl = UrlUtils.replaceUrlPrefixOssToCdn(transcodeUrl, resourceTypeEnum.getOssFileCdnEnum());
                reviewResource.setCompressState(ReviewCompressStateEnum.succeed.getCode());
            }

            // 保存视频封面
            boolean videoCover = isVideoCover(videoResultIdList, resourceId, resourceTypeEnum.getType());
            if (videoCover) {
                reviewResource.setCompressVideoCover(transcodeUrl);
                continue;
            }
            // 保存转码后url
            reviewResource.setCompressResource(transcodeUrl);
            LOGGER.info("JavaBean 转换成功,data:{}", reviewResource);
        }
        reviewResources.add(reviewResource);
    }

    /**
     * 判断是否视频封面
     *
     * @param videoResultIdList 视频结果id列表
     * @param resourceId        资源id
     * @param type              类型
     * @return boolean
     */
    private Boolean isVideoCover(List<Integer> videoResultIdList, Integer resourceId, String type) {
        boolean exitVideoId = videoResultIdList.contains(resourceId);
        boolean isImage = ReviewResourceTypeEnum.image.getType().equals(type);
        return exitVideoId && isImage;
    }
}
