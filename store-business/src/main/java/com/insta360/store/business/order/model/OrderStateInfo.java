package com.insta360.store.business.order.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.order.enums.OrderState;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-06-04
 * @Description:
 */
@TableName("order_state_info")
public class OrderStateInfo extends BaseModel<OrderStateInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单状态
     */
    private Integer state;

    /**
     * 语言
     */
    private String language;

    /**
     * 名称
     */
    private String name;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "OrderStateInfo{" +
                "id=" + id +
                ", state=" + state +
                ", language=" + language +
                ", name=" + name +
                "}";
    }

    public InstaLanguage language() {
        return InstaLanguage.parse(language);
    }

    public OrderState orderState() {
        return OrderState.parse(getState());
    }
}