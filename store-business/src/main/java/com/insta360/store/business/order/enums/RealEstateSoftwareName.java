package com.insta360.store.business.order.enums;

/**
 * @Author: hyc
 * @Date: 2020-01-11
 * @Description:
 */
public enum RealEstateSoftwareName {
    //
    kuula,

    istaging,

    matterport,

    cloudpano,

    vpix;

    public static RealEstateSoftwareName parse(String name) {
        RealEstateSoftwareName[] names = values();
        for (RealEstateSoftwareName n : names) {
            if (n.name().equalsIgnoreCase(name)) {
                return n;
            }
        }
        return null;
    }
}
