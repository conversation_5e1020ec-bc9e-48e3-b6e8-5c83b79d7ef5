package com.insta360.store.business.configuration.check.handler;

import com.insta360.store.business.configuration.check.bo.CheckResultBO;
import com.insta360.store.business.configuration.check.chain.review.ReviewCheckChain;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 评论双检查处理程序
 *
 * <AUTHOR>
 * @date 2024/05/23
 */
@Scope("prototype")
@Component
public class ReviewDoubleCheckHandler extends BaseOrderDoubleCheckHandler {

    /**
     * 评论资源校验
     */
    @Autowired
    List<ReviewCheckChain> reviewCheckChainList;

    @Override
    protected List<CheckResultBO> doDoubleCheck(DoubleCheckBO doubleCheckBO) {
        return reviewCheckChainList.stream().map(baseOrderCheckChain -> {
            String name = baseOrderCheckChain.getName();
            Boolean checkPass = baseOrderCheckChain.doCheck(doubleCheckBO);
            Boolean sendMessage = baseOrderCheckChain.sendMessage();
            return new CheckResultBO(name, checkPass, sendMessage);
        }).collect(Collectors.toList());
    }

    @Override
    protected FeiShuAtUser[] getFeishuAtUsers() {
        return new FeiShuAtUser[]{FeiShuAtUser.DXY};
    }

}
