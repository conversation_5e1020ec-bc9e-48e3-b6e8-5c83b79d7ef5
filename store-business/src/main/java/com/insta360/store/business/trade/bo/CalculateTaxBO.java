package com.insta360.store.business.trade.bo;

import com.alibaba.fastjson.JSONArray;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.integration.avalara.enums.AvalaraTransactionType;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.rma.bo.RmaOrderBO;
import com.insta360.store.business.trade.dto.TaxDTO;
import com.insta360.store.business.user.model.StoreAccount;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2023/06/23
 * @Description:
 */
public class CalculateTaxBO implements Serializable {

    /**
     * 与地址税率关联的唯一标识
     */
    private String uniqueKey;

    /**
     * 交易类型
     */
    private AvalaraTransactionType avalaraTransactionType;

    /**
     * 商品子项
     */
    private JSONArray itemArrays;

    /**
     * 国家地区二字码
     */
    private String countryCode;

    /**
     * 省份/州地址二字码
     */
    private String provinceCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址/街道
     */
    private String address;

    /**
     * 详细地址/街道
     */
    private String subAddress;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 预选的支付渠道
     */
    private String prePaymentChannel;

    /**
     * 交易券
     */
    private String tradeCode;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 订单预创建项
     */
    private OrderCreation orderCreation;

    /**
     * 商城订单号
     */
    private String orderNumber;

    /**
     * 货币
     */
    private String currency;

    /**
     * 售后订单信息
     */
    private RmaOrderBO rmaOrder;

    /**
     * 离线税率国家
     */
    private InstaCountry offlineRateCountry;

    /**
     * 订单支付时间
     */
    private LocalDateTime orderPayTime;

    /**
     * 离线税率下载时间（yyyy-MM-dd）
     */
    private String downloadDate;

    /**
     * 分销码
     */
    private String resellerCode;

    /**
     * 访问用户
     */
    private StoreAccount storeAccount;

    public CalculateTaxBO() {
    }

    public CalculateTaxBO(TaxDTO taxParam) {
        if (taxParam != null) {
            BeanUtil.copyProperties(taxParam, this);
        }
    }

    public String getUniqueKey() {
        return uniqueKey;
    }

    public void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    public AvalaraTransactionType getAvalaraTransactionType() {
        return avalaraTransactionType;
    }

    public void setAvalaraTransactionType(AvalaraTransactionType avalaraTransactionType) {
        this.avalaraTransactionType = avalaraTransactionType;
    }

    public JSONArray getItemArrays() {
        return itemArrays;
    }

    public void setItemArrays(JSONArray itemArrays) {
        this.itemArrays = itemArrays;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getPrePaymentChannel() {
        return prePaymentChannel;
    }

    public void setPrePaymentChannel(String prePaymentChannel) {
        this.prePaymentChannel = prePaymentChannel;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public OrderCreation getOrderCreation() {
        return orderCreation;
    }

    public void setOrderCreation(OrderCreation orderCreation) {
        this.orderCreation = orderCreation;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public RmaOrderBO getRmaOrder() {
        return rmaOrder;
    }

    public void setRmaOrder(RmaOrderBO rmaOrder) {
        this.rmaOrder = rmaOrder;
    }

    public InstaCountry getOfflineRateCountry() {
        return offlineRateCountry;
    }

    public void setOfflineRateCountry(InstaCountry offlineRateCountry) {
        this.offlineRateCountry = offlineRateCountry;
    }

    public LocalDateTime getOrderPayTime() {
        return orderPayTime;
    }

    public void setOrderPayTime(LocalDateTime orderPayTime) {
        this.orderPayTime = orderPayTime;
    }

    public String getDownloadDate() {
        return downloadDate;
    }

    public void setDownloadDate(String downloadDate) {
        this.downloadDate = downloadDate;
    }

    public String getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(String resellerCode) {
        this.resellerCode = resellerCode;
    }

    public StoreAccount getStoreAccount() {
        return storeAccount;
    }

    public void setStoreAccount(StoreAccount storeAccount) {
        this.storeAccount = storeAccount;
    }

    @Override
    public String toString() {
        return "CalculateTaxBO{" +
                "uniqueKey='" + uniqueKey + '\'' +
                ", avalaraTransactionType=" + avalaraTransactionType +
                ", itemArrays=" + itemArrays +
                ", countryCode='" + countryCode + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", city='" + city + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", prePaymentChannel='" + prePaymentChannel + '\'' +
                ", tradeCode='" + tradeCode + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", orderCreation=" + orderCreation +
                ", orderNumber='" + orderNumber + '\'' +
                ", currency='" + currency + '\'' +
                ", rmaOrder=" + rmaOrder +
                ", offlineRateCountry=" + offlineRateCountry +
                ", orderPayTime=" + orderPayTime +
                ", downloadDate='" + downloadDate + '\'' +
                ", resellerCode='" + resellerCode + '\'' +
                ", storeAccount=" + storeAccount +
                '}';
    }
}
