package com.insta360.store.business.integration.tiktok.dto;


import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * 上下文dto
 *
 * <AUTHOR>
 * @date 2023/03/03
 */
public class ContextDTO implements Serializable {

    /**
     * 广告
     */
    private AdDTO ad;

    /**
     * 页面
     */
    private PageDTO page;

    /**
     * 用户
     */
    private UserDTO user;

    /**
     * 用户代理
     */
    @JSONField(name = "user_agent")
    private String userAgent;

    /**
     * 知识产权
     */
    private String ip;

    public AdDTO getAd() {
        return ad;
    }

    public void setAd(AdDTO ad) {
        this.ad = ad;
    }

    public PageDTO getPage() {
        return page;
    }

    public void setPage(PageDTO page) {
        this.page = page;
    }

    public UserDTO getUser() {
        return user;
    }

    public void setUser(UserDTO user) {
        this.user = user;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    @Override
    public String toString() {
        return "ContextDTO{" +
                "ad=" + ad +
                ", page=" + page +
                ", user=" + user +
                ", userAgent='" + userAgent + '\'' +
                ", ip='" + ip + '\'' +
                '}';
    }

}