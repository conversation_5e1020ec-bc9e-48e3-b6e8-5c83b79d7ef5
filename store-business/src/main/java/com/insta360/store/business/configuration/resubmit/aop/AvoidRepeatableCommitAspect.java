package com.insta360.store.business.configuration.resubmit.aop;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.ServletUtil;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.exception.CommonErrorCode;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wbt
 * @Date: 2020/07/03
 * @Description:
 */
@Aspect
@Component
public class AvoidRepeatableCommitAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(AvoidRepeatableCommitAspect.class);

    private static final String SFX = "##";

    /**
     * 压力测试请求头
     */
    private static final String STRESS_TESTING_HEADER = "stress_testing";

    /**
     * 锁标志
     */
    private static final String LOCK = "lock";

    /**
     * redis键前缀
     */
    private static final String REDIS_KEY_PREFIX = "store:avoid_epeatable:";

    @Autowired
    HttpServletRequest request;

    @Autowired
    RedisTemplate redisTemplate;

    /**
     * 重复提交aop
     */
    @Around("@annotation(arc)")
    public Object around(ProceedingJoinPoint point, AvoidRepeatableCommit arc) throws Throwable {
        // 压力测试请求直接放行
        String stressTesting = request.getHeader(STRESS_TESTING_HEADER);
        LOGGER.info("stress_testing:{}", stressTesting);
        if (Boolean.TRUE.equals(Boolean.valueOf(stressTesting))) {
            LOGGER.info("压力测试请求，直接放行。");
            return point.proceed();
        }

        // 获取请求ip地址 + 请求接口路口 （唯一key）
        String key = REDIS_KEY_PREFIX + ServletUtil.getClientIP(request) + request.getRequestURI();

        try {
            // 防止重复读取
            Boolean lock = redisTemplate.opsForValue().setIfAbsent(key + LOCK, LOCK + SFX);
            if (!lock) {
                throw new InstaException(CommonErrorCode.RequestTimeOutException, arc.desc());
            }

            String value = (String) redisTemplate.opsForValue().get(key);
            if (value != null) {
                throw new InstaException(CommonErrorCode.RequestTimeOutException, arc.desc());
            }

            // 根据配置时间设置redis超时时间，若在规定时间内重复请求，抛出异常
            redisTemplate.opsForValue().set(key, SFX + key, arc.timeOut(), TimeUnit.MILLISECONDS);

            // 放行
            return point.proceed();
        } finally {
            // 释放
            redisTemplate.delete(key + LOCK);
        }
    }
}
