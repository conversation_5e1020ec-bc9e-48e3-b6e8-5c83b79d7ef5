package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.meta.dao.CartRecommendationDao;
import com.insta360.store.business.meta.model.CartRecommendation;
import com.insta360.store.business.meta.service.CartRecommendationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-08-21
 * @Description:
 */
@Service
public class CartRecommendationServiceImpl extends BaseServiceImpl<CartRecommendationDao, CartRecommendation> implements CartRecommendationService {

    @Override
    public List<CartRecommendation> getByProduct(Integer productId) {

        QueryWrapper<CartRecommendation> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<CartRecommendation> getDefaultRecommendation() {

        QueryWrapper<CartRecommendation> qw = new QueryWrapper<>();
        qw.isNull("product_id");
        return baseMapper.selectList(qw);
    }
}
