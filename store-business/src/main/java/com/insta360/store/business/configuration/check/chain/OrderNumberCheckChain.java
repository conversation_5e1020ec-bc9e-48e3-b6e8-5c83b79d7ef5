package com.insta360.store.business.configuration.check.chain;

import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/27
 */
@Component
public class OrderNumberCheckChain extends BaseOrderCheckChain {

    private static final String NAME = "商城订单数量校验";

    @Autowired
    OrderItemService orderItemService;

    @Override
    public Boolean doCheck(DoubleCheckBO doubleCheckBO) {
        Order order = super.getOrder(doubleCheckBO);
        if (order == null) {
            return false;
        }
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        for (OrderItem orderItem : orderItems) {
            Integer number = orderItem.getNumber();
            if (number == null || number <= 0) {
                return false;
            }
        }
        return true;
    }

    @Override
    public String getName() {
        return NAME;
    }
}
