package com.insta360.store.business.user;

import com.insta360.compass.devtool.DaoGenerator;
import com.insta360.store.business.common.constants.DataSourceConstant;

import java.util.Arrays;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
public class DevTool {

    public static void main(String[] args) {
        new DaoGenerator()
                .setDataSource(DataSourceConstant.STORE_DATASOURCE)
                .setReferenceClass(DevTool.class)
                .setTables(Arrays.asList(
                        "user_pay_info"
                ))
                .setWithMapperXml(true)
                .setWithService(true)
                .run();
    }

}
