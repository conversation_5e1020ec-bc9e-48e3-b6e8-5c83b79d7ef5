package com.insta360.store.business.insurance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-04-25
 * @Description: 
 */
@TableName("care_card_device_change_record")
public class CareCardDeviceChangeRecord extends BaseModel<CareCardDeviceChangeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * care实体卡id
     */
    private Integer cardId;

    /**
     * 关联设备类型语言版本id
     */
    private Integer deviceTypeId;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 金蝶工单号
     */
    private String workNumber;

    /**
     * 操作人员工号
     */
    private String operator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCardId() {
        return cardId;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public Integer getDeviceTypeId() {
        return deviceTypeId;
    }

    public void setDeviceTypeId(Integer deviceTypeId) {
        this.deviceTypeId = deviceTypeId;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CareCardDeviceChangeRecord{" +
        "id=" + id +
        ", cardId=" + cardId +
        ", deviceTypeId=" + deviceTypeId +
        ", deviceType=" + deviceType +
        ", workNumber=" + workNumber +
        ", operator=" + operator +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}