package com.insta360.store.business.prime.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 * @description Prime相关常量
 */
public interface PrimeConstants {

    /**
     * 请求相关常量
     */
    interface Request {

        /**
         * BWP默认客户端ID
         */
        String DEFAULT_CLIENT_ID = "client-c9lak1kbvfj5qq9nho5ydr9by";

        /**
         * BWP默认客户端密钥
         */
        String DEFAULT_CLIENT_SECRET = "bwp-cs-MuLZ74FuomFD0z-3iZJ-UXalOp8";

        /**
         * 默认授权类型
         */
        String DEFAULT_GRANT_TYPE = "client_credentials";
    }

    /**
     * 响应相关常量
     */
    interface Response {

        /**
         * HTTP成功状态码的最小值（包含）
         */
        int HTTP_SUCCESS_MIN = 200;

        /**
         * HTTP成功状态码的最大值（不包含）
         */
        int HTTP_SUCCESS_MAX = 300;

        /**
         * 错误信息字段键名
         */
        String ERRORS_KEY = "errors";

        /**
         * 错误消息键名
         */
        String ERROR_MESSAGE_KEY = "message";

        /**
         * 数据字段键名
         */
        String DATA_KEY = "data";
    }

    /**
     * 创建套餐相关常量
     */
    interface Commodity {

        /**
         * Bundle后缀
         */
        String BUNDLE_SUFFIX = "-bundle";

        /**
         * 默认语言
         */
        String DEFAULT_LANGUAGE = "en-US";

        /**
         * 默认单位
         */
        String DEFAULT_UNIT = "UNIT";

        /**
         * 产品标题分隔符
         */
        String TITLE_SEPARATOR = "||";
    }

}
