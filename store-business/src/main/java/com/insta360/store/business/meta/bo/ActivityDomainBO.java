package com.insta360.store.business.meta.bo;

import com.insta360.store.business.meta.model.ActivityDynamicParam;
import com.insta360.store.business.meta.model.ActivityLocalesConfig;
import com.insta360.store.business.meta.model.ActivityPage;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public class ActivityDomainBO implements Serializable {

    /**
     * 活动唯一标识
     */
    private Integer id;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 活动URL路径
     */
    private String urlKey;

    /**
     * 活动 配置key 根据key获取配置
     */
    private String activityKey;

    /**
     * 活动名
     */
    private String activityName;

    /**
     * 活动埋点名
     */
    private String campaignName;

    /**
     * 活动当前状态
     */
    private String status;

    /**
     * 活动创建时间
     */
    private LocalDateTime createTime;

    /**
     * 活动更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 最后操作人
     */
    private String operatorUsername;

    /**
     * 全局配置json字符串
     */
    private String globalSetting;

    /**
     * 页面组件配置
     */
    private String components;

    /**
     * 不同国家和地区的配置
     */
    private List<ActivityLocalesConfig> localesConfigs;

    /**
     * 动态参数类型
     */
    private List<ActivityDynamicParam> dynamicParams;

    public ActivityDomainBO() {
    }

    public ActivityDomainBO(ActivityPage activityPage) {
        if (activityPage != null) {
            BeanUtils.copyProperties(activityPage, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getUrlKey() {
        return urlKey;
    }

    public void setUrlKey(String urlKey) {
        this.urlKey = urlKey;
    }

    public String getActivityKey() {
        return activityKey;
    }

    public void setActivityKey(String activityKey) {
        this.activityKey = activityKey;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getOperatorUsername() {
        return operatorUsername;
    }

    public void setOperatorUsername(String operatorUsername) {
        this.operatorUsername = operatorUsername;
    }

    public List<ActivityLocalesConfig> getLocalesConfigs() {
        return localesConfigs;
    }

    public void setLocalesConfigs(List<ActivityLocalesConfig> localesConfigs) {
        this.localesConfigs = localesConfigs;
    }

    public String getComponents() {
        return components;
    }

    public void setComponents(String components) {
        this.components = components;
    }

    public List<ActivityDynamicParam> getDynamicParams() {
        return dynamicParams;
    }

    public void setDynamicParams(List<ActivityDynamicParam> dynamicParams) {
        this.dynamicParams = dynamicParams;
    }

    public String getGlobalSetting() {
        return globalSetting;
    }

    public void setGlobalSetting(String globalSetting) {
        this.globalSetting = globalSetting;
    }

    @Override
    public String toString() {
        return "ActivityDomainBO{" +
                "id=" + id +
                ", uuid='" + uuid + '\'' +
                ", urlKey='" + urlKey + '\'' +
                ", activityKey='" + activityKey + '\'' +
                ", activityName='" + activityName + '\'' +
                ", campaignName='" + campaignName + '\'' +
                ", status='" + status + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", operatorUsername='" + operatorUsername + '\'' +
                ", globalSetting='" + globalSetting + '\'' +
                ", locales=" + localesConfigs +
                ", components='" + components + '\'' +
                ", dynamicParams=" + dynamicParams +
                '}';
    }
}
