package com.insta360.store.business.admin.order.dto;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 物流限制dto
 *
 * <AUTHOR>
 * @Description: 物流限制
 * @date 2023/10/26
 */
public class LogisticsRestrictionDTO implements Serializable {

    /**
     * 物流限制ID
     */
    @NotNull(message = "ID不能为空")
    private Integer id;

    /**
     * 限制类型（0-无需限制，1-危险品物流，2-不支持危险品物流）
     */
    @NotNull(message = "限制等级不能为空")
    private Integer restriction;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRestriction() {
        return restriction;
    }

    public void setRestriction(Integer restriction) {
        this.restriction = restriction;
    }

    @Override
    public String toString() {
        return "LogisticsRestrictionDTO{" +
                "id=" + id +
                ", restriction=" + restriction +
                '}';
    }
}