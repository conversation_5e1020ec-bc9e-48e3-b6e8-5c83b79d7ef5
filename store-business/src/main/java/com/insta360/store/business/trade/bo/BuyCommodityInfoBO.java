package com.insta360.store.business.trade.bo;

import com.insta360.store.business.meta.bo.Price;
import org.springframework.beans.BeanUtils;

/**
 * 买商品信息Bo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/21
 */
public class BuyCommodityInfoBO {

    /**
     * 套餐ID
     */
    private Integer commodityId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 数量
     */
    private Integer number;

    /**
     * 价格
     */
    private Price price;

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Price getPrice() {
        return price;
    }

    public void setPrice(Price price) {
        this.price = price;
    }

    public CommodityTotalPriceBO toCommodityTotalPriceBo() {
        CommodityTotalPriceBO commodityTotalPriceBo = new CommodityTotalPriceBO();
        BeanUtils.copyProperties(this, commodityTotalPriceBo);
        return commodityTotalPriceBo;
    }

    @Override
    public String toString() {
        return "BuyCommodityInfoBO{" +
                "commodityId=" + commodityId +
                ", productId=" + productId +
                ", number=" + number +
                ", price=" + price +
                '}';
    }
}
