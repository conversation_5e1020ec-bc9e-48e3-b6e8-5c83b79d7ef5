package com.insta360.store.business.integration.shopify.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.integration.shopify.dao.ShopifySkuMappingDao;
import com.insta360.store.business.integration.shopify.module.ShopifySkuMapping;
import com.insta360.store.business.integration.shopify.service.ShopifySkuMappingService;
import org.springframework.stereotype.Service;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-05-09
 * @Description:
 */
@Service
public class ShopifySkuMappingServiceImpl extends BaseServiceImpl<ShopifySkuMappingDao, ShopifySkuMapping> implements ShopifySkuMappingService {

    @Override
    public ShopifySkuMapping getBySku(String sku) {
        QueryWrapper<ShopifySkuMapping> qw = new QueryWrapper<>();
        qw.eq("sku", sku);
        return baseMapper.selectOne(qw);
    }
}
