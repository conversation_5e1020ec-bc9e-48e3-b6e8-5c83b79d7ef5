package com.insta360.store.business.discount.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.discount.dao.GiftCardPolicyDao;
import com.insta360.store.business.discount.model.GiftCardPolicy;
import com.insta360.store.business.discount.service.GiftCardPolicyService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-03-22
 * @Description:
 */
@Service
public class GiftCardPolicyServiceImpl extends BaseServiceImpl<GiftCardPolicyDao, GiftCardPolicy> implements GiftCardPolicyService {

    @Override
    public List<GiftCardPolicy> listByPolicyId(List<Integer> policyIdList) {
        LambdaQueryWrapper<GiftCardPolicy> qw = new LambdaQueryWrapper<>();
        qw.eq(GiftCardPolicy::getId,policyIdList);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<GiftCardPolicy> listByGiftCardCode(String giftCardCode) {
        LambdaQueryWrapper<GiftCardPolicy> qw = new LambdaQueryWrapper<>();
        qw.eq(GiftCardPolicy::getGiftCardCode,giftCardCode);
        return baseMapper.selectList(qw);
    }

    @Override
    public void deleteByGiftCardCode(String giftCardCode) {
        LambdaQueryWrapper<GiftCardPolicy> qw = new LambdaQueryWrapper<>();
        qw.eq(GiftCardPolicy::getGiftCardCode,giftCardCode);
        baseMapper.delete(qw);
    }

    @Override
    public List<GiftCardPolicy> listByGiftCardCodes(List<String> giftCardCodes) {
        if(CollectionUtils.isEmpty(giftCardCodes)) {
            return null;
        }
        LambdaQueryWrapper<GiftCardPolicy> qw = new LambdaQueryWrapper<>();
        qw.in(GiftCardPolicy::getGiftCardCode,giftCardCodes);
        return baseMapper.selectList(qw);
    }
}
