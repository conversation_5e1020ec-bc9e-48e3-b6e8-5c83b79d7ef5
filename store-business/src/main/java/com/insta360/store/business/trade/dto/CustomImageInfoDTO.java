package com.insta360.store.business.trade.dto;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.bo.CustomItem;
import com.insta360.store.business.trade.enums.CustomImageOriginEnum;
import com.insta360.store.business.trade.enums.IconCustomShellOriginEnum;
import com.insta360.store.business.trade.model.EngravingImage;
import com.insta360.store.business.trade.service.impl.helper.bind_service.enums.CustomImageType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2020/11/20
 * @Description:
 */
public class CustomImageInfoDTO implements Serializable {

    private Integer id;

    /**
     * 产品id
     */
    @JSONField(name = "product_id")
    private Integer productId;

    /**
     * 定制贴效果url
     */
    @JSONField(name = "customer_image_url")
    private String customerImageUrl;

    /**
     * 源图像url
     */
    @JSONField(name = "origin_image_url")
    private String originImageUrl;

    /**
     * 定制贴logo图片
     */
    @JSONField(name = "logo_image_url")
    private String logoImageUrl;

    /**
     * 生产图
     */
    @JSONField(name = "produce_image_url")
    private String produceImageUrl;

    /**
     * 创建时间
     */
    @JSONField(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 订单号
     */
    @JSONField(name = "order_numbers")
    private String orderNumbers;

    /**
     * 图片ID
     */
    @JSONField(name = "image_ids")
    private String imageIds;

    /**
     * 图片后缀
     */
    @JSONField(name = "image_suffix")
    private String imageSuffix;

    /**
     * 订单id
     */
    @JSONField(name = "order_id")
    private Integer orderId;

    /**
     * 定制图像信息
     */
    @JSONField(name = "custom_image_infos")
    private List<JSONObject> customImageInfos;

    /**
     * 定制图片来源
     *
     * @see CustomImageOriginEnum
     */
    private Integer imageOrigin;

    /**
     * icon来源
     *
     * @see IconCustomShellOriginEnum
     */
    private Integer iconOrigin;

    /**
     * 定制服务类型
     */
    private String serviceType;

    /**
     * 图标颜色
     */
    private String iconColor;

    public EngravingImage getPojoObject() {
        EngravingImage engravingImage = new EngravingImage();
        BeanUtil.copyProperties(this, engravingImage);
        return engravingImage;
    }

    /**
     * 解析定制服务信息
     *
     * @return
     */
    public List<CustomItem.CustomItemImageInfo> parse() {
        return customImageInfos.stream().map(customImageInfo -> {
            Integer imageId = customImageInfo.getInteger("image_id");
            Integer customNumber = customImageInfo.getInteger("custom_number");
            Integer commodityId = customImageInfo.getInteger("commodity_id");
            String customType = customImageInfo.getString("custom_type");
            return new CustomItem.CustomItemImageInfo(imageId, customNumber, CustomImageType.parse(customType), commodityId);
        }).collect(Collectors.toList());
    }

    /**
     * 解析定制图像类型
     *
     * @return {@link CustomImageType }
     */
    public CustomImageType parseCustomImageType(){
        return CustomImageType.parse(serviceType);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getCustomerImageUrl() {
        return customerImageUrl;
    }

    public void setCustomerImageUrl(String customerImageUrl) {
        this.customerImageUrl = customerImageUrl;
    }

    public String getOriginImageUrl() {
        return originImageUrl;
    }

    public void setOriginImageUrl(String originImageUrl) {
        this.originImageUrl = originImageUrl;
    }

    public String getLogoImageUrl() {
        return logoImageUrl;
    }

    public void setLogoImageUrl(String logoImageUrl) {
        this.logoImageUrl = logoImageUrl;
    }

    public String getProduceImageUrl() {
        return produceImageUrl;
    }

    public void setProduceImageUrl(String produceImageUrl) {
        this.produceImageUrl = produceImageUrl;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getOrderNumbers() {
        return orderNumbers;
    }

    public void setOrderNumbers(String orderNumbers) {
        this.orderNumbers = orderNumbers;
    }

    public String getImageIds() {
        return imageIds;
    }

    public void setImageIds(String imageIds) {
        this.imageIds = imageIds;
    }

    public String getImageSuffix() {
        return imageSuffix;
    }

    public void setImageSuffix(String imageSuffix) {
        this.imageSuffix = imageSuffix;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<JSONObject> getCustomImageInfos() {
        return customImageInfos;
    }

    public void setCustomImageInfos(List<JSONObject> customImageInfos) {
        this.customImageInfos = customImageInfos;
    }

    public Integer getImageOrigin() {
        return imageOrigin;
    }

    public void setImageOrigin(Integer imageOrigin) {
        this.imageOrigin = imageOrigin;
    }

    public Integer getIconOrigin() {
        return iconOrigin;
    }

    public void setIconOrigin(Integer iconOrigin) {
        this.iconOrigin = iconOrigin;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getIconColor() {
        return iconColor;
    }

    public void setIconColor(String iconColor) {
        this.iconColor = iconColor;
    }

    @Override
    public String toString() {
        return "CustomImageInfoDTO{" +
                "id=" + id +
                ", productId=" + productId +
                ", customerImageUrl='" + customerImageUrl + '\'' +
                ", originImageUrl='" + originImageUrl + '\'' +
                ", logoImageUrl='" + logoImageUrl + '\'' +
                ", produceImageUrl='" + produceImageUrl + '\'' +
                ", createTime=" + createTime +
                ", orderNumbers='" + orderNumbers + '\'' +
                ", imageIds='" + imageIds + '\'' +
                ", imageSuffix='" + imageSuffix + '\'' +
                ", orderId=" + orderId +
                ", customImageInfos=" + customImageInfos +
                ", imageOrigin=" + imageOrigin +
                ", iconOrigin=" + iconOrigin +
                ", serviceType='" + serviceType + '\'' +
                ", iconColor='" + iconColor + '\'' +
                '}';
    }
}
