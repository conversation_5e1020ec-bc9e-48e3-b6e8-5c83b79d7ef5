package com.insta360.store.business.trade.dto;

import com.alibaba.fastjson.JSONArray;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * @Author: wbt
 * @Date: 2023/06/23
 * @Description:
 */
public class TaxDTO {

    /**
     * 与地址税率关联的唯一标识
     */
    private String uniqueKey;

    /**
     * 国家二字码
     */
    @NotBlank(message = "国家二字码必填")
    private String countryCode;

    /**
     * 省份/州地址二字码
     */
    @NotBlank(message = "省份/州地址二字码必填")
    private String provinceCode;

    /**
     * 城市
     */
    @NotBlank(message = "城市必填")
    private String city;

    /**
     * 详细地址/街道
     */
    @NotBlank(message = "详细地址/街道必填")
    private String address;

    /**
     * 详细地址/街道
     */
    private String subAddress;

    /**
     * 邮编
     */
    @NotBlank(message = "邮编必填")
    private String zipCode;

    /**
     * 预选的支付渠道
     */
    private String prePaymentChannel;

    /**
     * 交易券
     */
    private String tradeCode;

    /**
     * 商品项
     */
    @NotEmpty(message = "商品项必填")
    private JSONArray itemArrays;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 分销码
     */
    private String resellerCode;

    /**
     * abt校验参数
     */
    private String apt;

    public String getUniqueKey() {
        return uniqueKey;
    }

    public void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getPrePaymentChannel() {
        return prePaymentChannel;
    }

    public void setPrePaymentChannel(String prePaymentChannel) {
        this.prePaymentChannel = prePaymentChannel;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public JSONArray getItemArrays() {
        return itemArrays;
    }

    public void setItemArrays(JSONArray itemArrays) {
        this.itemArrays = itemArrays;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(String resellerCode) {
        this.resellerCode = resellerCode;
    }

    public String getApt() {
        return apt;
    }

    public void setApt(String apt) {
        this.apt = apt;
    }

    @Override
    public String toString() {
        return "TaxDTO{" +
                "uniqueKey='" + uniqueKey + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", city='" + city + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", prePaymentChannel='" + prePaymentChannel + '\'' +
                ", tradeCode='" + tradeCode + '\'' +
                ", itemArrays=" + itemArrays +
                ", contactEmail='" + contactEmail + '\'' +
                ", resellerCode='" + resellerCode + '\'' +
                '}';
    }
}
