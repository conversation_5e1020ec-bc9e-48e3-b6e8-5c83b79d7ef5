package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dao.HomepageItemCommodityGroupDao;
import com.insta360.store.business.meta.dto.HomepageItemCommodityGroupDTO;
import com.insta360.store.business.meta.exception.MetaErrorCode;
import com.insta360.store.business.meta.model.HomepageItemCommodityGroup;
import com.insta360.store.business.meta.model.HomepageItemCommodityInfo;
import com.insta360.store.business.meta.model.HomepageItemMain;
import com.insta360.store.business.meta.service.HomepageItemCommodityGroupService;
import com.insta360.store.business.meta.service.HomepageItemCommodityInfoService;
import com.insta360.store.business.meta.service.HomepageItemMainService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-09-16
 * @Description:
 */
@Service
public class HomepageItemCommodityGroupServiceImpl extends BaseServiceImpl<HomepageItemCommodityGroupDao, HomepageItemCommodityGroup> implements HomepageItemCommodityGroupService {

    @Autowired
    HomepageItemCommodityInfoService homepageItemCommodityInfoService;

    @Autowired
    HomepageItemMainService homepageItemMainService;

    @Override
    public List<HomepageItemCommodityGroup> listCommodityGroups(Integer homeItemId) {
        QueryWrapper<HomepageItemCommodityGroup> qw = new QueryWrapper<>();
        qw.eq("home_item_id", homeItemId);
        qw.eq("deleted", false);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public List<HomepageItemCommodityGroup> listAllHomepageItemCommodityInfos(Integer homeItemId) {
        QueryWrapper<HomepageItemCommodityGroup> qw = new QueryWrapper<>();
        qw.eq("home_item_id", homeItemId);
        qw.eq("disabled", false);
        qw.eq("deleted", false);
        qw.orderByAsc("order_index");
        return baseMapper.selectList(qw);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public Integer createHomeItemCommodityGroup(HomepageItemCommodityGroupDTO commodityGroupParam) {
        HomepageItemCommodityGroup homepageItemCommodityGroup = commodityGroupParam.getPojoObject();
        // 参数校验
        if (homepageItemCommodityGroup.getCommodityId() == null || homepageItemCommodityGroup.getOrderIndex() == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 类别校验
        HomepageItemMain homepageItemMain = homepageItemMainService.getById(commodityGroupParam.getHomeItemId());
        if (homepageItemMain == null || homepageItemMain.getDeleted()) {
            throw new InstaException(MetaErrorCode.HomeItemNotFoundException);
        }

        //新增位置不在末尾
        if (!commodityGroupParam.getEnd()) {
            //create ->end index+1 空出插入位置
            QueryWrapper<HomepageItemCommodityGroup> qw = new QueryWrapper<>();
            qw.eq("home_item_id", homepageItemCommodityGroup.getHomeItemId());
            qw.eq("deleted", false);
            qw.ge("order_index", commodityGroupParam.getOrderIndex());
            qw.orderByAsc("order_index");
            List<HomepageItemCommodityGroup> homepageItemCommodityGroups = baseMapper.selectList(qw);
            //如果新增的位置正好有空位就不需要修改后面的
            if (commodityGroupParam.getOrderIndex().equals(homepageItemCommodityGroups.get(0).getOrderIndex())) {
                homepageItemCommodityGroups.forEach(o -> {
                    Integer index = o.getOrderIndex();
                    o.setOrderIndex(++index);
                });
                baseMapper.updateBatchHomepageItemCommodityGroup(homepageItemCommodityGroups);
            }
        }

        // insert
        homepageItemCommodityGroup.setCreateTime(LocalDateTime.now());
        homepageItemCommodityGroup.setUpdateTime(LocalDateTime.now());
        baseMapper.insert(homepageItemCommodityGroup);

        return homepageItemCommodityGroup.getId();
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateHomeItemCommodityGroup(HomepageItemCommodityGroupDTO commodityGroupParam,HomepageItemCommodityGroup itemCommodityGroup) {
        Integer firstIndex = commodityGroupParam.getFirstIndex();
        Integer lastIndex = commodityGroupParam.getLastIndex();
        Integer orderIndex = commodityGroupParam.getOrderIndex();
        //first -> last 修改影响到的位置的排序  orderIndex = endIndex
        if (!firstIndex.equals(lastIndex)) {
            //如果首尾序号相等，证明没有修改序号
            QueryWrapper<HomepageItemCommodityGroup> qw = new QueryWrapper<>();
            qw.eq("home_item_id", itemCommodityGroup.getHomeItemId());
            qw.eq("deleted", false);
            qw.ge("order_index", firstIndex);
            qw.le("order_index", lastIndex);
            qw.orderByAsc("order_index");
            List<HomepageItemCommodityGroup> homepageItemCommodityGroups = baseMapper.selectList(qw);

            //避免下面的去除元素可能存在的空指针异常
            if (homepageItemCommodityGroups.isEmpty()) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }

            if (orderIndex.equals(firstIndex)) {
                homepageItemCommodityGroups.remove(homepageItemCommodityGroups.size() - 1);
                homepageItemCommodityGroups.forEach(o -> {
                    Integer index = o.getOrderIndex();
                    o.setOrderIndex(++index);
                });
            } else if (orderIndex.equals(lastIndex)) {
                //去掉第一个
                homepageItemCommodityGroups.remove(0);
                homepageItemCommodityGroups.forEach(o -> {
                    Integer index = o.getOrderIndex();
                    o.setOrderIndex(--index);
                });
            }
            baseMapper.updateBatchHomepageItemCommodityGroup(homepageItemCommodityGroups);
        }

        // update
        itemCommodityGroup.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(itemCommodityGroup);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void deleteHomeItemCommodityGroup(HomepageItemCommodityGroupDTO commodityGroupParam, HomepageItemCommodityGroup homepageItemCommodityGroup) {
        //前进被删除元素后面的order_index
        if (!commodityGroupParam.getEnd()) {
            QueryWrapper<HomepageItemCommodityGroup> qw = new QueryWrapper<>();
            qw.eq("home_item_id", homepageItemCommodityGroup.getHomeItemId());
            qw.eq("deleted", false);
            qw.ge("order_index", commodityGroupParam.getOrderIndex());
            qw.orderByAsc("order_index");
            List<HomepageItemCommodityGroup> homepageItemCommodityGroups = baseMapper.selectList(qw);
            homepageItemCommodityGroups.forEach(o -> {
                Integer index = o.getOrderIndex();
                o.setOrderIndex(--index);
            });
            baseMapper.updateBatchHomepageItemCommodityGroup(homepageItemCommodityGroups);
        }

        // delete
        homepageItemCommodityGroup.setDisabled(true);
        homepageItemCommodityGroup.setDeleted(true);
        homepageItemCommodityGroup.setUpdateTime(LocalDateTime.now());
        this.updateById(homepageItemCommodityGroup);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void deleteBatchHomepageItemCommodityGroup(List<HomepageItemCommodityGroup> commodityGroups) {
        if (CollectionUtils.isEmpty(commodityGroups)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // delete
        List<Integer> groupIds = commodityGroups.stream().map(HomepageItemCommodityGroup::getId).collect(Collectors.toList());
        baseMapper.deleteBatchHomepageItemCommodityGroup(groupIds, LocalDateTime.now());

        // 连带的commodity info一起删除
        List<HomepageItemCommodityInfo> homepageItemCommodityInfos = homepageItemCommodityInfoService.listHomepageItemCommodityInfos(groupIds);
        if (CollectionUtils.isNotEmpty(homepageItemCommodityInfos)) {
            homepageItemCommodityInfoService.deleteBatchHomepageItemCommodityInfo(homepageItemCommodityInfos);
        }
    }

    @Override
    public void enableHomeItemCommodityGroup(HomepageItemCommodityGroup homepageItemCommodityGroup) {
        if (homepageItemCommodityGroup == null) {
            throw new InstaException(MetaErrorCode.HomeItemCommodityGroupNotFoundException);
        }

        // 启用
        homepageItemCommodityGroup.setDisabled(false);
        homepageItemCommodityGroup.setUpdateTime(LocalDateTime.now());
        this.updateById(homepageItemCommodityGroup);
    }

    @Override
    public void disableHomeItemCommodityGroup(HomepageItemCommodityGroup homepageItemCommodityGroup) {
        if (homepageItemCommodityGroup == null) {
            throw new InstaException(MetaErrorCode.HomeItemCommodityGroupNotFoundException);
        }

        // 禁用
        homepageItemCommodityGroup.setDisabled(true);
        homepageItemCommodityGroup.setUpdateTime(LocalDateTime.now());
        this.updateById(homepageItemCommodityGroup);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateHomeItemCommodityGroupList(List<HomepageItemCommodityGroup> homepageItemCommodityGroups) {
        // 批量更新排序
        baseMapper.updateBatchHomepageItemCommodityGroup(homepageItemCommodityGroups);
    }

    @Override
    public void updateOrderIndexByIds(List<HomepageItemCommodityGroup> groups) {
        if (CollectionUtils.isEmpty(groups)){
            return;
        }
        baseMapper.updateBatchHomepageItemCommodityGroup(groups);
    }
}
