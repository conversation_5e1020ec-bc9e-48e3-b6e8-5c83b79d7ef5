package com.insta360.store.business.integration.avalara.bo;

import com.insta360.store.business.meta.model.AvalaraLineTax;
import com.insta360.store.business.meta.model.AvalaraLineTaxDetail;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/6/30
 */
public class AvalaraLineItemDbBO implements Serializable {

    /**
     * 商品行
     */
    private AvalaraLineTax avalaraLineTax;

    /**
     * 商品行阶梯税率明细
     */
    private List<AvalaraLineTaxDetail> avalaraLineTaxDetailList;

    public AvalaraLineItemDbBO() {
    }

    public AvalaraLineItemDbBO(AvalaraLineTax avalaraLineTax, List<AvalaraLineTaxDetail> avalaraLineTaxDetailList) {
        this.avalaraLineTax = avalaraLineTax;
        this.avalaraLineTaxDetailList = avalaraLineTaxDetailList;
    }

    public AvalaraLineTax getAvalaraLineTax() {
        return avalaraLineTax;
    }

    public void setAvalaraLineTax(AvalaraLineTax avalaraLineTax) {
        this.avalaraLineTax = avalaraLineTax;
    }

    public List<AvalaraLineTaxDetail> getAvalaraLineTaxDetailList() {
        return avalaraLineTaxDetailList;
    }

    public void setAvalaraLineTaxDetailList(List<AvalaraLineTaxDetail> avalaraLineTaxDetailList) {
        this.avalaraLineTaxDetailList = avalaraLineTaxDetailList;
    }
}
