package com.insta360.store.business.user.cache;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2023/08/17
 * @Description:
 */
public class UserAccountCache implements Serializable {

    /**
     * 用户id（insta account id）
     */
    private Integer accountId;

    /**
     * 账户邮箱
     */
    private String username;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 语言
     */
    private String language;

    /**
     * 国家
     */
    private String country;

    public UserAccountCache() {
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        return "UserAccountCache{" +
                "accountId=" + accountId +
                ", username='" + username + '\'' +
                ", createTime=" + createTime +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                '}';
    }
}
