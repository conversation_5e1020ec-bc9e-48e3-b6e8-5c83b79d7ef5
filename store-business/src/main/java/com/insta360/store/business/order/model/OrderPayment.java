package com.insta360.store.business.order.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: hyc
 * @Date: 2019/2/11
 * @Description:
 */
@TableName("order_payment")
public class OrderPayment extends BaseModel<OrderPayment> {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "`order`")
    @TableField(value = "`order`")
    private Integer order;

    /**
     * 前置支付渠道（代表的是每一次选择的支付渠道）
     */
    private String preChannel;

    /**
     * 支付渠道
     *
     * @see PaymentChannel
     */
    private String channel;

    /**
     * 支付币种
     *
     * @see Currency
     */
    private String currency;

    /**
     * 商品总价
     */
    private Float amount;

    /**
     * 税费
     */
    private Float tax;

    /**
     * 订单应付金额 (商品总价 + 税费 + 运费)
     */
    private Double payableAmount;

    /**
     * 订单实付金额 （订单应付金额 - 优惠总金额）
     */
    private Double actualPayAmount;

    /**
     * 运费
     */
    @JSONField(name = "shipping_cost")
    private Float shippingCost;

    /**
     * TODO: 2019-11-28 优惠总金额请使用 totalDiscountFee
     */
    @JSONField(name = "coupon_fee")
    private Float couponFee;

    /**
     * 优惠总金额
     */
    @JSONField(name = "total_discount_fee")
    private Float totalDiscountFee;

    @JSONField(name = "gift_card_fee")
    private Float giftCardFee;

    /**
     * 订单支付状态
     *
     * @see OrderPaymentState
     */
    private Integer state;

    /**
     * 支付时间
     */
    @JSONField(name = "pay_time")
    private LocalDateTime payTime;

    /**
     * 安全交易
     */
    @JSONField(name = "trade_security")
    private Integer tradeSecurity;

    /**
     * klarna 交易 capture 的截止时间
     */
    private LocalDateTime klarnaCaptureTimeout;

    @JSONField(name = "wx_out_order_no")
    private String wxOutOrderNo;

    @JSONField(name = "wx_prepay_id")
    private String wxPrepayId;

    @JSONField(name = "channel_payment_id")
    private String channelPaymentId;

    private String ip;

    /**
     * 支付渠道折扣标记
     */
    private Boolean paymentChannelDiscountMark;

    /**
     * 运费税费
     */
    private BigDecimal shippingCostTax;

    /**
     * 是否需要支付(不持久化库表)
     */
    @TableField(exist = false)
    private boolean notPay;


    public boolean isNotPay() {
        return notPay;
    }

    public void setNotPay(boolean notPay) {
        this.notPay = notPay;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getPreChannel() {
        return preChannel;
    }

    public void setPreChannel(String preChannel) {
        this.preChannel = preChannel;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public Float getTax() {
        return tax;
    }

    public void setTax(Float tax) {
        this.tax = tax;
    }

    public Float getShippingCost() {
        return shippingCost;
    }

    public void setShippingCost(Float shippingCost) {
        this.shippingCost = shippingCost;
    }

    public Float getCouponFee() {
        return couponFee;
    }

    public void setCouponFee(Float couponFee) {
        this.couponFee = couponFee;
    }

    public Float getGiftCardFee() {
        return giftCardFee;
    }

    public void setGiftCardFee(Float giftCardFee) {
        this.giftCardFee = giftCardFee;
    }

    public Float getTotalDiscountFee() {
        return totalDiscountFee;
    }

    public void setTotalDiscountFee(Float totalDiscountFee) {
        this.totalDiscountFee = totalDiscountFee;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getTradeSecurity() {
        return tradeSecurity;
    }

    public void setTradeSecurity(Integer tradeSecurity) {
        this.tradeSecurity = tradeSecurity;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public LocalDateTime getKlarnaCaptureTimeout() {
        return klarnaCaptureTimeout;
    }

    public void setKlarnaCaptureTimeout(LocalDateTime klarnaCaptureTimeout) {
        this.klarnaCaptureTimeout = klarnaCaptureTimeout;
    }

    public String getWxOutOrderNo() {
        return wxOutOrderNo;
    }

    public void setWxOutOrderNo(String wxOutOrderNo) {
        this.wxOutOrderNo = wxOutOrderNo;
    }

    public String getWxPrepayId() {
        return wxPrepayId;
    }

    public void setWxPrepayId(String wxPrepayId) {
        this.wxPrepayId = wxPrepayId;
    }

    public String getChannelPaymentId() {
        return channelPaymentId;
    }

    public void setChannelPaymentId(String channelPaymentId) {
        this.channelPaymentId = channelPaymentId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Double getPayableAmount() {
        return payableAmount;
    }

    public void setPayableAmount(Double payableAmount) {
        this.payableAmount = payableAmount;
    }

    public Double getActualPayAmount() {
        return actualPayAmount;
    }

    public void setActualPayAmount(Double actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    public Boolean getPaymentChannelDiscountMark() {
        return paymentChannelDiscountMark;
    }

    public void setPaymentChannelDiscountMark(Boolean paymentChannelDiscountMark) {
        this.paymentChannelDiscountMark = paymentChannelDiscountMark;
    }

    public BigDecimal getShippingCostTax() {
        return shippingCostTax;
    }

    public void setShippingCostTax(BigDecimal shippingCostTax) {
        this.shippingCostTax = shippingCostTax;
    }

    @Override
    public String toString() {
        return "OrderPayment{" +
                "order=" + order +
                ", preChannel='" + preChannel + '\'' +
                ", channel='" + channel + '\'' +
                ", currency='" + currency + '\'' +
                ", amount=" + amount +
                ", tax=" + tax +
                ", payableAmount=" + payableAmount +
                ", actualPayAmount=" + actualPayAmount +
                ", shippingCost=" + shippingCost +
                ", couponFee=" + couponFee +
                ", totalDiscountFee=" + totalDiscountFee +
                ", giftCardFee=" + giftCardFee +
                ", state=" + state +
                ", payTime=" + payTime +
                ", tradeSecurity=" + tradeSecurity +
                ", klarnaCaptureTimeout=" + klarnaCaptureTimeout +
                ", wxOutOrderNo='" + wxOutOrderNo + '\'' +
                ", wxPrepayId='" + wxPrepayId + '\'' +
                ", channelPaymentId='" + channelPaymentId + '\'' +
                ", ip='" + ip + '\'' +
                ", paymentChannelDiscountMark=" + paymentChannelDiscountMark +
                ", shippingCostTax=" + shippingCostTax +
                ", notPay=" + notPay +
                '}';
    }

    @JSONField(serialize = false)
    public Price getTotalPayPrice() {
        float totalAmount = amount + tax + shippingCost - totalDiscountFee;
        Price price = new Price(currency(), totalAmount);
        price.format();
        return price;
    }

    @JSONField(serialize = false)
    public Price getTotalPrice() {
        float totalAmount = amount + tax + shippingCost;
        Price price = new Price(currency(), totalAmount);
        price.format();
        return price;
    }

    @JSONField(serialize = false)
    public Currency currency() {
        return Currency.parse(currency);
    }

    @JSONField(serialize = false)
    public OrderPaymentState paymentState() {
        return OrderPaymentState.parse(state);
    }

    @JSONField(serialize = false)
    public PaymentChannel paymentChannel() {
        return PaymentChannel.parse(channel);
    }

    /**
     * 解析安全交易等级
     *
     * @return
     */
    @JSONField(serialize = false)
    public PaymentTradeSecurityType tradeSecurityType() {
        return PaymentTradeSecurityType.parseCode(tradeSecurity);
    }
}
