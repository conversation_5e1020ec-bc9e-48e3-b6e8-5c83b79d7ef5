package com.insta360.store.business.outgoing.mq.reseller.helper;

import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.reseller.sender.ResellerOrderCreateMq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2022/04/13
 * @Description:
 */
@Component
public class ResellerSendMessageHelper {

    @Autowired
    ResellerOrderCreateMq resellerOrderCreateMq;

    @Autowired
    OrderService orderService;

    /**
     * 分销订单创建消息发送
     *
     * @param order
     * @param orderPayTime
     */
    public void sendCreateResellerOrderMsg(Order order, LocalDateTime orderPayTime) {
        resellerOrderCreateMq.sendCreateResellerOrderMsg(order, orderPayTime);
    }
}
