package com.insta360.store.business.integration.search.service.impl;

import com.insta360.store.business.integration.search.bo.OpenSearchCollectionDataBO;
import com.insta360.store.business.integration.search.bo.OpenSearchDataCollectBO;
import com.insta360.store.business.integration.search.dto.OpenSearchCollectionDTO;
import com.insta360.store.business.integration.search.service.DataCollectionReportService;
import com.insta360.store.business.outgoing.mq.report.helper.DataReportingMessageHelper;
import com.insta360.store.business.user.model.StoreAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/29
 */
@Component
public class DataCollectionReportServiceImpl implements DataCollectionReportService {

    /**
     * 登录状态
     */
    private static final String LOGIN_STATUS = "1";

    /**
     * 未 登录状态
     */
    private static final String UN_LOGIN_STATUS = "0";

    @Autowired
    DataReportingMessageHelper dataReportingMessageHelper;

    @Override
    public void report(List<OpenSearchCollectionDTO> openSearchCollectionDtoList, StoreAccount accessUser) {
        String userId = Optional.ofNullable(accessUser).map(StoreAccount::getInstaAccount).map(Objects::toString).orElse(null);
        String login = Optional.ofNullable(accessUser).isPresent() ? LOGIN_STATUS : UN_LOGIN_STATUS;

        List<OpenSearchCollectionDataBO> openSearchCollectionDataBoList = openSearchCollectionDtoList.stream().map(item -> item.getItemIdList().stream().map(itemId -> {
            OpenSearchCollectionDataBO openSearchCollectionDataBo = item.getPojoObject();
            openSearchCollectionDataBo.setUserId(userId);
            openSearchCollectionDataBo.setLogin(login);
            openSearchCollectionDataBo.setItemId(itemId);
            return openSearchCollectionDataBo;
        }).collect(Collectors.toList())).flatMap(List::stream).collect(Collectors.toList());

        OpenSearchDataCollectBO openSearchDataCollectBo = new OpenSearchDataCollectBO(openSearchCollectionDataBoList);
        dataReportingMessageHelper.sendOpenSearchReportingMessage(openSearchDataCollectBo);
    }

}
