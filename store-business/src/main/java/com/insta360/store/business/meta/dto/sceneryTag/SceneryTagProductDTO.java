package com.insta360.store.business.meta.dto.sceneryTag;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: py
 * @create: 2024-12-19 15:17
 */
public class SceneryTagProductDTO implements Serializable {

    /**
     * 关联产品的ID
     */
    private Integer sceneryTagProductId;

    /**
     * 场景标签的id
     */
    private Integer sceneryTagId;

    /**
     * 关联产品的id
     */
    private List<Integer> productIdList;

    public Integer getSceneryTagProductId() {
        return sceneryTagProductId;
    }

    public void setSceneryTagProductId(Integer sceneryTagProductId) {
        this.sceneryTagProductId = sceneryTagProductId;
    }

    public Integer getSceneryTagId() {
        return sceneryTagId;
    }

    public void setSceneryTagId(Integer sceneryTagId) {
        this.sceneryTagId = sceneryTagId;
    }

    public List<Integer> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Integer> productIdList) {
        this.productIdList = productIdList;
    }

    @Override
    public String toString() {
        return "SceneryTagProductDTO{" +
                "sceneryTagProductId=" + sceneryTagProductId +
                ", sceneryTagId=" + sceneryTagId +
                ", productIdList=" + productIdList +
                '}';
    }
}
