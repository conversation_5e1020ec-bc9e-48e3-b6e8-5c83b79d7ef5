package com.insta360.store.business.outgoing.mq.order.sender;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class OrderAutoSendToGyMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderAutoCancelMq.class);

    /**
     * 消息延迟发送时间
     */
    private static final Long ORDER_AUTO_TO_GY_TTL = 7 * 60 * 1000L;

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_order_payed_auto_push_gy_delay, messageType = MessageSenderType.time)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * 订单自动推送延迟队列
     *
     * @param order
     */
    public void sendOrderAutoToGyMessage(Order order) {
        // 只有商城已支付订单才可以自动推单，工单订单也不必推单
        if (order == null || !OrderState.payed.equals(order.orderState()) || order.isRepairOrder()) {
            return;
        }

        LOGGER.info("send order auto to gy message. pending... order_number:{}", order.getOrderNumber());

        OrderMessageDTO orderMessage = new OrderMessageDTO();
        orderMessage.setOrder(order);
        String messageId = rocketTcpMessageSender.sendDelayMessage(JSONObject.toJSONString(orderMessage), ORDER_AUTO_TO_GY_TTL);
        MqUtils.isBlankMessageIdHandle(messageId, this, orderMessage);
        LOGGER.info("send order auto to gy message. success... messageId:{}", messageId);
        LOGGER.info("send order auto to gy message. success... order_number:{}", order.getOrderNumber());
    }

}
