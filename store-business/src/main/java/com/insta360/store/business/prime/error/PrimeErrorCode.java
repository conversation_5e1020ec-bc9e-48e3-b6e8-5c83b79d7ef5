package com.insta360.store.business.prime.error;

import com.insta360.compass.core.exception.ErrorCode;

/**
 * Prime商品服务错误代码枚举类
 * <p>
 * 该枚举定义了与Amazon Prime商品服务相关的所有错误代码。
 * 错误代码范围为105009009到105009011，用于标识不同类型的错误情况。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4
 */
public enum PrimeErrorCode implements ErrorCode {

    GRAPHQL_REQUEST_FAILED(105009009, "接口请求失败"),

    PRIME_COMMODITY_CREATE_FAILED(105009010, "Prime商品创建失败"),

    PRIME_COMMODITY_PARAMS_FAILED(105009011, "Prime套餐参数不正确"),

    /**
     * Prime套餐参数缺失错误
     * 当创建Prime商品时所需的参数不完整时抛出
     */
    PRIME_COMMODITY_PARAMS_DEFICIENCY(105009012, "Prime套餐参数缺失"),

    PRIME_PRODUCT_ID_IS_NULL(105009013, "primeProductId为空"),
    PRIME_COMMODITY_EXIST(105009014, "prime商品已存在无法创建");

    /**
     * 错误代码
     */
    private final Integer code;

    /**
     * 错误消息
     */
    private final String msg;

    PrimeErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
