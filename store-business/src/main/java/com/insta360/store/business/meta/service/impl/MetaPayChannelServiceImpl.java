package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.meta.dao.MetaPayChannelDao;
import com.insta360.store.business.meta.model.MetaPayChannel;
import com.insta360.store.business.meta.service.MetaPayChannelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/24
 * @Description:
 */
@Service
public class MetaPayChannelServiceImpl extends BaseServiceImpl<MetaPayChannelDao, MetaPayChannel> implements MetaPayChannelService {

    @Override
    public List<MetaPayChannel> listByModuleId(Integer moduleId) {
        QueryWrapper<MetaPayChannel> qw = new QueryWrapper<>();
        qw.in("module_id", moduleId);
        return baseMapper.selectList(qw);
    }
}
