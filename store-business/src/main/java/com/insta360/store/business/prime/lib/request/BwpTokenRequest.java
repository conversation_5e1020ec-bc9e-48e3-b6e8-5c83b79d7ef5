package com.insta360.store.business.prime.lib.request;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public class BwpTokenRequest implements PrimeRequest {

    @J<PERSON><PERSON>ield(name = "client_id")
    private String clientId;

    @JSO<PERSON>ield(name = "client_secret")
    private String clientSecret;

    @JSONField(name = "grant_type")
    private String grantType;

    public BwpTokenRequest() {
    }

    public BwpTokenRequest(String clientId, String clientSecret, String grantType) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.grantType = grantType;
    }

    public Map<String, Object> buildFormData() {
        HashMap<String, Object> formData = new HashMap<>();
        formData.put("client_id", clientId);
        formData.put("client_secret", clientSecret);
        formData.put("grant_type", grantType);
        return formData;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    @Override
    public String toString() {
        return "BwpTokenRequest{" +
                "clientId='" + clientId + '\'' +
                ", clientSecret='" + clientSecret + '\'' +
                ", grantType='" + grantType + '\'' +
                '}';
    }
}
