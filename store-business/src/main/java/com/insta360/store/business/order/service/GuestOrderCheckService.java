package com.insta360.store.business.order.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.order.model.GuestOrderCheck;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-03-12
 * @Description:
 */
public interface GuestOrderCheckService extends BaseService<GuestOrderCheck> {

    /**
     * 根据订单号获取游客token
     *
     * @param orderNumber
     * @return
     */
    GuestOrderCheck getByOrderNumber(String orderNumber);

    /**
     * 根据游客token进行获取
     *
     * @param guestToken
     * @return
     */
    GuestOrderCheck getByGuestToken(String guestToken);
}
