package com.insta360.store.business.discount.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.discount.enums.DiscountEffectState;
import com.insta360.store.business.discount.enums.old.DiscountType;
import com.insta360.store.business.meta.bo.TradeCode;
import com.insta360.store.business.meta.enums.TradeCodeGroup;
import com.insta360.store.business.meta.enums.TradeCodeType;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 新代金券DB实体
 * @Date 2022/4/8
 */
@TableName("gift_card_new")
public class GiftCard extends BaseModel<GiftCard> implements TradeCode {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 代金券编号
     */
    private String giftCardCode;

    /**
     * 优惠类型
     *
     * @see DiscountType
     */
    private String discountType;

    /**
     * 代金券模版ID
     */
    private Integer templateId;

    /**
     * 生效时间
     */
    private LocalDateTime effectTime;

    /**
     * 失效时间
     */
    private LocalDateTime invalidTime;

    /**
     * 多语言tag
     */
    private String infoTag;

    /**
     * 使用时间
     */
    private LocalDateTime useTime;

    /**
     * 使用订单id
     */
    private Integer useOrder;

    /**
     * 绑定邮箱
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String bindEmail;

    /**
     * 备注
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 创建人工号
     */
    private String jobNumber;

    /**
     * 平台来源
     *
     * @see com.insta360.store.business.discount.enums.PlatformSourceType
     */
    private Integer platformSource;

    /**
     * 启用状态 0：禁用 1：启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGiftCardCode() {
        return giftCardCode;
    }

    public void setGiftCardCode(String giftCardCode) {
        this.giftCardCode = giftCardCode;
    }

    public String getDiscountType() {
        return discountType;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public LocalDateTime getEffectTime() {
        return effectTime;
    }

    public void setEffectTime(LocalDateTime effectTime) {
        this.effectTime = effectTime;
    }

    public LocalDateTime getInvalidTime() {
        return invalidTime;
    }

    public void setInvalidTime(LocalDateTime invalidTime) {
        this.invalidTime = invalidTime;
    }

    public String getInfoTag() {
        return infoTag;
    }

    public void setInfoTag(String infoTag) {
        this.infoTag = infoTag;
    }

    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }

    public Integer getUseOrder() {
        return useOrder;
    }

    public void setUseOrder(Integer useOrder) {
        this.useOrder = useOrder;
    }

    public String getBindEmail() {
        return bindEmail;
    }

    public void setBindEmail(String bindEmail) {
        this.bindEmail = bindEmail;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }


    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getPlatformSource() {
        return platformSource;
    }

    public void setPlatformSource(Integer platformSource) {
        this.platformSource = platformSource;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    @JSONField(serialize = false)
    public boolean isUsed() {
        return useOrder != null;
    }

    @JSONField(serialize = false)
    public boolean isFreeType() {
        DiscountType discountType = DiscountType.parse(this.discountType);
        return DiscountType.free_commodity_discount.equals(discountType);
    }

    @JSONField(serialize = false)
    @Override
    public TradeCodeGroup getCodeGroup() {
        return TradeCodeGroup.GIFT_CARD;
    }

    @JSONField(serialize = false)
    @Override
    public TradeCodeType getCodeType() {
        return TradeCodeType.GIFT_CARD;
    }

    @Override
    public String getCode() {
        return giftCardCode;
    }

    @Override
    public DiscountType discountType() {
        return DiscountType.parse(discountType);
    }

    @Override
    public boolean isEnable() {
        LocalDateTime now = LocalDateTime.now();
        if (Objects.nonNull(effectTime) && now.isBefore(effectTime)) {
            return false;
        }
        if (Objects.nonNull(invalidTime) && now.isAfter(invalidTime)) {
            return false;
        }
        return enabled;
    }

    /**
     * 生效状态匹配
     *
     * @return
     */
    public DiscountEffectState matchEffectState() {
        LocalDateTime now = LocalDateTime.now();
        // 被禁用
        if (!enabled) {
            return DiscountEffectState.disabled;
        }
        // 生效时间 && 失效时间 都为空则 -> 生效中
        else if (Objects.isNull(effectTime) && Objects.isNull(invalidTime)) {
            return DiscountEffectState.effect;
        }
        // 生效时间为空 && 失效时间不为空 && 当前时间在失效时间之前 -> 生效中
        else if (Objects.isNull(effectTime) && Objects.nonNull(invalidTime) && now.isBefore(invalidTime)) {
            return DiscountEffectState.effect;
        }
        // 生效时间不为空 && 当前时间在生效时间之前 -> 未来生效
        else if (Objects.nonNull(effectTime) && now.isBefore(effectTime)) {
            return DiscountEffectState.future_effect;
        }
        // 生效时间不为空 && 当前时间在生效时间之后 && (失效时间为空 || 当前时间在失效时间之前) -> 生效中
        else if (Objects.nonNull(effectTime) && now.isAfter(effectTime) && (Objects.isNull(invalidTime) || now.isBefore(invalidTime))) {
            return DiscountEffectState.effect;
        }

        return DiscountEffectState.expired;
    }

    @Override
    public String toString() {
        return "GiftCard{" +
                "id=" + id +
                ", giftCardCode='" + giftCardCode + '\'' +
                ", discountType='" + discountType + '\'' +
                ", templateId=" + templateId +
                ", effectTime=" + effectTime +
                ", invalidTime=" + invalidTime +
                ", infoTag='" + infoTag + '\'' +
                ", useTime=" + useTime +
                ", useOrder=" + useOrder +
                ", bindEmail='" + bindEmail + '\'' +
                ", remark='" + remark + '\'' +
                ", jobNumber='" + jobNumber + '\'' +
                ", platformSource=" + platformSource +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
