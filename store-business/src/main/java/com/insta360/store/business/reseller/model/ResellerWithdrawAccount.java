package com.insta360.store.business.reseller.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.reseller.common.ResellerConstant;
import com.insta360.store.business.reseller.enums.ResellerWithdrawAccountType;

import java.util.Objects;

/**
 * @Author: hyc
 * @Date: 2019/2/27
 * @Description:
 */
@TableName("reseller_withdraw_account")
public class ResellerWithdrawAccount extends BaseModel<ResellerWithdrawAccount> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分销商ID
     */
    private Integer resellerAutoId;

    @TableField("`type`")
    private String type;

    private String data;

    @TableField("`enable`")
    private Boolean enable;

    @JSONField(serialize = false)
    public boolean isAlipay() {
        return ResellerWithdrawAccountType.alipay.equals(ResellerWithdrawAccountType.parse(type));
    }

    @JSONField(serialize = false)
    public boolean updateAlipayUsername(String username) {
        JSONObject dataJson = JSON.parseObject(data);
        String dbUsername = dataJson.getString(ResellerConstant.withdrawAccountUsernameJsonKey);
        if (StrUtil.equals(dbUsername, username)) {
            return false;
        }
        dataJson.put(ResellerConstant.withdrawAccountUsernameJsonKey, username);
        this.data = dataJson.toJSONString();
        return true;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getResellerAutoId() {
        return resellerAutoId;
    }

    public void setResellerAutoId(Integer resellerAutoId) {
        this.resellerAutoId = resellerAutoId;
    }

    /**
     * 检查当前对象是否属于指定的分销商
     *
     * @param reseller 分销商对象，用于比较账户信息
     * @return 如果当前对象的账户与分销商的账户匹配，则返回true，否则返回false
     */
    public boolean isBelongTo(Reseller reseller) {
        return Objects.nonNull(resellerAutoId) && resellerAutoId.equals(reseller.getId());
    }

    public ResellerWithdrawAccountType withdrawAccountType() {
        return ResellerWithdrawAccountType.parse(type);
    }


    @Override
    public String toString() {
        return "ResellerWithdrawAccount{" +
                "id=" + id +
                ", resellerAutoId=" + resellerAutoId +
                ", type='" + type + '\'' +
                ", data='" + data + '\'' +
                ", enable=" + enable +
                '}';
    }
}
