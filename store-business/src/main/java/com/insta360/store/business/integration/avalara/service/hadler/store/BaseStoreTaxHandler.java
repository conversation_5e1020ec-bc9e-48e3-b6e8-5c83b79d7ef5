package com.insta360.store.business.integration.avalara.service.hadler.store;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.admin.order.enums.CommodityAttributeType;
import com.insta360.store.business.cloud.bo.BenefitDiscountItemBO;
import com.insta360.store.business.cloud.bo.BenefitDiscountResultBO;
import com.insta360.store.business.cloud.service.impl.helper.StoreBenefitDiscountHelper;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.commodity.service.impl.helper.CommodityBatchHelper;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.discount.dto.bo.DiscountCheckResult;
import com.insta360.store.business.discount.dto.bo.DiscountDetailBO;
import com.insta360.store.business.discount.dto.bo.PaymentChannelDiscountCalculateBO;
import com.insta360.store.business.discount.dto.bo.PaymentChannelDiscountResultBO;
import com.insta360.store.business.discount.service.impl.helper.PaymentChannelDiscountHelper;
import com.insta360.store.business.integration.avalara.bo.*;
import com.insta360.store.business.integration.avalara.cache.TaxInfoCache;
import com.insta360.store.business.integration.avalara.constant.TaxConstant;
import com.insta360.store.business.integration.avalara.enums.StoreTaxType;
import com.insta360.store.business.integration.avalara.exception.TaxErrorCode;
import com.insta360.store.business.integration.avalara.service.hadler.BaseTaxHandler;
import com.insta360.store.business.integration.avalara.service.helper.StoreItemResolveHelper;
import com.insta360.store.business.meta.model.AvalaraTaxRateFinally;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.AvalaraTaxRateFinallyService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import com.insta360.store.business.trade.bo.CalculateTaxBO;
import com.insta360.store.business.trade.bo.CalculateTaxResultBO;
import com.insta360.store.business.trade.service.impl.helper.CartItemsParser;
import com.insta360.store.business.trade.service.impl.helper.OrderShippingFeeCounter;
import com.insta360.store.business.utils.CommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2023/07/23
 * @Description: 商城内部计税
 */
public abstract class BaseStoreTaxHandler extends BaseTaxHandler {

    protected static final Logger LOGGER = LoggerFactory.getLogger(BaseStoreTaxHandler.class);

    @Autowired
    OrderService orderService;

    @Autowired
    ProductService productService;

    @Autowired
    CartItemsParser cartItemsParser;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    StoreItemResolveHelper storeItemResolveHelper;

    @Autowired
    AvalaraTaxRateFinallyService avalaraTaxRateFinallyService;

    @Autowired
    PaymentChannelDiscountHelper paymentChannelDiscountHelper;

    @Autowired
    OrderShippingFeeCounter orderShippingFeeCounter;

    @Autowired
    CommodityBatchHelper commodityBatchHelper;

    @Autowired
    StoreBenefitDiscountHelper storeBenefitDiscountHelper;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    @Override
    protected CalculateTaxResultBO doExecuteTransaction(CalculateTaxBO calculateTax) {
        // 下单地区
        InstaCountry country = InstaCountry.parse(calculateTax.getCountryCode());

        // 初始商品解析
        List<OrderItem> orderItems = this.listTaxOrderItem(calculateTax);

        // 获取运费
        BigDecimal shippingCost = this.getShippingCost(orderItems, country);

        // 支付优惠
        PaymentChannelDiscountResultBO paymentDiscountResult = this.getPaymentDiscountResult(calculateTax, orderItems, shippingCost);
        if (Objects.nonNull(paymentDiscountResult)) {
            shippingCost = shippingCost.subtract(paymentDiscountResult.getShippingFeePayDiscount());
        }

        // 过滤不参与计税的商品项
        orderItems = this.filterTaxFreeItem(orderItems);

        // 计税起征点校验: 参与计税的商品应税总金额 < 20 不参与计税
        BigDecimal totalTaxableAmount = getTotalTaxableAmount(orderItems, shippingCost);
        if (totalTaxableAmount.compareTo(TaxConstant.TAX_THRESHOLD_CA) < 0) {
            return new CalculateTaxResultBO(BigDecimal.ZERO, StoreTaxType.NORMAL);
        }

        // 税率
        BigDecimal taxRate = this.calculateTaxRate(calculateTax);

        // 总税费
        BigDecimal totalTax = BigDecimal.ZERO;
        for (OrderItem orderItem : orderItems) {
            // 计算单个item的总税费
            BigDecimal itemTotalTax = taxRate.multiply(orderItem.getItemTotalAmountPaid()).setScale(2, BigDecimal.ROUND_HALF_UP);
            totalTax = totalTax.add(itemTotalTax);

            // reset
            orderItem.setTotalTax(itemTotalTax);
        }

        // 运费税费
        BigDecimal shippingCostTax = this.getShippingCostTax(shippingCost, taxRate);
        // 总税费 = 商品总税费 + 运费税费
        totalTax = totalTax.add(shippingCostTax);

        // 与地址税率关联的唯一标识
        String uniqueKey = null;
        if (StringUtils.isBlank(calculateTax.getTradeCode())) {
            uniqueKey = this.cacheLineItems(taxRate, totalTax);
        }

        // 结果封装
        CalculateTaxResultBO calculateTaxResult = new CalculateTaxResultBO();
        calculateTaxResult.setTotalTax(totalTax);
        calculateTaxResult.setUniqueKey(uniqueKey);
        calculateTaxResult.setItemTax(this.parseItemTax(orderItems, shippingCostTax));
        calculateTaxResult.setStoreTaxType(StoreTaxType.FINALLY);
        return calculateTaxResult;
    }

    /**
     * 构建商品税费与商品之间的关系
     * <p>
     * key:order item id
     * value:order item tax
     *
     * @param orderItems
     * @return
     */
    protected Map<Integer, BigDecimal> parseItemTax(List<OrderItem> orderItems, BigDecimal shippingCostTax) {
        Map<Integer, BigDecimal> itemTaxMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, OrderItem::getTotalTax));
        itemTaxMap.put(TaxConstant.SHIPPING_FEE_COMMODITY_ID, shippingCostTax);
        return itemTaxMap;
    }

    @Override
    public List<OrderItem> filterTaxFreeItem(List<OrderItem> orderItems) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return Lists.newArrayList();
        }

        // 商品过滤
        orderItems = orderItems.stream()
                // 剔除对应产品
                .filter(orderItem -> !TaxConstant.FILTER_TAX_PRODUCT_ID_BY_CA.contains(orderItem.getProduct()))
                // 剔除对应套餐
                .filter(orderItem -> !TaxConstant.FILTER_TAX_COMMODITY_ID_BY_CA.contains(orderItem.getCommodity()))
                // 剔除所有增值服务的产品
                .filter(orderItem -> {
                    // 产品
                    Product product = productService.getById(orderItem.getProduct());
                    // 产品一级类目
                    ProductCategoryMainType categoryMainType = productCategoryHelper.getCategoryMainByKey(product.getCategoryKey());
                    // 过滤虚拟服务
                    return Objects.nonNull(categoryMainType) && !TaxConstant.CA_FILTER_TYPES.contains(categoryMainType);
                }).collect(Collectors.toList());

        // 纯电商品套餐过滤
        // 赠品过滤
        return Optional.ofNullable(orderItems)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> chargedItemFilter(list))
                .map(list -> giftItemFilter(list))
                .orElse(Lists.newArrayList());
    }

    /**
     * 纯电商品过滤
     *
     * @param orderItems
     * @return
     */
    private List<OrderItem> chargedItemFilter(List<OrderItem> orderItems) {
        List<Integer> commodityIds = orderItems.stream()
                .map(OrderItem::getCommodity)
                .collect(Collectors.toList());

        // 获取商品套餐报关配置信息Map
        Map<Integer, CommodityMeta> commodityMetaMap = commodityBatchHelper.commodityMetaMapByCommodityIds(commodityIds);
        if (Objects.isNull(commodityMetaMap)) {
            return Lists.newArrayList();
        }

        // 查询预购商品套餐报关配置
        List<OrderItem> orderItemFinallyList = orderItems.stream()
                .filter(orderItem -> {
                    if (commodityMetaMap.containsKey(orderItem.getCommodity())) {
                        return true;
                    }
                    FeiShuMessageUtil.storeGeneralMessage(String.format("CA订单计税,套餐ID【%s】未配置报关信息!!!", orderItem.getCommodity()), FeiShuGroupRobot.MainNotice, FeiShuAtUser.LB, FeiShuAtUser.CYJ);
                    return false;
                })
                .collect(Collectors.toList());

        // 商品属性类型列表
        List<CommodityAttributeType> attributeTypeList = commodityMetaMap.values()
                .stream()
                .map(CommodityMeta::parseCommodityAttributeType)
                .collect(Collectors.toList());

        // 订单商品属性类型中若不含'危险品'则全部需要计税
        // 订单商品中所有商品的商品属性若含有‘危险品’,若商品属性还含有‘含电套餐’，则‘Titan续航电池包936’、‘Titan电池925’也剔除，其余商品则参与计税
        // 订单商品中所有商品的商品属性若含有‘危险品’,,若商品属性还不含有‘含电套餐’，则'危险品' & '充电座'不计税，其余商品计税
        if (attributeTypeList.contains(CommodityAttributeType.DangerousGoods)) {
            // 若订单商品剩余商品中有商品的属性为'含电套餐'则需要剔除掉套餐ID为'936，925'的订单商品
            if (attributeTypeList.contains(CommodityAttributeType.PackageWithElectricity)) {
                return orderItemFinallyList.stream()
                        .filter(this::isNotContainsTitan)
                        .collect(Collectors.toList());
            } else {
                // 过滤得到商品属性类型 != '充电座' & '危险品' 的商品列表
                orderItemFinallyList = orderItemFinallyList.stream()
                        .filter(orderItem -> !this.isContainsChargingStand(commodityMetaMap, orderItem))
                        .collect(Collectors.toList());
            }
        }

        return orderItemFinallyList;
    }

    /**
     * 是否包含充电座
     *
     * @param commodityMetaMap
     * @param orderItem
     * @return
     */
    private boolean isContainsChargingStand(Map<Integer, CommodityMeta> commodityMetaMap, OrderItem orderItem) {
        if (CommonUtil.isEmpty(commodityMetaMap) || Objects.isNull(orderItem)) {
            return false;
        }

        CommodityMeta commodityMeta = commodityMetaMap.get(orderItem.getCommodity());
        if (Objects.isNull(commodityMeta)) {
            return false;
        }

        CommodityAttributeType commodityAttributeType = commodityMeta.parseCommodityAttributeType();
        return CommodityAttributeType.ChargingStand.equals(commodityAttributeType) || CommodityAttributeType.DangerousGoods.equals(commodityAttributeType);
    }

    /**
     * 是否不包含Titan系列含电套餐
     *
     * @param orderItem
     * @return
     */
    private boolean isNotContainsTitan(OrderItem orderItem) {
        return !TaxConstant.TITAN_COMMODITYS.contains(orderItem.getCommodity());
    }

    /**
     * 缓存子项税率
     *
     * @param taxRate
     * @param totalTax
     * @return
     */
    protected String cacheLineItems(BigDecimal taxRate, BigDecimal totalTax) {
        // 商品项信息
        TaxInfoCache.StoreTaxItemInfoCache storeTaxItemInfoCache = new TaxInfoCache.StoreTaxItemInfoCache();
        storeTaxItemInfoCache.setTaxRate(taxRate);

        // 缓存数据参数
        TaxInfoCache taxInfoCache = new TaxInfoCache();
        taxInfoCache.setTotalTax(totalTax);
        taxInfoCache.setStoreTaxItemInfoCache(storeTaxItemInfoCache);

        // 缓存设置
        String uniqueKey = UUID.randomUUID().toString();
        RedisTemplateUtil.setKeyValue(TaxConstant.AVALARA_TAX_RATE_CACHE + uniqueKey, taxInfoCache, 5, TimeUnit.MINUTES);
        return uniqueKey;
    }

    @Override
    protected BigDecimal calculateCacheTax(CalculateTaxBO calculateTax, TaxInfoCache taxInfoCache) {
        // 获取缓存结构
        TaxInfoCache.StoreTaxItemInfoCache storeTaxItemInfoCache = taxInfoCache.getStoreTaxItemInfoCache();
        // 缓存的通用税率
        BigDecimal taxRate = storeTaxItemInfoCache.getTaxRate();

        // 下单地区
        InstaCountry country = InstaCountry.parse(calculateTax.getCountryCode());

        ParseLineItemBO storeParseLineItem = new ParseLineItemBO();
        storeParseLineItem.setTradeCode(calculateTax.getTradeCode());
        storeParseLineItem.setContactEmail(calculateTax.getContactEmail());
        storeParseLineItem.setPrePaymentChannel(calculateTax.getPrePaymentChannel());
        storeParseLineItem.setCountry(country);
        storeParseLineItem.setSheetItems(cartItemsParser.parse(calculateTax.getItemArrays()));
        storeParseLineItem.setResellerCode(calculateTax.getResellerCode());
        // 加购商品
        List<OrderItem> orderItems = storeItemResolveHelper.parseItemLines(storeParseLineItem);
        // 赠品
        List<OrderItem> giftItems = storeItemResolveHelper.parseGiftItems(storeParseLineItem);
        // 合并加购商品与赠品
        Optional.ofNullable(orderItems)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(list -> Optional.ofNullable(giftItems)
                        .filter(CollectionUtils::isNotEmpty)
                        .ifPresent(list::addAll));

        // 获取运费
        BigDecimal shippingCost = this.getShippingCost(orderItems, country);

        // 优惠折扣计算
        PaymentChannelDiscountResultBO discountResult = this.getPaymentDiscountResult(calculateTax, orderItems, shippingCost);
        if (Objects.nonNull(discountResult)) {
            shippingCost = shippingCost.subtract(discountResult.getShippingFeePayDiscount());
        }

        // 过滤特殊商品
        orderItems = this.filterTaxFreeItem(orderItems);

        // 计税起征点校验: 参与计税的商品应税总金额 < 20 不参与计税
        BigDecimal totalTaxableAmount = getTotalTaxableAmount(orderItems, shippingCost);
        if (totalTaxableAmount.compareTo(TaxConstant.TAX_THRESHOLD_CA) < 0) {
            return BigDecimal.ZERO;
        }

        // 总税费
        BigDecimal totalTax = Optional.ofNullable(orderItems)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .map(orderItem -> orderItem.getItemTotalAmountPaid()
                                .multiply(taxRate)
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                        )
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .orElse(BigDecimal.ZERO);

        // 总税费 = 商品总税费 + 运费税费
        totalTax = totalTax.add(this.getShippingCostTax(shippingCost, taxRate));

        return totalTax;
    }

    /**
     * 获取计税商品项
     *
     * @param calculateTax
     * @return
     */
    abstract protected List<OrderItem> listTaxOrderItem(CalculateTaxBO calculateTax);

    /**
     * 计算税率
     *
     * @param calculateTax
     * @return
     */
    abstract protected BigDecimal calculateTaxRate(CalculateTaxBO calculateTax);

    /**
     * 获取支付优惠结果
     *
     * @param calculateTax
     * @param orderItems
     * @param shippingCost
     * @return
     */
    abstract protected PaymentChannelDiscountResultBO getPaymentDiscountResult(CalculateTaxBO calculateTax, List<OrderItem> orderItems, BigDecimal shippingCost);

    /**
     * 获取参与计税的商品应税总金额
     *
     * @param orderItems
     * @param shippingCost
     * @return
     */
    protected BigDecimal getTotalTaxableAmount(List<OrderItem> orderItems, BigDecimal shippingCost) {
        if (CollectionUtils.isEmpty(orderItems)) {
            return BigDecimal.ZERO;
        }
        // 应税金额
        BigDecimal totalTaxableAmount = orderItems.stream()
                .map(OrderItem::getItemTotalAmountPaid)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(shippingCost);
        return totalTaxableAmount;
    }

    /**
     * 获取运费
     *
     * @param orderItemList
     * @param country
     * @return
     */
    protected BigDecimal getShippingCost(List<OrderItem> orderItemList, InstaCountry country) {
        return Objects.nonNull(orderPayment) ? new BigDecimal(String.valueOf(orderPayment.getShippingCost())) : BigDecimal.ZERO;
    }

    /**
     * 获取运费税费
     *
     * @param shippingCost
     * @param taxRate
     * @return
     */
    protected BigDecimal getShippingCostTax(BigDecimal shippingCost, BigDecimal taxRate) {
        return shippingCost.multiply(taxRate).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 获取税率
     *
     * @param countryCode
     * @param provinceCode
     * @return
     */
    protected BigDecimal getTaxRate(String countryCode, String provinceCode) {
        AvalaraTaxRateFinally avalaraTaxRateFinally = avalaraTaxRateFinallyService.getAvalaraTaxRateFinally(countryCode, provinceCode);
        if (avalaraTaxRateFinally == null) {
            LOGGER.info("【商城计税】兜底税率不存在。country_code:{},province_code:{}", countryCode, provinceCode);
            FeiShuMessageUtil.storeGeneralMessage("【CA计税】税率不存在。country_code:" + countryCode + ", province_code:" + provinceCode, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.CYJ);
            throw new InstaException(TaxErrorCode.AvalaraGetTaxErrorException);
        }
        return avalaraTaxRateFinally.getTaxRate();
    }

    /**
     * 设置商品交易券折扣
     *
     * @param orderItems
     * @param orderDiscountResult
     */
    protected void itemTradeDiscountUpdate(List<OrderItem> orderItems, DiscountCheckResult orderDiscountResult, BenefitDiscountResultBO benefitDiscountResult) {
        // 订单交易券折扣
        Map<String, BigDecimal> itemDiscountMap = new HashMap<>(orderItems.size());
        if (Objects.nonNull(orderDiscountResult) && CollectionUtils.isNotEmpty(orderDiscountResult.getDiscountDetails())) {
            itemDiscountMap = orderDiscountResult.getDiscountDetails()
                    .stream()
                    .collect(Collectors.toMap(DiscountDetailBO::getUniqueKey, DiscountDetailBO::getItemDiscountAmount));
        }

        // 订阅优惠
        Map<Integer, BigDecimal> subscribeDiscountMap = new HashMap<>(orderItems.size());
        if (Objects.nonNull(benefitDiscountResult) && CollectionUtils.isNotEmpty(benefitDiscountResult.getBenefitDiscountItems())) {
            subscribeDiscountMap = benefitDiscountResult.getBenefitDiscountItems()
                    .stream()
                    .collect(Collectors.toMap(BenefitDiscountItemBO::getCommodityId, BenefitDiscountItemBO::getBenefitDiscountTotalAmount));
        }

        for (OrderItem orderItem : orderItems) {
            if (itemDiscountMap.containsKey(orderItem.getUniqueKey())) {
                orderItem.setTotalDiscount(itemDiscountMap.get(orderItem.getUniqueKey()));
            } else if (subscribeDiscountMap.containsKey(orderItem.getCommodity())) {
                orderItem.setTotalDiscount(subscribeDiscountMap.get(orderItem.getCommodity()));
            } else {
                orderItem.setTotalDiscount(BigDecimal.ZERO);
            }
        }
    }

    @Override
    public void resolveTransaction(AvalaraResponseResolveBO avalaraResponseResolve) {
    }

    @Override
    public AvalaraPaymentDiscountResultBO paymentDiscountTaxCalculation(TaxRecalculationBO taxRecalculation) {
        // 获取订单
        Order order = taxRecalculation.getOrder();

        // 订单支付
        OrderPayment orderPayment = taxRecalculation.getOrderPayment();

        // 获取订单商品
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        // 支付优惠金额计算
        PaymentChannelDiscountCalculateBO discountCalculateBo = new PaymentChannelDiscountCalculateBO();
        discountCalculateBo.setOrderItemList(orderItemList);
        discountCalculateBo.setPaymentChannel(taxRecalculation.getPaymentChannel());
        discountCalculateBo.setArea(order.getArea());
        discountCalculateBo.setPayTime(taxRecalculation.getPayTime());
        discountCalculateBo.setShippingFee(new BigDecimal(String.valueOf(orderPayment.getShippingCost())));
        PaymentChannelDiscountResultBO paymentChannelDiscountResult = paymentChannelDiscountHelper.discountCalculate(discountCalculateBo);

        if (Objects.isNull(paymentChannelDiscountResult)) {
            return null;
        }

        // 过滤不参与CA计税的特殊商品
        orderItemList = this.filterTaxFreeItem(orderItemList);

        // 获取订单地址
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());

        // 获取CA兜底税率
        BigDecimal taxRate = getTaxRate(orderDelivery.getCountryCode(), orderDelivery.getProvinceCode());

        // 支付优惠后的运费
        BigDecimal shippingCost = new BigDecimal(String.valueOf(orderPayment.getShippingCost())).subtract(paymentChannelDiscountResult.getShippingFeePayDiscount());

        // 计税起征点校验: 参与计税的商品应税总金额 < 20 不参与计税
        BigDecimal totalTaxableAmount = getTotalTaxableAmount(orderItemList, shippingCost);
        if (totalTaxableAmount.compareTo(TaxConstant.TAX_THRESHOLD_CA) < 0) {
            taxRate = BigDecimal.ZERO;
            LOGGER.info("[CA订单计税]不符合计税起征点. storeTransactionType:{}, orderItemList:{}, totalTaxableAmount:{}, shippingCost:{}", getStoreTransactionType(), JSON.toJSONString(orderItemList), totalTaxableAmount, shippingCost);
        }

        // 订单总税费
        BigDecimal orderTotalTax = BigDecimal.ZERO;

        // 商品税费明细
        ArrayListMultimap<Integer, ItemTaxInfoBO> itemTaxInfoMap = ArrayListMultimap.create();
        for (OrderItem orderItem : orderItemList) {
            BigDecimal itemTotalAmountPaidAmount = orderItem.getItemTotalAmountPaid();
            BigDecimal itemTax = itemTotalAmountPaidAmount.multiply(taxRate).setScale(2, BigDecimal.ROUND_HALF_UP);
            orderTotalTax = orderTotalTax.add(itemTax);
            orderItem.setTotalTax(itemTax);
            itemTaxInfoMap.put(orderItem.getId(), new ItemTaxInfoBO(orderItem.getId(), orderItem.getUniqueKey(), itemTax));
        }

        // 运费税费计算
        BigDecimal shippingCostTax = shippingCost.multiply(taxRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        // 将运费税费添加进行明细map
        itemTaxInfoMap.put(TaxConstant.SHIPPING_FEE_COMMODITY_ID, new ItemTaxInfoBO(TaxConstant.SHIPPING_FEE_COMMODITY_ID, String.format("%s_%s", TaxConstant.SHIPPING_FEE_COMMODITY_ID, TaxConstant.SHIPPING_FEE_COMMODITY_ID), shippingCostTax));

        orderTotalTax = orderTotalTax.add(shippingCostTax);

        return new AvalaraPaymentDiscountResultBO(orderTotalTax, itemTaxInfoMap);
    }

    /**
     * 过滤赠品
     *
     * @param orderItemList
     * @return
     */
    protected List<OrderItem> giftItemFilter(List<OrderItem> orderItemList) {
        // 过滤赠品
        return Optional.ofNullable(orderItemList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.stream()
                        .filter(item -> !item.getIsGift())
                        .collect(Collectors.toList()))
                .orElse(Lists.newArrayList());
    }
}
