package com.insta360.store.business.prime.lib.response;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public class BwpTokenResponse {

    @JSONField(name = "access_token")
    private String accessToken;

    @JSONField(name = "expires_in")
    private Integer expiresIn;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Integer getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Integer expiresIn) {
        this.expiresIn = expiresIn;
    }
}
