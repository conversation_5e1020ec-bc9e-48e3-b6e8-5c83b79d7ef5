package com.insta360.store.business.search.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/7
 */
public class ResultFieldBO implements Serializable {

    private Long id;

    private String search_result;

    private String adapter_type_filter;

    private String accessories_category_filter;

    private String navigation_bar_filter;

    private Integer discount_mark;

    private Integer product_id;

    private String product_name;

    private Integer commodity_id;

    private String commodity_name;

    private String url_key;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSearch_result() {
        return search_result;
    }

    public void setSearch_result(String search_result) {
        this.search_result = search_result;
    }

    public String getAdapter_type_filter() {
        return adapter_type_filter;
    }

    public void setAdapter_type_filter(String adapter_type_filter) {
        this.adapter_type_filter = adapter_type_filter;
    }

    public String getAccessories_category_filter() {
        return accessories_category_filter;
    }

    public void setAccessories_category_filter(String accessories_category_filter) {
        this.accessories_category_filter = accessories_category_filter;
    }

    public String getNavigation_bar_filter() {
        return navigation_bar_filter;
    }

    public void setNavigation_bar_filter(String navigation_bar_filter) {
        this.navigation_bar_filter = navigation_bar_filter;
    }

    public Integer getProduct_id() {
        return product_id;
    }

    public void setProduct_id(Integer product_id) {
        this.product_id = product_id;
    }

    public String getProduct_name() {
        return product_name;
    }

    public void setProduct_name(String product_name) {
        this.product_name = product_name;
    }

    public Integer getCommodity_id() {
        return commodity_id;
    }

    public void setCommodity_id(Integer commodity_id) {
        this.commodity_id = commodity_id;
    }

    public String getCommodity_name() {
        return commodity_name;
    }

    public void setCommodity_name(String commodity_name) {
        this.commodity_name = commodity_name;
    }

    public String getUrl_key() {
        return url_key;
    }

    public void setUrl_key(String url_key) {
        this.url_key = url_key;
    }

    public Integer getDiscount_mark() {
        return discount_mark;
    }

    public void setDiscount_mark(Integer discount_mark) {
        this.discount_mark = discount_mark;
    }

    @Override
    public String toString() {
        return "ResultFieldBO{" +
                "id=" + id +
                ", search_result='" + search_result + '\'' +
                ", adapter_type_filter='" + adapter_type_filter + '\'' +
                ", accessories_category_filter='" + accessories_category_filter + '\'' +
                ", navigation_bar_filter='" + navigation_bar_filter + '\'' +
                ", discount_mark='" + discount_mark + '\'' +
                ", product_id=" + product_id +
                ", product_name='" + product_name + '\'' +
                ", commodity_id=" + commodity_id +
                ", commodity_name='" + commodity_name + '\'' +
                ", url_key='" + url_key + '\'' +
                '}';
    }
}
