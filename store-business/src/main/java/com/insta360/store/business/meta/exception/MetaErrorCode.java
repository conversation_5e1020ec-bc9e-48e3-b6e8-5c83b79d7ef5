package com.insta360.store.business.meta.exception;

import com.insta360.compass.core.exception.ErrorCode;

/**
 * @Author: wbt
 * @Date: 2021/08/12
 * @Description:
 */
public enum MetaErrorCode implements ErrorCode {

    /**
     * top bar未找到
     */
    TopBarNotFoundException(90001, "Top Bar未找到"),

    /**
     * top bar info未找到
     */
    TopBarInfoNotFoundException(90002, "Top Bar Info未找到"),

    /**
     * Top Bar Placeholder未找到
     */
    TopBarPlaceholderNotFoundException(90003, "Top Bar Placeholder未找到"),

    /**
     * Navigation Bar未找到
     */
    NavigationBarNotFoundException(90004, "Navigation Bar未找到"),

    /**
     * Navigation Bar一级目录未找到
     */
    NavigationBarCategoryNotFoundException(90005, "Navigation Bar一级目录未找到"),

    /**
     * Navigation Bar二级目录未找到
     */
    NavigationBarCategorySubsetNotFoundException(90006, "Navigation Bar二级目录未找到"),

    /**
     * Navigation Bar产品配置信息未找到
     */
    NavigationBarCommodityInfoNotFoundException(90007, "Navigation Bar产品配置信息未找到"),

    /**
     * 无效的Navigation Bar跳转链接类型
     */
    InvalidUrlLineTypeException(90008, "无效的Navigation Bar跳转链接类型"),

    /**
     * Trade Point未找到
     */
    TradePointNotFoundException(90009, "Trade Point未找到"),

    /**
     * Trade Point Info未找到
     */
    TradePointInfoNotFoundException(90010, "Trade Point Info未找到"),

    /**
     * Home Item未找到
     */
    HomeItemNotFoundException(90011, "Home Item未找到"),

    /**
     * Home Item Commodity Group未找到
     */
    HomeItemCommodityGroupNotFoundException(90012, "Home Item Commodity Group未找到"),

    /**
     * Home Item Info未找到
     */
    HomeItemCommodityInfoNotFoundException(90013, "Home Item Info未找到"),

    /**
     * Adapter Type未找到
     */
    AdapterTypeNotFoundException(90014, "Adapter Type未找到"),

    /**
     * Adapter Type未找到
     */
    AdapterTypeInfoNotFoundException(90015, "Adapter Type Info未找到"),

    /**
     * 税号数据重复
     */
    TaxCodeAlreadyExistException(90016, "税号数据重复"),

    /**
     * 税号绑定关系存在
     */
    HasTaxCategoryException(90017, "税号绑定已存在改类别"),

    /**
     * 税号绑定关系存在
     */
    TaxCodeBindAlreadyExistException(90018, "删除失败！仍有产品配置该税号"),

    /**
     * 不支持的国家
     */
    DoesNotSupportCountryException(90019, "不支持的国家"),

    /**
     * avalara收税州前置状态错误
     */
    AvalaraTaxProvinceEnableStateException(90020, "收税州前置状态错误"),

    /**
     * avalara收税州未找到
     */
    AvalaraTaxProvinceNotFoundException(90021, "收税州未找到"),

    /**
     * 参数错误
     */
    ParameterErrorException(90022, "参数错误"),

    /**
     * 订单上报状态已修改
     */
    StorePurchaseStateSetException(90023, "订单上报状态已修改"),

    /**
     * 新增搜索默认推荐位异常
     */
    SearchRecommendItemInsertException(90024, "新增搜索默认推荐位异常"),

    /**
     * 删除搜索默认推荐位异常
     */
    SearchRecommendItemDeleteException(90025, "删除搜索默认推荐位异常"),

    /**
     * 已删除的默认推荐位排序异常
     */
    SearchRecommendSortItemDeletedException(90026, "已删除的默认推荐位排序异常"),

    /**
     * 场景标签未找到异常
     */
    SceneryTagMainNotFoundException(*********, "场景标签未找到异常"),

    /**
     * 活动页面 本地配置地区缺失
     */
    ActivityLocalesConfigDeficiencyException(*********, "创建活动页面缺少本地配置地区"),

    /**
     * 活动配置urlKey已经存在
     */
    ActivityAlreadyExistedException(105009003, "活动配置[urlKey]或[埋点名]已经存在"),

    /**
     * 活动页面不存在
     */
    ActivityPageNotFoundException(105009004, "活动页面不存在"),

    /**
     * 活动页面 本地配置地区缺失
     */
    ActivityLocalesConfigNotFoundException(105009005, "活动本地配置地区缺失"),

    /**
     * 活动配置错误
     */
    ActivityStatusException(105009006, "活动配置状态异常"),

    /**
     * 活动配置时间校验异常
     */
    ActivityTimeException(105009007, "活动配置时间校验异常"),

    /**
     * 非法的key类型或语言！
     */
    IllegalKeyOrLanguageException(105009008, "非法的key类型或语言"),

    /**
     * 未查询到对应条款协议
     */
    AgreementNotFoundException(105009009, "未查询到对应条款协议"),

    ;

    private final Integer code;

    private final String msg;

    MetaErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
