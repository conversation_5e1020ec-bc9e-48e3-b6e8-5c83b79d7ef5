package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.dao.NavigationBarCategoryCommodityDao;
import com.insta360.store.business.meta.dto.NavigationBarCategoryCommodityDTO;
import com.insta360.store.business.meta.model.NavigationBarCategoryCommodity;
import com.insta360.store.business.meta.service.NavigationBarCategoryCommodityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-05-20
 * @Description:
 */
@Service
public class NavigationBarCategoryCommodityServiceImpl extends BaseServiceImpl<NavigationBarCategoryCommodityDao, NavigationBarCategoryCommodity> implements NavigationBarCategoryCommodityService {

    @Override
    public List<NavigationBarCategoryCommodity> listByCommodityIds(List<Integer> commodityIds) {
        if (CollectionUtils.isEmpty(commodityIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<NavigationBarCategoryCommodity> qw = new QueryWrapper<>();
        qw.in("commodity_id", commodityIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<NavigationBarCategoryCommodity> listBySubsetInsideIds(List<Integer> subsetInsideIds) {
        if (CollectionUtils.isEmpty(subsetInsideIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<NavigationBarCategoryCommodity> qw = new QueryWrapper<>();
        qw.in("subset_inside_id", subsetInsideIds);
        qw.orderByDesc("order_index");
        return baseMapper.selectList(qw);
    }

    @Override
    public void deleteBySubsetInsideIds(List<Integer> subsetInsideIds) {
        List<NavigationBarCategoryCommodity> categoryCommodities = listBySubsetInsideIds(subsetInsideIds);
        if (CollectionUtils.isEmpty(categoryCommodities)) {
            return;
        }
        List<Integer> categoryCommoditiesIds = categoryCommodities.stream().map(NavigationBarCategoryCommodity::getId).collect(Collectors.toList());
        removeByIds(categoryCommoditiesIds);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void createCategoryCommodity(List<NavigationBarCategoryCommodityDTO> categoryCommodityParam) {
        List<NavigationBarCategoryCommodity> commodityList = categoryCommodityParam.stream().map(categoryCommodityDTO -> {
            NavigationBarCategoryCommodity categoryCommodity = categoryCommodityDTO.getPojoObject();
            if (categoryCommodity.getId() != null) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }
            categoryCommodity.setCreateTime(LocalDateTime.now());
            categoryCommodity.setUpdateTime(LocalDateTime.now());
            return categoryCommodity;
        }).collect(Collectors.toList());
        baseMapper.createCategoryCommodity(commodityList);
    }

    @Override
    public void updateCategoryCommodity(NavigationBarCategoryCommodity categoryCommodity) {
        categoryCommodity.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(categoryCommodity);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void enableCategoryCommodity(List<Integer> categoryCommodityIds) {
        categoryCommodityIds.forEach(categoryCommodityId -> {
            NavigationBarCategoryCommodity categoryCommodity = getById(categoryCommodityId);
            if (categoryCommodity == null) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }

            if (categoryCommodity.getEnabled()) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }
        });
        baseMapper.enableCategoryCommodity(categoryCommodityIds, LocalDateTime.now());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void disableCategoryCommodity(List<Integer> categoryCommodityIds) {
        categoryCommodityIds.forEach(categoryCommodityId -> {
            NavigationBarCategoryCommodity categoryCommodity = getById(categoryCommodityId);
            if (categoryCommodity == null) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }

            if (!categoryCommodity.getEnabled()) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }
        });
        baseMapper.disableCategoryCommodity(categoryCommodityIds, LocalDateTime.now());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateCategoryCommodityIndex(List<NavigationBarCategoryCommodityDTO> categoryCommodityParam) {
        List<NavigationBarCategoryCommodity> categoryCommodityList = categoryCommodityParam.stream().map(categoryCommodityDTO -> {
            if (categoryCommodityDTO.getId() == null || categoryCommodityDTO.getOrderIndex() == null) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }

            NavigationBarCategoryCommodity categoryCommodity = getById(categoryCommodityDTO.getId());
            if (categoryCommodity == null) {
                throw new InstaException(CommonErrorCode.InvalidParameterException);
            }
            categoryCommodity.setOrderIndex(categoryCommodityDTO.getOrderIndex());
            categoryCommodity.setUpdateTime(LocalDateTime.now());
            return categoryCommodity;
        }).collect(Collectors.toList());
        baseMapper.updateCategoryCommodityIndex(categoryCommodityList);
    }

    @Override
    public void enableNewCategoryCommodity(NavigationBarCategoryCommodity categoryCommodity) {
        categoryCommodity.setNewTag(true);
        categoryCommodity.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(categoryCommodity);
    }

    @Override
    public void disableNewCategoryCommodity(NavigationBarCategoryCommodity categoryCommodity) {
        categoryCommodity.setNewTag(false);
        categoryCommodity.setUpdateTime(LocalDateTime.now());
        baseMapper.updateById(categoryCommodity);
    }

    @Override
    public void deleteCategoryCommodity(Integer categoryCommodityId) {
        this.removeById(categoryCommodityId);
    }
}
