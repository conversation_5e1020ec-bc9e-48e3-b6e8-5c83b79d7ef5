package com.insta360.store.business.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-10-10
 * @Description: 邮件订阅失败补偿记录表
 */
public class EmailSubscribeFailRecord extends BaseModel<EmailSubscribeFailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 邮件订阅ID
     */
    private Integer subscribeId;

    /**
     * 补偿状态 0：失败 1：成功
     */
    private Integer compensateState;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSubscribeId() {
        return subscribeId;
    }

    public void setSubscribeId(Integer subscribeId) {
        this.subscribeId = subscribeId;
    }

    public Integer getCompensateState() {
        return compensateState;
    }

    public void setCompensateState(Integer compensateState) {
        this.compensateState = compensateState;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "EmailSubscribeFailCompensateRecord{" +
        "id=" + id +
        ", subscribeId=" + subscribeId +
        ", compensateState=" + compensateState +
        ", createTime=" + createTime +
        ", modifyTime=" + modifyTime +
        "}";
    }
}