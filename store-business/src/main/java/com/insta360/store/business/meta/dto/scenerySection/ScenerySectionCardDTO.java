package com.insta360.store.business.meta.dto.scenerySection;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 场景专区卡片配置
 * @author: py
 * @create: 2025-03-12 18:33
 */
public class ScenerySectionCardDTO implements Serializable {

    /**
     * 卡片类型
     */
    @NotNull
    @NotBlank
    private String cardType;

    /**
     * 场景专区ID
     */
    @NotNull
    private Integer scenerySectionId;

    /**
     * 小卡片
     */
    private List<SceneryCardMainDTO> smallCardList;

    /**
     * 大卡片
     */
    private SceneryCardMainDTO largeCard;

    public List<SceneryCardMainDTO> getSmallCardList() {
        return smallCardList;
    }

    public void setSmallCardList(List<SceneryCardMainDTO> smallCardList) {
        this.smallCardList = smallCardList;
    }

    public SceneryCardMainDTO getLargeCard() {
        return largeCard;
    }

    public void setLargeCard(SceneryCardMainDTO largeCard) {
        this.largeCard = largeCard;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public Integer getScenerySectionId() {
        return scenerySectionId;
    }

    public void setScenerySectionId(Integer scenerySectionId) {
        this.scenerySectionId = scenerySectionId;
    }

    @Override
    public String toString() {
        return "ScenerySectionCardDTO{" +
                "cardType='" + cardType + '\'' +
                ", scenerySectionId=" + scenerySectionId +
                ", smallCardList=" + smallCardList +
                ", largeCard=" + largeCard +
                '}';
    }
}
