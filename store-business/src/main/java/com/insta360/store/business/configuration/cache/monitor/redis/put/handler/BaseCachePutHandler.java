package com.insta360.store.business.configuration.cache.monitor.redis.put.handler;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.cacheSync.dto.CacheDataChangeEvent;
import com.insta360.compass.libs.cacheSync.enums.CacheOrigin;
import com.insta360.compass.libs.cacheSync.enums.ClearType;
import com.insta360.compass.libs.cacheSync.mq.CacheMessageService;
import com.insta360.store.business.commodity.service.CommodityRecommendationService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.ProductRecommendationService;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.StoreCacheDataChangeEventBO;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductAdapterTypeService;
import com.insta360.store.business.product.service.ProductService;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2023/08/21
 * @Description:
 */
public abstract class BaseCachePutHandler implements CachePutService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseCachePutHandler.class);

    /**
     * 任务数暂定给3个
     */
    public static final Integer TASK_NUMBER = 3;

    /**
     * 是否需要重投递
     */
    public Boolean isRetryable = Boolean.FALSE;

    @Autowired
    public ProductService productService;

    @Autowired
    public CommodityService commodityService;

    @Autowired
    CacheMessageService cacheMessageService;

    @Autowired
    public ThreadPoolExecutor cachePutThreadPool;

    @Autowired
    public ProductRecommendationService productRecommendationService;

    @Autowired
    public CommodityRecommendationService commodityRecommendationService;

    @Autowired
    public ProductAdapterTypeService productAdapterTypeService;

    @Override
    public void cachePut(CachePutKeyParameterBO cachePutKeyParameter) throws Exception {
        LOGGER.info("处理触发缓存更新流程开始。类型:{}, 关键参数:{}", this.getCachePutType(), cachePutKeyParameter);
        // 重置重试标识
        this.resetRetryable();

        StoreCacheDataChangeEventBO keyPointreturn;
        try {
            // cache put
            keyPointreturn = this.doCachePut(cachePutKeyParameter);
        } catch (Exception e) {
            if (e instanceof InstaException) {
                LOGGER.error(String.format("缓存更新中断。缓存类型:{%s}，原因:{%s}，param:{%s}", this.getCachePutType(), e.getMessage(), cachePutKeyParameter), e);
                FeiShuMessageUtil.storeGeneralMessage(String.format("缓存更新中断。缓存类型:{%s}，原因:{%s}，param:{%s}", this.getCachePutType(),
                        e.getMessage(), cachePutKeyParameter), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            } else {
                LOGGER.error(String.format("缓存更新其它异常。缓存类型:{%s}，原因:{%s}，param:{%s}", this.getCachePutType(), e.getMessage(), cachePutKeyParameter), e);
                FeiShuMessageUtil.storeGeneralMessage(String.format("缓存更新其它异常。缓存类型:{%s}，原因:{%s}，param:{%s}", this.getCachePutType(),
                        e.getMessage(), cachePutKeyParameter), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
            return;
        }

        // 重投递监控
        if (Boolean.TRUE.equals(this.getretryable())) {
            LOGGER.error(String.format("缓存更新RPC调用失败。缓存类型:{%s}，原因:{Read time out}，param:{%s}", this.getCachePutType(), cachePutKeyParameter));
            FeiShuMessageUtil.storeGeneralMessage(String.format("缓存更新RPC调用失败。缓存类型:{%s}，原因:{Read time out}，param:{%s}",
                    this.getCachePutType(), cachePutKeyParameter), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new SocketTimeoutException();
        }

        // 是否推送 webSocket
        List<String> notifyCacheTypes = this.listWebSocketNotifyCacheType();
        if (!this.isWebSocketNotify() || notifyCacheTypes == null) {
            return;
        }

        // 构造前端缓存更新删除参数
        List<StoreCacheDataChangeEventBO> storeCacheDataChangeEvents = new ArrayList<>(notifyCacheTypes.size());
        for (String notifyCacheType : notifyCacheTypes) {
            StoreCacheDataChangeEventBO storeCacheDataChangeEvent = new StoreCacheDataChangeEventBO();
            // 产品页 和 评论 则推送产品id
            if (CacheableType.PRODUCT_INFO.equals(notifyCacheType) || CacheableType.REVIEW_INFO.equals(notifyCacheType)) {
                storeCacheDataChangeEvent.setProductEvents(keyPointreturn.getProductEvents());
            }
            storeCacheDataChangeEvent.setCacheType(notifyCacheType);
            storeCacheDataChangeEvent.setCountries(keyPointreturn.getCountries());
            storeCacheDataChangeEvent.setLanguages(keyPointreturn.getLanguages());
            storeCacheDataChangeEvents.add(storeCacheDataChangeEvent);
        }

        // websocket
        this.webSocketNotify(this.getCachePutType(), JSON.toJSONString(storeCacheDataChangeEvents));

        LOGGER.info("处理触发缓存更新流程结束。类型:{}, 关键参数:{}", this.getCachePutType(), cachePutKeyParameter);
    }

    /**
     * 缓存更新
     *
     * @param cachePutKeyParameter
     * @return
     * @throws Exception 线程中断exception
     */
    protected abstract StoreCacheDataChangeEventBO doCachePut(CachePutKeyParameterBO cachePutKeyParameter) throws Exception;

    /**
     * 获取缓存更新类型
     *
     * @return
     */
    protected abstract String getCachePutType();

    @Override
    public CachePutKeyParameterBO cacheParamParse(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        LOGGER.info("触发缓存更新参数解析。参数列表：{}", args);
        try {
            return (CachePutKeyParameterBO) args[0];
        } catch (Exception e) {
            LOGGER.error("参数解析失败。异常：{}", e.getMessage());
            FeiShuMessageUtil.storeGeneralMessage(String.format("缓存更新参数解析失败，没有适配关键参数。请及时更新缓存！缓存类型:{%s}",
                    this.getCachePutType()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
    }

    /**
     * 是否需要通知客户端更新缓存（websocket）
     *
     * @return
     */
    @Override
    public Boolean isWebSocketNotify() {
        return Boolean.FALSE;
    }

    @Override
    public List<String> listWebSocketNotifyCacheType() {
        return null;
    }

    @Override
    public void webSocketNotify(String cacheType, String extraMessage) {
        LOGGER.info("触发缓存更新 web scoket 通知。类型:{}, 额外信息:{}", cacheType, extraMessage);
        CacheDataChangeEvent cacheDataChangeEvent = new CacheDataChangeEvent();
        cacheDataChangeEvent.setCacheKey(cacheType);
        cacheDataChangeEvent.setExtraMessage(extraMessage);
        cacheDataChangeEvent.setClearType(ClearType.single);
        cacheDataChangeEvent.setCacheOrigin(CacheOrigin.store);
        cacheDataChangeEvent.setTimeMillis(System.currentTimeMillis());
        cacheMessageService.sendMessage(cacheDataChangeEvent);
    }

    @Override
    public Integer getTaskNumber() {
        return TASK_NUMBER;
    }

    /**
     * 构造 CountDownLatch
     *
     * @return
     */
    protected CountDownLatch getCountDownLatch() {
        return new CountDownLatch(this.getTaskNumber());
    }

    /**
     * 产品信息转换
     *
     * @param products
     * @return
     */
    protected List<StoreCacheDataChangeEventBO.StoreCacheProductEventBO> parseProductEvent(Collection<Product> products) {
        return products
                .stream()
                .map(product -> new StoreCacheDataChangeEventBO.StoreCacheProductEventBO(product.getId(), product.getUrlKey()))
                .collect(Collectors.toList());
    }

    /**
     * 修改重试标识
     *
     * @param retryable
     */
    protected synchronized void setRetryable(Boolean retryable) {
        this.isRetryable = retryable;
    }

    /**
     * 获取重试标识
     *
     * @return
     */
    protected Boolean getretryable() {
        return this.isRetryable;
    }

    /**
     * 重置重试标识
     */
    protected void resetRetryable() {
        this.isRetryable = Boolean.FALSE;
    }
}
