package com.insta360.store.business.rma.service.impl.helper;

import com.google.common.collect.Lists;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.service.impl.fatory.InsuranceFactory;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.order.enums.OrderItemState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderDeliveryUniqueCodeService;
import com.insta360.store.business.order.service.OrderItemBindService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.rma.bo.RefundApplyCheckBo;
import com.insta360.store.business.rma.configuration.RefundCommonConfigure;
import com.insta360.store.business.rma.enums.RmaApplyFailReason;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 售后业务检查处理类
 * @Date 2022/3/8
 */
@Component
public class RmaCheckHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(RmaCheckHelper.class);

    private final List<OrderState> orderStateList = Lists.newArrayList(OrderState.payed, OrderState.on_delivery, OrderState.part_delivery, OrderState.prepared, OrderState.success);

    @Autowired
    private OrderItemService orderItemService;

    @Autowired
    private OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderItemBindService orderItemBindService;

    @Autowired
    OrderDeliveryUniqueCodeService orderDeliveryUniqueCodeService;

    @Autowired
    RefundCommonConfigure refundCommonConfigure;

    @Autowired
    InsuranceFactory insuranceFactory;

    /**
     * 是否可申请售后前置检查
     *
     * @param order
     * @param orderItem
     * @return
     */
    public RefundApplyCheckBo orderReturnApplyCheck(Order order, OrderItem orderItem) {
        RefundApplyCheckBo refundApplyCheckBo = null;
        for (OrderReturnApplyCheckStrategy strategy : orderReturnApplyStrategyQueue) {
            //规则策略检查
            refundApplyCheckBo = strategy.applyCheck(order, orderItem);
            if (Objects.isNull(refundApplyCheckBo) || !refundApplyCheckBo.getAllowApply()) {
                return refundApplyCheckBo;
            }
        }
        return refundApplyCheckBo;
    }

    /**
     * 售后退款申请检查策略
     */
    private interface OrderReturnApplyCheckStrategy {

        /**
         * 申请检查
         *
         * @param order
         * @param orderItem
         * @return
         */
        RefundApplyCheckBo applyCheck(Order order, OrderItem orderItem);
    }

    /**
     * 订单检查是否存在
     */
    private OrderReturnApplyCheckStrategy orderCheckStrategy = (order, orderItem) -> Objects.nonNull(order) && Objects.nonNull(orderItem) ? new RefundApplyCheckBo(true, null) : new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_DEFAULT);

    /**
     * 订单状态检查
     * 规则：非（已支付、已配货、部分发货、已发货、已完成）订单状态，不允许申请售后
     */
    private OrderReturnApplyCheckStrategy orderStateCheckStrategy = (order, orderItem) -> orderStateList.contains(order.orderState()) ? new RefundApplyCheckBo(true, null) : new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_DEFAULT);

    /**
     * 订单商品状态检查
     */
    private OrderReturnApplyCheckStrategy orderItemStateCheckStrategy = (order, orderItem) -> OrderItemState.normal.equals(orderItem.orderItemState()) ? new RefundApplyCheckBo(true, null) : new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_DEFAULT);

    /**
     * 赠品检查
     * 规则：赠品不允许申请售后
     */
    private OrderReturnApplyCheckStrategy giveawayCheckStrategy = (order, orderItem) -> Objects.nonNull(orderItem.getIsGift()) && !orderItem.getIsGift() ? new RefundApplyCheckBo(true, null) : new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_DEFAULT);
    ;

    /**
     * 绑定服务检查
     * 规则：
     * 1、相机与绑定服务一起购买的场景，绑定服务已激活则不允许申请售后
     * 2、单独购买的增值服务，不允许申请售后
     */
    private OrderReturnApplyCheckStrategy bindServiceCheckStrategy = (order, orderItem) -> {
        if (Product.INSURANCE_SERVICE_PRODUCT.contains(orderItem.getProduct())) {
            List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
            boolean isBuyCamera = orderItems.stream().filter(item -> !Product.INSURANCE_SERVICE_PRODUCT.contains(item.getProduct())).findFirst().isPresent();
            if (!isBuyCamera) {
                return new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_BIND_SERVICE);
            } else {
                //部分发货、已发货、已完成
                if (Lists.newArrayList(OrderState.on_delivery, OrderState.success, OrderState.part_delivery).contains(order.orderState())) {
                    OrderItemBind itemBind = orderItemBindService.getByOrderItem(order.getId(), orderItem.getId());
                    if (Objects.isNull(itemBind) || itemBind.getOrderItemId().equals(itemBind.getBelongToOrderItemId())) {
                        return new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_BIND_SERVICE);
                    } else {
                        Integer belongCameraItemId = itemBind.getBelongToOrderItemId();
                        List<OrderDeliveryUniqueCode> orderDeliveryUniqueList = orderDeliveryUniqueCodeService.listByItemId(belongCameraItemId);
                        if (CollectionUtils.isEmpty(orderDeliveryUniqueList)) {
                            return new RefundApplyCheckBo(true, null);
                        }

                        // 获取相机主机序列号
                        List<String> cameraUniqueCodeList = orderDeliveryUniqueList.stream().map(OrderDeliveryUniqueCode::getUniqueCode).collect(Collectors.toList());

                        ServiceType serviceType = ServiceType.parseByProduct(orderItem.getProduct());
                        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(serviceType.name());
                        Boolean existService = insuranceHandler.checkInsuranceBySerials(cameraUniqueCodeList);
                        if (existService){
                            return new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_DEFAULT);
                        }
                    }
                }
            }
        }

        return new RefundApplyCheckBo(true, null);
    };


    /**
     * 售后有效期检查
     * 规则：订单状态为'已完成'且已确认收货情况下，售后申请时间超过 确认收货时间（+45天）不允许申请
     */
    private OrderReturnApplyCheckStrategy afterSaleTimeCheckStrategy = (order, orderItem) -> {
        if (OrderState.success.equals(order.orderState())) {
            OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
            if (Objects.isNull(orderDelivery)
                    || Objects.isNull(orderDelivery.getReceiveTime())
                    || LocalDateTime.now().isAfter(orderDelivery.getReceiveTime().plusDays(refundCommonConfigure.getDay()))
            ) {
                return new RefundApplyCheckBo(false, RmaApplyFailReason.APPLY_FAIL_ORDER_DATE_OVERDUE);
            }
        }
        return new RefundApplyCheckBo(true, null);
    };

    /**
     * 订单售后申请检查策略队列
     */
    private List<OrderReturnApplyCheckStrategy> orderReturnApplyStrategyQueue = Lists.newArrayList(
            orderCheckStrategy,
            orderStateCheckStrategy,
            afterSaleTimeCheckStrategy,
            orderItemStateCheckStrategy,
            giveawayCheckStrategy,
            bindServiceCheckStrategy
    );

}
