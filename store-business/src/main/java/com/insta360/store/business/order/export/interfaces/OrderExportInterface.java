package com.insta360.store.business.order.export.interfaces;

import com.insta360.store.business.order.dto.OrderExportDTO;
import com.insta360.store.business.outgoing.rpc.user.dto.OaUser;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/24
 */
public interface OrderExportInterface extends OrderWriteExcelService, OrderExcelUploadService {

    /**
     * 导出
     *
     * @param oaUser           用户信息
     * @param orderExportParam 订单导出参数
     */
    void export(OaUser oaUser, OrderExportDTO orderExportParam);

    /**
     * 参数检查
     *
     * @param orderExportParam 订单导出参数
     */
    void paramCheck(OrderExportDTO orderExportParam);

    /**
     * 导出错误处理程序
     *
     * @param e                e
     * @param orderExportParam 订单导出参数
     * @param jobEmail         邮箱
     */
    void exportErrorHandler(Exception e, String jobEmail, OrderExportDTO orderExportParam);
}
