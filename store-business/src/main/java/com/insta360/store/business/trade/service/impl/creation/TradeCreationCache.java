package com.insta360.store.business.trade.service.impl.creation;

import com.insta360.store.business.discount.model.Coupon;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.meta.bo.TradeCode;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.reseller.dto.condition.ResellerCode;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/4/13
 * @Description:
 */
public class TradeCreationCache {

    /**
     * 下单信息
     */
    private OrderSheet orderSheet;

    /**
     * 优惠券
     */
    private Coupon coupon;

    /**
     * 代金券
     */
    private GiftCard giftCard;

    /**
     * 分销码
     */
    private ResellerCode resellerCode;

    /**
     * 假订单标识
     */
    private Boolean fake;

    /**
     * 交易券集合
     */
    private List<TradeCode> usedTradeCodes;

    public TradeCreationCache(OrderSheet orderSheet) {
        this.orderSheet = orderSheet;
    }

    public OrderSheet getOrderSheet() {
        return orderSheet;
    }

    public Coupon getCoupon() {
        return coupon;
    }

    public void setCoupon(Coupon coupon) {
        this.coupon = coupon;
    }

    public GiftCard getGiftCard() {
        return giftCard;
    }

    public void setGiftCard(GiftCard giftCard) {
        this.giftCard = giftCard;
    }

    public ResellerCode getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(ResellerCode resellerCode) {
        this.resellerCode = resellerCode;
    }

    public Boolean getFake() {
        return fake;
    }

    public void setFake(Boolean fake) {
        this.fake = fake;
    }

    public List<TradeCode> getUsedTradeCodes() {
        return usedTradeCodes;
    }

    public void setUsedTradeCodes(List<TradeCode> usedTradeCodes) {
        this.usedTradeCodes = usedTradeCodes;
    }
}
