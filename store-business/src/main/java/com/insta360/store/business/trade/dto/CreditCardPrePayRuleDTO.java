package com.insta360.store.business.trade.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.trade.model.CreditCardPrePayRule;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2025/06/04
 * @Description:
 */
public class CreditCardPrePayRuleDTO implements Serializable {

    private String id;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 配置地区
     */
    private List<String> countries;

    /**
     * 钱海权重
     */
    private Integer oceanWeight;

    /**
     * checkout渠道权重
     */
    private Integer checkoutWeight;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * ids
     */
    private List<String> ids;

    public CreditCardPrePayRule getPojoObject() {
        CreditCardPrePayRule creditCardPrePayRule = new CreditCardPrePayRule();
        BeanUtil.copyProperties(this, creditCardPrePayRule);
        return creditCardPrePayRule;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public List<String> getCountries() {
        return countries;
    }

    public void setCountries(List<String> countries) {
        this.countries = countries;
    }

    public Integer getOceanWeight() {
        return oceanWeight;
    }

    public void setOceanWeight(Integer oceanWeight) {
        this.oceanWeight = oceanWeight;
    }

    public Integer getCheckoutWeight() {
        return checkoutWeight;
    }

    public void setCheckoutWeight(Integer checkoutWeight) {
        this.checkoutWeight = checkoutWeight;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    @Override
    public String toString() {
        return "CreditCardPrePayRuleDTO{" +
                "id=" + id +
                ", ruleName='" + ruleName + '\'' +
                ", countries=" + countries +
                ", oceanWeight=" + oceanWeight +
                ", checkoutWeight=" + checkoutWeight +
                ", enabled=" + enabled +
                ", ids=" + ids +
                '}';
    }
}
