package com.insta360.store.business.outgoing.mq.trade.dto;

import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.store.business.trade.enums.UserCartEmailEnum;
import com.insta360.store.business.user.model.StoreAccount;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/5/15 17:38
 * @Description:
 * @Version 1.0
 */
public class UserCartBusinessMessageDTO implements Serializable {

    /**
     * uuid
     */
    private String uuid;

    /**
     * 用户ID
     */
    private StoreAccount storeAccount;

    /**
     * 邮件类型
     */
    private UserCartEmailEnum userCartEmailEnum;

    public UserCartBusinessMessageDTO() {
    }

    public UserCartBusinessMessageDTO(StoreAccount storeAccount, UserCartEmailEnum userCartEmailEnum) {
        this.uuid = UUIDUtils.generateUuid();
        this.storeAccount = storeAccount;
        this.userCartEmailEnum = userCartEmailEnum;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public StoreAccount getStoreAccount() {
        return storeAccount;
    }

    public void setStoreAccount(StoreAccount storeAccount) {
        this.storeAccount = storeAccount;
    }

    public UserCartEmailEnum getUserCartEmailEnum() {
        return userCartEmailEnum;
    }

    public void setUserCartEmailEnum(UserCartEmailEnum userCartEmailEnum) {
        this.userCartEmailEnum = userCartEmailEnum;
    }

    @Override
    public String toString() {
        return "UserCartBusinessMessageDTO{" +
                "uuid='" + uuid + '\'' +
                ", storeAccount=" + storeAccount +
                ", userCartEmailEnum=" + userCartEmailEnum +
                '}';
    }
}
