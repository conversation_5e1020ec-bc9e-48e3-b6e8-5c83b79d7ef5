package com.insta360.store.business.insurance.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.insurance.model.InsuranceServiceCommodityRule;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-06-22
 * @Description:
 */
public interface ServiceCommodityRuleService extends BaseService<InsuranceServiceCommodityRule> {

    /**
     * 根据套餐绑定id查询
     *
     * @param commodityBindId
     * @return
     */
    List<InsuranceServiceCommodityRule> getByServiceCommodityId(Integer commodityBindId);

    /**
     * 根据套餐绑定id查询
     *
     * @param commodityBindId
     * @param serialRule      串行规则
     * @return
     */
    InsuranceServiceCommodityRule getByCommodityIdSerialRule(Integer commodityBindId, String serialRule);
}
