package com.insta360.store.business.order.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/18
 * @Description:
 */
public interface OrderPaymentService extends BaseService<OrderPayment> {

    /**
     * 获取订单的支付信息
     *
     * @param orderId
     * @return
     */
    OrderPayment getByOrder(Integer orderId);

    /**
     * 根据订单号获取订单的支付信息
     *
     * @param orderNumber
     * @return
     */
    OrderPayment getByOrderNumber(String orderNumber);

    /**
     * 恢复订单为未支付状态（取消、退款、退货时使用）
     *
     * @param orderId
     * @return
     */
    void recoverUnpaid(Integer orderId);

    /**
     * 订单支付失败
     *
     * @param orderNumber
     */
    void setPaymentFailure(String orderNumber);

    /**
     * 把订单设置成支付成功
     *
     * @param orderNumber
     * @param paymentChannel
     * @param channelPaymentId
     * @param securityType
     */
    void setPaymentSuccess(String orderNumber, PaymentChannel paymentChannel, String channelPaymentId, PaymentTradeSecurityType securityType);

    /**
     * 把订单设置支付处理中（Pending）
     *
     * @param orderNumber
     * @param paymentChannel
     * @param channelPaymentId
     */
    void setPaymentPending(String orderNumber, PaymentChannel paymentChannel, String channelPaymentId);

    /**
     * 把订单设置成拒付（ChargeBack）
     *
     * @param orderNumber
     * @param paymentChannel
     */
    void setPaymentChargeBack(String orderNumber, PaymentChannel paymentChannel);

    /**
     * 根据订单id查询
     *
     * @param orderIds
     * @return
     */
    List<OrderPayment> listByOrderIds(List<Integer> orderIds);

    /**
     * 设置为安全的交易
     *
     * @param orderId
     */
    void setSecurityForPayment(Integer orderId);

    /**
     * 通过订单id和已支付状态查询
     *
     * @param orderIds
     * @return
     */
    List<OrderPayment> listByOrderIdsAndPayStatus(List<Integer> orderIds);
}