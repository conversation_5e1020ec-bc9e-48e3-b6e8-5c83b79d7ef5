package com.insta360.store.business.trade.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.bo.CommoditySplitBO;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityMetaService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.impl.helper.CommoditySplitHelper;
import com.insta360.store.business.discount.utils.MathUtil;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.model.MetaShippingCost;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.MetaShippingCostService;
import com.insta360.store.business.meta.service.PriceService;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderShippingFeeRecord;
import com.insta360.store.business.payment.service.impl.helper.PaymentPriceCounter;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductHelper;
import com.insta360.store.business.trade.bo.BuyCommodityInfoBO;
import com.insta360.store.business.trade.bo.GroupCommodityBO;
import com.insta360.store.business.trade.bo.ShippingFeeCountBO;
import com.insta360.store.business.trade.model.ShippingFeeConfig;
import com.insta360.store.business.trade.service.ShippingFeeConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/25
 * @Description:
 */
@Component
public class OrderShippingFeeCounter {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderShippingFeeCounter.class);

    /**
     * 默认重量 单位kg
     */
    private static final String DEFAULT_WEIGHT = "0.200";
    /**
     * 默认最大总重量 单位kg
     */
    private static final String DEFAULT_MAX_TOTAL_WEIGHT = "20.000";

    @Autowired
    PriceService priceService;

    @Autowired
    ProductHelper productHelper;

    @Autowired
    CommoditySplitHelper commoditySplitHelper;

    @Autowired
    CommodityMetaService commodityMetaService;

    @Autowired
    PaymentPriceCounter orderItemsPriceCounter;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    MetaShippingCostService metaShippingCostService;

    @Autowired
    ShippingFeeConfigService shippingFeeConfigService;

    @Autowired
    ProductService productService;


    /**
     * 订单运费计算
     *
     * @param orderItems
     * @param country
     * @return
     */
    public Price orderFreightCalculate(List<OrderItem> orderItems, InstaCountry country) {
        Price itemsPrice = orderItemsPriceCounter.count(orderItems, null);
        return countShippingFee(orderItems, itemsPrice, country, null);
    }

    /**
     * 订单运费计算 并 解析运费毛重信息记录到orderCreation
     *
     * @param orderItems    订单项
     * @param country       国家
     * @param orderCreation 订单创建
     * @return {@link Price}
     */
    public Price orderFreightCalculate(List<OrderItem> orderItems, InstaCountry country, OrderCreation orderCreation) {
        Price itemsPrice = orderItemsPriceCounter.count(orderItems, null);
        return countShippingFee(orderItems, itemsPrice, country, orderCreation);
    }

    /**
     * 订单运费计算(订单创建前调用)
     *
     * @param shippingFeeCountBo
     * @return {@link Price}
     */
    public Price orderFreightCalculate(ShippingFeeCountBO shippingFeeCountBo) {


        MetaShippingCost shippingCostDefine = null;
        List<CommoditySplitBO> splitCommodity = null;
        Map<Integer, CommodityMeta> commodityMetaMap = null;
        try {
            // 过滤虚拟商品，获取实体商品,若实体商品为空则说明全是虚拟商品
            List<BuyCommodityInfoBO> buyPhysicalCommodityInfoBoList = shippingFeeCountBo.getBuyCommodityInfoBoList()
                    .stream()
                    .filter(buyCommodityInfoBo -> !productHelper.checkVirtualGoods(buyCommodityInfoBo.getProductId(), buyCommodityInfoBo.getCommodityId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(buyPhysicalCommodityInfoBoList)) {
                return new Price(shippingFeeCountBo.getCurrency(), 0f);
            }

            // 包含主机即免邮
            List<Integer> products = buyPhysicalCommodityInfoBoList.stream().map(BuyCommodityInfoBO::getProductId).collect(Collectors.toList());
            boolean containsCamera = productService.getProducts(products).stream().anyMatch(Product::getIsCamera);
            if (containsCamera) {
                return new Price(shippingFeeCountBo.getCurrency(), 0f);
            }

            // 获取包邮门槛
            shippingCostDefine = metaShippingCostService.getByCountry(shippingFeeCountBo.getCountry());

            // 判断是否达到包邮门槛
            if (freeShippingThreshold(shippingFeeCountBo.getTotalPrice(), shippingCostDefine)) {
                return new Price(shippingFeeCountBo.getCurrency(), 0f);
            }

            List<GroupCommodityBO> groupCommodityBoList = buyPhysicalCommodityInfoBoList.stream().map(buyCommodityInfoBO -> {
                GroupCommodityBO groupCommodityBO = new GroupCommodityBO();
                BeanUtils.copyProperties(buyCommodityInfoBO, groupCommodityBO);
                return groupCommodityBO;
            }).collect(Collectors.toList());

            // 套餐拆分并获取所有套餐ID及重量信息 子项不存在虚拟商品
            splitCommodity = commoditySplitHelper.listSplitCommodity(groupCommodityBoList);
            List<Integer> commodityIdList = splitCommodity.stream().map(CommoditySplitBO::getCommodityId).collect(Collectors.toList());
            List<CommodityMeta> commodityMetas = commodityMetaService.listCommodityMetaByCommodityIds(commodityIdList);

            // key:commodityId 套餐ID
            // value:commodityMeta 毛重信息
            commodityMetaMap = commodityMetas.stream().collect(Collectors.toMap(CommodityMeta::getCommodityId, commodityMeta -> commodityMeta));
            BigDecimal totalWeight = this.calculateTotalWeight(splitCommodity, shippingFeeCountBo.getCountry(), commodityMetaMap);

            // 如果算出来的总重量是0,则运费是0
            if (BigDecimal.ZERO.compareTo(totalWeight) == 0) {
                LOGGER.error("Gross order weight is zero,splitCommodity:{},country:{},commodityMetaMap:{}", splitCommodity, shippingFeeCountBo.getCountry(), commodityMetaMap);
                String message = String.format("订单总毛重为0,套餐信息:%s", JSON.toJSONString(splitCommodity));
                List<Integer> productIds = buyPhysicalCommodityInfoBoList.stream().map(BuyCommodityInfoBO::getProductId).collect(Collectors.toList());

                // 含有免邮产品 571 Insta360 Connect
                // TODO 免邮产品逻辑需要 产品跟进，此处临时更改
                boolean haveFreeProduct = productIds.stream().anyMatch(id -> id.equals(Product.FREE_PRODUCT));

                // 购买的产品数量
                long count = productIds.size();

                // 当毛重为0 且 购买的产品仅是 571
                if (!haveFreeProduct || count != 1) {
                    FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.WXQ, FeiShuAtUser.LCY);
                }

                return new Price(shippingFeeCountBo.getCurrency(), 0f);
            }

            // 根据重量获取运费
            ShippingFeeConfig shippingFeeConfig = shippingFeeConfigService.getShippingFeeByWeight(totalWeight, shippingFeeCountBo.getCountry());
            if (shippingFeeConfig == null) {
                LOGGER.error("failed to get shipping by country and weight,totalWeight:{},country:{}", totalWeight, shippingFeeCountBo.getCountry());
                String message = String.format("无法获取到运费，请检查按重计费表,毛重:%s,国家:%s", totalWeight, shippingFeeCountBo.getCountry());
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.WXQ, FeiShuAtUser.LCY);
                return new Price(shippingFeeCountBo.getCurrency(), 0f);
            }

            return new Price(shippingFeeCountBo.getCurrency(), shippingFeeConfig.getShipping().floatValue());
        } catch (Exception e) {
            LOGGER.error("运费计算发生未知异常:" + JSON.toJSONString(shippingFeeCountBo), e);
            ShippingFeeConfig shippingFeeConfig = shippingFeeConfigService.getMinFeeByCountry(shippingFeeCountBo.getCountry());
            String message = String.format("运费计算发生未知异常，兜底最低运费,订单信息:%s,异常信息:%s", shippingFeeCountBo.getBuyCommodityInfoBoList(), e.getMessage());
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.WXQ, FeiShuAtUser.LCY);
            return new Price(shippingFeeCountBo.getCurrency(), shippingFeeConfig.getShipping().floatValue());
        } finally {
            LOGGER.info("运费计算入参:{},运费门槛:{},套餐拆分:{},报关毛重信息:{}", JSON.toJSONString(shippingFeeCountBo), shippingCostDefine, shippingCostDefine, commodityMetaMap);
            this.createOrderShippingFeeRecords(shippingFeeCountBo.getCountry(), shippingFeeCountBo.getOrderCreation(), shippingCostDefine, splitCommodity, commodityMetaMap);
        }
    }

    /**
     * 运费计算(废弃)
     *
     * @param orderCreation 订单创建
     * @return {@link Price}
     * @deprecated 废弃
     */
    public Price count(OrderCreation orderCreation) {
        Price itemsPrice = orderItemsPriceCounter.count(orderCreation.getOrderItems(), null);
        return count(orderCreation.getOrderItems(), itemsPrice, orderCreation.getOrderSheet().getCountry());
    }

    /**
     * 计算运费
     *
     * @param itemsPrice    物品价格
     * @param country       国家
     * @param orderItems    订单项
     * @param orderCreation 订单创建
     * @return {@link Price}
     */
    private Price countShippingFee(List<OrderItem> orderItems, Price itemsPrice, InstaCountry country, OrderCreation orderCreation) {
        ShippingFeeCountBO shippingFeeCountBO = new ShippingFeeCountBO();
        shippingFeeCountBO.setCountry(country);
        shippingFeeCountBO.setTotalPrice(itemsPrice);
        shippingFeeCountBO.setOrderCreation(orderCreation);
        shippingFeeCountBO.setCurrency(itemsPrice.getCurrency());
        List<BuyCommodityInfoBO> buyCommodityInfoBoList = orderItems.stream().map(orderItem -> {
            BuyCommodityInfoBO buyCommodityInfoBO = new BuyCommodityInfoBO();
            buyCommodityInfoBO.setCommodityId(orderItem.getCommodity());
            buyCommodityInfoBO.setProductId(orderItem.getProduct());
            buyCommodityInfoBO.setNumber(orderItem.getNumber());
            buyCommodityInfoBO.setPrice(orderItem.price());
            return buyCommodityInfoBO;
        }).collect(Collectors.toList());
        shippingFeeCountBO.setBuyCommodityInfoBoList(buyCommodityInfoBoList);
        return orderFreightCalculate(shippingFeeCountBO);
    }

    /**
     * 创建订单运费毛重记录
     *
     * @param country            国家
     * @param orderCreation      订单创建
     * @param shippingCostDefine 运费门槛信息
     * @param splitCommodity     分商品
     * @param commodityMetaMap   商品毛重信息
     */
    private void createOrderShippingFeeRecords(InstaCountry country, OrderCreation orderCreation, MetaShippingCost shippingCostDefine, List<CommoditySplitBO> splitCommodity, Map<Integer, CommodityMeta> commodityMetaMap) {
        if (orderCreation == null || CollectionUtils.isEmpty(splitCommodity)) {
            return;
        }
        try {
            List<OrderShippingFeeRecord> orderShippingFeeRecordList = splitCommodity.stream().map(commoditySplitBo -> {
                OrderShippingFeeRecord orderShippingFeeRecord = new OrderShippingFeeRecord();
                orderShippingFeeRecord.setCommodityId(commoditySplitBo.getCommodityId());
                orderShippingFeeRecord.setGroupCommodityId(commoditySplitBo.getGroupCommodityId());
                orderShippingFeeRecord.setCountryCode(country.name());

                // 毛重信息和毛重信息可能无记录或为空
                Optional.ofNullable(commodityMetaMap).map(metaMap -> metaMap.get(commoditySplitBo.getCommodityId()))
                        .map(CommodityMeta::getWeight)
                        .map(MathUtil::getBigDecimal)
                        .ifPresent(orderShippingFeeRecord::setGrossWeigh);
                Optional.ofNullable(shippingCostDefine).map(MetaShippingCost::getFreeLimit)
                        .map(MathUtil::getBigDecimal).ifPresent(orderShippingFeeRecord::setFreeLimit);
                orderShippingFeeRecord.setNumber(commoditySplitBo.getNumber());
                orderShippingFeeRecord.setCreateTime(LocalDateTime.now());
                return orderShippingFeeRecord;
            }).collect(Collectors.toList());
            orderCreation.setOrderShippingFeeRecords(orderShippingFeeRecordList);
        } catch (Exception e) {
            LOGGER.error(String.format("记录毛重信息发生异常,国家:%s,订单信息:%s,运费信息:%s,拆分套餐:%s,套餐毛重信息:%s", country, orderCreation, shippingCostDefine, splitCommodity, commodityMetaMap), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("毛重信息记录出现异常%s", e.getMessage()), FeiShuGroupRobot.MainNotice, FeiShuAtUser.WXQ);
        }
    }

    /**
     * 计算总重量
     *
     * @param splitCommodity 拆分套餐
     * @param country        国家
     * @return {@link BigDecimal}
     */
    private BigDecimal calculateTotalWeight(List<CommoditySplitBO> splitCommodity, InstaCountry country, Map<Integer, CommodityMeta> commodityMetaMap) {
        // 计算总毛重
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal commodityWeight;
        for (CommoditySplitBO commoditySplitBo : splitCommodity) {
            CommodityMeta commodityMeta = commodityMetaMap.get(commoditySplitBo.getCommodityId());
            if (Objects.nonNull(commodityMeta) && Objects.nonNull(commodityMeta.getWeight()) && commodityMeta.getWeight() >= 0) {
                commodityWeight = MathUtil.getBigDecimal(commodityMeta.getWeight());
            }
            // 毛重信息异常(毛重信息为空或负数)则设置默认毛重
            else {
                LOGGER.error("套餐ID:{},毛重信息异常,data:{}", commoditySplitBo.getCommodityId(), commodityMeta);
                String message = String.format("套餐ID:%s,毛重信息异常,data:%s", commoditySplitBo.getCommodityId(), JSON.toJSONString(commodityMeta));
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.WXQ, FeiShuAtUser.LCY);
                commodityWeight = new BigDecimal(DEFAULT_WEIGHT);
            }

            BigDecimal itemWeight = commodityWeight.multiply(MathUtil.getBigDecimal(commoditySplitBo.getNumber()));
            totalWeight = totalWeight.add(itemWeight);
        }

        // 获取最大的重量配置
        ShippingFeeConfig maxWeightConfig = shippingFeeConfigService.getMaxWeightConfig(country);
        BigDecimal weightHeight = Optional.ofNullable(maxWeightConfig).map(ShippingFeeConfig::getWeightHeight).orElse(new BigDecimal(DEFAULT_MAX_TOTAL_WEIGHT));
        // 大于0表示 超过最大重量配置，取配置中最大值，小于则取原值。
        return totalWeight.compareTo(weightHeight) > 0 ? weightHeight : totalWeight;
    }

    /**
     * 计算运费（弃用）
     *
     * @param items      订单项
     * @param itemsPrice 物品价格
     * @param country    国家
     * @return {@link Price}
     * @deprecated 2023.08.14 弃用该方法
     */
    private Price count(List<OrderItem> items, Price itemsPrice, InstaCountry country) {
        Price shippingFee;
        MetaShippingCost shippingCostDefine = metaShippingCostService.getByCountry(country);

        if (freeShippingThreshold(itemsPrice, shippingCostDefine)) {
            // 免运费
            shippingFee = new Price(itemsPrice.getCurrency(), 0f);
        } else {
            // 按商品价格配置计算运费
            shippingFee = countItemsShippingFee(items, country);

            // 最高运费限制
            Price maxShippingFee = shippingCostDefine.getCostPrice();
            if (shippingFee != null && maxShippingFee != null && shippingFee.getAmount() > maxShippingFee.getAmount()) {
                shippingFee = maxShippingFee;
            }
        }

        // 格式化
        if (shippingFee != null) {
            shippingFee.format();
        }

        return shippingFee;
    }

    /**
     * 计算是否达到包邮门槛
     *
     * @param itemsPrice         当前计算运费
     * @param shippingCostDefine 包邮门槛相关信息
     * @return boolean
     */
    private Boolean freeShippingThreshold(Price itemsPrice, MetaShippingCost shippingCostDefine) {
        if (shippingCostDefine == null || itemsPrice == null || shippingCostDefine.getFreePrice() == null) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("包邮门槛%s或价格%s信息不存在", shippingCostDefine, itemsPrice), FeiShuGroupRobot.MainNotice, FeiShuAtUser.WXQ);
            return false;
        }
        Price freeLimit = shippingCostDefine.getFreePrice();
        if (!itemsPrice.getCurrency().equals(shippingCostDefine.currency())) {
            Price changeCurrencyPrice = priceService.changeCurrency(itemsPrice, shippingCostDefine.currency());
            return changeCurrencyPrice.getAmount() >= freeLimit.getAmount();
        }
        return itemsPrice.getAmount() >= freeLimit.getAmount();
    }

    /**
     * 计算运费（废弃）
     *
     * @param items   项目
     * @param country 国家
     * @return {@link Price}
     * @deprecated 2023.08.14 弃用该方法
     */
    private Price countItemsShippingFee(List<OrderItem> items, InstaCountry country) {
        Price shippingFee = null;

        for (OrderItem item : items) {
            Integer commodityId = item.getCommodity();
            CommodityPrice commodityPrice = commodityPriceService.getPrice(commodityId, country);
            if (commodityPrice == null) {
                throw new InstaException(CommodityErrorCode.CommodityPriceNotFoundException);
            }
            Price itemShippingFee = commodityPrice.shippingFeePrice();

            // 英国
            if (InstaCountry.GB.equals(country)) {
                if (shippingFee == null) {
                    shippingFee = itemShippingFee.multiply(1);
                } else {
                    // 取最高的价格作为运费
                    if (itemShippingFee.getAmount() > shippingFee.getAmount()) {
                        shippingFee.setAmount(itemShippingFee.getAmount());
                    }
                }
            }
            // 其他国家
            else {
                if (shippingFee == null) {
                    shippingFee = new Price(itemShippingFee.getCurrency(), 0f);
                }

                // 累加
                shippingFee = shippingFee.sum(itemShippingFee.multiply(item.getNumber()));
            }
        }

        return shippingFee;
    }
}
