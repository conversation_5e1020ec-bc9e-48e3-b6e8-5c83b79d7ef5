package com.insta360.store.business.meta.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.meta.model.GraphicNavigationMain;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-03-08
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface GraphicNavigationMainDao extends BaseDao<GraphicNavigationMain> {

    /**
     * 批量更新排序
     *
     * @param updateIndexList
     */
    void updateMainIndex(@Param("updateIndexList") List<GraphicNavigationMain> updateIndexList);
}
