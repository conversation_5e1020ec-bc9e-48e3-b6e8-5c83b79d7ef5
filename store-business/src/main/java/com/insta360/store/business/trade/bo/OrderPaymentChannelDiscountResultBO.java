package com.insta360.store.business.trade.bo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 订单支付渠道折扣结果BO
 * @Date 2023/8/9
 */
public class OrderPaymentChannelDiscountResultBO implements Serializable {

    /**
     * 预选支付渠道
     *
     * @see com.insta360.store.business.meta.enums.PaymentChannel
     */
    private String prePaymentChannel;

    /**
     * 支付渠道折扣金额
     */
    private BigDecimal paymentChannelDiscountAmount;

    /**
     * 是否存在折扣
     */
    private Boolean existDiscount;


    public OrderPaymentChannelDiscountResultBO() {
    }

    public OrderPaymentChannelDiscountResultBO(String prePaymentChannel, BigDecimal paymentChannelDiscountAmount, Boolean existDiscount) {
        this.prePaymentChannel = prePaymentChannel;
        this.paymentChannelDiscountAmount = paymentChannelDiscountAmount;
        this.existDiscount = existDiscount;
    }

    public String getPrePaymentChannel() {
        return prePaymentChannel;
    }

    public void setPrePaymentChannel(String prePaymentChannel) {
        this.prePaymentChannel = prePaymentChannel;
    }

    public BigDecimal getPaymentChannelDiscountAmount() {
        return paymentChannelDiscountAmount;
    }

    public void setPaymentChannelDiscountAmount(BigDecimal paymentChannelDiscountAmount) {
        this.paymentChannelDiscountAmount = paymentChannelDiscountAmount;
    }

    public Boolean getExistDiscount() {
        return existDiscount;
    }

    public void setExistDiscount(Boolean existDiscount) {
        this.existDiscount = existDiscount;
    }

    @Override
    public String toString() {
        return "OrderPaymentChannelDiscountResultBO{" +
                "prePaymentChannel='" + prePaymentChannel + '\'' +
                ", paymentChannelDiscountAmount=" + paymentChannelDiscountAmount +
                ", existDiscount=" + existDiscount +
                '}';
    }
}
