package com.insta360.store.business.discount.dto.ro;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 优惠券创建响应RO
 * @Date 2022/3/31
 */
public class CreateCommonDiscountRo implements Serializable {

    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 创建
     * @param code
     * @return
     */
    public static CreateCommonDiscountRo build(String code) {
        CreateCommonDiscountRo commonDiscountRo = new CreateCommonDiscountRo();
        commonDiscountRo.setCode(code);
        return commonDiscountRo;
    }
}
