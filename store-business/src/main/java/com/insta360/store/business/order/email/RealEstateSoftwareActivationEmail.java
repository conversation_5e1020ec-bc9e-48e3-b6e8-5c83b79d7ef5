package com.insta360.store.business.order.email;

import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 订单发货后行业软件激活指引邮件
 * @Date 2023/9/7
 */
@Scope("prototype")
@Component
public class RealEstateSoftwareActivationEmail extends BaseOrderEmail{

    @Override
    public String getTemplateName() {
        return "store_order_industry_software_activation";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        // 客户名称
        templateParams.addBodyParam("customer_name", this.getCustomerName());

        // 软件权益名
        templateParams.addBodyParam("software_name", this.getSoftwareType().getType());
    }

}
