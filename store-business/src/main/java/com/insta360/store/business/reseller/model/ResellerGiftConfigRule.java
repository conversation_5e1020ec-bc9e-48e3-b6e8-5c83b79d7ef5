package com.insta360.store.business.reseller.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-07-25
 * @Description: 
 */
public class ResellerGiftConfigRule extends BaseModel<ResellerGiftConfigRule> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 分销赠品配置ID
     */
    private Integer resellerConfigId;

    /**
     * 活动类目ID
     */
    private Integer resellerActivityId;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否赠送赠品
     */
    private Boolean freebiesMark;

    /**
     * 状态 0:禁用 1：启用
     */
    private Integer state;

    /**
     * 赠品扩展配置
     */
    private String extraGifts;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人工号
     */
    private String operatorNumber;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getResellerConfigId() {
        return resellerConfigId;
    }

    public void setResellerConfigId(Integer resellerConfigId) {
        this.resellerConfigId = resellerConfigId;
    }

    public Integer getResellerActivityId() {
        return resellerActivityId;
    }

    public void setResellerActivityId(Integer resellerActivityId) {
        this.resellerActivityId = resellerActivityId;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getExtraGifts() {
        return extraGifts;
    }

    public void setExtraGifts(String extraGifts) {
        this.extraGifts = extraGifts;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOperatorNumber() {
        return operatorNumber;
    }

    public void setOperatorNumber(String operatorNumber) {
        this.operatorNumber = operatorNumber;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(LocalDateTime modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Boolean getFreebiesMark() {
        return freebiesMark;
    }

    public void setFreebiesMark(Boolean freebiesMark) {
        this.freebiesMark = freebiesMark;
    }

    @Override
    public String toString() {
        return "ResellerGiftConfigRule{" +
                "id=" + id +
                ", resellerConfigId=" + resellerConfigId +
                ", resellerActivityId=" + resellerActivityId +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", freebiesMark=" + freebiesMark +
                ", state=" + state +
                ", extraGifts='" + extraGifts + '\'' +
                ", remark='" + remark + '\'' +
                ", operatorNumber='" + operatorNumber + '\'' +
                ", operatorName='" + operatorName + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}