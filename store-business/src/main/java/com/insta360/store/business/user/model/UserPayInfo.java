package com.insta360.store.business.user.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.configuration.utils.AESUtil;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-06-06
 * @Description: 用户快捷支付信息
 */
@TableName("user_pay_info")
public class UserPayInfo extends BaseModel<UserPayInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户账户id
     */
    private Integer instaAccount;

    /**
     * 购买邮箱（用于回溯游客账户关联）
     */
    private String contactEmail;

    /**
     * 支付机构
     */
    private String paymentMethod;

    /**
     * 支付机构衍生子类型
     */
    private String paymentSubMethod;

    /**
     * 扣款类别（paypal：账号；Ocean：前六后四（111111*****1232）；cko：前六后四（拼接））
     */
    private String deductCategory;

    /**
     * 卡种
     */
    private String cardType;

    /**
     * 卡地区
     */
    private String cardCountry;

    /**
     * 快捷支付id
     */
    private String payId;

    /**
     * cko payment id 用作后续订阅扣款的previous payment id
     * paypal 用于保存order id，用于webhook vault id兜底
     */
    private String paymentTradeId;

    /**
     * paypal客户id，跟随vault id一起返回，用于后续传给前端sdk给returning客户渲染button
     */
    private String paypalCustomerId;

    /**
     * 游客主动取消续费标识
     */
    private Boolean guestCancel;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 解析支付机构
     *
     * @return
     */
    public StorePaymentMethodEnum parsePaymentMethod() {
        return StorePaymentMethodEnum.parse(getPaymentMethod());
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInstaAccount() {
        return instaAccount;
    }

    public void setInstaAccount(Integer instaAccount) {
        this.instaAccount = instaAccount;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getDeductCategory() {
        return deductCategory;
    }

    public void setDeductCategory(String deductCategory) {
        this.deductCategory = deductCategory;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardCountry() {
        return cardCountry;
    }

    public void setCardCountry(String cardCountry) {
        this.cardCountry = cardCountry;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getPaymentSubMethod() {
        return paymentSubMethod;
    }

    public void setPaymentSubMethod(String paymentSubMethod) {
        this.paymentSubMethod = paymentSubMethod;
    }

    public Boolean getGuestCancel() {
        return guestCancel;
    }

    public void setGuestCancel(Boolean guestCancel) {
        this.guestCancel = guestCancel;
    }

    public String getPaymentTradeId() {
        return paymentTradeId;
    }

    public void setPaymentTradeId(String paymentTradeId) {
        this.paymentTradeId = paymentTradeId;
    }

    public String getPaypalCustomerId() {
        return paypalCustomerId;
    }

    public void setPaypalCustomerId(String paypalCustomerId) {
        this.paypalCustomerId = paypalCustomerId;
    }

    public void setAutoPayId(String payId) {
        this.payId = AESUtil.encode(AESUtil.PAY_TOKEN_KEY, payId);
    }

    @Override
    public String toString() {
        return "UserPayInfo{" +
                "id=" + id +
                ", instaAccount=" + instaAccount +
                ", contactEmail='" + contactEmail + '\'' +
                ", paymentMethod='" + paymentMethod + '\'' +
                ", paymentSubMethod='" + paymentSubMethod + '\'' +
                ", deductCategory='" + deductCategory + '\'' +
                ", cardType='" + cardType + '\'' +
                ", cardCountry='" + cardCountry + '\'' +
                ", payId='" + payId + '\'' +
                ", paymentTradeId='" + paymentTradeId + '\'' +
                ", paypalCustomerId='" + paypalCustomerId + '\'' +
                ", guestCancel=" + guestCancel +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "} " + super.toString();
    }
}