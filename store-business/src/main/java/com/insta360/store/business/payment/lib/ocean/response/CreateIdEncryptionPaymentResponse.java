package com.insta360.store.business.payment.lib.ocean.response;

import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import com.alibaba.fastjson.JSONObject;

/**
 * @Author: wkx
 * @Date: 2024/05/21
 * @Description:
 */
public class CreateIdEncryptionPaymentResponse extends BaseOceanPaymentResponse {

    /**
     * 交易安全签名,用于验证交易的安全性。
     * <p>
     * 使用 SHA256 加密,明文加密结构: account+terminal+customer_id+order_notes+card_number+quickpay_id+quickpay_status+secureCode
     */
    private String signValue;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 卡号 前六后四
     */
    private String cardNumber;

    /**
     * 卡种
     */
    private String cardType;

    /**
     * 卡地区
     */
    private String cardCountry;

    /**
     * 快捷支付id
     */
    private String quickPayId;

    /**
     * 快捷支付id响应状态
     */
    private String quickPayStatus;

    /**
     * 快捷支付id响应明细
     */
    private String quickPayDetails;

    public CreateIdEncryptionPaymentResponse(JSONObject json) {
        account = json.getString("account");
        terminal = json.getString("terminal");
        signValue = json.getString("signValue");
        orderNotes = json.getString("order_notes");
        cardNumber = json.getString("card_number");
        customerId = json.getString("customer_id");
        cardType = json.getString("card_type");
        cardCountry = json.getString("card_country");
        quickPayId = json.getString("quickpay_id");
        quickPayStatus = json.getString("quickpay_status");
        quickPayDetails = json.getString("quickpay_details");
    }

    /**
     * 根据返回值进行反解匹配是否一致
     *
     * @param secureCode
     * @return
     */
    public boolean isSignatureValid(String secureCode) {
        StringBuilder preSignValue = new StringBuilder();
        preSignValue.append(this.getAccount());
        preSignValue.append(this.getTerminal());
        preSignValue.append(this.getCustomerId());
        preSignValue.append(this.getOrderNotes());
        preSignValue.append(this.getCardNumber());
        preSignValue.append(this.getQuickPayId());
        preSignValue.append(this.getQuickPayStatus());
        preSignValue.append(secureCode);
        return new Digester(DigestAlgorithm.SHA256).digestHex(preSignValue.toString()).toUpperCase().equalsIgnoreCase(this.getSignValue());
    }

    public String getOrderNotes() {
        return orderNotes;
    }

    public void setOrderNotes(String orderNotes) {
        this.orderNotes = orderNotes;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardCountry() {
        return cardCountry;
    }

    public void setCardCountry(String cardCountry) {
        this.cardCountry = cardCountry;
    }

    public String getQuickPayId() {
        return quickPayId;
    }

    public void setQuickPayId(String quickPayId) {
        this.quickPayId = quickPayId;
    }

    public String getQuickPayStatus() {
        return quickPayStatus;
    }

    public void setQuickPayStatus(String quickPayStatus) {
        this.quickPayStatus = quickPayStatus;
    }

    public String getQuickPayDetails() {
        return quickPayDetails;
    }

    public void setQuickPayDetails(String quickPayDetails) {
        this.quickPayDetails = quickPayDetails;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getSignValue() {
        return signValue;
    }

    public void setSignValue(String signValue) {
        this.signValue = signValue;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    @Override
    public String toString() {
        return "CreateIdEncryptionPaymentResponse{" +
                "account='" + account + '\'' +
                ", terminal='" + terminal + '\'' +
                ", signValue='" + signValue + '\'' +
                ", customerId='" + customerId + '\'' +
                ", orderNotes='" + orderNotes + '\'' +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardType='" + cardType + '\'' +
                ", cardCountry='" + cardCountry + '\'' +
                ", quickPayId='" + quickPayId + '\'' +
                ", quickPayStatus='" + quickPayStatus + '\'' +
                ", quickPayDetails='" + quickPayDetails + '\'' +
                '}';
    }
}
