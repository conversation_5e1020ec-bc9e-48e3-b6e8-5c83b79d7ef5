package com.insta360.store.business.meta.robot;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/6
 */
public enum FeiShuAtUser {

    /**
     * 全部
     */
    ALL("all", "@所有人", "<EMAIL>"),

    /**
     * 商城用户-王宝铜
     */
    WBT("store_user_wbt", "王宝铜", "<EMAIL>"),

    /**
     * 商城用户-吴晓强
     */
    WXQ("store_user_wxq", "吴晓强", "<EMAIL>"),

    /**
     * 商城用户-彭越(<PERSON>)
     */
    PY("store_user_py", "彭越(<PERSON>)", "<EMAIL>"),

    /**
     * 商城用户-魏坤祥
     */
    WKX("store_user_wkx", "魏坤祥", "<EMAIL>"),

    /**
     * 商城用户-唐微
     */
    TW("store_user_tw", "唐微", "<EMAIL>"),

    /**
     * zxt
     */
    ZXT("store_user_zxt", "赵晓彤", "<EMAIL>"),

    /**
     * zxj
     */
    ZXJ("store_user_zxj", "郑晓洁", "<EMAIL>"),

    /**
     * yct
     */
    YCT("store_user_yct", "游楚婷", "<EMAIL>"),

    /**
     * zxx
     */
    ZXX("store_user_zxx", "郑晓璇", "<EMAIL>"),

    /**
     * gsl
     */
    GSL("store_user_gsl", "郭少玲", "<EMAIL>"),

    /**
     * lb
     */
    LB("store_user_lb", "李斌", "<EMAIL>"),

    /**
     * lxh
     */
    LXH("store_user_lxh", "连秀华", "<EMAIL>"),

    /**
     * lzj
     */
    LZJ("store_user_lzj", "李子君", "<EMAIL>"),

    /**
     * lyb
     */
    LYB("store_user_lyb", "罗雅冰", "<EMAIL>"),

    /**
     * yxq
     */
    YXQ("store_user_yxq", "杨璇琪", "<EMAIL>"),

    /**
     * hxy
     */
    HXY("store_user_hxy", "何秀燕", "<EMAIL>"),

    /**
     * jhf
     */
    JHF("store_user_jhf", "江海帆", "<EMAIL>"),

    /**
     * zyn
     */
    ZYN("store_user_zyn", "赵艳宁", "<EMAIL>"),

    /**
     * ZHY
     */
    ZHY("store_user_zhy", "周辉苑", "<EMAIL>"),

    /**
     * THY
     */
    THY("store_user_thy", "谭红艳", "<EMAIL>"),

    /**
     * THY
     */
    GQY("store_user_gqy", "关倩怡", "<EMAIL>"),

    /**
     * FA
     */
    FA("store_user_fa", "范澳", "<EMAIL>"),

    /**
     * YLN
     */
    YLN("store_user_yln", "余琳娜", "<EMAIL>"),

    /**
     * ZMH
     */
    ZMH("store_user_zmh", "朱美华", "<EMAIL>"),

    /**
     * ZDX
     */
    ZDX("store_user_zdx", "庄丹欣", "<EMAIL>"),

    /**
     * LJC
     */
    LJC("store_user_ljc", "李建超", "<EMAIL>"),

    /**
     * ZJW
     */
    ZJW("store_user_zjw", "周加伟", "<EMAIL>"),

    /**
     * CSD
     */
    CSD("store_user_csd", "陈思丹", "<EMAIL>"),

    /**
     * CYJ
     */
    CYJ("store_user_cyj", "陈永杰", "<EMAIL>"),

    /**
     * TF
     */
    TF("store_user_tf", "童芳", "<EMAIL>"),

    /**
     * CYE
     */
    CYE("store_user_cye", "陈雅恩", "<EMAIL>"),

    /**
     * ZYX
     */
    ZYX("store_user_zyx", "朱颖欣", "<EMAIL>"),

    /**
     * HSM
     */
    HSM("store_user_hsm", "黄思敏", "<EMAIL>"),

    /**
     * 芦恬
     */
    LT("store_user_lt", "芦恬", "<EMAIL>"),

    /**
     * ojh
     */
    OJH("store_user_ojh", "欧俊虹", "<EMAIL>"),

    /**
     * yy
     */
    YY("store_user_yy", "颜炎", "<EMAIL>"),

    /**
     * tzb
     */
    TZB("store_user_tzb", "唐智博", "<EMAIL>"),

    /**
     * zm
     */
    ZM("store_user_zm", "郑敏", "<EMAIL>"),

    /**
     * cqs
     */
    CQS("store_user_cqs", "陈庆珊", "<EMAIL>"),

    /**
     * mxj
     */
    MXJ("store_user_msj", "孟祥吉", "<EMAIL>"),

    /**
     * lcn
     */
    LCN("store_user_lcn", "梁楚妮", "<EMAIL>"),

    /**
     * dxy
     */
    DXY("store_user_dyx", "董星雨", "<EMAIL>"),

    /**
     * zly
     */
    ZLY("store_user_zly", "张琳悦", "<EMAIL>"),

    /**
     * zxy
     */
    ZXY("store_user_zxy", "赵兴艺", "<EMAIL>"),

    /**
     * cww
     */
    CWW("store_user_cww", "陈蔚雯", "<EMAIL>"),

    /**
     * lcy
     */
    LCY("store_user_lcy", "陆纯一", "<EMAIL>"),

    /**
     * lr
     */
    LR("store_user_lr", "卢然", "<EMAIL>"),

    /**
     * czy
     */
    CZY("store_user_czy", "陈子莹", "<EMAIL>"),

    /**
     * lq
     */
    LQ("store_user_lq", "梁倩", "<EMAIL>"),

    /**
     * lcx
     */
    LCX("store_user_lcx", "李忱羲", "<EMAIL>"),

    /**
     * gn
     */
    GN("store_user_gn", "郭楠", "<EMAIL>"),

    /**
     * 刘柯雨
     */
    LKY("store_user_lky", "刘柯雨", "<EMAIL>"),

    /**
     * 邹宇星
     */
    ZYX2("store_user_zyx2", "邹宇星", "<EMAIL>"),

    /**
     * 孙家莹
     */
    SJY("store_user_sjy", "孙家莹", "<EMAIL>"),

    /**
     * 许可
     */
    XK("store_user_xk", "许可", "<EMAIL>"),

    /**
     * 张成
     */
    ZC("store_user_zc", "张成", "<EMAIL>"),

    ;


    /**
     * 主要运营同事
     */
    public static final FeiShuAtUser[] OPERATION_NOTICE = new FeiShuAtUser[]{FeiShuAtUser.ZLY, FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY};

    /**
     * 主要客服同事
     */
    public static final FeiShuAtUser[] CUSTOMER_NOTICE = new FeiShuAtUser[]{FeiShuAtUser.JHF, FeiShuAtUser.ZM, FeiShuAtUser.GQY};

    /**
     * 分销用户提醒人
     */
    public static final FeiShuAtUser[] RESELLER_NOTICE = new FeiShuAtUser[]{FeiShuAtUser.CZY, FeiShuAtUser.CWW, FeiShuAtUser.LCN, FeiShuAtUser.XK};

    /**
     * 代码 具体要@哪个用户的唯一标识
     */
    private final String code;

    /**
     * 姓名
     */
    private final String name;

    /**
     * 邮箱
     */
    private final String email;

    FeiShuAtUser(String code, String name, String email) {
        this.code = code;
        this.name = name;
        this.email = email;
    }

    /**
     * 匹配邮件
     *
     * @param jobEmail 工单邮件
     * @return {@link FeiShuAtUser}
     */
    public static FeiShuAtUser matchEmail(String jobEmail) {
        for (FeiShuAtUser feiShuAtUser : values()) {
            if (feiShuAtUser.email.equals(jobEmail)) {
                return feiShuAtUser;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getEmail() {
        return email;
    }
}
