package com.insta360.store.business.prime.enums;

/**
 * Amazon Buy with Prime 商品类型枚举
 *
 * <p>
 * 用于定义在 Amazon Buy with Prime 平台中商品的类型:
 * - Individual: 单个商品，对应 Prime 平台中的单个 SKU
 * - Bundle: 组合商品，包含多个单商品的套装
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 * @see <a href="https://documents.buywithprime.amazon.com/bwp-api/docs/buy-with-prime-api-overview">Amazon Buy with Prime API</a>
 */
public enum PrimeCommodityType {

    /**
     * 单商品类型
     * <p>
     * 单个产品，一个 SKU 对应一个实物商品。
     * 在 Prime 创建为单商品类型后，可以作为组合商品的组成部分。
     * </p>
     */
    Individual("Individual", "单商品"),

    /**
     * 组合商品类型
     * <p>
     * 由多个单商品组成的套装。
     * Bundle 类型商品只能由已在 Prime 创建的 Individual 类型商品组成。
     * 每个 Bundle 最多可包含 10 个商品项。
     * </p>
     */
    Bundle("Bundle", "组合商品"),
    ;

    /**
     * 商品类型代码
     * 对应 Amazon Buy with Prime API 中的类型标识符
     */
    private final String code;

    /**
     * 商品类型名称
     * 用于界面显示的中文名称
     */
    private final String name;

    PrimeCommodityType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据类型代码匹配枚举值
     *
     * @param code 类型代码，如 "Individual" 或 "Bundle"
     * @return 匹配的枚举值，如果没有匹配项则返回 null
     */
    public static PrimeCommodityType matchCode(String code) {
        if (code == null) {
            return null;
        }

        for (PrimeCommodityType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为单商品类型
     *
     * @return 如果是 Individual 类型返回 true，否则返回 false
     */
    public boolean isIndividual() {
        return this == Individual;
    }

    /**
     * 判断是否为组合商品类型
     *
     * @return 如果是 Bundle 类型返回 true，否则返回 false
     */
    public boolean isBundle() {
        return this == Bundle;
    }

    /**
     * 获取商品类型代码
     *
     * @return 类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取商品类型名称
     *
     * @return 类型名称
     */
    public String getName() {
        return name;
    }
}
