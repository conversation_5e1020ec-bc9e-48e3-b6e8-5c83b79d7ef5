package com.insta360.store.business.outgoing.rpc.store.job;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.feign.CachePutFeignConfiguration;
import com.insta360.store.business.outgoing.common.ServiceName;
import com.insta360.store.business.outgoing.rpc.store.job.fallback.CommodityCachePutFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: wkx
 * @Date: 2023/11/6
 * @Description:
 */
@FeignClient(contextId = "commodityCachePutService", value = ServiceName.STORE_SERVICE, fallback = CommodityCachePutFallBack.class, configuration = CachePutFeignConfiguration.class)
public interface CommodityCachePutService {

    /**
     * 更新套餐信息
     *
     * @param commodityId
     * @param country
     * @param language
     */
    @GetMapping("/rpc/store/service/cacheput/product/commodity/getInfo")
    void getInfo(@RequestParam Integer commodityId, @RequestParam InstaCountry country, @RequestParam InstaLanguage language);

    /**
     * 更新缓存指定产品所有套餐的包装清单信息
     *
     * @param productId
     * @param language
     * @param country
     */
    @GetMapping("/rpc/store/service/cacheput/product/commodity/getDifferences")
    void getDifferences(@RequestParam Integer productId, @RequestParam InstaCountry country, @RequestParam InstaLanguage language);

    /**
     * 更新指定产品所有套餐的推荐配件信息
     *
     * @param productId
     * @param language
     * @param country
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/product/commodity/getCommodityRecommendationInfo")
    void getCommodityRecommendationInfo(@RequestParam Integer productId, @RequestParam InstaCountry country, @RequestParam InstaLanguage language);
}
