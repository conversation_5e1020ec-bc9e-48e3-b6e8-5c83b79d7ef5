package com.insta360.store.business.meta.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategorySubsetInside;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/5/25
 * @Description:
 */
public class NavigationBarCategorySubsetInsideDTO implements Serializable {

    private Integer id;

    /**
     * 一级内部分类id
     */
    @NotNull(message = "一级内部分类id不允许为空")
    private Integer categoryInsideId;

    /**
     * 二级分类内部名称
     */
    @NotBlank(message = "二级分类内部名称不允许为空")
    private String subsetInsideName;

    /**
     * 二级排序
     */
    @NotNull(message = "二级排序不允许为空")
    private Integer orderIndex;

    /**
     * 跳转链接
     */
    private String urlLink;

    public NavigationBarCategorySubsetInside getPojoObject() {
        NavigationBarCategorySubsetInside categorySubsetInside = new NavigationBarCategorySubsetInside();
        BeanUtil.copyProperties(this, categorySubsetInside);
        return categorySubsetInside;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public String getSubsetInsideName() {
        return subsetInsideName;
    }

    public void setSubsetInsideName(String subsetInsideName) {
        this.subsetInsideName = subsetInsideName;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public void setUrlLink(String urlLink) {
        this.urlLink = urlLink;
    }

    @Override
    public String toString() {
        return "NavigationBarCategorySubsetInsideDTO{" +
                "id=" + id +
                ", categoryInsideId=" + categoryInsideId +
                ", subsetInsideName='" + subsetInsideName + '\'' +
                ", orderIndex=" + orderIndex +
                ", urlLink='" + urlLink + '\'' +
                '}';
    }
}
