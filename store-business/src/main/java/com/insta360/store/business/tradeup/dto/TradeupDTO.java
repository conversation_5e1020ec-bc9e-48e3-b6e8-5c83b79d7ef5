package com.insta360.store.business.tradeup.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.user.model.StoreAccount;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2020/11/20
 * @Description:
 */
public class TradeupDTO implements Serializable {

    private StoreAccount account;

    private Integer rule;

    private InstaCountry country;

    private String countryName;

    private String province;

    private String city;

    @JSONField(name = "first_name")
    private String firstName;

    @JSONField(name = "last_name")
    private String lastName;

    private String company;

    private String phone;

    @JSONField(name = "phone_code")
    private String phoneCode;

    private String address;

    @J<PERSON>NField(name = "sub_address")
    private String subAddress;

    private String email;

    @JSONField(name = "zip_code")
    private String zipCode;

    /**
     * 参与trade up的机型
     */
    private Integer typeId;

    private Integer orderId;

    /**
     * 参与trade up的被置换设备
     */
    private Integer deviceId;

    public StoreAccount getAccount() {
        return account;
    }

    public void setAccount(StoreAccount account) {
        this.account = account;
    }

    public Integer getRule() {
        return rule;
    }

    public void setRule(Integer rule) {
        this.rule = rule;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public void setCountry(InstaCountry country) {
        this.country = country;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Integer deviceId) {
        this.deviceId = deviceId;
    }

    @Override
    public String toString() {
        return "TradeupDTO{" +
                "account=" + account +
                ", rule=" + rule +
                ", country=" + country +
                ", countryName='" + countryName + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", company='" + company + '\'' +
                ", phone='" + phone + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", email='" + email + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", typeId=" + typeId +
                ", orderId=" + orderId +
                ", deviceId=" + deviceId +
                '}';
    }
}
