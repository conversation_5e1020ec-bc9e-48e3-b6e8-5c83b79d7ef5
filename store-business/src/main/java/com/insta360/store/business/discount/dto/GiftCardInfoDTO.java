package com.insta360.store.business.discount.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.discount.model.GiftCardInfo;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2020/11/19
 * @Description:
 */
public class GiftCardInfoDTO implements Serializable {

    private String tag;

    private String title;

    @JSONField(name = "content_detail")
    private String contentDetail;

    @JSONField(name = "image_url")
    private String imageUrl;

    @JSONField(name = "use_link")
    private String useLink;

    @J<PERSON>NField(name = "create_time")
    private LocalDateTime createTime;

    public GiftCardInfo getPojoObject() {
        GiftCardInfo giftCardInfo = new GiftCardInfo();
        BeanUtil.copyProperties(this, giftCardInfo);
        return giftCardInfo;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContentDetail() {
        return contentDetail;
    }

    public void setContentDetail(String contentDetail) {
        this.contentDetail = contentDetail;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getUseLink() {
        return useLink;
    }

    public void setUseLink(String useLink) {
        this.useLink = useLink;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
