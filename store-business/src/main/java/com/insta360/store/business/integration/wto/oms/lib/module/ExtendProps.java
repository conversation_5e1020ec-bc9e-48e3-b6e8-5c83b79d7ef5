package com.insta360.store.business.integration.wto.oms.lib.module;

import com.insta360.store.business.integration.wto.oms.lib.module.order.MallSalesOrderDiscount;
import com.insta360.store.business.integration.wto.oms.lib.module.order.OrderDetail;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2024/12/03
 * @Description:
 */
public class ExtendProps {

    /**
     * 货币
     */
    private String currencyCode;

    /**
     * 总税费
     */
    private String totalTaxPrice;

    /**
     * 运费总税额
     */
    private String expressFeeTaxAmount;

    /**
     * 订单相关标签(标签会和OMS配置进行动态匹配)
     */
    private List<String> tagCodes;

    /**
     * 其他出入库类型
     * 使用免单券的订单：CP01
     * 常规销售订单不传
     */
    private String customCodeOne;

    /**
     * 七级税费
     */
    private SevenLevelTaxPrice sevenLevelTaxPrice;

    /**
     * 订单子项明细
     */
    private List<OrderDetail> orderDetails;

    /**
     * 订单优惠信息
     */
    private List<MallSalesOrderDiscount> mallSalesOrderDiscounts;

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getTotalTaxPrice() {
        return totalTaxPrice;
    }

    public void setTotalTaxPrice(String totalTaxPrice) {
        this.totalTaxPrice = totalTaxPrice;
    }

    public List<OrderDetail> getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(List<OrderDetail> orderDetails) {
        this.orderDetails = orderDetails;
    }

    public List<MallSalesOrderDiscount> getMallSalesOrderDiscounts() {
        return mallSalesOrderDiscounts;
    }

    public void setMallSalesOrderDiscounts(List<MallSalesOrderDiscount> mallSalesOrderDiscounts) {
        this.mallSalesOrderDiscounts = mallSalesOrderDiscounts;
    }

    public SevenLevelTaxPrice getSevenLevelTaxPrice() {
        return sevenLevelTaxPrice;
    }

    public void setSevenLevelTaxPrice(SevenLevelTaxPrice sevenLevelTaxPrice) {
        this.sevenLevelTaxPrice = sevenLevelTaxPrice;
    }

    public List<String> getTagCodes() {
        return tagCodes;
    }

    public void setTagCodes(List<String> tagCodes) {
        this.tagCodes = tagCodes;
    }

    public String getExpressFeeTaxAmount() {
        return expressFeeTaxAmount;
    }

    public void setExpressFeeTaxAmount(String expressFeeTaxAmount) {
        this.expressFeeTaxAmount = expressFeeTaxAmount;
    }

    public String getCustomCodeOne() {
        return customCodeOne;
    }

    public void setCustomCodeOne(String customCodeOne) {
        this.customCodeOne = customCodeOne;
    }

    @Override
    public String toString() {
        return "ExtendProps{" +
                "currencyCode='" + currencyCode + '\'' +
                ", totalTaxPrice='" + totalTaxPrice + '\'' +
                ", expressFeeTaxAmount='" + expressFeeTaxAmount + '\'' +
                ", tagCodes=" + tagCodes +
                ", customCodeOne='" + customCodeOne + '\'' +
                ", sevenLevelTaxPrice=" + sevenLevelTaxPrice +
                ", orderDetails=" + orderDetails +
                ", mallSalesOrderDiscounts=" + mallSalesOrderDiscounts +
                '}';
    }
}
