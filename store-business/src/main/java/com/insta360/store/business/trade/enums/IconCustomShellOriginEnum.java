package com.insta360.store.business.trade.enums;

import java.util.Objects;

/**
 * 定制ICON来源枚举
 *
 * <AUTHOR>
 * @Date: 2023/03/27
 * @Description:
 */
public enum IconCustomShellOriginEnum {
    /**
     * 未知
     */
    UNKNOWN(-1, "", ""),

    /**
     * 没有图标
     */
    NO_ICON(-2, "无", ""),

    /**
     * 默认提供的
     */
    CUSTOM_BY_NORMAL(0, "预设icon", ""),

    /**
     * 用户自己上传的
     */
    CUSTOM_BY_CONSUMER_UPLOAD(1, "本地上传", "-upload"),
    ;

    /**
     * 标识
     */
    private final Integer code;

    /**
     * 图片描述
     */
    private final String detail;

    /**
     * 英文描述
     */
    private final String enDetail;

    /**
     * 类型解析
     *
     * @param code
     * @return
     */
    public static IconCustomShellOriginEnum parse(Integer code) {
        if (Objects.isNull(code)) {
            return NO_ICON;
        }
        for (IconCustomShellOriginEnum customShellOrigin : values()) {
            if (customShellOrigin.code.equals(code)) {
                return customShellOrigin;
            }
        }
        return UNKNOWN;
    }

    IconCustomShellOriginEnum(Integer code, String detail, String enDetail) {
        this.code = code;
        this.detail = detail;
        this.enDetail = enDetail;
    }

    public Integer getCode() {
        return code;
    }

    public String getDetail() {
        return detail;
    }

    public String getEnDetail() {
        return enDetail;
    }
}
