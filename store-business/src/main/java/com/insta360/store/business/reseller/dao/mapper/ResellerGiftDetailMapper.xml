<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.reseller.dao.ResellerGiftDetailDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.reseller.dao.ResellerGiftDetailDao"/>

    <!-- 批量保存 -->
    <insert id="saveBatch" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into reseller_gift_detail (config_rule_id,commodity_id,number)
        values
        <foreach collection="list" item="detail" separator=",">
            (#{detail.configRuleId},#{detail.commodityId},#{detail.number})
        </foreach>
    </insert>

    <select id="listByConfigRuleIds" resultType="com.insta360.store.business.reseller.model.ResellerGiftDetail">
        select * from reseller_gift_detail where config_rule_id in
            <foreach collection="configRuleIds" item="configRuleId" open="("  close=")" separator=",">
                #{configRuleId}
            </foreach>
    </select>
</mapper>
