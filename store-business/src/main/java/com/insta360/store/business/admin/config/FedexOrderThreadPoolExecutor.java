package com.insta360.store.business.admin.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: wbt
 * @Date: 2020/11/03
 * @Description:
 */
public class FedexOrderThreadPoolExecutor extends ThreadPoolExecutor {

    public static final Logger LOGGER = LoggerFactory.getLogger(FedexOrderThreadPoolExecutor.class);

    // 保存任务开始执行的时间,当任务结束时,用任务结束时间减去开始时间计算任务执行时间
    private ConcurrentHashMap<String, LocalDateTime> startTimes;

    // 线程池名称,一般以业务名称命名,方便区分
    private String poolName;

    public FedexOrderThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, String poolName) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, new EventThreadFactory(poolName));
        this.startTimes = new ConcurrentHashMap<>();
        this.poolName = poolName;
    }

    /**
     * 创建缓存型线程池
     *
     * @param poolName
     * @return
     */
    public static FedexOrderThreadPoolExecutor newCachedThreadPool(String poolName) {
        FedexOrderThreadPoolExecutor executor = new FedexOrderThreadPoolExecutor(0, 20, 1L, TimeUnit.MILLISECONDS, new SynchronousQueue<>(), poolName);
        executor.allowCoreThreadTimeOut(true);
        return executor;
    }

    /**
     * 生成线程池所用的线程,只是改写了线程池默认的线程工厂,传入线程池名称,便于问题追踪
     */
    static class EventThreadFactory implements ThreadFactory {
        private static final AtomicInteger poolNumber = new AtomicInteger(1);
        private final ThreadGroup group;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        /**
         * 初始化线程工厂
         *
         * @param poolName 线程池名称
         */
        EventThreadFactory(String poolName) {
            SecurityManager s = System.getSecurityManager();
            group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
            namePrefix = poolName + "-pool-" + poolNumber.getAndIncrement() + "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(group, r, namePrefix + threadNumber.getAndIncrement(), 0);
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
}
