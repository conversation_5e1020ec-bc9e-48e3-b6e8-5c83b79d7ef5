package com.insta360.store.business.integration.lingxing.enums;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/22
 */
public enum LxOrderStateEnum {
    /**
     * 未知
     */
    UNKNOWN(0, "unknown"),

    /**
     * 已发货
     */
    SHIPPED(2, "已发货"),

    /**
     * 未付款
     */
    UNPAID(3, "未付款"),

    /**
     * 待审核
     */
    PENDING_REVIEW(4, "待审核"),

    /**
     * 待发货
     */
    AWAITING_SHIPMENT(5, "待发货"),

    /**
     * 已取消
     */
    CANCELLED(6, "已取消"),

    /**
     * 不发货
     */
    NOT_MAKE_DELIVERY(7, "不发货"),
    ;

    /**
     * 代码
     */
    private final int code;

    /**
     * 状态名称
     */
    private final String name;

    LxOrderStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 解析代码
     *
     * @param code 代码
     * @return {@link LxOrderStateEnum}
     */
    public static LxOrderStateEnum parseCode(int code) {
        for (LxOrderStateEnum status : LxOrderStateEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return UNKNOWN;
    }

    /**
     * 解析名称
     *
     * @param name 名称
     * @return {@link LxOrderStateEnum}
     */
    public static LxOrderStateEnum parseName(String name) {
        for (LxOrderStateEnum status : LxOrderStateEnum.values()) {
            if (status.name.equals(name)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}