package com.insta360.store.business.integration.attentive.enums;

/**
 * @description: 需要上报的国家
 * @author: py
 * @create: 2023-09-06 11:17
 */
public enum AttentiveCountryType {

    /**
     * 美国
     */
    US("en"),

    ;

    /**
     * 语言
     */
    private final String language;

    AttentiveCountryType(String language) {
        this.language = language;
    }

    public String getLanguage() {
        return language;
    }

    /**
     * 解析
     *
     * @param countryType
     * @return
     */
    public static AttentiveCountryType parse(String countryType) {
        for (AttentiveCountryType attentiveCountryType : values()) {
            if (attentiveCountryType.name().equals(countryType)) {
                return attentiveCountryType;
            }
        }
        return null;
    }
}
