package com.insta360.store.business.outgoing.rpc.user.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/05/10
 * @Description:
 */
public class UserAccount implements Serializable {

    private Integer id;

    private String username;

    private String type;

    private String language;

    private String nickname;

    private String avatar;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String source;

    private Integer gender;

    private String description;

    private String email;

    private String country;

    private Boolean official;

    private Boolean test;

    private Boolean robot;

    private LocalDate birthday;

    private String deviceSerial;

    private String phone;

    private Boolean hideGps;

    /**
     * 佩戴勋章的icon
     */
    private String badgeIcon;

    /**
     * 用户勋章数量
     */
    private Integer badgeTotal;

    /**
     * 是否校验邮箱
     */
    private Boolean isVerifyEmail;

    /**
     * 是否活跃
     */
    private Boolean active;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Boolean getOfficial() {
        return official;
    }

    public void setOfficial(Boolean official) {
        this.official = official;
    }

    public Boolean getTest() {
        return test;
    }

    public void setTest(Boolean test) {
        this.test = test;
    }

    public Boolean getRobot() {
        return robot;
    }

    public void setRobot(Boolean robot) {
        this.robot = robot;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Boolean getHideGps() {
        return hideGps;
    }

    public void setHideGps(Boolean hideGps) {
        this.hideGps = hideGps;
    }

    public String getBadgeIcon() {
        return badgeIcon;
    }

    public void setBadgeIcon(String badgeIcon) {
        this.badgeIcon = badgeIcon;
    }

    public Integer getBadgeTotal() {
        return badgeTotal;
    }

    public void setBadgeTotal(Integer badgeTotal) {
        this.badgeTotal = badgeTotal;
    }

    public Boolean getVerifyEmail() {
        return isVerifyEmail;
    }

    public void setVerifyEmail(Boolean verifyEmail) {
        isVerifyEmail = verifyEmail;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    @Override
    public String toString() {
        return "UserAccount{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", type='" + type + '\'' +
                ", language='" + language + '\'' +
                ", nickname='" + nickname + '\'' +
                ", avatar='" + avatar + '\'' +
                ", createTime=" + createTime +
                ", source='" + source + '\'' +
                ", gender=" + gender +
                ", description='" + description + '\'' +
                ", email='" + email + '\'' +
                ", country='" + country + '\'' +
                ", official=" + official +
                ", test=" + test +
                ", robot=" + robot +
                ", birthday=" + birthday +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", phone='" + phone + '\'' +
                ", hideGps=" + hideGps +
                ", badgeIcon='" + badgeIcon + '\'' +
                ", badgeTotal=" + badgeTotal +
                ", isVerifyEmail=" + isVerifyEmail +
                ", active=" + active +
                '}';
    }
}
