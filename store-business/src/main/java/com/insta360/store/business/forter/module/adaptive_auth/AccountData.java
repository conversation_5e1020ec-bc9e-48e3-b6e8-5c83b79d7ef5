package com.insta360.store.business.forter.module.adaptive_auth;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 客户账户数据
 * @Date 2021/5/20
 */
public class AccountData implements Serializable {

    /**
     * 帐户类型（企业帐户，私人帐户，商户帐户）
     * 游客传（GUEST）,用户传（PRIVATE）
     */
    private String type;

    /**
     * 账户状态
     */
    private String merchantAccountStatus;

    /**
     *状态更改-如果更新包括帐户状态更改，则为必填项。
     * 示例：MERCHANT_ADMIN
     * 可能的值为：“ MERCHANT_ADMIN”，“ END_USER”，“ FORTER”
     */
    private String statusChangeBy;

    /**
     * 状态变更原因
     */
    private String statusChangeReason;

    /**
     * 密码数据
     */
    private PasswordData passwordData;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMerchantAccountStatus() {
        return merchantAccountStatus;
    }

    public void setMerchantAccountStatus(String merchantAccountStatus) {
        this.merchantAccountStatus = merchantAccountStatus;
    }

    public String getStatusChangeBy() {
        return statusChangeBy;
    }

    public void setStatusChangeBy(String statusChangeBy) {
        this.statusChangeBy = statusChangeBy;
    }

    public String getStatusChangeReason() {
        return statusChangeReason;
    }

    public void setStatusChangeReason(String statusChangeReason) {
        this.statusChangeReason = statusChangeReason;
    }

    public PasswordData getPasswordData() {
        return passwordData;
    }

    public void setPasswordData(PasswordData passwordData) {
        this.passwordData = passwordData;
    }
}
