package com.insta360.store.business.payment.lib.worldpay.model.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * @Author: wbt
 * @Date: 2020/02/17
 * @Description:
 */
@XStreamAlias("date")
public class WorldPayDateResponse {

    @XStreamAsAttribute
    private String dayOfMonth;

    @XStreamAsAttribute
    private String month;

    @XStreamAsAttribute
    private String year;

    public String getDayOfMonth() {
        return dayOfMonth;
    }

    public void setDayOfMonth(String dayOfMonth) {
        this.dayOfMonth = dayOfMonth;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    @Override
    public String toString() {
        return "WorldPayDateResponse{" +
                "dayOfMonth='" + dayOfMonth + '\'' +
                ", month='" + month + '\'' +
                ", year='" + year + '\'' +
                '}';
    }
}
