package com.insta360.store.business.payment.lib.worldpay.model;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * @Author: wbt
 * @Date: 2020/01/13
 * @Description:
 */
@XStreamAlias("CSE-DATA")
public class WorldPayCseData {

    @XStreamAlias("encryptedData")
    private String encryptedData;

    @XStreamAlias("cardAddress")
    private WorldPayCardAddress worldPayCardAddress;

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }

    public WorldPayCardAddress getWorldPayCardAddress() {
        return worldPayCardAddress;
    }

    public void setWorldPayCardAddress(WorldPayCardAddress worldPayCardAddress) {
        this.worldPayCardAddress = worldPayCardAddress;
    }

    @Override
    public String toString() {
        return "WorldPayCseData{" +
                "encryptedData='" + encryptedData + '\'' +
                ", worldPayCardAddress=" + worldPayCardAddress +
                '}';
    }
}
