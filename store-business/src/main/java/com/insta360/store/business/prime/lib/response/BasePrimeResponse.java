package com.insta360.store.business.prime.lib.response;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.store.business.prime.constants.PrimeConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public class BasePrimeResponse implements PrimeResponse {

    private static final Logger LOGGER = LoggerFactory.getLogger(BasePrimeResponse.class);

    private Integer httpCode;

    private String responseBody;

    /**
     * 解析后的JSON对象
     */
    private JSONObject parsedResponse;

    public BasePrimeResponse() {
    }

    public BasePrimeResponse(Integer httpCode, String responseBody) {
        this.httpCode = httpCode;
        this.responseBody = responseBody;
        parseResponse();
    }

    /**
     * 判断请求是否成功
     * <p>
     * 成功的条件是：
     * 1. HTTP状态码在200-299之间
     * 2. 响应体中不包含errors数组
     *
     * @return 如果请求成功则返回true，否则返回false
     */
    public boolean ok() {
        // 首先检查HTTP状态码
        if (httpCode == null || httpCode < PrimeConstants.Response.HTTP_SUCCESS_MIN || httpCode >= PrimeConstants.Response.HTTP_SUCCESS_MAX) {
            return false;
        }

        // 然后检查是否存在errors
        if (parsedResponse != null && parsedResponse.containsKey(PrimeConstants.Response.ERRORS_KEY)) {
            return false;
        }

        // 如果无法解析响应，但HTTP状态码正常，则默认为成功
        return true;
    }

    /**
     * 获取错误消息
     *
     * @return 如果存在错误则返回第一个错误的消息，否则返回null
     */
    public String errorMessage() {
        if (parsedResponse == null) {
            return StringUtils.EMPTY;

        }
        JSONArray errors = parsedResponse.getJSONArray(PrimeConstants.Response.ERRORS_KEY);
        if (CollectionUtils.isEmpty(errors)) {
            return StringUtils.EMPTY;
        }
        JSONObject firstError = errors.getJSONObject(0);
        return firstError.getString(PrimeConstants.Response.ERROR_MESSAGE_KEY);
    }

    /**
     * 获取响应中的数据部分
     *
     * @param path  数据路径
     * @param clazz 目标类型
     * @param <T>   类型参数
     * @return 转换后的对象
     */
    public <T> T searchDataFiled(String path, Class<T> clazz) {
        if (parsedResponse != null && parsedResponse.containsKey(PrimeConstants.Response.DATA_KEY)) {
            JSONObject data = parsedResponse.getJSONObject(PrimeConstants.Response.DATA_KEY);
            if (data != null && data.containsKey(path)) {
                return data.getObject(path, clazz);
            }
        }
        return null;
    }

    /**
     * 获取响应中的数据部分
     *
     * @param clazz 目标类型
     * @param <T>   类型参数
     * @return 转换后的对象
     */
    public <T> T dataParseResponse(Class<T> clazz) {
        if (parsedResponse != null && parsedResponse.containsKey(PrimeConstants.Response.DATA_KEY)) {
            return parsedResponse.getObject(PrimeConstants.Response.DATA_KEY, clazz);
        }
        return null;
    }

    public Integer getHttpCode() {
        return httpCode;
    }

    public void setHttpCode(Integer httpCode) {
        this.httpCode = httpCode;
    }

    public String getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody;
        parseResponse();
    }

    /**
     * 获取原始解析后的JSON对象
     *
     * @return JSON对象
     */
    public JSONObject getParsedResponse() {
        return parsedResponse;
    }

    /**
     * 解析响应体为JSON对象
     */
    private void parseResponse() {
        if (StringUtils.isBlank(responseBody)) {
            return;
        }
        try {
            this.parsedResponse = JSON.parseObject(responseBody);
        } catch (Exception e) {
            LOGGER.error("响应体解析异常 res:{}", responseBody, e);
        }
    }

}
