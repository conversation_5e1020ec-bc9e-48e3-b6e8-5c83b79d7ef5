package com.insta360.store.business.outgoing.mq.rma.sender;

import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.outgoing.mq.config.CommonMqConfigure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 售后邮件挽回延时MQ处理
 * @Date 2023/2/13
 */
@Component
public class RmaOrderEmailRecoveryDelayMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(RmaOrderStateChangeSyncMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.rma_order_rma_email_recovery, messageType = MessageSenderType.time)
    private RocketTcpMessageSender rocketTcpMessageSender;

    @Autowired
    private CommonMqConfigure commonMqConfigure;

    /**
     * 售后状态变更消息推送
     *
     * @param refundId
     */
    public void sendRmaOrderEmailRecoveryMessage(Integer refundId) {
        if (Objects.isNull(refundId)) {
            LOGGER.info("[售后业务]售后单创建发送售后挽回延时邮件MQ消息失败,参数错误! 消息体:{}", refundId);
            return;
        }
        String messageId = rocketTcpMessageSender.sendDelayMessage(String.valueOf(refundId), String.valueOf(refundId), commonMqConfigure.getRmaRecoverDelay());
        MqUtils.isBlankMessageIdHandle(messageId, this, refundId);
        if (StringUtils.isBlank(messageId)) {
            LOGGER.info("[售后业务]售后单创建发送售后挽回延时邮件MQ消息失败,消息体:{}", refundId);
            return;
        }
        LOGGER.info("[售后业务]售后单创建发送售后挽回延时邮件MQ消息成功,消息体:{}", refundId);
    }
}
