package com.insta360.store.business.payment.bo;

import com.insta360.store.business.cloud.enums.SubscribeActionType;

/**
 * @Author: wbt
 * @Date: 2022/01/22
 * @Description:
 */
public class ThreeDomainSecureBO {

    private String orderNumber;

    /**
     * checkout获取订单状态
     */
    private String sid;

    /**
     * 订阅动作类型
     */
    private SubscribeActionType subscribeActionType;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public SubscribeActionType getSubscribeActionType() {
        return subscribeActionType;
    }

    public void setSubscribeActionType(SubscribeActionType subscribeActionType) {
        this.subscribeActionType = subscribeActionType;
    }

    @Override
    public String toString() {
        return "ThreeDomainSecureBO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", sid='" + sid + '\'' +
                ", subscribeActionType=" + subscribeActionType +
                '}';
    }
}
