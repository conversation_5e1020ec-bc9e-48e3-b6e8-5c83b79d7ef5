package com.insta360.store.business.integration.lingxing.dto;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 领星订单dto
 *
 * <AUTHOR>
 * @date 2023/10/09
 */
public class LxOrderDTO implements Serializable {

    /**
     * 销售单号
     */
    private String salesNumber;

    /**
     * 物流号
     */
    private String expressCode;

    /**
     * 订单号
     */
    private String lxOrderNumber;

    public String getSalesNumber() {
        return salesNumber;
    }

    public void setSalesNumber(String salesNumber) {
        this.salesNumber = salesNumber;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getLxOrderNumber() {
        return lxOrderNumber;
    }

    public void setLxOrderNumber(String lxOrderNumber) {
        this.lxOrderNumber = lxOrderNumber;
    }

    @Override
    public String toString() {
        return "LxOrderDTO{" +
                "salesNumber='" + salesNumber + '\'' +
                ", expressCode='" + expressCode + '\'' +
                ", lxOrderNumber='" + lxOrderNumber + '\'' +
                '}';
    }
}
