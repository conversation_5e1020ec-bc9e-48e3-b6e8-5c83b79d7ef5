package com.insta360.store.business.prime.bo;

import java.io.Serializable;

/**
 * Prime商品创建业务对象
 * <p>
 * 该类用于封装创建Prime商品所需的基本信息，包括商品图片URL和标题。
 * 在创建Amazon Prime商品时作为数据传输对象使用。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public class PrimeCreateCommodityBO implements Serializable {

    /**
     * 商品图片源URL
     * 用于在Amazon Prime商品页面显示的商品图片地址
     */
    private String sourceImage;

    /**
     * 商品标题
     * 格式通常为"产品名称 + 分隔符 + 套餐名称"
     */
    private String title;

    private String detailUrl;

    public String getSourceImage() {
        return sourceImage;
    }

    public void setSourceImage(String sourceImage) {
        this.sourceImage = sourceImage;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }
}
