package com.insta360.store.business.integration.wto.service.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.integration.wto.enums.IntegrationType;
import com.insta360.store.business.integration.wto.enums.OmsIntegrationBusinessType;
import com.insta360.store.business.integration.wto.enums.WmsIntegrationBusinessType;
import com.insta360.store.business.integration.wto.oms.service.factory.OmsIntegrationFactory;
import com.insta360.store.business.integration.wto.oms.service.handler.OmsService;
import com.insta360.store.business.integration.wto.service.handler.BaseIntegrationServiceHandler;
import com.insta360.store.business.integration.wto.service.handler.OmsIntegrationServiceHandler;
import com.insta360.store.business.integration.wto.service.handler.WmsIntegrationServiceHandler;
import com.insta360.store.business.integration.wto.wms.service.factory.WmsIntegrationFactory;
import com.insta360.store.business.integration.wto.wms.service.handler.WmsService;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2024/11/29
 * @Description:
 */
@Component
public class IntegrationSimpleFactory {

    /**
     * 获取oms平台处理器
     *
     * @return
     */
    public OmsService getOmsService(OmsIntegrationBusinessType omsIntegrationBusinessType) {
        OmsIntegrationFactory omsIntegrationFactory = ApplicationContextHolder.getApplicationContext().getBean(OmsIntegrationFactory.class);
        return omsIntegrationFactory.getIntegrationService(omsIntegrationBusinessType);
    }

    /**
     * 获取wms平台处理器
     *
     * @return
     */
    public WmsService getWmsService(WmsIntegrationBusinessType wmsIntegrationBusinessType) {
        WmsIntegrationFactory wmsIntegrationFactory = ApplicationContextHolder.getApplicationContext().getBean(WmsIntegrationFactory.class);
        return wmsIntegrationFactory.getIntegrationService(wmsIntegrationBusinessType);
    }

    /**
     * 获取平台服务
     *
     * @param integrationType
     * @return
     */
    public BaseIntegrationServiceHandler getIntegrationHandler(IntegrationType integrationType) {
        ApplicationContext applicationContext = ApplicationContextHolder.getApplicationContext();

        switch (integrationType) {
            case OMS_INTEGRATION:
                return applicationContext.getBean(OmsIntegrationServiceHandler.class);
            case WMS_INTEGRATION:
                return applicationContext.getBean(WmsIntegrationServiceHandler.class);
            default:
                throw new InstaException(CommonErrorCode.InvalidParameter);
        }
    }
}
