package com.insta360.store.business.cloud.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 云存储权益变更数据DTO
 * @Date 2024/5/11
 */
public class BenefitChangeDTO implements Serializable {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID缺失")
    private Integer userId;

    /**
     * SKU_ID (类似年度/季度/月度定义)
     */
    @NotBlank(message = "skuId缺失")
    private String skuId;

    /**
     * 平台来源
     */
    @NotBlank(message = "平台来源缺失")
    private String platform;

    /**
     * 权益状态
     * @see com.insta360.store.business.cloud.enums.BenefitStatus
     */
    @NotBlank(message = "权益状态缺失")
    private String status;

    /**
     * 是否启用
     */
    @NotNull(message = "启用状态缺失")
    private Integer enable;

    /**
     * 订阅到期时间
     */
    @NotNull(message = "过期时间缺失")
    private Long expirationTime;

    /**
     * 用户下单地区
     * @see com.insta360.compass.core.enums.InstaCountry
     */
    @NotBlank(message = "用户权益地区缺失")
    private String region;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public Long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Long expirationTime) {
        this.expirationTime = expirationTime;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }
}
