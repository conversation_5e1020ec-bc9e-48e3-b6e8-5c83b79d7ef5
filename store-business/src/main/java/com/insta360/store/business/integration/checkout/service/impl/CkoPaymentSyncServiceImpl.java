package com.insta360.store.business.integration.checkout.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.integration.checkout.configuration.CkoPaymentOrderManageConfiguration;
import com.insta360.store.business.integration.checkout.lib.request.CreateGetCkoOrderDetailRequest;
import com.insta360.store.business.integration.checkout.lib.response.CreateGetCkoOrderDetailResponse;
import com.insta360.store.business.integration.checkout.service.CkoPaymentSyncService;
import com.insta360.store.business.meta.enums.StoreSdkCallApiType;
import com.insta360.store.business.meta.enums.StoreSdkCallBusinessType;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.payment.lib.checkout.CheckoutConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: wbt
 * @Date: 2021/11/23
 * @Description:
 */
@Service
public class CkoPaymentSyncServiceImpl implements CkoPaymentSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CkoPaymentSyncServiceImpl.class);

    @Autowired
    StoreSdkCallRecordService storeSdkCallRecordService;

    @Autowired
    CkoPaymentOrderManageConfiguration ckoPaymentOrderManageConfiguration;

    @Override
    public CreateGetCkoOrderDetailResponse getCkoOrderStateResponse(CheckoutConfig checkoutConfig, String orderNumber, String payId) {
        if (StringUtil.isBlank(payId)) {
            throw new InstaException(-1, "checkout order payId为空。订单号：" + orderNumber);
        }

        CreateGetCkoOrderDetailRequest request = new CreateGetCkoOrderDetailRequest(ckoPaymentOrderManageConfiguration, payId);
        String result = null;
        try {
            LOGGER.info("checkout order detail request:{}", request);
            result = request.executeGet(checkoutConfig);
            LOGGER.info("checkout order detail result:{}", result);
            return CreateGetCkoOrderDetailResponse.parse(result);
        } catch (Exception e) {
            FeiShuMessageUtil.storeGeneralMessage("cko订单详细信息接口数据转换失败，请手动同步。订单：" + orderNumber + ", result：" + result, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw e;
        } finally {
            // 保存调用记录
            storeSdkCallRecordService.saveSdkCallRecord(orderNumber, orderNumber, JSONObject.toJSONString(request), result, StoreSdkCallBusinessType.CKO_ORDER_STATE, StoreSdkCallApiType.FORTER_ORDER_STATUS);
        }
    }
}
