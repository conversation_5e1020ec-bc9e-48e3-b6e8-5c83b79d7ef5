package com.insta360.store.business.integration.attentive.service.Handler;

import cn.hutool.http.HttpResponse;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.integration.attentive.config.AttentiveConfiguration;
import com.insta360.store.business.integration.attentive.enums.AttentiveCountryType;
import com.insta360.store.business.integration.attentive.enums.AttentiveOperationType;
import com.insta360.store.business.integration.attentive.enums.AttentiveResponseCode;
import com.insta360.store.business.integration.attentive.lib.module.AttentiveSubscribeLocale;
import com.insta360.store.business.integration.attentive.lib.module.AttentiveSubscribeUser;
import com.insta360.store.business.integration.attentive.lib.request.AttentiveSubscribeInterestRequest;
import com.insta360.store.business.integration.attentive.lib.request.AttentiveSubscribeUserRequest;
import com.insta360.store.business.integration.attentive.service.helper.AttentiveHelper;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.subscribe.dto.EmailSubscribeDTO;
import com.insta360.store.business.user.enums.AttentiveSubmitResultType;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.model.EmailSubscribeInterest;
import com.insta360.store.business.user.service.EmailSubscribeInterestService;
import com.insta360.store.business.user.service.EmailSubscribeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2023-11-04 17:16
 */
public abstract class BaseAttentiveHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseAttentiveHandler.class);

    /**
     * 订阅类型
     */
    protected static final String SUBSCRIPTION_TYPE = "MARKETING";

    @Autowired
    AttentiveConfiguration attentiveConfiguration;

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    EmailSubscribeInterestService emailSubscribeInterestService;

    @Autowired
    AttentiveHelper attentiveHelper;

    /**
     * 上报数据统一入口
     *
     * @param emailSubscribe
     * @param emailSubscribeParam
     */
    public void subscribeUser(EmailSubscribe emailSubscribe, EmailSubscribeDTO emailSubscribeParam) {
        Integer operationCode = emailSubscribeParam.getOperationCode();
        AttentiveOperationType operationType = AttentiveOperationType.parse(operationCode);
        if (Objects.isNull(operationType)) {
            return;
        }

        // 只上报指定国家的
        AttentiveCountryType attentiveCountryType = AttentiveCountryType.parse(emailSubscribe.getCountry());
        if (Objects.isNull(attentiveCountryType)) {
            LOGGER.info("[attentive]上报失败,原因:非指定国家的兴趣");
            return;
        }

        AttentiveSubscribeUser attentiveSubscribeUser = new AttentiveSubscribeUser();
        String phoneNumber = emailSubscribe.getPhoneNumber();
        if (StringUtil.isBlank(phoneNumber)) {
            LOGGER.info("[attentive]上报失败,原因:手机号为空");
            // 没有值不上报
            return;
        }
        attentiveSubscribeUser.setPhone(phoneNumber);

        AttentiveSubscribeLocale attentiveSubscribeLocale = new AttentiveSubscribeLocale();
        attentiveSubscribeLocale.setCountry(attentiveCountryType.name());
        attentiveSubscribeLocale.setLanguage(attentiveCountryType.getLanguage());

        AttentiveSubscribeUserRequest userRequest = new AttentiveSubscribeUserRequest(attentiveCountryType.name(), attentiveConfiguration);
        userRequest.setUser(attentiveSubscribeUser);
        userRequest.setLocale(attentiveSubscribeLocale);
        userRequest.setSubscriptionType(SUBSCRIPTION_TYPE);
        userRequest.setSubscribeId(emailSubscribe.getId());

        switch (operationType) {
            case OnlySubscribe:
                reportUser(userRequest, emailSubscribe, null);
                break;
            case OnlyAttribute:
                reportAttribute(null, emailSubscribe, attentiveSubscribeUser);
                break;
            case SubscribeAndAttribute:
                reportUser(userRequest, emailSubscribe, null);
                reportAttribute(null, emailSubscribe, attentiveSubscribeUser);
                break;
            default:
                break;
        }
    }

    /**
     * 用户订阅
     *
     * @param userRequest
     * @param emailSubscribe
     * @param attentiveSubscribeUser
     */
    public void reportUser(AttentiveSubscribeUserRequest userRequest,
                           EmailSubscribe emailSubscribe,
                           AttentiveSubscribeUser attentiveSubscribeUser) {
        HttpResponse httpResponse = null;
        try {
            httpResponse = userRequest.executePost();
        } catch (Exception e) {
            LOGGER.error(String.format("[attentive]user上报失败,存在异常,request:%s", userRequest.toString()), e);
            FeiShuMessageUtil.storeGeneralMessage("attentive user 上报失败,存在异常，request：" +
                    userRequest, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
        }
        if (Objects.isNull(httpResponse)) {
            return;
        }

        Integer subscribeId = userRequest.getSubscribeId();

        int status = httpResponse.getStatus();
        if (!AttentiveResponseCode.AcceptCode.getCode().equals(status)) {
            LOGGER.error("[attentive]user上报失败,request:" + userRequest + "response:" + httpResponse);
            // 修改状态为失败
            emailSubscribeService.updateSubscribeStatusById(subscribeId, AttentiveSubmitResultType.SubmitFailed.getCode());
            return;
        }

        // 修改emailSubscribe 订阅状态
        emailSubscribeService.updateSubscribeStatusById(subscribeId, AttentiveSubmitResultType.SubmitSuccess.getCode());
        LOGGER.info("[attentive]user上报成功,response:" + httpResponse.body());
    }

    /**
     * 标记tag
     *
     * @param interestList
     * @param emailSubscribe
     * @param attentiveSubscribeUser
     */
    public void reportAttribute(List<EmailSubscribeInterest> interestList, EmailSubscribe emailSubscribe,
                                AttentiveSubscribeUser attentiveSubscribeUser) {
        Map<String, String> attentivePropertiesMap = new HashMap<>();
        attentivePropertiesMap.put(emailSubscribe.getActivityPage(), emailSubscribe.getActivityPage());
        attentivePropertiesMap.put("flag", "store");
        attentivePropertiesMap.put("source", "popWindow");

        AttentiveSubscribeInterestRequest interestRequest =
                new AttentiveSubscribeInterestRequest(AttentiveCountryType.US.name(), attentiveConfiguration);
        interestRequest.setProperties(attentivePropertiesMap);
        interestRequest.setUser(attentiveSubscribeUser);

        // 批量上报用户数据至attentive
        HttpResponse response = interestRequest.executePost();
        int status = response.getStatus();
        if (!AttentiveResponseCode.AcceptInterestCode.getCode().equals(status)) {
            LOGGER.error(String.format("[attentive]Attribute上报失败。request：[%S],response:[%S]",
                    interestRequest, response));
            FeiShuMessageUtil.storeGeneralMessage("attentiveAttribute上报失败。request：" + interestRequest
                    + "response:" + response, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return;
        }
        LOGGER.info("attentive attribute 上报成功");
    }
}
