package com.insta360.store.business.integration.avalara.service.helper;

import com.insta360.store.business.integration.avalara.bo.*;
import com.insta360.store.business.meta.service.AvalaraLineTaxService;
import com.insta360.store.business.meta.service.AvalaraTaxInfoService;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.rma.bo.RmaOrderBO;
import com.insta360.store.business.user.model.StoreAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/6/17
 */
@Component
public class AvalaraLineResolveHelper {

    @Autowired
    AvalaraLineTaxService avalaraLineTaxService;

    @Autowired
    AvalaraTaxInfoService avalaraTaxInfoService;

    @Autowired
    OrderItemPreResolveHelper orderItemPreResolveHelper;

    @Autowired
    AvalaraLineAmountApportionmentHelper avalaraLineAmountApportionmentHelper;

    /**
     * 结账页-商品解析
     *
     * @param avalaraParseLineItem
     * @return
     */
    public List<AvalaraLineBO> parseAvalaraLines(ParseLineItemBO avalaraParseLineItem) {
        // 解析预购买商品
        List<OrderItem> orderItemList = orderItemPreResolveHelper.orderSheetItemsResolve(avalaraParseLineItem.getSheetItems(), avalaraParseLineItem.getCountry());
        return avalaraLineAmountApportionmentHelper.avalaraItemAmountResolve(avalaraParseLineItem, orderItemList);
    }

    /**
     * 下单过程中-商品解析
     *
     * @param orderCreation
     * @return
     */
    public List<AvalaraLineBO> parseAvalaraLines(OrderCreation orderCreation) {
        return avalaraLineAmountApportionmentHelper.avalaraItemAmountResolve(orderCreation);
    }

    /**
     * 订单支付后-商品解析
     *
     * @param avalaraOrderPayedCommitBo
     * @return
     */
    public AvalaraPaymentLineBO parseAvalaraLines(AvalaraOrderPayedCommitBO avalaraOrderPayedCommitBo) {
        return avalaraLineAmountApportionmentHelper.avalaraItemAmountResolve(avalaraOrderPayedCommitBo);
    }

    /**
     * 售后退款完成-商品解析
     *
     * @param rmaOrder
     * @return
     */
    public AvalaraRefundOrderBO refundAvalaraLines(RmaOrderBO rmaOrder) {
        return avalaraLineAmountApportionmentHelper.avalaraItemAmountResolve(rmaOrder);
    }
}
