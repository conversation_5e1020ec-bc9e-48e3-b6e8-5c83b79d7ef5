package com.insta360.store.business.transcode.service.impl.handler;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.dto.CommodityDisplayDTO;
import com.insta360.store.business.commodity.enums.DisplayCompressState;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.configuration.utils.UrlUtils;
import com.insta360.store.business.meta.enums.OSSFileUrlEnum;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.transcode.dto.TranscodeMessageDTO;
import com.insta360.store.business.transcode.exception.TranscodeErrorCode;
import com.insta360.store.business.transcode.service.impl.BaseTranscodeHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2024-05-24 11:46
 */
@Component
public class DisplayTranscodeHandler extends BaseTranscodeHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DisplayTranscodeHandler.class);

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Override
    public void saveTranscodeResult(TranscodeMessageDTO transcodeMessageParam) {
        CommodityDisplayDTO commodityDisplayParam = transcodeMessageParam.getCommodityDisplayParam();
        if (Objects.isNull(commodityDisplayParam)) {
            LOGGER.error("[主图转码]转码结果为空:{}", transcodeMessageParam);
            return;
        }
        LOGGER.info("[主图转码]转码结果数据回填:{}", commodityDisplayParam);

        Integer displayId = commodityDisplayParam.getId();
        CommodityDisplay commodityDisplay = commodityDisplayService.getById(displayId);
        if (Objects.isNull(commodityDisplay)) {
            LOGGER.error(String.format("[主图转码]展示图id不存在:%s", displayId));
            return;
        }

        try {
            String urlM = commodityDisplayParam.getUrlM();
            String urlS = commodityDisplayParam.getUrlS();
            if (StringUtil.isBlank(urlM) || StringUtil.isBlank(urlS)) {
                LOGGER.error(String.format("[主图转码]中图和小图结果为空,数据:%s", commodityDisplayParam));
                throw new InstaException(CommodityErrorCode.DisplayCompressFailed);
            }

            // 数据落库
            urlM = checkTranscodeUrl(urlM, displayId);
            urlS = checkTranscodeUrl(urlS, displayId);

            commodityDisplay.setUrlM(urlM);
            commodityDisplay.setUrl(urlM);
            commodityDisplay.setUrlS(urlS);
            commodityDisplay.setCompressState(DisplayCompressState.ossOverseasSync.getCode());
            commodityDisplayService.updateByDisplayId(commodityDisplay);
            LOGGER.info(String.format("[主图转码]展示图转码结果落库:%s", commodityDisplay));
        } catch (Exception exception) {
            commodityDisplay.setCompressState(DisplayCompressState.failed.getCode());
            commodityDisplayService.updateByDisplayId(commodityDisplay);
            LOGGER.error(String.format("[主图转码]展示图id:%s,转码失败,异常信息:%s", displayId, exception.getMessage()), exception);
            FeiShuMessageUtil.storeGeneralMessage(String.format("【套餐主图】转码失败，主图id：%s", displayId), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
        }
    }

    /**
     * 校验链接是否存在
     *
     * @param transcodeUrl
     * @param displayId
     * @return
     */
    private String checkTranscodeUrl(String transcodeUrl, Integer displayId) {
        boolean urlExists = UrlUtils.urlExists(transcodeUrl);
        if (!urlExists) {
            LOGGER.error(String.format("[主图转码]展示图id:%s,转码结果文件不存在:%s", displayId, transcodeUrl));
            throw new InstaException(TranscodeErrorCode.FileNotFoundException);
        }

        // 转为cdn
        transcodeUrl = UrlUtils.replaceUrlPrefixOssToCdn(transcodeUrl, OSSFileUrlEnum.image);
        LOGGER.info(String.format("[主图转码]展示图id:%s,替换url前缀:%s", displayId, transcodeUrl));
        return transcodeUrl;
    }
}
