package com.insta360.store.business.product.service.impl.helper;

import com.insta360.store.business.product.dto.ProductMainVideoDTO;
import com.insta360.store.business.product.model.*;
import com.insta360.store.business.product.service.ProductMainVideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2024-05-10 11:40
 */
@Component
public class ProductMainVideoHelper {

    @Autowired
    ProductMainVideoService productMainVideoService;

    @Autowired
    ProductMainVideoConditionHelper productMainVideoConditionHelper;

    /**
     * 新增产品主视频
     *
     * @param productMainVideoParam
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void insertMainVideo(ProductMainVideoDTO productMainVideoParam) {
        Integer product = productMainVideoParam.getProduct();
        String language = productMainVideoParam.getLanguage();
        Integer orderIndex = productMainVideoParam.getOrderIndex();
        ProductMainVideo productMainVideo = productMainVideoParam.getPojoObject();
        List<ProductMainVideo> productMainVideos = productMainVideoService.listByProductIndex(product, language, orderIndex);
        productMainVideos.forEach(mainVideo -> {
            Integer index = mainVideo.getOrderIndex();
            mainVideo.setOrderIndex(++index);
        });
        // 排序&主视频保存
        productMainVideoService.updateOrderIndexByIds(productMainVideos);
        productMainVideoService.saveMainVideo(productMainVideo);

        // 保存条件
        productMainVideoConditionHelper.saveCondition(productMainVideo.getId(), productMainVideoParam);
    }

    /**
     * 编辑产品主视频
     *
     * @param productMainVideoParam
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateMainVideo(ProductMainVideoDTO productMainVideoParam) {
        Integer mainVideoId = productMainVideoParam.getId();
        ProductMainVideo productMainVideo = productMainVideoService.getById(mainVideoId);
        if (Objects.isNull(productMainVideo)) {
            return;
        }

        ProductMainVideo productMainVideoData = productMainVideoParam.getPojoObject();
        Integer fromIndex = productMainVideo.getOrderIndex();
        Integer toIndex = productMainVideoData.getOrderIndex();
        Integer product = productMainVideoData.getProduct();

        // 序号相等只更新数据
        if (toIndex.equals(fromIndex)) {
            productMainVideoService.updateByMainVideoId(productMainVideoData);
            // 删除历史数据
            productMainVideoConditionHelper.clearCondition(mainVideoId);
            // 保存国家和套餐
            productMainVideoConditionHelper.saveCondition(mainVideoId, productMainVideoParam);
            return;
        }

        String language = productMainVideoParam.getLanguage();
        List<ProductMainVideo> productMainVideos = null;
        // 大 -> 小 list最少有自己
        if (fromIndex > toIndex) {
            productMainVideos = productMainVideoService.listByProductRange(product, language, toIndex, fromIndex);
            productMainVideos.forEach(video -> {
                if (video.getId().equals(mainVideoId)) {
                    video.setOrderIndex(toIndex);
                    return;
                }
                Integer orderIndex = video.getOrderIndex();
                video.setOrderIndex(++orderIndex);
            });
        }

        // 小 -> 大
        if (fromIndex < toIndex) {
            productMainVideos = productMainVideoService.listByProductRange(product, language, fromIndex, toIndex);
            productMainVideos.forEach(video -> {
                if (video.getId().equals(mainVideoId)) {
                    video.setOrderIndex(toIndex);
                    return;
                }
                Integer orderIndex = video.getOrderIndex();
                video.setOrderIndex(--orderIndex);
            });
        }

        // update
        productMainVideoService.updateOrderIndexByIds(productMainVideos);
        productMainVideoService.updateByMainVideoId(productMainVideoData);

        // 删除历史数据
        productMainVideoConditionHelper.clearCondition(mainVideoId);
        // 保存国家和套餐
        productMainVideoConditionHelper.saveCondition(mainVideoId, productMainVideoParam);
    }

    /**
     * 删除产品主视频
     *
     * @param productMainVideo
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteMainVideo(ProductMainVideo productMainVideo) {
        Integer orderIndex = productMainVideo.getOrderIndex();
        Integer product = productMainVideo.getProduct();
        String language = productMainVideo.getLanguage();
        List<ProductMainVideo> productMainVideos = productMainVideoService.listByProductIndex(product, language, orderIndex);
        productMainVideos.forEach(video -> {
            Integer index = video.getOrderIndex();
            video.setOrderIndex(--index);
        });

        // 清除条件
        productMainVideoConditionHelper.clearCondition(productMainVideo.getId());

        // 删除主视频
        productMainVideoService.deleteMainVideo(productMainVideo.getId());

        // 更改其他视频排序
        productMainVideoService.updateOrderIndexByIds(productMainVideos);
    }
}
