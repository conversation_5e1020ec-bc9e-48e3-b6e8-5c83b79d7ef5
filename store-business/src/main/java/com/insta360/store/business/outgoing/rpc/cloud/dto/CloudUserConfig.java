package com.insta360.store.business.outgoing.rpc.cloud.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/24
 */
public class CloudUserConfig implements Serializable {

    /**
     * 协议URL
     */
    private String agreementUrl;

    /**
     * 最新协议版本号
     */
    private Integer agreementVersion;

    /**
     * 可用区域
     */
    private List<String> availableRegions;

    /**
     * 用户数据存储地
     */
    private String storeRegion;

    public String getAgreementUrl() {
        return agreementUrl;
    }

    public void setAgreementUrl(String agreementUrl) {
        this.agreementUrl = agreementUrl;
    }

    public Integer getAgreementVersion() {
        return agreementVersion;
    }

    public void setAgreementVersion(Integer agreementVersion) {
        this.agreementVersion = agreementVersion;
    }

    public List<String> getAvailableRegions() {
        return availableRegions;
    }

    public void setAvailableRegions(List<String> availableRegions) {
        this.availableRegions = availableRegions;
    }

    public String getStoreRegion() {
        return storeRegion;
    }

    public void setStoreRegion(String storeRegion) {
        this.storeRegion = storeRegion;
    }

    @Override
    public String toString() {
        return "CloudUserConfig{" +
                "agreementUrl='" + agreementUrl + '\'' +
                ", agreementVersion=" + agreementVersion +
                ", availableRegions=" + availableRegions +
                ", storeRegion='" + storeRegion + '\'' +
                '}';
    }
}
