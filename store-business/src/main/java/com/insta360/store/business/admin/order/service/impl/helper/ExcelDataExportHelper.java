package com.insta360.store.business.admin.order.service.impl.helper;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.merge.OnceAbsoluteMergeStrategy;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.compass.libs.aliyun.oss.enums.EndpointEnum;
import com.insta360.compass.libs.aliyun.oss.enums.ModuleEnum;
import com.insta360.store.business.admin.order.export.OrderExportData;
import com.insta360.store.business.admin.order.export.ResellerOrderExportData;
import com.insta360.store.business.admin.order.service.impl.handler.constant.OrderExportConstant;
import com.insta360.store.business.admin.reseller.export.WithdrawApplyData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2021/8/16
 * @Description:
 */
@Component
public class ExcelDataExportHelper {

    private final static Logger LOGGER = LoggerFactory.getLogger(ExcelDataExportHelper.class);

    @Autowired
    OSSService ossService;

    /**
     * 创建OSS文件
     *
     * @param exportData
     * @param fileName
     * @param sheetName
     * @param clazz
     * @param <T>
     * @return
     * @throws Exception
     */
    public <T> String createOSSFile(List<T> exportData, String fileName, String sheetName, Class<?> clazz) throws Exception {
        String file = URLEncoder.encode(fileName + "_" + System.currentTimeMillis(), "UTF-8");
        if (!FileUtil.exist(OrderExportConstant.PATH)) {
            FileUtil.mkdir(OrderExportConstant.PATH);
        }
        File targetFile = new File(OrderExportConstant.PATH + file + ".xlsx");
        EasyExcel.write(new FileOutputStream(targetFile), clazz).sheet(sheetName).doWrite(exportData);

        return doCreateOssFile(targetFile);
    }

    /**
     * Create multiple sheets in the same table to write data
     * @param fileName
     * @param sheetNameList
     * @param classList
     * @param exportData
     * @return
     * @throws UnsupportedEncodingException
     */
    public String createOSSFileWithLotSheets(String fileName, List<String> sheetNameList, List<Class<?>> classList, List<?>... exportData) throws UnsupportedEncodingException {
        if (!FileUtil.exist(OrderExportConstant.PATH)) {
            FileUtil.mkdir(OrderExportConstant.PATH);
        }
        File targetFile = new File(OrderExportConstant.PATH + fileName + ".xlsx");

        // Ensure that the sizes of sheetNameList and classList match the exportData
        if (sheetNameList.size() != exportData.length || classList.size() != exportData.length) {
            LOGGER.error("The size of sheetNameList and classList does not match the size of exportData");
            return null;
        }

        try (FileOutputStream fileOutputStream = new FileOutputStream(targetFile)) {
            ExcelWriter excelWriter = EasyExcel.write(fileOutputStream).build();

            for (int i=0; i<sheetNameList.size(); i++){
                // type judgment
                if (!classList.get(i).isInstance(exportData[i].get(0))){
                    LOGGER.error("The data type written to the current table sheet does not match the parameter type passed in.");
                    return null;
                }
                WriteSheet writeSheet = EasyExcel.writerSheet(i+1, sheetNameList.get(i)).head(classList.get(i)).build();
                LOGGER.info("Writing is the {} writeSheet", i+1);
                excelWriter.write(exportData[i], writeSheet);
            }
            excelWriter.finish();
        } catch (Exception e) {
            LOGGER.error("Write to excel error....", e);
        }

        return doCreateOssFile(targetFile);
    }

    /**
     * 合并单元格处理并返回OSS链接
     *
     * @param exportData
     * @param fileName
     * @param clazz
     * @return
     * @throws UnsupportedEncodingException
     */
    public String mergeStrategy(List<WithdrawApplyData> exportData, String fileName, Class<?> clazz) throws UnsupportedEncodingException {
        String file = URLEncoder.encode(fileName + "_" + System.currentTimeMillis(), "UTF-8");
        if (!FileUtil.exist(OrderExportConstant.PATH)) {
            FileUtil.mkdir(OrderExportConstant.PATH);
        }
        File targetFile = new File(OrderExportConstant.PATH + file + ".xlsx");
        ExcelWriterBuilder writerBuilder = EasyExcel.write(targetFile, clazz);

        // 合并单元格
        int beginIndex = 0;
        while (beginIndex < exportData.size()) {
            String accountInfo = exportData.get(beginIndex).getWithdrawAccountInfo();
            int firstRowIndex = beginIndex;
            int lastRowIndex = exportData.size();
            for (int j = firstRowIndex; j < exportData.size(); j++) {
                WithdrawApplyData model = exportData.get(j);
                if (accountInfo.equals(model.getWithdrawAccountInfo())) {
                    lastRowIndex = j;
                } else {
                    beginIndex = j;
                    break;
                }
            }

            // 两个以上才合并
            if (firstRowIndex != lastRowIndex) {
                writerBuilder.registerWriteHandler(new OnceAbsoluteMergeStrategy(firstRowIndex + 1, lastRowIndex + 1, 9, 9));
                writerBuilder.registerWriteHandler(new OnceAbsoluteMergeStrategy(firstRowIndex + 1, lastRowIndex + 1, 10, 10));
                writerBuilder.registerWriteHandler(new OnceAbsoluteMergeStrategy(firstRowIndex + 1, lastRowIndex + 1, 11, 11));
                writerBuilder.registerWriteHandler(new OnceAbsoluteMergeStrategy(firstRowIndex + 1, lastRowIndex + 1, 12, 12));
                writerBuilder.registerWriteHandler(new OnceAbsoluteMergeStrategy(firstRowIndex + 1, lastRowIndex + 1, 13, 13));

            }

            if (lastRowIndex + 1 == exportData.size()) {
                break;
            }
        }

        writerBuilder.sheet("提现数据").doWrite(exportData);

        return doCreateOssFile(targetFile);
    }

    /**
     * 创建OSS文件
     *
     * @param targetFile
     * @return
     */
    private String doCreateOssFile(File targetFile) {
        String ossUrl = ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, targetFile);
        if (FileUtil.exist(targetFile)) {
            FileUtil.del(targetFile);
        }
        return ossUrl;
    }
}
