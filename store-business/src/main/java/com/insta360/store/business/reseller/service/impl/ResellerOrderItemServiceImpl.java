package com.insta360.store.business.reseller.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.reseller.dao.ResellerOrderItemDao;
import com.insta360.store.business.reseller.model.ResellerOrderItem;
import com.insta360.store.business.reseller.service.ResellerOrderItemService;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @Author: hyc
 * @Date: 2019/2/28
 * @Description:
 */
@Service
public class ResellerOrderItemServiceImpl extends BaseServiceImpl<ResellerOrderItemDao, ResellerOrderItem> implements ResellerOrderItemService {

    @Autowired
    ResellerOrderService orderService;

    @Override
    public List<ResellerOrderItem> listByOrderIds(List<Integer> orderIds) {
        QueryWrapper<ResellerOrderItem> qw = new QueryWrapper<>();
        qw.in("reseller_order", orderIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ResellerOrderItem> getByOrder(Integer resellerOrderId) {
        QueryWrapper<ResellerOrderItem> qw = new QueryWrapper<>();
        qw.eq("reseller_order", resellerOrderId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ResellerOrderItem> getOrderItems(Integer resellerOrderId, Integer productId) {
        QueryWrapper<ResellerOrderItem> qw = new QueryWrapper<>();
        qw.eq(Objects.nonNull(resellerOrderId), "reseller_order", resellerOrderId);
        qw.eq(Objects.nonNull(productId), "product_id", productId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ResellerOrderItem> getResellerOrderItemGift(Integer resellerOrderId, Integer commodityId, Boolean isGift) {
        if(Objects.isNull(resellerOrderId) || Objects.isNull(commodityId) || Objects.isNull(isGift)) {
            return null;
        }
        QueryWrapper<ResellerOrderItem> qw = new QueryWrapper<>();
        qw.eq(Objects.nonNull(resellerOrderId), "reseller_order", resellerOrderId);
        qw.eq((Objects.nonNull(commodityId)), "commodity_id", commodityId);
        qw.eq((Objects.nonNull(isGift)), "is_gift", isGift);
        return baseMapper.selectList(qw);
    }

    @Override
    public ResellerOrderItem getResellerOrderItemByItemId(Integer orderItemId) {
        if (Objects.isNull(orderItemId)) {
            return null;
        }
        QueryWrapper<ResellerOrderItem> qw = new QueryWrapper<>();
        qw.eq("order_item_id", orderItemId);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<ResellerOrderItem> getResellerOrderItems(List<Integer> resellerOrderIds,List<Integer> productIds) {
        if(CollectionUtils.isEmpty(resellerOrderIds) || CollectionUtils.isEmpty(productIds)) {
            return null;
        }
        LambdaQueryWrapper<ResellerOrderItem> qw = new LambdaQueryWrapper<>();
        qw.in(ResellerOrderItem::getResellerOrder,resellerOrderIds);
        qw.in(ResellerOrderItem::getProductId,productIds);
        qw.eq(ResellerOrderItem::getIsGift,false);
        return baseMapper.selectList(qw);
    }


}
