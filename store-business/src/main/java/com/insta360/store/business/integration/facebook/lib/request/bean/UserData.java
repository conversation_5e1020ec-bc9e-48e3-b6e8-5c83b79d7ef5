package com.insta360.store.business.integration.facebook.lib.request.bean;


import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.insta360.store.business.integration.facebook.util.ServerSideApiConstants;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 客户信息实体
 * @Date 2021/8/31
 */
public class UserData implements Serializable {

    @JSONField(name = ServerSideApiConstants.EMAIL)
    private List<String> emails;

    @JSONField(name = ServerSideApiConstants.PHONE_NUMBER)
    private List<String> phones;

    @JSONField(name = ServerSideApiConstants.LAST_NAME)
    private List<String> lastNames;

    @JSONField(name = ServerSideApiConstants.FIRST_NAME)
    private List<String> firstNames;

    @J<PERSON>NField(name = ServerSideApiConstants.CITY)
    private List<String> cities;

    @JSONField(name = ServerSideApiConstants.STATE)
    private List<String> states;

    @JSONField(name = ServerSideApiConstants.ZIP_CODE)
    private List<String> zipcodes;

    @JSONField(name = ServerSideApiConstants.COUNTRY)
    private List<String> countryCodes;

    @JSONField(name = ServerSideApiConstants.CLIENT_IP_ADDRESS)
    private String clientIpAddress;

    @JSONField(name = ServerSideApiConstants.CLIENT_USER_AGENT)
    private String clientUserAgent;

    @JSONField(name = ServerSideApiConstants.FBC)
    private String fbc;

    @JSONField(name = ServerSideApiConstants.FBP)
    private String fbp;

    @JSONField(name = ServerSideApiConstants.SUBSCRIPTION_ID)
    private String subscriptionId;


    public List<String> getEmails() {
        return emails;
    }

    public void setEmails(String email) {
        this.emails = StringUtils.isNotBlank(email) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(email.toLowerCase())) : null;
    }

    public List<String> getPhones() {
        return phones;
    }

    public void setPhones(String phone) {
        this.phones = StringUtils.isNotBlank(phone) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(phone)) : null;
    }

    public List<String> getLastNames() {
        return lastNames;
    }

    public void setLastNames(String lastName) {
        this.lastNames = StringUtils.isNotBlank(lastName) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(lastName.toLowerCase())) : null;
    }

    public List<String> getFirstNames() {
        return firstNames;
    }

    public void setFirstNames(String firstName) {
        this.firstNames = StringUtils.isNotBlank(firstName) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(firstName.toLowerCase())) : null;
    }

    public List<String> getCities() {
        return cities;
    }

    public void setCities(String city) {
        this.cities = StringUtils.isNotBlank(city) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(city)) : null;
    }

    public List<String> getStates() {
        return states;
    }

    public void setStates(String state) {
        this.states = StringUtils.isNotBlank(state) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(state.toLowerCase())) : null;
    }

    public List<String> getZipcodes() {
        return zipcodes;
    }

    public void setZipcodes(String zipcode) {
        this.zipcodes = StringUtils.isNotBlank(zipcode) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(zipcode)) : null;
    }

    public List<String> getCountryCodes() {
        return countryCodes;
    }

    public void setCountryCodes(String countryCode) {
        this.countryCodes = StringUtils.isNotBlank(countryCode) ? Lists.newArrayList(ServerSideApiConstants.digester.digestHex(countryCode.toLowerCase())) : null;
    }

    public String getClientIpAddress() {
        return clientIpAddress;
    }

    public void setClientIpAddress(String clientIpAddress) {
        this.clientIpAddress = clientIpAddress;
    }

    public String getClientUserAgent() {
        return clientUserAgent;
    }

    public void setClientUserAgent(String clientUserAgent) {
        this.clientUserAgent = clientUserAgent;
    }

    public String getFbc() {
        return fbc;
    }

    public void setFbc(String fbc) {
        this.fbc = fbc;
    }

    public String getFbp() {
        return fbp;
    }

    public void setFbp(String fbp) {
        this.fbp = fbp;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }
}
