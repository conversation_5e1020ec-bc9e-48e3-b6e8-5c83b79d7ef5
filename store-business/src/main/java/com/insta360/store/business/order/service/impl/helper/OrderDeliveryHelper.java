package com.insta360.store.business.order.service.impl.helper;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteResultBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsOrderDeliveryBO;
import com.insta360.store.business.integration.wto.oms.exception.OmsErrorCode;
import com.insta360.store.business.integration.wto.oms.lib.response.BaseOmsResponse;
import com.insta360.store.business.integration.wto.oms.service.handler.admin.StoreOrderDeliveryUpdateHandler;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.service.OrderDeliveryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/4
 */
@Component
public class OrderDeliveryHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderDeliveryHelper.class);

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    StoreOrderDeliveryUpdateHandler storeOrderDeliveryUpdateHandler;

    /**
     * 更新订单收货地址
     *
     * @param delivery 收货地址信息对象
     */
    public void updateOrderDelivery(Order order, OrderDelivery delivery) {
        // 获取当前订单状态
        OrderState orderState = order.orderState();
        LOGGER.info("[oms更新地址] 订单号:{} 订单状态:{},订单地址:{}", order.getOrderNumber(), orderState, JSON.toJSONString(delivery));

        // 订单状态为配货中 才更新收货地址
        if (OrderState.prepared.equals(orderState)) {
            // 创建一个新的订单收货地址业务对象
            OmsOrderDeliveryBO omsOrderDeliveryBo = new OmsOrderDeliveryBO();
            // 将传入的收货地址信息复制到业务对象中
            BeanUtils.copyProperties(delivery, omsOrderDeliveryBo);
            // 执行OMS交易以更新收货地址
            OmsExecuteBO omsExecuteBo = new OmsExecuteBO(omsOrderDeliveryBo);
            omsExecuteBo.setOrderId(order.getId());
            OmsExecuteResultBO omsExecuteResultBo = storeOrderDeliveryUpdateHandler.executeOmsTransaction(omsExecuteBo);
            // 获取OMS响应对象
            BaseOmsResponse baseOmsResponse = omsExecuteResultBo.getBaseOmsResponse();

            // 如果OMS响应失败，则记录日志并抛出异常
            if (baseOmsResponse.isFail()) {
                String message = String.format("[oms更新地址]订单号 %s 更新执行失败 message：%s res:%s", order.getOrderNumber(), baseOmsResponse.getMessage(), JSON.toJSONString(baseOmsResponse));
                LOGGER.error(message);
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.CUSTOMER_NOTICE);
                throw new InstaException(OmsErrorCode.OrderDeliveryUpdateException);
            }
        } else {
            // 如果订单状态不允许更新收货地址，则记录日志
            LOGGER.info("[oms更新地址]订单状态不符合更新OMS 订单号:{} 状态:{}", order.getOrderNumber(), orderState);
        }

        // 设置收货地址对应的订单ID
        delivery.setOrder(order.getId());
        // 更新数据库中的收货地址信息
        orderDeliveryService.updateById(delivery);
    }

}
