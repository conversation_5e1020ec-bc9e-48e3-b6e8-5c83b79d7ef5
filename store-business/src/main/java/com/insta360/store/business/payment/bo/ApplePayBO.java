package com.insta360.store.business.payment.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 1/4/24
 * @Description:
 */
public class ApplePayBO implements Serializable {

    /**
     * 卡信息
     */
    @NotNull(message = "data不允许为null")
    @NotBlank(message = "data不允许为空")
    private String data;

    /**
     * 签名
     */
    @NotNull(message = "signature不允许为null")
    @NotBlank(message = "signature不允许为空")
    private String signature;

    /**
     * 公钥hash
     */
    @NotNull(message = "publicKeyHash不允许为null")
    @NotBlank(message = "publicKeyHash不允许为空")
    private String publicKeyHash;

    /**
     * 临时公钥
     */
    @NotNull(message = "ephemeralPublicKey不允许为null")
    @NotBlank(message = "ephemeralPublicKey不允许为空")
    private String ephemeralPublicKey;

    /**
     * 交易号
     */
    @NotNull(message = "transactionId不允许为null")
    @NotBlank(message = "transactionId不允许为空")
    private String transactionId;

    /**
     * 算法版本
     */
    @NotNull(message = "version不允许为null")
    @NotBlank(message = "version不允许为空")
    private String version;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getPublicKeyHash() {
        return publicKeyHash;
    }

    public void setPublicKeyHash(String publicKeyHash) {
        this.publicKeyHash = publicKeyHash;
    }

    public String getEphemeralPublicKey() {
        return ephemeralPublicKey;
    }

    public void setEphemeralPublicKey(String ephemeralPublicKey) {
        this.ephemeralPublicKey = ephemeralPublicKey;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return "ApplePayBO{" +
                "data='" + data + '\'' +
                ", signature='" + signature + '\'' +
                ", publicKeyHash='" + publicKeyHash + '\'' +
                ", ephemeralPublicKey='" + ephemeralPublicKey + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
