package com.insta360.store.business.integration.guanyi.lib.request;

import com.insta360.store.business.integration.guanyi.lib.model.GyReturnOrderItem;
import com.insta360.store.business.integration.guanyi.lib.model.GyReturnOrderRefund;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2020/12/31
 * @Description:
 */
public class GyTradeReturnAddRequest extends GyRequest {

    private static final String METHOD = "gy.erp.trade.return.add";

    private String shop_code;   // 店铺代码

    private String vip_code;    // 会员代码

    private String trade_platform_code;  // 销售订单平台单号

    private Integer return_type;    // 售后类型

    private String type_code;   // 退货原因代码

    private String warehousein_code;    // 退回仓库代码

    private List<GyReturnOrderItem> item_detail;   // 退入商品明细

    private List<GyReturnOrderRefund> refund_detail; // 退款明细

    public GyTradeReturnAddRequest() {
        super(METHOD);
    }

    public static String getMETHOD() {
        return METHOD;
    }

    public String getShop_code() {
        return shop_code;
    }

    public void setShop_code(String shop_code) {
        this.shop_code = shop_code;
    }

    public String getVip_code() {
        return vip_code;
    }

    public void setVip_code(String vip_code) {
        this.vip_code = vip_code;
    }

    public String getTrade_platform_code() {
        return trade_platform_code;
    }

    public void setTrade_platform_code(String trade_platform_code) {
        this.trade_platform_code = trade_platform_code;
    }

    public Integer getReturn_type() {
        return return_type;
    }

    public void setReturn_type(Integer return_type) {
        this.return_type = return_type;
    }

    public String getType_code() {
        return type_code;
    }

    public void setType_code(String type_code) {
        this.type_code = type_code;
    }

    public String getWarehousein_code() {
        return warehousein_code;
    }

    public void setWarehousein_code(String warehousein_code) {
        this.warehousein_code = warehousein_code;
    }

    public List<GyReturnOrderItem> getItem_detail() {
        return item_detail;
    }

    public void setItem_detail(List<GyReturnOrderItem> item_detail) {
        this.item_detail = item_detail;
    }

    public List<GyReturnOrderRefund> getRefund_detail() {
        return refund_detail;
    }

    public void setRefund_detail(List<GyReturnOrderRefund> refund_detail) {
        this.refund_detail = refund_detail;
    }

    @Override
    public String toString() {
        return "GyTradeReturnAddRequest{" +
                "shop_code='" + shop_code + '\'' +
                ", vip_code='" + vip_code + '\'' +
                ", trade_platform_code='" + trade_platform_code + '\'' +
                ", return_type=" + return_type +
                ", type_code='" + type_code + '\'' +
                ", warehousein_code='" + warehousein_code + '\'' +
                ", item_detail=" + item_detail +
                ", refund_detail=" + refund_detail +
                '}';
    }
}
