package com.insta360.store.business.payment.service.impl.channel;

import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.bo.PaymentChannelBO;
import com.insta360.store.business.payment.enums.PaymentBusinessType;
import com.insta360.store.business.payment.service.impl.helper.CkoPaymentHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 1/9/24
 * @Description:
 */
@Component
public class CkoPaymentChannelHandler extends BasePaymentChannelHandler {

    @Autowired
    CkoPaymentHelper ckoPaymentHelper;

    @Override
    public PaymentChannel getPaymentChannel(PaymentChannelBO paymentChannelParam) {
        PaymentBusinessType paymentBusinessType = paymentChannelParam.getPaymentBusinessType();
        if (Objects.isNull(paymentBusinessType)) {
            return ckoPaymentHelper.getCkoNormalChannel(paymentChannelParam.getOrder());
        }

        switch (paymentBusinessType) {
            case NORMAL_PAY:
                return getNormalPaymentChannel(paymentChannelParam.getOrder(), paymentChannelParam.getPayChannel());
            case SUBSCRIBE_PAY:
                return getSubscribePaymentChannel(paymentChannelParam.getOrder(), paymentChannelParam.getPayChannel());
            default:
                return PaymentChannel.cko_credit_card;
        }
    }

    /**
     * 订阅支付渠道
     *
     * @param order
     * @param payChannel
     * @return
     */
    private PaymentChannel getSubscribePaymentChannel(Order order, PaymentChannel payChannel) {
        // Google Pay渠道解析
        if (PaymentChannel.isCkoGPChannel(payChannel)) {
            return ckoPaymentHelper.getCkoGpSubscribeChannel(order);
        }

        // Apple Pay渠道解析
        if (PaymentChannel.isCkoAPChannel(payChannel)) {
            return ckoPaymentHelper.getCkoApSubscribeChannel(order);
        }
        return ckoPaymentHelper.getCkoSubscribeChannel(order);
    }

    /**
     * 普通支付渠道
     *
     * @param order
     * @param payChannel
     * @return
     */
    private PaymentChannel getNormalPaymentChannel(Order order, PaymentChannel payChannel) {
        // Google Pay渠道解析
        if (PaymentChannel.isCkoGPChannel(payChannel)) {
            return ckoPaymentHelper.getCkoGpChannel(order);
        }

        //Apple Pay 渠道解析
        if (PaymentChannel.isCkoAPChannel(payChannel)) {
            return ckoPaymentHelper.getCkoApChannel(order);
        }
        return ckoPaymentHelper.getCkoNormalChannel(order);
    }
}
