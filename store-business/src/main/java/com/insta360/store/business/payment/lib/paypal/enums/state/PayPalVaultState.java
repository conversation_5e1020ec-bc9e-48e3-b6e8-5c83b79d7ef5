package com.insta360.store.business.payment.lib.paypal.enums.state;

/**
 * @Author: wkx
 * @Date: 2024/06/11
 * @Description:
 */
public enum PayPalVaultState {

    /**
     * pay token已保存至vault capture同步返回
     */
    VAULTED("VAULTED", "pay token已保存至vault capture同步返回"),

    /**
     * 用户已批准，capture没有及时返回创建的vault id，需依赖webhook通知
     */
    APPROVED("APPROVED", "用户已批准，capture没有及时返回创建的vault id，需依赖webhook通知");

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述
     */
    private final String detail;

    PayPalVaultState(String name, String detail) {
        this.name = name;
        this.detail = detail;
    }

    public String getName() {
        return name;
    }

    public String getDetail() {
        return detail;
    }
}
