package com.insta360.store.business.payment.lib.klarna.request;

import com.insta360.store.business.payment.lib.klarna.config.KlarnaPaymentConfiguration;
import com.insta360.store.business.payment.lib.klarna.module.KlarnaShippingInfo;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/04/11
 * @Description: 订单交易捕获
 */
public class KlarnaOrderCaptureRequest extends BaseKlarnaPaymentRequest {

    /**
     * 接口路径
     */
    private static final String METHOD = "/ordermanagement/v1/orders/%s/captures";

    /**
     * 捕获金额
     */
    private Integer captured_amount;

    /**
     * 物流信息
     */
    private List<KlarnaShippingInfo> shipping_info;

    public KlarnaOrderCaptureRequest(String orderId, KlarnaPaymentConfiguration configuration) {
        super(String.format(METHOD, orderId), configuration);
    }

    public Integer getCaptured_amount() {
        return captured_amount;
    }

    public void setCaptured_amount(Integer captured_amount) {
        this.captured_amount = captured_amount;
    }

    public List<KlarnaShippingInfo> getShipping_info() {
        return shipping_info;
    }

    public void setShipping_info(List<KlarnaShippingInfo> shipping_info) {
        this.shipping_info = shipping_info;
    }

    @Override
    public String toString() {
        return "KlarnaOrderCaptureRequest{" +
                "method='" + method + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                ", captured_amount=" + captured_amount +
                ", shipping_info=" + shipping_info +
                '}';
    }
}
