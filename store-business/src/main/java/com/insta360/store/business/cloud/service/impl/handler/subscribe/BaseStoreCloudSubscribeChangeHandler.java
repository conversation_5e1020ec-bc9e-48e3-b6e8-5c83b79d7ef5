package com.insta360.store.business.cloud.service.impl.handler.subscribe;

import com.insta360.store.business.cloud.model.CloudStorageSubscribe;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/24
 */
public abstract class BaseStoreCloudSubscribeChangeHandler extends BaseStoreCloudSubscribeHandler {

    @Override
    public void updateSubscribe(CloudStorageSubscribe cloudStorageSubscribe, String notifyReason) {
    }

    @Override
    public Boolean handleSubscribeFinalStep(CloudStorageSubscribe storageSubscribe) {
        return Boolean.TRUE;
    }

    @Override
    protected void updateExecuteMark(CloudStorageSubscribe storageSubscribe) {
    }
}
