package com.insta360.store.business.payment.lib.checkout.model;

/**
 * @Author: wkx
 * @Date: 3/12/24
 * @Description:
 */
public class GPTokenData {

    /**
     * 签名
     */
    private String signature;

    /**
     * 算法版本
     */
    private String protocolVersion;

    /**
     * 签名详情
     */
    private String signedMessage;

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getProtocolVersion() {
        return protocolVersion;
    }

    public void setProtocolVersion(String protocolVersion) {
        this.protocolVersion = protocolVersion;
    }

    public String getSignedMessage() {
        return signedMessage;
    }

    public void setSignedMessage(String signedMessage) {
        this.signedMessage = signedMessage;
    }

    @Override
    public String toString() {
        return "GPTokenData{" +
                "signature='" + signature + '\'' +
                ", protocolVersion='" + protocolVersion + '\'' +
                ", signedMessage='" + signedMessage + '\'' +
                '}';
    }
}
