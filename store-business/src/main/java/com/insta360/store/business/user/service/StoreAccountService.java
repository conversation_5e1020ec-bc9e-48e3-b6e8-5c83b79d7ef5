package com.insta360.store.business.user.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.user.model.StoreAccount;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
public interface StoreAccountService extends BaseService<StoreAccount> {

    /**
     * 获取用户绑定了商城小程序的openid
     *
     * @param accountId
     * @return 没绑定过则返回null
     */
    String getUserWeappOpenid(Integer userId);

    /**
     * 获取用户绑定了社区小程序的openid（骑行活动用）
     *
     * @param accountId
     * @param appId
     * @return 没绑定过则返回null
     */
    String getUserWeappOpenid(Integer userId, String appId);

    /**
     * 获取用户绑定了社区小程序的openid（骑行活动用）
     *
     * @param userId
     * @param appId
     * @return 没绑定过则返回null
     */
    String getUserWeappOpenidByUserId(Integer userId, String appId);

    /**
     * 根据insta id 获取store account
     *
     * @param instaAccountId
     * @return
     */
    StoreAccount getByInstaAccountId(Integer instaAccountId);

    /**
     * 使用 user token 创建商城用户
     *
     * @param instaAccount
     * @param username
     * @param language
     * @param country
     * @return
     */
    StoreAccount createAccount(Integer instaAccount, String username, InstaCountry country, InstaLanguage language);
}

