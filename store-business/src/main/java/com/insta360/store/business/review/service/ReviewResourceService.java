package com.insta360.store.business.review.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.review.enums.ReviewResourceStateEnum;
import com.insta360.store.business.review.model.ReviewResource;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-05-25
 * @Description:
 */
public interface ReviewResourceService extends BaseService<ReviewResource> {

    /**
     * 获取 允许展示到客户端(审核通过、转码完成) 评论媒体资源集合
     *
     * @param reviewIds 评论id
     * @return {@link List}<{@link ReviewResource}>
     */
    List<ReviewResource> listAllowResourceByReviewIds(List<Integer> reviewIds);

    /**
     * 更新资源状态
     *
     * @param reviewResourceIds 评论资源id
     * @param reviewId          评论id
     */
    void updateResourceStateReleased(Integer reviewId, List<Integer> reviewResourceIds);

    /**
     * 获取 评论媒体资源集合
     *
     * @param reviewIds 评论id
     * @return {@link List}<{@link ReviewResource}>
     */
    List<ReviewResource> listResourceByReviewIds(List<Integer> reviewIds);

    /**
     * 更新资源状态
     *
     * @param reviewId          评论id
     * @param resourceStateEnum 资源状态枚举
     */
    void updateResourceState(Integer reviewId, ReviewResourceStateEnum resourceStateEnum);

    /**
     * 获取未转码完成的资源
     *
     * @param reviewId 评论id
     * @return {@link ReviewResource}
     */
    ReviewResource getUnCompression(Integer reviewId);

    /**
     * 获取资源列表通过评论id
     *
     * @param reviewId 评论id
     * @return {@link List}<{@link ReviewResource}>
     */
    List<ReviewResource> listResourceByReviewId(Integer reviewId);

    /**
     * 批量更新资源
     *
     * @param reviewResources 评论资源
     */
    void updateReviewResourceBatchById(List<ReviewResource> reviewResources);
}
