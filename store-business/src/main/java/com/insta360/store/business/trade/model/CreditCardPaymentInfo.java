package com.insta360.store.business.trade.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-12-03
 * @Description:
 */
@TableName("credit_card_payment_info")
public class CreditCardPaymentInfo extends BaseModel<CreditCardPaymentInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 支付卡种
     */
    private String creditCardType;

    /**
     * 支付机构
     */
    private String payMethod;

    /**
     * 支付通道（）
     */
    private Integer payChannelId;

    /**
     * 获取权重时分配的随机数
     */
    private Integer randomValue;

    /**
     * 订单支付时分配的权重
     */
    private Integer payWeight;

    /**
     * 卡bin
     */
    private String cardBin;

    /**
     * 下单地区
     */
    private String orderCountry;

    /**
     * forter决策（Approved or Decline or Not Reviewed）
     */
    private String forterLastDecision;

    /**
     * forter决策（是否3d）
     */
    private String forterLastRecommendation;

    /**
     * 是否走3D（0：走3d；1：不走3d）
     */
    private Boolean threeDomainSecure;

    /**
     * eci值
     */
    private String paymentEci;

    /**
     * 支付授权编码
     */
    private String paymentAuthCode;

    /**
     * 支付授权结果
     */
    private String paymentAuthText;

    /**
     * 支付授权时间
     */
    private LocalDateTime paymentAuthTime;

    /**
     * 订单支付结果（0：失败；1：成功）
     */
    private Boolean paymentResult;

    /**
     * 订单发起支付请求的时间
     */
    private LocalDateTime orderPayRequestTime;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;

    /**
     * forter最后一次决策时间
     */
    private LocalDateTime forterLastDecisionTime;

    /**
     * 支付时间
     */
    private LocalDateTime orderPayTime;

    /**
     * 本条记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 本条记录修改时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getCreditCardType() {
        return creditCardType;
    }

    public void setCreditCardType(String creditCardType) {
        this.creditCardType = creditCardType;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getPayChannelId() {
        return payChannelId;
    }

    public void setPayChannelId(Integer payChannelId) {
        this.payChannelId = payChannelId;
    }

    public Integer getRandomValue() {
        return randomValue;
    }

    public void setRandomValue(Integer randomValue) {
        this.randomValue = randomValue;
    }

    public Integer getPayWeight() {
        return payWeight;
    }

    public void setPayWeight(Integer payWeight) {
        this.payWeight = payWeight;
    }

    public String getCardBin() {
        return cardBin;
    }

    public void setCardBin(String cardBin) {
        this.cardBin = cardBin;
    }

    public String getOrderCountry() {
        return orderCountry;
    }

    public void setOrderCountry(String orderCountry) {
        this.orderCountry = orderCountry;
    }

    public String getForterLastDecision() {
        return forterLastDecision;
    }

    public void setForterLastDecision(String forterLastDecision) {
        this.forterLastDecision = forterLastDecision;
    }

    public String getForterLastRecommendation() {
        return forterLastRecommendation;
    }

    public void setForterLastRecommendation(String forterLastRecommendation) {
        this.forterLastRecommendation = forterLastRecommendation;
    }

    public Boolean getThreeDomainSecure() {
        return threeDomainSecure;
    }

    public void setThreeDomainSecure(Boolean threeDomainSecure) {
        this.threeDomainSecure = threeDomainSecure;
    }

    public String getPaymentEci() {
        return paymentEci;
    }

    public void setPaymentEci(String paymentEci) {
        this.paymentEci = paymentEci;
    }

    public String getPaymentAuthCode() {
        return paymentAuthCode;
    }

    public void setPaymentAuthCode(String paymentAuthCode) {
        this.paymentAuthCode = paymentAuthCode;
    }

    public String getPaymentAuthText() {
        return paymentAuthText;
    }

    public void setPaymentAuthText(String paymentAuthText) {
        this.paymentAuthText = paymentAuthText;
    }

    public LocalDateTime getPaymentAuthTime() {
        return paymentAuthTime;
    }

    public void setPaymentAuthTime(LocalDateTime paymentAuthTime) {
        this.paymentAuthTime = paymentAuthTime;
    }

    public Boolean getPaymentResult() {
        return paymentResult;
    }

    public void setPaymentResult(Boolean paymentResult) {
        this.paymentResult = paymentResult;
    }

    public LocalDateTime getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(LocalDateTime orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public LocalDateTime getOrderPayRequestTime() {
        return orderPayRequestTime;
    }

    public void setOrderPayRequestTime(LocalDateTime orderPayRequestTime) {
        this.orderPayRequestTime = orderPayRequestTime;
    }

    public LocalDateTime getForterLastDecisionTime() {
        return forterLastDecisionTime;
    }

    public void setForterLastDecisionTime(LocalDateTime forterLastDecisionTime) {
        this.forterLastDecisionTime = forterLastDecisionTime;
    }

    public LocalDateTime getOrderPayTime() {
        return orderPayTime;
    }

    public void setOrderPayTime(LocalDateTime orderPayTime) {
        this.orderPayTime = orderPayTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CreditCardPaymentInfo{" +
                "id=" + id +
                ", orderNumber='" + orderNumber + '\'' +
                ", creditCardType='" + creditCardType + '\'' +
                ", payMethod='" + payMethod + '\'' +
                ", payChannelId=" + payChannelId +
                ", randomValue=" + randomValue +
                ", payWeight=" + payWeight +
                ", cardBin='" + cardBin + '\'' +
                ", orderCountry='" + orderCountry + '\'' +
                ", forterLastDecision='" + forterLastDecision + '\'' +
                ", forterLastRecommendation='" + forterLastRecommendation + '\'' +
                ", threeDomainSecure=" + threeDomainSecure +
                ", paymentEci='" + paymentEci + '\'' +
                ", paymentAuthCode='" + paymentAuthCode + '\'' +
                ", paymentAuthText='" + paymentAuthText + '\'' +
                ", paymentAuthTime=" + paymentAuthTime +
                ", paymentResult=" + paymentResult +
                ", orderPayRequestTime=" + orderPayRequestTime +
                ", orderCreateTime=" + orderCreateTime +
                ", forterLastDecisionTime=" + forterLastDecisionTime +
                ", orderPayTime=" + orderPayTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}