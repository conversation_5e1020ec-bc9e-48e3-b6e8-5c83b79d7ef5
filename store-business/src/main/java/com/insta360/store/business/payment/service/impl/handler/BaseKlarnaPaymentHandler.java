package com.insta360.store.business.payment.service.impl.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.util.ThreadUtil;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.configuration.utils.EncodingUtil;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.exception.RetryHandlerException;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.model.MetaExpress;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDeliveryPartly;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.impl.helper.OrderItemAverageHelper;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.outgoing.mq.check.bo.PaymentParamCheckBO;
import com.insta360.store.business.outgoing.mq.check.helper.DoubleCheckSendHelper;
import com.insta360.store.business.payment.bo.*;
import com.insta360.store.business.payment.constants.KlarnaConstant;
import com.insta360.store.business.payment.context.PaymentResultDataContext;
import com.insta360.store.business.payment.enums.*;
import com.insta360.store.business.payment.exception.PaymentErrorCode;
import com.insta360.store.business.payment.lib.klarna.bo.KlarnaPaymentParamBO;
import com.insta360.store.business.payment.lib.klarna.bo.KlarnaResponseBO;
import com.insta360.store.business.payment.lib.klarna.config.KlarnaPaymentConfiguration;
import com.insta360.store.business.payment.lib.klarna.enums.KlarnaHppSessionStatusEnum;
import com.insta360.store.business.payment.lib.klarna.enums.KlarnaOrderStatusEnum;
import com.insta360.store.business.payment.lib.klarna.module.*;
import com.insta360.store.business.payment.lib.klarna.request.*;
import com.insta360.store.business.payment.lib.klarna.response.*;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.trade.enums.CreditCardPaymentActionEnum;
import com.insta360.store.business.trade.exception.TradeErrorCode;
import com.insta360.store.business.trade.model.CreditCardPaymentAuthResult;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.model.KlarnaPaymentConfig;
import com.insta360.store.business.trade.service.KlarnaPaymentConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2023/03/22
 * @Description:
 */
public abstract class BaseKlarnaPaymentHandler extends BasePaymentHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseKlarnaPaymentHandler.class);

    /**
     * klarna 订单状态
     */
    private static final List<String> KLARNA_ORDER_STATUS = Lists.newArrayList(KlarnaOrderStatusEnum.AUTHORIZED.getName(), KlarnaOrderStatusEnum.CAPTURED.getName());

    /**
     * klarna 默认延迟对账时间
     */
    private static final Long KLARNA_DEFAULT_DELAY_TIME = 60000L;

    @Autowired
    OrderItemAverageHelper orderItemAverageHelper;

    @Autowired
    KlarnaPaymentConfigService klarnaPaymentConfigService;

    @Autowired
    DoubleCheckSendHelper doubleCheckSendHelper;

    /**
     * 税费文案
     */
    protected String taxFeeText;

    /**
     * 运费文案
     */
    protected String shippingFeeText;

    @Override
    protected <T> T _payOrder(PaymentInfo paymentInfo, PaymentExtra paymentExtra) {
        KlarnaPaymentConfig klarnaPaymentConfig = klarnaPaymentConfigService.getByCountry(order.getArea());
        if (klarnaPaymentConfig == null) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }

        // 文案封装
        taxFeeText = klarnaPaymentConfig.getTaxFeeText();
        shippingFeeText = klarnaPaymentConfig.getShippingFeeText();

        // 价格限制校验
        if (!this.amountLimitCheck(klarnaPaymentConfig)) {
            LOGGER.error("klarna支付，最大金额阈值校验未通过。order_number:{}", order.getOrderNumber());
            throw new InstaException(TradeErrorCode.KlarnaAmountLimitException);
        }

        // 配置信息
        KlarnaPaymentConfiguration klarnaPaymentConfiguration = this.getKlarnaPaymentConfiguration(order.getArea());

        // 支付链接获取失败则返回订单号
        String orderNumber = order.getOrderNumber();
        String payUrl = orderNumber;

        try {
            // 1、先创建 kp session
            BaseKlarnaPaymentRequest kpSessionRequest = this.getKpSessionRequest(paymentInfo, klarnaPaymentConfiguration);
            LOGGER.info("klarna kp session【订单支付】请求参数:{}", kpSessionRequest);
            String kpResult = kpSessionRequest.executePost(orderNumber);
            LOGGER.info("klarna kp session【订单支付】响应参数:{}", kpResult);
            KlarnaKpSessionResponse klarnaPaymentResponse = KlarnaKpSessionResponse.parse(kpResult);
            if (klarnaPaymentResponse.isErrorResponse()) {
                LOGGER.error("kp session create fail. order_number:{}, error_message:{}", orderNumber,
                        klarnaPaymentResponse.getError_code() + " " + klarnaPaymentResponse.getError_messages());
                return (T) payUrl;
            }

            // 2、根据 kp session id 创建 hpp session
            String kpSessionId = klarnaPaymentResponse.getSession_id();
            BaseKlarnaPaymentRequest hppSessionRequest = this.getHppSessionRequest(kpSessionId, klarnaPaymentConfiguration);
            LOGGER.info("klarna hpp session【订单支付】请求参数:{}", hppSessionRequest);
            String hppResult = hppSessionRequest.executePost(orderNumber);
            LOGGER.info("klarna hpp session【订单支付】响应参数:{}", hppResult);
            KlarnaHppSessionResponse hostPaymentPageSessionResponse = KlarnaHppSessionResponse.parse(hppResult);
            if (hostPaymentPageSessionResponse.isErrorResponse()) {
                LOGGER.error("hpp session create fail. order_number:{}, kp_session_id:{}, error_message:{}", orderNumber,
                        kpSessionId, klarnaPaymentResponse.getError_code() + " " + klarnaPaymentResponse.getError_messages());
                return (T) payUrl;
            }

            // 发送交易校验监控事件
            String hppSession = hostPaymentPageSessionResponse.getSession_id();
            this.sendOrderPaymentCheckMessage(orderNumber, hppSession);

            // 发送 hpp session 禁用事件，且更新缓存
            ThreadUtil.execute(() -> this.sendHppSessionDisableMessageAndAddCache(orderNumber, hppSession));

            // 支付跳转链接
            payUrl = hostPaymentPageSessionResponse.getRedirect_url();
        } catch (Exception e) {
            LOGGER.error(String.format("klarna 支付创建失败。order_number:{%s}, error_message:{%s}", orderNumber, e.getMessage()), e);
        } finally {
            // init paypal pay result
            LocalDateTime now = LocalDateTime.now();
            CreditCardPaymentInfo creditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(orderNumber);
            if (creditCardPaymentInfo == null) {
                creditCardPaymentInfo = new CreditCardPaymentInfo();
                creditCardPaymentInfo.setCreateTime(now);
                creditCardPaymentInfo.setOrderNumber(orderNumber);
                creditCardPaymentInfo.setOrderCreateTime(paymentInfo.orderCreateTime);
            }
            creditCardPaymentInfo.setUpdateTime(now);
            creditCardPaymentInfo.setPaymentAuthTime(now);
            creditCardPaymentInfo.setOrderPayRequestTime(now);
            creditCardPaymentInfo.setOrderCountry(paymentInfo.countryCode);
            creditCardPaymentInfo.setPayMethod(StorePaymentMethodEnum.KLARNA_PAYMENT.getName());
            creditCardPaymentInfoService.saveOrUpdate(creditCardPaymentInfo);
        }

        // 3、得到跳转链接
        return (T) payUrl;
    }

    /**
     * 发送 hpp session 禁用事件 且 更新session缓存
     *
     * @param orderNumber
     * @param hppSession
     */
    protected void sendHppSessionDisableMessageAndAddCache(String orderNumber, String hppSession) {
        try {
            String cacheKey = KlarnaConstant.HPP_SESSION_CACHE_KEY + orderNumber;
            List<String> hppSessions = (List<String>) RedisTemplateUtil.getValue(cacheKey);

            // 如果存在旧的 hpp session 的话
            if (CollectionUtils.isNotEmpty(hppSessions)) {
                // 发送 hpp session 禁用事件
                paymentMessageSendHelper.sendKlarnaPaymentHppSessionDisableMessage(orderNumber, hppSessions);
            }

            // 将最新创建的 hpp session 放入缓存
            hppSessions = new ArrayList<>();
            hppSessions.add(hppSession);
            RedisTemplateUtil.setKeyValue(cacheKey, hppSessions, 48L, TimeUnit.HOURS);
        } catch (Exception e) {
            LOGGER.error(String.format("发送 hpp session 禁用事件 且 更新session缓存 失败。原因:{%s}。order_number:{%s}, hpp_session:{%s}",
                    e.getMessage(), orderNumber, hppSession), e);
        }
    }

    /**
     * 发送交易校验监控事件
     *
     * @param orderNumber
     * @param hppSession
     */
    protected void sendOrderPaymentCheckMessage(String orderNumber, String hppSession) {
        OrderPaymentCheckBO orderPaymentCheckParam = new OrderPaymentCheckBO();
        orderPaymentCheckParam.setOrderNumber(orderNumber);
        orderPaymentCheckParam.setKlarnaHppSessionId(hppSession);
        orderPaymentCheckParam.setDelayTime(this.getTradeCheckDelayTime());
        orderPaymentCheckParam.setPaymentChannel(this.getPaymentChannel());
        orderMessageSendHelper.sendOrderPaymentCheckMonitorMessage(orderPaymentCheckParam);
    }

    /**
     * kp session request
     *
     * @param paymentInfo
     * @param klarnaPaymentConfiguration
     * @return
     */
    private BaseKlarnaPaymentRequest getKpSessionRequest(PaymentInfo paymentInfo, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        KlarnaKpSessionRequest request = new KlarnaKpSessionRequest(klarnaPaymentConfiguration);
        request.setMerchant_reference1(paymentInfo.orderNumber);
        request.setPurchase_country(paymentInfo.countryCode);
        request.setPurchase_currency(paymentInfo.currency.name());
        request.setOrder_amount(this.changeAmount(paymentInfo.amount, paymentInfo.currency));
        request.setBilling_address(this.getKlarnaBillingAddress(paymentInfo));
        request.setShipping_address(this.getKlarnaShippingAddress(paymentInfo));
        request.setOrder_lines(this.listKlarnaOrderLines(paymentInfo.currency));
        request.setIntent(this.getIntent());
        KlarnaPaymentConfig klarnaPaymentConfig = klarnaPaymentConfigService.getByCountry(order.getArea());
        request.setLocale(klarnaPaymentConfig.getLocale());
        // 参数校验
        if (!request.checker()) {
            LOGGER.error("klarna支付，支付总金额与子项金额汇总不一致。order_number:{}", paymentInfo.orderNumber);
            throw new InstaException(TradeErrorCode.KlarnaAmountLimitException);
        }
        return request;
    }

    /**
     * hpp session request
     *
     * @param kpSessionId
     * @param klarnaPaymentConfiguration
     * @return
     */
    protected BaseKlarnaPaymentRequest getHppSessionRequest(String kpSessionId, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        KlarnaHppSessionRequest request = new KlarnaHppSessionRequest(klarnaPaymentConfiguration);
        request.setOptions(this.getKlarnaOption());
        request.setPayment_session_url(String.format(KlarnaConstant.PAYMENT_SESSION_URL, kpSessionId));
        request.setMerchant_urls(this.getKlarnaMerchantUrl(klarnaPaymentConfiguration, kpSessionId));
        return request;
    }

    /**
     * get hpp session info
     *
     * @param hppSessionId
     * @param klarnaPaymentConfiguration
     * @return
     */
    protected BaseKlarnaPaymentRequest getHppSessionInfoRequest(String hppSessionId, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        return new KlarnaHppSessionInfoRequest(hppSessionId, klarnaPaymentConfiguration);
    }

    /**
     * get klarna order detail
     *
     * @param klarnaOrderId
     * @param klarnaPaymentConfiguration
     * @return
     */
    public BaseKlarnaPaymentRequest getOrderDetailRequest(String klarnaOrderId, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        return new KlarnaOrderDetailRequest(klarnaOrderId, klarnaPaymentConfiguration);
    }

    /**
     * disable klarna hpp session
     *
     * @param hppSessionId
     * @param klarnaPaymentConfiguration
     * @return
     */
    protected BaseKlarnaPaymentRequest getHppSessionDisableRequest(String hppSessionId, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        return new KlarnaHppSessionDisableRequest(hppSessionId, klarnaPaymentConfiguration);
    }

    /**
     * klarna order capture
     *
     * @param allowCaptureAmount
     * @param orderId
     * @param klarnaOrderId
     * @param klarnaPaymentConfiguration
     * @return
     */
    protected BaseKlarnaPaymentRequest getOrderCaptureRequest(Integer allowCaptureAmount, Integer orderId, String klarnaOrderId, KlarnaPaymentConfiguration klarnaPaymentConfiguration) {
        KlarnaOrderCaptureRequest request = new KlarnaOrderCaptureRequest(klarnaOrderId, klarnaPaymentConfiguration);
        request.setShipping_info(this.listKlarnaShippingInfos(orderId));

        // TODO: 24/04/2023 等待售后模块优化完成再完成替换
//        request.setCaptured_amount(this.getCaptureAmount(orderId));
        request.setCaptured_amount(allowCaptureAmount);
        return request;
    }

    /**
     * 价格转换。日韩价格不变，其它货币价格使用"分"进行结算。
     *
     * @param amount
     * @param currency
     * @return
     */
    protected Integer changeAmount(Float amount, Currency currency) {
        if (KlarnaConstant.ORIGIN_CURRENCY.contains(currency)) {
            return amount.intValue();
        }
        return Math.round(amount * 100);
    }

    /**
     * 构建 billing address
     *
     * @param paymentInfo
     * @return
     */
    protected KlarnaBillingAddress getKlarnaBillingAddress(PaymentInfo paymentInfo) {
        KlarnaBillingAddress billingAddress = new KlarnaBillingAddress();
        billingAddress.setCountry(paymentInfo.countryCode);
        billingAddress.setCity(paymentInfo.billing_city);
        billingAddress.setEmail(paymentInfo.email);
        billingAddress.setGiven_name(paymentInfo.billing_firstName);
        billingAddress.setFamily_name(paymentInfo.billing_lastName);
        billingAddress.setRegion(paymentInfo.billing_province);
        billingAddress.setPhone(paymentInfo.billing_phone);
        billingAddress.setPostal_code(paymentInfo.billing_zipCode);
        billingAddress.setStreet_address(paymentInfo.billing_address);
        return billingAddress;
    }

    /**
     * 构建 shipping address
     *
     * @param paymentInfo
     * @return
     */
    private KlarnaShippingAddress getKlarnaShippingAddress(PaymentInfo paymentInfo) {
        KlarnaShippingAddress shippingAddress = new KlarnaShippingAddress();
        shippingAddress.setCountry(paymentInfo.countryCode);
        shippingAddress.setCity(paymentInfo.city);
        shippingAddress.setEmail(paymentInfo.email);
        shippingAddress.setGiven_name(paymentInfo.firstName);
        shippingAddress.setFamily_name(paymentInfo.lastName);
        shippingAddress.setRegion(paymentInfo.province);
        shippingAddress.setPhone(paymentInfo.phone);
        shippingAddress.setPostal_code(paymentInfo.zipCode);
        shippingAddress.setStreet_address(paymentInfo.address);
        return shippingAddress;
    }

    /**
     * 商品子项（税费/运费单独作为一列展示）
     *
     * @return
     */
    protected List<KlarnaOrderLine> listKlarnaOrderLines(Currency currency) {
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        List<KlarnaOrderLine> orderLines = orderItems.stream().map(orderItem -> {
            Product product = productService.getById(orderItem.getProduct());
            CountryConfig countryConfig = countryConfigService.getByCountry(order.country());
            ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(orderItem.getProduct(), countryConfig.language());
            CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(orderItem.getCommodity());

            // 子项总折扣价
            Price discountPrice = orderItemAverageHelper.getOrderItemTotalDiscountAmount(orderItem.getId());

            // 子项总支付价
            float itemPayAmount = orderItem.getPrice() * orderItem.getNumber() - discountPrice.getAmount();
            if (itemPayAmount < 0) {
                String message = String.format("Klarna支付子项价格小于0。请及时查看处理。order_number:{%s}", order.getOrderNumber());
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }

            // 子项信息
            KlarnaOrderLine itemLine = new KlarnaOrderLine();
            itemLine.setQuantity(orderItem.getNumber());
            itemLine.setUnit_price(this.changeAmount(orderItem.getTotalItemPayPrice().getAmount(), currency));
            itemLine.setTotal_amount(this.changeAmount(itemPayAmount, currency));
            itemLine.setName(productInfo == null ? "" : productInfo.getName());
            itemLine.setProductUrl(product == null ? "" : gatewayConfiguration.getStoreUrl() + "/product/" + product.getUrlKey());
            itemLine.setImage_url(commodityDisplay == null ? "" : commodityDisplay.getUrl());
            return itemLine;
        }).collect(Collectors.toList());

        // 税费
        Float tax = payment.getTax();
        if (tax != 0) {
            Integer taxChange = this.changeAmount(tax, payment.currency());
            KlarnaOrderLine taxLine = new KlarnaOrderLine();
            taxLine.setQuantity(1);
            taxLine.setName(taxFeeText);
            taxLine.setTotal_amount(taxChange);
            taxLine.setUnit_price(taxChange);
            orderLines.add(taxLine);
        }

        // 运费
        Float shippingCost = payment.getShippingCost();
        if (shippingCost != 0) {
            Integer shippingCostChange = this.changeAmount(shippingCost, payment.currency());
            KlarnaOrderLine shippingCostLine = new KlarnaOrderLine();
            shippingCostLine.setQuantity(1);
            shippingCostLine.setName(shippingFeeText);
            shippingCostLine.setTotal_amount(shippingCostChange);
            shippingCostLine.setUnit_price(shippingCostChange);
            orderLines.add(shippingCostLine);
        }
        return orderLines;
    }

    /**
     * 支付模式
     *
     * @return
     */
    protected KlarnaOption getKlarnaOption() {
        KlarnaOption option = new KlarnaOption();
        option.setPlace_order_mode(KlarnaPlaceOrderMode.PLACE_ORDER.getName());
        return option;
    }

    /**
     * 获取支付意图
     *
     * @return
     */
    protected String getIntent() {
        return KlarnaPayIntentType.BUY.getName();
    }

    /**
     * 封装跳转链接
     *
     * @param klarnaPaymentConfiguration
     * @param kpSessionId
     * @return
     */
    protected KlarnaMerchantUrl getKlarnaMerchantUrl(KlarnaPaymentConfiguration klarnaPaymentConfiguration, String kpSessionId) {
        // 订单号RSA后encode
        String orderNumberSignValue = this.getOrderNumberSignValue(true);

        // 后撤 或 取消 的跳转链接（拼接订单号供页面加载接口）
        String backOrCancelUrl = String.format(klarnaPaymentConfiguration.getBackOrCancelUrl(), orderNumberSignValue);

        // 错误 或 失败 的跳转链接（拼接订单号供页面加载接口）
        String errorOrFailureUrl = String.format(klarnaPaymentConfiguration.getErrorOrFailureUrl(), orderNumberSignValue);

        KlarnaMerchantUrl merchantUrl = new KlarnaMerchantUrl();
        merchantUrl.setBack(backOrCancelUrl);
        merchantUrl.setCacnel(backOrCancelUrl);
        merchantUrl.setError(errorOrFailureUrl);
        merchantUrl.setFailure(errorOrFailureUrl);
        merchantUrl.setSuccess(this.getSuccess(kpSessionId));
        merchantUrl.setStatus_update(this.getReturnUrl());
        return merchantUrl;
    }

    /**
     * 获取支付成功回调url
     *
     * @param kpSessionId
     * @return
     */
    protected String getSuccess(String kpSessionId) {
        return String.format(this.getNotifyUrl(), this.getOrderNumberSignValue(false));
    }

    /**
     * 订单物流信息
     *
     * @param orderId
     * @return
     */
    protected List<KlarnaShippingInfo> listKlarnaShippingInfos(Integer orderId) {
        // 订单所有的发货信息
        List<OrderDeliveryPartly> orderDeliveryPartlies = orderDeliveryPartlyService.getByOrder(orderId);

        // key:express_code
        // value: express_company
        Map<String, OrderDeliveryPartly> expressMap = orderDeliveryPartlies
                .stream()
                .collect(Collectors.groupingBy(
                        OrderDeliveryPartly::getExpressCode, Collectors.collectingAndThen(
                                Collectors.toList(), orderDeliverires -> orderDeliverires.get(0))));
        return expressMap.keySet().stream().map(expressCode -> {
            String expressCompany = expressMap.get(expressCode).getExpressCompany();
            MetaExpress metaExpress = metaExpressService.getById(expressCompany);

            // klarn shipping info
            KlarnaShippingInfo klarnaShippingInfo = new KlarnaShippingInfo();
            klarnaShippingInfo.setTracking_number(expressCode);
            klarnaShippingInfo.setTracking_uri(metaExpress != null ? metaExpress.getTrackUrl() : null);
            klarnaShippingInfo.setShipping_company(metaExpress != null ? metaExpress.getKlarnaExpressCompany() : expressCompany);
            return klarnaShippingInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 订单capture总额
     *
     * @param orderId
     * @return
     */
    protected Integer getCaptureAmount(Integer orderId) {
        // 售后总金额
        Float rmaSumAmount = 0f;
        List<RmaOrder> rmaOrders = rmaOrderService.listByOrderId(orderId);
        for (RmaOrder rmaOrder : rmaOrders) {
            if (RmaState.success.equals(rmaOrder.rmaState())) {
                rmaSumAmount += rmaOrder.getRefundAmount();
            }
        }

        // capture 金额 = 订单支付总金额 - 售后总金额
        OrderPayment orderPayment = orderPaymentService.getByOrder(orderId);
        Price totalPayPrice = orderPayment.getTotalPayPrice();
        return this.changeAmount(totalPayPrice.getAmount() - rmaSumAmount, totalPayPrice.getCurrency());
    }

    /**
     * 获取加密后的订单号
     *
     * @param isUseWeb 是否是web页面使用
     * @return
     */
    protected String getOrderNumberSignValue(Boolean isUseWeb) {
        String orderNumber = order.getOrderNumber();
        return EncodingUtil.encode(isUseWeb ? RSAUtil.encryptByPub(orderNumber) : RSAUtil.encryptByMyPub(orderNumber));
    }

    /**
     * 获取支付配置
     *
     * @param countryCode
     * @return
     */
    public abstract KlarnaPaymentConfiguration getKlarnaPaymentConfiguration(String countryCode);

    /**
     * 接收 sid 和 order id
     *
     * @return
     */
    @Override
    protected String getNotifyUrl() {
        return super.getNotifyUrl() + "&sid={{session_id}}&order_id={{order_id}}&sign=%s";
    }

    /**
     * 接收 sid 和 order id
     *
     * @return
     */
    @Override
    protected String getReturnUrl() {
        return super.getReturnUrl() + "&sid={{session_id}}&order_id={{order_id}}&sign=" + this.getOrderNumberSignValue(false);
    }

    /**
     * 价格限制校验
     *
     * @return
     */
    protected Boolean amountLimitCheck(KlarnaPaymentConfig klarnaPaymentConfig) {
        Price payPrice = getPayPrice();
        return payPrice.getAmount() <= klarnaPaymentConfig.getAmountLimit()
                && payPrice.getCurrency().name().equals(klarnaPaymentConfig.getCurrencyCode());
    }

    @Override
    public <T> T paymentCapture(PaymentCaptureBO paymentCaptureParam) {
        String orderNumber = paymentCaptureParam.getOrderNumber();
        LOGGER.info("klarna payment capture begin. order_number:{}", orderNumber);
        if (StringUtil.isBlank(orderNumber)) {
            LOGGER.error("klarna payment capture error. orderNumber is blank.");
            return null;
        }

        // 订单有效性校验
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 只关注支付渠道为klarna的订单
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        PaymentChannel paymentChannel = orderPayment.paymentChannel();
        if (!PaymentChannel.isKlarnaChannel(orderPayment.paymentChannel())) {
            LOGGER.error("支付渠道不是klarna。不予处理. order_number:{}", orderNumber);
            return null;
        }

        // klarna交易号
        String channelPaymentId = orderPayment.getChannelPaymentId();
        if (StringUtil.isBlank(channelPaymentId)) {
            LOGGER.error("klarna交易号为空。不予处理. order_number:{}", orderNumber);
            return null;
        }

        // 配置信息
        KlarnaPaymentConfiguration klarnaPaymentConfiguration = this.getKlarnaPaymentConfiguration(order.getArea());
        String klarnaOrderId = orderPayment.getChannelPaymentId();

        try {
            // 根据 klarna order id 获取订单交易详情
            BaseKlarnaPaymentRequest orderDetailRequest = this.getOrderDetailRequest(klarnaOrderId, klarnaPaymentConfiguration);
            LOGGER.info("get order detail【交易捕获】请求参数:{}", orderDetailRequest);
            String orderDetailResult = orderDetailRequest.executeGet();
            LOGGER.info("get order detail【交易捕获】响应参数:{}", orderDetailResult);
            KlarnaOrderDetailResponse orderDetailResponse = KlarnaOrderDetailResponse.parse(orderDetailResult);
            if (orderDetailResponse.isErrorResponse()) {
                LOGGER.error("klarna order detail get fail. klarna_order_id:{}, error_message:{}", klarnaOrderId,
                        orderDetailResponse.getError_code() + " " + orderDetailResponse.getError_messages());
                return null;
            }

            // 允许cpature的金额
            Integer allowCaptureAmount = orderDetailResponse.getRemaining_authorized_amount();
            LOGGER.info("订单:{} 最大允许capture金额为:{}", order.getOrderNumber(), allowCaptureAmount);

            // 调用 klarna capture 接口
            BaseKlarnaPaymentRequest orderCaptureRequest = this.getOrderCaptureRequest(allowCaptureAmount, order.getId(), klarnaOrderId, klarnaPaymentConfiguration);
            LOGGER.info("klarna order capture【交易捕获】请求参数:{}", orderCaptureRequest);
            String captureResult = orderCaptureRequest.executePost(orderNumber);
            LOGGER.info("klarna order capture【交易捕获】响应参数:{}", captureResult);
            KlarnaOrderCaptureResponse orderCaptureResponse = KlarnaOrderCaptureResponse.parse(captureResult);
            if (orderCaptureResponse.isErrorResponse()) {
                LOGGER.error("klarna order capture fail. order_number:{}, error_message:{}", order.getOrderNumber(),
                        orderCaptureResponse.getError_code() + " " + orderCaptureResponse.getError_messages());
                String message = String.format("klarna order capture fail. order_number:{%s}, error_message:{%s}",
                        order.getOrderNumber(), orderCaptureResponse.getError_code() + " " + orderCaptureResponse.getError_messages());
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return null;
            }

            // 设置为安全的交易
            orderPaymentService.setSecurityForPayment(order.getId());

            // 记录必要的支付数据
            KlarnaResponseBO klarnaResponse = new KlarnaResponseBO();
            klarnaResponse.setOrderNumber(orderNumber);
            klarnaResponse.setAuthType(paymentChannel.name());
            klarnaResponse.setAuthCode(paymentChannel.name());
            klarnaResponse.setAuthText(paymentChannel.name());
            klarnaResponse.setStatus(KlarnaOrderStatusEnum.CAPTURED.getName());
            klarnaResponse.setActionType(CreditCardPaymentActionEnum.CAPTURE_RESULT.getActionName());
            this.savePaymentInfo(klarnaResponse);
        } catch (Exception e) {
            LOGGER.error(String.format("klarna 支付 capture 失败。order_number:{%s}, error_message:{%s}", orderNumber, e.getMessage()), e);
        }
        return null;
    }

    @Override
    public void _handlePaymentResult(Object paymentResult, PaymentHandleResult handleResult) {
        KlarnaPaymentParamBO klarnaPaymentParam = (KlarnaPaymentParamBO) paymentResult;

        // 订单号
        String orderNumber = klarnaPaymentParam.getOrderNumber();
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // 配置信息
        KlarnaPaymentConfiguration klarnaPaymentConfiguration = this.getKlarnaPaymentConfiguration(order.getArea());

        // hpp session id
        String sid = klarnaPaymentParam.getSid();

        // klarna order id
        String klarnaOrderId = klarnaPaymentParam.getKlarnaOrderId();

        // 如果 klarna order id 不存在，则需要根据 hpp sessionId 获取 klarna order id
        if (StringUtil.isBlank(klarnaOrderId)) {
            // 如果 hpp sessionId 也不存在，则无需理会本次回传。
            if (StringUtil.isBlank(sid)) {
                LOGGER.error("klarna notify 回传未携带 sid 和 orderId。不予处理。");
                return;
            }

            // 调用 hpp session 查询接口
            BaseKlarnaPaymentRequest hppSessionInfoRequest = this.getHppSessionInfoRequest(sid, klarnaPaymentConfiguration);
            LOGGER.info("get hpp session info【结果处理】请求参数:{}", hppSessionInfoRequest);
            String hppSessionInfoResult;
            try {
                hppSessionInfoResult = hppSessionInfoRequest.executeGet();
            } catch (Exception e) {
                LOGGER.error(String.format("get hpp session info【结果处理】request fail. message:{%s}, hpp_session_id:{%s}", e.getMessage(), sid), e);
                return;
            }
            LOGGER.info("get hpp session info【结果处理】响应参数:{}", hppSessionInfoResult);
            KlarnaHppSessionInfoResponse hppSessionInfoResponse = KlarnaHppSessionInfoResponse.parse(hppSessionInfoResult);
            if (hppSessionInfoResponse.isErrorResponse()) {
                LOGGER.error("hpp session get fial. order_number:{}, error_message:{}", order.getOrderNumber(),
                        hppSessionInfoResponse.getError_code() + " " + hppSessionInfoResponse.getError_messages());
                return;
            }

            // 拿到 klarna order id
            klarnaOrderId = hppSessionInfoResponse.getOrder_id();
        }

        // 根据 klarna order id 获取订单交易详情
        BaseKlarnaPaymentRequest orderDetailRequest = this.getOrderDetailRequest(klarnaOrderId, klarnaPaymentConfiguration);
        LOGGER.info("get order detail【结果处理】请求参数:{}", orderDetailRequest);
        String orderDetailResult;
        try {
            orderDetailResult = orderDetailRequest.executeGet();
        } catch (Exception e) {
            LOGGER.error(String.format("get order detail【结果处理】request fail. message:{%s}, klarna_order_id:{%s}", e.getMessage(), klarnaOrderId), e);
            return;
        }
        LOGGER.info("get order detail【结果处理】响应参数:{}", orderDetailResult);
        KlarnaOrderDetailResponse orderDetailResponse = KlarnaOrderDetailResponse.parse(orderDetailResult);
        if (orderDetailResponse.isErrorResponse()) {
            LOGGER.error("klarna order detail get fail【结果处理】. klarna_order_id:{}, error_message:{}", klarnaOrderId,
                    orderDetailResponse.getError_code() + " " + orderDetailResponse.getError_messages());
            return;
        }

        OrderPayment payment = orderPaymentService.getByOrder(order.getId());
        if (payment == null) {
            LOGGER.error("klarna transaction fail. message: order payment not found. paymentResult:{}", paymentResult);
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        try {
            float amount = orderPaymentCounter.count(payment);
            int amountRule = KlarnaConstant.ORIGIN_CURRENCY.contains(payment.currency()) ? (int) amount : Math.round(amount * 100);
            BigDecimal orderAmount = new BigDecimal(amountRule);
            BigDecimal payAmount = new BigDecimal(orderDetailResponse.getOrder_amount());
            if (orderAmount.compareTo(payAmount) != 0) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[发起交易时支付对账]Klarna 交易结果处理失败。回传金额与订单金额不一致。paymentResult:{%s}",
                        paymentResult), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        } catch (Exception e) {
            LOGGER.error("[发起交易时支付对账]klarna 金额比对失败。error message:{}", e.getMessage());
            FeiShuMessageUtil.storeGeneralMessage(String.format("[发起交易时支付对账]klarna 金额比对失败。paymentResult:{%s}，error message:{%s}",
                    paymentResult, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }

        // 订单一致性校验
        if (!order.getOrderNumber().equals(orderDetailResponse.getMerchant_reference1())) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("klarna回调支付订单不匹配！请及时处理。回传订单号{%s},klarna实际订单号{%s}",
                    order.getOrderNumber(), orderDetailResponse.getMerchant_reference1()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            throw new InstaException(OrderErrorCode.OrderActionNotPermittedException);
        }

        // 预授权即标识交易成功
        if (KlarnaOrderStatusEnum.AUTHORIZED.getName().equals(orderDetailResponse.getStatus())) {
            // 设置为支付成功
            this.processPaymentSuccess(order.getOrderNumber(), klarnaOrderId, klarnaPaymentParam.getKpSid());
        } else {
            // 按照正常支付流程来说，一笔新的交易，不可能初始状态是除authorized的其它状态，如果出现了其它的status的情况，则表明
            // order_id回传有误，需要人工介入定位该异常。所以针对这种情况，给出飞书通知提醒即可。
            String message = String.format("klarna支付授权状态返回异常。订单号:{%s}, klarna_order_id:{%s}",
                    order.getOrderNumber(), klarnaOrderId);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }

        try {
            // 记录必要的支付数据
            saveKlarnaAuthResult(orderNumber, orderDetailResponse);

            // 记录交易结果信息
            PaymentResultDataContext paymentResultContext = this.getPaymentResultContext();
            paymentResultContext.setOrderNumber(orderNumber);
            paymentResultContext.setResultData(JSONObject.toJSONString(orderDetailResponse.toString()));
            paymentResultContext.setPaymentChannel(this.getPaymentChannel());
        } catch (Exception e) {
            LOGGER.error(String.format("klarna 支付数据记录异常。异常信息:{%s}，交易回传结果:{%s}", e.getMessage(), paymentResult), e);
        } finally {
            // 更新 order capture 有效期
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            orderPayment.setKlarnaCaptureTimeout(orderDetailResponse.getExpires_at());
            orderPaymentService.updateById(orderPayment);
        }
    }

    /**
     * 处理klarna支付结果
     *
     * @param orderNumber
     * @param klarnaOrderId
     * @param kpSid
     */
    protected void processPaymentSuccess(String orderNumber, String klarnaOrderId, String kpSid) {
        orderPaymentService.setPaymentSuccess(orderNumber, this.getPaymentChannel(), klarnaOrderId, PaymentTradeSecurityType.HAVE_NOT_CAPTURE);
    }

    @Override
    public Long getTradeCheckDelayTime() {
        return KLARNA_DEFAULT_DELAY_TIME;
    }

    @Override
    public void handlePaymentResultCheck(OrderPaymentCheckBO orderPaymentCheckParam) {
        LOGGER.info("触发 klarna 交易核对流程。参数:{}", orderPaymentCheckParam);
        String orderNumber = orderPaymentCheckParam.getOrderNumber();
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            LOGGER.error("触发 klarna 交易核对异常。订单不存在");
            return;
        }

        String hppSessionId = orderPaymentCheckParam.getKlarnaHppSessionId();
        if (StringUtil.isBlank(hppSessionId)) {
            LOGGER.error("触发 klarna 交易核对异常。hpp session id不存在。订单号:{}", orderNumber);
            return;
        }

        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (OrderPaymentState.PAYED.equals(orderPayment.paymentState())) {
            LOGGER.info("触发 klarna 交易核对异常。交易状态已支付，不予进行交易核对。");
            return;
        }

        // 配置信息
        KlarnaPaymentConfiguration klarnaPaymentConfiguration = this.getKlarnaPaymentConfiguration(order.getArea());

        // 调用 hpp session 查询接口
        BaseKlarnaPaymentRequest hppSessionInfoReuest = this.getHppSessionInfoRequest(hppSessionId, klarnaPaymentConfiguration);
        LOGGER.info("get hpp session info【交易对账】请求参数:{}", hppSessionInfoReuest);
        String hppSessionInfoResult;
        try {
            hppSessionInfoResult = hppSessionInfoReuest.executeGet();
        } catch (Exception e) {
            LOGGER.error(String.format("get hpp session info【交易对账】request fail. message:{%s}, hpp_session_id:{%s}", e.getMessage(), hppSessionId), e);
            // 抛出去重投递
            if (e instanceof RetryHandlerException) {
                throw e;
            }
            return;
        }
        LOGGER.info("get hpp session info【交易对账】响应参数:{}", hppSessionInfoResult);
        KlarnaHppSessionInfoResponse hppSessionInfoResponse = KlarnaHppSessionInfoResponse.parse(hppSessionInfoResult);
        if (hppSessionInfoResponse.isErrorResponse()) {
            LOGGER.error("hpp session get fail【交易对账】. order_number:{}, error_message:{}", order.getOrderNumber(),
                    hppSessionInfoResponse.getError_code() + " " + hppSessionInfoResponse.getError_messages());
            return;
        }

        // 当前 seesion 状态
        String sessionStatus = hppSessionInfoResponse.getStatus();
        LOGGER.info("session status:{}", sessionStatus);

        // 不是 completed 的话，不用理会
        if (!KlarnaHppSessionStatusEnum.COMPLETED.getName().equals(sessionStatus)) {
            return;
        }

        // 是 completed 的话，则进一步验证交易信息
        String klarnaOrderId = hppSessionInfoResponse.getOrder_id();

        // 根据 klarna order id 获取订单交易详情
        BaseKlarnaPaymentRequest orderDetailRequest = this.getOrderDetailRequest(klarnaOrderId, klarnaPaymentConfiguration);
        LOGGER.info("get order detail【交易对账】请求参数:{}", orderDetailRequest);
        String orderDetailResult;
        try {
            orderDetailResult = orderDetailRequest.executeGet();
        } catch (Exception e) {
            LOGGER.error(String.format("get order detail【交易对账】request fail. message:{%s}, klarna_order_id:{%s}", e.getMessage(), klarnaOrderId), e);
            // 抛出去重投递
            if (e instanceof RetryHandlerException) {
                throw e;
            }
            return;
        }
        LOGGER.info("get order detail【交易对账】响应参数:{}", orderDetailResult);
        KlarnaOrderDetailResponse orderDetailResponse = KlarnaOrderDetailResponse.parse(orderDetailResult);
        if (orderDetailResponse.isErrorResponse()) {
            LOGGER.error("klarna order detail get fail. klarna_order_id:{}, error_message:{}", klarnaOrderId,
                    orderDetailResponse.getError_code() + " " + orderDetailResponse.getError_messages());
            return;
        }

        // 预授权即标识交易成功
        if (KlarnaOrderStatusEnum.AUTHORIZED.getName().equals(orderDetailResponse.getStatus())) {
            this.processPaymentSuccess(orderNumber, klarnaOrderId, hppSessionId);
        } else {
            // 按照正常支付流程来说，一笔新的交易，不可能初始状态是除authorized的其它状态，如果出现了其它的status的情况，则表明
            // order_id回传有误，需要人工介入定位该异常。所以针对这种情况，给出飞书通知提醒即可。
            FeiShuMessageUtil.storeGeneralMessage(String.format("klarna支付授权状态返回异常。订单号:{%s}, klarna_order_id:{%s}",
                    order.getOrderNumber(), klarnaOrderId), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        // 记录auth result
        orderDetailResponse.setOrder_id(orderDetailResponse.getOrder_id() + "（交易对账）");
        this.saveKlarnaAuthResult(orderNumber, orderDetailResponse);
    }

    @Override
    public Boolean isSignatureValid(Object signObject) {
        PaymentSignatureValidBO paymentSignatureValid = (PaymentSignatureValidBO) signObject;
        return paymentSignatureValid.getOrderNumber().equalsIgnoreCase(RSAUtil.decryptByMyPri(paymentSignatureValid.getKlarnaSignValue()));
    }

    /**
     * klarna 订单状态变更通知
     *
     * @param paymentResult
     */
    public void klarnaOrderStatusChangeNotify(Object paymentResult) {
        KlarnaPaymentParamBO klarnaPaymentParam = (KlarnaPaymentParamBO) paymentResult;

        // 订单号
        String orderNumber = klarnaPaymentParam.getOrderNumber();
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        // hpp session id
        String sid = klarnaPaymentParam.getSid();
        if (StringUtil.isBlank(sid)) {
            return;
        }

        try {
            // 获取当前 hpp session 状态
            BaseKlarnaPaymentRequest hppSessionInfoRequest = this.getHppSessionInfoRequest(sid, this.getKlarnaPaymentConfiguration(order.getArea()));
            LOGGER.info("get hpp session info【状态变更】请求参数:{}", hppSessionInfoRequest);
            String hppSessionInfoResult = hppSessionInfoRequest.executeGet();
            LOGGER.info("get hpp session info【状态变更】响应参数:{}", hppSessionInfoResult);
            KlarnaHppSessionInfoResponse hppSessionInfoResponse = KlarnaHppSessionInfoResponse.parse(hppSessionInfoResult);
            if (hppSessionInfoResponse.isErrorResponse()) {
                LOGGER.error("hpp session get fail. order_number:{}, error_message:{}", order.getOrderNumber(),
                        hppSessionInfoResponse.getError_code() + " " + hppSessionInfoResponse.getError_messages());
                return;
            }
            // 支付成功延迟对账处理
            if (KlarnaHppSessionStatusEnum.COMPLETED.equals(KlarnaHppSessionStatusEnum.parse(hppSessionInfoResponse.getStatus()))) {
                // 兜底支付成功状态未改变场景
                if (!OrderState.afterPayedState().contains(order.orderState())) {
                    DoubleCheckBO doubleCheckBo = new DoubleCheckBO();
                    doubleCheckBo.setBusinessId(order.getId());
                    doubleCheckBo.setCheckType(DoubleCheckEnum.PaymentKlarnaPayCheck);
                    PaymentParamCheckBO paymentParamCheckBo = new PaymentParamCheckBO();
                    paymentParamCheckBo.setPayId(hppSessionInfoResponse.getOrder_id());
                    paymentParamCheckBo.setPaymentChannel(getPaymentChannel());
                    paymentParamCheckBo.setKlarnaHppSessionId(sid);
                    doubleCheckBo.setPaymentParamCheckBo(paymentParamCheckBo);
                    doubleCheckSendHelper.sendDoubleCheckMessage(doubleCheckBo);
                }
            }

            // 状态回调中，忽略掉这两种状态的支付数据的记录。前置已记录。
            String hppSessionStatus = hppSessionInfoResponse.getStatus();
            if (KlarnaHppSessionStatusEnum.isIgnoreNotifyStatus(hppSessionStatus)) {
                return;
            }

            // klarna会触发多次重复的推送，如果与上一次推送的内容一致且时间没有超过10秒的话，按重复推送处理。
            CreditCardPaymentAuthResult cardPaymentAuthResult = creditCardPaymentAuthResultService.getLastRecordByAuthType(orderNumber,
                    StorePaymentMethodEnum.KLARNA_PAYMENT.getName(), hppSessionStatus);
            if (cardPaymentAuthResult != null && Duration.between(cardPaymentAuthResult.getCreateTime(), LocalDateTime.now()).toMillis() <= 10000) {
                LOGGER.info("klarna status 重复推送，不予处理。order_number:{}，hpp_session_status:{}", orderNumber, hppSessionStatus);
                return;
            }

            // 支付失败的webhook
            KlarnaHppSessionStatusEnum hppSessionStatusEnum = KlarnaHppSessionStatusEnum.parse(hppSessionInfoResponse.getStatus());
            if (KlarnaHppSessionStatusEnum.isPaymentFialed(hppSessionStatusEnum)) {
                // 设置为支付失败
                orderPaymentService.setPaymentFailure(orderNumber);
            } else if (KlarnaHppSessionStatusEnum.isPaymentCanceled(hppSessionStatusEnum)) {
                // 初始化交易状态
                orderPaymentService.setSecurityForPayment(order.getId());
            }

            // 记录必要的支付数据
            KlarnaResponseBO klarnaResponse = new KlarnaResponseBO();
            klarnaResponse.setOrderNumber(orderNumber);
            klarnaResponse.setStatus(hppSessionStatus);
            klarnaResponse.setAuthType(hppSessionStatus);
            klarnaResponse.setAuthText(hppSessionInfoResponse.getOrder_id());
            klarnaResponse.setAuthCode(hppSessionInfoResponse.getKlarna_reference());
            klarnaResponse.setActionType(CreditCardPaymentActionEnum.PAYED_RESULT.getActionName());
            this.savePaymentInfo(klarnaResponse);

            // 记录交易结果信息
            PaymentResultDataContext paymentResultContext = this.getPaymentResultContext();
            paymentResultContext.setOrderNumber(orderNumber);
            paymentResultContext.setResultData(JSONObject.toJSONString(hppSessionInfoResponse.toString()));
            paymentResultContext.setPaymentChannel(this.getPaymentChannel());
        } catch (Exception e) {
            LOGGER.error(String.format("klarna session 状态回调处理异常。order_number:{%s}, sid:{%s}, error_message:{%s}",
                    order.getOrderNumber(), sid, e.getMessage()), e);
        }
    }

    /**
     * 禁用 hpp session
     *
     * @param hppSessions
     * @param country
     */
    public void klarnaHppSessionDisable(List<String> hppSessions, String country) {
        if (CollectionUtils.isEmpty(hppSessions)) {
            return;
        }

        // 配置信息
        KlarnaPaymentConfiguration klarnaPaymentConfiguration = this.getKlarnaPaymentConfiguration(country);

        for (String hppSession : hppSessions) {
            BaseKlarnaPaymentRequest request = this.getHppSessionDisableRequest(hppSession, klarnaPaymentConfiguration);
            LOGGER.info("disable hpp session 请求参数:{}", request);
            String result = request.executeDelete();
            LOGGER.info("disable hpp session 响应参数:{}", result);
            KlarnaHppSessionDisableResponse hppSessionDisableResponse = KlarnaHppSessionDisableResponse.parse(result);
            if (hppSessionDisableResponse.isErrorResponse()) {
                LOGGER.error("disable hpp session fail. hpp session:{}, error_message:{}", hppSession,
                        hppSessionDisableResponse.getError_code() + " " + hppSessionDisableResponse.getError_messages());
            }
        }
    }

    /**
     * klarna auth result记录
     *
     * @param orderNumber
     * @param orderDetailResponse
     */
    protected void saveKlarnaAuthResult(String orderNumber, KlarnaOrderDetailResponse orderDetailResponse) {
        KlarnaResponseBO klarnaResponse = new KlarnaResponseBO();
        klarnaResponse.setOrderNumber(orderNumber);
        klarnaResponse.setStatus(orderDetailResponse.getStatus());
        klarnaResponse.setAuthType(orderDetailResponse.getStatus());
        klarnaResponse.setAuthCode(orderDetailResponse.getKlarna_reference());
        klarnaResponse.setAuthText(orderDetailResponse.getOrder_id());
        klarnaResponse.setActionType(CreditCardPaymentActionEnum.PAYED_RESULT.getActionName());
        this.savePaymentInfo(klarnaResponse);
    }

    @Override
    protected <T> void savePaymentInfo(T paymentInfo) {
        KlarnaResponseBO klarnaResponse = (KlarnaResponseBO) paymentInfo;

        String status = klarnaResponse.getStatus();
        String orderNumber = klarnaResponse.getOrderNumber();
        LocalDateTime now = LocalDateTime.now();

        // save klarna payment info
        CreditCardPaymentInfo creditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(orderNumber);
        if (creditCardPaymentInfo == null) {
            creditCardPaymentInfo = new CreditCardPaymentInfo();
            creditCardPaymentInfo.setCreateTime(now);
            creditCardPaymentInfo.setOrderNumber(orderNumber);
        }
        if (KlarnaOrderStatusEnum.AUTHORIZED.name().equals(status)) {
            creditCardPaymentInfo.setPayMethod(StorePaymentMethodEnum.KLARNA_PAYMENT.getName());
            creditCardPaymentInfo.setPaymentResult(true);
            creditCardPaymentInfo.setOrderPayTime(now);
        }
        creditCardPaymentInfo.setPaymentAuthCode(klarnaResponse.getAuthCode());
        creditCardPaymentInfo.setPaymentAuthText(klarnaResponse.getAuthText());
        creditCardPaymentInfo.setPaymentAuthTime(now);
        creditCardPaymentInfo.setUpdateTime(now);
        creditCardPaymentInfoService.updateById(creditCardPaymentInfo);


        // save klarna pay result list
        CreditCardPaymentAuthResult creditCardPaymentAuthResult = new CreditCardPaymentAuthResult();
        creditCardPaymentAuthResult.setOrderNumber(orderNumber);
        creditCardPaymentAuthResult.setPayMethod(StorePaymentMethodEnum.KLARNA_PAYMENT.getName());
        creditCardPaymentAuthResult.setActionType(klarnaResponse.getActionType());
        creditCardPaymentAuthResult.setAuthType(klarnaResponse.getAuthType());
        creditCardPaymentAuthResult.setAuthCode(klarnaResponse.getAuthCode());
        creditCardPaymentAuthResult.setAuthText(klarnaResponse.getAuthText());
        creditCardPaymentAuthResult.setCreateTime(now);
        creditCardPaymentAuthResult.setUpdateTime(now);
        creditCardPaymentAuthResultService.save(creditCardPaymentAuthResult);
    }

    @Override
    public PaymentReconciliationCheckResultBO handleTransactionReconciliation(PaymentReconciliationCheckBO paymentReconciliationCheckParam) {
        // 商城订单
        Order order = paymentReconciliationCheckParam.getOrder();
        if (Objects.isNull(order)) {
            LOGGER.error("[支付后交易对账]klarna 订单不存在。param:{}", paymentReconciliationCheckParam);
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        OrderPayment orderPayment = paymentReconciliationCheckParam.getOrderPayment();
        if (Objects.isNull(orderPayment)) {
            LOGGER.error("[支付后交易对账]klarna 订单支付信息不存在。param:{}", paymentReconciliationCheckParam);
            throw new InstaException(OrderErrorCode.OrderPaymentNotFoundException);
        }

        // 交易流水号
        String channelPaymentId = orderPayment.getChannelPaymentId();
        if (StringUtils.isBlank(channelPaymentId)) {
            LOGGER.info("[支付后交易对账]klarna 订单支付流水号缺失. orderNumber:{}", order.getOrderNumber());
            throw new InstaException(PaymentErrorCode.TransactionIdDoesNotExistException);
        }

        // 配置信息
        KlarnaPaymentConfiguration klarnaPaymentConfiguration = this.getKlarnaPaymentConfiguration(order.getArea());
        // 获取订单详情
        BaseKlarnaPaymentRequest orderDetailRequest = this.getOrderDetailRequest(channelPaymentId, klarnaPaymentConfiguration);
        String orderDetailResult;
        try {
            LOGGER.info("[支付后交易对账]klarna 查询交易订单详情 requestParam:{}", JSON.toJSONString(orderDetailRequest));
            orderDetailResult = orderDetailRequest.executeGet();
            LOGGER.info("[支付后交易对账]klarna 查询交易订单详情 responseBody:{}", orderDetailResult);
        } catch (Exception e) {
            LOGGER.error(String.format("[支付后交易对账]klarna 查询交易订单详情异常. 异常信息:{%s}, 交易流水号:{%s}, 订单号:{%s}", e.getMessage(), channelPaymentId, order.getOrderNumber()), e);
            throw new InstaException(PaymentErrorCode.ThirdPartyCallException);
        }

        KlarnaOrderDetailResponse orderDetailResponse = KlarnaOrderDetailResponse.parse(orderDetailResult);
        if (orderDetailResponse.isErrorResponse()) {
            LOGGER.error("[支付后交易对账]klarna 查询交易订单详情响应码异常. 交易流水号:{%s}, 订单号:{%s}, 异常码:{%s}", channelPaymentId, order.getOrderNumber(), orderDetailResponse.getError_code() + " " + orderDetailResponse.getError_messages());
            throw new InstaException(PaymentErrorCode.MissingResponseDataException);
        }

        // 订单支付金额
        float amount = orderPaymentCounter.count(orderPayment);
        int amountRule = KlarnaConstant.ORIGIN_CURRENCY.contains(orderPayment.currency()) ? (int) amount : Math.round(amount * 100);
        BigDecimal orderAmount = new BigDecimal(String.valueOf(amountRule));

        // 交易对账结果对象
        PaymentReconciliationCheckResultBO paymentReconciliationCheckResult = null;
        // 交易对账状态
        PaymentReconciliationStatus status = PaymentReconciliationStatus.RECONCILED;
        // 预授权即标识交易成功
        if (KLARNA_ORDER_STATUS.contains(orderDetailResponse.getStatus())) {
            // 机构交易金额
            BigDecimal payAmount = new BigDecimal(orderDetailResponse.getOrder_amount());
            // 机构交易币种
            String purchaseCurrency = orderDetailResponse.getPurchase_currency();

            if (orderAmount.compareTo(payAmount) != 0) {
                LOGGER.info("[支付后交易对账]Klarna 交易对账失败,支付结果响应金额与订单金额不一致。订单号:{}", order.getOrderNumber());
                FeiShuMessageUtil.storeGeneralMessage(String.format("[支付后交易对账]Klarna 交易对账失败,支付结果响应金额与订单金额不一致。订单号:{%s}", order.getOrderNumber()), FeiShuGroupRobot.PaymentInfo, FeiShuAtUser.ZM, FeiShuAtUser.LCY, FeiShuAtUser.TW);
                status = PaymentReconciliationStatus.ERROR;
            }
            if (!orderPayment.getCurrency().equals(purchaseCurrency)) {
                LOGGER.info("[支付后交易对账]Klarna 交易对账失败,交易币种不一致。订单号:{}", order.getOrderNumber());
                FeiShuMessageUtil.storeGeneralMessage(String.format("[支付后交易对账]Klarna 交易对账失败,交易币种不一致。订单号:{%s}", order.getOrderNumber()), FeiShuGroupRobot.PaymentInfo, FeiShuAtUser.ZM, FeiShuAtUser.LCY, FeiShuAtUser.TW);
                status = PaymentReconciliationStatus.ERROR;
            }

            // 构建交易对账结果对象
            paymentReconciliationCheckResult = new PaymentReconciliationCheckResultBO();
            paymentReconciliationCheckResult.setPaymentAmount(orderAmount);
            paymentReconciliationCheckResult.setPaymentCurrency(orderPayment.getCurrency());
            paymentReconciliationCheckResult.setReconciliationStatus(status.getCode());
            paymentReconciliationCheckResult.setActualPaymentAmount(payAmount);
            paymentReconciliationCheckResult.setActualPaymentCurrency(purchaseCurrency);
        }

        return paymentReconciliationCheckResult;
    }
}
