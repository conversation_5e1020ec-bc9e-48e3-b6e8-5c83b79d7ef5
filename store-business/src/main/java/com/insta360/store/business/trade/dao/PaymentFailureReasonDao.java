package com.insta360.store.business.trade.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.trade.model.PaymentFailureReason;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-12-17
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface PaymentFailureReasonDao extends BaseDao<PaymentFailureReason> {

}
