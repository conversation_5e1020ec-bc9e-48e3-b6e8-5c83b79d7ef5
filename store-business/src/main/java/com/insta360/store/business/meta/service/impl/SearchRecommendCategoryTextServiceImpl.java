package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.dao.SearchRecommendCategoryTextDao;
import com.insta360.store.business.meta.model.SearchRecommendCategoryText;
import com.insta360.store.business.meta.model.SearchRecommendItemText;
import com.insta360.store.business.meta.service.SearchRecommendCategoryTextService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
@Service
public class SearchRecommendCategoryTextServiceImpl extends ServiceImpl<SearchRecommendCategoryTextDao, SearchRecommendCategoryText> implements SearchRecommendCategoryTextService {

    @Override
    public void deleteByItemId(Integer categoryId) {
        if (categoryId == null) {
            return;
        }

        // 先查再删
        List<SearchRecommendCategoryText> searchRecommendCategoryTexts = listByCategoryId(categoryId);
        if (CollectionUtils.isNotEmpty(searchRecommendCategoryTexts)) {
            List<Integer> searchRecommendItemTextIds = searchRecommendCategoryTexts.stream()
                    .map(SearchRecommendCategoryText::getId).collect(Collectors.toList());
            baseMapper.deleteBatchIds(searchRecommendItemTextIds);
        }
    }

    @Override
    public void addSearchRecommendCategoryTexts(List<SearchRecommendCategoryText> searchRecommendCategoryTexts) {
        baseMapper.addSearchRecommendCategoryTexts(searchRecommendCategoryTexts);
    }

    @Override
    public List<SearchRecommendCategoryText> listByCategoryId(Integer categoryId) {
        QueryWrapper<SearchRecommendCategoryText> qw = new QueryWrapper<>();
        qw.eq("recommend_category_id", categoryId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<SearchRecommendCategoryText> listByCategoryIds(List<Integer> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<SearchRecommendCategoryText> qw = new QueryWrapper<>();
        qw.in("recommend_category_id", categoryIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<SearchRecommendCategoryText> listByLanguage(List<Integer> categoryIds, InstaCountry country, InstaLanguage language) {
        if (CollectionUtils.isEmpty(categoryIds) || Objects.isNull(country) || Objects.isNull(language)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<SearchRecommendCategoryText> qw = new QueryWrapper<>();
        qw.in("recommend_category_id", categoryIds);
        qw.eq("country", country.name());
        qw.eq("language", language.name());
        return baseMapper.selectList(qw);
    }
}
