package com.insta360.store.business.trade.dto;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 订单二次支付页税费重计算DTO
 * @Date 2023/7/25
 */
public class OrderPayedAgainCalculateTaxDTO implements Serializable {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号必填")
    private String orderNumber;

    /**
     * 预选的支付渠道
     */
    @NotBlank(message = "支付渠道必填")
    private String prePaymentChannel;

    /**
     * 与支付渠道关联的唯一标识
     */
    private String uniqueKey;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getPrePaymentChannel() {
        return prePaymentChannel;
    }

    public void setPrePaymentChannel(String prePaymentChannel) {
        this.prePaymentChannel = prePaymentChannel;
    }

    public String getUniqueKey() {
        return uniqueKey;
    }

    public void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    @Override
    public String toString() {
        return "OrderPayedAgainCalculateTaxDTO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", prePaymentChannel='" + prePaymentChannel + '\'' +
                ", uniqueKey='" + uniqueKey + '\'' +
                '}';
    }
}
