package com.insta360.store.business.payment.lib.affirm;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: hyc
 * @Date: 2019/1/21
 * @Description:
 */
@Configuration
public class AffirmConfiguration {

    @Value("${payment.affirm.public_api_key}")
    protected String publicApiKey;

    @Value("${payment.affirm.private_api_key}")
    protected String privateApiKey;

    @Value("${payment.affirm.api_url}")
    protected String apiUrl;

    @Value("${payment.affirm.dashboard_url}")
    protected String dashboardUrl;

    @Value("${payment.affirm.script_url}")
    protected String scriptUrl;

    public String getPublicApiKey() {
        return publicApiKey;
    }

    public String getPrivateApiKey() {
        return privateApiKey;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public String getDashboardUrl() {
        return dashboardUrl;
    }

    public String getScriptUrl() {
        return scriptUrl;
    }
}
