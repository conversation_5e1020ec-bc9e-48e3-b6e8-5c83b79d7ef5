package com.insta360.store.business.admin.order.print.helper;

import com.google.common.collect.Lists;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.admin.order.dto.BatchDeclarationDTO;
import com.insta360.store.business.admin.order.dto.BatchPrintDTO;
import com.insta360.store.business.admin.order.enums.DeclareCurrencyType;
import com.insta360.store.business.admin.order.enums.OrderSource;
import com.insta360.store.business.admin.order.print.handler.MbOrderDeclareHandler;
import com.insta360.store.business.admin.order.print.handler.StoreOrderDeclareHandler;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.integration.mabang.model.MbOrder;
import com.insta360.store.business.integration.mabang.model.MbOrderDelivery;
import com.insta360.store.business.integration.mabang.service.MbOrderDeliveryService;
import com.insta360.store.business.integration.mabang.service.MbOrderService;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderDeliveryPartly;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderDeliveryPartlyService;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @Description 订单单一窗口报关处理类
 * @Date 2023/5/22
 */
@Component
public class OrderSingleWindowDeclareHelper {

    @Autowired
    private OrderDeliveryService orderDeliveryService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private MbOrderDeliveryService mbOrderDeliveryService;

    @Autowired
    private MbOrderService mbOrderService;

    @Autowired
    private OrderPaymentService orderPaymentService;

    @Autowired
    private OrderDeliveryPartlyService orderDeliveryPartlyService;


    /**
     * 批量申报数据检查
     *
     * @param batchDeclarationDto
     */
    public void declareBatch(BatchDeclarationDTO batchDeclarationDto) {
        // 查询参数
        List<String> waybillNumbers = batchDeclarationDto.getWaybillNumbers();
        LocalDateTime startTime = batchDeclarationDto.getStartTime();
        LocalDateTime endTime = batchDeclarationDto.getEndTime();

        OrderSource orderSource = OrderSource.matchCode(batchDeclarationDto.getOrderSource());
        switch (orderSource) {
            case STORE:
                storeOrderCheck(waybillNumbers, startTime, endTime);
                break;
            case LOTTE:
                lotteOrderCheck(waybillNumbers, startTime, endTime);
                break;
            default:
                throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
    }

    /**
     * 执行批量打印
     *
     * @param batchDeclarationDto
     */
    @Async
    public void printExecute(BatchDeclarationDTO batchDeclarationDto) {
        // 是否美元报关
        boolean isUsd = DeclareCurrencyType.usd.equals(DeclareCurrencyType.matchCode(batchDeclarationDto.getDeclareCurrency()));
        // 申报口岸
        Integer declarationPort = batchDeclarationDto.getDeclarationPort();
        // 订单来源
        OrderSource orderSource = OrderSource.matchCode(batchDeclarationDto.getOrderSource());
        switch (orderSource) {
            case STORE:
                List<OrderDelivery> orderDeliveryList = orderDeliveryService.listByExpressCodeAndTime(batchDeclarationDto.getWaybillNumbers(), batchDeclarationDto.getStartTime(), batchDeclarationDto.getEndTime());
                List<Integer> orderIdList = orderDeliveryList.stream()
                        .map(OrderDelivery::getOrder)
                        .collect(Collectors.toList());
                // 所有订单
                List<Order> orderList = (List<Order>) orderService.listByIds(orderIdList);

                StoreOrderDeclareHandler storeOrderDeclareHandler = ApplicationContextHolder.getApplicationContext()
                        .getBean(StoreOrderDeclareHandler.class);
                storeOrderDeclareHandler.multiChannelPrint(new BatchPrintDTO(orderList, isUsd, declarationPort, batchDeclarationDto.getJobNumber()));
                break;
            case LOTTE:
                List<MbOrderDelivery> mbOrderDeliveryList = mbOrderDeliveryService.listByExpressCodeAndTime(batchDeclarationDto.getWaybillNumbers(), batchDeclarationDto.getStartTime(), batchDeclarationDto.getEndTime());
                List<Integer> mbOrderIdList = mbOrderDeliveryList.stream()
                        .map(MbOrderDelivery::getOrder)
                        .collect(Collectors.toList());
                // 所有订单
                List<MbOrder> mbOrderList = (List<MbOrder>) mbOrderService.listByIds(mbOrderIdList);

                MbOrderDeclareHandler mbOrderDeclareHandler = ApplicationContextHolder.getApplicationContext()
                        .getBean(MbOrderDeclareHandler.class);
                mbOrderDeclareHandler.multiChannelPrint(new BatchPrintDTO(mbOrderList, isUsd, declarationPort, batchDeclarationDto.getJobNumber()));
                break;
            default:
                throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
    }

    /**
     * 乐天订单基础数据检查
     *
     * @param waybillNumbers
     * @param startTime
     * @param endTime
     */
    private void lotteOrderCheck(List<String> waybillNumbers, LocalDateTime startTime, LocalDateTime endTime) {
        List<MbOrderDelivery> mbOrderDeliveryList = mbOrderDeliveryService.listByExpressCodeAndTime(waybillNumbers, startTime, endTime);
        if (CollectionUtils.isEmpty(mbOrderDeliveryList)) {
            throw new InstaException(-1, "运单号均未查询到订单信息");
        }

        // 大陆订单场景
        List<String> orderCnList = mbOrderDeliveryList.stream()
                .filter(mbOrderDelivery -> InstaCountry.CN.equals(mbOrderDelivery.country()))
                .map(MbOrderDelivery::getExpressCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderCnList)) {
            throw new InstaException(-1, String.format("运单号%s为大陆订单，请检查", StringUtils.join(orderCnList)));
        }

        // 合并发货场景
        List<String> repeatWaybillNumbers = mbOrderDeliveryList.stream()
                .collect(groupingBy(MbOrderDelivery::getExpressCode, Collectors.counting()))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(repeatWaybillNumbers)) {
            throw new InstaException(-1, String.format("运单号%s为合并发货的情况，请在'单对单打印'中手动调整报关资料", StringUtils.join(repeatWaybillNumbers)));
        }

        // 运单号合法性校验场景
        Map<String, List<MbOrderDelivery>> mbOrderDeliveryMap = mbOrderDeliveryList.stream()
                .collect(groupingBy(MbOrderDelivery::getExpressCode));
        if (mbOrderDeliveryMap.size() != waybillNumbers.size()) {
            List<String> notExistWaybillList = waybillNumbers.stream()
                    .filter(waybillNumber -> !mbOrderDeliveryMap.containsKey(waybillNumber))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExistWaybillList)) {
                throw new InstaException(-1, String.format("运单号%s在该平台不存在", StringUtils.join(notExistWaybillList)));
            }
        }

        // 订单合并发货场景
        List<String> mergeWaybillList = mbOrderDeliveryMap.entrySet()
                .stream()
                .map(
                        entry -> {
                            if (entry.getValue()
                                    .size() > 1) {
                                return entry.getKey();
                            }
                            return null;
                        })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mergeWaybillList)) {
            throw new InstaException(-1, String.format("运单号%s为合并发货的情况，请在'单对单打印'中手动调整报关资料", StringUtils.join(mergeWaybillList)));
        }

        List<Integer> orderIds = mbOrderDeliveryList.stream()
                .map(MbOrderDelivery::getOrder)
                .collect(Collectors.toList());
        // 拆单发货场景
        List<String> splitBillList = Lists.newArrayList();
        Map<Integer, List<MbOrderDelivery>> splitBillMap = Optional.ofNullable(mbOrderDeliveryService.listByOrderIds(orderIds))
                .map(list -> list.stream()
                        .collect(groupingBy(MbOrderDelivery::getOrder)))
                .orElse(new HashMap<>());
        splitBillMap.forEach((key, value) -> {
            if (value.size() > 1) {
                value.stream()
                        .forEach(mbOrderDelivery -> splitBillList.add(mbOrderDelivery.getExpressCode()));
            }
        });
        List<String> splitWaybillNumberList = waybillNumbers.stream()
                .filter(waybillNumber -> splitBillList.contains(waybillNumber))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(splitWaybillNumberList)) {
            throw new InstaException(-1, String.format("运单号%s为拆单发货的情况，请在'单对单打印'中手动调整报关资料", StringUtils.join(splitWaybillNumberList)));
        }
    }

    /**
     * 官方商城订单基础数据检查
     *
     * @param waybillNumbers
     * @param startTime
     * @param endTime
     */
    private void storeOrderCheck(List<String> waybillNumbers, LocalDateTime startTime, LocalDateTime endTime) {
        List<OrderDelivery> orderDeliveryList = orderDeliveryService.listByExpressCodeAndTime(waybillNumbers, startTime, endTime);
        if (CollectionUtils.isEmpty(orderDeliveryList)) {
            throw new InstaException(-1, "运单号均未查询到订单信息");
        }


        // 大陆订单场景
        List<String> orderCnList = orderDeliveryList.stream()
                .filter(orderDelivery -> InstaCountry.CN.equals(orderDelivery.country()))
                .map(OrderDelivery::getExpressCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(orderCnList)) {
            throw new InstaException(-1, String.format("运单号%s为大陆订单，请检查", StringUtils.join(orderCnList)));
        }

        // 合并发货场景
        List<String> repeatWaybillNumbers = orderDeliveryList.stream()
                .collect(groupingBy(OrderDelivery::getExpressCode, Collectors.counting()))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(repeatWaybillNumbers)) {
            throw new InstaException(-1, String.format("运单号%s为合并发货的情况，请在'单对单打印'中手动调整报关资料", StringUtils.join(repeatWaybillNumbers)));
        }

        // 拆单发货场景
        List<Integer> orderIds = orderDeliveryList.stream()
                .map(OrderDelivery::getOrder)
                .collect(Collectors.toList());

        List<OrderDeliveryPartly> splitBillOrderDeliveryPartlyList = orderDeliveryPartlyService.listDeliveryPartlyByOrder(orderIds);
        List<String> splitBillList = Lists.newArrayList();
        Map<Integer, List<OrderDeliveryPartly>> splitBillDeliveryPartlyMap = splitBillOrderDeliveryPartlyList.stream()
                .collect(groupingBy(OrderDeliveryPartly::getOrderId));

        splitBillDeliveryPartlyMap.forEach((key, value) -> {
            Set<String> expressCodeSet = value.stream()
                    .map(OrderDeliveryPartly::getExpressCode)
                    .collect(Collectors.toSet());
            if (expressCodeSet.size() > 1) {
                splitBillList.addAll(expressCodeSet);
            }
        });

        List<String> splitWaybillNumberList = waybillNumbers.stream()
                .filter(waybillNumber -> splitBillList.contains(waybillNumber))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(splitWaybillNumberList)) {
            throw new InstaException(-1, String.format("运单号%s为拆单发货的情况，请在'单对单打印'中手动调整报关资料", StringUtils.join(splitWaybillNumberList)));
        }

        // 查询出订单物流明细
        List<OrderDeliveryPartly> orderDeliveryPartlyList = orderDeliveryPartlyService.listByExpressCodeAndTime(waybillNumbers, startTime, endTime);
        // 运单号合法性校验场景
        Map<String, List<OrderDeliveryPartly>> orderDeliveryMap = orderDeliveryPartlyList.stream()
                .collect(groupingBy(OrderDeliveryPartly::getExpressCode));
        if (orderDeliveryMap.size() != waybillNumbers.size()) {
            List<String> notExistWaybillList = waybillNumbers.stream()
                    .filter(waybillNumber -> !orderDeliveryMap.containsKey(waybillNumber))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExistWaybillList)) {
                throw new InstaException(-1, String.format("运单号%s在该平台不存在", StringUtils.join(notExistWaybillList)));
            }
        }

        // 订单合并发货场景
        List<String> mergeWaybillList = orderDeliveryMap.entrySet()
                .stream()
                .map(
                        entry -> {
                            List<OrderDeliveryPartly> deliveryPartlies = entry.getValue();
                            Set<Integer> orderIdSet = deliveryPartlies.stream()
                                    .map(OrderDeliveryPartly::getOrderId)
                                    .collect(Collectors.toSet());
                            if (orderIdSet.size() > 1) {
                                return entry.getKey();
                            }
                            return null;
                        })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mergeWaybillList)) {
            throw new InstaException(-1, String.format("运单号%s为合并发货的情况，请在'单对单打印'中手动调整报关资料", StringUtils.join(mergeWaybillList)));
        }

        // 订单&运单Map
//        Map<Integer, String> expressCodeMap = orderDeliveryPartlyList.stream()
//                .collect(Collectors.toMap(OrderDeliveryPartly::getOrderId, OrderDeliveryPartly::getExpressCode, (oldValue, newValue) -> newValue));
//        List<Integer> orderIdList = orderDeliveryPartlyList.stream()
//                .map(OrderDeliveryPartly::getOrderId)
//                .distinct()
//                .collect(Collectors.toList());

        // 内部支付渠道订单ID列表
//        List<Integer> internalPayOrderIdList = orderPaymentService.listByOrderIds(orderIdList)
//                .stream()
//                .filter(orderPayment -> PaymentChannel.isInternalPayFilterChannel(orderPayment.paymentChannel()))
//                .map(OrderPayment::getOrder)
//                .collect(Collectors.toList());

        // 订单为市场领用等内部支付运单号
//        List<String> internalPayWaybillNumberList = internalPayOrderIdList.stream()
//                .map(orderId -> expressCodeMap.get(orderId))
//                .filter(StringUtils::isNotBlank)
//                .collect(Collectors.toList());

//        if (CollectionUtils.isNotEmpty(internalPayWaybillNumberList)) {
//            throw new InstaException(-1, String.format("运单号%s为领用订单，请检查", StringUtils.join(internalPayWaybillNumberList)));
//        }
    }
}
