package com.insta360.store.business.payment.lib.checkout.model;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * @Author: wbt
 * @Date: 2021/11/15
 * @Description:
 */
public class Links {

    /**
     * 支付跳转链接
     */
    private Self self;

    /**
     * 指向支付相关操作的链接
     */
    private Actions actions;

    /**
     * 3D验证链接
     */
    private Redirect redirect;

    /**
     * Void a payment才会有
     */
    @JSONField(name = "void")
    private Void aVoid;

    /**
     * capture a payment才会有
     */
    private Capture capture;

    /**
     * refund a payment才会有
     */
    private Refund refund;

    public Self getSelf() {
        return self;
    }

    public void setSelf(Self self) {
        this.self = self;
    }

    public Actions getActions() {
        return actions;
    }

    public void setActions(Actions actions) {
        this.actions = actions;
    }

    public Redirect getRedirect() {
        return redirect;
    }

    public void setRedirect(Redirect redirect) {
        this.redirect = redirect;
    }

    public Void getaVoid() {
        return aVoid;
    }

    public void setaVoid(Void aVoid) {
        this.aVoid = aVoid;
    }

    public Capture getCapture() {
        return capture;
    }

    public void setCapture(Capture capture) {
        this.capture = capture;
    }

    public Refund getRefund() {
        return refund;
    }

    public void setRefund(Refund refund) {
        this.refund = refund;
    }

    @Override
    public String toString() {
        return "Links{" +
                "self=" + self +
                ", actions=" + actions +
                ", redirect=" + redirect +
                ", aVoid=" + aVoid +
                ", capture=" + capture +
                ", refund=" + refund +
                '}';
    }
}
