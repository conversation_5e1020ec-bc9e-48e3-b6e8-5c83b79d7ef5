package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.dao.ActivityLocalesConfigDao;
import com.insta360.store.business.meta.model.ActivityLocalesConfig;
import com.insta360.store.business.meta.service.ActivityLocalesConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-01-13
 * @Description:
 */
@Service
public class ActivityLocalesConfigServiceImpl extends BaseServiceImpl<ActivityLocalesConfigDao, ActivityLocalesConfig> implements ActivityLocalesConfigService {

    @Override
    public ActivityLocalesConfig getByActivityIdCountry(Integer activityId, InstaCountry country) {
        QueryWrapper<ActivityLocalesConfig> qw = new QueryWrapper<>();
        qw.eq("activity_id", activityId);
        qw.eq("country", country.name());
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<ActivityLocalesConfig> listActivityIds(List<Integer> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)){
            return new ArrayList<>(0);
        }
        QueryWrapper<ActivityLocalesConfig> qw = new QueryWrapper<>();
        qw.in("activity_id", activityIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ActivityLocalesConfig> listActivityId(Integer activityId) {
        QueryWrapper<ActivityLocalesConfig> qw = new QueryWrapper<>();
        qw.eq("activity_id", activityId);
        return baseMapper.selectList(qw);
    }

    @Override
    public void removeByActivityId(Integer activityId) {
        QueryWrapper<ActivityLocalesConfig> qw = new QueryWrapper<>();
        qw.eq("activity_id", activityId);
        baseMapper.delete(qw);
    }
}
