package com.insta360.store.business.order.email;

import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2020/08/10
 * @Description: 订单支付成功包含相机且不包含Care服务发送Care促销邮件(包含OneR)
 */
@Scope("prototype")
@Component
public class DeviceInsuranceSpecialNotifyEmail extends BaseOrderEmail {

    @Override
    public String getTemplateName() {
        return "device_insurance_notify_order_payed_oner";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        // 客户名称
        templateParams.addBodyParam("customer_name", this.getCustomerName());
    }
}
