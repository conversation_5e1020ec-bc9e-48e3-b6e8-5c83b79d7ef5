package com.insta360.store.business.configuration.operation.bo;

import com.insta360.store.business.configuration.operation.eums.BusinessType;
import com.insta360.store.business.configuration.operation.eums.HandleType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/16
 */
public class BusinessLogBO implements Serializable {

    /**
     * 业务描述
     */
    private String operationContent;

    /**
     * 业务主键ID
     */
    private Integer businessId;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 业务类型
     */
    private BusinessType businessType;

    /**
     * 操作类型
     */
    private HandleType handleType;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    public String getOperationContent() {
        return operationContent;
    }

    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public BusinessType getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BusinessType businessType) {
        this.businessType = businessType;
    }

    public HandleType getHandleType() {
        return handleType;
    }

    public void setHandleType(HandleType handleType) {
        this.handleType = handleType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }
}
