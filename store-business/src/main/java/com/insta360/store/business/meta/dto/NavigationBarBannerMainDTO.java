package com.insta360.store.business.meta.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarBannerMain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/5/26
 * @Description:
 */
public class NavigationBarBannerMainDTO implements Serializable {

    private Integer id;

    /**
     * 一级分类内部id
     */
    @NotNull(message = "一级分类内部id不允许为空")
    private Integer categoryInsideId;

    /**
     * banner内部名称
     */
    @NotBlank(message = "banner内部名称不允许为空")
    private String bannerInsideName;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不允许为空")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不允许为空")
    private LocalDateTime endTime;

    /**
     * 跳转链接
     */
    @NotBlank(message = "跳转链接不允许为空")
    private String urlLink;

    /**
     * 是否在移动端展示
     */
    private Boolean isMobile;

    /**
     * 是否在pc端展示
     */
    private Boolean isPc;

    /**
     * 排序
     */
    @NotNull(message = "排序不允许为空")
    private Integer orderIndex;

    public NavigationBarBannerMain getPojoObject() {
        NavigationBarBannerMain bannerMain = new NavigationBarBannerMain();
        BeanUtil.copyProperties(this, bannerMain);
        return bannerMain;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public void setUrlLink(String urlLink) {
        this.urlLink = urlLink;
    }

    public Boolean getIsMobile() {
        return isMobile;
    }

    public void setIsMobile(Boolean mobile) {
        isMobile = mobile;
    }

    public Boolean getIsPc() {
        return isPc;
    }

    public void setIsPc(Boolean pc) {
        isPc = pc;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getBannerInsideName() {
        return bannerInsideName;
    }

    public void setBannerInsideName(String bannerInsideName) {
        this.bannerInsideName = bannerInsideName;
    }

    @Override
    public String toString() {
        return "NavigationBarBannerMainDTO{" +
                "id=" + id +
                ", categoryInsideId=" + categoryInsideId +
                ", bannerInsideName='" + bannerInsideName + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", urlLink='" + urlLink + '\'' +
                ", isMobile=" + isMobile +
                ", isPc=" + isPc +
                ", orderIndex=" + orderIndex +
                '}';
    }
}
