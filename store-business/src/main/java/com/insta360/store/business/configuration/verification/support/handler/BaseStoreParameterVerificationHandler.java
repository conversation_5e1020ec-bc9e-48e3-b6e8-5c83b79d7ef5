package com.insta360.store.business.configuration.verification.support.handler;

import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.verification.enums.ParameterBusinessType;
import com.insta360.store.business.configuration.verification.support.helper.GrafanaInterfaceParamVerificationHelper;
import com.insta360.store.business.product.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Description 商城接口参数校验基础处理服务
 * @Date 2022/10/14
 */
public abstract class BaseStoreParameterVerificationHandler {

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    GrafanaInterfaceParamVerificationHelper grafanaInterfaceParamVerificationHelper;

    /**
     * 参数校验
     *
     * @param type
     * @param obj
     */
    public abstract void parameterCheck(ParameterBusinessType type, Object obj);

    /**
     * 添加缓存
     *
     * @param obj
     */
    public abstract void putCache(String obj);

    /**
     * 初始化缓存
     */
    public abstract void initCache();

    /**
     * 获取所有缓存值
     * @return
     */
    public abstract String getAllCache();

    /**
     * 初始化容量
     *
     * @param size
     * @return
     */
    int initCapacity(int size) {
        return size + (size / 3);
    }
}
