package com.insta360.store.business.integration.fedex.service.handler;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.compass.libs.aliyun.oss.enums.EndpointEnum;
import com.insta360.compass.libs.aliyun.oss.enums.ModuleEnum;
import com.insta360.store.business.admin.order.service.impl.handler.FedexVatExportHandler;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.integration.fedex.bo.FedexApiContextBO;
import com.insta360.store.business.integration.fedex.config.FedexConfigurable;
import com.insta360.store.business.integration.fedex.constant.FedexCommonConstant;
import com.insta360.store.business.integration.fedex.enums.FedexLogisticsTimeType;
import com.insta360.store.business.integration.fedex.exception.FedexApiErrorCode;
import com.insta360.store.business.integration.fedex.lib.request.FedexAuthorizationRequest;
import com.insta360.store.business.integration.fedex.lib.response.FedexAuthorizationResponse;
import com.insta360.store.business.integration.fedex.service.FedexShipmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/22
 */
public abstract class BaseFedexApiHandler implements FedexShipmentService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseFedexApiHandler.class);

    @Autowired
    FedexVatExportHandler fedexVatExportHandler;

    @Autowired
    FedexConfigurable fedexConfigurable;

    @Autowired
    OSSService ossService;


    /**
     * 获取FedexVatContext
     * @param orderNumbers
     * @return
     */
    protected List<FedexApiContextBO> listFedexVatContext(List<String> orderNumbers) {
        return fedexVatExportHandler.listFedexVatContext(orderNumbers);
    }

    /**
     * 获取物流时效类型
     * @param fedexLogisticsTimeType
     * @return
     */
    protected FedexLogisticsTimeType getFedexLogisticsTimeType(Integer fedexLogisticsTimeType) {
        return FedexLogisticsTimeType.parse(fedexLogisticsTimeType);
    }

    /**
     * 获取Fedex token
     * @return
     */
    protected String getAccessToken() {
        Object token = RedisTemplateUtil.getValue(FedexCommonConstant.FEDEX_API_TOKEN_KEY);
        if(Objects.nonNull(token)) {
            return (String) token;
        }

        // 调用Fedex auth API鉴权获取token
        FedexAuthorizationRequest authorizationRequest = new FedexAuthorizationRequest(fedexConfigurable);
        String result = null;
        try {
            result = authorizationRequest.executePost();
        } catch (Exception e) {
            LOGGER.error("调用Fedex Authorization API 获取token异常",e);
        }

        FedexAuthorizationResponse response = FedexAuthorizationResponse.parse(result);
        if(response.isErrorResponse()) {
            throw new InstaException(FedexApiErrorCode.FEDEX_AUTHORIZATION_API_EXCEPTION);
        }

        String access_token = response.getAccess_token();
        // 将访问token放入缓存
        RedisTemplateUtil.setKeyValueOrExpire(FedexCommonConstant.FEDEX_API_TOKEN_KEY, access_token, 55L, TimeUnit.MINUTES);

        return access_token;
    }

    /**
     * 上传文件到OSS
     * @param bytes
     * @param fileName
     * @return
     */
    protected String uploadOss(byte[] bytes, String fileName) {
        String url = null;
        try {
            url = ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, bytes, fileName);
        } catch (Exception e) {
            LOGGER.error("Fedex API 运单文档上传oss异常",e);
        }

        return url;
    }
}
