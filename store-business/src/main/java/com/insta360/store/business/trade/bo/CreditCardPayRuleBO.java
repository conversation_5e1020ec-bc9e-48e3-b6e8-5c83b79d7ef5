package com.insta360.store.business.trade.bo;

import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;

/**
 * @Author: wbt
 * @Date: 2021/12/22
 * @Description:
 */
public class CreditCardPayRuleBO {

    /**
     * 支付渠道
     */
    private Integer payChannelId;

    /**
     * 支付机构
     */
    private StorePaymentMethodEnum creditCardPaymentMethodEnum;

    public CreditCardPayRuleBO(StorePaymentMethodEnum creditCardPaymentMethodEnum, Integer payChannelId) {
        this.creditCardPaymentMethodEnum = creditCardPaymentMethodEnum;
        this.payChannelId = payChannelId;
    }

    public Integer getPayChannelId() {
        return payChannelId;
    }

    public void setPayChannelId(Integer payChannelId) {
        this.payChannelId = payChannelId;
    }

    public StorePaymentMethodEnum getCreditCardPaymentMethodEnum() {
        return creditCardPaymentMethodEnum;
    }

    public void setCreditCardPaymentMethodEnum(StorePaymentMethodEnum creditCardPaymentMethodEnum) {
        this.creditCardPaymentMethodEnum = creditCardPaymentMethodEnum;
    }

    @Override
    public String toString() {
        return "CreditCardPayRuleBO{" +
                "payChannelId=" + payChannelId +
                ", creditCardPaymentMethodEnum=" + creditCardPaymentMethodEnum +
                '}';
    }
}
