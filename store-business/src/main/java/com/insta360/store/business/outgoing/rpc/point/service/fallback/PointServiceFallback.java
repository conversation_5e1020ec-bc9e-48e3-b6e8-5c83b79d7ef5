package com.insta360.store.business.outgoing.rpc.point.service.fallback;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.outgoing.rpc.point.dto.PointAccount;
import com.insta360.store.business.outgoing.rpc.point.service.PointService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2021/05/29
 * @Description:
 */
@Component
public class PointServiceFallback implements PointService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PointServiceFallback.class);

    @Override
    public Response putAndCheck(PointAccount pointAcount) {
        LOGGER.error("point-service调用失败。路径：/rpc/point/service/pointChange/putAndCheck。参数：【pointAcount: " + pointAcount + "】");
        return Response.failed("积分调用失败");
    }
}
