package com.insta360.store.business.configuration.graphql;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4
 * @description GraphQL响应封装类，用于处理GraphQL响应结果
 */
public class GraphqlResponse {

    /**
     * HTTP状态码
     */
    private Integer httpCode;

    /**
     * 响应体字符串
     */
    private String responseBody;

    public GraphqlResponse(Integer httpCode, String responseBody) {
        this.httpCode = httpCode;
        this.responseBody = responseBody;
    }

    public Integer getHttpCode() {
        return httpCode;
    }

    public void setHttpCode(Integer httpCode) {
        this.httpCode = httpCode;
    }

    public String getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody;
    }
}
