package com.insta360.store.business.reseller.enums;

/**
 * <AUTHOR>
 * @Description 分销商赠品标题类型枚举
 * @Date 2021/8/16
 */
public enum ResellerTitleType {
    DEFAULT_GIFT(0,"默认赠品"),
    RESELLER_GIFT_FINE_GRAINED(1,"分销商专属赠品(细粒度)"),
    RESELLER_GIFT_COARSE_GRAINED(2,"分销商专属赠品(粗粒度)");

    public final int code;

    public final String value;

    ResellerTitleType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static ResellerTitleType matchCode(int code) {
        for (ResellerTitleType titleType : ResellerTitleType.values()) {
            if(titleType.code == code) {
                return titleType;
            }
        }
        return null;
    }

    public static boolean isExist(int code) {
        for (ResellerTitleType titleType : ResellerTitleType.values()) {
            if(titleType.code == code) {
                return true;
            }
        }
        return false;
    }
}
