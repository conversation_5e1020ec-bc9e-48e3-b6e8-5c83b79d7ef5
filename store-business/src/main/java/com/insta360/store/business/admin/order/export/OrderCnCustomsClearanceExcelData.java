package com.insta360.store.business.admin.order.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.insta360.store.business.admin.order.enums.ItemUnit;
import com.insta360.store.business.admin.order.enums.ShipToCountry;
import com.insta360.store.business.admin.order.service.impl.handler.bo.CnCustomsClearanceBO;
import com.insta360.store.business.commodity.model.CommodityMeta;
import com.insta360.store.business.order.model.OrderItem;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 国内清关Excel数据映射实体
 * @Date 2024/3/12
 */
public class OrderCnCustomsClearanceExcelData implements Serializable {

    @ExcelProperty("订单编号")
    private String orderNumber;

    @ExcelProperty("运单号")
    private String waybillNumber;

    @ExcelProperty("商品项号")
    private Integer sortId;

    @ExcelProperty("商品货号")
    private String productCode;

    @ExcelProperty("商品海关编码")
    private String customsCode;

    @ExcelProperty("商品名称")
    private String productName;

    @ExcelProperty("规格型号")
    private String specifications;

    @ExcelProperty("申报要素")
    private String declaration;

    @ExcelProperty("申报单位")
    private String unit;

    @ExcelProperty("数量")
    private String quantity;

    @ExcelProperty("单价")
    private String price;

    @ExcelProperty("净重（公斤）")
    private String netWeight;

    @ExcelProperty("毛重（公斤）")
    private String grossWeight;

    @ExcelProperty("币制")
    private String currency;

    @ExcelProperty("运抵国")
    private String shipToCountry;

    @ExcelProperty("保费")
    private String insuranceFee;

    @ExcelProperty("运费")
    private String freight;

    /**
     * 构建
     *
     * @param cnCustomsClearanceParam
     * @return
     */
    public static OrderCnCustomsClearanceExcelData build(CnCustomsClearanceBO cnCustomsClearanceParam) {
        // 订单商品
        OrderItem orderItem = cnCustomsClearanceParam.getOrderItem();
        // 套餐报关配置
        CommodityMeta commodityMeta = cnCustomsClearanceParam.getCommodityMeta();
        // 运抵国
        ShipToCountry shipToCountry = cnCustomsClearanceParam.getShipToCountry();

        OrderCnCustomsClearanceExcelData excelData = new OrderCnCustomsClearanceExcelData();
        excelData.setOrderNumber(cnCustomsClearanceParam.getOrderNumber());
        excelData.setWaybillNumber(cnCustomsClearanceParam.getWaybillNumber());
        excelData.setSortId(cnCustomsClearanceParam.getSortId());
        excelData.setProductCode(formatProductCode(commodityMeta.getOfficialNameEn()));
        excelData.setCustomsCode(formatCustomsCode(commodityMeta.getCustomCode()));
        excelData.setProductName(commodityMeta.getOfficialNameCn());
        excelData.setSpecifications(formatSpecifications(commodityMeta.getModelNumber()));
        excelData.setDeclaration(commodityMeta.getModel());
        excelData.setUnit(formatUnit(commodityMeta.getUnit()));
        excelData.setQuantity(orderItem.getNumber().toString());
        excelData.setPrice(orderItem.getPrice().toString());
        excelData.setNetWeight(commodityMeta.getNw().toString());
        excelData.setGrossWeight(commodityMeta.getWeight().toString());
        excelData.setCurrency(orderItem.getCurrency());
        excelData.setShipToCountry(shipToCountry.getCode());
        excelData.setInsuranceFee(insuranceFeeCalculate(new BigDecimal(orderItem.getPrice().toString())).toString());
        excelData.setFreight("1");
        return excelData;
    }

    /**
     * 格式化商品货号
     * @param officialNameEn
     * @return
     */
    private static String formatProductCode(String officialNameEn) {
        return StringUtils.isNotBlank(officialNameEn) && officialNameEn.length() > 18 ? officialNameEn.substring(0,18) : officialNameEn;
    }

    /**
     * 格式化商品报关编码
     * @param customsCode
     * @return
     */
    private static String formatCustomsCode(String customsCode) {
        return StringUtils.isNotBlank(customsCode) ? JSON.parseObject(customsCode).getString("default") : "";
    }

    /**
     * 格式化商品规格
     * @param modelNumber
     * @return
     */
    private static String formatSpecifications(String modelNumber) {
        return StringUtils.isBlank(modelNumber) || modelNumber.equals("/") ? "无" : modelNumber;
    }

    /**
     * 格式化商品单位
     * @param unit
     * @return
     */
    private static String formatUnit(String unit) {
        ItemUnit itemUnit = ItemUnit.matchKey(unit);
        return Objects.nonNull(itemUnit) ? itemUnit.getVal() : null;
    }

    /**
     * 计算保险费
     * @param itemPrice
     * @return
     */
    private static BigDecimal insuranceFeeCalculate(BigDecimal itemPrice) {
        return itemPrice.multiply(new BigDecimal("0.003")).setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getWaybillNumber() {
        return waybillNumber;
    }

    public void setWaybillNumber(String waybillNumber) {
        this.waybillNumber = waybillNumber;
    }

    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCustomsCode() {
        return customsCode;
    }

    public void setCustomsCode(String customsCode) {
        this.customsCode = customsCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getDeclaration() {
        return declaration;
    }

    public void setDeclaration(String declaration) {
        this.declaration = declaration;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(String netWeight) {
        this.netWeight = netWeight;
    }

    public String getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(String grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getShipToCountry() {
        return shipToCountry;
    }

    public void setShipToCountry(String shipToCountry) {
        this.shipToCountry = shipToCountry;
    }

    public String getInsuranceFee() {
        return insuranceFee;
    }

    public void setInsuranceFee(String insuranceFee) {
        this.insuranceFee = insuranceFee;
    }

    public String getFreight() {
        return freight;
    }

    public void setFreight(String freight) {
        this.freight = freight;
    }

    @Override
    public String toString() {
        return "OrderCnCustomsClearanceExcelData{" +
                "orderNumber='" + orderNumber + '\'' +
                ", waybillNumber='" + waybillNumber + '\'' +
                ", sortId=" + sortId +
                ", productCode='" + productCode + '\'' +
                ", customsCode='" + customsCode + '\'' +
                ", productName='" + productName + '\'' +
                ", specifications='" + specifications + '\'' +
                ", declaration='" + declaration + '\'' +
                ", unit='" + unit + '\'' +
                ", quantity='" + quantity + '\'' +
                ", price='" + price + '\'' +
                ", netWeight='" + netWeight + '\'' +
                ", grossWeight='" + grossWeight + '\'' +
                ", currency='" + currency + '\'' +
                ", shipToCountry='" + shipToCountry + '\'' +
                ", insuranceFee='" + insuranceFee + '\'' +
                ", freight='" + freight + '\'' +
                '}';
    }
}
