package com.insta360.store.business.outgoing.mq.review.sender;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.outgoing.mq.review.dto.ReviewMessageDTO;
import com.insta360.store.business.review.enums.ReviewLikeEnum;
import com.insta360.store.business.review.model.Review;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2022/07/13
 * @Description:
 */
@Component
public class ReviewLikeSendMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReviewLikeSendMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.review_like, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * 评论点赞事件
     *
     * @param review
     * @param reviewLikeType
     */
    public void sendReviewLikeMessage(Review review, ReviewLikeEnum reviewLikeType) {
        if (review == null || reviewLikeType == null) {
            LOGGER.error("review info miss....");
            return;
        }

        // 数据封装
        ReviewMessageDTO reviewMessage = new ReviewMessageDTO();
        reviewMessage.setReview(review);
        reviewMessage.setReviewLikeType(reviewLikeType);
        String messageId = rocketTcpMessageSender.sendMessage(JSON.toJSONString(reviewMessage));
        MqUtils.isBlankMessageIdHandle(messageId, this, reviewMessage);

        LOGGER.info("review like message send success. review_id:{}", review.getId());
    }
}
