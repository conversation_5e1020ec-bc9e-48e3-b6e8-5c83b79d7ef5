package com.insta360.store.business.meta.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.meta.model.MetaModuleChannel;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-05-24
 * @Description:
 */
public interface MetaModuleChannelService extends BaseService<MetaModuleChannel> {

    /**
     * 获取默认的收款主体
     *
     * @return
     */
    MetaModuleChannel getDefaultModule();

    /**
     * 根据key获取
     *
     * @param key
     * @return
     */
    MetaModuleChannel getMetaModuleChannel(String key);
}
