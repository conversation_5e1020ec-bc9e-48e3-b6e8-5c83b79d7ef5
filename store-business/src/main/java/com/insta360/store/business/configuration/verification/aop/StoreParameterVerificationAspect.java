package com.insta360.store.business.configuration.verification.aop;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.verification.annotation.StoreParameterVerification;
import com.insta360.store.business.configuration.verification.enums.ParameterBusinessType;
import com.insta360.store.business.configuration.verification.exception.StoreParameterVerificationErrorCode;
import com.insta360.store.business.configuration.verification.support.factory.StoreParameterVerificationFactory;
import com.insta360.store.business.configuration.verification.support.handler.BaseStoreParameterVerificationHandler;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.CodeSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 商城参数校验切面
 * @Date 2022/10/12
 */
@Order(0)
@Aspect
@Component
public class StoreParameterVerificationAspect {

    private static final Logger log = LoggerFactory.getLogger(StoreParameterVerificationAspect.class);

    @Autowired
    StoreParameterVerificationFactory storeParameterVerificationFactory;

    @Before("@annotation(spv)")
    public void aroud(JoinPoint point, StoreParameterVerification spv) {
        try {
            // 参数值
            List<Object> args = Lists.newArrayList(point.getArgs());
            // 参数名
            List<String> parameterNames = Lists.newArrayList(((CodeSignature) point.getSignature()).getParameterNames());
            // 接口参数Map (key:参数名 value:参数值)
            Map<String, Object> parameterMap = parameterNames.stream().collect(Collectors.toMap(key -> key, parameterName -> args.get(parameterNames.indexOf(parameterName))));
            for (ParameterBusinessType businessType : ParameterBusinessType.values()) {
                // 参数名
                String parameterName = businessType.getParameterName();
                // 所有需校验的参数都走一遍
                if (!parameterMap.containsKey(parameterName)) {
                    continue;
                }
                Object value = parameterMap.get(parameterName);
                BaseStoreParameterVerificationHandler handler = storeParameterVerificationFactory.getStoreParameterVerificationHandler(businessType);
                handler.parameterCheck(businessType, value);
            }
        } catch (Exception e) {
            if (e instanceof InstaException) {
                if (((InstaException) e).getErrorCode() == StoreParameterVerificationErrorCode.CACHE_MISS_ERROR.getCode()) {
                    log.info("[商城接口参数合法性校验自定义注解]未命中缓存已拦截目标方法执行... args:{}", JSON.toJSONString(point.getArgs()));
                    throw e;
                }
            }
            log.error("[商城接口参数合法性校验自定义注解]发生非业务异常继续执行目标方法...", e);
        }
    }
}
