package com.insta360.store.business.order.email;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.AssertUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.admin.order.service.OrderExportInfoService;
import com.insta360.store.business.commodity.bo.SubscribeEmailRecommendationBO;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.commodity.service.impl.helper.CommodityRecommendHelper;
import com.insta360.store.business.common.constants.CommonConstant;
import com.insta360.store.business.configuration.gateway.GatewayConfiguration;
import com.insta360.store.business.configuration.utils.EncodingUtil;
import com.insta360.store.business.configuration.utils.ProfileUtil;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInfo;
import com.insta360.store.business.faq.model.FaqOtherQuestionBind;
import com.insta360.store.business.faq.model.FaqOtherQuestionQaBind;
import com.insta360.store.business.faq.service.FaqCategoryQuestionInfoService;
import com.insta360.store.business.faq.service.FaqOtherQuestionBindService;
import com.insta360.store.business.faq.service.FaqOtherQuestionQaBindService;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.email.BaseStoreEmail;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.model.MetaExpress;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.meta.service.MetaExpressService;
import com.insta360.store.business.order.bo.OrderPrintPdfCreateBO;
import com.insta360.store.business.order.email.constant.OrderEmailConstantPool;
import com.insta360.store.business.order.enums.RealEstateSoftwareType;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.order.service.*;
import com.insta360.store.business.order.service.impl.handler.OrderOverseasInvoiceHelper;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.trade.service.UserCartService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019/2/24
 * @Description:
 */
public abstract class BaseOrderEmail extends BaseStoreEmail implements OrderEmailConstantPool {

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    MetaExpressService metaExpressService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    OrderDeliveryPartlyService orderDeliveryPartlyService;

    @Autowired
    OrderStateInfoService orderStateInfoService;

    @Autowired
    OrderOverseasInvoiceHelper orderOverseasInvoiceHelper;

    @Autowired
    FaqOtherQuestionBindService faqOtherQuestionBindService;

    @Autowired
    FaqOtherQuestionQaBindService faqOtherQuestionQaBindService;

    @Autowired
    FaqCategoryQuestionInfoService faqCategoryQuestionInfoService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommodityRecommendHelper commodityRecommendHelper;

    @Autowired
    UserCartService userCartService;

    @Autowired
    GatewayConfiguration gatewayConfiguration;

    @Autowired
    OrderExportInfoService orderExportInfoService;

    /**
     * 用户教育邮件
     */
    protected static final Long DELAY_TIME = 4 * 24 * 60 * 60 * 1000L;

    /**
     * 订单
     */
    private Order order;

    /**
     * 物流号
     */
    private String expressCode;

    /**
     * care激活链接
     */
    private String careUrl;

    /**
     * 国家
     */
    private InstaCountry country;

    /**
     * 代金券code
     */
    private List<String> giftCodes;

    /**
     * 软件权益名称
     */
    private RealEstateSoftwareType softwareType;

    @Override
    protected Integer getSleepTime() {
        return SLEEP_TIME;
    }

    public RealEstateSoftwareType getSoftwareType() {
        return softwareType;
    }

    public void setSoftwareType(RealEstateSoftwareType softwareType) {
        this.softwareType = softwareType;
    }

    public InstaCountry getCountry() {
        return country;
    }

    public void setCountry(InstaCountry country) {
        this.country = country;
    }

    void setOrder(Order order) {
        this.order = order;
    }

    protected Order getOrder() {
        return order;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getCareUrl() {
        return careUrl;
    }

    public void setCareUrl(String careUrl) {
        this.careUrl = careUrl;
    }

    public List<String> getGiftCodes() {
        return giftCodes;
    }

    public void setGiftCodes(List<String> giftCodes) {
        this.giftCodes = giftCodes;
    }

    @Override
    public void doSend(String toAddress) {
        AssertUtil.notNull(order);
        super.doSend(toAddress);
    }

    @Override
    protected InstaLanguage getLanguage() {
        InstaLanguage language = countryConfigService.getCountryLanguage(order.country());
        return language != null ? language : DEFAULT_LANGUAGE;
    }

    @Override
    protected Long getDelayTimeConfig() {
        return DEFAULT_DELAY_TIME;
    }

    protected String getOrderNumber() {
        return order.getOrderNumber();
    }

    /**
     * RSA 加密 订单号 -> encode 转码
     */
    protected String getOrderNumberByRSA() {
        Order order = getOrder();
        String encryptText = RSAUtil.encryptByPub(order.getOrderNumber());
        return EncodingUtil.encode(encryptText);
    }

    /**
     * 订单创建时间
     *
     * @return
     */
    protected String getOrderCreateTime() {
        return DateTimeFormatter.ofPattern("yyyy-MM-dd").format(order.getCreateTime());
    }

    /**
     * 订单子项
     */
    protected JSONArray getOrderItems() {
        JSONArray items = new JSONArray();
        InstaLanguage language = getLanguage();
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        orderItems
                .forEach(orderItem -> {
                    JSONObject itemJson = new JSONObject();

                    Commodity commodity = commodityService.getById(orderItem.getCommodity());
                    CommodityInfo commodityInfo = commodityInfoService.getInfoDefaultEnglish(commodity.getId(), language);
                    ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(orderItem.getProduct(), language);
                    CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(commodity.getId());

                    String commodityName = commodityInfo.getName().trim();
                    String productName = productInfo.getName().trim();
                    String image = commodityDisplay.getUrl();
                    Integer number = orderItem.getNumber();
                    Currency currency = orderItem.currency();
                    Float price = orderItem.getPrice();
                    Boolean isGift = orderItem.getIsGift();

                    itemJson.put("product_name", productName);
                    itemJson.put("commodity_name", commodityName);
                    itemJson.put("image", image);
                    itemJson.put("number", number);
                    itemJson.put("currency", currency);
                    itemJson.put("signal", getSignal(order.country(), currency));
                    itemJson.put("price", price);
                    itemJson.put("sum_price", price * number);
                    itemJson.put("is_gift", isGift);
                    items.add(itemJson);
                });
        return items;
    }

    /**
     * 五个模板比较产品名和套餐名
     * 如果一直，返回产品名，套餐名为""空字符串
     *
     * @param commodityName
     * @param productName
     * @return
     */
    protected String checkCommodityName(String commodityName, String productName) {
        if (commodityName.equals(productName)) {
            commodityName = "";
        }
        return commodityName;
    }

    /**
     * 订单的支付信息
     */
    protected JSONObject getOrderPayment() {
        OrderPayment payment = orderPaymentService.getByOrder(order.getId());
        JSONObject paymentJson = (JSONObject) JSON.toJSON(payment);
        paymentJson.put("signal", getSignal(order.country(), payment.currency()));
        paymentJson.put("origin_price", payment.getAmount());
        paymentJson.put("tax", payment.getTax());
        paymentJson.put("discount", payment.getTotalDiscountFee());
        paymentJson.put("shipping_cost", payment.getShippingCost());
        paymentJson.put("sum", payment.getTotalPayPrice().getAmount());
        return paymentJson;
    }

    /**
     * 获取订单状态多语言文案
     */
    protected String getOrderStateInfo() {
        OrderStateInfo orderStateInfo = orderStateInfoService.getInfo(getLanguage(), order.orderState());
        if (orderStateInfo == null) {
            String message = "订单状态缺少语言文案。状态：" + order.orderState().getNameZh() + "；语言：" + InstaLanguage.parse(order.getArea());
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.PY);
            throw new InstaException(OrderErrorCode.OrderStateNotExistException);
        }
        return orderStateInfo.getName();
    }

    /**
     * 获取sku相关的产品套餐信息
     */
    protected JSONArray getSkuInfo() {
        JSONArray skuInfo = new JSONArray();
        OrderDelivery delivery = orderDeliveryService.getById(order.getId());
        // 兼容管理后台邮件发送
        String needExpressCode = this.expressCode != null ? this.expressCode : delivery.getExpressCode();
        // 根据物流号获取发货信息
        List<OrderDeliveryPartly> orderDeliveryPartlies = orderDeliveryPartlyService.getDeliveryPartly(needExpressCode);
        for (OrderDeliveryPartly orderDeliveryPartly : orderDeliveryPartlies) {
            // 只获取自己订单的商品信息（解决合并发货问题）
            if (!orderDeliveryPartly.getOrderId().equals(order.getId())) {
                continue;
            }
            InstaLanguage language = getLanguage();
            // 拿到对应的的套餐（正常销售状态）
            Commodity commodity = getCommodityBySku(orderDeliveryPartly.getSkuCode());
            CommodityInfo commodityInfo = commodityInfoService.getInfo(commodity.getId(), language);
            ProductInfo productInfo = productInfoService.getInfo(commodity.getProduct(), language);
            CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(commodity.getId());
            OrderItem orderItem = orderItemService.getById(orderDeliveryPartly.getOrderItem());

            String commodityName = commodityInfo.getName().trim();
            String productName = productInfo.getName().trim();
            String image = commodityDisplay.getUrl();
            Integer number = orderItem.getNumber();
            Currency currency = orderItem.currency();
            Float price = orderItem.getPrice();
            Boolean isGift = orderItem.getIsGift();

            JSONObject skuJson = new JSONObject();
            skuJson.put("product_name", productName);
            skuJson.put("commodity_name", commodityName);
            skuJson.put("item_qty", orderDeliveryPartly.getQty());
            skuJson.put("image", image);
            skuJson.put("number", number);
            skuJson.put("currency", currency);
            skuJson.put("signal", getSignal(order.country(), currency));
            skuJson.put("price", price);
            skuJson.put("sum_price", price * number);
            skuJson.put("is_gift", isGift);
            skuInfo.add(skuJson);
        }
        return skuInfo;
    }

    /**
     * 通过sku获取套餐信息
     *
     * @param skuCode
     * @return
     */
    protected Commodity getCommodityBySku(String skuCode) {
        try {
            return commodityService.getCommodityBySkuCode(skuCode);
        } catch (Exception exception) {
            if (exception instanceof InstaException) {
                InstaException instaException = (InstaException) exception;
                String message = instaException.getMessage();
                String msg = "邮件发送异常！ 邮件模板：" + getTemplateName() + ", orderNumber:" + order.getOrderNumber() +
                        "，异常原因：" + message + "，未匹配到正常销售的套餐" + "，sku信息：" + skuCode + "。";
                FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            }
            throw exception;
        }
    }

    /**
     * 获取订单的发货信息
     */
    protected JSONObject getOrderDelivery() {
        OrderDelivery delivery = orderDeliveryService.getById(order.getId());
        String country = delivery.getCountry();
        String province = delivery.getProvince();
        String city = delivery.getCity();
        String location = Arrays.asList(InstaCountry.CN, InstaCountry.HK, InstaCountry.TW, InstaCountry.MO).contains(delivery.country())
                ? String.format("%s，%s, %s", country, province, city)
                : String.format("%s, %s, %s", city, province, country);
        JSONObject deliveryJson = new JSONObject();
        deliveryJson.put("name", ProfileUtil.getFullName(delivery.getFirstName(), delivery.getLastName()));
        deliveryJson.put("address", ProfileUtil.getFullAddress(delivery.getAddress(), delivery.getSubAddress()));
        deliveryJson.put("location", location);
        deliveryJson.put("phone", ProfileUtil.getFullPhone(delivery.getPhoneCode(), delivery.getPhone()));
        deliveryJson.put("postal_code", delivery.getZipCode());
        return deliveryJson;
    }

    /**
     * 获取用户名称
     *
     * @return
     */
    protected String getUserName() {
        OrderDelivery delivery = orderDeliveryService.getById(order.getId());
        return ProfileUtil.getFullName(delivery.getFirstName(), delivery.getLastName());
    }

    /**
     * 获取订单的物流信息
     */
    protected JSONObject getOrderExpressInfo() {
        OrderDelivery delivery = orderDeliveryService.getById(order.getId());
        // 兼容管理后台邮件发送
        String needExpressCode = this.expressCode != null ? this.expressCode : delivery.getExpressCode();
        // 拿到第一个即可
        OrderDeliveryPartly deliveryPartly = orderDeliveryPartlyService.getDeliveryPartly(needExpressCode).get(0);
        MetaExpress expressCompany = metaExpressService.getById(deliveryPartly.getExpressCompany());
        String country = delivery.getCountry();
        String province = delivery.getProvince();
        String city = delivery.getCity();
        String countryCode = delivery.getCountryCode();

        String expressCompanyName = expressCompany == null ? null : expressCompany.getName(getLanguage());
        String expressTrackUrl = expressCompany == null ? null : expressCompany.getTrackUrl();
        expressTrackUrl = expressTrackUrl == null ? null : String.format(expressTrackUrl, deliveryPartly.getExpressCode());
        String location = Arrays.asList(InstaCountry.CN, InstaCountry.HK, InstaCountry.TW, InstaCountry.MO).contains(delivery.country())
                ? String.format("%s，%s, %s", country, province, city)
                : String.format("%s, %s, %s", city, province, country);

        JSONObject expressJson = new JSONObject();
        expressJson.put("name", ProfileUtil.getFullName(delivery.getFirstName(), delivery.getLastName()));
        expressJson.put("express_company", expressCompanyName);
        expressJson.put("express_tracking_url", expressTrackUrl);
        expressJson.put("express_tracking_number", needExpressCode);
        expressJson.put("postal_code", delivery.getZipCode());
        expressJson.put("address", ProfileUtil.getFullAddress(delivery.getAddress(), delivery.getSubAddress()));
        expressJson.put("location", location);
        expressJson.put("phone", ProfileUtil.getFullPhone(delivery.getPhoneCode(), delivery.getPhone()));
        expressJson.put("country_code", Objects.nonNull(countryCode) ? countryCode : StringUtils.EMPTY);
        return expressJson;
    }

    /**
     * 订单详情页地址
     */
    protected String getOrderDetailUrl() {
        return gatewayConfiguration.getStoreUrl() + "/order/detail/" + EncodingUtil.encode(RSAUtil.encryptByPub(order.getOrderNumber()));
    }

    /**
     * 客户名称
     */
    protected String getCustomerName() {
        Order order = getOrder();
        OrderDelivery delivery = orderDeliveryService.getById(order.getId());
        return ProfileUtil.getFullName(delivery.getFirstName(), delivery.getLastName());
    }

    /**
     * 订单发票PDF下载链接
     *
     * @return
     */
    protected String getOrderInvoiceDownloadUrl() {
        Order order = getOrder();
        if (Objects.isNull(order.country()) || InstaCountry.CN.equals(order.country())) {
            return "";
        }
        OrderPrintPdfCreateBO orderPrintPdfCreateBo = orderOverseasInvoiceHelper.createOrderOverseasInvoice(order.getId(), CommonConstant.INVOICE_DOWNLOAD_SOURCE_EMAIL);
        if (Objects.isNull(orderPrintPdfCreateBo) || !orderPrintPdfCreateBo.getResult()) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单号[%s]发货邮件中'发票链接'创建失败...", getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return "";
        }

        return orderPrintPdfCreateBo.getPdfUrl();
    }

    /**
     * 获取FAQ信息
     *
     * @return
     */
    protected JSONArray getFaqItems(String templateName) {
        InstaCountry country = InstaCountry.parse(order.getArea());

        // 区分海外和中国大陆的FAQ
        if (InstaCountry.CN == country) {
            templateName = templateName + FAQ_EMAIL_CN;
        } else {
            templateName = templateName + FAQ_EMAIL_OVERSEAS;
        }

        FaqOtherQuestionBind faqOtherQuestionBind = faqOtherQuestionBindService.getByQuestionBind(FAQ_OTHER_BIND_TYPE, templateName);

        if (Objects.isNull(faqOtherQuestionBind)) {
            FeiShuMessageUtil.storeGeneralMessage("[邮件模板FAQ配置]邮件模板:" + templateName + "没有配置FAQ!!", FeiShuGroupRobot.MainNotice, FeiShuAtUser.PY);
            return new JSONArray();
        } else {
            Integer questionBindId = faqOtherQuestionBind.getId();
            List<Integer> questionIds = faqOtherQuestionQaBindService.listByQuestionBindIdEnableOrderByIndex(questionBindId).stream().map(FaqOtherQuestionQaBind::getQuestionId).collect(Collectors.toList());
            List<FaqCategoryQuestionInfo> faqCategoryQuestionInfos = faqCategoryQuestionInfoService.listByQuestionInsideIds(questionIds, getLanguage(), country);
            List<FaqCategoryQuestionInfo> faqCategoryQuestionInfosFinal = sortFaqCategoryQuestionInfo(faqCategoryQuestionInfos, questionIds);

            JSONArray faqItemsJsonArray = new JSONArray();
            faqCategoryQuestionInfosFinal.forEach(faqInfo -> {
                String question = faqInfo.getQuestion();
                String answer = getAnswer(faqInfo);

                JSONObject faq = new JSONObject();
                faq.put("question", question);
                faq.put("answer", answer);
                faqItemsJsonArray.add(faq);
            });
            return faqItemsJsonArray;
        }
    }

    /**
     * 解决mybatisplus根据id重排序的问题
     *
     * @param faqCategoryQuestionInfos
     * @param questionIds
     * @return
     */
    private List<FaqCategoryQuestionInfo> sortFaqCategoryQuestionInfo(List<FaqCategoryQuestionInfo> faqCategoryQuestionInfos, List<Integer> questionIds) {
        Map<Integer, FaqCategoryQuestionInfo> faqMap = faqCategoryQuestionInfos.stream().collect(Collectors.toMap(FaqCategoryQuestionInfo::getCategoryQuestionInsideId, o -> o));
        return questionIds.stream().map(faqMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 回答的md格式切割
     *
     * @param faqInfo
     * @return
     */
    private String getAnswer(FaqCategoryQuestionInfo faqInfo) {
        String answerText = faqInfo.getAnswerText();
        if (StringUtil.isNotBlank(answerText)) {
            return answerText;
        }
        // todo:发版兼容
        String answer = faqInfo.getAnswer();
        int start = answer.indexOf("<p>");
        int end = answer.indexOf("</p>");
        String substring = answer.substring(start + 3, end);
        String replace = substring.replace("\\n", "<br/>");
        return replace.replace("\\", "");
    }

    /**
     * 获得store推荐配件
     *
     * @return
     */
    protected JSONArray getRecommendationItems() {
        String area = order.getArea();
        InstaCountry country = InstaCountry.parse(area);
        // 通用配件id查询
        List<Integer> generalRecommendationList = commodityRecommendHelper.getGeneralRecommendationList();

        // 单独配合的个性化配件id
        List<Integer> recommendationCommodityIdAll = getRecommendationCommodityIdAll(country);

        // 获得最终的推荐配件结果id list
        List<Integer> recommendationIds = commodityRecommendHelper.getRecommendationIds(generalRecommendationList, recommendationCommodityIdAll, getOrderItemCommodityIds());

        // 获取最终的推荐配件
        List<SubscribeEmailRecommendationBO> recommendationItems = commodityRecommendHelper.getRecommendationItems(this.getLanguage(), recommendationIds, country);
        recommendationItems.forEach(subscribeEmailRecommendationBO -> {
            CommodityPrice price = subscribeEmailRecommendationBO.getCommodityPrice();
            subscribeEmailRecommendationBO.setSignal(getSignal(country, Currency.parse(price.getCurrency())));
        });

        return JSONArray.parseArray(JSON.toJSONString(recommendationItems));
    }

    /**
     * 获得订单子项套餐id
     *
     * @return
     */
    private List<Integer> getOrderItemCommodityIds() {
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        return orderItems.stream().map(OrderItem::getCommodity).collect(Collectors.toList());
    }

    /**
     * 获得套餐设置的推荐配件id
     *
     * @return
     */
    private List<Integer> getRecommendationCommodityIdAll(InstaCountry country) {
        List<Integer> orderItemCommodityIds = getOrderItemCommodityIds();
        List<Commodity> commodities = userCartService.listByCommodities(orderItemCommodityIds, country);
        if (CollectionUtils.isNotEmpty(commodities)) {
            return commodities.stream().map(Commodity::getId).collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    /**
     * 是否实付款零元订单
     *
     * @return
     */
    protected Boolean getInstaOrder() {
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        Price totalPayPrice = orderPayment.getTotalPayPrice();
        Float actualPayAmount = totalPayPrice.getAmount();
        Float noPay = new Float("0.00");
        return noPay.equals(actualPayAmount);
    }

    /**
     * 跳转care+的产品链接
     *
     * @return
     */
    protected String getInsuranceUrl() {
        return gatewayConfiguration.getStoreUrl() + "/service?category=77";
    }
}