package com.insta360.store.business.cloud.service.impl;

import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitChange;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitChangeService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitService;
import com.insta360.store.business.cloud.service.StoreBenefitBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/15
 */
@Service
public class StoreBenefitBusinessServiceImpl implements StoreBenefitBusinessService {

    @Autowired
    CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    CloudStorageStoreBenefitChangeService cloudStorageStoreBenefitChangeService;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void insertStoreBenefit(CloudStorageStoreBenefit storeBenefit, CloudStorageStoreBenefitChange storeBenefitChangeRecord) {
        if(Objects.nonNull(storeBenefit)) {
            cloudStorageStoreBenefitService.save(storeBenefit);
        }
        if(Objects.nonNull(storeBenefitChangeRecord)) {
            cloudStorageStoreBenefitChangeService.save(storeBenefitChangeRecord);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void updateStoreBenefit(CloudStorageStoreBenefit storeBenefit, CloudStorageStoreBenefitChange storeBenefitChangeRecord) {
        if(Objects.nonNull(storeBenefit)) {
            cloudStorageStoreBenefitService.updateById(storeBenefit);
        }
        if(Objects.nonNull(storeBenefitChangeRecord)) {
            cloudStorageStoreBenefitChangeService.save(storeBenefitChangeRecord);
        }
    }
}
