package com.insta360.store.business.integration.mabang.enums;

/**
 * @Author: wbt
 * @Date: 2020/07/23
 * @Description:
 */
public enum MbOrderState {

    // 1.待处理 2.配货中 3.已发货 4.已完成 5.已作废 6.所有未发货 默认配货中订单 7.所有非未发货,默认配货中订单

    pending("待处理", 1),

    prepared("配货中", 2),

    on_delivery("已发货", 3),

    success("已完成", 4),

    closed("已作废", 5),

    no_delivery_all("所有未发货", 6),

    // 按其他状态处理(已发货，已完成，已作废)
    other("其他", 7);

    private String name;

    private Integer code;

    MbOrderState(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }

    public static MbOrderState param(Integer code) {
        for (MbOrderState orderStatus : values()) {
            if (orderStatus.code.equals(code)) {
                return orderStatus;
            }
        }
        return prepared;
    }
}
