package com.insta360.store.business.trade.enums;

import com.insta360.store.business.trade.email.*;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/18
 */
public enum UserCartEmailEnum {

    /**
     * 第一封邮件，延迟时间为1小时
     */
    FIRST_EMAIL(1, Arrays.asList(1, 11, 21), "", UserCartEmailTypeEnum.normal.name(), "store_cart_abandon_email_first", 1L, TimeUnit.HOURS, "第一封邮件，延迟时间为1小时", TradeUserCartAbandonEmail.class),

    /**
     * 第二封邮件，延迟时间为24小时
     */
    SECOND_EMAIL(2, Arrays.asList(2, 12, 22), "", UserCartEmailTypeEnum.normal.name(), "store_cart_abandon_email_second", 1L, TimeUnit.DAYS, "次日X点", TradeUserCartAbandonEmail.class),

    /**
     * 第三封邮件，延迟时间为120小时
     */
    THIRD_EMAIL(3, Arrays.asList(3, 13, 23), "", UserCartEmailTypeEnum.normal.name(), "store_cart_abandon_email_third", 4L, TimeUnit.DAYS, "4日后X点", TradeUserCartAbandonEmail.class),

    // ======================================================== A3 ======================================================== //

    /**
     * 第一封邮件，延迟时间为1小时
     */
    A3_FIRST_EMAIL(11, Arrays.asList(1, 11, 21), "cart_email_A3first", UserCartEmailTypeEnum.a3.name(), "store_cart_abandon_email_A3first", 1L, TimeUnit.HOURS, "第一封邮件，延迟时间为1小时", TradeUserCartAbandonA3Email.class),

    /**
     * 第二封邮件，延迟时间为24小时
     */
    A3_SECOND_EMAIL(12, Arrays.asList(2, 12, 22), "cart_email_A3second", UserCartEmailTypeEnum.a3.name(), "store_cart_abandon_email_A3second", 1L, TimeUnit.DAYS, "次日X点", TradeUserCartAbandonA3TWOEmail.class),

    /**
     * 第三封邮件，延迟时间为120小时
     */
    A3_THIRD_EMAIL(13, Arrays.asList(3, 13, 23), "cart_email_A3third", UserCartEmailTypeEnum.a3.name(), "store_cart_abandon_email_A3third", 4L, TimeUnit.DAYS, "4日后X点", TradeUserCartAbandonA3ThreeEmail.class),

    // ======================================================== FLOW2PRO ======================================================== //

    /**
     * 第一封邮件，延迟时间为1小时
     */
    FLOW2PRO_FIRST_EMAIL(21, Arrays.asList(1, 11, 21), "cart_email_Flowfirst", UserCartEmailTypeEnum.flow2pro.name(), "store_cart_abandon_email_Flowfirst", 1L, TimeUnit.HOURS, "第一封邮件，延迟时间为1小时", TradeUserCartAbandonFlow2ProEmail.class),

    /**
     * 第二封邮件，延迟时间为24小时
     */
    FLOW2PRO_SECOND_EMAIL(22, Arrays.asList(2, 12, 22), "cart_email_Flowsecond", UserCartEmailTypeEnum.flow2pro.name(), "store_cart_abandon_email_Flowsecond", 1L, TimeUnit.DAYS, "次日X点", CartAbandonFlow2ProTwoEmail.class),

    /**
     * 第三封邮件，延迟时间为120小时
     */
    FLOW2PRO_THIRD_EMAIL(23, Arrays.asList(3, 13, 23), "cart_email_Flowthird", UserCartEmailTypeEnum.flow2pro.name(), "store_cart_abandon_email_Flowthird", 4L, TimeUnit.DAYS, "4日后X点", CartAbandonFlow2ProThreeEmail.class),

    ;

    /**
     * 邮件类型
     */
    private final Integer code;

    /**
     * 互斥code
     */
    private final List<Integer> exclusiveCodes;

    /**
     * 链接名称
     */
    private final String linkName;

    /**
     * 邮件类型
     */
    private final String type;

    /**
     * 模板键
     */
    private final String templateKey;

    /**
     * 延迟时间值
     */
    private final Long delayTime;

    /**
     * 延迟时间单位
     */
    private final TimeUnit timeUnit;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 模版
     */
    private final Class<? extends BaseTradeEmail> template;

    UserCartEmailEnum(Integer code, List<Integer> exclusiveCodes, String linkName, String type, String templateKey, Long delayTime, TimeUnit timeUnit, String desc, Class<? extends BaseTradeEmail> template) {
        this.code = code;
        this.exclusiveCodes = exclusiveCodes;
        this.linkName = linkName;
        this.type = type;
        this.templateKey = templateKey;
        this.delayTime = delayTime;
        this.timeUnit = timeUnit;
        this.desc = desc;
        this.template = template;
    }

    /**
     * 根据邮件类型获取邮件类型枚举
     *
     * @param code 邮件类型
     * @return 邮件类型枚举
     */
    public static UserCartEmailEnum matchCode(Integer code) {
        for (UserCartEmailEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取邮件类型
     *
     * @return 邮件类型
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取模板键
     *
     * @return 模板键
     */
    public String getTemplateKey() {
        return templateKey;
    }

    /**
     * 获取延迟时间（以时间单位表示）
     *
     * @return 延迟时间
     */
    public Long getDelayTime() {
        return delayTime;
    }

    /**
     * 获取延迟时间单位
     *
     * @return 延迟时间单位
     */
    public TimeUnit getTimeUnit() {
        return timeUnit;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 获取延迟时间（秒）
     *
     * @return 延迟时间（秒）
     */
    public Long getDelayTimeInMillis() {
        return timeUnit.toMillis(delayTime);
    }

    /**
     * 获取下一封邮件
     *
     * @return
     */
    public UserCartEmailEnum getNextEmail() {
        Integer nextCode = this.getCode() + 1;
        return matchCode(nextCode);
    }

    /**
     * 邮件发送的优先级
     *
     * @return
     */
    public static List<UserCartEmailEnum> sortByPriority() {
        return Arrays.asList(UserCartEmailEnum.A3_FIRST_EMAIL,
                UserCartEmailEnum.FLOW2PRO_FIRST_EMAIL,
                UserCartEmailEnum.FIRST_EMAIL);
    }

    public Class<? extends BaseTradeEmail> getTemplate() {
        return template;
    }

    public String getType() {
        return type;
    }

    public List<Integer> getExclusiveCodes() {
        return exclusiveCodes;
    }

    public String getLinkName() {
        return linkName;
    }
}