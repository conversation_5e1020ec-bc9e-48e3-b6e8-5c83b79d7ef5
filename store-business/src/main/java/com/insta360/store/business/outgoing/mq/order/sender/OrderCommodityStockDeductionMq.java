package com.insta360.store.business.outgoing.mq.order.sender;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpSender;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageSenderType;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.compass.libs.rocketmq.tcp.producer.RocketTcpMessageSender;
import com.insta360.store.business.configuration.utils.MqUtils;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/17 下午6:04
 */
@Component
public class OrderCommodityStockDeductionMq {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCommodityStockDeductionMq.class);

    @MessageTcpSender(messageChannel = MessageTcpChannelEnum.store_commodity_stock_deduction_notify, messageType = MessageSenderType.normal)
    RocketTcpMessageSender rocketTcpMessageSender;

    /**
     * 库存扣减队列
     *
     * @param order
     */
    public void sendCommodityStockDeductionMessage(Order order) {
        if (order == null) {
            return;
        }

        LOGGER.info("send commodity stock deduction message. pending... order_number:{}", order.getOrderNumber());

        OrderMessageDTO orderMessage = new OrderMessageDTO();
        orderMessage.setOrder(order);
        String messageId = rocketTcpMessageSender.sendMessage(JSONObject.toJSONString(orderMessage));
        MqUtils.isBlankMessageIdHandle(messageId, this, orderMessage);
        LOGGER.info("send commodity stock deduction message. success... order_number:{} messageId:{}", order.getOrderNumber(), messageId);
    }
}
