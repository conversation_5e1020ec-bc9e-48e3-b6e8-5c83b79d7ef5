package com.insta360.store.business.work.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.work.model.WorkOrderDiscountRule;

import java.util.List;

public interface WorkOrderDiscountRuleService extends BaseService<WorkOrderDiscountRule> {

    /**
     * 根据优惠折扣组合ID查询对应地区优惠规则
     *
     * @param discountCombinationId
     * @return
     */
    List<WorkOrderDiscountRule> listWorkOrderDiscountRule(Long discountCombinationId);

    /**
     * 根据优惠组合ID、国家代码查询出对应优惠规则
     * @param discountCombinationId
     * @param country
     * @return
     */
    WorkOrderDiscountRule getAreaDiscountRule(Long discountCombinationId, InstaCountry country);

    /**
     * 根据优惠组合ID集合、国家代码查询出对应优惠规则
     * @param discountCombinationIds
     * @param country
     * @return
     */
    WorkOrderDiscountRule getAreaDiscountRules(List<Long> discountCombinationIds, InstaCountry country);

    void remove(Long discountCombinationId);
}
