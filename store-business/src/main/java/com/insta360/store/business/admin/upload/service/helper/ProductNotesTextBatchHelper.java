package com.insta360.store.business.admin.upload.service.helper;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.admin.upload.bo.UploadExcelDataBO;
import com.insta360.store.business.admin.upload.common.UploadConstant;
import com.insta360.store.business.admin.upload.service.data.ProductPackageExcelData;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.product.dto.ProductPrecautionsTextCreateDTO;
import com.insta360.store.business.product.enums.ProductPackingListType;
import com.insta360.store.business.product.service.impl.helper.ProductPrecautionsTextHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/12/27
 */
@Component
public class ProductNotesTextBatchHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductNotesTextBatchHelper.class);

    @Autowired
    ProductPrecautionsTextHelper productPrecautionsTextHelper;

    /**
     * 注意事项数据封装
     *
     * @param uploadExcelDataMap
     * @return
     */
    public void productPrecautionsTextEncapsulation(Map<Integer, List<UploadExcelDataBO<ProductPackageExcelData>>> uploadExcelDataMap) {
        List<ProductPrecautionsTextCreateDTO> productPrecautionsTextCreateDtoList = uploadExcelDataMap
                .entrySet()
                .stream()
                .map(this::buildProductPrecautionsTextCreateDTO)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(productPrecautionsTextCreateDtoList)) {
            return;
        }

        List<Integer> productIds = productPrecautionsTextCreateDtoList.stream()
                .map(ProductPrecautionsTextCreateDTO::getProductId)
                .distinct()
                .collect(Collectors.toList());

        // 缓存更新参数
        CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
        cachePutKeyParameter.setProductIds(productIds);

        productPrecautionsTextHelper.batchSavePrecautionsText(cachePutKeyParameter, productPrecautionsTextCreateDtoList);
    }

    /**
     * 注意事项数据封装
     *
     * @param configNameMap
     * @return
     */
    private ProductPrecautionsTextCreateDTO buildProductPrecautionsTextCreateDTO(Map.Entry<Integer, List<UploadExcelDataBO<ProductPackageExcelData>>> configNameMap) {
        // 产品ID
        Integer productId = configNameMap.getKey();
        // 产品ID对应的注意事项文案
        List<UploadExcelDataBO<ProductPackageExcelData>> excelDataList = configNameMap.getValue();
        // 根据序号重排序
        excelDataList.sort(Comparator.comparing(uploadExcelData -> uploadExcelData.getRowNumber()));
        // 模版类型转换
        ProductPackingListType productPackingListType = ProductPackingListType.matchDesc(excelDataList.get(0).getData().getType());

        ProductPrecautionsTextCreateDTO productPrecautionsTextCreateDto = new ProductPrecautionsTextCreateDTO();
        productPrecautionsTextCreateDto.setProductId(productId);
        productPrecautionsTextCreateDto.setStyleType(productPackingListType.getType());
        productPrecautionsTextCreateDto.setLanguageTextList(this.buildLanguageText(excelDataList));
        return productPrecautionsTextCreateDto;
    }

    /**
     * 构建多语言配置
     *
     * @param excelDataList
     * @return
     */
    private List<ProductPrecautionsTextCreateDTO.LanguageTextBean> buildLanguageText(List<UploadExcelDataBO<ProductPackageExcelData>> excelDataList) {
        List<ProductPrecautionsTextCreateDTO.LanguageTextBean> languageTextBeans = Lists.newArrayList();
        for (InstaLanguage language : UploadConstant.languageList) {
            StringBuffer sb = new StringBuffer();
            for (UploadExcelDataBO<ProductPackageExcelData> bo : excelDataList) {
                try {
                    ProductPackageExcelData data = bo.getData();
                    Class<? extends ProductPackageExcelData> aClass = data.getClass();
                    Field field = aClass.getDeclaredField(language.name());
                    field.setAccessible(true);
                    String obj = (String) field.get(data);
                    if (StringUtils.isNotBlank(obj)) {
                        sb.append("<p>")
                                .append(obj)
                                .append("</p>");
                    }
                } catch (Exception e) {
                    LOGGER.error("注意事项批量导入，多语言文案反射失败",e);
                }
            }
            if (Objects.nonNull(sb) && sb.length() > 0) {
                languageTextBeans.add(new ProductPrecautionsTextCreateDTO.LanguageTextBean(sb.toString(), language.name()));
            }
        }

        return languageTextBeans;
    }
}
