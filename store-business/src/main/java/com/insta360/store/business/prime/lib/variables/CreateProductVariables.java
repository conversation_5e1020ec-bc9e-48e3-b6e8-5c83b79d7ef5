package com.insta360.store.business.prime.lib.variables;

import com.insta360.store.business.prime.constants.PrimeConstants;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4
 * @description 创建产品的GraphQL变量
 * 本类用于封装向Amazon Prime服务发送创建产品请求时所需的所有参数
 * 遵循GraphQL变量格式，作为请求参数传递给Prime API
 */
public class CreateProductVariables implements PrimeVariables {

    /**
     * 创建产品的输入参数
     */
    private CreateProductInput input;

    /**
     * 默认构造函数
     */
    public CreateProductVariables() {
    }

    /**
     * 通过产品输入参数创建GraphQL变量对象
     *
     * @param input 创建产品的输入参数
     */
    public CreateProductVariables(CreateProductInput input) {
        this.input = input;
    }

    /**
     * 获取创建产品的输入参数
     *
     * @return 创建产品的输入参数对象
     */
    public CreateProductInput getInput() {
        return input;
    }

    /**
     * 设置创建产品的输入参数
     *
     * @param input 创建产品的输入参数对象
     */
    public void setInput(CreateProductInput input) {
        this.input = input;
    }

    /**
     * 亚马逊SKU
     * 用于在Amazon系统中唯一标识产品
     * 作为产品在Amazon系统中的识别码
     */
    public static class AmazonSku {

        private String value;

        /**
         * 默认构造函数
         */
        public AmazonSku() {
        }

        /**
         * 通过SKU值创建AmazonSku对象
         *
         * @param value SKU值
         */
        public AmazonSku(String value) {
            this.value = value;
        }

        /**
         * 获取SKU值
         *
         * @return SKU字符串值
         */
        public String getValue() {
            return value;
        }

        /**
         * 设置SKU值
         *
         * @param value SKU字符串值
         */
        public void setValue(String value) {
            this.value = value;
        }
    }

    /**
     * 产品标题信息
     */
    public static class Title {

        private String defaultLocale = PrimeConstants.Commodity.DEFAULT_LANGUAGE;

        private Values values;

        public Title() {
        }

        public Title(Values values) {
            this.values = values;
        }

        /**
         * 创建标题对象
         * 
         * @param locale 语言区域
         * @param value 标题值
         * @return 标题对象
         */
        public static Title create(String locale, String value) {
            Values values = new Values(locale, value);
            Title title = new Title(values);
            title.defaultLocale = locale;
            return title;
        }

        public Values getValues() {
            return values;
        }

        public void setValues(Values values) {
            this.values = values;
        }

        public String getDefaultLocale() {
            return defaultLocale;
        }

        public void setDefaultLocale(String defaultLocale) {
            this.defaultLocale = defaultLocale;
        }

        /**
         * 标题值对象
         */
        public static class Values {

            private String locale;

            private String value;

            public Values() {
            }

            public Values(String locale, String value) {
                this.locale = locale;
                this.value = value;
            }

            public String getLocale() {
                return locale;
            }

            public void setLocale(String locale) {
                this.locale = locale;
            }

            public String getValue() {
                return value;
            }

            public void setValue(String value) {
                this.value = value;
            }
        }
    }

    /**
     * 产品图片信息
     * 包含产品展示图片的URL信息
     * 用于在Amazon Prime页面上显示产品图片
     */
    public static class ProductImage {

        private String sourceUrl;

        /**
         * 默认构造函数
         */
        public ProductImage() {
        }

        /**
         * 通过图片URL创建产品图片对象
         *
         * @param sourceUrl 图片源URL地址
         */
        public ProductImage(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }

        /**
         * 获取图片源URL
         *
         * @return 图片源URL字符串
         */
        public String getSourceUrl() {
            return sourceUrl;
        }

        /**
         * 设置图片源URL
         *
         * @param sourceUrl 图片源URL字符串
         */
        public void setSourceUrl(String sourceUrl) {
            this.sourceUrl = sourceUrl;
        }
    }

    /**
     * 创建产品的输入参数
     * 包含创建Amazon Prime产品时所需的所有必要信息
     * 包括产品标识、SKU、标题、图片和详情页URL等
     */
    public static class CreateProductInput {

        /**
         * 外部ID，用于在外部系统中标识产品
         */
        private String externalId;

        /**
         * 亚马逊SKU对象
         */
        private AmazonSku amazonSku;

        /**
         * 产品SKU
         */
        private String sku;

        /**
         * 是否提供Prime服务
         */
        private Boolean offerPrime;

        /**
         * 产品详情页URL
         */
        private String productDetailPageUrl;

        /**
         * 产品标题对象
         */
        private Title title;

        /**
         * 产品图片对象
         */
        private ProductImage image;

        public CreateProductInput() {
        }

        /**
         * 创建构建器实例
         *
         * @return 构建器对象
         */
        public static Builder builder() {
            return new Builder();
        }

        /**
         * 获取外部ID
         *
         * @return 外部ID字符串
         */
        public String getExternalId() {
            return externalId;
        }

        /**
         * 设置外部ID
         *
         * @param externalId 外部ID字符串
         */
        public void setExternalId(String externalId) {
            this.externalId = externalId;
        }

        /**
         * 获取亚马逊SKU对象
         *
         * @return 亚马逊SKU对象
         */
        public AmazonSku getAmazonSku() {
            return amazonSku;
        }

        /**
         * 设置亚马逊SKU对象
         *
         * @param amazonSku 亚马逊SKU对象
         */
        public void setAmazonSku(AmazonSku amazonSku) {
            this.amazonSku = amazonSku;
        }

        /**
         * 获取产品SKU
         *
         * @return 产品SKU字符串
         */
        public String getSku() {
            return sku;
        }

        /**
         * 设置产品SKU
         *
         * @param sku 产品SKU字符串
         */
        public void setSku(String sku) {
            this.sku = sku;
        }

        /**
         * 获取是否提供Prime服务
         *
         * @return 是否提供Prime服务的布尔值
         */
        public Boolean getOfferPrime() {
            return offerPrime;
        }

        /**
         * 设置是否提供Prime服务
         *
         * @param offerPrime 是否提供Prime服务的布尔值
         */
        public void setOfferPrime(Boolean offerPrime) {
            this.offerPrime = offerPrime;
        }

        /**
         * 获取产品详情页URL
         *
         * @return 产品详情页URL字符串
         */
        public String getProductDetailPageUrl() {
            return productDetailPageUrl;
        }

        /**
         * 设置产品详情页URL
         *
         * @param productDetailPageUrl 产品详情页URL字符串
         */
        public void setProductDetailPageUrl(String productDetailPageUrl) {
            this.productDetailPageUrl = productDetailPageUrl;
        }

        /**
         * 获取产品图片对象
         *
         * @return 产品图片对象
         */
        public ProductImage getImage() {
            return image;
        }

        /**
         * 设置产品图片对象
         *
         * @param image 产品图片对象
         */
        public void setImage(ProductImage image) {
            this.image = image;
        }

        /**
         * 获取产品标题对象
         *
         * @return 产品标题对象
         */
        public Title getTitle() {
            return title;
        }

        /**
         * 设置产品标题对象
         *
         * @param title 产品标题对象
         */
        public void setTitle(Title title) {
            this.title = title;
        }

        /**
         * 构建器类，用于链式构建CreateProductInput对象
         * 提供流式API，简化产品创建参数的设置过程
         * 支持所有必要参数的设置，并提供便捷方法处理复杂对象
         */
        public static class Builder {

            /**
             * 构建器中的产品输入参数实例
             * 在构建过程中逐步设置各个属性
             */
            private CreateProductInput input = new CreateProductInput();

            /**
             * 设置外部ID
             *
             * @param externalId 外部系统中的产品标识符
             * @return 构建器实例，支持链式调用
             */
            public Builder externalId(String externalId) {
                input.setExternalId(externalId);
                return this;
            }

            /**
             * 设置亚马逊SKU对象
             *
             * @param amazonSku 亚马逊SKU对象
             * @return 构建器实例，支持链式调用
             */
            public Builder amazonSku(AmazonSku amazonSku) {
                input.setAmazonSku(amazonSku);
                return this;
            }

            /**
             * 设置亚马逊SKU值（便捷方法）
             * 自动创建AmazonSku对象并设置值
             *
             * @param value 亚马逊SKU字符串值
             * @return 构建器实例，支持链式调用
             */
            public Builder amazonSkuValue(String value) {
                input.setAmazonSku(new AmazonSku(value));
                return this;
            }

            /**
             * 设置产品SKU
             *
             * @param sku 产品SKU字符串
             * @return 构建器实例，支持链式调用
             */
            public Builder sku(String sku) {
                input.setSku(sku);
                return this;
            }

            /**
             * 设置是否提供Prime服务
             *
             * @param offerPrime 是否提供Prime服务的布尔值
             * @return 构建器实例，支持链式调用
             */
            public Builder offerPrime(Boolean offerPrime) {
                input.setOfferPrime(offerPrime);
                return this;
            }

            /**
             * 设置产品详情页URL
             *
             * @param url 产品详情页的完整URL
             * @return 构建器实例，支持链式调用
             */
            public Builder productDetailPageUrl(String url) {
                input.setProductDetailPageUrl(url);
                return this;
            }

            /**
             * 设置产品图片对象
             *
             * @param image 产品图片对象
             * @return 构建器实例，支持链式调用
             */
            public Builder image(ProductImage image) {
                input.setImage(image);
                return this;
            }

            /**
             * 设置产品图片URL（便捷方法）
             * 自动创建ProductImage对象并设置源URL
             *
             * @param sourceUrl 产品图片的源URL
             * @return 构建器实例，支持链式调用
             */
            public Builder imageSourceUrl(String sourceUrl) {
                input.setImage(new ProductImage(sourceUrl));
                return this;
            }

            /**
             * 设置标题对象
             *
             * @param title 标题对象
             * @return 构建器
             */
            public Builder title(Title title) {
                input.setTitle(title);
                return this;
            }

            /**
             * 设置标题（快捷方法）
             *
             * @param locale 语言区域
             * @param value 标题内容
             * @return 构建器
             */
            public Builder titleValue(String locale, String value) {
                input.setTitle(Title.create(locale, value));
                return this;
            }

            /**
             * 构建并返回完整的CreateProductInput对象
             * 在设置完所有必要参数后调用此方法获取最终的输入对象
             *
             * @return 构建好的CreateProductInput对象
             */
            public CreateProductInput build() {
                return input;
            }
        }
    }

}
