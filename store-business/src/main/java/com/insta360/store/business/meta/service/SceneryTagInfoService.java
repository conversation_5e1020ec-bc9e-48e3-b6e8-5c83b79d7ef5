package com.insta360.store.business.meta.service;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.model.SceneryTagInfo;
import com.insta360.compass.core.common.BaseService;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-12-18
 * @Description:
 */
public interface SceneryTagInfoService extends BaseService<SceneryTagInfo> {

    /**
     * 通过id查询场景标签多语言信息
     *
     * @param sceneryTagId
     * @return
     */
    List<SceneryTagInfo> listByTagId(Integer sceneryTagId);

    /**
     * 通过tag ID删除场景标签
     *
     * @param sceneryTagId
     */
    void deleteByTagId(Integer sceneryTagId);

    /**
     * 批量保存场景标签多语言信息
     *
     * @param sceneryTagInfos
     */
    void saveSceneryTagInfoBatch(List<SceneryTagInfo> sceneryTagInfos);

    /**
     * 批量查询指定语言文案
     *
     * @param textFilterIds
     * @param language
     * @return
     */
    List<SceneryTagInfo> listByTagIds(List<Integer> textFilterIds, InstaLanguage language);
}
