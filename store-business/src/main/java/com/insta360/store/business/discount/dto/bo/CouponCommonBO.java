package com.insta360.store.business.discount.dto.bo;

import com.insta360.store.business.discount.dto.ao.coupon.CreateCouponCommonAO;
import com.insta360.store.business.discount.model.Coupon;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 优惠券、优惠券模版主体BO
 * @Date 2022/4/11
 */
public class CouponCommonBO extends DiscountBaseBO{

    /**
     * 优惠券ID or 优惠券模版ID
     */
    private Integer couponCommonId;

    /**
     * 优惠券code or 优惠券模版code
     */
    private String couponCommonCode;

    /**
     * 剩余可以次数
     */
    private Integer remainCount;

    /**
     * 最大可使用次数
     */
    private Integer totalCount;

    /**
     * 优惠券门槛
     */
    private CouponThresholdCommonBO couponThresholdCommonBo;

    /**
     * 优惠券优惠政策
     */
    private List<CouponPolicyCommonBO> couponPolicyCommonBoList;

    /**
     * AO转换BO
     * @param createCouponCommonAo
     * @return
     */
    public static CouponCommonBO build(CreateCouponCommonAO createCouponCommonAo) {
        CouponCommonBO couponCommonBo = new CouponCommonBO();
        BeanUtils.copyProperties(createCouponCommonAo,couponCommonBo);
        couponCommonBo.setTotalCount(createCouponCommonAo.getCouponCount());
        couponCommonBo.setRemainCount(createCouponCommonAo.getCouponCount());
        CouponThresholdCommonBO couponThresholdBo = CouponThresholdCommonBO.buildCouponThresholdData(createCouponCommonAo.getCouponThresholdAo(), createCouponCommonAo.getCouponCommonCode());
        List<CouponPolicyCommonBO> couponPolicyCommonBoList = CouponPolicyCommonBO.batchBuildCouponPolicyCommonBo(createCouponCommonAo.getCouponPolicyAoList(), createCouponCommonAo.getCouponCommonCode());
        couponCommonBo.setCouponThresholdCommonBo(couponThresholdBo);
        couponCommonBo.setCouponPolicyCommonBoList(couponPolicyCommonBoList);
        return couponCommonBo;
    }

    /**
     *
     * @return
     */
    public Coupon convertCreate(){
        return Optional.ofNullable(this).map(bo -> {
            Coupon coupon = new Coupon();
            BeanUtils.copyProperties(bo,coupon);
            coupon.setCouponCode(bo.getCouponCommonCode());
            return coupon;
        }).orElse(null);
    }

    /**
     * BO转DB实体
     * @param coupon
     * @return
     */
    public Coupon convertUpdate(Coupon coupon){
        return Optional.ofNullable(this).map(bo -> {
            if(Objects.nonNull(bo.getEffectTime())) {
                coupon.setEffectTime(bo.getEffectTime());
            }
            if(Objects.nonNull(bo.getInvalidTime())) {
                coupon.setInvalidTime(bo.getInvalidTime());
            }
            coupon.setRemark(bo.getRemark());
            couponResidueCountaClculate(coupon);
            return coupon;
        }).orElse(null);
    }

    /**
     * 获取优惠券剩余可用次数
     * 1、是否存在剩余次数，若不存在，则是对以往没有限制次数的优惠券进行次数限制，如存在则 最新剩余次数 = 剩余的次数 + 追加的次数（追加的次数 = 新的总次数 - 旧的总次数）
     * @param coupon
     * @return
     */
    private void couponResidueCountaClculate(Coupon coupon) {
        Integer newCouponCount = this.totalCount;
        Integer remainCount = coupon.getRemainCount();
        Integer oldCouponCount = coupon.getTotalCount();
        if(Objects.isNull(newCouponCount)) {
            coupon.setTotalCount(null);
            coupon.setRemainCount(null);
            return;
        }

        coupon.setTotalCount(newCouponCount);
        coupon.setRemainCount(newCouponCount);
        if(Objects.nonNull(remainCount) && Objects.nonNull(oldCouponCount)) {
            int couponCount = newCouponCount == null ? 0 : newCouponCount;
            // 最新剩余次数 = 剩余的次数 + 追加的次数（追加的次数 = 新的总次数 - 旧的总次数）
            int newLastCount = remainCount + (couponCount - oldCouponCount);
            coupon.setRemainCount(newLastCount);
        }
    }


    public Integer getCouponCommonId() {
        return couponCommonId;
    }

    public void setCouponCommonId(Integer couponCommonId) {
        this.couponCommonId = couponCommonId;
    }

    public String getCouponCommonCode() {
        return couponCommonCode;
    }

    public void setCouponCommonCode(String couponCommonCode) {
        this.couponCommonCode = couponCommonCode;
    }

    public Integer getRemainCount() {
        return remainCount;
    }

    public void setRemainCount(Integer remainCount) {
        this.remainCount = remainCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public CouponThresholdCommonBO getCouponThresholdCommonBo() {
        return couponThresholdCommonBo;
    }

    public void setCouponThresholdCommonBo(CouponThresholdCommonBO couponThresholdCommonBo) {
        this.couponThresholdCommonBo = couponThresholdCommonBo;
    }

    public List<CouponPolicyCommonBO> getCouponPolicyCommonBoList() {
        return couponPolicyCommonBoList;
    }

    public void setCouponPolicyCommonBoList(List<CouponPolicyCommonBO> couponPolicyCommonBoList) {
        this.couponPolicyCommonBoList = couponPolicyCommonBoList;
    }
}
