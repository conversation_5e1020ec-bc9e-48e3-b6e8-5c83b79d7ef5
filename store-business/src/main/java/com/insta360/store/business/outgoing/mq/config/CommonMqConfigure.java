package com.insta360.store.business.outgoing.mq.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description MQ公用动态配置类
 * @Date 2023/2/14
 */
@RefreshScope
@Configuration
public class CommonMqConfigure {

    /**
     * 售后挽留邮件发送延时等级
     */
    @Value("${app.outgoing.mq.rmaRecoverDelay}")
    private long rmaRecoverDelay;

    public long getRmaRecoverDelay() {
        return rmaRecoverDelay;
    }
}
