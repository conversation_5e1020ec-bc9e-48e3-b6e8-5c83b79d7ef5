package com.insta360.store.business.integration.weapp.service;

import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.integration.weapp.lib.request.WeappOrderConfirmDeliveryRequest;
import com.insta360.store.business.integration.weapp.lib.request.WeappOrderPayRequest;
import com.insta360.store.business.integration.weapp.lib.request.WeappOrderShippedRequest;
import com.insta360.store.business.integration.weapp.lib.response.BaseWeappResponse;
import com.insta360.store.business.integration.weapp.lib.response.WeappOrderConfirmDeliveryResponse;
import com.insta360.store.business.integration.weapp.lib.response.WeappOrderPayResponse;
import com.insta360.store.business.integration.weapp.lib.response.WeappOrderShippedResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description 微信小程序接口调用服务类
 * @Date 2022/3/24
 */
@Service
public class WeAppOrderService {

    public static final Logger LOGGER = LoggerFactory.getLogger(WeAppOrderService.class);

    /**
     * 订单确认收获数据同步
     *
     * @param request
     * @return
     */
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER,GrafanaStatisticsType.COUNTER,GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_WEAPP
    )
    public BaseWeappResponse doConfirmDeliveryOrderSync(WeappOrderConfirmDeliveryRequest request) {
        // 请求
        String result = request.executePost(request.removeOtherParam());
        LOGGER.info("weapp_order_confirm_delivery_request_result:" + result);
        BaseWeappResponse weappResponse = WeappOrderConfirmDeliveryResponse.parse(result);
        return weappResponse;
    }


    /**
     * 订单支付信息同步微信小程序
     *
     * @param request
     * @return
     */
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER,GrafanaStatisticsType.COUNTER,GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_WEAPP
    )
    public BaseWeappResponse doOrderPaymentInfoSync(WeappOrderPayRequest request) {
        // 请求
        String result = request.executePost(request.removeOtherParam());
        LOGGER.info("weapp_order_pay_request_result:" + result);
        BaseWeappResponse weappResponse = WeappOrderPayResponse.parse(result);
        return weappResponse;
    }


    /**
     * 订单物流信息同步微信小程序
     *
     *
     * @param request
     * @return
     */
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER,GrafanaStatisticsType.COUNTER,GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_WEAPP
    )
    public BaseWeappResponse doOrderDeliverySync(WeappOrderShippedRequest request) {
        // 请求
        String result = request.executePost(request.removeOtherParam());
        LOGGER.info("weapp_order_shipped_request_result:" + result);
        BaseWeappResponse weappResponse = WeappOrderShippedResponse.parse(result);
        return weappResponse;
    }
}
