package com.insta360.store.business.rma.dto.condition;

import com.insta360.compass.core.util.TimeUtil;
import com.insta360.store.business.rma.dto.RmaOrderQueryDTO;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: hyc
 * @Date: 2019-09-16
 * @Description:
 */
public class RmaQueryCondition {

    /**
     * 售后订单号
     */
    private String rmaNumber;

    /**
     * 售后类型
     *
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    private String rmaType;

    /**
     * 用户邮箱
     */
    private String userEmail;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 售后单创建开始时间
     */
    private LocalDateTime fromTime;

    /**
     * 售后单结束开始时间
     */
    private LocalDateTime endTime;

    /**
     * 售后单完成开始时间
     */
    private LocalDateTime finishFromTime;

    /**
     * 售后单完成结束时间
     */
    private LocalDateTime finishEndTime;

    /**
     * 国家三字码
     */
    private String countryCode;

    /**
     * 售后单状态
     *
     * @see RmaState
     */
    private List<Integer> rmaStates;

    /**
     * 是否退回
     */
    private Boolean needReturn;

    /**
     * 仅退款-选项文案类型
     */
    private String negotiationOptionsKey;

    /**
     * 用户售后原因
     */
    private String customerRefundReason;

    public static RmaQueryCondition parse(RmaOrderQueryDTO rmaOrderParam) {
        RmaQueryCondition condition = new RmaQueryCondition();

        if (StringUtils.isNotBlank(rmaOrderParam.getRmaNumber())) {
            condition.rmaNumber = rmaOrderParam.getRmaNumber();
        }

        if (StringUtils.isNotBlank(rmaOrderParam.getOrderNumber())) {
            condition.orderNumber = rmaOrderParam.getOrderNumber();
        }

        if (StringUtils.isNotBlank(rmaOrderParam.getUserEmail())) {
            condition.userEmail = rmaOrderParam.getUserEmail();
        }

        if (StringUtils.isNotBlank(rmaOrderParam.getRmaType())) {
            condition.rmaType = rmaOrderParam.getRmaType();
        }

        if (StringUtils.isNotBlank(rmaOrderParam.getCountryCode())) {
            condition.countryCode = rmaOrderParam.getCountryCode();
        }

        if (Objects.nonNull(rmaOrderParam.getFrom())) {
            condition.fromTime = TimeUtil.parseLocalDateTime(rmaOrderParam.getFrom());
        }

        if (Objects.nonNull(rmaOrderParam.getEnd())) {
            condition.endTime = TimeUtil.parseLocalDateTime(rmaOrderParam.getEnd());
        }

        if (Objects.nonNull(rmaOrderParam.getFinishFrom())) {
            condition.finishFromTime = TimeUtil.parseLocalDateTime(rmaOrderParam.getFinishFrom());
        }

        if (Objects.nonNull(rmaOrderParam.getFinishEnd())) {
            condition.finishEndTime = TimeUtil.parseLocalDateTime(rmaOrderParam.getFinishEnd());
        }

        if (CollectionUtils.isNotEmpty(rmaOrderParam.getStates())) {
            condition.rmaStates = rmaOrderParam.getStates();
        }

        if (Objects.nonNull(rmaOrderParam.getNeedReturn())) {
            condition.needReturn = rmaOrderParam.getNeedReturn();
        }

        if (StringUtils.isNotBlank(rmaOrderParam.getCustomerRefundReason()) || StringUtils.isNotBlank(rmaOrderParam.getNegotiationOptionsKey())) {
            condition.customerRefundReason = rmaOrderParam.getCustomerRefundReason();
            condition.negotiationOptionsKey = rmaOrderParam.getNegotiationOptionsKey();
            condition.rmaType = RmaType.rma_refund.name();
        }

        return condition;
    }

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public LocalDateTime getFromTime() {
        return fromTime;
    }

    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public LocalDateTime getFinishFromTime() {
        return finishFromTime;
    }

    public void setFinishFromTime(LocalDateTime finishFromTime) {
        this.finishFromTime = finishFromTime;
    }

    public LocalDateTime getFinishEndTime() {
        return finishEndTime;
    }

    public void setFinishEndTime(LocalDateTime finishEndTime) {
        this.finishEndTime = finishEndTime;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public List<Integer> getRmaStates() {
        return rmaStates;
    }

    public void setRmaStates(List<Integer> rmaStates) {
        this.rmaStates = rmaStates;
    }

    public Boolean getNeedReturn() {
        return needReturn;
    }

    public void setNeedReturn(Boolean needReturn) {
        this.needReturn = needReturn;
    }

    public String getNegotiationOptionsKey() {
        return negotiationOptionsKey;
    }

    public void setNegotiationOptionsKey(String negotiationOptionsKey) {
        this.negotiationOptionsKey = negotiationOptionsKey;
    }

    public String getCustomerRefundReason() {
        return customerRefundReason;
    }

    public void setCustomerRefundReason(String customerRefundReason) {
        this.customerRefundReason = customerRefundReason;
    }
}
