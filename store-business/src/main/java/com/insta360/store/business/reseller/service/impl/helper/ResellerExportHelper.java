package com.insta360.store.business.reseller.service.impl.helper;

import cn.hutool.core.io.FileUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.compass.libs.aliyun.oss.enums.EndpointEnum;
import com.insta360.compass.libs.aliyun.oss.enums.ModuleEnum;
import com.insta360.store.business.admin.order.export.OrderExportData;
import com.insta360.store.business.admin.order.service.impl.handler.constant.OrderExportConstant;
import com.insta360.store.business.admin.reseller.service.impl.constant.ResellerExportConstant;
import com.insta360.store.business.reseller.enums.ResellerWithdrawAccountType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/1/2 下午4:30
 */
@Component
public class ResellerExportHelper {

    @Autowired
    OSSService ossService;

    /**
     * 按照分销商邮箱分组返回
     *
     * @param orderExportDataList
     * @return
     */
    public List<OrderExportData> groupingByResellerEmail(List<OrderExportData> orderExportDataList) {
        // key -> 分销商邮箱  value -> 订单导出数据
        Map<String, List<OrderExportData>> exportDataMap = orderExportDataList.stream().collect(Collectors.groupingBy(OrderExportData::getResellerEmail));
        return exportDataMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    /**
     * 获取订单导出详情的文件名
     *
     * @param withdrawAccountType
     * @param orderExcelDataList
     * @return
     */
    public String getOrderExportFileName(String withdrawAccountType, List<OrderExportData> orderExcelDataList) {
        ResellerWithdrawAccountType accountType = ResellerWithdrawAccountType.parse(withdrawAccountType);
        String withdrawChannel = accountType != null ? accountType.getDesc() : "";
        // 第一个存在的分销商姓名
        String firstResellerName = orderExcelDataList.stream()
                .map(OrderExportData::getResellerName)
                .filter(StringUtil::isNotBlank)
                .filter(name -> !ResellerExportConstant.SLASH.equals(name))
                .findFirst()
                .orElse("");
        return StringUtil.isNotBlank(firstResellerName) ? withdrawChannel + "-" + firstResellerName + "-" + "订单详情" : withdrawChannel + "-" + "订单详情";
    }

    /**
     * 获取银行信息的文件名
     *
     * @param accountType
     * @return
     */
    public String getBankInfoExportFileName(ResellerWithdrawAccountType accountType) {
        String withdrawChannel = accountType != null ? accountType.getDesc() : "";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String formattedDate = LocalDateTime.now().plusHours(8).format(dateTimeFormatter);
        return withdrawChannel + "-" + "付款信息汇总" + "-" + formattedDate + ".xlsx";
    }

    /**
     * 创建文件
     * @param fileName
     * @return
     */
    public File createBankInfoFile(String fileName) {
        String path = OrderExportConstant.PATH;
        if (!FileUtil.exist(path)) {
            FileUtil.mkdir(path);
        }
        return new File(path + fileName);
    }

    /**
     * 上传oss
     * @param file
     * @return
     */
    public String uploadFile(File file) {
        String ossUrl = ossService.uploadFile(EndpointEnum.cn_shanghai, ModuleEnum.store, file);
        if (FileUtil.exist(file)) {
            FileUtil.del(file);
        }
        return ossUrl;
    }
}
