package com.insta360.store.business.prime.lib.response;

import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * Amazon Prime API更新产品响应类
 * <p>
 * 该类封装了从Prime GraphQL API更新产品操作返回的响应数据结构。
 * 主要用于解析和访问通过createProduct操作返回的产品ID等信息。
 * 在Prime商品服务中，用于获取新更新的产品的唯一标识符。
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
public class UpdateProductResponse implements PrimeResponse {

    /**
     * 更新产品的响应对象
     * <p>
     * 包含从Prime GraphQL API返回的更新产品结果，
     * 主要包含新更新产品的ID信息
     * </p>
     */
    private UpdateProduct updateProduct;

    public UpdateProduct getUpdateProduct() {
        return updateProduct;
    }

    public void setUpdateProduct(UpdateProduct updateProduct) {
        this.updateProduct = updateProduct;
    }

    /**
     * 获取Prime产品ID
     * <p>
     * 该方法安全地获取从API返回的Prime产品ID。
     * 使用Optional处理可能为空的情况，避免空指针异常。
     * 如果createProduct对象为空或ID为空，则返回空字符串。
     * </p>
     *
     * @return Prime产品唯一标识符，如果不存在则返回空字符串
     */
    public String primeProductId() {
        return Optional.ofNullable(updateProduct).map(UpdateProduct::getId).orElse(StringUtils.EMPTY);
    }

    /**
     * 更新产品结果内部类
     * <p>
     * 该内部类对应GraphQL响应中的createProduct字段，
     * 封装了更新产品操作的返回结果，主要包含产品ID信息。
     * </p>
     */
    public static class UpdateProduct {

        /**
         * 产品ID
         * <p>
         * 由Prime API生成的唯一产品标识符，
         * 用于后续对该产品的引用和操作
         * </p>
         */
        private String id;

        /**
         * 获取产品ID
         *
         * @return Prime系统中的产品唯一标识符
         */
        public String getId() {
            return id;
        }

        /**
         * 设置产品ID
         *
         * @param id Prime系统中的产品唯一标识符
         */
        public void setId(String id) {
            this.id = id;
        }
    }

}
