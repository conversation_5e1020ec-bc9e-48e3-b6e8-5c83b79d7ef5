package com.insta360.store.business.insurance.service.impl.handler.base;

import cn.hutool.core.lang.Validator;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.bo.InsuranceBO;
import com.insta360.store.business.insurance.bo.InsuranceInfoBO;
import com.insta360.store.business.insurance.bo.InsuranceSwitchBindingResultBO;
import com.insta360.store.business.insurance.constant.InsuranceCommonConstant;
import com.insta360.store.business.insurance.email.BaseInsuranceEmail;
import com.insta360.store.business.insurance.email.CarePlusBindUrlEmail;
import com.insta360.store.business.insurance.email.DeviceCarePlusInsuranceEmail;
import com.insta360.store.business.insurance.enums.InsuranceOrderOrigin;
import com.insta360.store.business.insurance.enums.InsuranceOriginType;
import com.insta360.store.business.insurance.enums.InsuranceRebindResponseStatus;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.insurance.model.CareInsuranceActivationCard;
import com.insta360.store.business.insurance.model.CarePlusInsurance;
import com.insta360.store.business.insurance.model.InsuranceServiceCommodityBind;
import com.insta360.store.business.insurance.model.InsuranceServiceType;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.insurance.service.impl.helper.generator.CarePlusInsuranceCodeGenerator;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.email.BaseOrderEmail;
import com.insta360.store.business.order.email.DeviceCarePlusInsuranceNotifyEmail;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2024-02-01 12:06
 */
@Component
public class CarePlusHandler extends BaseInsuranceHandler {

    public static final Logger LOGGER = LoggerFactory.getLogger(CarePlusHandler.class);

    @Autowired
    CarePlusInsuranceCodeGenerator carePlusInsuranceCodeGenerator;

    @Override
    public ServiceType getServiceType() {
        return ServiceType.care_plus;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void autoActivationInsurance(InsuranceBO activationCreation) {
        // 防止重复激活
        String serial = activationCreation.getDeviceSerial();
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(serial);
        if (Objects.nonNull(carePlusInsurance)) {
            return;
        }

        // care和相机数量一致校验
        Order order = activationCreation.getOrder();
        Integer insuranceItemNumber = activationCreation.getInsuranceNumber();
        List<CarePlusInsurance> careInsurances = carePlusInsuranceService.listByOrderNumberAndDeviceType(order.getOrderNumber(), activationCreation.getDeviceType());
        if (careInsurances.size() >= insuranceItemNumber) {
            return;
        }

        String insuranceNumber = carePlusInsuranceCodeGenerator.generateInsuranceNumber();
        String activateCode = carePlusInsuranceCodeGenerator.generateActivateCode(insuranceNumber);

        carePlusInsurance = this.buildCarePlusInsurance(activationCreation, false, insuranceNumber, activateCode);
        carePlusInsuranceService.save(carePlusInsurance);
        LOGGER.info("carePlus自动激活成功:" + serial);

        // 发送激活邮件（包含服务协议）
        BaseInsuranceEmail sendEmail = insuranceEmailFactory.getEmail(carePlusInsurance, DeviceCarePlusInsuranceEmail.class);
        sendEmail.doSend(order.getContactEmail());
    }

    @Override
    public void autoInvalidInsurance(String insuranceType, List<String> serials, Integer orderId) {
        if (CollectionUtils.isEmpty(serials)) {
            return;
        }

        if (StringUtil.isBlank(insuranceType)) {
            Order order = orderService.getById(orderId);
            String message = String.format("care+自动作废失败，原因：增值服务类型解析失败。商城订单号：%s", order.getOrderNumber());
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.YCT, FeiShuAtUser.ZM, FeiShuAtUser.GQY, FeiShuAtUser.ZDX);
            return;
        }

        ServiceType serviceType = ServiceType.parse(insuranceType);
        if (Objects.isNull(serviceType)) {
            Order order = orderService.getById(orderId);
            String message = String.format("care+自动作废失败，原因：增值服务类型解析失败。商城订单号：%s", order.getOrderNumber());
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.YCT, FeiShuAtUser.ZM, FeiShuAtUser.GQY, FeiShuAtUser.ZDX);
            return;
        }
        LOGGER.info("自动作废care+涉及的序列号:" + serials);
        List<CarePlusInsurance> careInsurances = carePlusInsuranceService.listBySerial(serials);
        Boolean success = carePlusInsuranceService.batchAutoInvalid(careInsurances);

        if (!success) {
            Order order = orderService.getById(orderId);
            String message = String.format("care+自动作废失败，原因：数据库修改失败。商城订单号：%s", order.getOrderNumber());
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.YCT, FeiShuAtUser.ZM, FeiShuAtUser.JHF, FeiShuAtUser.GQY, FeiShuAtUser.ZDX);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void bindInsurance(InsuranceBO insuranceParam) {
        String deviceSerial = insuranceParam.getDeviceSerial();
        // 通用校验
        this.checkCamera(deviceSerial);

        String activateCode = insuranceParam.getActivateCode();
        if (StringUtil.isBlank(activateCode)) {
            throw new InstaException(InsuranceErrorCode.ActivationCodeNotFoundException);
        }

        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByActivateCode(activateCode);
        if (Objects.isNull(carePlusInsurance)) {
            throw new InstaException(InsuranceErrorCode.InsuranceNotFoundException);
        }

        // 绑定实体卡/care后的设备不可再次绑定(实体卡禁用掉的可以再次绑定)
        this.checkSerial(deviceSerial);

        // 序列号校验
        this.isCameraCheck(deviceSerial, carePlusInsurance.getDeviceType(), getServiceType());

        if (carePlusInsurance.getBindTime() != null) {
            throw new InstaException(InsuranceErrorCode.DeviceAlreadyBindException);
        }

        // 校验序列号
        LocalDateTime createTime = this.getCreateTime(deviceSerial, insuranceParam.getStore());

        // 校验设备类型是否一致
        this.CheckDeviceSerialExist(deviceSerial, getServiceType().name(), carePlusInsurance.getDeviceType());

        carePlusInsurance.setDeviceSerial(deviceSerial);
        carePlusInsurance.setBindTime(createTime);
        carePlusInsurance.setExpireTime(insuranceServiceTypeService.getExpireDay(carePlusInsurance.getInsuranceType(), createTime));
        carePlusInsurance.setDisabled(false);
        carePlusInsurance.setUpdateTime(LocalDateTime.now());
        carePlusInsuranceService.updateById(carePlusInsurance);
        LOGGER.info("carePlus商城手动激活成功:{}", deviceSerial);
    }

    @Override
    public void checkOnLine(InsuranceBO insuranceParam) {
        String deviceSerial = insuranceParam.getDeviceSerial();
        InstaCountry instaCountry = insuranceParam.getInstaCountry();
        // 校验是否在上线国家或者地区
        List<String> careOnlineRegions = insuranceCommonConfig.getCarePlusOnlineRegions();
        if (careOnlineRegions.contains(instaCountry.name())) {
            return;
        }

        LOGGER.info(String.format("care+不在当前地区销售,序列号:%s,地区:%s", deviceSerial, instaCountry.getNameZh()));

        if (InsuranceOriginType.store.name().equals(insuranceParam.getOrigin())) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("care+不在当前地区销售,序列号:%s,地区:%s", deviceSerial, instaCountry.getNameZh())
                    , FeiShuGroupRobot.MainNotice, FeiShuAtUser.PY, FeiShuAtUser.ZXY);
        }

        throw new InstaException(CommonErrorCode.InvalidParameter);
    }

    @Override
    public void adminCheckSerial(String deviceSerial, String deviceType) {
        // 验证序列号是否存在
        DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(deviceSerial);
        if (deviceInfo == null) {
            throw new InstaException(InsuranceErrorCode.DeviceNotFoundException);
        }
        LOGGER.info(String.format("后台序列号校验,deviceInfo:[%S]", deviceInfo.toString()));

        // 是否属于该保险机型的序列号
        if (!deviceType.equals(deviceInfo.getDeviceType())) {
            throw new InstaException(InsuranceErrorCode.DeviceMismatchException);
        }

        // 一个序列号只让绑定一次
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(deviceSerial);
        if (carePlusInsurance != null) {
            throw new InstaException(InsuranceErrorCode.DeviceAlreadyBindException);
        }

    }

    @Override
    public String createUrl(InsuranceBO insuranceParam) {
        String insuranceNumber = carePlusInsuranceCodeGenerator.generateInsuranceNumber();
        String activateCode = carePlusInsuranceCodeGenerator.generateActivateCode(insuranceNumber);

        String email = insuranceParam.getEmail();
        String deviceType = insuranceParam.getDeviceType();
        String insuranceType = insuranceParam.getInsuranceType();

        if (!Validator.isEmail(email) || deviceType.isEmpty() || insuranceType == null) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        CarePlusInsurance carePlusInsurance = new CarePlusInsurance();
        String orderNumber = insuranceParam.getOrderNumber();
        carePlusInsurance.setInsuranceNumber(insuranceNumber);
        carePlusInsurance.setActivateCode(activateCode);
        carePlusInsurance.setDeviceType(deviceType);
        carePlusInsurance.setEmail(email);
        carePlusInsurance.setInsuranceType(insuranceType);
        carePlusInsurance.setOrderOrigin(insuranceParam.getOrderOrigin());
        carePlusInsurance.setOrderNumber(orderNumber);
        carePlusInsurance.setArea(insuranceParam.getArea());
        carePlusInsurance.setCreateTime(LocalDateTime.now());
        carePlusInsurance.setUpdateTime(LocalDateTime.now());
        carePlusInsurance.setDisabled(false);
        carePlusInsuranceService.save(carePlusInsurance);

        InstaLanguage instaLanguage = InstaLanguage.parse(insuranceParam.getLanguage());
        if (StringUtil.isNotBlank(orderNumber) && !orderNumber.startsWith(InsuranceCommonConstant.ORDER_PREFIX)) {
            // 除商城订单发邮件
            BaseInsuranceEmail tradeEmail = insuranceEmailFactory.getEmail(carePlusInsurance, instaLanguage, CarePlusBindUrlEmail.class);
            tradeEmail.doSend(carePlusInsurance.getEmail());
        }

        // 激活链接
        return insuranceHelper.generateBindingUrl(activateCode, insuranceType);
    }

    @Override
    public List<InsuranceInfoBO> getBindInsuranceInfoList(OrderItemBind orderItemBind) {
        Order order = orderService.getById(orderItemBind.getOrderId());
        List<CarePlusInsurance> carePlusInsuranceList = carePlusInsuranceService.listByOrderNumber(order.getOrderNumber());
        OrderItem orderItem = orderItemService.getById(orderItemBind.getOrderItemId());
        if (CollectionUtils.isEmpty(carePlusInsuranceList)) {
            return null;
        }

        InsuranceServiceCommodityBind commodityBind = serviceCommodityBindService.getByCommodityId(orderItem.getCommodity());
        InsuranceServiceType insuranceServiceType = insuranceServiceTypeService.getById(commodityBind.getServiceId());
        return carePlusInsuranceList.stream()
                .filter(carePlusInsurance -> getServiceType().name().equals(insuranceServiceType.getServiceType())
                        && carePlusInsurance.getDeviceType().equals(commodityBind.getDeviceType()))
                .map(carePlusInsurance -> {
                    InsuranceInfoBO insuranceInfoVO = new InsuranceInfoBO();
                    insuranceInfoVO.setInsuranceNumber(carePlusInsurance.getInsuranceNumber());
                    return insuranceInfoVO;
                }).collect(Collectors.toList());
    }

    @Override
    public InsuranceSwitchBindingResultBO switchBinding(String oldDeviceSerial, String newDeviceSerial) {
        // 只进行换绑操作，前置过滤在api层已完成
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(oldDeviceSerial);
        if (carePlusInsurance == null) {
            throw new InstaException(InsuranceErrorCode.InsuranceNotFoundException);
        }
        carePlusInsurance.setDeviceSerial(newDeviceSerial);
        try {
            carePlusInsuranceService.updateById(carePlusInsurance);
        } catch (Exception e) {
            LOGGER.error(String.format("care_plus商城换绑失败，旧设备序列号：%s，新设备序列号：%s，失败原因：%s", oldDeviceSerial, newDeviceSerial, e.getMessage()), e);
            return new InsuranceSwitchBindingResultBO(Boolean.FALSE, InsuranceRebindResponseStatus.FAILURE.getStatus());
        }
        LOGGER.info(String.format("care_plus商城换绑成功，旧设备序列号：%s，新设备序列号：%s", oldDeviceSerial, newDeviceSerial));
        return new InsuranceSwitchBindingResultBO(Boolean.TRUE, InsuranceRebindResponseStatus.SUCCESS.getStatus());
    }

    @Override
    public void sendPromoteEmail(OrderDeliveryUniqueCode orderDeliveryUniqueCode, List<OrderItem> orderItems, Order order) {
        // 剔除包含care+产品的订单
        List<Integer> productIds = orderItems.stream().map(OrderItem::getProduct).collect(Collectors.toList());
        productIds.retainAll(getServiceType().getProductIds());
        if (CollectionUtils.isNotEmpty(productIds)) {
            return;
        }

        // Care+上线的地区才参与
        List<String> carePlusOnlineRegions = insuranceCommonConfig.getCarePlusOnlineRegions();
        if (!carePlusOnlineRegions.contains(order.getArea())) {
            return;
        }

        // 过滤市场领用与研发领用以及对公转账的订单
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (PaymentChannel.isTransferPayment(PaymentChannel.parse(orderPayment.getChannel()))) {
            return;
        }

        // 发送推广邮件
        BaseOrderEmail orderEmail = orderEmailFactory.getEmail(order, DeviceCarePlusInsuranceNotifyEmail.class);
        this.saveStoreEmailSendRuleSendEmail(order, orderEmail);
        LOGGER.info(String.format("[发送推广邮件]carePlus促销邮件发送成功,序列号[%s],订单id[%s]",
                orderDeliveryUniqueCode.getUniqueCode(), orderDeliveryUniqueCode.getOrderId()));
    }

    @Override
    public void useCard(CareInsuranceActivationCard careInsuranceActivationCard, LocalDateTime useTime) {
        if (!getServiceType().name().equals(careInsuranceActivationCard.getInsuranceType())) {
            throw new InstaException(InsuranceErrorCode.InsuranceTypeNotMatchException);
        }
        Integer remainingUsageCount = careInsuranceActivationCard.getRemainingUsageCount();
        if (remainingUsageCount == null || remainingUsageCount <= InsuranceCommonConstant.USAGE_ZERO) {
            throw new InstaException(InsuranceErrorCode.UsageLimitExceededException);
        }

        useTime = Objects.isNull(useTime) ? LocalDateTime.now() : useTime;
        if (useTime.isAfter(careInsuranceActivationCard.getExpireTime())) {
            throw new InstaException(CommonErrorCode.InsuranceAlreadyUsed);
        }

        careInsuranceActivationCardService.useCard(careInsuranceActivationCard, useTime);
    }

    @Override
    public void useVirtualService(String deviceSerial, LocalDateTime useTime) {
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getRepairBySerialNotExpire(deviceSerial);
        if (Objects.isNull(carePlusInsurance)) {
            throw new InstaException(CommonErrorCode.InsuranceNotFound);
        }

        DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(deviceSerial);
        if (deviceInfo == null || !deviceInfo.getDeviceType().equals(carePlusInsurance.getDeviceType())) {
            throw new InstaException(InsuranceErrorCode.DeviceMismatchException);
        }

        Integer remainingUsageCount = carePlusInsurance.getRemainingUsageCount();
        if (remainingUsageCount == null || remainingUsageCount <= InsuranceCommonConstant.USAGE_ZERO) {
            throw new InstaException(InsuranceErrorCode.UsageLimitExceededException);
        }

        useTime = Objects.isNull(useTime) ? LocalDateTime.now() : useTime;
        if (useTime.isAfter(carePlusInsurance.getExpireTime())) {
            throw new InstaException(CommonErrorCode.InsuranceAlreadyUsed);
        }

        carePlusInsuranceService.useCarePlus(carePlusInsurance, useTime);
    }

    @Override
    public Boolean checkInsuranceBySerials(List<String> serials) {
        List<CarePlusInsurance> carePlusInsuranceList = carePlusInsuranceService.listBySerial(serials);
        return CollectionUtils.isNotEmpty(carePlusInsuranceList);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void businessBindService(InsuranceBO insuranceParam) {
        String serial = insuranceParam.getDeviceSerial();
        this.checkSerialBind(serial, insuranceParam.getBusinessType());
        LOGGER.info("carePlus云服务权益校验成功:" + serial);

        String insuranceNumber = carePlusInsuranceCodeGenerator.generateInsuranceNumber();
        String activateCode = carePlusInsuranceCodeGenerator.generateActivateCode(insuranceNumber);

        insuranceParam.setOrderOrigin(InsuranceOrderOrigin.store.name());
        CarePlusInsurance carePlusInsurance = this.buildCarePlusInsurance(insuranceParam, true, insuranceNumber, activateCode);
        carePlusInsuranceService.save(carePlusInsurance);
        LOGGER.info("carePlus云服务权益绑定成功:" + serial);

        // 发送激活邮件（包含服务协议）
        BaseInsuranceEmail sendEmail = insuranceEmailFactory.getEmail(carePlusInsurance, DeviceCarePlusInsuranceEmail.class);
        sendEmail.doSend(insuranceParam.getEmail());
    }

    @Override
    public void cloudCancelService(String serial) {
        if (StringUtil.isBlank(serial)) {
            return;
        }
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getBySerialCloud(serial);
        if (Objects.isNull(carePlusInsurance)) {
            return;
        }
        carePlusInsurance.setDisabled(true);
        carePlusInsurance.setUpdateTime(LocalDateTime.now());
        carePlusInsuranceService.updateById(carePlusInsurance);
    }
}
