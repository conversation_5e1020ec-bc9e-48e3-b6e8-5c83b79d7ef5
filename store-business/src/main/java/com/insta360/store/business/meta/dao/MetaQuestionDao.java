package com.insta360.store.business.meta.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.meta.model.MetaQuestion;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface MetaQuestionDao extends BaseDao<MetaQuestion> {

}
