package com.insta360.store.business.integration.tiktok.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * 用户dto
 *
 * <AUTHOR>
 * @date 2023/03/03
 */
public class UserDTO implements Serializable {

    /**
     * 外部id
     */
    @JSO<PERSON>ield(name = "external_id")
    private String externalId;

    /**
     * 电话号码
     */
    @JSONField(name = "phone_number")
    private String phoneNumber;

    /**
     * 电子邮件
     */
    private String email;

    /**
     * ttp
     */
    private String ttp;

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTtp() {
        return ttp;
    }

    public void setTtp(String ttp) {
        this.ttp = ttp;
    }

    @Override
    public String toString() {
        return "UserDTO{" +
                "externalId='" + externalId + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", email='" + email + '\'' +
                ", ttp='" + ttp + '\'' +
                '}';
    }
}