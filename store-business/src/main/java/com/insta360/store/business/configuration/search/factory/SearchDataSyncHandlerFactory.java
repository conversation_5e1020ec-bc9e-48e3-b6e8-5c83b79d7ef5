package com.insta360.store.business.configuration.search.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.search.constant.SearchDataChangeType;
import com.insta360.store.business.configuration.search.handler.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/3
 */
@Component
public class SearchDataSyncHandlerFactory {

    /**
     * 根据数据类型获取对应的处理器
     *
     * @param searchDataSyncType 数据类型
     * @return 处理器
     */
    public SearchDataChangeSyncService getSearchDataChangeSyncService(String searchDataSyncType) {
        if (StringUtils.isBlank(searchDataSyncType)) {
            return null;
        }

        switch (searchDataSyncType) {
            case SearchDataChangeType.COMMODITY:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityDataChangeSyncHandler.class);
            case SearchDataChangeType.PRODUCT:
                return ApplicationContextHolder.getApplicationContext().getBean(ProductDataChangeSyncHandler.class);
            case SearchDataChangeType.ACCESSORIES_CATEGORY:
                return ApplicationContextHolder.getApplicationContext().getBean(AccessoriesCategoryDataChangeSyncHandler.class);
            case SearchDataChangeType.ADAPTER_TYPE_MAIN:
                return ApplicationContextHolder.getApplicationContext().getBean(AdapterTypeMainDataChangeSyncHandler.class);
            case SearchDataChangeType.COMMODITY_TAG_GROUP:
                return ApplicationContextHolder.getApplicationContext().getBean(CommodityTagGroupDataSyncHandler.class);
            case SearchDataChangeType.NAVIGATION_BAR_CATEGORY:
                return ApplicationContextHolder.getApplicationContext().getBean(NavigationBarCategorySyncHandler.class);
            default:
                throw new InstaException(CommonErrorCode.InvalidParameter);
        }
    }
}
