package com.insta360.store.business.integration.wto.oms.service.helper;

import com.insta360.store.business.integration.wto.enums.OmsIntegrationBusinessType;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsSkuCodeBO;
import com.insta360.store.business.order.enums.OrderStockSourceType;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.outgoing.mq.wto.helper.StoreDataSyncOmsMessageSendHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/16
 */
@Component
public class OmsPushPlatformRouterHelper {

    @Autowired
    StoreDataSyncOmsMessageSendHelper storeDataSyncOmsMessageSendHelper;

    /**
     * sku编码同步
     *
     * @param skuCodes sku编码列表
     */
    public void skuCodeSync(List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) {
            return;
        }
        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        omsExecuteBo.setOmsSkuCodeBo(new OmsSkuCodeBO(skuCodes));
        storeDataSyncOmsMessageSendHelper.sendStoreDataSyncOmsMessage(OmsIntegrationBusinessType.STORE_SKU_CODE_SYNC, omsExecuteBo);
    }

    /**
     * 正向订单推送oms路由分配
     *
     * @param order      订单对象，包含订单详细信息
     * @param isAutoPush 是否自动推单的标志
     */
    public void determinePlatformForOrder(Order order, boolean isAutoPush) {
        // 是否自动推单
        OrderStockSourceType orderStockSourceType = isAutoPush ? OrderStockSourceType.SYSTEM : null;

        // 推送巨益
        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        omsExecuteBo.setOrderId(order.getId());
        omsExecuteBo.setOrderStockSourceType(orderStockSourceType);
        storeDataSyncOmsMessageSendHelper.sendStoreDataSyncOmsMessage(OmsIntegrationBusinessType.STORE_PUSH_ORDER, omsExecuteBo);
    }

    /**
     * 异步确定订单列表中的每个订单适用的平台
     * 此方法使用异步执行，意味着它将在单独的线程中执行，不会阻塞调用线程
     * 这对于需要处理大量订单或对性能有高要求的情况特别有用
     *
     * @param orderList 订单列表，每个订单将被处理以确定适用的平台
     */
    @Async
    public void determinePlatformForOrder(List<Order> orderList) {
        // 遍历订单列表，为每个订单调用determinePlatformForOrder方法
        orderList.forEach(order -> determinePlatformForOrder(order, false));
    }
}
