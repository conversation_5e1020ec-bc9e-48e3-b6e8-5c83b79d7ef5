package com.insta360.store.business.cloud.exception;

import com.insta360.compass.core.exception.ErrorCode;

/**
 * <AUTHOR>
 * @Description 云存储商城权益自定义异常码类
 * @Date 2024/5/13
 */
public enum CloudStorageBenefitErrorCode implements ErrorCode {

    /**
     * 延保超过激活时间
     */
    ExtendOverTimeException(11002, "[延保]设备超过激活时间"),

    /**
     * care超过激活时间
     */
    CareOverTimeException(11003, "[care]设备超过激活时间"),

    /**
     * 当前机型不支持兑换增值服务权益
     */
    InsuranceNotSupportException(11004, "当前机型不支持兑换增值服务权益"),

    /**
     * 当前用户不存在云服务兑换权益
     */
    InsuranceNotExistException(11005, "当前用户不存在云服务兑换权益"),

    /**
     * 同一个周期存在两种权益，其中一个已经绑定，剩余一个与已绑定的序列号不一致
     */
    SerialInconsistentException(11006, "与已绑定的序列号不一致"),

    /**
     * 相机没激活不允许绑定
     */
    ProhibitedBindException(11007, "相机没激活不允许绑定"),

    /**
     * 补偿代金券失败
     */
    CompensateGiftCardFailedException(11008, "补偿代金券失败"),

    /**
     * 序列号代金券补偿重复
     */
    CompensateRepeatException(11009, "序列号代金券补偿重复"),


    /////----------------------配件折扣权益异常码
    /**
     * 配件折扣权益不存在
     */
    DiscountBenefitNotExistException(12001, "当前用户不存在配件折扣权益"),
    /**
     * 权益额度已用完
     */
    NoQuotaException(12002, "权益额度已用完"),

    /**
     * 配件折扣权益已使用额度回退异常
     */
    UsedQuotaGoBackException(12003, "配件折扣权益已使用额度回退异常"),

    /**
     * 配件折扣权益可使用额度回退异常
     */
    RemainderQuotaGoBackException(12004, "配件折扣权益可使用额度回退异常"),

    /**
     * 免单券场景'配件折扣权益'不再适用
     */
    NotSupportFreeCouponException(13005, "免单券场景'配件折扣权益'不再适用"),

    /**
     * 配件八折权益额度扣减异常
     */
    DiscountBenefitQuotaDeductionException(13006, "配件八折权益额度扣减异常"),


    ///-----------------------主权益记录异常码
    /**
     * 非法的渠道来源
     */
    IllegalPlatformException(13001, "非法的渠道来源"),

    /**
     * 非法的订阅类型
     */
    IllegalSkuSubscribeTypeException(13002, "非法的订阅类型"),

    /**
     * 未开放的订阅类型
     */
    NotOpenSkuSubscribeTypeException(13003, "未开放的订阅类型"),

    /**
     * 订阅类型、容量映射异常
     */
    TypeMappingException(13004, "订阅类型、容量映射异常"),

    /**
     * 用户权益已过期
     */
    UserBenefitExpiredException(13005, "用户权益已过期"),

    /**
     * 用户未订阅
     */
    UserNotSubscribedException(13006, "用户未订阅"),

    /**
     * 非法的权益变更场景类型
     */
    IllegalServiceScenesTypeException(13007, "非法的权益变更场景类型"),

    /**
     * 错误的过期时间推送
     */
    IllegalExpiredTimeException(13008, "错误的过期时间推送"),

    /**
     * 平台来源不一致
     */
    PlatformSourceInconsistentException(13009, "平台来源不一致"),

    /**
     * 权益记录构建异常
     */
    BenefitBuildException(13010, "权益记录构建异常"),

    /**
     * 原始权益记录不存在
     */
    NotExistOriginalBenefitRecordException(13011, "原始权益记录不存在"),

    /**
     * 不支持的地区
     */
    NotSupportRegionException(13012,"不支持的地区"),

    /**
     * 重复订阅
     */
    RepeatSubscriptionException(13013, "重复订阅"),

    /**
     * 权益回收异常
     */
    BenefitRecycleException(105002001,  "权益回收异常")

    ;


    /**
     * 异常码
     */
    private final Integer code;

    /**
     * 异常信息
     */
    private final String msg;


    CloudStorageBenefitErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
