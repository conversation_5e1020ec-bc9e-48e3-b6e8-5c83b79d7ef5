package com.insta360.store.business.admin.upload.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 上传信息dto
 * @Date 2023/10/7
 */
public class UploadInfoDTO implements Serializable {

    /**
     * Excel文件链接
     */
    @NotBlank(message = "Excel文件链接必填")
    private String excelOssUrl;

    /**
     * 上传数据业务类型
     *
     * @see com.insta360.store.business.admin.upload.enums.UploadBusinessType
     */
    @NotNull(message = "上传数据业务类型必填")
    private Integer uploadBusinessType;

    /**
     * 文件名
     */
    @NotBlank(message = "Excel文件名必填")
    private String fileName;

    /**
     * 导入数量模版类型
     * @see com.insta360.store.business.admin.upload.enums.UploadTemplateType
     */
    @NotNull(message = "模版类型必填")
    private Integer templateType;


    public String getExcelOssUrl() {
        return excelOssUrl;
    }

    public void setExcelOssUrl(String excelOssUrl) {
        this.excelOssUrl = excelOssUrl;
    }

    public Integer getUploadBusinessType() {
        return uploadBusinessType;
    }

    public void setUploadBusinessType(Integer uploadBusinessType) {
        this.uploadBusinessType = uploadBusinessType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    @Override
    public String toString() {
        return "UploadInfoDTO{" +
                "excelOssUrl='" + excelOssUrl + '\'' +
                ", uploadBusinessType='" + uploadBusinessType + '\'' +
                ", fileName='" + fileName + '\'' +
                ", templateType=" + templateType +
                '}';
    }
}
