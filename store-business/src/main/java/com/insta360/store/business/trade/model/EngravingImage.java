package com.insta360.store.business.trade.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.trade.enums.CustomImageOriginEnum;
import com.insta360.store.business.trade.enums.IconCustomShellOriginEnum;
import com.insta360.store.business.trade.service.impl.helper.bind_service.enums.CustomImageType;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2019-08-22
 * @Description:
 */
@TableName("engraving_image")
public class EngravingImage extends BaseModel<EngravingImage> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单子项ID
     */
    private Integer orderItemId;

    /**
     * 产品id
     */
    @JSONField(name = "product_id")
    private Integer productId;

    /**
     * 绑定id
     */
    @JSONField(name = "bind_id")
    private Integer bindId;

    /**
     * 定制数量
     */
    @JSONField(name = "custom_number")
    private Integer customNumber;

    /**
     * 序号
     */
    @JSONField(name = "serial_id")
    private String serialId;

    /**
     * 序号前缀
     */
    private String serialPrefix;

    /**
     * 序号数字
     */
    private Long serialNum;

    /**
     * 定制效果图
     */
    @JSONField(name = "customer_image_url")
    private String customerImageUrl;

    /**
     * 源图像url
     */
    @JSONField(name = "origin_image_url")
    private String originImageUrl;

    /**
     * logo图
     */
    @JSONField(name = "logo_image_url")
    private String logoImageUrl;

    /**
     * 生产图像url
     */
    @JSONField(name = "produce_image_url")
    private String produceImageUrl;

    /**
     * 定制贴类型
     */
    private String customType;

    /**
     * 图片来源
     */
    private Integer imageOrigin;

    /**
     * ICON来源
     */
    private Integer iconOrigin;

    /**
     * 图标颜色
     */
    private String iconColor;

    @JSONField(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 图片资源来源
     *
     * @return
     */
    public CustomImageOriginEnum parseByOrigin() {
        return CustomImageOriginEnum.matchCode(imageOrigin);
    }

    /**
     * ICON资源来源
     *
     * @return
     */
    public IconCustomShellOriginEnum parseByIconOrigin() {
        return IconCustomShellOriginEnum.parse(this.iconOrigin);
    }

    /**
     * 解析图标类型
     *
     * @return {@link String}
     */
    public String parseByIconType() {
        IconCustomShellOriginEnum iconCustomShellOriginEnum = parseByIconOrigin();
        if (StringUtil.isNotBlank(this.iconColor) && IconCustomShellOriginEnum.CUSTOM_BY_NORMAL.equals(iconCustomShellOriginEnum)) {
            return parseByIconOrigin().getDetail() + "-" + this.iconColor;
        }
        return parseByIconOrigin().getDetail();
    }

    /**
     * 定制图像类型
     *
     * @return {@link CustomImageType }
     */
    public CustomImageType customImageType() {
        return CustomImageType.parse(this.customType);
    }

    /**
     * 解析定制贴序号及图片来源
     *
     * @return
     */
    public String parseSerialIdAndImageOrigin() {
        String customSerialNumber = StringUtils.defaultIfBlank(serialId, id.toString());
        Integer imageOrigin = this.getImageOrigin();
        CustomImageOriginEnum originEnum = CustomImageOriginEnum.matchCode(imageOrigin);
        return customSerialNumber + originEnum.getEnDetail();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getBindId() {
        return bindId;
    }

    public void setBindId(Integer bindId) {
        this.bindId = bindId;
    }

    public Integer getCustomNumber() {
        return customNumber;
    }

    public void setCustomNumber(Integer customNumber) {
        this.customNumber = customNumber;
    }

    public String getSerialId() {
        return serialId;
    }

    public void setSerialId(String serialId) {
        this.serialId = serialId;
    }

    public String getSerialPrefix() {
        return serialPrefix;
    }

    public void setSerialPrefix(String serialPrefix) {
        this.serialPrefix = serialPrefix;
    }

    public Long getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(Long serialNum) {
        this.serialNum = serialNum;
    }

    public String getCustomerImageUrl() {
        return customerImageUrl;
    }

    public void setCustomerImageUrl(String customerImageUrl) {
        this.customerImageUrl = customerImageUrl;
    }

    public String getOriginImageUrl() {
        return originImageUrl;
    }

    public void setOriginImageUrl(String originImageUrl) {
        this.originImageUrl = originImageUrl;
    }

    public String getLogoImageUrl() {
        return logoImageUrl;
    }

    public void setLogoImageUrl(String logoImageUrl) {
        this.logoImageUrl = logoImageUrl;
    }

    public String getProduceImageUrl() {
        return produceImageUrl;
    }

    public void setProduceImageUrl(String produceImageUrl) {
        this.produceImageUrl = produceImageUrl;
    }

    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType;
    }

    public Integer getImageOrigin() {
        return imageOrigin;
    }

    public void setImageOrigin(Integer imageOrigin) {
        this.imageOrigin = imageOrigin;
    }

    public Integer getIconOrigin() {
        return iconOrigin;
    }

    public void setIconOrigin(Integer iconOrigin) {
        this.iconOrigin = iconOrigin;
    }

    public String getIconColor() {
        return iconColor;
    }

    public void setIconColor(String iconColor) {
        this.iconColor = iconColor;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "EngravingImage{" +
                "id=" + id +
                ", orderNumber='" + orderNumber + '\'' +
                ", orderItemId=" + orderItemId +
                ", productId=" + productId +
                ", bindId=" + bindId +
                ", customNumber=" + customNumber +
                ", serialId='" + serialId + '\'' +
                ", serialPrefix='" + serialPrefix + '\'' +
                ", serialNum=" + serialNum +
                ", customerImageUrl='" + customerImageUrl + '\'' +
                ", originImageUrl='" + originImageUrl + '\'' +
                ", logoImageUrl='" + logoImageUrl + '\'' +
                ", produceImageUrl='" + produceImageUrl + '\'' +
                ", customType='" + customType + '\'' +
                ", imageOrigin=" + imageOrigin +
                ", iconOrigin=" + iconOrigin +
                ", iconColor='" + iconColor + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
