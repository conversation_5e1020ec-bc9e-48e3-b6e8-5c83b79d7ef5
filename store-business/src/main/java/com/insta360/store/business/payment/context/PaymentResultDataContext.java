package com.insta360.store.business.payment.context;

import com.insta360.store.business.meta.enums.PaymentChannel;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2022/11/29
 * @Description:
 */
@Scope("prototype")
@Component
public class PaymentResultDataContext {

    /**
     * 支付结果数据上下文
     */
    private static final ThreadLocal<PaymentResultDataContext> PAYMENT_RESULT_CONTEXT_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 支付结果
     */
    private String resultData;

    /**
     * 支付渠道
     */
    private PaymentChannel paymentChannel;

    public static void set(PaymentResultDataContext paymentResultDataContext) {
        PAYMENT_RESULT_CONTEXT_THREAD_LOCAL.set(paymentResultDataContext);
    }

    public static PaymentResultDataContext get() {
        return PAYMENT_RESULT_CONTEXT_THREAD_LOCAL.get();
    }

    public static void remove() {
        PAYMENT_RESULT_CONTEXT_THREAD_LOCAL.remove();
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getResultData() {
        return resultData;
    }

    public void setResultData(String resultData) {
        this.resultData = resultData;
    }

    public PaymentChannel getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(PaymentChannel paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    @Override
    public String toString() {
        return "PaymentResultDataContext{" +
                "orderNumber='" + orderNumber + '\'' +
                ", resultData='" + resultData + '\'' +
                ", paymentChannel=" + paymentChannel +
                '}';
    }
}
