package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.meta.model.SceneryTagInfo;
import com.insta360.store.business.meta.dao.SceneryTagInfoDao;
import com.insta360.store.business.meta.service.SceneryTagInfoService;
import com.insta360.compass.core.common.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-12-18
 * @Description:
 */
@Service
public class SceneryTagInfoServiceImpl extends BaseServiceImpl<SceneryTagInfoDao, SceneryTagInfo> implements SceneryTagInfoService {

    @Override
    public List<SceneryTagInfo> listByTagId(Integer sceneryTagId) {
        QueryWrapper<SceneryTagInfo> qw = new QueryWrapper<>();
        qw.eq("scenery_tag_id", sceneryTagId);
        return baseMapper.selectList(qw);
    }

    @Override
    public void deleteByTagId(Integer sceneryTagId) {
        UpdateWrapper<SceneryTagInfo> uw = new UpdateWrapper<>();
        uw.eq("scenery_tag_id", sceneryTagId);
        baseMapper.delete(uw);
    }

    @Override
    public void saveSceneryTagInfoBatch(List<SceneryTagInfo> sceneryTagInfos) {
        if (CollectionUtils.isEmpty(sceneryTagInfos)) {
            return;
        }

        baseMapper.saveSceneryTagInfoBatch(sceneryTagInfos);
    }

    @Override
    public List<SceneryTagInfo> listByTagIds(List<Integer> textFilterIds, InstaLanguage language) {
        QueryWrapper<SceneryTagInfo> qw = new QueryWrapper<>();
        qw.eq("language", language);
        qw.in("scenery_tag_id", textFilterIds);
        return baseMapper.selectList(qw);
    }
}
