package com.insta360.store.business.integration.fedex.constant;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description Fedex公用常量类
 * @Date 2023/11/15
 */
public class FedexCommonConstant {

    /**
     * 标签响应选项
     */
    public static final String LABEL_RESPONSE_OPTIONS = "URL_ONLY";

    /**
     * 托运人名称
     */
    public static final String SHIPPER_NAME = "Jianchao Li";

    /**
     * 托运人手机号码
     */
    public static final String SHIPPER_PHONE = "***********";

    /**
     * 托运人公司名称
     */
    public static final String shipper_COMPANY_NAME = "Shenzhen Shunshi ELEC CO.,LTD";

    /**
     * 托运人地址
     */
    public static final List<String> SHIPPER_STREET_LINES = Lists.newArrayList("1st Floor, Building 1, ","No. 168 Xinsha 2nd Road, ","High-tech Zone");

    /**
     * 托运人地址城市
     */
    public static final String SHIPPER_CITY = "Zhuhai, Guangdong";


    public static final String SHIPPER_POSTAL_CODE = "519000";

    /**
     * Fedex API Token Key
     */
    public static final String FEDEX_API_TOKEN_KEY = "FEDEX_API_TOKEN_KEY";

    /**
     * IOSS国家代码
     */
    public static final List<String> IOSS_COUNTRY_CODE = Lists.newArrayList("AT","BE","BG","HR","CY","CZ","DK","EE","FI","FR","DE","GR","HU","IE","IT","LV","LT","LU","MT","NL","PL","PT","RO","SK","SI","ES","SE");

    /**
     * 加拿大省份代码
     */
    public static final List<String> CA_PROVINCE_CODE = Lists.newArrayList("AB","BC","MB","NB","NF","NT","NS","NU","ON","PE","PQ","QC","SK","YT");

    /**
     * 美国州代码
     */
    public static final List<String> US_STATE_CODE = Lists.newArrayList("AL","AK","AZ","AR","CA","CO","CT","DE","DC","FL","GA","HI","ID","IL","IN","MT","NE","NV","NH","NJ","NM","NY","NC","ND","OH","OK","OR","PA","RI","SC","IA","KS","KY","LA","ME","MD","MA","MI","MN","MS","MO","SD","TN","TX","UT","VT","VA","WA","WV","WI","WY","PR");

    /**
     * 签名服务费国家地区代码
     */
    public static final List<InstaCountry> SERVICE_CHARGE_LIST = Lists.newArrayList(InstaCountry.US, InstaCountry.FR);

    /**
     * 取货类型
     */
    public static final String PICKUP_TYPE = "CONTACT_FEDEX_TO_SCHEDULE";

    /**
     * 托运人TIN号
     */
    public static final String SHIPPER_TINS_NUMBER = "IM3720014240";

    /**
     * 托运人TIN类型
     */
    public static final String SHIPPER_TINS_TYPE = "BUSINESS_UNION";

    /**
     * 付款方式
     */
    public static final String CHARGES_PAYMENT_TYPE = "SENDER";

    /**
     * 特殊服务类型
     */
    public static final List<String> SPECIAL_SERVICE_TYPES = Lists.newArrayList("ELECTRONIC_TRADE_DOCUMENTS");

    /**
     * 运输文档类型
     */
    public static final List<String> SHIPPING_DOCUMENT_TYPES = Lists.newArrayList("COMMERCIAL_INVOICE");

    /**
     * 文档类型
     */
    public static final String DOC_TYPE = "PDF";

    /**
     * 库存类型
     */
    public static final String STOCK_TYPE = "PAPER_LETTER";

    /**
     * 图片类型
     */
    public static final String IMAGE_TYPE = "PDF";

    /**
     * 标签类型
     */
    public static final String LABEL_STOCK_TYPE = "STOCK_4X6";

    /**
     * 文档内容
     */
    public static final String DOCUMENT_CONTENT = "COMMODITY";

    /**
     * 数量单位
     */
    public static final String QUANTITY_UNITS = "PCS";

    /**
     * 重量单位
     */
    public static final String WEIGHT_UNITS = "KG";

    /**
     * 销售条款
     */
    public static final String TERMS_OF_SALE = "CIF";

    /**
     * 客户参考类型
     */
    public static final String CUSTOMER_REFERENCE_TYPE = "CUSTOMER_REFERENCE";

    /**
     * 税费类型
     */
    public static final String TAXES_OR_MISCELLANEOUS_CHARGE_TYPE = "TAXES";

    /**
     * 声明
     */
    public static final String DECLARATION_STATEMENT = "The manufacturer is same as shipper.";

    /**
     * 包装特殊服务
     */
    public static final List<String> PACKAGE_SPECIAL_SERVICE_TYPES = Lists.newArrayList("SIGNATURE_OPTION");

    /**
     * 签名选项
     */
    public static final String SIGNATURE_OPTION_TYPE = "ADULT";

    /**
     * 签名费用限制
     */
    public static final BigDecimal SIGNATURE_FEE_LIMIT = new BigDecimal("400");

    /**
     * 聚合类型
     */
    public static final String aggregationType = "PER_PACKAGE";

}
