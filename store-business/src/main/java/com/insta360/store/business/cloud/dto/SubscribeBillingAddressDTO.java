package com.insta360.store.business.cloud.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.cloud.model.SubscribeBillingAddress;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2024/08/01
 * @Description:
 */
public class SubscribeBillingAddressDTO implements Serializable {

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 名
     */
    @NotNull(message = "名称不允许为空")
    @NotBlank(message = "名称不允许为空")
    private String lastName;

    /**
     * 姓
     */
    @NotNull(message = "姓不允许为空")
    @NotBlank(message = "姓不允许为空")
    private String firstName;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    @NotNull(message = "城市不允许为空")
    @NotBlank(message = "城市不允许为空")
    private String city;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    @NotNull(message = "地址不允许为空")
    @NotBlank(message = "地址不允许为空")
    private String address;

    /**
     * 子地址
     */
    private String subAddress;

    /**
     * 电话地区码
     */
    @NotNull(message = "名称不允许为空")
    @NotBlank(message = "名称不允许为空")
    private String phoneCode;

    /**
     * 电话号码
     */
    @NotNull(message = "名称不允许为空")
    @NotBlank(message = "名称不允许为空")
    private String phone;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 抬头
     */
    private String taxTitle;

    /**
     * oceanCode
     */
    private String oceanCode;

    public SubscribeBillingAddress buildPojoObject() {
        SubscribeBillingAddress billingAddress = new SubscribeBillingAddress();
        BeanUtil.copyProperties(this, billingAddress);
        billingAddress.setCreateTime(LocalDateTime.now());
        billingAddress.setUpdateTime(LocalDateTime.now());
        return billingAddress;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getTaxTitle() {
        return taxTitle;
    }

    public void setTaxTitle(String taxTitle) {
        this.taxTitle = taxTitle;
    }

    public String getOceanCode() {
        return oceanCode;
    }

    public void setOceanCode(String oceanCode) {
        this.oceanCode = oceanCode;
    }

    @Override
    public String toString() {
        return "SubscribeBillingAddressDTO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", lastName='" + lastName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", phone='" + phone + '\'' +
                ", taxNumber='" + taxNumber + '\'' +
                ", taxTitle='" + taxTitle + '\'' +
                ", oceanCode='" + oceanCode + '\'' +
                '}';
    }
}
