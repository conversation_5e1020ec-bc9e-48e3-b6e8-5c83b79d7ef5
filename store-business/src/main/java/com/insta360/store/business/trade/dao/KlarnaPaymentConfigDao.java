package com.insta360.store.business.trade.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.trade.model.KlarnaPaymentConfig;
import org.apache.ibatis.annotations.CacheNamespace;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-04-10
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface KlarnaPaymentConfigDao extends BaseDao<KlarnaPaymentConfig> {

}
