package com.insta360.store.business.outgoing.rpc.store.job.fallback;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.outgoing.rpc.store.job.SeoCachePutService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/11/15
 */
@Component
public class SeoCachePutFallBack implements SeoCachePutService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SeoCachePutFallBack.class);

    @Override
    public void listSeoConfig(InstaLanguage language) {
        LOGGER.error(String.format("store-service调用失败。路径：/rpc/store/service/cacheput/meta/seo/listSeoConfig。" +
                "language:{%s}", language));
    }
}
