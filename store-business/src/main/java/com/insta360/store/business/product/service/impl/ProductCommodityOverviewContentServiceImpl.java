package com.insta360.store.business.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.product.dao.ProductCommodityOverviewContentDao;
import com.insta360.store.business.product.model.ProductCommodityOverviewContent;
import com.insta360.store.business.product.service.ProductCommodityOverviewContentService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-09-21
 * @Description:
 */
@Service
public class ProductCommodityOverviewContentServiceImpl extends BaseServiceImpl<ProductCommodityOverviewContentDao, ProductCommodityOverviewContent> implements ProductCommodityOverviewContentService {

    @Override
    public List<ProductCommodityOverviewContent> listByOverviewIdsData(List<Integer> overviewIds) {
        if (CollectionUtils.isEmpty(overviewIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.eq("overview_id", overviewIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<ProductCommodityOverviewContent> listByTemplateAndLanguage(Integer overviewId, String language) {
        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.eq("language", language);
        qw.eq("overview_id", overviewId);
        return baseMapper.selectList(qw);
    }

    @Override
    public void deleteByLanguage(Integer overviewId, String language) {
        if (Objects.isNull(overviewId) || Objects.isNull(language)) {
            return;
        }
        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.eq("overview_id", overviewId);
        qw.eq("language", language);
        baseMapper.delete(qw);
    }

    @Override
    public void deleteByOverviewIdList(List<Integer> overviewIdList) {
        if (Objects.isNull(overviewIdList)) {
            return;
        }
        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.in("overview_id", overviewIdList);
        baseMapper.delete(qw);
    }

    @Override
    public void saveContentBatch(List<ProductCommodityOverviewContent> productCommodityOverviewContents) {
        if (CollectionUtils.isNotEmpty(productCommodityOverviewContents)) {
            baseMapper.saveContentBatch(productCommodityOverviewContents);
        }
    }

    @Override
    public void deleteByOverviewIdAndParameterId(ProductCommodityOverviewContent content) {
        Integer overviewId = content.getOverviewId();
        Integer overviewParameterId = content.getOverviewParameterId();
        if (Objects.isNull(overviewId) && Objects.isNull(overviewParameterId)) {
            return;
        }
        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.eq("overview_id", overviewId);
        qw.eq("overview_parameter_id", overviewParameterId);
        baseMapper.delete(qw);
    }

    @Override
    public List<ProductCommodityOverviewContent> listByOverviewId(Integer overviewId) {
        if (Objects.isNull(overviewId)) {
            return null;
        }
        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.eq("overview_id", overviewId);
        return baseMapper.selectList(qw);
    }

    @Override
    public void updateContentBatch(List<ProductCommodityOverviewContent> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }

        baseMapper.updateContentBatch(updateList);
    }

    @Override
    public List<ProductCommodityOverviewContent> listByTemplateListAndLanguage(List<Integer> overviewIds, String language) {
        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.eq("language", language);
        qw.in("overview_id", overviewIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public Map<String, Map<Integer, List<ProductCommodityOverviewContent>>> listByOverviewIds(List<Integer> overviewIds) {
        if (CollectionUtils.isEmpty(overviewIds)) {
            return null;
        }

        QueryWrapper<ProductCommodityOverviewContent> qw = new QueryWrapper<>();
        qw.in("overview_id", overviewIds);
        List<ProductCommodityOverviewContent> productCommodityOverviewContents = baseMapper.selectList(qw);
        return productCommodityOverviewContents.stream()
                .collect(Collectors.groupingBy(ProductCommodityOverviewContent::getLanguage,
                        Collectors.groupingBy(ProductCommodityOverviewContent::getOverviewId,
                                Collectors.mapping(o -> o, Collectors.toList()))));
    }
}
