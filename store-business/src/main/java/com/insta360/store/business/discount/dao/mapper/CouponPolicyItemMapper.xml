<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.discount.dao.CouponPolicyItemDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.discount.dao.CouponPolicyItemDao"/>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO coupon_policy_item (coupon_code, policy_id, product_commodity_id, policy_item_type)
        VALUES
        <foreach collection="policyItemList" item="policyItem" separator=",">
            (
                #{policyItem.couponCode},
                #{policyItem.policyId},
                #{policyItem.productCommodityId},
                #{policyItem.policyItemType}
            )
        </foreach>
    </insert>
</mapper>