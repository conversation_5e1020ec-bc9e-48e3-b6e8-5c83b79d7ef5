package com.insta360.store.business.payment.lib.paypal.module;

/**
 * @Author: wbt
 * @Date: 2024/04/10
 * @Description:
 */
public class PayPalSellerReceivableBreakdown {

    private PayPalAmount gross_amount;

    private PayPalAmount paypal_fee;

    private PayPalAmount net_amount;

    public PayPalAmount getGross_amount() {
        return gross_amount;
    }

    public void setGross_amount(PayPalAmount gross_amount) {
        this.gross_amount = gross_amount;
    }

    public PayPalAmount getPaypal_fee() {
        return paypal_fee;
    }

    public void setPaypal_fee(PayPalAmount paypal_fee) {
        this.paypal_fee = paypal_fee;
    }

    public PayPalAmount getNet_amount() {
        return net_amount;
    }

    public void setNet_amount(PayPalAmount net_amount) {
        this.net_amount = net_amount;
    }

    @Override
    public String toString() {
        return "PayPalSellerReceivableBreakdown{" +
                "gross_amount=" + gross_amount +
                ", paypal_fee=" + paypal_fee +
                ", net_amount=" + net_amount +
                '}';
    }
}
