package com.insta360.store.business.commodity.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.commodity.dto.CommodityPlatformCodeBindDTO;
import com.insta360.store.business.commodity.model.CommodityPlatformCodeBind;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2022-04-11
 * @Description:
 */
public interface CommodityPlatformCodeBindService extends BaseService<CommodityPlatformCodeBind> {

    /**
     * 创建套餐料号绑定关系
     *
     * @param platformCodeBindParam
     */
    void createPlatformCodeBind(CommodityPlatformCodeBindDTO platformCodeBindParam);

    /**
     * 根据料号查询绑定关系
     *
     * @param skuCodes
     */
    List<CommodityPlatformCodeBind> listBySkuCodes(List<String> skuCodes);

    /**
     * 根据料号查询绑定关系
     *
     * @param skuCode
     */
    CommodityPlatformCodeBind getBySkuCode(String skuCode);

    /**
     * 根据套餐id查询
     *
     * @param commodityId
     * @return
     */
    List<CommodityPlatformCodeBind> listByCommodityId(Integer commodityId);

    /**
     * 根据套餐id删除绑定关系
     *
     * @param commodityId
     */
    void deleteByCommodityId(Integer commodityId);

    /**
     * 更新套餐料号绑定关系
     *
     * @param platformCodeBindParam
     */
    void updateByCommodityId(CommodityPlatformCodeBindDTO platformCodeBindParam);
}
