package com.insta360.store.business.payment.lib.checkout.enums;

/**
 * @Author: wbt
 * @Date: 2021/11/15
 * @Description:
 */
public enum CkoPaymentRequestEnum {

    /************************ 对应paymentRequest中的payment_type字段 ************************/

    /**
     * default
     */
    REGULAR("Regular", "默认值"),

    /**
     * 重复支付（选择该项时，previous_payment_id必填。为上一次支付返回结果的id）
     */
    RECURRING("Recurring", "重复支付"),

    /**
     * 未知状态（没有用到
     */
    MOTO("MOTO", "未知状态（没有用到）");

    /**
     * 名称
     */
    private final String name;

    /**
     * 描述信息
     */
    private final String detail;

    CkoPaymentRequestEnum(String name, String detail) {
        this.name = name;
        this.detail = detail;
    }

    public String getName() {
        return name;
    }

    public String getDetail() {
        return detail;
    }
}
