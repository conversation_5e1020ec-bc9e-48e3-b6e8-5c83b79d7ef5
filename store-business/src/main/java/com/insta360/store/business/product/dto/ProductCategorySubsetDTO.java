package com.insta360.store.business.product.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.product.model.ProductCategorySubset;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2023/8/31
 * @Description:
 */
public class ProductCategorySubsetDTO implements Serializable {

    private Integer id;

    /**
     * 一级类目key
     */
    @NotNull
    @NotBlank(message = "一级类目key不允许为空")
    private String categoryMainKey;

    /**
     * 二级类目内部名称
     */
    @NotNull
    @NotBlank(message = "二级类目内部名称不允许为空")
    private String categorySubsetName;

    /**
     * 二级类目key
     */
    @NotNull
    @NotBlank(message = "二级类目key不允许为空")
    private String categorySubsetKey;

    /**
     * 二级排序index
     */
    private Integer orderIndex;

    public ProductCategorySubset getPojoObject() {
        ProductCategorySubset categorySubset = new ProductCategorySubset();
        BeanUtil.copyProperties(this, categorySubset);
        categorySubset.setCreateTime(LocalDateTime.now());
        categorySubset.setUpdateTime(LocalDateTime.now());
        return categorySubset;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCategoryMainKey() {
        return categoryMainKey;
    }

    public void setCategoryMainKey(String categoryMainKey) {
        this.categoryMainKey = categoryMainKey;
    }

    public String getCategorySubsetName() {
        return categorySubsetName;
    }

    public void setCategorySubsetName(String categorySubsetName) {
        this.categorySubsetName = categorySubsetName;
    }

    public String getCategorySubsetKey() {
        return categorySubsetKey;
    }

    public void setCategorySubsetKey(String categorySubsetKey) {
        this.categorySubsetKey = categorySubsetKey;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    @Override
    public String toString() {
        return "ProductCategorySubsetDTO{" +
                "id=" + id +
                ", categoryMainKey='" + categoryMainKey + '\'' +
                ", categorySubsetName='" + categorySubsetName + '\'' +
                ", categorySubsetKey='" + categorySubsetKey + '\'' +
                ", orderIndex=" + orderIndex +
                '}';
    }
}
