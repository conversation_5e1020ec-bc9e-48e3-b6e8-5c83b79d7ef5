package com.insta360.store.business.integration.wto.oms.lib.module.order;

import com.insta360.store.business.integration.wto.oms.lib.module.ReceiverInfo;

/**
 * @Author: wkx
 * @Date: 2024/12/03
 * @Description:
 */
public class DeliveryOrder {

    /**
     * 买家备注
     */
    private String buyerMessage;

    /**
     * 会员昵称
     */
    private String buyerNick;

    /**
     * 交易号
     */
    private String deliveryOrderCode;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 订单支付金额
     */
    private String gotAmount;

    /**
     * 订单类型（传固定值：JYCK）
     */
    private String orderType;

    /**
     * 操作时间（传订单创建时间）
     */
    private String operateTime;

    /**
     * 传订单创建时间
     */
    private String placeOrderTime;

    /**
     * 传当前时间
     */
    private String modifiedTime;

    /**
     * OMS店铺编码
     */
    private String shopCode;

    /**
     * 订单实付金额
     */
    private String totalAmount;

    /**
     * 订单状态（固定值：WAIT_SELLER_SEND_GOODS）
     */
    private String status;

    /**
     * 交易号
     */
    private String oaidOrderSourceCode;

    /**
     * 运费
     */
    private String freight;

    /**
     * 卖家备注
     */
    private String sellerMessage;

    /**
     * 是否加急
     */
    private Boolean isUrgency;

    /**
     * 收件人信息
     */
    private ReceiverInfo receiverInfo;

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getDeliveryOrderCode() {
        return deliveryOrderCode;
    }

    public void setDeliveryOrderCode(String deliveryOrderCode) {
        this.deliveryOrderCode = deliveryOrderCode;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getGotAmount() {
        return gotAmount;
    }

    public void setGotAmount(String gotAmount) {
        this.gotAmount = gotAmount;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getPlaceOrderTime() {
        return placeOrderTime;
    }

    public void setPlaceOrderTime(String placeOrderTime) {
        this.placeOrderTime = placeOrderTime;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOaidOrderSourceCode() {
        return oaidOrderSourceCode;
    }

    public void setOaidOrderSourceCode(String oaidOrderSourceCode) {
        this.oaidOrderSourceCode = oaidOrderSourceCode;
    }

    public String getFreight() {
        return freight;
    }

    public void setFreight(String freight) {
        this.freight = freight;
    }

    public String getSellerMessage() {
        return sellerMessage;
    }

    public void setSellerMessage(String sellerMessage) {
        this.sellerMessage = sellerMessage;
    }

    public Boolean getIsUrgency() {
        return isUrgency;
    }

    public void setIsUrgency(Boolean urgency) {
        isUrgency = urgency;
    }

    public ReceiverInfo getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(ReceiverInfo receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public String getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(String modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    @Override
    public String toString() {
        return "DeliveryOrder{" +
                "buyerMessage='" + buyerMessage + '\'' +
                ", buyerNick='" + buyerNick + '\'' +
                ", deliveryOrderCode='" + deliveryOrderCode + '\'' +
                ", createTime='" + createTime + '\'' +
                ", gotAmount='" + gotAmount + '\'' +
                ", orderType='" + orderType + '\'' +
                ", operateTime='" + operateTime + '\'' +
                ", placeOrderTime='" + placeOrderTime + '\'' +
                ", modifiedTime='" + modifiedTime + '\'' +
                ", shopCode='" + shopCode + '\'' +
                ", totalAmount='" + totalAmount + '\'' +
                ", status='" + status + '\'' +
                ", oaidOrderSourceCode='" + oaidOrderSourceCode + '\'' +
                ", freight='" + freight + '\'' +
                ", sellerMessage='" + sellerMessage + '\'' +
                ", isUrgency=" + isUrgency +
                ", receiverInfo=" + receiverInfo +
                '}';
    }
}
