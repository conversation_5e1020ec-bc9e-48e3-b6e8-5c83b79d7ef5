package com.insta360.store.business.integration.guanyi.lib.model;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description:
 */
public class GyOrder {
    String code;    //订单编号
    String order_type_name;    //订单类型
    String platform_code;    //平台单号
    String createtime;    //制单时间
    String dealtime;    //拍单时间
    String paytime;        //支付时间
    Boolean cod;    //是否为货到付款
    Boolean approve;    //是否已审核
    Integer delivery_state;    //发货状态
    String warehouse_code;    //仓库代码
    String warehouse_name;    //仓库名称
    String shop_code;    //店铺代码
    String shop_name;    //店铺名称
    String express_code;    //物流公司代码
    String express_name;    //物流公司名称
    String express_num;     //运单号
    String buyer_memo;    //买家留言
    String seller_memo;    //卖家备注
    String seller_memo_late;    //二次备注
    String extend_memo;//附加信息 (管易场景 为州代码)
    String vip_code;    //会员代码
    String vip_name;    //会员名称
    String receiver_name;    //收件人姓名
    String receiver_phone;    //收件人电话
    String receiver_mobile;    //收件人手机
    String receiver_zip;    //收件邮编
    String receiver_area;    //收件区域信息
    String receiver_address;        //收件详细地址
    String payCode;    //支付流水号
    String vipIdCard;    //身份证号
    String vipRealName;    //真实姓名
    String vipEmail;    //电子邮箱
    String amount;//订单金额
    String payment_amount;        //货款金额
    String post_fee;    //物流费用
    String cod_fee;    //到付服务费
    String discount_fee;    //让利金额
    String payment;    //实付金额
    Integer qty;    //商品数量
    Integer weight_origin;    //标准重量
    String post_cost;        //物流成本
    Integer platform_flag;    //平台旗帜
    String business_man;//业务员
    String currency_code;//币别代码
    String note;    // 备注

    String trade_platform_code;  // 销售订单平台单号
    String return_type;         // 售后类型
    String type_code;            // 退货原因代码note
    String warehousein_code;     // 退回仓库代码
    List<GyReturnOrderItem> item_detail;   // 退入商品明细
    List<GyReturnOrderRefund> refund_detail; // 退款明细

    List<GyOrderDetail> details;
    List<GyOrderPayment> payments;
    List<GyOrderInvoice> invoices;
    List<GyOrderDelivery> deliverys;

    String receiver_province;//	收货人省份
    String receiver_city;//收货人城市
    String receiver_district;//	收货人区域

    // 内部属性
    Boolean tradeup_order;

    // 标记code
    String tag_code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrder_type_name() {
        return order_type_name;
    }

    public void setOrder_type_name(String order_type_name) {
        this.order_type_name = order_type_name;
    }

    public String getPlatform_code() {
        return platform_code;
    }

    public String getCurrency_code() {
        return currency_code;
    }

    public void setCurrency_code(String currency_code) {
        this.currency_code = currency_code;
    }

    public void setPlatform_code(String platform_code) {
        this.platform_code = platform_code;
    }

    public String getCreatetime() {
        return createtime;
    }

    public void setCreatetime(String createtime) {
        this.createtime = createtime;
    }

    public String getDealtime() {
        return dealtime;
    }

    public void setDealtime(String dealtime) {
        this.dealtime = dealtime;
    }

    public String getPaytime() {
        return paytime;
    }

    public void setPaytime(String paytime) {
        this.paytime = paytime;
    }

    public Boolean getCod() {
        return cod;
    }

    public void setCod(Boolean cod) {
        this.cod = cod;
    }

    public Boolean getApprove() {
        return approve;
    }

    public void setApprove(Boolean approve) {
        this.approve = approve;
    }

    public Integer getDelivery_state() {
        return delivery_state;
    }

    public void setDelivery_state(Integer delivery_state) {
        this.delivery_state = delivery_state;
    }

    public String getWarehouse_code() {
        return warehouse_code;
    }

    public void setWarehouse_code(String warehouse_code) {
        this.warehouse_code = warehouse_code;
    }

    public String getWarehouse_name() {
        return warehouse_name;
    }

    public void setWarehouse_name(String warehouse_name) {
        this.warehouse_name = warehouse_name;
    }

    public String getShop_code() {
        return shop_code;
    }

    public void setShop_code(String shop_code) {
        this.shop_code = shop_code;
    }

    public String getShop_name() {
        return shop_name;
    }

    public void setShop_name(String shop_name) {
        this.shop_name = shop_name;
    }

    public String getExpress_code() {
        return express_code;
    }

    public void setExpress_code(String express_code) {
        this.express_code = express_code;
    }

    public String getExpress_name() {
        return express_name;
    }

    public void setExpress_name(String express_name) {
        this.express_name = express_name;
    }

    public String getExpress_num() {
        return express_num;
    }

    public void setExpress_num(String express_num) {
        this.express_num = express_num;
    }

    public String getBuyer_memo() {
        return buyer_memo;
    }

    public void setBuyer_memo(String buyer_memo) {
        this.buyer_memo = buyer_memo;
    }

    public String getSeller_memo() {
        return seller_memo;
    }

    public void setSeller_memo(String seller_memo) {
        this.seller_memo = seller_memo;
    }

    public String getSeller_memo_late() {
        return seller_memo_late;
    }

    public void setSeller_memo_late(String seller_memo_late) {
        this.seller_memo_late = seller_memo_late;
    }

    public String getExtend_memo() {
        return extend_memo;
    }

    public void setExtend_memo(String extend_memo) {
        this.extend_memo = extend_memo;
    }

    public String getVip_code() {
        return vip_code;
    }

    public void setVip_code(String vip_code) {
        this.vip_code = vip_code;
    }

    public String getVip_name() {
        return vip_name;
    }

    public void setVip_name(String vip_name) {
        this.vip_name = vip_name;
    }

    public String getReceiver_name() {
        return receiver_name;
    }

    public void setReceiver_name(String receiver_name) {
        this.receiver_name = receiver_name;
    }

    public String getReceiver_phone() {
        return receiver_phone;
    }

    public void setReceiver_phone(String receiver_phone) {
        this.receiver_phone = receiver_phone;
    }

    public String getReceiver_mobile() {
        return receiver_mobile;
    }

    public void setReceiver_mobile(String receiver_mobile) {
        this.receiver_mobile = receiver_mobile;
    }

    public String getReceiver_zip() {
        return receiver_zip;
    }

    public void setReceiver_zip(String receiver_zip) {
        this.receiver_zip = receiver_zip;
    }

    public String getReceiver_area() {
        return receiver_area;
    }

    public void setReceiver_area(String receiver_area) {
        this.receiver_area = receiver_area;
    }

    public String getReceiver_address() {
        return receiver_address;
    }

    public void setReceiver_address(String receiver_address) {
        this.receiver_address = receiver_address;
    }

    public String getPayCode() {
        return payCode;
    }

    public void setPayCode(String payCode) {
        this.payCode = payCode;
    }

    public String getVipIdCard() {
        return vipIdCard;
    }

    public void setVipIdCard(String vipIdCard) {
        this.vipIdCard = vipIdCard;
    }

    public String getVipRealName() {
        return vipRealName;
    }

    public void setVipRealName(String vipRealName) {
        this.vipRealName = vipRealName;
    }

    public String getVipEmail() {
        return vipEmail;
    }

    public void setVipEmail(String vipEmail) {
        this.vipEmail = vipEmail;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getPayment_amount() {
        return payment_amount;
    }

    public void setPayment_amount(String payment_amount) {
        this.payment_amount = payment_amount;
    }

    public String getPost_fee() {
        return post_fee;
    }

    public void setPost_fee(String post_fee) {
        this.post_fee = post_fee;
    }

    public String getCod_fee() {
        return cod_fee;
    }

    public void setCod_fee(String cod_fee) {
        this.cod_fee = cod_fee;
    }

    public String getDiscount_fee() {
        return discount_fee;
    }

    public void setDiscount_fee(String discount_fee) {
        this.discount_fee = discount_fee;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public Integer getWeight_origin() {
        return weight_origin;
    }

    public void setWeight_origin(Integer weight_origin) {
        this.weight_origin = weight_origin;
    }

    public String getPost_cost() {
        return post_cost;
    }

    public void setPost_cost(String post_cost) {
        this.post_cost = post_cost;
    }

    public Integer getPlatform_flag() {
        return platform_flag;
    }

    public void setPlatform_flag(Integer platform_flag) {
        this.platform_flag = platform_flag;
    }

    public String getBusiness_man() {
        return business_man;
    }

    public void setBusiness_man(String business_man) {
        this.business_man = business_man;
    }

    public List<GyOrderDetail> getDetails() {
        return details;
    }

    public void setDetails(List<GyOrderDetail> details) {
        this.details = details;
    }

    public List<GyOrderPayment> getPayments() {
        return payments;
    }

    public void setPayments(List<GyOrderPayment> payments) {
        this.payments = payments;
    }

    public List<GyOrderInvoice> getInvoices() {
        return invoices;
    }

    public void setInvoices(List<GyOrderInvoice> invoices) {
        this.invoices = invoices;
    }

    public List<GyOrderDelivery> getDeliverys() {
        return deliverys;
    }

    public void setDeliverys(List<GyOrderDelivery> deliverys) {
        this.deliverys = deliverys;
    }

    public String getReceiver_province() {
        return receiver_province;
    }

    public void setReceiver_province(String receiver_province) {
        this.receiver_province = receiver_province;
    }

    public String getReceiver_city() {
        return receiver_city;
    }

    public void setReceiver_city(String receiver_city) {
        this.receiver_city = receiver_city;
    }

    public String getReceiver_district() {
        return receiver_district;
    }

    public void setReceiver_district(String receiver_district) {
        this.receiver_district = receiver_district;
    }

    public Boolean getTradeup_order() {
        return tradeup_order;
    }

    public void setTradeup_order(Boolean tradeup_order) {
        this.tradeup_order = tradeup_order;
    }

    public String getTrade_platform_code() {
        return trade_platform_code;
    }

    public void setTrade_platform_code(String trade_platform_code) {
        this.trade_platform_code = trade_platform_code;
    }

    public String getReturn_type() {
        return return_type;
    }

    public void setReturn_type(String return_type) {
        this.return_type = return_type;
    }

    public String getType_code() {
        return type_code;
    }

    public void setType_code(String type_code) {
        this.type_code = type_code;
    }

    public String getWarehousein_code() {
        return warehousein_code;
    }

    public void setWarehousein_code(String warehousein_code) {
        this.warehousein_code = warehousein_code;
    }

    public List<GyReturnOrderItem> getItem_detail() {
        return item_detail;
    }

    public void setItem_detail(List<GyReturnOrderItem> item_detail) {
        this.item_detail = item_detail;
    }

    public List<GyReturnOrderRefund> getRefund_detail() {
        return refund_detail;
    }

    public void setRefund_detail(List<GyReturnOrderRefund> refund_detail) {
        this.refund_detail = refund_detail;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getTag_code() {
        return tag_code;
    }

    public void setTag_code(String tag_code) {
        this.tag_code = tag_code;
    }
}
