package com.insta360.store.business.product.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.product.model.ProductDeliveryTimeText;
import org.apache.ibatis.annotations.CacheNamespace;
import org.springframework.stereotype.Repository;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
@Repository
@CacheNamespace(implementation= MybatisRedisCache.class,eviction= MybatisRedisCache.class)
public interface ProductDeliveryTimeTextDao extends BaseDao<ProductDeliveryTimeText> {

}
