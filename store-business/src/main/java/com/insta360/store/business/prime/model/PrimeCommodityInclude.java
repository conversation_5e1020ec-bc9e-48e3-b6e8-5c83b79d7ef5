package com.insta360.store.business.prime.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-06-04
 * @Description: Prime子商品表
 */
public class PrimeCommodityInclude extends BaseModel<PrimeCommodityInclude> {

    private static final long serialVersionUID = 1L;

    /**
     * 键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * product_prime_commodity#id
     */
    private Long primeCommodityId;

    /**
     * 套餐ID
     */
    private Integer commodityId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPrimeCommodityId() {
        return primeCommodityId;
    }

    public void setPrimeCommodityId(Long primeCommodityId) {
        this.primeCommodityId = primeCommodityId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PrimeCommodityInclude{" +
        "id=" + id +
        ", primeCommodityId=" + primeCommodityId +
        ", commodityId=" + commodityId +
        ", quantity=" + quantity +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}