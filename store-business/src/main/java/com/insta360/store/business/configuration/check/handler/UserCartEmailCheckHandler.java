package com.insta360.store.business.configuration.check.handler;

import com.insta360.store.business.configuration.check.bo.CheckResultBO;
import com.insta360.store.business.configuration.check.chain.BaseUserCartCheckChain;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 购物车邮件检查处理器
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@Component
public class UserCartEmailCheckHandler extends BaseUserCartCheckHandler {

    public static final Logger LOGGER = LoggerFactory.getLogger(UserCartEmailCheckHandler.class);

    /**
     * 订单检查链条集合
     */
    @Autowired
    List<BaseUserCartCheckChain> userCartCheckChains;

    @Override
    protected List<CheckResultBO> doDoubleCheck(DoubleCheckBO doubleCheckBo) {
        List<CheckResultBO> checkResults = new ArrayList<>();
        List<BaseUserCartCheckChain> userCartCheckChains = this.userCartCheckChains.stream().sorted(Comparator.comparingInt(BaseUserCartCheckChain::orderIndex)).collect(Collectors.toList());
        for (BaseUserCartCheckChain userCartCheckChain : userCartCheckChains) {
            Boolean pass = userCartCheckChain.doCheck(doubleCheckBo);
            CheckResultBO checkResultBO = new CheckResultBO(userCartCheckChain.getName(), pass);
            LOGGER.info("[发送购物车邮件规则] uuid:{} name:{} pass:{}", doubleCheckBo.getUuid(), userCartCheckChain.getName(), pass);
            checkResults.add(checkResultBO);

            // 如果校验失败，则不再继续校验
            if (!pass) {
                return checkResults;
            }
        }
        return checkResults;
    }
}
