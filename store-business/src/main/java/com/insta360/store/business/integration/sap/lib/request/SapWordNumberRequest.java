package com.insta360.store.business.integration.sap.lib.request;

import com.insta360.store.business.integration.sap.configuration.SapCommonConfiguration;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: py
 * @create: 2025-03-05 10:55
 */
public class SapWordNumberRequest extends BaseSapRequest {

    private static final String METHOD = "/http/prd/getWorkOrder";

    /**
     * 工单号
     */
    private String worknumber;

    /**
     * token
     */
    private String token;

    public SapWordNumberRequest(SapCommonConfiguration sapCommonConfiguration) {
        super(METHOD, sapCommonConfiguration.getWorkApiUrl());
    }

    @Override
    protected String getAuthorization() {
        return "Bearer " + this.getToken();
    }

    @Override
    protected Map<String, Object> getHeaderMap() {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("Authorization", this.getAuthorization());
        return headerMap;
    }

    public String getWorknumber() {
        return worknumber;
    }

    public void setWorknumber(String worknumber) {
        this.worknumber = worknumber;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return "SapWordNumberRequest{" +
                "worknumber='" + worknumber + '\'' +
                ", token='" + token + '\'' +
                '}';
    }
}
