package com.insta360.store.business.payment.lib.paypal.request.api;

/**
 * @Author: wbt
 * @Date: 2024/04/10
 * @Description: show payment detail
 * 接口文档： https://developer.paypal.com/docs/api/payments/v2/#captures_get
 */
public class CreatePayPalGetPaymentDetailRequest extends BasePayPalRequest {

    private static final String METHOD = "/v2/payments/captures/%s";

    public CreatePayPalGetPaymentDetailRequest(String paymentId) {
        super(String.format(METHOD, paymentId));
    }
}
