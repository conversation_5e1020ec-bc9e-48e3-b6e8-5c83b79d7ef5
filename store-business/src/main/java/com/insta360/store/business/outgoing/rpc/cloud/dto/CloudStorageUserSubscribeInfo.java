package com.insta360.store.business.outgoing.rpc.cloud.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 云存储用户订阅明细
 * @Date 2024/6/21
 */
public class CloudStorageUserSubscribeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 平台
     * @see com.insta360.store.business.cloud.enums.BenefitPlatform
     */
    private String platform;

    /**
     * 状态
     * @see com.insta360.store.business.cloud.enums.BenefitStatus
     */
    private String status;

    /**
     * SKU ID
     */
    private String skuId;

    /**
     * 区域
     */
    private String region;

    /**
     * 权益过期时间
     */
    private Long expirationTime;

    /**
     * 是否可用
     */
    private Integer enable;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Long expirationTime) {
        this.expirationTime = expirationTime;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    @Override
    public String toString() {
        return "CloudStorageUserSubscribeInfo{" +
                "userId=" + userId +
                ", platform='" + platform + '\'' +
                ", status='" + status + '\'' +
                ", skuId='" + skuId + '\'' +
                ", region='" + region + '\'' +
                ", expirationTime=" + expirationTime +
                ", enable=" + enable +
                '}';
    }
}
