package com.insta360.store.business.order.bo;

import com.insta360.store.business.admin.order.print.bo.OrderPrintInfo;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/5/25
 */
public class OrderPrintInfoBO implements Serializable {

    /**
     * 订单打印信息
     */
    private OrderPrintInfo info;

    /**
     * 发货日期
     */
    private String expressDate;


    public static OrderPrintInfoBO build(OrderPrintInfo info,String expressDate) {
        if(Objects.isNull(info) || StringUtils.isBlank(expressDate)) {
            return null;
        }
        OrderPrintInfoBO orderPrintInfoBO = new OrderPrintInfoBO();
        orderPrintInfoBO.setInfo(info);
        orderPrintInfoBO.setExpressDate(expressDate);
        return orderPrintInfoBO;
    }

    public OrderPrintInfo getInfo() {
        return info;
    }

    public void setInfo(OrderPrintInfo info) {
        this.info = info;
    }

    public String getExpressDate() {
        return expressDate;
    }

    public void setExpressDate(String expressDate) {
        this.expressDate = expressDate;
    }
}
