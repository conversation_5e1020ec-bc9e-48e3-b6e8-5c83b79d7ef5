package com.insta360.store.business.cloud.enums;

import com.google.common.collect.Lists;

/**
 * @Author: wkx
 * @Date: 2024/05/24
 * @Description:
 */
public enum SubscribeStatus {

    /**
     * 订阅未生效
     */
    NEVER_SUBSCRIBE(-1, "订阅未生效"),

    /**
     * 订阅中
     */
    SUBSCRIBE_ING(1, "订阅中"),

    /**
     * 已关闭续费
     */
    CLOSE_RENEW(2, "已关闭续费"),

    /**
     * 已过期
     */
    EXPIRED(3, "已过期"),

    /**
     * 预处理
     */
    PREPROCESSING(4,"预处理中");

    private final Integer code;

    private final String name;

    SubscribeStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 是否生效
     *
     * @param subscribeStatus
     * @return
     */
    public static boolean isEffect(SubscribeStatus subscribeStatus) {
        return Lists.newArrayList(SUBSCRIBE_ING, CLOSE_RENEW).contains(subscribeStatus);
    }

    public static SubscribeStatus parse(Integer code) {
        for (SubscribeStatus state : values()) {
            if (state.code.equals(code)) {
                return state;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
