package com.insta360.store.business.product.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.integration.google.bo.GoogleAdapterTypeBO;
import com.insta360.store.business.product.model.ProductAdapterType;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-09-13
 * @Description:
 */
public interface ProductAdapterTypeDao extends BaseDao<ProductAdapterType> {

    /**
     * 查询套餐适配机型
     *
     * @return
     */
    List<GoogleAdapterTypeBO> listAccessoryCompatibility();

    /**
     * 批量保存产品适配机型
     *
     * @param productAdapterTypes
     */
    void saveAdapterType(@Param("productAdapterTypes") List<ProductAdapterType> productAdapterTypes);

    /**
     * 批量更新适配机型顺序
     *
     * @param adapterTypes
     */
    void updateOrderIndexByIds(@Param("adapterTypes") ArrayList<ProductAdapterType> adapterTypes);
}
