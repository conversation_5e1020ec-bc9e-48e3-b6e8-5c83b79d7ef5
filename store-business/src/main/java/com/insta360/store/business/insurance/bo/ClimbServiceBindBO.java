package com.insta360.store.business.insurance.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/2/12 上午11:07
 */
public class ClimbServiceBindBO implements Serializable {

    /**
     * 增值服务套餐id
     */
    private Integer serviceCommodityId;

    /**
     * 增值服务套餐名称
     */
    private String serviceCommodityName;

    /**
     * 增值服务类型
     */
    private String serviceType;

    /**
     * 主机商品id
     */
    private Integer productId;

    /**
     * 主机商品名称
     */
    private String productName;

    /**
     * 主机商品套餐id
     */
    private Integer suitableCommodityId;

    /**
     * 主机商品套餐名称
     */
    private String suitableCommodityName;

    /**
     * 主机商品套餐状态
     */
    private Boolean suitableCommodityStatus;

    public Integer getServiceCommodityId() {
        return serviceCommodityId;
    }

    public void setServiceCommodityId(Integer serviceCommodityId) {
        this.serviceCommodityId = serviceCommodityId;
    }

    public String getServiceCommodityName() {
        return serviceCommodityName;
    }

    public void setServiceCommodityName(String serviceCommodityName) {
        this.serviceCommodityName = serviceCommodityName;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getSuitableCommodityId() {
        return suitableCommodityId;
    }

    public void setSuitableCommodityId(Integer suitableCommodityId) {
        this.suitableCommodityId = suitableCommodityId;
    }

    public String getSuitableCommodityName() {
        return suitableCommodityName;
    }

    public void setSuitableCommodityName(String suitableCommodityName) {
        this.suitableCommodityName = suitableCommodityName;
    }

    public Boolean getSuitableCommodityStatus() {
        return suitableCommodityStatus;
    }

    public void setSuitableCommodityStatus(Boolean suitableCommodityStatus) {
        this.suitableCommodityStatus = suitableCommodityStatus;
    }

    @Override
    public String toString() {
        return "ClimbServiceBindBO{" +
                "serviceCommodityId=" + serviceCommodityId +
                ", serviceCommodityName='" + serviceCommodityName + '\'' +
                ", serviceType='" + serviceType + '\'' +
                ", productId=" + productId +
                ", productName='" + productName + '\'' +
                ", suitableCommodityId=" + suitableCommodityId +
                ", suitableCommodityName='" + suitableCommodityName + '\'' +
                ", suitableCommodityStatus=" + suitableCommodityStatus +
                '}';
    }
}
