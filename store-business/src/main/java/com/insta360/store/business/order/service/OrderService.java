package com.insta360.store.business.order.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.admin.order.service.impl.creation.OrderCustomExportCreation;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.commodity.bo.CommodityStockNoticeBO;
import com.insta360.store.business.order.bo.OrderCreation;
import com.insta360.store.business.order.bo.RepeatOrderQueryBO;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.user.model.StoreAccount;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
public interface OrderService extends BaseService<Order> {

    /**
     * 根据时间范围获取一个订单
     *
     * @param email
     * @param startTime
     * @param endTime
     * @return
     */
    Order getByBetweenTime(String email, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取某个邮箱下的所有订单
     *
     * @param contactEmail
     * @return
     */
    List<Order> getByContactEmail(String contactEmail);

    /**
     * 获取游客订单
     *
     * @param contactEmail
     * @return
     */
    List<Order> listGuestOrderByContactEmail(String contactEmail);

    /**
     * 用户下单
     *
     * @param orderCreation
     * @return
     */
    Order createOrder(OrderCreation orderCreation);

    /**
     * 根据订单号获取订单
     *
     * @param orderNumber
     * @return
     */
    Order getByOrderNumber(String orderNumber);

    /**
     * 根据订单号批量获取订单
     *
     * @param orderNumbers
     * @return
     */
    List<Order> listByOrderNumber(List<String> orderNumbers);

    /**
     * 根据分销码获取订单
     *
     * @param promoCode
     * @return
     */
    List<Order> getByPromoCode(String promoCode);

    /**
     * 根据订单状态获取订单（已排除工单系统订单）
     *
     * @param orderStates
     * @return
     */
    List<Order> getByOrderState(List<OrderState> orderStates);

    /**
     * 根据订单状态获取订单
     *
     * @param orderStates
     * @return
     */
    List<Order> getByOrderState(String email, List<OrderState> orderStates);

    /**
     * 根据邮箱，订单状态获取一周内的订单
     *
     * @param email
     * @param orderStates
     * @param now
     * @return
     */
    List<Order> listByEmail(String email, List<OrderState> orderStates, LocalDateTime now);

    /**
     * 根据套餐查询订单信息
     *
     * @param commodityIds
     * @param orderStates
     * @param now
     * @return
     */
    List<CommodityStockNoticeBO> listByCommodityIds(List<Integer> commodityIds, List<OrderState> orderStates, LocalDateTime now);

    /**
     * 根据手机号/地址，订单状态获取一周内的订单
     *
     * @param repeatOrderQueryParams
     * @return
     */
    List<Order> listByPhoneOrAddress(RepeatOrderQueryBO repeatOrderQueryParams);

    /**
     * 根据订单状态获取订单（获取某一时间区间的订单）
     *
     * @param orderStates
     * @param now
     * @return
     */
    List<Order> getByOrderState(List<OrderState> orderStates, LocalDateTime now);

    /**
     * 获取用户订单 （不再返回工单信息）
     *
     * @param storeAccount
     * @param pageQuery
     * @return
     */
    PageResult<Order> getUserOrders(StoreAccount storeAccount, PageQuery pageQuery);

    /**
     * 取消订单
     *
     * @param orderId
     * @param account
     * @param reason
     */
    void cancelOrder(Integer orderId, StoreAccount account, String reason);

    /**
     * 确认收货
     *
     * @param orderId
     * @param account
     */
    void confirmDelivery(Integer orderId, StoreAccount account);

    /**
     * 订单项是否包含某产品
     *
     * @param orderId
     * @param productId
     * @return
     */
    boolean containProduct(Integer orderId, Integer productId);

    /**
     * 订单项是否包含某套餐
     *
     * @param orderId
     * @param commodityId
     * @return
     */
    boolean containsCommodity(Integer orderId, Integer commodityId);

    /**
     * 订单发货
     *
     * @param order
     */
    void shipOrder(Order order);

    /**
     * 部分发货
     *
     * @param order
     */
    void partlyOrder(Order order);

    /**
     * 按时间获取定制订单
     *
     * @param customExportCreation
     * @return
     */
    List<Order> listCustomOrders(OrderCustomExportCreation customExportCreation);

    /**
     * 订单导出数据
     *
     * @param fromTimeLong  订单创建开始时间
     * @param endTimeLong   订单创建结束时间
     * @param orderStates   订单状态
     * @param tradeCodeType 交易券类型
     * @param tradeCodes    交易券列表
     * @param orderNumber   订单号列表
     * @return
     */
    List<Order> listExportOrders(Long fromTimeLong, Long endTimeLong, List<Integer> orderStates, String tradeCodeType, List<String> tradeCodes, String orderNumber);

    /**
     * 分页查询订单信息
     *
     * @param fromTimeLong
     * @param endTimeLong
     * @param orderStates
     * @param pageQuery
     * @return
     */
    PageResult<Order> getOrderPage(Long fromTimeLong, Long endTimeLong, Integer id, List<Integer> orderStates, PageQuery pageQuery);

    /**
     * 修改订单状态
     *
     * @param orders
     * @param adminJobNumber
     */
    void updateBatchOrders(List<Order> orders, String adminJobNumber);

    /**
     * 修改单个订单状态
     *
     * @param order
     * @param adminJobNumber
     */
    void updateOrder(Order order, String adminJobNumber);

    /**
     * 通过时间范围和国家查询订单
     *
     * @param from
     * @param end
     * @param country
     * @return
     */
    List<Order> listByTimeRangeAndArea(LocalDateTime from, LocalDateTime end, String country);


    /**
     * 计数定制导出订单
     *
     * @param orderQueryWrapper 订单查询包装器
     * @return {@link Integer}
     */
    Integer countExportOrders(QueryWrapper<Order> orderQueryWrapper);

    /**
     * 分页导出订单
     *
     * @param page              分页
     * @param orderQueryWrapper 订单查询包装器
     * @return {@link IPage}<{@link Order}>
     */
    IPage<Order> pageExportOrders(Page<Order> page, QueryWrapper<Order> orderQueryWrapper);

    /**
     * 分页获取定制订单
     *
     * @param page                      分页
     * @param orderCustomExportCreation 订单定制导出创建
     * @return {@link List}<{@link Order}>
     */
    List<Order> pageCustomOrders(Page<Order> page, OrderCustomExportCreation orderCustomExportCreation);

    /**
     * 计数定制导出订单
     *
     * @param orderCustomExportCreation 订单定制导出创建
     * @return {@link Integer}
     */
    Integer countCustomExportOrders(OrderCustomExportCreation orderCustomExportCreation);

    /**
     * 根据下单邮箱获取通过该邮箱下单的所有支付后的游客云订阅订单
     *
     * @param email
     * @param isFixedId
     * @param isContainProcessing
     * @return
     */
    List<Order> listCloudSubscribeOrderByEmail(String email, Boolean isFixedId, Boolean isContainProcessing);

    /**
     * 根据下单邮箱查询未支付云服务订阅订单列表
     *
     * @param email
     * @return
     */
    List<Order> listUnpaidCloudSubscribeOrder(String email);

    /**
     * 批量更新订单状态
     *
     * @param orderIdList
     * @param orderState
     * @return
     */
    Integer batchUpdateOrderState(List<Integer> orderIdList, Integer orderState);

    /**
     * 权限检查
     *
     * @param orderId 订单id
     * @param account 账户
     * @return {@link Order}
     */
    Order checkPermission(Integer orderId, StoreAccount account);

    /**
     * 根据用户商城账户ID、订单状态列表获取云服务订阅订单列表
     *
     * @param userId         账户id
     * @param orderStateList 订单状态列表
     * @return {@link List}<{@link Order}>
     */
    List<Order> listCloudSubscribeOrderByState(Integer userId, List<Integer> orderStateList);

    /**
     * 获取最新未支付续费订单
     *
     * @param storeAccountId
     * @param orderState
     * @return
     */
    Order getCloudSubscribeRenewOrder(Integer storeAccountId, Integer orderState);

    /**
     * 根据用户商城账户ID、订单状态列表、订阅场景类型获取云服务订单列表（二期使用，勿删）
     *
     * @param userId            账户id
     * @param orderStateList    订单状态列表
     * @param serviceScenesType 场景类型
     * @return {@link List}<{@link Order}>
     */
    List<Order> listCloudSubscribeOrder(Integer userId, List<Integer> orderStateList, ServiceScenesType serviceScenesType);

    /**
     * 批量同步用户id
     *
     * @param limit
     * @return
     */
    int batchSyncUserId(Integer limit);

    /**
     * 查询符合状态的psp订单
     *
     * @param orderNumbers
     * @return
     */
    List<Order> listByPspOrderNumber(List<String> orderNumbers);
}
