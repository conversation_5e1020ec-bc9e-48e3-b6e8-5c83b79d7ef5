package com.insta360.store.business.trade.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/23
 */
public class UserCartCommodityInfoBO implements Serializable {

    /**
     * 产品图片URL
     */
    @NotBlank
    private String productImage;

    /**
     * 产品名称
     */
    @NotBlank
    private String productName;

    /**
     * 商品名称
     */
    @NotBlank
    private String commodityName;

    /**
     * 当前价格
     */
    @NotNull
    private Double currentPrice;

    /**
     * 原价
     */
    @NotNull
    private Double originPrice;

    /**
     * 产品数量
     */
    @NotNull
    private Integer number;

    /**
     * 新品标签
     */
    private Boolean newTag;

    public String getProductImage() {
        return productImage;
    }

    public void setProductImage(String productImage) {
        this.productImage = productImage;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Double getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(Double currentPrice) {
        this.currentPrice = currentPrice;
    }

    public Double getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Double originPrice) {
        this.originPrice = originPrice;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Boolean getNewTag() {
        return newTag;
    }

    public void setNewTag(Boolean newTag) {
        this.newTag = newTag;
    }

    @Override
    public String toString() {
        return "UserCartCommodityInfoBO{" +
                "productImage='" + productImage + '\'' +
                ", productName='" + productName + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", currentPrice=" + currentPrice +
                ", originPrice=" + originPrice +
                ", number=" + number +
                ", newTag=" + newTag +
                '}';
    }
}
