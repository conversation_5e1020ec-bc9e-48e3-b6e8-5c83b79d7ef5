package com.insta360.store.business.configuration.check.chain.custom;

import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.trade.model.EngravingImage;
import com.insta360.store.business.trade.service.EngravingImageService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/27
 */
@Component
public class CustomBindCheckChain extends BaseCustomCheckChain {

    private static final String NAME = "定制贴绑定检查";

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    EngravingImageService engravingImageService;

    @Override
    public Boolean doCheck(DoubleCheckBO doubleCheckBO) {
        List<EngravingImage> engravingImages = super.listEngravingImageList(doubleCheckBO);
        if (CollectionUtils.isEmpty(engravingImages)) {
            return false;
        }
        for (EngravingImage engravingImage : engravingImages) {
            String serialId = engravingImage.getSerialId();
            if (StringUtils.isBlank(serialId)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer getCheckIndex() {
        return 1;
    }
}
