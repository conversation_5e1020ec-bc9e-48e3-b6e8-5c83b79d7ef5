package com.insta360.store.business.email.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: py
 * @create: 2023-02-13 16:13
 */
public class EmailBlackListDTO implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 邮箱
     */
    private List<String> emailList;

    /**
     * 邮件模版key list
     */
    private List<String> emailTemplateKeyList;

    /**
     * 邮件模版key-增加营销邮件key
     */
    private String emailTemplateKey;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public List<String> getEmailList() {
        return emailList;
    }

    public void setEmailList(List<String> emailList) {
        this.emailList = emailList;
    }

    public List<String> getEmailTemplateKeyList() {
        return emailTemplateKeyList;
    }

    public void setEmailTemplateKeyList(List<String> emailTemplateKeyList) {
        this.emailTemplateKeyList = emailTemplateKeyList;
    }

    public String getEmailTemplateKey() {
        return emailTemplateKey;
    }

    public void setEmailTemplateKey(String emailTemplateKey) {
        this.emailTemplateKey = emailTemplateKey;
    }

    @Override
    public String toString() {
        return "EmailBlackListDTO{" +
                "id=" + id +
                ", emailList=" + emailList +
                ", emailTemplateKeyList=" + emailTemplateKeyList +
                ", emailTemplateKey='" + emailTemplateKey + '\'' +
                '}';
    }
}
