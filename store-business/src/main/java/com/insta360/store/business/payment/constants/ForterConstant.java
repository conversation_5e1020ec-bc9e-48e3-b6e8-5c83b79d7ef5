package com.insta360.store.business.payment.constants;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/6/17
 */
public class ForterConstant {

    public static final Map<String, String> chargeBackReasonMap = ImmutableMap.<String, String>builder()
            .put("重复处理", "Duplicate Processing")
            .put("服务未提供或未收到商品", "Merchandise/Services Not Received")
            .put("错误交易代码", "Incorrect Transaction Code")
            .put("账号不匹配", "Non Matching Account Number")
            .put("已取消的循环交易", "Cancelled Recurring")
            .put("货物损坏/货不对版", "Not as Described or Defective Merchandise/Services")
            .put("持卡人无法识别交易", "Cardholder Does Not Recognize the Transaction")
            .put("未收到退款", "Credit Not Processed")
            .put("欺诈交易", "Other Fraud-Card Absent Environment")
            .put("其他", "Others")
            .put("过期卡片", "Expired Card")
            .put("假卡交易", "Counterfeit Transaction")
            .put("交易金额或账户错误", "Incorrect Transaction Amount or Account Number")
            .put("已用其他方式支付", "Paid by Other Means")
            .put("已取消的商品/服务", "Cancelled Merchandise/Services")
            .put("持卡人未授权", "No Authorization")
            .put("无效数据", "Invalid Data")
            .put("交易清算延迟", "Late Presentment")
            .put("错误账户", "Incorrect Account Number")
            .put("错误交易金额", "Incorrect Amount")
            .put("错误交易币种", "Incorrect Currency")
            .put("触发伪冒监控程序", "Visa Fraud Monitoring Program")
            .put("虚假商品", "Counterfeit Merchandise")
            .put("虚假描述", "Misrepresentation")
            .put("调单请求不清晰/理由不充分", "Request for support illegible/insufficient")
            .put("重复处理/已用其他方式支付", "Duplicate Processing/Paid by Other Means")
            .build();

    public static final String cardType = "CREDIT";

    public static final String status = "failed";

    public static final String declinedReason = "declined by forter";

    public static final List<String> countrys = Lists.newArrayList(InstaCountry.HK.name(), InstaCountry.MO.name());

    /**
     * 同步forter的编号（目前cko与ocean一致，后续若发生变化，建议落库）
     */
    public static final Integer MERCHANT_CATEGORY_CODE = 5732;

    /**
     * forter拒绝的 auth code
     */
    public static final String FORTER_DECLINED_AUTH_CODE = "99999";

    /**
     * forter拒绝的 auth text
     */
    public static final String FORTER_DECLINED_AUTH_TEX = "cancel_by_forter";
}
