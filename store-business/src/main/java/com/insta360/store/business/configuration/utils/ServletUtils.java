package com.insta360.store.business.configuration.utils;

import com.insta360.compass.core.util.ServletUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: wbt
 * @Date: 2022/02/18
 * @Description:
 */
public class ServletUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServletUtils.class);

    /**
     * 根据ua判断是移动端还是PC端（解析异常默认走PC配置）
     *
     * @param servletRequest
     * @return
     */
    public static boolean isMobile(HttpServletRequest servletRequest) {
        try {
            return ServletUtil.isMobile(servletRequest);
        } catch (Exception e) {
            LOGGER.error("Request content parsing failed... error message:{}", e.getMessage(), e);
            return false;
        }
    }
}
