package com.insta360.store.business.trade.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.compass.core.datasource.util.PageUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;
import com.insta360.store.business.trade.bo.CreditCardOrderPaymentInfoQueryBO;
import com.insta360.store.business.trade.dao.CreditCardPaymentInfoDao;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.service.CreditCardPaymentInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2021-12-03
 * @Description:
 */
@Service
public class CreditCardPaymentInfoServiceImpl extends BaseServiceImpl<CreditCardPaymentInfoDao, CreditCardPaymentInfo> implements CreditCardPaymentInfoService {

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Override
    public CreditCardPaymentInfo getByOrderNumber(String orderNumber) {
        QueryWrapper<CreditCardPaymentInfo> qw = new QueryWrapper<>();
        qw.eq("order_number", orderNumber);
        return baseMapper.selectOne(qw);
    }

    @Override
    public List<CreditCardPaymentInfo> listPaymentInfo(CreditCardOrderPaymentInfoQueryBO creditCardOrderPaymentInfoQuery) {
        return baseMapper.selectList(getQueryWrapper(creditCardOrderPaymentInfoQuery));
    }

    @Override
    public List<CreditCardPaymentInfo> listAllPaymentInfo() {
        return baseMapper.selectList(null);
    }

    @Override
    public PageResult<CreditCardPaymentInfo> queryPaymentInfo(CreditCardOrderPaymentInfoQueryBO creditCardOrderPaymentInfoQuery, PageQuery pageQuery) {
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), getQueryWrapper(creditCardOrderPaymentInfoQuery)));
    }

    @Override
    public PageResult<CreditCardPaymentInfo> listCapturePaymentInfo(PageQuery pageQuery) {
        // 获取未capture成功的订单号
        QueryWrapper<OrderPayment> orderPaymentQw = new QueryWrapper<>();
        orderPaymentQw.eq("trade_security", PaymentTradeSecurityType.HAVE_NOT_CAPTURE.getCode());
        List<OrderPayment> orderPayments = orderPaymentService.list(orderPaymentQw);
        List<Integer> orderIds = orderPayments.stream().map(OrderPayment::getOrder).collect(Collectors.toList());
        List<String> orderNumbers = orderService.listByIds(orderIds).stream().map(Order::getOrderNumber).collect(Collectors.toList());

        // 获取对应的支付信息
        QueryWrapper<CreditCardPaymentInfo> qw = new QueryWrapper<>();
        qw.in(CollectionUtils.isNotEmpty(orderNumbers), "order_number", orderNumbers);
        return PageUtil.toPageResult(baseMapper.selectPage(PageUtil.toIPage(pageQuery), qw));
    }

    private QueryWrapper<CreditCardPaymentInfo> getQueryWrapper(CreditCardOrderPaymentInfoQueryBO creditCardOrderPaymentInfoQuery) {
        QueryWrapper<CreditCardPaymentInfo> qw = new QueryWrapper<>();

        // 订单号
        String orderNumber = creditCardOrderPaymentInfoQuery.getOrderNumber();
        qw.eq(StringUtil.isNotBlank(orderNumber), "order_number", orderNumber);

        // 支付卡种
        String creditCardType = creditCardOrderPaymentInfoQuery.getCreditCardType();
        qw.eq(StringUtil.isNotBlank(creditCardType), "credit_card_type", creditCardType);

        // 支付机构
        String payMethod = creditCardOrderPaymentInfoQuery.getPayMethod();
        qw.eq(StringUtil.isNotBlank(payMethod), "pay_method", payMethod);

        // 支付通道
        Integer payChannelId = creditCardOrderPaymentInfoQuery.getPayChannelId();
        qw.eq(payChannelId != null, "pay_channel_id", payChannelId);

        // 订单所属国
        String orderCountry = creditCardOrderPaymentInfoQuery.getOrderCountry();
        qw.eq(StringUtil.isNotBlank(orderCountry), "order_country", orderCountry);

        // forter决策建议
        String forterDecision = creditCardOrderPaymentInfoQuery.getForterDecision();
        qw.eq(StringUtil.isNotBlank(forterDecision), "forter_last_decision", forterDecision);

        // 是否是3d验证订单
        Boolean threeDomainSecure = creditCardOrderPaymentInfoQuery.getThreeDomainSecure();
        qw.eq(threeDomainSecure != null, "three_domain_secure", threeDomainSecure);

        // 支付结果
        Boolean payResult = creditCardOrderPaymentInfoQuery.getPayResult();
        qw.eq(payResult != null, "payment_result", payResult);

        // 订单创建时间
        LocalDateTime orderCreateStartTime = creditCardOrderPaymentInfoQuery.getOrderCreateStartTime();
        LocalDateTime orderCreateEndTime = creditCardOrderPaymentInfoQuery.getOrderCreateEndTime();
        qw.gt(orderCreateStartTime != null, "order_create_time", orderCreateStartTime);
        qw.lt(orderCreateEndTime != null, "order_create_time", orderCreateEndTime);

        // 支付请求发起时间区间
        LocalDateTime orderPayRequestStartTime = creditCardOrderPaymentInfoQuery.getOrderPayRequestStartTime();
        LocalDateTime orderPayRequestEndTime = creditCardOrderPaymentInfoQuery.getOrderPayRequestEndTime();
        qw.gt(orderPayRequestStartTime != null, "order_pay_request_time", orderPayRequestStartTime);
        qw.lt(orderPayRequestEndTime != null, "order_pay_request_time", orderPayRequestEndTime);

        qw.orderByDesc("create_time");
        return qw;
    }
}
