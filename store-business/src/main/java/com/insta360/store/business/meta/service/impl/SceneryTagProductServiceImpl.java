package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.store.business.meta.model.SceneryTagProduct;
import com.insta360.store.business.meta.dao.SceneryTagProductDao;
import com.insta360.store.business.meta.service.SceneryTagProductService;
import com.insta360.compass.core.common.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-12-18
 * @Description:
 */
@Service
public class SceneryTagProductServiceImpl extends BaseServiceImpl<SceneryTagProductDao, SceneryTagProduct> implements SceneryTagProductService {

    @Override
    public List<SceneryTagProduct> listByProductId(Integer productId) {
        QueryWrapper<SceneryTagProduct> qw = new QueryWrapper<>();
        qw.eq("product_id", productId);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<SceneryTagProduct> listByTagIds(List<Integer> sceneryMainIds) {
        if (CollectionUtils.isEmpty(sceneryMainIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<SceneryTagProduct> qw = new QueryWrapper<>();
        qw.in("scenery_tag_id", sceneryMainIds);
        return baseMapper.selectList(qw);
    }

    @Override
    public List<SceneryTagProduct> listByTagId(Integer sceneryTagId) {
        if (Objects.isNull(sceneryTagId)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<SceneryTagProduct> qw = new QueryWrapper<>();
        qw.eq("scenery_tag_id", sceneryTagId);
        return baseMapper.selectList(qw);
    }

    @Override
    public void deleteById(Integer sceneryTagProductId) {
        baseMapper.deleteById(sceneryTagProductId);
    }

    @Override
    public void saveProductBatch(List<SceneryTagProduct> sceneryTagProducts) {
        if (CollectionUtils.isEmpty(sceneryTagProducts)) {
            return;
        }
        baseMapper.saveProductBatch(sceneryTagProducts);
    }

    @Override
    public List<SceneryTagProduct> listByProductIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>(0);
        }
        QueryWrapper<SceneryTagProduct> qw = new QueryWrapper<>();
        qw.in("product_id", productIds);
        return baseMapper.selectList(qw);
    }
}
