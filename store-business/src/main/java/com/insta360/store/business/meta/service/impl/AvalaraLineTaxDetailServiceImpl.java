package com.insta360.store.business.meta.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.common.BaseServiceImpl;
import com.insta360.store.business.meta.dao.AvalaraLineTaxDetailDao;
import com.insta360.store.business.meta.model.AvalaraLineTaxDetail;
import com.insta360.store.business.meta.service.AvalaraLineTaxDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-06-29
 * @Description:
 */
@Service
public class AvalaraLineTaxDetailServiceImpl extends BaseServiceImpl<AvalaraLineTaxDetailDao, AvalaraLineTaxDetail> implements AvalaraLineTaxDetailService {

    @Override
    public List<AvalaraLineTaxDetail> listByOrderItemAvalaraTaxId(List<Integer> avalaraTaxIds) {
        QueryWrapper<AvalaraLineTaxDetail> qw = new QueryWrapper<>();
        qw.in("avalara_line_tax_id", avalaraTaxIds);
        return baseMapper.selectList(qw);
    }
}
