package com.insta360.store.business.discount.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 平台来源类型
 * @Date 2022/4/8
 */
public enum PlatformSourceType {
    UN_KNOW(0,"未知"),
    OPERATION(1,"运营人员"),
    TEMPLATE(2,"模版生成"),
    TRADEUP(3,"以旧换新"),
    REPAIRMENT(4,"维修工单");

    public final int code;

    public final String value;

    PlatformSourceType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static PlatformSourceType matchCode(Integer code) {
        if(Objects.isNull(code)) {
            return UN_KNOW;
        }
        for (PlatformSourceType platformSourceType : values()) {
            if(Integer.valueOf(platformSourceType.code).equals(code)) {
                return platformSourceType;
            }
        }
        return UN_KNOW;
    }
}
