package com.insta360.store.business.order.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.admin.email.model.UserGuideEmailRecord;
import com.insta360.store.business.admin.email.service.UserGuideEmailRecordService;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.service.OrderDeliveryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/11/19 下午6:10
 */
@Scope("prototype")
@Component
public class OrderOnDeliveryEmailX4 extends BaseOrderEmail {

    @Autowired
    UserGuideEmailRecordService userGuideEmailRecordService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Override
    public String getTemplateName() {
        return "store_order_on_delivery_x4";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        Order order = this.getOrder();

        // 客户姓名
        templateParams.addBodyParam("customer_name", this.getCustomerName());

        // 唯一标识
        templateParams.addBodyParam("uuid", getUuid(order.getOrderNumber()));

        // 国家码
        templateParams.addBodyParam("country", order.getArea());
    }

    @Override
    protected InstaLanguage getLanguage() {
        InstaCountry instaCountry = this.getOrder().country();
        // HK特殊处理
        if (InstaCountry.HK.equals(instaCountry)) {
            Order order = this.getOrder();
            OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
            if (Objects.isNull(orderDelivery)) {
                return InstaLanguage.zh_TW;
            }
            String country = orderDelivery.getCountry();
            if (country.contains("Hong Kong")) {
                return InstaLanguage.en_US;
            }
            return InstaLanguage.zh_TW;
        }
        InstaLanguage language = countryConfigService.getCountryLanguage(instaCountry);
        return language != null ? language : DEFAULT_LANGUAGE;
    }

    /**
     * 根据单号获取uuid
     * 传给前端进行埋点
     *
     * @param orderNumber 订单号
     * @return uuid
     */
    private String getUuid(String orderNumber) {
        UserGuideEmailRecord userGuideEmailRecord = userGuideEmailRecordService.getByOrderNumber(orderNumber);
        if (Objects.isNull(userGuideEmailRecord)) {
            return StringUtils.EMPTY;
        }
        return userGuideEmailRecord.getUuid();
    }
}