package com.insta360.store.business.trade.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.trade.model.CreditCardPostPayRule;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2025/06/04
 * @Description:
 */
public class CreditCardPostPayRuleDTO implements Serializable {

    /**
     * Snowflake ID
     */
    private String id;

    /**
     * 卡种类型id
     */
    private Integer cardTypeId;

    /**
     * 地区
     */
    private String country;

    /**
     * 钱海支付通道
     */
    private Integer oceanPayChannelId;

    /**
     * checkou支付通道
     */
    private Integer checkoutPayChannelId;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * ids
     */
    private List<String> ids;


    public CreditCardPostPayRule getPojoObject() {
        CreditCardPostPayRule creditCardPostPayRule = new CreditCardPostPayRule();
        BeanUtil.copyProperties(this, creditCardPostPayRule);
        return creditCardPostPayRule;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCardTypeId() {
        return cardTypeId;
    }

    public void setCardTypeId(Integer cardTypeId) {
        this.cardTypeId = cardTypeId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getOceanPayChannelId() {
        return oceanPayChannelId;
    }

    public void setOceanPayChannelId(Integer oceanPayChannelId) {
        this.oceanPayChannelId = oceanPayChannelId;
    }

    public Integer getCheckoutPayChannelId() {
        return checkoutPayChannelId;
    }

    public void setCheckoutPayChannelId(Integer checkoutPayChannelId) {
        this.checkoutPayChannelId = checkoutPayChannelId;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    @Override
    public String toString() {
        return "CreditCardPostPayRuleDTO{" +
                "id=" + id +
                ", cardTypeId=" + cardTypeId +
                ", country='" + country + '\'' +
                ", oceanPayChannelId=" + oceanPayChannelId +
                ", checkoutPayChannelId=" + checkoutPayChannelId +
                ", enabled='" + enabled + '\'' +
                ", ids=" + ids +
                '}';
    }
}
