package com.insta360.store.business.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.insta360.store.business.product.model.ProductMainVideoCountry;
import com.insta360.store.business.product.dao.ProductMainVideoCountryDao;
import com.insta360.store.business.product.service.ProductMainVideoCountryService;
import com.insta360.compass.core.common.BaseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-05-10
 * @Description:
 */
@Service
public class ProductMainVideoCountryServiceImpl extends BaseServiceImpl<ProductMainVideoCountryDao, ProductMainVideoCountry> implements ProductMainVideoCountryService {

    @Override
    public void saveCountryList(List<String> countryList, Integer mainVideoId) {
        if (CollectionUtils.isEmpty(countryList)) {
            return;
        }

        List<ProductMainVideoCountry> mainVideoCountries = countryList.stream().map(country -> {
            ProductMainVideoCountry productMainVideoCountry = new ProductMainVideoCountry();
            productMainVideoCountry.setCountry(country);
            productMainVideoCountry.setMainVideoId(mainVideoId);
            productMainVideoCountry.setCreateTime(LocalDateTime.now());
            productMainVideoCountry.setUpdateTime(LocalDateTime.now());
            return productMainVideoCountry;
        }).collect(Collectors.toList());
        baseMapper.saveCountryList(mainVideoCountries);
    }

    @Override
    public void deleteByConditionId(Integer mainVideoId) {
        UpdateWrapper<ProductMainVideoCountry> uw = new UpdateWrapper<>();
        uw.eq("main_video_id", mainVideoId);
        baseMapper.delete(uw);
    }

    @Override
    public List<ProductMainVideoCountry> listByMainVideoIds(List<Integer> mainVideoIds) {
        QueryWrapper<ProductMainVideoCountry> qw = new QueryWrapper<>();
        qw.in("main_video_id", mainVideoIds);
        return baseMapper.selectList(qw);
    }
}
