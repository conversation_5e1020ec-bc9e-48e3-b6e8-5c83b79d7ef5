package com.insta360.store.business.meta.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.meta.model.NavigationBarCategoryThirdInfo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-11-14
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface NavigationBarCategoryThirdInfoDao extends BaseDao<NavigationBarCategoryThirdInfo> {

    /**
     * 批量创建
     *
     * @param navigationBarCategoryThirdInfoList
     */
    void createCategoryThirdInfo(@Param("thirdInfoList") List<NavigationBarCategoryThirdInfo> navigationBarCategoryThirdInfoList);
}
