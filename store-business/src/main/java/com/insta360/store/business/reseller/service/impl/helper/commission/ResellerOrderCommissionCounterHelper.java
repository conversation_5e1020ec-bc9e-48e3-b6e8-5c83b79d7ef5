package com.insta360.store.business.reseller.service.impl.helper.commission;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.model.MetaCurrency;
import com.insta360.store.business.meta.service.MetaCurrencyService;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import com.insta360.store.business.reseller.bo.CommissionBO;
import com.insta360.store.business.reseller.bo.CommissionDetailBO;
import com.insta360.store.business.reseller.common.ResellerConstant;
import com.insta360.store.business.reseller.config.ResellerConfig;
import com.insta360.store.business.reseller.enums.ResellerCommonState;
import com.insta360.store.business.reseller.enums.ResellerWithdrawAccountType;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerProduct;
import com.insta360.store.business.reseller.service.ResellerCommissionRateService;
import com.insta360.store.business.reseller.service.ResellerProductService;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 分销订单佣金计算助手
 * @Date 2022/12/14
 */
@Component
public class ResellerOrderCommissionCounterHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResellerOrderCommissionCounterHelper.class);

    @Autowired
    ResellerCommissionRateService commissionRateService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    StoreConfigService configService;

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ResellerProductService resellerProductService;

    @Autowired
    ResellerConfig resellerConfig;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    MetaCurrencyService metaCurrencyService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    /**
     * 分销订单佣金计算
     *
     * @param reseller
     * @param order
     * @return
     */
    public CommissionBO orderCommissionCalculate(Reseller reseller, Order order) {
        LOGGER.info("[分销订单佣金处理]分销订单创建总佣金计算流程处理开始... orderNumber:{},resellerCode:{}", order.getOrderNumber(), reseller.getPromoCode());

        if (Objects.isNull(reseller) || Objects.isNull(order)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "非法参数");
        }
        // 获取订单支付信息
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        if (Objects.isNull(orderPayment)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "支付信息缺失");
        }
        // 获取订单商品
        List<OrderItem> orderItemList = orderItemService.getByOrder(order.getId());
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException, "订单商品信息缺失");
        }

        // 获取分销商提现币种,未设置提现币种场景默认'美元'
        Currency withdrawCurrency = Objects.nonNull(reseller.useCurrency()) ? reseller.useCurrency() : Currency.USD;
        // 订单支付币种
        Currency payCurrency = orderPayment.currency();

        // 记录支付货币、提现货币的初始美元汇率
        double payCurrencyUsdRate = 1d;
        double withdrawCurrencyUsdRate = 1d;
        if (!payCurrency.equals(withdrawCurrency)) {
            payCurrencyUsdRate = getUsdRate(payCurrency);
            withdrawCurrencyUsdRate = getUsdRate(withdrawCurrency);
        }

        // 使用该优惠券的分销订单，不计算佣金。（佣金变成折扣让利给了他的粉丝）
        if (StringUtils.isNotBlank(order.getCouponCode()) && ResellerConstant.COUPON_CODE.equals(order.getCouponCode())) {
            return buildSpecialResellerCommission(orderItemList, withdrawCurrency, payCurrencyUsdRate, withdrawCurrencyUsdRate);
        }

        // 构建套餐佣金计算所需上下文参数
        ResellerCommissionCalculateContext context = ResellerCommissionCalculateContext.buildContext(reseller, null, null, withdrawCurrency, null, payCurrencyUsdRate, withdrawCurrencyUsdRate, true);

        // 分销订单佣金明细
        List<CommissionDetailBO> commissionDetailList = orderItemList.stream()
                .map(
                        orderItem -> {
                            RmaOrder rmaOrder = rmaOrderService.getByOrderItem(orderItem.getId());
                            context.setRmaOrder(rmaOrder);
                            context.setOrderItem(orderItem);
                            return orderItemCommissionInitCalculate(context);
                        })
                .collect(Collectors.toList());

        // 分销订单总收益
        double totalIncome = commissionDetailList.stream()
                .map(
                        commissionDetail ->
                                new BigDecimal(String.valueOf(commissionDetail.getEstimatedIncome()))
                )
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .doubleValue();

        return new CommissionBO(totalIncome, commissionDetailList, withdrawCurrency, payCurrencyUsdRate, withdrawCurrencyUsdRate);
    }

    /**
     * 获取货币美元汇率
     *
     * @param currency 币种
     * @return 美元汇率
     */
    private double getUsdRate(Currency currency) {
        MetaCurrency metaCurrency = metaCurrencyService.getByCurrency(currency);
        return Objects.nonNull(metaCurrency) ? Double.parseDouble(String.valueOf(metaCurrency.getUsdRate())) : 0d;
    }

    /**
     * 构建特殊分销商佣金
     *
     * @param orderItemList
     * @param withdrawCurrency
     * @param payCurrencyUsdRate
     * @param withdrawCurrencyUsdRate
     * @return
     */
    private CommissionBO buildSpecialResellerCommission(List<OrderItem> orderItemList, Currency withdrawCurrency, double payCurrencyUsdRate, double withdrawCurrencyUsdRate) {
        List<CommissionDetailBO> commissionDetailList = orderItemList.stream()
                .map(
                        orderItem -> new CommissionDetailBO(orderItem.getId(), 0d, orderItem.getNumber(), 0d))
                .collect(Collectors.toList());
        return new CommissionBO(0d, commissionDetailList, withdrawCurrency, payCurrencyUsdRate, withdrawCurrencyUsdRate);
    }

    /**
     * 订单商品套餐佣金初始化计算
     *
     * @param resellerCommissionCalculateContext 分销佣金计算上下文
     * @return
     */
    public CommissionDetailBO orderItemCommissionInitCalculate(ResellerCommissionCalculateContext resellerCommissionCalculateContext) {
        LOGGER.info("[分销订单佣金处理]订单商品套餐佣金初始化计算流程处理开始... Context:{}", JSON.toJSONString(resellerCommissionCalculateContext));
        if (Objects.isNull(resellerCommissionCalculateContext)) {
            throw new InstaException(CommonErrorCode.InvalidParameterException);
        }
        // 需计算套餐佣金的商品
        OrderItem orderItem = resellerCommissionCalculateContext.getOrderItem();
        // 反佣率
        double commissionRate = resellerCommissionCalculateContext.getInitCommissionRate();
        Boolean initCalculate = resellerCommissionCalculateContext.getInitCalculate();
        if (Objects.nonNull(initCalculate) && initCalculate) {
            // 分销商信息
            Reseller reseller = resellerCommissionCalculateContext.getReseller();
            // 查询相应产品or套餐信息
            Integer productId = orderItem.getProduct();
            Product product = productService.getById(productId);
            Commodity commodity = commodityService.getById(orderItem.getCommodity());
            // 佣金初始化计算检查
            CommissionDetailBO commissionDetail = commissionInitCalculateCheck(orderItem, product, commodity);
            LOGGER.info("[分销订单佣金处理]分销商佣金初始化佣金率 分销商：{}，订单：{},订单子项：{}，初始佣金信息：{}", reseller.getPromoCode(), orderItem.getOrder(), orderItem.getId(), JSON.toJSONString(commissionDetail));
            if (Objects.nonNull(commissionDetail)) {
                return commissionDetail;
            }
            commissionRate = getCommissionRate(reseller, product);
            LOGGER.info("[分销订单佣金处理]分销商最终佣金率  分销商：{}，订单：{},订单子项：{} 佣金率为：{}", reseller.getPromoCode(), orderItem.getOrder(), orderItem.getId(), commissionRate);
        }

        // 商品所属售后单
        RmaOrder rmaOrder = resellerCommissionCalculateContext.getRmaOrder();
        // 支付币种兑换美元的汇率
        double payCurrencyUsdRate = resellerCommissionCalculateContext.getPayCurrencyInitUsdRate();
        // 提现币种兑换美元的汇率
        double withdrawCurrencyUsdRate = resellerCommissionCalculateContext.getWithdrawCurrencyInitUsdRate();

        // 套餐佣金计算
        double estimatedIncome = commissionCoreCalculate(commissionRate, payCurrencyUsdRate, withdrawCurrencyUsdRate, orderItem, rmaOrder);

        return new CommissionDetailBO(orderItem.getId(), estimatedIncome, orderItem.getNumber(), commissionRate);
    }

    /**
     * 佣金初始化计算检查
     *
     * @param orderItem
     * @param product
     * @param commodity
     * @return
     */
    private CommissionDetailBO commissionInitCalculateCheck(OrderItem orderItem, Product product, Commodity commodity) {
        if (!isEnabled(product, commodity)) {
            return new CommissionDetailBO(orderItem.getId(), 0d, orderItem.getNumber(), 0d);
        }

        ProductCategoryMainType categoryMainType = productCategoryHelper.getCategoryMainByKey(product.getCategoryKey());
        // 非分销产品类目 且非云服务 不参与佣金计算
        if (!ProductCategoryMainType.isResellerCategory(categoryMainType) && !product.whetherCould()) {
            LOGGER.info("[佣金初始化计算检查] 非分销产品类目 或 非云服务 不参与佣金计算 订单子项:{} 产品ID:{},套餐ID:{}", orderItem.getId(), product.getId(), commodity.getId());
            return new CommissionDetailBO(orderItem.getId(), 0d, orderItem.getNumber(), 0d);
        }
        // 赠品不参与佣金计算
        if (orderItem.getIsGift()) {
            LOGGER.info("[佣金初始化计算检查] 赠品不参与佣金计算 订单子项:{} 产品ID:{},套餐ID:{}", orderItem.getId(), product.getId(), commodity.getId());
            return new CommissionDetailBO(orderItem.getId(), 0d, orderItem.getNumber(), 0d);
        }

        // 推广产品返佣-佣金计算  该产品必需在reseller_product表存在，且state = 1（支持佣金）
        ResellerProduct resellerProduct = resellerProductService.getResellerProduct(product.getId(), ResellerCommonState.ENABLE.code);
        if (Objects.isNull(resellerProduct)) {
            LOGGER.info("[佣金初始化计算检查] 分销产品未启用或不存在 订单子项:{} 产品ID:{},套餐ID:{}", orderItem.getId(), product.getId(), commodity.getId());
            return new CommissionDetailBO(orderItem.getId(), 0d, orderItem.getNumber(), 0d);
        }
        return null;
    }

    /**
     * 获取产品佣金率
     *
     * @param reseller
     * @param product
     * @return
     */
    private double getCommissionRate(Reseller reseller, Product product) {
        // 产品类型
        ProductCategoryMainType categoryMainType = productCategoryHelper.getCategoryMainByKey(product.getCategoryKey());

        // 佣金率
        double commissionRate = 0d;
        // 相机类目 以及云服务产品 直接使用当前产品ID及分销ID查询对应返佣比例
        if (ProductCategoryMainType.isCameraType(categoryMainType) || product.whetherCould()) {
            commissionRate = commissionRateService.getRate(reseller, product.getId());
        }

        // 若产品类型为'配件'则使用默认产品ID（339）及当前下单绑定的分销ID去佣金表查询对应返佣比例
        if (ProductCategoryMainType.CM_ACCESSORY.equals(categoryMainType)) {
            commissionRate = commissionRateService.getRate(reseller, resellerConfig.getDefaultAccessoriesId());
        }

        return commissionRate;
    }

    /**
     * 分销佣金核心底层计算
     *
     * @param commissionRate          产品佣金率
     * @param payCurrencyUsdRate      支付币种兑换美元的汇率
     * @param withdrawCurrencyUsdRate 提现币种兑换美元的汇率
     * @param orderItem               当前需计算的订单商品
     * @param rmaOrder                当前需计算的订单商品所属售后单
     * @return 套餐佣金
     */
    private double commissionCoreCalculate(double commissionRate, double payCurrencyUsdRate, double withdrawCurrencyUsdRate, OrderItem orderItem, RmaOrder rmaOrder) {
        // 商品实际支付总额 = 商品现价 * 购买数量 - 商品优惠总额
        BigDecimal totalItemActualAmount = orderItem.getItemTotalAmountPaid();
        // 商品售后退款金额
        BigDecimal refundAmount = BigDecimal.ZERO;
        if (Objects.nonNull(rmaOrder) && !RmaType.rma_change.equals(rmaOrder.rmaType()) && !rmaOrder.isClose()) {
            refundAmount = new BigDecimal(String.valueOf(rmaOrder.getRefundAmount()));
        }

        // 商品预估收益 = 商品减去优惠后的总价 - 商品售后退款金额 * 反佣率
        BigDecimal estimatedIncome = (totalItemActualAmount.subtract(refundAmount)).multiply(new BigDecimal(String.valueOf(commissionRate))).setScale(2, BigDecimal.ROUND_HALF_UP);
        // 汇率转换 计算公式：原币种金额 * 原币种兑换美元的汇率 / 提现币种兑换美元的汇率
        estimatedIncome = estimatedIncome.multiply(new BigDecimal(String.valueOf(payCurrencyUsdRate))).divide(new BigDecimal(String.valueOf(withdrawCurrencyUsdRate)), 2, BigDecimal.ROUND_HALF_UP);

        return estimatedIncome.compareTo(BigDecimal.ZERO) < 0 ? 0d : estimatedIncome.doubleValue();
    }

    /**
     * 产品or套餐是否启用
     *
     * @param product
     * @param commodity
     * @return
     */
    private boolean isEnabled(Product product, Commodity commodity) {
        return Objects.nonNull(product) && product.getEnabled() && Objects.nonNull(commodity) && commodity.getEnabled();
    }

    /**
     * 税费（按比率）
     *
     * @param price
     * @return
     * @Description 废弃原因是因为中国大陆现在不在以单个订单计算税费，而是以一个分销商所有可以提现的总金额进行计算税费（可参考）。
     */
    // TODO: 2020-01-03 准备废弃
    public Price getTax(Price price) {
        float amount = price.getAmount();
        Currency currency = price.getCurrency();

        if (Currency.CNY.equals(currency)) {
            float rate = Float.parseFloat(configService.getConfigValue(StoreConfigKey.reseller_withdraw_tax_rate));
            // TODO: 2020/7/6 正式废弃（保留这段逻辑的原因是可以了解到以前的计算逻辑，防止恢复以订单为单位计算后需要重新编写逻辑）。直接设置为0f即可。
            amount = amount * rate * 0f;
        } else {
            amount = 0f;
        }

        return new Price(currency, amount);
    }

    /**
     * 手续费（固定USD）
     *
     * @param currency
     * @param withdrawAccountType
     * @return
     */
    public Price getFee(Currency currency, ResellerWithdrawAccountType withdrawAccountType) {
        if (Currency.USD.equals(currency)
                && ResellerWithdrawAccountType.bank.equals(withdrawAccountType)) {
            float fee = Float.parseFloat(configService.getConfigValue(StoreConfigKey.reseller_withdraw_fee));
            return new Price(Currency.USD, fee);
        } else {
            return new Price(currency, 0);
        }
    }
}
