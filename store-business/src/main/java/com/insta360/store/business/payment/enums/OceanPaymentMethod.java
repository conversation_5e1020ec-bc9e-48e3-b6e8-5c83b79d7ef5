package com.insta360.store.business.payment.enums;

/**
 * @Author: hyc
 * @Date: 2019-05-20
 * @Description:
 */
public enum OceanPaymentMethod {

    // 德国、奥地利、荷兰、比利时、法国、意大利、西班牙
    Sofortbanking("Directpay"),

    // 德国，荷兰，奥地利
    Giropay("Giropay"),

    // 荷兰
    iDEAL("iDEAL"),

    credit_card("Credit Card"),

    // 国际银联
    UnionPay("UnionPay"),

    // 韩国
    Kakaopay("KOR_eWallet"),

    // 韩国
    NaverPay("NaverPay"),

    // 韩国
    Kor_credit_card("KOR_Credit Card"),

    ;

    private final String methodParaName;

    OceanPaymentMethod(String paraName) {
        this.methodParaName = paraName;
    }

    public String getMethodParaName() {
        return methodParaName;
    }
}

