package com.insta360.store.business.insurance.email;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.insurance.email.BaseInsuranceEmail;
import com.insta360.store.business.insurance.model.ExtendInsurance;
import com.insta360.store.business.meta.bo.EmailTemplateParams;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2021/4/9
 * @Description: 延保激活邮件
 */
@Scope("prototype")
@Component
public class DeviceExtendInsuranceEmail extends BaseInsuranceEmail {

    @Override
    public String getTemplateName() {
        return "device_extend_insurance_bind_notification";
    }

    @Override
    protected void configTemplateParams(EmailTemplateParams templateParams) {
        ExtendInsurance extendInsurance = this.getExtendInsurance();
        templateParams.addBodyParam("name", this.getCustomerName());
        templateParams.addBodyParam("email", extendInsurance.getEmail());
        templateParams.addBodyParam("phone", extendInsurance.getPhone());
        templateParams.addBodyParam("device_serial", extendInsurance.getDeviceSerial());
        templateParams.addBodyParam("expire_time", getInsuranceExpireTime(extendInsurance.getExpireTime()));
        templateParams.addBodyParam("insurance_number", extendInsurance.getInsuranceNumber());
    }

    @Override
    protected InstaLanguage getLanguage() {
        if (InstaCountry.HK.equals(parseCountry() != null ? parseCountry() : null)) {
            return InstaLanguage.zh_TW;
        }
        return super.getLanguage();
    }

    @Override
    protected InstaCountry parseCountry() {
        return InstaCountry.parse(getExtendInsurance().getArea());
    }
}
