package com.insta360.store.business.meta.enums;

/**
 * @Author: hyc
 * @Date: 2019/3/1
 * @Description:
 */
public enum StoreConfigKey {

    reseller_sub_test,

    reseller_sub_rate,

    reseller_withdraw_tax_rate,

    reseller_withdraw_fee,

    order_auto_cancel_minutes,

    order_auto_check_delivery_minutes,

    /**
     * 订单自动取消的阈值（秒）
     */
    store_order_auto_cancel_ttl,

    /**
     * 订单自动取消的阈值（秒）
     */
    store_order_auto_confirm_delivery_ttl,

    support_languages,

    order_state_allow_action,

    print_set_incoterm,

    print_set_ignore_commodity,

    tradeup_cn_receiver_phone,

    /**
     * 目前care所应用的地区
     */
    care_country_config,

    /**
     * 目前care所应用的欧洲地区
     */
    europe_care_country_config,

    /**
     * 禁止下单的地区
     */
    disabled_create_order_country,

    /**
     * 新品直播链接
     */
    live_link,

    /**
     * 分销条款协议版本号
     */
    reseller_clause_version,

    /**
     * 新品数据展示版本
     */
    new_data_display_version,

    /**
     * 评论有效期（单位：天）
     */
    review_delay_time_config,

    /**
     * 订单折扣比率
     */
    order_discount_ratio,

    /**
     * 不参与监控套餐列表
     */
    monitor_ignore_commodity,
    /**
     * 邮件推荐配件通用配件id
     */
    subscribe_email_recommendation,

    /**
     * 评论加权产品列表
     */
    review_weight_products,

    /**
     * klarna订单capture截止时间（单位：秒）
     */
    klarna_capture_monitor,

    /**
     * 虚拟商品列表
     */
    virtual_goods,

    /**
     * 赠送T恤产品列表
     */
    @Deprecated
    t_xu_products,

    /**
     * 自动推单开关
     */
    auto_push_to_gy_switch,

    /**
     * 自动推单配置条件
     */
    auto_push_to_gy_params,

    /**
     * 行为不端用户链接
     */
    mis_behavior_names_link,

    /**
     * 搜索过滤产品
     */
    search_filter_product,

    /**
     * 搜索过滤套餐
     */
    search_filter_commodity,

    /**
     * 云服务订阅订单自动取消时间
     */
    cloud_subscribe_upgrade_auto_cancel_ttl,

    /**
     * US关税收取开关
     */
    us_customs_tax_switch,

    /**
     * 套餐发货预估天数配置
     */
    commodity_delivery_time_config_key,

    /**
     * 美国州地区限制
     */
    us_province_code_limit;
}
