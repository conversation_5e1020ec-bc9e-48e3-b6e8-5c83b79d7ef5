package com.insta360.store.business.integration.yipiaoyun.enums;

/**
 * <AUTHOR>
 * @Description 折扣行标识类型
 * @Date 2021/9/10
 */
public enum InvoiceNatureType {
    NORMAL(0,"正常"),
    DISCOUNT_LINE(1,"折扣行"),
    COVER_DISCOUNT_LINE(2,"被折扣行");

    private int code;

    private String value;

    InvoiceNatureType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
