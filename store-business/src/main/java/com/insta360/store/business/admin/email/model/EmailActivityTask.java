package com.insta360.store.business.admin.email.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.admin.email.enums.EmailActivityTaskState;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2024-01-04
 * @Description: 活动邮件任务
 */
@TableName("email_activity_task")
public class EmailActivityTask extends BaseModel<EmailActivityTask> {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作者邮箱
     */
    private String jobEmail;

    /**
     * 邮件模版名
     */
    private String templateName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 表格名/任务名
     */
    private String fileName;

    /**
     * 备注
     */
    private String description;

    /**
     * 邮件发送总数
     */
    private Integer sendCount;

    /**
     * 表格邮箱总数
     */
    private Integer totalCount;

    /**
     * 状态
     *
     * @see EmailActivityTaskState
     */
    private Integer state;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 解析状态
     *
     * @return {@link EmailActivityTaskState}
     */
    public EmailActivityTaskState parseState() {
        return EmailActivityTaskState.matchCode(state);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getJobEmail() {
        return jobEmail;
    }

    public void setJobEmail(String jobEmail) {
        this.jobEmail = jobEmail;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSendCount() {
        return sendCount;
    }

    public void setSendCount(Integer sendCount) {
        this.sendCount = sendCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "EmailActivityTask{" +
                "id=" + id +
                ", jobEmail='" + jobEmail + '\'' +
                ", templateName='" + templateName + '\'' +
                ", fileUrl='" + fileUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", description='" + description + '\'' +
                ", sendCount=" + sendCount +
                ", totalCount=" + totalCount +
                ", state=" + state +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}