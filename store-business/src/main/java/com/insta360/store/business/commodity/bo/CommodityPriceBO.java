package com.insta360.store.business.commodity.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CommodityPriceBO implements Serializable {
    private Integer id;

    private Integer commodityId;

    private Float amount;

    private String currency;

    private String productName;

    private String language;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodity) {
        this.commodityId = commodity;
    }

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Override
    public String toString() {
        return "CommodityPriceBO{" +
                "product=" + id +
                ", commodityId=" + commodityId +
                ", amount=" + amount +
                ", currency='" + currency + '\'' +
                ", productName='" + productName + '\'' +
                ", language='" + language + '\'' +
                '}';
    }
}
