package com.insta360.store.business.configuration.check.chain.cart;

import com.alibaba.fastjson.JSONArray;
import com.insta360.store.business.configuration.check.chain.BaseUserCartCheckChain;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.trade.model.UserCart;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/27
 */
@Component
public class UserCartNotEmptyCheckChain extends BaseUserCartCheckChain {

    /**
     * 规则名
     */
    private static final String NAME = "购物车不为空";

    @Override
    public Boolean doCheck(DoubleCheckBO doubleCheckBo) {
        UserCart userCart = super.getUserCart(doubleCheckBo);
        String cartItem = Optional.ofNullable(userCart).map(UserCart::getItems).orElse("[]");
        JSONArray cartItems = JSONArray.parseArray(cartItem);
        return CollectionUtils.isNotEmpty(cartItems);
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public Integer orderIndex() {
        return 3;
    }
}
