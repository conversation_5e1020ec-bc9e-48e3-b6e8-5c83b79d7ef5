package com.insta360.store.business.order.enums;

/**
 * @Author: mowi
 * @Date: 2019/1/15
 * @Description:
 */
public enum OrderItemState {

    // 正常
    normal(0),

    // 退货中
    returning(1),

    // 退款中
    refunding(2),

    // 换货中
    changing(3),

    // 已退款
    refunded(-1),

    // 已退货
    returned(-2),

    // 已换货
    changed(-3),

    //已发货
    on_delivery(4),

    // 关闭
    closed(-9);

    public static OrderItemState parse(Integer i) {
        if (i == null) {
            return normal;
        }

        for (OrderItemState orderState : values()) {
            if (orderState.code == i) {
                return orderState;
            }
        }
        return normal;
    }

    private int code;

    OrderItemState(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    /**
     * 售后状态
     *
     * @param orderItemState
     * @return
     */
    public static Boolean isRmaState(OrderItemState orderItemState) {
        return orderItemState.equals(returning) ||
                orderItemState.equals(refunding) ||
                orderItemState.equals(changing) ||
                orderItemState.equals(refunded) ||
                orderItemState.equals(returned) ||
                orderItemState.equals(changed);
    }

    /**
     * 售后最终态
     *
     * @param orderItemState
     * @return
     */
    public static Boolean isRmaFinalState(OrderItemState orderItemState) {
        return orderItemState.equals(refunded) ||
                orderItemState.equals(returned) ||
                orderItemState.equals(changed);
    }

    /**
     * 售后退款状态
     *
     * @param orderItemState
     * @return
     */
    public static Boolean isRmaRefundState(OrderItemState orderItemState) {
        return orderItemState.equals(refunded) ||
                orderItemState.equals(returned);
    }
}
