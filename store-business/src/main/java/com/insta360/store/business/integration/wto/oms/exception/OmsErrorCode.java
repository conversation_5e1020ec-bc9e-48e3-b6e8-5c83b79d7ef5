package com.insta360.store.business.integration.wto.oms.exception;

import com.insta360.compass.core.exception.ErrorCode;

/**
 * @Author: wkx
 * @Date: 2024/11/29
 * @Description:
 */
public enum OmsErrorCode implements ErrorCode {

    RmaTypeNoMatchException(*********, "OMS售后同步售后类型不匹配"),

    SkuCodeNotExistException(*********, "OMS同步套餐sku_code不存在"),

    RefundAvalaraTaxInfoNotExistException(*********, "OMS同步退款订单，售后退款消费税记录不存在"),

    OrderDeliveryUpdateException(*********, "调用OMS更新地址失败"),

    GiftUpdateFailException(*********, "OMS修改赠品失败"),

    OrderNumberAreInconsistentException(*********, "订单号不一致"),

    OrderItemIdAreInconsistentException(*********, "订单商品ID不一致"),

    RmaOrderNotExistException(*********, "售后单不存在"),

    OrderNotExistException(*********, "订单不存在"),

    OrderItemNotExistException(*********, "订单商品不存在"),

    OrderDeliveryDetailsParamException(*********, "订单发货参数异常");

    /**
     * 异常码
     */
    private final Integer code;

    /**
     * 错误描述
     */
    private final String msg;

    OmsErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
