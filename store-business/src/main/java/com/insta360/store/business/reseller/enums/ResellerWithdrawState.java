package com.insta360.store.business.reseller.enums;

/**
 * @Author: hyc
 * @Date: 2019/3/1
 * @Description:
 */
public enum ResellerWithdrawState {
    // 提现取消
    canceled(-2,"已取消"),

    // 提现被拒绝
    reject(-1,"已拒绝"),

    // 提现申请中
    applying(0,"待审核"),

    // 提现处理中
    processing(1,"待支付"),

    // 提现成功
    success(9,"已提现"),

    //未可提现 （提供页面展示不可用于业务处理及持久化）
    not_yet_withdrawn(2,"未可提现"),

    //不可提现   （提供页面展示不可用于业务处理及持久化）
    not_allowed_withdrawn(4,"不可提现"),

    //可提现   （提供页面展示不可用于业务处理及持久化）
    withdraw(3,"可提现");





    private final int code;

    private final String desc;

    ResellerWithdrawState(int code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ResellerWithdrawState parse(int code) {
        for (ResellerWithdrawState state : values()) {
            if (state.code == code) {
                return state;
            }
        }
        return null;
    }

    public static boolean isAlreadyWithdraw(ResellerWithdrawState state) {
        return applying.equals(state) || processing.equals(state) || success.equals(state);
    }

    public static boolean isWithdrawFailed(ResellerWithdrawState state){
        return canceled.equals(state) || reject.equals(state);
    }
}
