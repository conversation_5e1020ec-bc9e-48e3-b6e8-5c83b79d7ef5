package com.insta360.store.business.reseller.service;

import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.reseller.dto.ResellerWithdrawQueryDTO;
import com.insta360.store.business.reseller.dto.condition.ResellerWithdrawQueryCondition;
import com.insta360.store.business.reseller.enums.ResellerWithdrawAccountType;
import com.insta360.store.business.reseller.enums.ResellerWithdrawState;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.model.ResellerWithdrawAccount;
import com.insta360.store.business.reseller.model.ResellerWithdrawRecord;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/28
 */
public interface ResellerWithdrawService extends BaseService<ResellerWithdrawRecord> {

    /**
     * 按分销订单获取提现记录
     *
     * @param resellerOrderId
     * @return
     */
    ResellerWithdrawRecord getByResellerOrder(Integer resellerOrderId);

    /**
     * 根据分销商ID获取提现记录
     *
     * @param resellerId
     * @return
     */
    List<ResellerWithdrawRecord> getWithdrawRecordByReseller(Integer resellerId);


    /**
     * 支付提现
     *
     * @param withdrawId
     */
    void payOffWithdraw(Integer withdrawId);

    /**
     * 批量支付提现
     *
     * @param withdrawId
     */
    void batchPayOffWithdraw(List<Integer> withdrawId);;

    /**
     * 分页查询提现记录
     *
     * @param condition
     * @param pageQuery
     * @return
     */
    PageResult<ResellerWithdrawRecord> pageQuery(ResellerWithdrawQueryCondition condition, PageQuery pageQuery);


    /**
     * 按提现状态和提现账户查询提现记录
     *
     * @param withdrawState
     * @param accountType
     * @return
     */
    List<ResellerWithdrawRecord> getWithdrawRecords(ResellerWithdrawState withdrawState, ResellerWithdrawAccountType accountType);

    /**
     * 根据提现记录ID集合查询对应提现记录
     *
     * @param withdrawIds
     * @return
     */
    List<ResellerWithdrawRecord> listByWithdrawIds(List<Integer> withdrawIds);

    /**
     * 根据分销订单ID集合查询对应提现记录
     *
     * @param orderIds
     * @return
     */
    List<ResellerWithdrawRecord> listByOrderIds(List<Integer> orderIds);

    /**
     * 复杂条件查询
     *
     * @param condition
     * @return
     */
    List<ResellerWithdrawRecord> listByComplexCondition(ResellerWithdrawQueryCondition condition);

    /**
     * 根据提现账户ID查询对应提现记录
     *
     * @param resellerWithdrawQueryDto
     * @return
     */
    List<ResellerWithdrawRecord> listByWithdrawAccountId(ResellerWithdrawQueryDTO resellerWithdrawQueryDto);

    /**
     * 分页查询指定分销商提现记录
     *
     * @param resellerAccount
     * @param pageQuery
     * @return
     */
    PageResult<ResellerWithdrawRecord> listWithdrawPageNew(Integer resellerAccount, PageQuery pageQuery);

    /**
     * 批量保存分销商提现记录
     *
     * @param resellerWithdrawRecordList
     * @param resellerOrderList
     * @param resellerWithdrawAccount
     * @param isUpdateWithdrawAccountUsername
     */
    void batchSaveResellerWithdrawRecord(List<ResellerWithdrawRecord> resellerWithdrawRecordList, List<ResellerOrder> resellerOrderList, ResellerWithdrawAccount resellerWithdrawAccount, Boolean isUpdateWithdrawAccountUsername);

    /**
     * 删除提现记录 根据分销订单orderId
     *
     * @param resellerOrderIds 分销订单身份证
     */
    void removeByResellerOrderIds(List<Integer> resellerOrderIds);

}
