package com.insta360.store.business.prime.service;

import com.insta360.compass.core.common.BaseService;
import com.insta360.store.business.prime.bo.PrimeCommodityDomainBO;
import com.insta360.store.business.prime.dto.PrimeCommodityDTO;
import com.insta360.store.business.prime.model.PrimeCommodity;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-06-04
 * @Description:
 */
public interface PrimeCommodityService extends BaseService<PrimeCommodity> {

    void deletePrimeCommodity(PrimeCommodityDTO commodityParam);

    /**
     * 更新prime商品
     *
     * @param commodityParam
     */
    void updatePrimeCommodity(PrimeCommodityDTO commodityParam);

    /**
     * 创建prime商品
     *
     * @param commodityParam
     */
    void createPrimeCommodity(PrimeCommodityDTO commodityParam);

    /**
     * 获取prime套餐信息
     *
     * @param commodityId
     * @return
     */
    PrimeCommodityDomainBO getPrimeDetail(Long commodityId);
}
