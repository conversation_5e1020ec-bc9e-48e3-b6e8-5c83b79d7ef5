package com.insta360.store.business.insurance.dto;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description: care复杂查询dto
 * @author: py
 * @create: 2022-04-26 10:57
 */
public class CareQueryDTO implements Serializable {

    private Integer id;

    /**
     * 区域
     */
    private String area;

    /**
     * 查询的邮箱
     */
    private String email;

    /**
     * 设备序列号
     */
    private String deviceSerial;

    /**
     * 开始日期
     */
    private LocalDateTime fromTime;

    /**
     * 结束日期
     */
    private LocalDateTime toTime;

    /**
     * 保险号
     */
    private String insuranceNumber;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 是否绑定
     */
    private Boolean isBind;

    /**
     * 是否被禁用连接
     */
    private Boolean isDisabled;

    /**
     * 是否是自动激活邮件
     */
    private Boolean isAuto;

    /**
     * 页码数
     */
    private Integer pageNumber;

    /**
     * 每页的大小
     */
    private Integer pageSize;

    /**
     * 后台账户email
     */
    private String adminUserEmail;

    /**
     * 是否启用
     */
    private Boolean insuranceEnabled;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 操作原因
     */
    @NotBlank(message = "操作原因不能为空！")
    private String operationReason;

    public CareQueryDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public LocalDateTime getFromTime() {
        return fromTime;
    }

    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
    }

    public LocalDateTime getToTime() {
        return toTime;
    }

    public void setToTime(LocalDateTime toTime) {
        this.toTime = toTime;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Boolean isBind() {
        return isBind;
    }

    public void setBind(boolean bind) {
        isBind = bind;
    }

    public Boolean isDisabled() {
        return isDisabled;
    }

    public void setDisabled(boolean disabled) {
        isDisabled = disabled;
    }

    public Boolean isAuto() {
        return isAuto;
    }

    public void setAuto(boolean auto) {
        isAuto = auto;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getAdminUserEmail() {
        return adminUserEmail;
    }

    public void setAdminUserEmail(String adminUserEmail) {
        this.adminUserEmail = adminUserEmail;
    }

    public Boolean getInsuranceEnabled() {
        return insuranceEnabled;
    }

    public void setInsuranceEnabled(Boolean insuranceEnabled) {
        this.insuranceEnabled = insuranceEnabled;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public String getOperationReason() {
        return operationReason;
    }

    public void setOperationReason(String operationReason) {
        this.operationReason = operationReason;
    }

    @Override
    public String toString() {
        return "CareQueryDTO{" +
                "id=" + id +
                ", area='" + area + '\'' +
                ", email='" + email + '\'' +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", fromTime=" + fromTime +
                ", toTime=" + toTime +
                ", insuranceNumber='" + insuranceNumber + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", phone='" + phone + '\'' +
                ", isBind=" + isBind +
                ", isDisabled=" + isDisabled +
                ", isAuto=" + isAuto +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", adminUserEmail='" + adminUserEmail + '\'' +
                ", insuranceEnabled=" + insuranceEnabled +
                ", expireTime=" + expireTime +
                ", operationReason='" + operationReason + '\'' +
                '}';
    }
}
