package com.insta360.store.business.integration.yipiaoyun.service.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.store.business.common.HttpRetryFactory;
import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.integration.yipiaoyun.common.InvoiceConstant;
import com.insta360.store.business.integration.yipiaoyun.config.YiPiaoYunConfiguration;
import com.insta360.store.business.integration.yipiaoyun.lib.request.BillingRequest;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreSdkCallRecordService;
import com.insta360.store.business.order.model.Order;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/3/24
 */
@Component
public class YpyHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(YpyHelper.class);

    @Autowired
    private YiPiaoYunConfiguration yiPiaoYunConfiguration;

    @Autowired
    private HttpRetryFactory httpRetryFactory;

    @Autowired
    private StoreSdkCallRecordService storeSdkCallRecordService;

    /**
     * 易票云异步开票
     *
     * @param request
     */
    @GrafanaDataStats(
            statisticsType = {GrafanaStatisticsType.TIMER, GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE},
            businessType = GrafanaBusinessType.EXTERNAL_CALL,
            keyType = GrafanaKeyType.EXTERNAL_CALL_YPY
    )
    public void billing(BillingRequest request, Order order) {
        try {
            // 设置重试
            String result = httpRetryFactory.getRetryerBuilder().call(new YiPiaoYunInvoiceRetryCallable(request, yiPiaoYunConfiguration, storeSdkCallRecordService));
            JSONObject jsonObject = JSON.parseObject(result, JSONObject.class);

            if (StringUtils.isBlank(result) || jsonObject == null || "F".equals(jsonObject.getString("operateCode"))) {
                // 推送飞书告警
                String msg = "[商城自动开票]通知易票云开票失败请及时人工处理开票！订单号:" + request.getOrderNo() + " 失败原因：" + (StringUtils.isBlank(result) ? "第三方未响应" : jsonObject.getString("message"));
                sendRobotMsg(order, msg);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("[商城自动开票]系统异常!req:%s", JSON.toJSONString(request)), e);
            String msg = "[商城自动开票]系统内部异常开票失败请及时人工处理开票！订单号:" + request.getOrderNo();
            sendRobotMsg(order, msg);
        }
    }

    /**
     * 发送告警群消息
     *
     * @param order
     * @param msg
     */
    private void sendRobotMsg(Order order, String msg) {
        if (order.isRepairOrder()) {
            FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.RmaInvoice, InvoiceConstant.WWW_AT_USERS);
        } else {
            FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.AutoInvoice, InvoiceConstant.STORE_AT_USERS);
        }
    }
}
