package com.insta360.store.business.integration.wto.oms.dto;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 订单售后商品退货入库通知DTO
 * @Date 2024/11/30
 */
public class OrderProductReturnNotificationDTO implements Serializable {

    /**
     * 商城订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNumber;

    /**
     * oms退换货单号
     */
    @NotBlank(message = "oms退换货单号不能为空")
    private String omsRefundNumber;

    /**
     * 商城售后单号
     */
    @NotBlank(message = "商城售后单号不能为空")
    private String rmaNumber;

    /**
     * 售后订单商品ID
     */
    private Integer orderItemId;

    /**
     * 售后商品skuCode(料号)
     */
    @NotBlank(message = "售后商品skuCode(料号)不能为空")
    private String skuCode;

    /**
     * 售后商品退货数量
     */
    @NotNull(message = "售后商品退货数量不能为空")
    @Min(value = 1, message = "售后商品退货数量不能小于1")
    private Integer returnQuantity;

    /**
     * 售后商品入库状态
     */
    @NotNull(message = "售后商品退货入库状态不能为空")
    private Integer returnStockState;

    /**
     * 商品退货入库详情列表
     */
    private List<ReturnItemDetail> details;

    /**
     * 商品退货入库详情
     */
    public static class ReturnItemDetail implements Serializable {

        /**
         * 组合商品对应的子商品skuCode(料号)
         */
        private String subSkuCode;

        /**
         * 组合商品对应的子商品退回入库数量
         */
        private Integer returnStockQuantity;

        /**
         * 主机序列号
         */
        private List<String> cameraSerialNumbers;

        public String getSubSkuCode() {
            return subSkuCode;
        }

        public void setSubSkuCode(String subSkuCode) {
            this.subSkuCode = subSkuCode;
        }

        public Integer getReturnStockQuantity() {
            return returnStockQuantity;
        }

        public void setReturnStockQuantity(Integer returnStockQuantity) {
            this.returnStockQuantity = returnStockQuantity;
        }

        public List<String> getCameraSerialNumbers() {
            return cameraSerialNumbers;
        }

        public void setCameraSerialNumbers(List<String> cameraSerialNumbers) {
            this.cameraSerialNumbers = cameraSerialNumbers;
        }

        @Override
        public String toString() {
            return "ReturnItemDetail{" +
                    "subSkuCode='" + subSkuCode + '\'' +
                    ", returnStockQuantity=" + returnStockQuantity +
                    ", cameraSerialNumbers=" + cameraSerialNumbers +
                    '}';
        }
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getOmsRefundNumber() {
        return omsRefundNumber;
    }

    public void setOmsRefundNumber(String omsRefundNumber) {
        this.omsRefundNumber = omsRefundNumber;
    }

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public Integer getReturnQuantity() {
        return returnQuantity;
    }

    public void setReturnQuantity(Integer returnQuantity) {
        this.returnQuantity = returnQuantity;
    }

    public Integer getReturnStockState() {
        return returnStockState;
    }

    public void setReturnStockState(Integer returnStockState) {
        this.returnStockState = returnStockState;
    }

    public List<ReturnItemDetail> getDetails() {
        return details;
    }

    public void setDetails(List<ReturnItemDetail> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "OrderProductReturnNotificationDTO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", omsRefundNumber='" + omsRefundNumber + '\'' +
                ", rmaNumber='" + rmaNumber + '\'' +
                ", orderItemId=" + orderItemId +
                ", skuCode='" + skuCode + '\'' +
                ", returnQuantity=" + returnQuantity +
                ", returnStockState=" + returnStockState +
                ", details=" + details +
                '}';
    }
}
