package com.insta360.store.business.integration.wto.enums;

/**
 * @Author: wkx
 * @Date: 2024/11/29
 * @Description:
 */
public enum OmsIntegrationBusinessType {

    /**
     * 商城推单至oms
     */
    STORE_PUSH_ORDER("store_push_order", "商城推单至oms"),

    /**
     * 商城订单发货（oms -> 商城）
     */
    STORE_ORDER_DELIVERY("store_order_delivery", "订单发货（oms -> 商城）"),

    /**
     * 商城售后单同步oms（仅退款、退货退款）
     */
    STORE_RMA_REFUND_ORDER_INFO_SYNC("store_rma_refund_order_info_sync", "商城售后单同步oms（仅退款、退货退款）"),

    /**
     * 商城售后单同步oms（换货）
     */
    STORE_RMA_CHANGE_ORDER_INFO_SYNC("store_rma_change_order_info_sync", "商城售后单同步oms（换货）"),

    /**
     * 商城售后单退款货物拦截通知
     */
    OMS_REFUND_INTERCEPT_NOTIFY("oms_refund_intercept_notify", "oms售后单退款货物拦截通知"),

    /**
     * 商城退货商品入库回传通知
     */
    OMS_RETURN_ITEM_STOCK_NOTIFY("oms_return_item_stock_notify", "oms退货商品入库回传通知"),

    /**
     * 商城运营后台操作业务
     */
    STORE_ADMIN_HANDLER("store_admin_handler", "商城运营后台操作业务"),

    /**
     * 商城组合套餐料号查询
     */
    STORE_BUNDLE_COMMODITY_CODE_QUERY("store_bundle_commodity_code_query", "商城组合套餐料号查询"),

    /**
     * oms获取商品库存
     */
    OMS_SKU_STOCK_GET("oms_sku_stock_get", "oms获取商品库存"),

    /**
     * oms sku库存同步回调
     */
    OMS_SKU_STOCK_SYNC_CALLBACK("oms_sku_stock_sync_callback", "oms sku库存同步回调"),

    /**
     * 商城订单发货物流轨迹变更回调
     */
    STORE_ORDER_DELIVERY_LOGISTICS_CHANGE_CALLBACK("store_order_delivery_logistics_change_callback", "商城订单发货物流轨迹变更回调"),

    /**
     * 商城sku编码同步
     */
    STORE_SKU_CODE_SYNC("store_sku_code_sync", "商城sku编码同步"),
    ;

    /**
     * 名称
     */
    private final String nameEn;

    /**
     * 描述
     */
    private final String detail;

    OmsIntegrationBusinessType(String nameEn, String detail) {
        this.nameEn = nameEn;
        this.detail = detail;
    }

    public String getNameEn() {
        return nameEn;
    }

    public String getDetail() {
        return detail;
    }
}
