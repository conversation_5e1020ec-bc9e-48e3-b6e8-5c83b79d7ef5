package com.insta360.store.business.admin.order.enums;

/**
 * 物流限制类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/24
 */
public enum LogisticsRestrictionType {

    /**
     * 不受限制
     */
    NotRestriction(0, "无需限制"),

    /**
     * 危险限制
     */
    DangerRestriction(1, "限制危险物流"),

    /**
     * 禁止危险限制
     */
    ProhibitedDangerousRestriction(2, "禁止危险品"),
    ;

    /**
     * 限制状态码
     */
    private final Integer code;

    /**
     * 限制名称
     */
    private final String name;

    /**
     * 根据状态码获取枚举
     *
     * @param code 代码
     * @return {@link LogisticsRestrictionType}
     */
    public static LogisticsRestrictionType fromCode(Integer code) {
        for (LogisticsRestrictionType restrictionType : values()) {
            if (restrictionType.getCode().equals(code)) {
                return restrictionType;
            }
        }
        return null;
    }

    LogisticsRestrictionType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
