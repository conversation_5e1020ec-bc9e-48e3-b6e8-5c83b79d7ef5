package com.insta360.store.business.trade.dto;

import com.insta360.store.business.configuration.validation.annotation.MinIntegerNumber;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 订单运费dto
 *
 * <AUTHOR>
 * @date 2023/08/15
 */
public class ShippingFeeDTO implements Serializable {

    /**
     * 套餐ID
     */
    @NotNull(message = "套餐ID不能为空")
    private Integer commodityId;

    /**
     * 数量
     */
    @MinIntegerNumber
    private Integer number;

    /**
     * 绑定服务
     */
    @Valid
    private List<BinsServices> bindServices;

    /**
     * 绑定服务
     */
    public static class BinsServices {

        /**
         * 套餐ID
         */
        @NotNull(message = "套餐ID不能为空")
        private Integer commodityId;

        /**
         * 数量
         */
        @MinIntegerNumber
        private Integer number;

        public Integer getCommodityId() {
            return commodityId;
        }

        public void setCommodityId(Integer commodityId) {
            this.commodityId = commodityId;
        }

        public Integer getNumber() {
            return number;
        }

        public void setNumber(Integer number) {
            this.number = number;
        }

        @Override
        public String toString() {
            return "BinsServices{" +
                    "commodityId=" + commodityId +
                    ", number=" + number +
                    '}';
        }
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public List<BinsServices> getBindServices() {
        return bindServices;
    }

    public void setBindServices(List<BinsServices> bindServices) {
        this.bindServices = bindServices;
    }

    @Override
    public String toString() {
        return "ShippingFeeDTO{" +
                "commodityId=" + commodityId +
                ", number=" + number +
                '}';
    }
}
