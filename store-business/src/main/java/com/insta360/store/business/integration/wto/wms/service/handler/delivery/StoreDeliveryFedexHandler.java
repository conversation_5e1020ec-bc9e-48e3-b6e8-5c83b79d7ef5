package com.insta360.store.business.integration.wto.wms.service.handler.delivery;


import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.aliyun.oss.OSSService;
import com.insta360.store.business.admin.order.service.impl.handler.FedexVatExportHandler;
import com.insta360.store.business.integration.fedex.bo.FedexApiContextBO;
import com.insta360.store.business.integration.fedex.enums.FedexDocumentContentType;
import com.insta360.store.business.integration.fedex.enums.FedexLogisticsTimeType;
import com.insta360.store.business.integration.fedex.exception.FedexApiErrorCode;
import com.insta360.store.business.integration.fedex.lib.module.PieceDocument;
import com.insta360.store.business.integration.fedex.lib.module.TransactionShipment;
import com.insta360.store.business.integration.fedex.lib.request.FedexShipmentFileDownloadRequest;
import com.insta360.store.business.integration.fedex.service.handler.FedexCheckHandler;
import com.insta360.store.business.integration.fedex.service.handler.FedexShipmentHandler;
import com.insta360.store.business.integration.wto.wms.bo.WmsDeliveryDocumentResultBO;
import com.insta360.store.business.integration.wto.wms.bo.WmsExecuteBO;
import com.insta360.store.business.integration.wto.wms.bo.WmsOrderDeliveryCreateShipmentBO;
import com.insta360.store.business.integration.wto.wms.exception.WmsErrorCode;
import com.insta360.store.business.integration.wto.wms.lib.request.WmsDeliveryCreateShipmentNotifyRequest;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2025-01-14 14:02
 */
@Scope("prototype")
@Component
public class StoreDeliveryFedexHandler extends BaseStoreDeliveryService {

    public static final Logger LOGGER = LoggerFactory.getLogger(StoreDeliveryFedexHandler.class);

    @Autowired
    FedexCheckHandler fedexCheckHandler;

    @Autowired
    FedexVatExportHandler fedexVatExportHandler;

    @Autowired
    FedexShipmentHandler fedexShipmentHandler;

    @Autowired
    OSSService ossService;

    @Override
    protected void doExecuteWmsTransaction(WmsExecuteBO wmsExecuteBo) {
        WmsOrderDeliveryCreateShipmentBO orderDeliveryCreateShipmentParam = wmsExecuteBo.getOrderDeliveryCreateShipmentParam();

        // 前置校验-单个订单校验
        fedexCheckHandler.orderCheck(Collections.singletonList(orderDeliveryCreateShipmentParam.getOrderNumber()));
        List<WmsDeliveryDocumentResultBO> wmsDeliveryDocumentResultBos = this.listShipmentDocumentResult(wmsExecuteBo);
        if (CollectionUtils.isEmpty(wmsDeliveryDocumentResultBos)) {
            throw new InstaException(WmsErrorCode.CreateShipmentFailedException);
        }

        // 返回运单信息通知WMS
        for (WmsDeliveryDocumentResultBO fedexDocumentResultParam : wmsDeliveryDocumentResultBos) {
            WmsDeliveryCreateShipmentNotifyRequest wmsRequest = getWmsRequest(fedexDocumentResultParam);
            String responseBody = wmsRequest.executePost();
            LOGGER.info(String.format("[WMS创建运单]FEDEX托运信息:该批订单[%s],请求参数:[%s],回传WMS结果[%s]", orderDeliveryCreateShipmentParam.getOrderNumber(), wmsRequest, responseBody));
        }
    }

    @Override
    protected List<WmsDeliveryDocumentResultBO> listShipmentDocumentResult(WmsExecuteBO wmsExecuteBo) {
        WmsOrderDeliveryCreateShipmentBO orderDeliveryCreateShipmentParam = wmsExecuteBo.getOrderDeliveryCreateShipmentParam();
        List<String> orderNumberList = Collections.singletonList(orderDeliveryCreateShipmentParam.getOrderNumber());
        List<Order> orderList = orderService.listByOrderNumber(orderNumberList);
        if (CollectionUtils.isEmpty(orderList)) {
            throw new InstaException(WmsErrorCode.OrderNumberNotExistException);
        }

        // Fedex 物流时效类型
        String logisticsTimeType = orderDeliveryCreateShipmentParam.getLogisticsTimeType();
        FedexLogisticsTimeType fedexLogisticsTimeType = FedexLogisticsTimeType.parse(logisticsTimeType);
        if (Objects.isNull(fedexLogisticsTimeType)) {
            // 失败信息返回
            throw new InstaException(WmsErrorCode.ParameterNotCorrectException);
        }

        // 获取Fedex托运单所需数据
        List<FedexApiContextBO> fedexApiContextList = fedexVatExportHandler.listFedexVatContext(orderNumberList);
        if (CollectionUtils.isEmpty(fedexApiContextList)) {
            LOGGER.error(String.format("[WMS创建运单]Fedex托运:该批订单[%s],构建Fedex托运所需上下文数据时,出现异常", orderNumberList));
            FeiShuMessageUtil.storeGeneralMessage(String.format("[WMS创建运单]Fedex托运:该批订单[%s],构建Fedex托运所需上下文数据时,出现异常", orderNumberList), FeiShuGroupRobot.ShippingFedex, FeiShuAtUser.LB, FeiShuAtUser.ZJW, FeiShuAtUser.LJC);
            throw new InstaException(WmsErrorCode.FedexContentBuildException);
        }

        // 批量执行Fedex运单创建
        return fedexApiContextList.stream()
                .map(fedexApiContext -> {
                    List<TransactionShipment> shipmentList = fedexShipmentHandler.createShipment(fedexApiContext, fedexLogisticsTimeType);
                    if (CollectionUtils.isEmpty(shipmentList)) {
                        FeiShuMessageUtil.storeGeneralMessage(String.format("[WMS创建运单]Fedex托运:该批订单[%s],构建Fedex托运所需上下文数据时,出现异常", orderNumberList), FeiShuGroupRobot.ShippingFedex, FeiShuAtUser.LB, FeiShuAtUser.ZJW, FeiShuAtUser.LJC);
                        throw new InstaException(FedexApiErrorCode.FEDEX_CONTEXT_BUILD_EXCEPTION);
                    }
                    // 交易发货信息
                    TransactionShipment transactionShipment = shipmentList.get(0);
                    // 物流单号
                    String masterTrackingNumber = transactionShipment.getMasterTrackingNumber();
                    String orderSpliceNumber = fedexApiContext.getOrderSpliceNumber();
                    // 转运联相关信息
                    String labelUrl = getLabelUrl(transactionShipment, masterTrackingNumber, orderSpliceNumber);
                    return new WmsDeliveryDocumentResultBO(orderSpliceNumber, masterTrackingNumber, labelUrl);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取转运联pdf
     *
     * @param transactionShipment
     * @param masterTrackingNumber
     * @return
     */
    private String getLabelUrl(TransactionShipment transactionShipment, String masterTrackingNumber, String orderSpliceNumber) {
        List<PieceDocument> pieceResponses = transactionShipment.getPieceResponses();
        // 运单文档OSS地址
        if (CollectionUtils.isEmpty(pieceResponses)) {
            LOGGER.error(String.format("[WMS创建运单]Fedex托运:该批订单[%s],创建运单后信息为空", orderSpliceNumber));
            FeiShuMessageUtil.storeGeneralMessage(String.format("[WMS创建运单]Fedex托运:该批订单[%s],创建运单后信息为空", orderSpliceNumber), FeiShuGroupRobot.ShippingDhl, FeiShuAtUser.LB, FeiShuAtUser.ZJW, FeiShuAtUser.LJC);
            return null;
        }

        List<PieceDocument.PackageDocument> packageDocuments = pieceResponses.stream()
                .findFirst()
                .get()
                .getPackageDocuments();

        return Optional.ofNullable(packageDocuments)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(list -> list.stream()
                        .filter(packageDocument -> FedexDocumentContentType.LABEL.getType().equals(packageDocument.getContentType()))
                        .findFirst()
                        .map(pieceDocument -> this.uploadOss(
                                new FedexShipmentFileDownloadRequest(pieceDocument.getUrl()).executeGetFile(),
                                this.getFileName(masterTrackingNumber, FedexDocumentContentType.LABEL.getValue())
                        ))
                )
                .orElse(null);
    }
}
