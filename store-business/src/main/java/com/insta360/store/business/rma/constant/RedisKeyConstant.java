package com.insta360.store.business.rma.constant;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/5/21 下午4:46
 */
public class RedisKeyConstant {

    /**
     * 退款缓存key前缀
     */
    public static final String RMA_REFUND_PRE_FIX = "rma:refund:";

    /**
     * 退货缓存key前缀
     */
    public static final String RMA_RETURN_PRE_FIX = "rma:return:";

    /**
     * 换货缓存key前缀
     */
    public static final String RMA_REPLACEMENT_PRE_FIX = "rma:replacement:";

    /**
     * 退款缓存key过期时间（单位：小时）
     */
    public static final Long RMA_REFUND_EXPIRE = 18L;

    /**
     * 退货缓存key过期时间（单位：小时）
     */
    public static final Long RMA_RETURN_EXPIRE = 24L;

    /**
     * 换货缓存key过期时间（单位：小时）
     */
    public static final Long RMA_REPLACEMENT_EXPIRE = 24L;
}
