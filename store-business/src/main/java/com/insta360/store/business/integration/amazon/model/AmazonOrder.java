package com.insta360.store.business.integration.amazon.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.bo.ToGuanyiOrder;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2020-05-09
 * @Description:
 */
@TableName("amazon_order")
public class AmazonOrder extends BaseModel<AmazonOrder> implements ToGuanyiOrder {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @JSONField(name = "order_number")
    private String orderNumber;

    private String zone;

    private String company;

    private String name;

    @JSONField(name = "country_code")
    private String countryCode;

    private String country;

    private String province;

    private String city;

    private String address;

    @JSONField(name = "zip_code")
    private String zipCode;

    private String phone;

    @JSONField(name = "sales_channel")
    private String salesChannel;

    @JSONField(name = "fulfillment_channel")
    private String fulfillmentChannel;

    @JSONField(name = "purchase_time")
    private LocalDateTime purchaseTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSalesChannel() {
        return salesChannel;
    }

    public void setSalesChannel(String salesChannel) {
        this.salesChannel = salesChannel;
    }

    public String getFulfillmentChannel() {
        return fulfillmentChannel;
    }

    public void setFulfillmentChannel(String fulfillmentChannel) {
        this.fulfillmentChannel = fulfillmentChannel;
    }

    public LocalDateTime getPurchaseTime() {
        return purchaseTime;
    }

    public void setPurchaseTime(LocalDateTime purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    @Override
    public String toString() {
        return "AmazonOrder{" +
                "id=" + id +
                ", orderNumber='" + orderNumber + '\'' +
                ", zone='" + zone + '\'' +
                ", company='" + company + '\'' +
                ", name='" + name + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", address='" + address + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", phone='" + phone + '\'' +
                ", salesChannel='" + salesChannel + '\'' +
                ", fulfillmentChannel='" + fulfillmentChannel + '\'' +
                ", purchaseTime=" + purchaseTime +
                '}';
    }

    @Override
    public String orderIdInGuanyi() {
        return getOrderNumber();
    }

    public InstaCountry country() {
        return InstaCountry.parse(country);
    }
}