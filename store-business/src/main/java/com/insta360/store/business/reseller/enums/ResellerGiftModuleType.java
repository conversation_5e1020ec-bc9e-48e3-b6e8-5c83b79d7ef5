package com.insta360.store.business.reseller.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 分销赠品模块类型
 * @Date 2022/7/28
 */
public enum ResellerGiftModuleType {
    UNKNOWN(0,"未知"),
    DEFAULT(1,"默认"),
    CUSTOM_MADE(2,"定制");

    public final int type;

    public final String value;

    ResellerGiftModuleType(int type, String value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 匹配
     * @param type
     * @return
     */
    public static ResellerGiftModuleType matchType(Integer type) {
        if(Objects.isNull(type)) {
            return UNKNOWN;
        }
        for (ResellerGiftModuleType moduleType : ResellerGiftModuleType.values()) {
            if(moduleType.type == type) {
                return moduleType;
            }
        }
        return UNKNOWN;
    }

    /**
     * 是否存在
     * @param moduleType
     * @return
     */
    public static boolean isExist(ResellerGiftModuleType moduleType) {
        return Objects.nonNull(moduleType) && !UNKNOWN.equals(moduleType);
    }
}
