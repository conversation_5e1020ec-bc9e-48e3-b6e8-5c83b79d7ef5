package com.insta360.store.business.trade.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2020/11/26
 * @Description:
 */
public class RealEstateSoftwareDTO implements Serializable {

    @J<PERSON><PERSON>ield(name = "order_number")
    private String orderNumber;

    private String software;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getSoftware() {
        return software;
    }

    public void setSoftware(String software) {
        this.software = software;
    }

    @Override
    public String toString() {
        return "RealEstateSoftwareDTO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", software='" + software + '\'' +
                '}';
    }
}
