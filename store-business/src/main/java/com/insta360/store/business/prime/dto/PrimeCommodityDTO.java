package com.insta360.store.business.prime.dto;

import com.google.common.collect.Lists;
import com.insta360.store.business.prime.constants.PrimeConstants;
import com.insta360.store.business.prime.enums.PrimeCommodityType;
import com.insta360.store.business.prime.model.PrimeCommodity;
import com.insta360.store.business.prime.model.PrimeCommodityInclude;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Prime商品创建DTO
 * <p>
 * 用于创建Prime商品的数据传输对象，支持两种商品类型：Individual和Bundle。
 * Individual类型为单个商品，Bundle类型为组合商品套餐。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
public class PrimeCommodityDTO implements Serializable {

    public PrimeCommodityDTO() {
    }

    public PrimeCommodityDTO(Long commodityId, String amazonSku, Boolean offerPrime, List<IncludeCommodity> includeCommodities, String productDetailPageUrl, PrimeCommodityType primeCommodityType) {
        this.commodityId = commodityId;
        this.amazonSku = amazonSku;
        this.offerPrime = offerPrime;
        this.includeCommodities = includeCommodities;
        this.productDetailPageUrl = productDetailPageUrl;
        this.primeCommodityType = primeCommodityType;
    }

    /**
     * 商品ID
     * <p>
     * 对于Individual类型，Prime SKU默认与套餐ID一致，不支持更改。
     * 对于Bundle类型，Prime SKU默认取套餐ID拼接"-bundle"，不支持更改。
     * </p>
     */
    @NotNull
    private Long commodityId;

    /**
     * Amazon SKU
     * <p>
     * 必填字段，用于将Prime SKU与Amazon SKU关联，指定FC履约实物。
     * </p>
     */
    private String amazonSku;

    /**
     * 是否提供Prime服务
     * <p>
     * 必选字段，默认禁用，支持启用或禁用两种状态。
     * 对于Individual和Bundle类型商品都适用。
     * </p>
     */
    private Boolean offerPrime;

    /**
     * 包含的商品列表
     * <p>
     * 仅在Prime商品类型为Bundle时必传。
     * 选项仅包含已在Prime创建为Individual类型的商品套餐。
     * 每个套餐默认数量为1，可更改。
     * 总套餐项不允许超过10个，当配置满10个时将隐藏新增按钮。
     * </p>
     */
    @Size(max = 10, min = 1)
    private List<IncludeCommodity> includeCommodities;

    /**
     * 产品详情页URL
     * <p>
     * 产品在电商平台上的详情页链接。
     * </p>
     */
    @NotBlank
    private String productDetailPageUrl;

    /**
     * Prime商品类型
     * <p>
     * 必选字段，可选值为Individual或Bundle。
     * Individual：单个商品。
     * Bundle：组合商品套餐。
     * </p>
     */
    private PrimeCommodityType primeCommodityType;

    private Boolean reBuild = Boolean.FALSE;

    public String externalId() {
        if (PrimeCommodityType.Bundle.equals(primeCommodityType)) {
            return commodityId + PrimeConstants.Commodity.BUNDLE_SUFFIX;
        } else {
            return commodityId.toString();
        }
    }

    public PrimeCommodity buildPrimeCommodity(String primeProductId) {
        PrimeCommodity primeCommodity = new PrimeCommodity();
        primeCommodity.setCommodityId(commodityId);
        primeCommodity.setAmazonSku(amazonSku);
        primeCommodity.setPrimeProductId(primeProductId);
        primeCommodity.setOfferPrime(offerPrime);
        primeCommodity.setPrimeCommodityType(primeCommodityType.getCode());
        primeCommodity.setCreateTime(LocalDateTime.now());
        primeCommodity.setUpdateTime(LocalDateTime.now());
        return primeCommodity;
    }

    public List<PrimeCommodityInclude> buildPrimeCommdotiyIncludeList(Long primeCommodityId) {
        if (CollectionUtils.isEmpty(includeCommodities)) {
            return Lists.newArrayList();
        }
        return includeCommodities.stream().map(includeCommodity -> {
            PrimeCommodityInclude primeCommodityInclude = new PrimeCommodityInclude();
            primeCommodityInclude.setPrimeCommodityId(primeCommodityId);
            primeCommodityInclude.setCommodityId(includeCommodity.getCommodityId());
            primeCommodityInclude.setQuantity(includeCommodity.getQuantity());
            primeCommodityInclude.setCreateTime(LocalDateTime.now());
            primeCommodityInclude.setUpdateTime(LocalDateTime.now());
            return primeCommodityInclude;
        }).collect(Collectors.toList());
    }

    public Long getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public String getAmazonSku() {
        return amazonSku;
    }

    public void setAmazonSku(String amazonSku) {
        this.amazonSku = amazonSku;
    }

    public Boolean getOfferPrime() {
        return offerPrime;
    }

    public void setOfferPrime(Boolean offerPrime) {
        this.offerPrime = offerPrime;
    }

    public List<IncludeCommodity> getIncludeCommodities() {
        return includeCommodities;
    }

    public void setIncludeCommodities(List<IncludeCommodity> includeCommodities) {
        this.includeCommodities = includeCommodities;
    }

    public String getProductDetailPageUrl() {
        return productDetailPageUrl;
    }

    public void setProductDetailPageUrl(String productDetailPageUrl) {
        this.productDetailPageUrl = productDetailPageUrl;
    }

    public PrimeCommodityType getPrimeCommodityType() {
        return primeCommodityType;
    }

    public void setPrimeCommodityType(PrimeCommodityType primeCommodityType) {
        this.primeCommodityType = primeCommodityType;
    }

    public Boolean getReBuild() {
        return reBuild;
    }

    public void setReBuild(Boolean reBuild) {
        this.reBuild = reBuild;
    }

    /**
     * 包含商品项
     * <p>
     * 用于描述Bundle类型商品中包含的具体商品项及其数量。
     * </p>
     */
    public static class IncludeCommodity {

        /**
         * 商品ID
         * <p>
         * 已在Prime创建为Individual类型的商品ID。
         * </p>
         */
        private Integer commodityId;

        /**
         * 数量
         * <p>
         * 每个套餐的数量，默认为1，可更改。
         * 允许输入1-100之间的整数。
         * </p>
         */
        private Integer quantity;

        public IncludeCommodity() {
        }

        public IncludeCommodity(Integer commodityId, Integer quantity) {
            this.commodityId = commodityId;
            this.quantity = quantity;
        }

        public Integer getCommodityId() {
            return commodityId;
        }

        public void setCommodityId(Integer commodityId) {
            this.commodityId = commodityId;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }

}
