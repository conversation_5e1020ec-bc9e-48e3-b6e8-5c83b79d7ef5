package com.insta360.store.business.payment.lib.ocean.request.store;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.insta360.store.business.payment.lib.ocean.config.OceanPaymentConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2024/05/21
 * @Description:
 */
public class CreateADJCHKInternationalSubscribePaymentRequest extends BaseOceanSubscribeRequest {

    public CreateADJCHKInternationalSubscribePaymentRequest(OceanPaymentConfig oceanPaymentConfig) {
        super(oceanPaymentConfig, METHOD);
    }

    @Override
    protected Map<String, Object> getRequestBodyAndRemoveOtherParam() {
        JSONObject requestBodyJson = JSONObject.parseObject(JSON.toJSONString(this));
        requestBodyJson.remove("requestUrl");
        requestBodyJson.remove("card_data");

        Map<String, Object> body = new HashMap<>(requestBodyJson.size());
        for (Map.Entry<String, Object> entry : requestBodyJson.entrySet()) {
            body.put(entry.getKey(), entry.getValue());
        }

        // 记录必要信息
        savePaymentInfo();
        return body;
    }
}
