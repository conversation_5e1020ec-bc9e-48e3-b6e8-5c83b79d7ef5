package com.insta360.store.business.outgoing.mq.trade.helper;

import com.insta360.store.business.outgoing.mq.trade.dto.UserCartBusinessMessageDTO;
import com.insta360.store.business.outgoing.mq.trade.dto.UserCartMessageDTO;
import com.insta360.store.business.outgoing.mq.trade.sender.UserCartBusinessSendMq;
import com.insta360.store.business.outgoing.mq.trade.sender.UserCartSendMq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2022/04/13
 * @Description:
 */
@Component
public class UserCartMessageHelper {

    @Autowired
    UserCartSendMq userCartSendMq;

    @Autowired
    UserCartBusinessSendMq userCartBusinessSendMq;

    /**
     * 发送购物车促销邮件
     *
     * @param userCartMessageDto 用户购物车dto
     */
    public void sendUserCartEmailMessage(UserCartMessageDTO userCartMessageDto) {
        userCartSendMq.sendUserCartEmailMessage(userCartMessageDto);
    }

    /**
     * 发送更新用户购物车信息
     *
     * @param userCartMessageDto 用户购物车dto
     */
    public void sendUpdateUserCartMessage(UserCartBusinessMessageDTO userCartMessageDto) {
        userCartBusinessSendMq.sendUpdateUserCartMessage(userCartMessageDto);
    }

}
