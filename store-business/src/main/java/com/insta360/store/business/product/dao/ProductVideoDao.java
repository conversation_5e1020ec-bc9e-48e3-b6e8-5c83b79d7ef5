package com.insta360.store.business.product.dao;

import com.insta360.compass.core.common.BaseDao;
import com.insta360.store.business.configuration.cache.mybatis.MybatisRedisCache;
import com.insta360.store.business.product.model.ProductVideo;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
@CacheNamespace(implementation = MybatisRedisCache.class, eviction = MybatisRedisCache.class)
public interface ProductVideoDao extends BaseDao<ProductVideo> {

    /**
     * 批量更新序号
     *
     * @param productVideos
     */
    void updateOrderIndexByIds(@Param("productVideos") List<ProductVideo> productVideos);

    /**
     * 批量更新全部的数据
     *
     * @param productVideos
     */
    void updateDataByIds(@Param("productVideos") List<ProductVideo> productVideos);
}
