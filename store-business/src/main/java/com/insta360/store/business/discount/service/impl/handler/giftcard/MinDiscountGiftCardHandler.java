package com.insta360.store.business.discount.service.impl.handler.giftcard;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.commodity.bo.GiftItem;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.discount.dto.bo.DiscountCalculateResultBO;
import com.insta360.store.business.discount.dto.bo.DiscountCheckResult;
import com.insta360.store.business.discount.enums.PolicyRuleType;
import com.insta360.store.business.discount.exception.StoreDiscountErrorCode;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.model.GiftCardPolicyGift;
import com.insta360.store.business.discount.model.GiftCardPolicyItem;
import com.insta360.store.business.discount.service.impl.handler.context.GiftCardContext;
import com.insta360.store.business.discount.service.impl.handler.dto.DiscountCalculationDTO;
import com.insta360.store.business.discount.service.impl.handler.dto.TradeCalculateDTO;
import com.insta360.store.business.discount.service.impl.handler.dto.TradeDiscountGiftItemQueryDTO;
import com.insta360.store.business.order.bo.OrderSheet;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 套餐维度不可叠加 -代金券
 * @Date 2022/4/20
 */
@Scope("prototype")
@Component
public class MinDiscountGiftCardHandler extends BaseGiftCardCalculateHandler {

    private static final Logger logger = LoggerFactory.getLogger(MinDiscountGiftCardHandler.class);


    /**
     * 初始化
     *
     * @param giftCard
     */
    @Override
    public void init(GiftCard giftCard) {
        super.init(giftCard);
    }

    @Override
    public void thresholdCheck(DiscountCalculationDTO discountCalculationDto) {
        super.commodityDimensionRuleCheck(discountCalculationDto);
    }

    @Override
    public TradeCalculateDTO policyCheck(DiscountCalculationDTO discountCalculationDto) {
        TradeCalculateDTO tradeCalculateDTO = matchCommodityDimensionPolicy(discountCalculationDto.getOrderItems(), discountCalculationDto.getInstaCountry());
        if (Objects.isNull(tradeCalculateDTO) || CollectionUtils.isEmpty(tradeCalculateDTO.getTradeOrderItemPolicyList())) {
            throw new InstaException(StoreDiscountErrorCode.InvalidTradeCodeException);
        }

        return tradeCalculateDTO;
    }

    @Override
    public List<GiftItem> getDiscountGiftItem(TradeDiscountGiftItemQueryDTO tradeDiscountGiftItemQueryDTO) {
        List<GiftCardContext.GiftCardPolicyBean> policyList = discountContext.getPolicyList();
        if (CollectionUtils.isEmpty(policyList)) {
            return Lists.newArrayList();
        }
        List<OrderSheet.SheetItem> sheetItems = tradeDiscountGiftItemQueryDTO.getSheetItems();
        if (CollectionUtils.isEmpty(sheetItems)) {
            return Lists.newArrayList();
        }

        //筛选出存在赠品赠送规则的政策
        policyList = policyList.stream()
                .filter(
                        giftCardPolicyBean ->
                                CollectionUtils.isNotEmpty(giftCardPolicyBean.getGiftCardPolicyGiftList())
                )
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(policyList)) {
            return Lists.newArrayList();
        }

        //如何政策优惠规则的下单商品
        Map<Integer, List<OrderSheet.SheetItem>> policySheetItemMap = Maps.newHashMap();
        for (OrderSheet.SheetItem sheetItem : sheetItems) {
            for (GiftCardContext.GiftCardPolicyBean giftCardPolicyBean : policyList) {
                PolicyRuleType policyRuleType = PolicyRuleType.matchCode(giftCardPolicyBean.getPolicyRuleType());
                List<Integer> productCommodityIdList = giftCardPolicyBean.getGiftCardPolicyItemList().stream().map(GiftCardPolicyItem::getProductCommodityId).collect(Collectors.toList());
                switch (policyRuleType) {
                    case ASSIGN_EFFECT_PRODUCT:
                        List<Commodity> commodities = commodityService.getCommodities(productCommodityIdList);
                        if (CollectionUtils.isEmpty(commodities)) {
                            continue;
                        }
                        productCommodityIdList = commodities.stream().map(Commodity::getId).collect(Collectors.toList());
                        if (productCommodityIdList.contains(sheetItem.getCommodityId())) {
                            buildPolicySheetItemMap(policySheetItemMap, sheetItem, giftCardPolicyBean);
                        }
                        continue;

                    case ASSIGN_EFFECT_COMMODITY:
                        if (productCommodityIdList.contains(sheetItem.getCommodityId())) {
                            buildPolicySheetItemMap(policySheetItemMap, sheetItem, giftCardPolicyBean);
                        }
                        continue;

                    default:
                        continue;
                }
            }
        }

        if (MapUtils.isEmpty(policySheetItemMap)) {
            return Lists.newArrayList();
        }

        Map<Integer, Integer> policyGiftItemMap = Maps.newHashMap();
        for (GiftCardContext.GiftCardPolicyBean giftCardPolicyBean : policyList) {
            List<OrderSheet.SheetItem> list = policySheetItemMap.get(giftCardPolicyBean.getId());
            //政策最大优惠数量
            Integer discountMaxNumber = giftCardPolicyBean.getDiscountMaxNumber();
            if (CollectionUtils.isNotEmpty(list)) {
                for (OrderSheet.SheetItem sheetItem : list) {
                    //当前下单商品购买数量
                    Integer itemBuyNumber = sheetItem.getNumber();
                    //最终商品数量
                    int itemNum = Objects.nonNull(discountMaxNumber) && discountMaxNumber > 0 && discountMaxNumber < itemBuyNumber ? discountMaxNumber : itemBuyNumber;
                    //当前政策规则赠送的赠品配置
                    Map<Integer, Integer> policyGiftConfigMap = giftCardPolicyBean.getGiftCardPolicyGiftList().stream().collect(Collectors.toMap(GiftCardPolicyGift::getCommodityId, GiftCardPolicyGift::getNum));
                    for (Integer giftCommodityId : policyGiftConfigMap.keySet()) {
                        Integer giftNum = policyGiftConfigMap.get(giftCommodityId);
                        int giftTotalNum = itemNum * giftNum;

                        //不同政策可能赠送相同的赠品，则赠品的数量需要累计
                        if (policyGiftItemMap.containsKey(giftCommodityId)) {
                            policyGiftItemMap.put(giftCommodityId, policyGiftItemMap.get(giftCommodityId) + giftTotalNum);
                        } else {
                            policyGiftItemMap.put(giftCommodityId, giftTotalNum);
                        }
                    }
                }
            }
        }

        if (MapUtils.isEmpty(policyGiftItemMap)) {
            return Lists.newArrayList();
        }

        List<GiftItem> giftItemList = policyGiftItemMap.entrySet().stream().map(emtry -> {
            GiftItem giftItem = new GiftItem();
            giftItem.setCommodityId(emtry.getKey());
            giftItem.setNumber(emtry.getValue());
            return giftItem;
        }).collect(Collectors.toList());

        return giftItemList;
    }

    @Override
    public DiscountCheckResult getDiscountAmount(TradeCalculateDTO tradeCalculateDTO) {
        //1、开始进行计算
        DiscountCalculateResultBO discountCalculateResult = tradeDiscountCalculateHelper.commodityComboFlexibleDiscountCalculate(tradeCalculateDTO);

        DiscountCheckResult discountCheckResult = new DiscountCheckResult();
        discountCheckResult.setTradeCode(discountContext.getTradeCode());
        discountCheckResult.setAvailable(true);
        discountCheckResult.setPrice(discountCalculateResult.getTotalDiscountAmount());
        discountCheckResult.setDiscountDetails(discountCalculateResult.getDiscountDetails());
        return discountCheckResult;
    }

    private void buildPolicySheetItemMap(Map<Integer, List<OrderSheet.SheetItem>> policySheetItemMap, OrderSheet.SheetItem sheetItem, GiftCardContext.GiftCardPolicyBean giftCardPolicyBean) {
        if (policySheetItemMap.containsKey(giftCardPolicyBean.getId())) {
            List<OrderSheet.SheetItem> policyItems = policySheetItemMap.get(giftCardPolicyBean.getId());
            policyItems.add(sheetItem);
            policySheetItemMap.put(giftCardPolicyBean.getId(), policyItems);
        } else {
            policySheetItemMap.put(giftCardPolicyBean.getId(), Lists.newArrayList(sheetItem));
        }
    }
}
