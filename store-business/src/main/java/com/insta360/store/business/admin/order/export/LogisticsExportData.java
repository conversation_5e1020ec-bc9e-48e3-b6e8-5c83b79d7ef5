package com.insta360.store.business.admin.order.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

/**
 * @Author: wkx
 * @Date: 2023/6/7
 * @Description:
 */
@ColumnWidth(25)
public class LogisticsExportData {

    @ExcelProperty("运单号")
    private String expressNumber;

    @ExcelProperty("货物参考信息")
    private String goodInfo;

    @ExcelProperty("商品HS CODE")
    private String hsCode;

    @ExcelProperty("货物总重量")
    private Float goodWeight;

    @ExcelProperty("收件人邮箱")
    private String email;

    @ExcelProperty("货物描述")
    private String goodDescription;

    @ExcelProperty("销售条款(1) FCA (2) CIP (3) CPT (4) EXW (6) DDP (7) DAT (8) DAP (9) Other")
    private Integer salePolicy;

    @ExcelProperty("服务方式 2P(优先) (3)经济")
    private String serviceWay;

    @ExcelProperty("包装方式[1273](1)自备包装(2)PAK")
    private Integer packWay;

    @ExcelProperty("VIT/EIN")
    private String vitEin;

    @ExcelProperty("商品数量")
    private Integer goodQuantity;

    @ExcelProperty("商品单价")
    private String goodPrice;

    @ExcelProperty("Duty / Tax Paymernt Type\n" +
            "关税付款方\n" +
            "1:寄件人\n" +
            "2:收件人\n" +
            "3:第三方")
    private Integer taxPayType;

    @ExcelProperty("Duty / Tax Payer Account Number\n" +
            "关税付款账号")
    private String taxPayerAccount;

    @ExcelProperty("收件人姓名")
    private String customerName;

    @ExcelProperty("收件人地址1")
    private String customerAddress1;

    @ExcelProperty("收件人地址2")
    private String customerAddress2;

    @ExcelProperty("国家代码")
    private String countryCode;

    @ExcelProperty("收件人省份/州")
    private String province;

    @ExcelProperty("收件人城市")
    private String city;

    @ExcelProperty("收件人邮编")
    private String zipCode;

    @ExcelProperty("收件人电话")
    private String phone;

    @ExcelProperty("卖家备注")
    private String orderAdminRemark;

    @ExcelProperty("支付时间")
    private String paymentPayTime;

    @ExcelProperty("总重量")
    private String totalWeight;

    @ExcelProperty("支付金额")
    private String payAmount;

    @ExcelProperty("货币")
    private String currency;

    @ExcelProperty("报关金额(USD)")
    private String paymentAmountFinal;

    @ExcelProperty("发票报关金额(USD)")
    private String invoicePrice;

    @ExcelProperty("发票报关金额(EUR)")
    private String invoiceEurPrice;

    @ExcelProperty("是否带电")
    private String classify;

    @ExcelProperty("收款主体")
    private String paymentModule;

    @ExcelProperty("物流渠道")
    private String logistics;

    public String getExpressNumber() {
        return expressNumber;
    }

    public void setExpressNumber(String expressNumber) {
        this.expressNumber = expressNumber;
    }

    public String getGoodInfo() {
        return goodInfo;
    }

    public void setGoodInfo(String goodInfo) {
        this.goodInfo = goodInfo;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public Float getGoodWeight() {
        return goodWeight;
    }

    public void setGoodWeight(Float goodWeight) {
        this.goodWeight = goodWeight;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGoodDescription() {
        return goodDescription;
    }

    public void setGoodDescription(String goodDescription) {
        this.goodDescription = goodDescription;
    }

    public Integer getSalePolicy() {
        return salePolicy;
    }

    public void setSalePolicy(Integer salePolicy) {
        this.salePolicy = salePolicy;
    }

    public String getServiceWay() {
        return serviceWay;
    }

    public void setServiceWay(String serviceWay) {
        this.serviceWay = serviceWay;
    }

    public Integer getPackWay() {
        return packWay;
    }

    public void setPackWay(Integer packWay) {
        this.packWay = packWay;
    }

    public String getVitEin() {
        return vitEin;
    }

    public void setVitEin(String vitEin) {
        this.vitEin = vitEin;
    }

    public Integer getGoodQuantity() {
        return goodQuantity;
    }

    public void setGoodQuantity(Integer goodQuantity) {
        this.goodQuantity = goodQuantity;
    }

    public String getGoodPrice() {
        return goodPrice;
    }

    public void setGoodPrice(String goodPrice) {
        this.goodPrice = goodPrice;
    }

    public Integer getTaxPayType() {
        return taxPayType;
    }

    public void setTaxPayType(Integer taxPayType) {
        this.taxPayType = taxPayType;
    }

    public String getTaxPayerAccount() {
        return taxPayerAccount;
    }

    public void setTaxPayerAccount(String taxPayerAccount) {
        this.taxPayerAccount = taxPayerAccount;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerAddress1() {
        return customerAddress1;
    }

    public void setCustomerAddress1(String customerAddress1) {
        this.customerAddress1 = customerAddress1;
    }

    public String getCustomerAddress2() {
        return customerAddress2;
    }

    public void setCustomerAddress2(String customerAddress2) {
        this.customerAddress2 = customerAddress2;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOrderAdminRemark() {
        return orderAdminRemark;
    }

    public void setOrderAdminRemark(String orderAdminRemark) {
        this.orderAdminRemark = orderAdminRemark;
    }

    public String getPaymentPayTime() {
        return paymentPayTime;
    }

    public void setPaymentPayTime(String paymentPayTime) {
        this.paymentPayTime = paymentPayTime;
    }

    public String getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(String totalWeight) {
        this.totalWeight = totalWeight;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getPaymentAmountFinal() {
        return paymentAmountFinal;
    }

    public void setPaymentAmountFinal(String paymentAmountFinal) {
        this.paymentAmountFinal = paymentAmountFinal;
    }

    public String getInvoicePrice() {
        return invoicePrice;
    }

    public void setInvoicePrice(String invoicePrice) {
        this.invoicePrice = invoicePrice;
    }

    public String getInvoiceEurPrice() {
        return invoiceEurPrice;
    }

    public void setInvoiceEurPrice(String invoiceEurPrice) {
        this.invoiceEurPrice = invoiceEurPrice;
    }

    public String getClassify() {
        return classify;
    }

    public void setClassify(String classify) {
        this.classify = classify;
    }

    public String getPaymentModule() {
        return paymentModule;
    }

    public void setPaymentModule(String paymentModule) {
        this.paymentModule = paymentModule;
    }

    public String getLogistics() {
        return logistics;
    }

    public void setLogistics(String logistics) {
        this.logistics = logistics;
    }
}
