package com.insta360.store.business.payment.lib.checkout.model;

/**
 * @Author: wkx
 * @Date: 1/4/24
 * @Description:
 */
public class Header {

    /**
     * 公钥hash
     */
    private String publicKeyHash;

    /**
     * 临时公钥
     */
    private String ephemeralPublicKey;

    /**
     * 交易号
     */
    private String transactionId;

    public String getPublicKeyHash() {
        return publicKeyHash;
    }

    public void setPublicKeyHash(String publicKeyHash) {
        this.publicKeyHash = publicKeyHash;
    }

    public String getEphemeralPublicKey() {
        return ephemeralPublicKey;
    }

    public void setEphemeralPublicKey(String ephemeralPublicKey) {
        this.ephemeralPublicKey = ephemeralPublicKey;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @Override
    public String toString() {
        return "Header{" +
                "publicKeyHash='" + publicKeyHash + '\'' +
                ", ephemeralPublicKey='" + ephemeralPub<PERSON><PERSON>ey + '\'' +
                ", transactionId='" + transactionId + '\'' +
                '}';
    }
}
