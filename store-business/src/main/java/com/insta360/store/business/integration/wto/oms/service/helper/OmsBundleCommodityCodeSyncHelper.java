package com.insta360.store.business.integration.wto.oms.service.helper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.model.BundleCommodityDetail;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityCode;
import com.insta360.store.business.commodity.service.BundleCommodityDetailService;
import com.insta360.store.business.commodity.service.CommodityCodeService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.integration.wto.oms.bo.CombinationProductReturnBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteResultBO;
import com.insta360.store.business.integration.wto.oms.lib.response.BundleCommodityCodeQueryResponse;
import com.insta360.store.business.integration.wto.oms.service.handler.OmsService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/26 上午11:20
 */
@Component
public class OmsBundleCommodityCodeSyncHelper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OmsBundleCommodityCodeSyncHelper.class);

    @Autowired
    CommodityCodeService commodityCodeService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    BundleCommodityDetailService bundleCommodityDetailService;

    /**
     * 批量同步
     *
     * @param bundleCommodityList
     * @param omsService
     * @param commodityCodeMap
     * @param commodityEnableMap
     * @param productEnableMap
     */
    @Async
    public void bundleCommodityBatchSync(List<Commodity> bundleCommodityList, OmsService omsService, Map<Integer, CommodityCode> commodityCodeMap, Map<Integer, Boolean> commodityEnableMap, Map<Integer, Boolean> productEnableMap) {
        List<BundleCommodityDetail> bundleCommodityDetailList = new ArrayList<>();
        for (Commodity commodity : bundleCommodityList) {
            try {
                Optional.ofNullable(bundleCommoditySync(commodity, omsService, commodityCodeMap, commodityEnableMap, productEnableMap))
                        .filter(CollectionUtils::isNotEmpty)
                        .ifPresent(bundleCommodityDetailList::addAll);
            } catch (InstaException e) {
                LOGGER.error(String.format("组合套餐%d，oms同步发生异常，异常原因：%s", commodity.getId(), e.getMessage()), e);
                FeiShuMessageUtil.storeGeneralMessage(String.format("组合套餐%d，oms同步发生异常，异常原因：%s", commodity.getId(), e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        }
        if (CollectionUtils.isNotEmpty(bundleCommodityDetailList)) {
            List<List<BundleCommodityDetail>> allBundleCommodityDetailList = Lists.partition(bundleCommodityDetailList, 2000);
            allBundleCommodityDetailList.stream().forEach(list -> bundleCommodityDetailService.addBundleCommodityDetailList(list));
            FeiShuMessageUtil.storeGeneralMessage(String.format("组合套餐，同步成功,总计: {%s}", bundleCommodityDetailList.size()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
    }

    /**
     * 解析组合套餐，并入库
     *
     * @param commodity
     * @return
     */
    public List<BundleCommodityDetail> bundleCommoditySync(Commodity commodity, OmsService omsService, Map<Integer, CommodityCode> commodityCodeMap,
                                    Map<Integer, Boolean> commodityEnableMap, Map<Integer, Boolean> productEnableMap) {
        Integer commodityId = commodity.getId();
        CommodityCode commodityCode = commodityCodeMap.get(commodityId);
        if (commodityCode == null || StringUtil.isBlank(commodityCode.getCode())) {
            LOGGER.error(String.format("套餐ID：%d，在%s地区，料号为空", commodityId, InstaCountry.CN.name()));
            throw new InstaException(-1, String.format("套餐ID：%d，在%s地区，料号为空", commodityId, InstaCountry.CN.name()));
        }
        LOGGER.info(String.format("解析组合套餐开始。。。套餐id：%d，料号：%s", commodityId, commodityCode.getCode()));
        // 待更新组合套餐明细
        List<BundleCommodityDetail> bundleCommodityDetails = new ArrayList<>();
        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        omsExecuteBo.setBundleCommodityCode(commodityCode.getCode());
        // 调用oms查询接口
        OmsExecuteResultBO omsExecuteResultBo = omsService.executeOmsTransaction(omsExecuteBo);
        BundleCommodityCodeQueryResponse bundleCommodityCodeQueryResponse = (BundleCommodityCodeQueryResponse) omsExecuteResultBo.getBaseOmsResponse();
        List<String> subCommodityCodes = bundleCommodityCodeQueryResponse.getData();
        if (CollectionUtils.isEmpty(subCommodityCodes)) {
            LOGGER.error(String.format("组合套餐查询到子套餐料号为空。。。套餐id：%d，料号：%s", commodityId, commodityCode.getCode()));
            throw new InstaException(-1, String.format("组合套餐查询到子套餐料号为空。。。套餐id：%d，料号：%s", commodityId, commodityCode.getCode()));
        }
        // response解析
        CombinationProductReturnBO combinationProductReturnBo = JSON.parseObject(subCommodityCodes.get(0), CombinationProductReturnBO.class);
        // 所有子套餐料号
        List<String> skuCodes = combinationProductReturnBo.getDetails().stream().map(CombinationProductReturnBO.CombinationDetail::getSkuCode).collect(Collectors.toList());
        LOGGER.info(String.format("解析出子套餐料号。组合套餐料号：%s，子套餐料号：%s", commodityCode.getCode(), skuCodes));
        // 根据料号反查套餐
        Map<String, List<CommodityCode>> commodityCodeListMap = commodityCodeService.listBySkuCodes(skuCodes)
                .stream()
                .collect(Collectors.groupingBy(CommodityCode::getCode));

        // 打印料号与套餐映射关系
        commodityCodeListMap.forEach((code, codeList) ->
                LOGGER.info("料号: {} 对应的套餐列表: {}",
                        code,
                        codeList.stream()
                                .map(CommodityCode::getCommodity)
                                .distinct()
                                .map(cc -> String.format("套餐ID:%d", cc))
                                .collect(Collectors.joining(", ")))
        );

        combinationProductReturnBo.getDetails().forEach(detail -> {
            // 子套餐料号
            String skuCode = detail.getSkuCode();
            // 子套餐数量
            Integer quantity = detail.getQuantity();
            bundleCommodityDetails.add(new BundleCommodityDetail(commodityId, commodity.getProduct(),
                    pickSuitableCommodityId(skuCode, commodityCodeListMap, commodityEnableMap, productEnableMap), quantity));
        });
        LOGGER.info(String.format("解析组合套餐完成。。。套餐id：%d，料号：%s", commodityId, commodityCode.getCode()));
        return bundleCommodityDetails;
    }

    /**
     * 匹配合适的套餐id
     * 匹配规则：
     * Step 1：先查找产品状态和套餐状态都为启用的套餐ID
     * Step 2：套餐ID更大的值
     *
     * @param skuCode
     * @param commodityCodeListMap
     * @return
     */
    private Integer pickSuitableCommodityId(String skuCode, Map<String, List<CommodityCode>> commodityCodeListMap,
                                            Map<Integer, Boolean> commodityEnableMap, Map<Integer, Boolean> productEnableMap) {
        // 料号匹配到的记录集合
        List<CommodityCode> commodityCodes = commodityCodeListMap.get(skuCode);
        String errorMsg;
        // 料号未匹配到套餐
        if (CollectionUtils.isEmpty(commodityCodes)) {
            errorMsg = String.format("料号未匹配到套餐，料号：%s", skuCode);
            LOGGER.error(errorMsg);
            throw new InstaException(-1, errorMsg);
        }
        List<Integer> subCommodityIds = commodityCodes.stream().map(CommodityCode::getCommodity).distinct().collect(Collectors.toList());
        // 料号只对应一个套餐，直接返回该套餐id
        if (subCommodityIds.size() == 1) {
            return subCommodityIds.get(0);
        }
        List<Commodity> commodities = commodityService.listCommodities(subCommodityIds);
        // 料号匹配到的多个套餐，按规则查找最终合适的套餐
        Optional<Commodity> suitableCommodity = commodities.stream()
                .filter(commodity -> commodityEnableMap.getOrDefault(commodity.getId(), false) && productEnableMap.getOrDefault(commodity.getProduct(), false))
                .max(Comparator.comparing(Commodity::getId));
        if (!suitableCommodity.isPresent()) {
            errorMsg = String.format("按规则未找到合适的套餐id, 料号：%s，对应套餐为：%s", skuCode, subCommodityIds);
            LOGGER.error(errorMsg);
            throw new InstaException(-1, errorMsg);
        }
        return suitableCommodity.get().getId();
    }
}
