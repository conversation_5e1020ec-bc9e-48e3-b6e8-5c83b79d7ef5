package com.insta360.store.business.payment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.insta360.compass.core.common.BaseModel;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2025-05-06
 * @Description: 钱海免费试用接口请求数据
 */
public class OceanFreeCheckRecord extends BaseModel<OceanFreeCheckRecord> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户账户id
     */
    private Integer userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 临时订单号
     */
    private String serialNumber;

    /**
     * 卡种名
     */
    private String creditCardName;

    /**
     * 支付ID,Oceanpayment的支付唯一单号
     */
    private String paymentId;

    /**
     * 国家地区
     */
    private String county;

    /**
     * 调用接口
     */
    private String method;

    /**
     * 扣款类别->user_pay_info
     */
    private String deductCategory;

    /**
     * 卡种->user_pay_info
     */
    private String cardType;

    /**
     * 卡地区->user_pay_info
     */
    private String cardCountry;

    /**
     * 账单地址
     */
    private String billingAddress;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getCreditCardName() {
        return creditCardName;
    }

    public void setCreditCardName(String creditCardName) {
        this.creditCardName = creditCardName;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getDeductCategory() {
        return deductCategory;
    }

    public void setDeductCategory(String deductCategory) {
        this.deductCategory = deductCategory;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardCountry() {
        return cardCountry;
    }

    public void setCardCountry(String cardCountry) {
        this.cardCountry = cardCountry;
    }

    public String getBillingAddress() {
        return billingAddress;
    }

    public void setBillingAddress(String billingAddress) {
        this.billingAddress = billingAddress;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OceanFreeCheckRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", email='" + email + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", creditCardName='" + creditCardName + '\'' +
                ", paymentId='" + paymentId + '\'' +
                ", county='" + county + '\'' +
                ", method='" + method + '\'' +
                ", deductCategory='" + deductCategory + '\'' +
                ", cardType='" + cardType + '\'' +
                ", cardCountry='" + cardCountry + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}