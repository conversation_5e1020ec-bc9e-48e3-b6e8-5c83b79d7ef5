package com.insta360.store.business.meta.bo;

import com.insta360.store.business.meta.enums.ActivityComponentEnum;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/14
 */
public class ActivityComponentInfoBO implements Serializable {

    /**
     * 排序字段
     * 防止由于序列化组件的修改 升级影响序列化的排序
     */
    @NotNull
    private Integer orderIndex;

    /**
     * 组件类型
     * 保留参数 便于后续可能的拓展
     *
     * @see ActivityComponentEnum
     */
    @NotNull
    private String componentType;

    /**
     * 组件配置
     */
    @NotNull
    private String config;

    public ActivityComponentEnum activityComponentEnum() {
        return ActivityComponentEnum.getByCode(componentType);
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    @Override
    public String toString() {
        return "ActivityComponentInfoBO{" +
                "orderIndex=" + orderIndex +
                ", componentType='" + componentType + '\'' +
                ", config='" + config + '\'' +
                '}';
    }
}
