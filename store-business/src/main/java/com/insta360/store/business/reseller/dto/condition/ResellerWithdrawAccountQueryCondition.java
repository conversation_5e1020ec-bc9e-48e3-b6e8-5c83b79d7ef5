package com.insta360.store.business.reseller.dto.condition;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.store.business.reseller.dto.ResellerWithdrawDTO;
import com.insta360.store.business.reseller.enums.ResellerWithdrawAccountType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 分销提现账户查询条件
 * @Date 2022/11/30
 */
public class ResellerWithdrawAccountQueryCondition implements Serializable {

    /**
     * 提现状态
     */
    private Integer withdrawState;

    /**
     * 分销商邮箱
     */
    private List<String> emailList;

    /**
     * 订单号
     */
    private List<String> orderNumberList;

    /**
     * 提现渠道类型
     *
     * @see ResellerWithdrawAccountType
     */
    private String withdrawAccountType;

    /**
     * 提现开始时间
     */
    private LocalDateTime withdrawStartTime;

    /**
     * 提现结束时间
     *
     * @return
     */
    private LocalDateTime withdrawEndTime;

    /**
     * 分销码
     */
    private List<String> promoCodeList;

    /**
     * 提现佣金是否等于0
     */
    private Boolean estimatedIncomeIsZero;


    /**
     * 构建查询条件
     *
     * @param resellerWithdrawDto
     * @return
     */
    public static ResellerWithdrawAccountQueryCondition buildQueryCondition(ResellerWithdrawDTO resellerWithdrawDto) {
        if (Objects.isNull(resellerWithdrawDto)) {
            throw new InstaException(-1, "非法参数");
        }
        ResellerWithdrawAccountQueryCondition queryCondition = new ResellerWithdrawAccountQueryCondition();
        Integer state = resellerWithdrawDto.getState();
        if (Objects.nonNull(state)) {
            queryCondition.setWithdrawState(state);
        }

        String withdrawAccountType = resellerWithdrawDto.getWithdrawAccountType();
        if (StringUtils.isNotBlank(withdrawAccountType)) {
            queryCondition.setWithdrawAccountType(withdrawAccountType);
        }

        List<String> orderNumberList = resellerWithdrawDto.getOrderNumberList();
        if (CollectionUtils.isNotEmpty(orderNumberList)) {
            queryCondition.setOrderNumberList(orderNumberList);
        }

        List<String> emailList = resellerWithdrawDto.getEmailList();
        if (CollectionUtils.isNotEmpty(emailList)) {
            queryCondition.setEmailList(emailList);
        }

        List<String> promoCodeList = resellerWithdrawDto.getPromoCodeList();
        if (CollectionUtils.isNotEmpty(promoCodeList)) {
            queryCondition.setPromoCodeList(promoCodeList);
        }

        Long withdrawStartTime = resellerWithdrawDto.getWithdrawStartTime();
        Long withdrawEndTime = resellerWithdrawDto.getWithdrawEndTime();
        if (Objects.nonNull(withdrawEndTime) && Objects.nonNull(withdrawEndTime)) {
            queryCondition.setWithdrawStartTime(TimeUtil.parseLocalDateTime(withdrawStartTime));
            queryCondition.setWithdrawEndTime(TimeUtil.parseLocalDateTime(withdrawEndTime));
        }

        Boolean estimatedIncomeIsZero = resellerWithdrawDto.getEstimatedIncomeIsZero();
        if (Objects.nonNull(estimatedIncomeIsZero)) {
            queryCondition.setEstimatedIncomeIsZero(estimatedIncomeIsZero);
        }
        return queryCondition;
    }

    public Integer getWithdrawState() {
        return withdrawState;
    }

    public void setWithdrawState(Integer withdrawState) {
        this.withdrawState = withdrawState;
    }

    public String getWithdrawAccountType() {
        return withdrawAccountType;
    }

    public void setWithdrawAccountType(String withdrawAccountType) {
        this.withdrawAccountType = withdrawAccountType;
    }

    public LocalDateTime getWithdrawStartTime() {
        return withdrawStartTime;
    }

    public void setWithdrawStartTime(LocalDateTime withdrawStartTime) {
        this.withdrawStartTime = withdrawStartTime;
    }

    public LocalDateTime getWithdrawEndTime() {
        return withdrawEndTime;
    }

    public void setWithdrawEndTime(LocalDateTime withdrawEndTime) {
        this.withdrawEndTime = withdrawEndTime;
    }

    public List<String> getEmailList() {
        return emailList;
    }

    public void setEmailList(List<String> emailList) {
        this.emailList = emailList;
    }

    public List<String> getOrderNumberList() {
        return orderNumberList;
    }

    public void setOrderNumberList(List<String> orderNumberList) {
        this.orderNumberList = orderNumberList;
    }

    public List<String> getPromoCodeList() {
        return promoCodeList;
    }

    public void setPromoCodeList(List<String> promoCodeList) {
        this.promoCodeList = promoCodeList;
    }

    public Boolean getEstimatedIncomeIsZero() {
        return estimatedIncomeIsZero;
    }

    public void setEstimatedIncomeIsZero(Boolean estimatedIncomeIsZero) {
        this.estimatedIncomeIsZero = estimatedIncomeIsZero;
    }
}
