package com.insta360.store.business.configuration.graphql.example;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.configuration.graphql.GraphqlConfig;
import com.insta360.store.business.configuration.graphql.GraphqlRequest;
import com.insta360.store.business.configuration.graphql.GraphqlResponse;
import com.insta360.store.business.configuration.graphql.GraphqlUtils;
import com.insta360.store.business.prime.lib.variables.CreateProductVariables;
import com.insta360.store.business.prime.lib.variables.CreateProductVariables.CreateProductInput;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4
 * @description GraphqlUtils使用示例类
 */
public class GraphqlUtilsExample {

    public static final String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJFUzI1NiJ9.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.XW6HZh1Poy7d8pKWbX3UVkmL5JU43CuxM6Ra4upRFHfv_Wak_S1ayqJzYQwwdsy_RPL_qP0e4qfPPmtZuBWI-w";

    /**
     * 示例方法：创建产品（使用变量方式）
     */
    public static void createProductWithVariablesExample() {
        // 1. 创建GraphQL配置
        GraphqlConfig config = GraphqlConfig.builder()
                .url("https://api.buywithprime.amazon.com/graphql")
                .authorization(token)
                .targetId("bp-1e719710-061b-48d0-8f8e-75da204127f4")
                .apiVersion("2024-11-01")
                .build();

        // 2. 构建输入参数
        CreateProductInput input = CreateProductInput.builder()
                .externalId("a1111")
                .amazonSkuValue("a1111")
                .sku("a1111")
                .offerPrime(true)
                .productDetailPageUrl("https://store-dev.insta360.com/product/x4")
                .imageSourceUrl("https://res.insta360.com/static/338a8405bc5d902f0089a4d7f0773e22/%E6%97%8B%E8%BD%AC%E5%8A%A8%E7%94%BB%203480-2270_00000.jpg?x-oss-process=image%2Fauto-orient%2C1%2Fresize%2Cm_lfit%2Cw_1736%2Ch_1135%2Fquality%2Cq_80")
                .build();

        // 3. 调用创建产品方法（使用变量）
        // GraphqlResponse response = GraphqlUtils.createProductWithVariables(config, input);

        // 构建变量对象
        CreateProductVariables variables = new CreateProductVariables(input);

        // GraphQL查询模板（使用变量）
        String query = "mutation CreateProduct($input: CreateProductInput!) {\n" +
                "    createProduct(input: $input) {\n" +
                "        id\n" +
                "    }\n" +
                "}";

        // 构建请求对象
        GraphqlRequest request = GraphqlRequest.builder()
                .query(query)
                .operationName("CreateProduct")
                .variables(variables)
                .build();

        // 执行请求
        GraphqlResponse response = GraphqlUtils.execute(request, config);
        System.out.println(JSON.toJSONString(response));

    }

    /**
     * 主方法，运行示例
     */
    public static void main(String[] args) {
        // 运行示例1：直接方式创建产品
        // System.out.println("===== 示例1：直接方式创建产品 =====");
        // createProductExample();

        // 运行示例2：使用变量方式创建产品
        System.out.println("\n===== 示例2：使用变量方式创建产品 =====");
        createProductWithVariablesExample();
    }
}
