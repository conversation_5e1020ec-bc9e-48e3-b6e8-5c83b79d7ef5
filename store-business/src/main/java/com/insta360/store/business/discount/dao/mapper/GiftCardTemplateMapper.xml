<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.insta360.store.business.discount.dao.GiftCardTemplateDao">

    <!-- 兼容 mybatis cache接口自定义二级缓存   -->
    <cache-ref namespace="com.insta360.store.business.discount.dao.GiftCardTemplateDao"/>

    <!-- 分页查询 -->
    <select id="selectGiftCardTemplateByPage" resultType="com.insta360.store.business.discount.model.GiftCardTemplate" useCache="false">
        select * from gift_card_template_new
        <where>
            <choose>
                <when test="condition.code != null and condition.code != ''">
                    and template_code = #{condition.code}
                </when>
                <otherwise>
                    and job_number = #{condition.adminJobNumber}
                </otherwise>
            </choose>
        </where>
        order by create_time desc
    </select>
</mapper>