package com.insta360.store.business.cloud.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/15
 */
public enum BenefitCapacityType {

    FIVE_HUNDRED_G(1,500D, "512GB"),

    TWO_HUNDRED_G(2,200D, "200GB"),

    ONE_T(3,1024D, "1TB"),

    TWO_T(4,2048D, "2TB"),

    FOUR_T(5,4096D, "4TB");

    private final Integer code;

    private final Double capacity;

    private final String desc;

    BenefitCapacityType(Integer code, Double capacity, String desc) {
        this.code = code;
        this.capacity = capacity;
        this.desc = desc;
    }

    /**
     * 根据容量匹配枚举
     *
     * @param capacity
     * @return
     */
    public static BenefitCapacityType matchType(Double capacity) {
        if (Objects.isNull(capacity)) {
            return null;
        }

        for (BenefitCapacityType benefitCapacityType : BenefitCapacityType.values()) {
            if (benefitCapacityType.capacity.equals(capacity)) {
                return benefitCapacityType;
            }
        }
        return null;
    }

    /**
     * 根据名称匹配枚举
     *
     * @param code
     * @return
     */
    public static BenefitCapacityType matchCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }

        for (BenefitCapacityType benefitCapacityType : BenefitCapacityType.values()) {
            if (benefitCapacityType.code.equals(code)) {
                return benefitCapacityType;
            }
        }

        return null;
    }

    public Double getCapacity() {
        return capacity;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }
}
