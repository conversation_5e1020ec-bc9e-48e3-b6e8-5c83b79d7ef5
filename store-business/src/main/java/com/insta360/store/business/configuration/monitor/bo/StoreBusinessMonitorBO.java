package com.insta360.store.business.configuration.monitor.bo;

import com.insta360.store.business.configuration.monitor.enums.MonitorKeyType;
import com.insta360.store.business.meta.enums.PaymentChannel;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/8/5
 * @Description:
 */
public class StoreBusinessMonitorBO implements Serializable {

    /**
     * 业务key类型
     */
    private MonitorKeyType keyType;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 支付渠道
     */
    private PaymentChannel paymentChannel;

    /**
     * 代金券
     */
    private String tradeCode;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public MonitorKeyType getKeyType() {
        return keyType;
    }

    public void setKeyType(MonitorKeyType keyType) {
        this.keyType = keyType;
    }

    public PaymentChannel getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(PaymentChannel paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    @Override
    public String toString() {
        return "StoreBusinessMonitorBO{" +
                "keyType=" + keyType +
                ", orderNumber='" + orderNumber + '\'' +
                ", orderId=" + orderId +
                ", paymentChannel=" + paymentChannel +
                ", tradeCode='" + tradeCode + '\'' +
                '}';
    }
}
