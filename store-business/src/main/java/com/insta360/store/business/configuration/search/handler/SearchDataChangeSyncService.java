package com.insta360.store.business.configuration.search.handler;

import com.insta360.store.business.configuration.search.bo.SearchSyncParametersBO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/28
 */
public interface SearchDataChangeSyncService {

    /**
     * 同步商城业务搜索数据变更
     *
     * @param searchDataChangeParams
     */
    void syncSearchDataChanges(SearchSyncParametersBO searchDataChangeParams);

    /**
     * 获取过滤产品ID列表
     *
     * @return
     */
    List<Integer> getSearchFilterProductIds();

    /**
     * 获取搜索过滤商品ID列表
     * <p>
     * 本方法从商店配置服务中获取特定配置值，该配置值是用于搜索过滤的商品ID列表
     * 如果配置值为空，则返回一个空的列表
     * 否则，将配置值（商品ID）字符串分割成数组，转换为整数列表并返回
     *
     * @return 商品ID列表，用于搜索过滤
     */
    List<Integer> getSearchFilterCommodityIds();
}
