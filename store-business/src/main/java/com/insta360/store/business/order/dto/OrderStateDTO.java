package com.insta360.store.business.order.dto;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/11/29
 * @Description:
 */
public class OrderStateDTO implements Serializable {

    @JSONField(name = "order_id")
    private Integer orderId;

    private Integer order;

    /**
     * 订单id的list
     */
    private List<String> orderList;

    /**
     * 交易号
     */
    private String channelPaymentId;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public List<String> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<String> orderList) {
        this.orderList = orderList;
    }

    public String getChannelPaymentId() {
        return channelPaymentId;
    }

    public void setChannelPaymentId(String channelPaymentId) {
        this.channelPaymentId = channelPaymentId;
    }

    @Override
    public String toString() {
        return "OrderStateDTO{" +
                "orderId=" + orderId +
                ", order=" + order +
                ", orderList=" + orderList +
                ", channelPaymentId='" + channelPaymentId + '\'' +
                '}';
    }

}
