package com.insta360.store.business.meta.dto;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategoryThirdInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: py
 * @Date: 2023/11/15
 * @Description:
 */
public class NavigationBarCategoryThirdInfoDTO implements Serializable {

    private Integer id;

    /**
     * 三级导航id
     */
    @NotNull(message = "三级导航分类id不允许为空")
    private Integer thirdId;

    /**
     * 三级导航的多语言文案
     */
    @NotBlank(message = "三级导航多语言名称不允许为空")
    private String thirdName;

    /**
     * 语言
     */
    @NotBlank(message = "三级导航多语言名称不允许为空")
    private String language;

    /**
     * 国家
     */
    @NotBlank(message = "三级导航多语言名称不允许为空")
    private String country;

    public NavigationBarCategoryThirdInfo getPojoObject() {
        NavigationBarCategoryThirdInfo navigationBarCategoryThirdInfo = new NavigationBarCategoryThirdInfo();
        BeanUtil.copyProperties(this, navigationBarCategoryThirdInfo);
        return navigationBarCategoryThirdInfo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getThirdId() {
        return thirdId;
    }

    public void setThirdId(Integer thirdId) {
        this.thirdId = thirdId;
    }

    public String getThirdName() {
        return thirdName;
    }

    public void setThirdName(String thirdName) {
        this.thirdName = thirdName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        return "NavigationBarCategoryThirdInfoDTO{" +
                "id=" + id +
                ", thirdId=" + thirdId +
                ", thirdName='" + thirdName + '\'' +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                '}';
    }
}
