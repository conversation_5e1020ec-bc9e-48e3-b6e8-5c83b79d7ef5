package com.insta360.store.business.payment.service.impl.handler;

import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.payment.constants.PayPalConstant;
import com.insta360.store.business.payment.lib.paypal.PayPalHkYsConfiguration;
import com.insta360.store.business.payment.lib.paypal.PaypalConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2024/06/01
 * @Description:
 */
@Scope("prototype")
@Component
public class PayPalHkInternationalSubscribePaymentHandler extends BasePayPalPaymentSubscribeHandler {

    @Autowired
    PayPalHkYsConfiguration payPalHkYsConfiguration;

    @Override
    protected PaypalConfig getPayPalConfigInfo() {
        return payPalHkYsConfiguration;
    }

    @Override
    public PaymentChannel getPaymentChannel() {
        return PaymentChannel.paypal_hk_international_subscribe;
    }

    @Override
    protected String getPayPalAccessTokenCacheKey() {
        return PayPalConstant.PAYPAL_HKYS_ACCESS_TOKEN;
    }
}
