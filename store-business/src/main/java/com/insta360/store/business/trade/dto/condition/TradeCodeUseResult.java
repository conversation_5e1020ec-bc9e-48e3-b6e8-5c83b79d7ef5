package com.insta360.store.business.trade.dto.condition;

import com.insta360.store.business.discount.dto.bo.DiscountCheckResult;
import com.insta360.store.business.meta.bo.TradeCode;

/**
 * @Author: mowi
 * @Date: 2019-03-19
 * @Description:
 */
public class TradeCodeUseResult {

    /**
     * 分销代码
     */
    private String resellerCode;

    /**
     * 是绑定优惠券
     */
    private Boolean bindCoupon;

    /**
     * 交易代码
     */
    private TradeCode tradeCode;

    private DiscountCheckResult discountCheckResult;

    /**
     * 是否免支付
     */
    private boolean free;

    public TradeCodeUseResult() {
    }

    public TradeCodeUseResult(TradeCode tradeCode, DiscountCheckResult discountCheckResult) {
        this.tradeCode = tradeCode;
        this.discountCheckResult = discountCheckResult;
    }

    public boolean isFree() {
        return free;
    }

    public void setFree(boolean free) {
        this.free = free;
    }

    public TradeCode getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(TradeCode tradeCode) {
        this.tradeCode = tradeCode;
    }

    public DiscountCheckResult getDiscountCheckResult() {
        return discountCheckResult;
    }

    public void setDiscountCheckResult(DiscountCheckResult discountCheckResult) {
        this.discountCheckResult = discountCheckResult;
    }

    public Boolean isBindCoupon() {
        return bindCoupon;
    }

    public void setBindCoupon(Boolean bindCoupon) {
        this.bindCoupon = bindCoupon;
    }

    public Boolean getBindCoupon() {
        return bindCoupon;
    }

    public String getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(String resellerCode) {
        this.resellerCode = resellerCode;
    }

    @Override
    public String toString() {
        return "TradeCodeUseResult{" +
                "resellerCode='" + resellerCode + '\'' +
                ", bindCoupon=" + bindCoupon +
                ", tradeCode=" + tradeCode +
                ", discountCheckResult=" + discountCheckResult +
                ", free=" + free +
                '}';
    }
}
