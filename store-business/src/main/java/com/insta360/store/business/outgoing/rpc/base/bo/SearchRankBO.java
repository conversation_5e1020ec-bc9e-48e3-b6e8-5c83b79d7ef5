package com.insta360.store.business.outgoing.rpc.base.bo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/7
 */
public class SearchRankBO implements Serializable {

    /**
     * 粗排表达式名称
     */
    private String fistRankName;

    /**
     * 精排表达式名称
     */
    private String secondRankName;

    /**
     * 排序表达式类型（0：策略排序；1：CAVA脚本)
     *
     * @return
     */
    private Boolean secondRankType;

    /**
     * 参与精排个数
     * <p>
     * 默认值：200
     * 取值范围：【0，2000】
     */
    private Integer reRankSize;

    public String getFistRankName() {
        return fistRankName;
    }

    public void setFistRankName(String fistRankName) {
        this.fistRankName = fistRankName;
    }

    public String getSecondRankName() {
        return secondRankName;
    }

    public void setSecondRankName(String secondRankName) {
        this.secondRankName = secondRankName;
    }

    public Boolean getSecondRankType() {
        return secondRankType;
    }

    public void setSecondRankType(Boolean secondRankType) {
        this.secondRankType = secondRankType;
    }

    public Integer getReRankSize() {
        return reRankSize;
    }

    public void setReRankSize(Integer reRankSize) {
        this.reRankSize = reRankSize;
    }

    @Override
    public String toString() {
        return "SearchRankBO{" +
                "fistRankName='" + fistRankName + '\'' +
                ", secondRankName='" + secondRankName + '\'' +
                ", secondRankType=" + secondRankType +
                ", reRankSize=" + reRankSize +
                '}';
    }
}
