package com.insta360.store.business.integration.wto.wms.exception;

import com.insta360.compass.core.exception.ErrorCode;

/**
 * @Author: py
 * @Date: 2025/01/21
 * @Description:
 */
public enum WmsErrorCode implements ErrorCode {

    CreateShipmentFailedException(105003001, "创建运单失败"),

    OrderNumberNotExistException(105003002, "订单号不存在"),

    ParameterNotCorrectException(105003003, "参数不正确异常"),

    FedexContentBuildException(105003004, "构建Fedex托运所需上下文数据错误"),

    DhlContentBuildException(105003005, "构建Dhl托运所需上下文数据错误"),


    ;

    /**
     * 异常码
     */
    private final Integer code;

    /**
     * 错误描述
     */
    private final String msg;

    WmsErrorCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }
}
