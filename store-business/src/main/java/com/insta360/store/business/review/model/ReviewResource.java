package com.insta360.store.business.review.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.insta360.compass.core.common.BaseModel;
import com.insta360.store.business.review.enums.ReviewCompressStateEnum;
import com.insta360.store.business.review.enums.ReviewResourceTypeEnum;

import java.time.LocalDateTime;

/**
 * @Author: MyBatis Plus Generator
 * @Date: 2023-05-25
 * @Description: 评论资源表
 */
@TableName("review_resource")
public class ReviewResource extends BaseModel<ReviewResource> {

    private static final long serialVersionUID = 1L;

    /**
     * 评论资源ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 评论ID
     */
    private Integer reviewId;

    /**
     * 资源类型（image、video）
     */
    private String type;

    /**
     * 资源地址
     */
    private String resource;

    /**
     * 压缩后的资源地址
     */
    private String compressResource;

    /**
     * 视频封面 略缩图
     */
    private String compressVideoCover;

    /**
     * 预览方形视频封面
     */
    private String squareImage;

    /**
     * 视频封面
     */
    private String videoCover;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 高度
     */
    private Integer height;

    /**
     * 排序字段
     */
    private Integer orderIndex;

    /**
     * 资源状态(0 待审核，1通过，2拒绝)
     */
    private Integer state;

    /**
     * 压缩状态(0未处理，1压缩成功，2压缩失败)
     */
    private Integer compressState;

    /**
     * 方向信息
     */
    private Integer orientation;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 获取压缩状态枚举
     *
     * @return {@link ReviewCompressStateEnum}
     */
    public ReviewCompressStateEnum getCompressStateEnum() {
        return ReviewCompressStateEnum.parse(this.compressState);
    }

    /**
     * 获取资源类型枚举
     *
     * @return {@link ReviewResourceTypeEnum}
     */
    public ReviewResourceTypeEnum getResourceTypeEnum() {
        return ReviewResourceTypeEnum.parse(this.type);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReviewId() {
        return reviewId;
    }

    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getCompressResource() {
        return compressResource;
    }

    public void setCompressResource(String compressResource) {
        this.compressResource = compressResource;
    }

    public String getCompressVideoCover() {
        return compressVideoCover;
    }

    public void setCompressVideoCover(String compressVideoCover) {
        this.compressVideoCover = compressVideoCover;
    }

    public String getSquareImage() {
        return squareImage;
    }

    public void setSquareImage(String squareImage) {
        this.squareImage = squareImage;
    }

    public String getVideoCover() {
        return videoCover;
    }

    public void setVideoCover(String videoCover) {
        this.videoCover = videoCover;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getCompressState() {
        return compressState;
    }

    public void setCompressState(Integer compressState) {
        this.compressState = compressState;
    }

    public Integer getOrientation() {
        return orientation;
    }

    public void setOrientation(Integer orientation) {
        this.orientation = orientation;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ReviewResource{" +
                "id=" + id +
                ", reviewId=" + reviewId +
                ", type='" + type + '\'' +
                ", resource='" + resource + '\'' +
                ", compressResource='" + compressResource + '\'' +
                ", compressVideoCover='" + compressVideoCover + '\'' +
                ", squareImage='" + squareImage + '\'' +
                ", videoCover='" + videoCover + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", orderIndex=" + orderIndex +
                ", state=" + state +
                ", compressState=" + compressState +
                ", orientation=" + orientation +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
