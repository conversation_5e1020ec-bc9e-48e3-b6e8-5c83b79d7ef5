apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: store-job
  name: store-job
  namespace: test
spec:
  selector:
    matchLabels:
      app: store-job
  template:
    metadata:
      labels:
        app: store-job
    spec:
      containers:
        - env:
            # 注册中心Namespace配置
            - name: NACOS_NAMESPACE
              valueFrom:
                configMapKeyRef:
                  name: nacos-namespace
                  key: discovery.namespace
            # 配置中心Namespace配置
            - name: NACOS_NAMESPACE
              valueFrom:
                configMapKeyRef:
                  name: nacos-namespace
                  key: config.namespace
            # 日志汇聚
            - name: aliyun_logs_apps-o-log
              value: stdout
            # nacos的五项全局配置
            - name: GLOBAL_ENV_VAR_NACOS_AK
              valueFrom:
                configMapKeyRef:
                  key: accessKey
                  name: nacos-key-secret
            - name: GLOBAL_ENV_VAR_NACOS_SK
              valueFrom:
                configMapKeyRef:
                  key: secretKey
                  name: nacos-key-secret
            - name: GLOBAL_ENV_VAR_NACOS_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: username
                  name: nacos-key-secret
            - name: GLOBAL_ENV_VAR_NACOS_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: password
                  name: nacos-key-secret
            - name: GLOBAL_ENV_NACOS_URL
              valueFrom:
                configMapKeyRef:
                  key: url
                  name: nacos-key-secret
          image: ${IMAGE}
          name: store-job
          # 存活探测
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /store/job/heathcheck
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 120
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          # 就绪探测
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /store/job/heathcheck
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 2
          # 端口映射
          ports:
            - containerPort: 8080
              protocol: TCP