package com.insta360.store.job.task.cloud;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.cloud.config.CloudStorageConfig;
import com.insta360.store.business.cloud.constant.StoreBenefitConstant;
import com.insta360.store.business.cloud.enums.SkuSubscribeType;
import com.insta360.store.business.cloud.model.CloudStorageSku;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSkuService;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.impl.helper.CloudServiceSubscribeEngineHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 云服务续费预扣款调度任务
 * @Date 2024/8/8
 */
@Component
public class StoreCloudSubscribeRenewEmailTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudSubscribeRenewEmailTask.class);

    @Autowired
    CloudStorageConfig cloudStorageConfig;

    @Autowired
    CloudStorageSkuService cloudStorageSkuService;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    CloudServiceSubscribeEngineHelper cloudServiceSubscribeEngineHelper;


    /**
     * 执行云服务续订调度任务
     *
     * @param param 调度任务的参数，此处未具体使用
     * @return ReturnT<String> 表示任务的执行结果，成功或失败的提示信息
     */
    @XxlJob("storeCloudSubscribeRenewJobHandler")
    public ReturnT<String> execute(String param) {
        // 初始化订阅列表，用于存储符合续订条件的订阅信息
        List<CloudStorageSubscribe> cloudStorageSubscribeList = new ArrayList<>();

        // 查询年包套餐列表，并处理符合续订条件的订阅
        List<CloudStorageSku> yearlySkuList = cloudStorageSkuService.listSkuBySubscribeType(SkuSubscribeType.YEARLY);
        if (CollectionUtils.isNotEmpty(yearlySkuList)) {
            // 提取年包套餐的ID列表
            List<Integer> yearlyCommodityIds = yearlySkuList.stream().map(CloudStorageSku::getCommodityId).collect(Collectors.toList());
            // 获取或设置年包续订提醒天数
            int yearlyRenewRemindDay = Objects.isNull(cloudStorageConfig.getYearlyRenewRemindDay()) ? StoreBenefitConstant.YEARLY_RENEW_REMIND_DAY : cloudStorageConfig.getYearlyRenewRemindDay();
            // 查询并处理即将续订的年包订阅
            Optional.ofNullable(cloudStorageSubscribeService.listNextRenewalSubscribeByTime(yearlyRenewRemindDay, yearlyCommodityIds))
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(list -> cloudStorageSubscribeList.addAll(list));
        }

        // 查询月包套餐列表，并处理符合续订条件的订阅
        List<CloudStorageSku> monthlySkuList = cloudStorageSkuService.listSkuBySubscribeType(SkuSubscribeType.MONTHLY);
        if (CollectionUtils.isNotEmpty(monthlySkuList)) {
            // 提取月包套餐的ID列表
            List<Integer> monthlyCommodityIds = monthlySkuList.stream().map(CloudStorageSku::getCommodityId).collect(Collectors.toList());
            // 获取或设置月包续订提醒天数
            int monthlyRenewRemindDay = Objects.isNull(cloudStorageConfig.getMonthlyRenewRemindDay()) ? StoreBenefitConstant.MONTHLY_RENEW_REMIND_DAY : cloudStorageConfig.getMonthlyRenewRemindDay();
            // 查询并处理即将续订的月包订阅
            Optional.ofNullable(cloudStorageSubscribeService.listNextRenewalSubscribeByTime(monthlyRenewRemindDay, monthlyCommodityIds))
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(list -> cloudStorageSubscribeList.addAll(list));
        }

        // 记录日志，表示任务开始执行，并输出符合规则的订阅订单数据量
        XxlJobLogger.log("商城云服务续订调度任务执行开始. 符合规则的订阅订单数据:{}", cloudStorageSubscribeList.size());

        // 如果没有待续订的订阅记录，则直接返回成功
        if (CollectionUtils.isEmpty(cloudStorageSubscribeList)) {
            return ReturnT.SUCCESS;
        }

        // 遍历云服务订阅列表，处理每条订阅记录的续费提醒及预扣款消息发送
        for (CloudStorageSubscribe cloudStorageSubscribe : cloudStorageSubscribeList) {
            try {
                // 处理订阅记录的续费提醒
                cloudServiceSubscribeEngineHelper.renewalEmailReminder(cloudStorageSubscribe);
            } catch (Exception e) {
                // 异常处理：记录日志，并输出异常信息
                LOGGER.error(String.format("[商城云服务续订调度任务]续费提醒及预扣款处理异常. 订阅记录: %s", JSON.toJSONString(cloudStorageSubscribe)), e);
            }
        }

        // 记录日志，表示任务执行结束
        XxlJobLogger.log("商城云服务续订调度任务执行结束.");
        // 返回任务执行成功
        return ReturnT.SUCCESS;
    }
}
