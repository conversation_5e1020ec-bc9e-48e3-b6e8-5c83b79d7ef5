package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.dto.CloudSubscribeRenewMqDTO;
import com.insta360.store.business.cloud.enums.SubscribeStatus;
import com.insta360.store.business.cloud.enums.SubscribeSubStatus;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.constants.OrderBizMarkConstant;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.aop.annotation.LogOrderStateChange;
import com.insta360.store.business.payment.bo.PaymentChannelBO;
import com.insta360.store.business.payment.bo.PaymentExtra;
import com.insta360.store.business.payment.constants.CkoPaymentConstant;
import com.insta360.store.business.payment.constants.OceanConstant;
import com.insta360.store.business.payment.enums.OceanCreditCardType;
import com.insta360.store.business.payment.enums.PaymentBusinessType;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.service.PaymentService;
import com.insta360.store.business.payment.service.impl.channel.PaymentChannelFactory;
import com.insta360.store.business.payment.service.impl.channel.PaymentChannelService;
import com.insta360.store.business.payment.service.impl.helper.CkoPaymentHelper;
import com.insta360.store.business.payment.service.impl.helper.PaymentHelper;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.service.CreditCardPaymentInfoService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.StoreAccountService;
import com.insta360.store.business.user.service.UserPayInfoService;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2024/08/13
 * @Description: 续费订单扣款处理
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class StoreCloudRenewOrderPaymentConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudRenewOrderPaymentConsumer.class);

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    StoreAccountService storeAccountService;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    PaymentService paymentService;

    @Autowired
    UserAccountHelper userAccountHelper;

    @Autowired
    PaymentChannelFactory paymentChannelFactory;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    CreditCardPaymentInfoService creditCardPaymentInfoService;

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    CkoPaymentHelper ckoPaymentHelper;

    @Autowired
    OrderService orderService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_renew_order_payment)
    @Override
    public Action consume(Message message, ConsumeContext context) {
        // 接收到的消息内容
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("续费扣款已接收到消息... msg:{}", messageBody);

        CloudStorageSubscribe subscribeRecord = null;
        Integer oldSubscribeSubState = null;
        try {
            // 将消息体转换为CloudSubscribeRenewMqDTO对象
            CloudSubscribeRenewMqDTO subscribeRenewMqDto = JSON.parseObject(messageBody, CloudSubscribeRenewMqDTO.class);
            // 检查转换结果是否为空或关键参数是否为空
            if (Objects.isNull(subscribeRenewMqDto) || Objects.isNull(subscribeRenewMqDto.getOrder())) {
                LOGGER.error("续费扣款关键参数为空. msg:{}", messageBody);
                FeiShuMessageUtil.storeGeneralMessage("续费扣款mq接收到关键参数为空!", FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            Order order = subscribeRenewMqDto.getOrder();
            order = orderService.getByOrderNumber(order.getOrderNumber());
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (Objects.isNull(orderPayment)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("续费订单支付信息不存在! 订单号{%s}", order.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            // 已支付订单不再处理
            if (OrderPaymentState.PAYED.equals(orderPayment.paymentState())) {
                LOGGER.info("续费订单已支付. msg:{}", order.getOrderNumber());
                return Action.CommitMessage;
            }

            // 获取用户信息
            StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(order.getUserId());
            if (Objects.isNull(storeAccount)) {
                LOGGER.error(String.format("账号为空. 订单号: {%s}", order.getOrderNumber()));
                FeiShuMessageUtil.storeGeneralMessage(String.format("账号为空. 订单号: {%s}", order.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(storeAccount.getInstaAccount());
            if (Objects.isNull(userPayInfo) || StringUtil.isBlank(userPayInfo.getPaymentMethod())) {
                LOGGER.info(String.format("续费订单账户user pay info不符合续费条件.instaAccount: {%s} userPayInfo:{%s}", storeAccount.getInstaAccount(), userPayInfo));
                FeiShuMessageUtil.storeGeneralMessage(String.format("续费订单账户user pay info不符合续费条件.instaAccount: {%s}",
                        storeAccount.getInstaAccount()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            if (StringUtil.isBlank(userPayInfo.getPayId())) {
                LOGGER.info(String.format("续费订单账户user pay info不符合续费条件.instaAccount: {%s} userPayInfo:{%s}", storeAccount.getInstaAccount(), userPayInfo));
                FeiShuMessageUtil.storeGeneralMessage(String.format("续费订单pay id为空，不符合续费条件.instaAccount: {%s}",
                        storeAccount.getInstaAccount()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                // pay id为空则需要发送续费失败邮件
                paymentHelper.processSubscribeRenewOrderFailed(order);
                return Action.CommitMessage;
            }

            // 重新查询一次订阅记录，确保状态未变更
            subscribeRecord = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(storeAccount.getInstaAccount());
            // 检查订阅状态和订阅子状态是否符合续费条件
            if (Objects.isNull(subscribeRecord) || !SubscribeStatus.SUBSCRIBE_ING.getCode().equals(subscribeRecord.getSubscribeState())
                    || !SubscribeSubStatus.isRenewDeductionState(SubscribeSubStatus.parse(subscribeRecord.getSubscribeSubState()))) {
                LOGGER.info("续费订单账户订阅状态不符合续费条件. subscribeRecord:{}", JSON.toJSONString(subscribeRecord));
                // 发送飞书消息通知异常
                FeiShuMessageUtil.storeGeneralMessage(String.format("续费订单账户订阅状态不符合续费条件. 原始订单号: [%s]，instaAccount: {%s}",
                        order.getOrderNumber(), storeAccount.getInstaAccount()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            // 用户发起过续费失败订单支付，则不再重试
            if (order.isMark(OrderBizMarkConstant.cloud_subscribe_renew_order_pay)) {
                LOGGER.info("用户主动发起续费失败订单支付，不符合续费条件. order:{}", JSON.toJSONString(order));
                return Action.CommitMessage;
            }

            // 更新订阅子状态为续费扣款中
            oldSubscribeSubState = subscribeRecord.getSubscribeSubState();
            subscribeRecord.setSubscribeSubState(SubscribeSubStatus.RENEW_DEDUCTION_ING.getCode());
            cloudStorageSubscribeService.updateCloudSubscribe(subscribeRecord);

            // 获取支付渠道
            ApplicationContextHolder.getApplicationContext().getBean(StoreCloudRenewOrderPaymentConsumer.class)
                    .handleOrderPayment(order, userPayInfo, storeAccount, subscribeRecord, oldSubscribeSubState);
        } catch (Exception e) {
            // 异常处理：记录日志并发送飞书通知
            LOGGER.error(String.format("续费订单扣费异常. 消息体:%s", messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("续费订单扣费异常. 消息体:%s, 异常信息:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);

            // 更新订阅子状态为旧状态
            if (Objects.nonNull(subscribeRecord)) {
                callBackSubscribeSubState(subscribeRecord, oldSubscribeSubState);
            }

            // 根据异常类型决定是否需要重试
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        return Action.CommitMessage;
    }

    /**
     * 支付扣款处理
     *
     * @param order
     * @param userPayInfo
     * @param storeAccount
     * @param subscribeRecord
     * @param oldSubscribeSubState
     */
    @LogOrderStateChange
    public void handleOrderPayment(Order order, UserPayInfo userPayInfo, StoreAccount storeAccount,
                                   CloudStorageSubscribe subscribeRecord, Integer oldSubscribeSubState) {
        // 获取支付渠道
        PaymentChannel paymentChannel;
        PaymentChannelBO paymentChannelParam = new PaymentChannelBO();
        paymentChannelParam.setOrder(order);
        paymentChannelParam.setPaymentBusinessType(PaymentBusinessType.SUBSCRIBE_PAY);

        // 扩展参数
        PaymentExtra paymentExtra = new PaymentExtra();
        paymentExtra.setStoreAccount(storeAccount);
        paymentExtra.setIsRenewDeductionRetry(SubscribeSubStatus.RENEW_DEDUCTION_RETRY_ING.getCode().equals(oldSubscribeSubState)
                ? Boolean.TRUE : Boolean.FALSE);

        StorePaymentMethodEnum storePaymentMethodEnum = userPayInfo.parsePaymentMethod();
        switch (storePaymentMethodEnum) {
            case OCEAN_PAYMENT:
                // 兼容订阅卡种全称
                OceanCreditCardType creditCardType = OceanCreditCardType.parseSubscribeCardName(userPayInfo.getCardType());
                if (Objects.isNull(creditCardType)) {
                    FeiShuMessageUtil.storeGeneralMessage(String.format("钱海续订卡种解析失败！订单号{%s}", order.getOrderNumber()),
                            FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                    throw new InstaException(CommonErrorCode.InvalidParameter);
                }
                CreditCardPaymentInfo oldPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(subscribeRecord.getOrderNumber());
                paymentExtra.setPaymentChannelId(Objects.isNull(oldPaymentInfo) || oldPaymentInfo.getPayChannelId() == -1
                        // 正常流程不会出现pay channel不存在，兜底欧洲通道
                        ? OceanConstant.DEFAULT_OCEAN_CHANNEL : oldPaymentInfo.getPayChannelId());
                paymentChannelParam.setCardName(creditCardType.getCardName());
            case PAYPAL_PAYMENT:
            case KLARNA_PAYMENT:
                PaymentChannelService paymentChannelService = paymentChannelFactory.getPaymentChannel(storePaymentMethodEnum);
                paymentChannel = paymentChannelService.getPaymentChannel(paymentChannelParam);
                break;
            case CKO_PAYMENT:
                CreditCardPaymentInfo oldOrderPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(subscribeRecord.getOrderNumber());
                paymentChannel = ckoPaymentHelper.getCkoSubscribeChannel(order);
                // todo 如果支付通道为空，则走一遍信用卡规则
                paymentExtra.setPaymentChannelId(Objects.isNull(oldOrderPaymentInfo) || oldOrderPaymentInfo.getPayChannelId() == -1
                        // 正常流程不会出现pay channel不存在，兜底香港通道
                        ? CkoPaymentConstant.DEFAULT_CKO_CHANNEL : oldOrderPaymentInfo.getPayChannelId());
                paymentExtra.setClientIP(order.getIp());
                break;
            default:
                throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        Object payResult = paymentService.payOrder(order.getId(), paymentChannel, paymentExtra);
        // 前置续费扣款失败回滚订阅子项状态
        if (!Boolean.TRUE.equals(payResult)) {
            callBackSubscribeSubState(subscribeRecord, oldSubscribeSubState);
        }
    }

    /**
     * 回滚订阅子项状态
     *
     * @param subscribeRecord
     * @param oldSubscribeSubState
     */
    public void callBackSubscribeSubState(CloudStorageSubscribe subscribeRecord, Integer oldSubscribeSubState) {
        LOGGER.info(String.format("回滚订阅子项状态。。。订阅信息{%s}, 旧状态{%s}", subscribeRecord, oldSubscribeSubState));
        CloudStorageSubscribe storageSubscribe = cloudStorageSubscribeService.getById(subscribeRecord.getId());
        if (!SubscribeSubStatus.CLOSED.getCode().equals(storageSubscribe.getSubscribeSubState())
                && !SubscribeSubStatus.RENEW_DEDUCTION_RETRY_ING.getCode().equals(storageSubscribe.getSubscribeSubState())) {
            subscribeRecord.setSubscribeSubState(oldSubscribeSubState);
            cloudStorageSubscribeService.updateCloudSubscribe(subscribeRecord);
        }
    }
}
