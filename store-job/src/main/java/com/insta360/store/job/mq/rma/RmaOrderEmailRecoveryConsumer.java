package com.insta360.store.job.mq.rma;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderCheckRuleService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.rma.email.BaseRmaEmail;
import com.insta360.store.business.rma.email.RmaEmailFactory;
import com.insta360.store.business.rma.enums.RmaReasonKeyEmailEnum;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import com.insta360.store.business.trade.model.StoreEmailSendRule;
import com.insta360.store.business.trade.service.StoreEmailSendRuleService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 售后邮件挽回业务MQ消费
 * @Date 2023/2/13
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class RmaOrderEmailRecoveryConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(RmaOrderEmailRecoveryConsumer.class);

    @Autowired
    private RmaOrderService rmaOrderService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderCheckRuleService orderCheckRuleService;

    @Autowired
    private StoreEmailSendRuleService storeEmailSendRuleService;

    @Autowired
    RmaEmailFactory rmaEmailFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.rma_order_rma_email_recovery)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 消息体转换
        String refundId = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[售后邮件挽回业务MQ消费]收到MQ消息,消息体:{}", message.getBody());
        if (StringUtils.isBlank(refundId)) {
            return Action.CommitMessage;
        }
        // 查询售后单
        RmaOrder rmaOrder = rmaOrderService.getById(Integer.valueOf(refundId));
        // 发送邮件
        sendEmail(rmaOrder);

        return Action.CommitMessage;
    }

    /**
     * 邮件发送处理
     *
     * @param rmaOrder
     */
    public void sendEmail(RmaOrder rmaOrder) {
        // 售后单是否存在
        if (Objects.isNull(rmaOrder)) {
            LOGGER.info("[售后邮件挽回业务MQ消费]邮件发送核心处理,售后单不存在 RmaOrder:{}", JSON.toJSONString(rmaOrder));
            return;
        }

        // 售后单是否'已取消'、'已拒绝'
        if (rmaOrder.isClose()) {
            LOGGER.info("[售后邮件挽回业务MQ消费]邮件发送核心处理,售后单'已取消'或'已拒绝' RmaOrder:{}", JSON.toJSONString(rmaOrder));
            return;
        }

        // 订单是否存在
        Order order = orderService.getById(rmaOrder.getOrderId());
        if (Objects.isNull(order)) {
            LOGGER.info("[售后邮件挽回业务MQ消费]邮件发送核心处理,售后单关联的订单不存在 RmaOrder:{}", JSON.toJSONString(rmaOrder));
            return;
        }

        // 售后原因邮件枚举映射
        RmaReasonKeyEmailEnum reasonKeyEmail = RmaReasonKeyEmailEnum.parse(rmaOrder.getReason(), rmaOrder.getRmaType());
        if (reasonKeyEmail == null) {
            LOGGER.info("[售后邮件挽回业务MQ消费]邮件发送核心处理,售后原因未正确映射上邮件枚举 RmaOrder:{}", JSON.toJSONString(rmaOrder));
            return;
        }
        BaseRmaEmail rmaEmail = rmaEmailFactory.getEmail(rmaOrder, reasonKeyEmail.getClz());

        // 同一个订单只发送一次售后挽回邮件 （是否只会有一种售后原因）
        StoreEmailSendRule storeEmailSendRule = storeEmailSendRuleService.getTemplateSendRuleByOrderId(rmaOrder.getOrderId(), rmaEmail.getRmaBaseTemplateName());

        // 判断是否已经发送过邮件
        if (Objects.nonNull(storeEmailSendRule)) {
            LOGGER.info("[售后邮件挽回业务MQ消费] 存在数据,已经发送过邮件 邮件重复发送了 rmaId:{},storeEmailSendRule:{}", rmaOrder.getId(), JSON.toJSONString(storeEmailSendRule));
            storeEmailSendRule.setUpdateTime(LocalDateTime.now());
            storeEmailSendRuleService.updateById(storeEmailSendRule);
        } else {
            LOGGER.info("发送售后挽回邮件：{}", rmaOrder.getContactEmail());
            doSendEmail(rmaOrder, reasonKeyEmail.getClz());
            this.saveOrderEmailCheckRule(rmaEmail, order.getId());
        }
    }

    /**
     * 保存售后单挽回邮件发送规则
     *
     * @param rmaEmail 售后邮件
     * @param orderId  订单id
     */
    private void saveOrderEmailCheckRule(BaseRmaEmail rmaEmail, Integer orderId) {
        StoreEmailSendRule storeEmailSendRule = new StoreEmailSendRule();
        storeEmailSendRule.setOrderId(orderId);
        storeEmailSendRule.setTemplateKey(rmaEmail.getRmaBaseTemplateName());
        storeEmailSendRule.setCreateTime(LocalDateTime.now());
        storeEmailSendRule.setUpdateTime(LocalDateTime.now());
        storeEmailSendRuleService.save(storeEmailSendRule);
        LOGGER.info("[售后邮件挽回业务MQ消费]售后单挽回邮件发送规则保存成功,storeEmailSendRule:{}", JSON.toJSONString(storeEmailSendRule));
    }

    /**
     * 邮件发送
     *
     * @param rmaOrder
     * @param clsEmail
     */
    private void doSendEmail(RmaOrder rmaOrder, Class<? extends BaseRmaEmail> clsEmail) {
        BaseRmaEmail email = rmaEmailFactory.getEmail(rmaOrder, clsEmail);
        email.doSend(rmaOrder.getContactEmail());
    }
}
