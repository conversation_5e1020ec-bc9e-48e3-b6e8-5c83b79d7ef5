package com.insta360.store.job.mq.insurance;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.insurance.model.InsuranceServiceCommodityBind;
import com.insta360.store.business.insurance.model.InsuranceServiceType;
import com.insta360.store.business.insurance.service.InsuranceServiceTypeService;
import com.insta360.store.business.insurance.service.ServiceCommodityBindService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.OrderInsurance;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderInsuranceService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.outgoing.mq.insurance.dto.InsuranceDTO;
import com.insta360.store.business.product.model.Product;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2022/3/4
 * @Description: 保险服务绑定mq消息消费
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class InsuranceBindMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(InsuranceBindMqConsumer.class);

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderInsuranceService orderInsuranceService;

    @Autowired
    InsuranceServiceTypeService insuranceServiceTypeService;

    @Autowired
    ServiceCommodityBindService serviceCommodityBindService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_created_insurance_bind)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[保险服务绑定]成功接收到消息,消息体[%s]", messageBody));

        InsuranceDTO insuranceParam = JSON.parseObject(messageBody, InsuranceDTO.class);

        List<OrderItem> orderItems = orderItemService.getByOrder(insuranceParam.getOrderId());
        if (CollectionUtils.isEmpty(orderItems)) {
            return Action.ReconsumeLater;
        }

        // 单独下单只会存在一个item
        OrderItem orderItem = orderItems.get(0);
        if (!Product.INSURANCE_SERVICE_PRODUCT.contains(orderItem.getProduct())) {
            LOGGER.info(String.format("[保险服务绑定]绑定失败,原因:[item的产品不属于增值服务],消息体[%s],", messageBody));
            FeiShuMessageUtil.storeGeneralMessage("保险服务绑定出错,订单id：" + insuranceParam.getOrderId(), FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW);
            return Action.CommitMessage;
        }

        Integer commodity = orderItem.getCommodity();

        // 设备&服务类型
        InsuranceServiceCommodityBind commodityBind = serviceCommodityBindService.getByCommodityId(commodity);
        InsuranceServiceType insuranceServiceType = insuranceServiceTypeService.getById(commodityBind.getServiceId());

        OrderInsurance orderInsurance = orderInsuranceService.getBySerial(insuranceParam.getOrderId(), insuranceParam.getDeviceSerial(), insuranceServiceType.getServiceType());
        if (Objects.nonNull(orderInsurance)) {
            LOGGER.info(String.format("[保险服务绑定]重复绑定,本次不予绑定.orderId:[%s],序列号:[%s],保险类型:[%s]",
                    insuranceParam.getOrderId(), insuranceParam.getDeviceSerial(), insuranceServiceType.getServiceType()));
            return Action.CommitMessage;
        }

        // 保存单独购买服务绑定信息，用于激活设备（订单支付后无法拿到设备序列号）
        orderInsurance = new OrderInsurance();
        orderInsurance.setOrderId(insuranceParam.getOrderId());
        orderInsurance.setSerial(insuranceParam.getDeviceSerial());
        orderInsurance.setInsuranceType(insuranceServiceType.getServiceType());
        orderInsurance.setDeviceType(commodityBind.getDeviceType());
        orderInsurance.setCreateTime(LocalDateTime.now());
        orderInsurance.setUpdateTime(LocalDateTime.now());
        orderInsuranceService.save(orderInsurance);

        LOGGER.info(String.format("[保险服务绑定]绑定成功,订单id:%s,serial:%s", orderInsurance.getOrderId(), orderInsurance.getSerial()));
        return Action.CommitMessage;
    }
}
