package com.insta360.store.job.task.order_task;

import com.insta360.store.business.order.email.BaseOrderEmail;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.trade.model.StoreEmailSendRule;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/11/12
 * @Description: 订单任务接口
 */
public interface OrderTask {

    String COUNTRY_KEY = "country_key";

    /**
     * core task run
     */
    void run();

    /**
     * task type
     */
    String getTaskType();

    /**
     * 保存商城邮件发送规则
     *
     * @param needOrder          需要订单
     * @param storeEmailSendRule 商城邮件发送规则
     * @param taskType           任务类型
     * @param orderEmail         订单邮件
     */
    void saveStoreEmailSendRuleSendEmail(Order needOrder, StoreEmailSendRule storeEmailSendRule, String taskType, BaseOrderEmail orderEmail);

    /**
     * get need orders by state
     */
    List<Order> getOrders(OrderState... orderState);

    /**
     * get need orders by email and state
     */
    List<Order> getOrders(String email, OrderState... orderState);

    /**
     * get need orders by create_time
     */
    List<Order> getOrders(LocalDateTime now, OrderState... orderStates);
}
