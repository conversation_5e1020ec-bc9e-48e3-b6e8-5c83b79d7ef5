package com.insta360.store.job.mq.search;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.configuration.search.bo.SearchSyncParametersBO;
import com.insta360.store.business.configuration.search.factory.SearchDataSyncHandlerFactory;
import com.insta360.store.business.configuration.search.handler.SearchDataChangeSyncService;
import com.insta360.store.business.outgoing.mq.search.dto.SearchBusinessDataDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/3
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class SearchChangeDataSyncConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchChangeDataSyncConsumer.class);

    @Autowired
    SearchDataSyncHandlerFactory searchDataSyncHandlerFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_search_data_change_notify)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[商城搜索]OpenSearch数据同步接收到消费消息,msg:{}", messageBody);

        try {
            SearchBusinessDataDTO searchBusinessDataDto = JSON.parseObject(messageBody, SearchBusinessDataDTO.class);
            // 复制对应变更参数
            SearchSyncParametersBO searchSyncParameters = new SearchSyncParametersBO();
            BeanUtils.copyProperties(searchBusinessDataDto, searchSyncParameters);
            searchSyncParameters.setNavigationCategoryBo(searchBusinessDataDto.getNavigationCategoryBo());

            // 获取对应的处理器
            SearchDataChangeSyncService searchDataChangeSyncService = searchDataSyncHandlerFactory.getSearchDataChangeSyncService(searchBusinessDataDto.getSearchDataChangeType());
            searchDataChangeSyncService.syncSearchDataChanges(searchSyncParameters);
        } catch (Exception e) {
            LOGGER.error(String.format("OpenSearch数据同步消费消息异常,msg:%s", messageBody), e);
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        return Action.CommitMessage;
    }
}
