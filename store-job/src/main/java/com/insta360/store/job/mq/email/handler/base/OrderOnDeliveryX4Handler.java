package com.insta360.store.job.mq.email.handler.base;

import cn.hutool.core.util.RandomUtil;
import com.insta360.store.business.admin.email.model.UserGuideEmailRecord;
import com.insta360.store.business.admin.email.service.UserGuideEmailRecordService;
import com.insta360.store.business.email.enums.EmailSendBusinessType;
import com.insta360.store.business.order.email.BaseOrderEmail;
import com.insta360.store.business.order.email.OrderEmailFactory;
import com.insta360.store.business.order.email.OrderOnDeliveryEmailX4;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.email.dto.StoreEmailCommonDTO;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.job.mq.email.handler.BaseEmailSendHandler;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/11/21 上午11:32
 */
@Component
public class OrderOnDeliveryX4Handler extends BaseEmailSendHandler {

    @Autowired
    UserGuideEmailRecordService userGuideEmailRecordService;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductService productService;

    @Autowired
    OrderEmailFactory orderEmailFactory;

    @Override
    public BaseOrderEmail getEmail(StoreEmailCommonDTO storeEmailMessage) {
        // 业务类型校验
        EmailSendBusinessType emailSendBusinessType = this.getEmailSendBusinessType();
        if (emailSendBusinessType == null) {
            return null;
        }

        // 邮件内容校验
        Order order = storeEmailMessage.getOrder();
        if (Objects.isNull(order)) {
            return null;
        }

        // 前置判断
        // 不含X4，不发送
        boolean containX4 = orderService.containProduct(order.getId(), Product.X4_ID);
        if (!containX4) {
            return null;
        }

        // 含其他主机，不发送
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        List<Integer> productIds = orderItems.stream().map(OrderItem::getProduct).collect(Collectors.toList());
        // 非X4主机产品列表
        List<Product> noX4productList = productService.getProducts(productIds).stream().filter(Product::whetherCamera)
                .filter(product -> product.getId() != Product.X4_ID).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noX4productList)) {
            return null;
        }

        UserGuideEmailRecord userGuideEmailRecord = new UserGuideEmailRecord(
                order.getOrderNumber(),
                RandomUtil.simpleUUID(),
                order.getContactEmail(),
                emailSendBusinessType.getEmailTemplateKey(),
                true,
                false,
                LocalDateTime.now());
        userGuideEmailRecordService.save(userGuideEmailRecord);
        // 返回该业务类型的邮件模板
        return orderEmailFactory.getEmail(order, OrderOnDeliveryEmailX4.class);

    }

}
