package com.insta360.store.job.mq.subscribe;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.subscribe.dto.CommoditySubscribeDTO;
import com.insta360.store.business.user.email.CommoditySubscribeEmail;
import com.insta360.store.business.user.email.UserEmailFactory;
import com.insta360.store.business.user.model.UserCommoditySubscribe;
import com.insta360.store.business.user.service.UserCommoditySubscribeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2022/5/7
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class CommoditySubscribeMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(CommoditySubscribeMqConsumer.class);

    @Autowired
    UserCommoditySubscribeService subscribeService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    UserEmailFactory userEmailFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_commodity_subscribe_notify)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        CommoditySubscribeDTO commoditySubscribeDTO = JSON.parseObject(messageBody, CommoditySubscribeDTO.class);

        Integer commodityId = commoditySubscribeDTO.getCommodityId();
        List<String> countryList = commoditySubscribeDTO.getCountryList();
        LOGGER.info("套餐订阅消费：" + commodityId + ":" + countryList.toString());

        // 获得用户订阅信息
        List<UserCommoditySubscribe> userCommoditySubscribes = new ArrayList<>();
        for (String country : countryList) {
            List<UserCommoditySubscribe> subscribes = subscribeService.getSubscribes(commodityId, country);
            userCommoditySubscribes.addAll(subscribes);
        }

        Commodity commodity = commodityService.getById(commodityId);

        Map<InstaCountry, CommoditySubscribeEmail> cache = new HashMap<>();
        FeiShuMessageUtil.storeGeneralMessage("准备发送到货通知邮件" + userCommoditySubscribes.size() + "封。套餐id：" + commodityId + "，国家：" + countryList.toString(), FeiShuGroupRobot.MainNotice);
        for (UserCommoditySubscribe subscribe : userCommoditySubscribes) {
            InstaLanguage language = subscribe.language();
            // 批量发送的 可能同一种语言，但是货币符号不对
            InstaCountry country = subscribe.country();
            CommoditySubscribeEmail arrivalNoticeEmail = cache.get(country);

            if (arrivalNoticeEmail == null) {
                arrivalNoticeEmail = (CommoditySubscribeEmail) userEmailFactory.getEmail(commodity, language, country, CommoditySubscribeEmail.class);
                cache.put(country, arrivalNoticeEmail);
            }

            String toAddress = subscribe.getEmail();
            arrivalNoticeEmail.doSend(toAddress);

            // 发了邮件更新一下数据库
            subscribe.setEmailSend(true);
            subscribeService.updateById(subscribe);
        }
        return Action.CommitMessage;
    }
}
