package com.insta360.store.job.task.cloud;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.impl.helper.CloudServiceSubscribeEngineHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/22
 */
@Component
public class StoreCloudSubscribeRenewOrderTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudSubscribeRenewOrderTask.class);

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    CloudServiceSubscribeEngineHelper cloudServiceSubscribeEngineHelper;

    @XxlJob("storeCloudSubscribeRenewOrderJobHandler")
    public ReturnT<String> execute(String param) {
        // 查询出需进行续费处理的订阅记录
        List<CloudStorageSubscribe> cloudStorageSubscribeList = cloudStorageSubscribeService.listNextRenewalSubscribeByTime(3);
        if (CollectionUtils.isEmpty(cloudStorageSubscribeList)) {
            XxlJobLogger.log("[商城云服务续费订单创建前置处理]本次调度任务未扫描出符合规则的订阅记录。");
            return ReturnT.SUCCESS;
        }

        // 记录日志，表示任务开始执行，并输出符合规则的订阅订单数据量
        LOGGER.info("[商城云服务续费订单创建前置处理]日常调度任务执行开始,符合即将续费的订阅记录数据数量:{}, 具体数据: {}", cloudStorageSubscribeList.size(), JSON.toJSONString(cloudStorageSubscribeList));
        XxlJobLogger.log("[商城云服务续费订单创建前置处理]日常调度任务执行开始,符合即将续费的订阅记录数据数量:{}, 具体数据: {}", cloudStorageSubscribeList.size(), JSON.toJSONString(cloudStorageSubscribeList));

        for (CloudStorageSubscribe cloudStorageSubscribe : cloudStorageSubscribeList) {
            cloudServiceSubscribeEngineHelper.processPreRenewal(cloudStorageSubscribe);
        }

        // 记录日志，表示任务执行结束
        XxlJobLogger.log("[商城云服务续费订单创建前置处理]本次调度任务执行结束.");

        return ReturnT.SUCCESS;
    }
}
