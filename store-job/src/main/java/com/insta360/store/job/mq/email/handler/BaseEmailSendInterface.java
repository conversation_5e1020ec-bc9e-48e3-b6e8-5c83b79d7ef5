package com.insta360.store.job.mq.email.handler;

import com.insta360.store.business.order.email.BaseOrderEmail;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.outgoing.mq.email.dto.StoreEmailCommonDTO;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/11/21 上午11:30
 */
public interface BaseEmailSendInterface {

    /**
     * 封装邮件信息，获取对应的邮件模板
     * @param storeEmailMessage 消息体
     * @return 邮件模板
     */
    BaseOrderEmail getEmail(StoreEmailCommonDTO storeEmailMessage);
}
