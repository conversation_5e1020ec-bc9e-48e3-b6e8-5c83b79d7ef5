package com.insta360.store.job.mq.order;

import cn.hutool.http.HttpException;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.payment.bo.PaymentCaptureBO;
import com.insta360.store.business.payment.enums.PaymentTradeSecurityType;
import com.insta360.store.business.payment.service.impl.handler.BasePaymentHandler;
import com.insta360.store.business.payment.service.impl.handler.PaymentHandlerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: wbt
 * @Date: 2023/04/13
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderOnDeliveryKlarnaCaptureConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderOnDeliveryKlarnaCaptureConsumer.class);

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    PaymentHandlerFactory paymentHandlerFactory;

    /**
     * 订单发货后 klarna 交易捕获
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_on_delivery_klarna_capture)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("接收到 klarna capture 消息投递。消息体:{}", messageBody);

        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.error("订单发货后klarna交易捕获失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 只关注支付渠道为klarna的订单
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            PaymentChannel paymentChannel = orderPayment.paymentChannel();
            if (!PaymentChannel.isKlarnaChannel(orderPayment.paymentChannel())) {
                LOGGER.error("支付渠道不是klarna。不予处理。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 交易安全等级校验
            if (!PaymentTradeSecurityType.HAVE_NOT_CAPTURE.equals(orderPayment.tradeSecurityType())) {
                LOGGER.error("交易安全等级不满足。不予处理。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // capture 参数封装
            PaymentCaptureBO paymentCapture = new PaymentCaptureBO();
            paymentCapture.setOrderNumber(order.getOrderNumber());

            // klarna capture
            BasePaymentHandler paymentHandler = paymentHandlerFactory.getPaymentHandler(paymentChannel);
            paymentHandler.paymentCapture(paymentCapture);
        } catch (Exception e) {
            if (e instanceof HttpException) {
                // 如果是网络异常则重投递
                LOGGER.error(String.format("klarna capture network error. error_message:{%s}, messsage_body:{%s}", e.getMessage(), messageBody), e);
                return Action.ReconsumeLater;
            }
            LOGGER.error(String.format("klarna capture other error. error_message:{%s}, messsage_body:{%s}", e.getMessage(), messageBody), e);
        }
        return Action.CommitMessage;
    }
}
