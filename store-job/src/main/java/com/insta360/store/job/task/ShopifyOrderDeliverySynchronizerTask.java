package com.insta360.store.job.task;

import com.insta360.store.business.integration.guanyi.lib.model.GyOrderDelivery;
import com.insta360.store.business.integration.guanyi.service.GuanyiSyncService;
import com.insta360.store.business.integration.shopify.module.ShopifyOrder;
import com.insta360.store.business.integration.shopify.service.ShopifyOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/05/22
 * @Description: 将管易系统订单的发货信息同步到Shopify订单
 */
@Component
public class ShopifyOrderDeliverySynchronizerTask {

    public static final Logger logger = LoggerFactory.getLogger(ShopifyOrderDeliverySynchronizerTask.class);

    @Autowired
    ShopifyOrderService shopifyOrderService;

    @Autowired
    GuanyiSyncService guanyiSyncService;

    @XxlJob("shopifyOrderDeliverySyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("将管易系统订单的发货信息同步到Shopify订单_任务开始...");
        // 相同订单每隔10分钟重新同步一次
        List<ShopifyOrder> orderList = shopifyOrderService.getUnshippedOrders();
        if (CollectionUtils.isEmpty(orderList)) {
            return ReturnT.SUCCESS;
        }
        for (ShopifyOrder order : orderList) {
            try {
                boolean success = syncOrder(order);
                logger.info("Shop订单同步管易发货信息成功... orderNumber:{},syncResult:{}", order.getOrderNumber(), success);
            } catch (Exception e) {
                logger.error(String.format("Shop订单同步管易发货信息异常... orderNumber:%s", order.getOrderNumber()), e);
            }
        }

        XxlJobLogger.log("将管易系统订单的发货信息同步到Shopify订单_任务结束...");
        return ReturnT.SUCCESS;
    }

    private boolean syncOrder(ShopifyOrder order) {
        try {
            GyOrderDelivery gyOrderDelivery = guanyiSyncService.syncGuanyiOrderDelivery(order);
            boolean syncSuccess = false;

            if (gyOrderDelivery != null) {
                // 保存快递信息
                String trackNumber = gyOrderDelivery.getMail_no();
                if (trackNumber != null) {
                    trackNumber = trackNumber.trim();
                }
                order.setTrackCompany(gyOrderDelivery.getExpress_code());
                order.setTrackNumber(trackNumber);
                shopifyOrderService.updateById(order);

                // 同步快递信息到Shopify
                syncExpressInfoToShopify(order);

                syncSuccess = true;
            }

            order.setTrackSyncTime(LocalDateTime.now());
            shopifyOrderService.updateById(order);
            return syncSuccess;
        } catch (Exception e) {
        }
        return false;
    }

    private void syncExpressInfoToShopify(ShopifyOrder order) {
        shopifyOrderService.syncTrackingInfo(order);
    }
}
