package com.insta360.store.job.task.payment.ocean;

import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.payment.service.OceanPaymentTransactionRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/8
 */
@Component
public class OceanPayInfoRecordTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(OceanPayInfoRecordTask.class);

    @Autowired
    OceanPaymentTransactionRecordService oceanPaymentTransactionRecordService;

    @XxlJob("pullOceanPayInfo")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("开始拉取前海支付信息数据_任务开始...");
        try {
            oceanPaymentTransactionRecordService.pullOceanPaymentTransactionData(param);
        } catch (Exception e) {
            LOGGER.error("拉取前海支付信息数据_任务异常", e);
            FeiShuMessageUtil.storeGeneralMessage("拉取前海支付信息数据_任务异常" + e.getMessage(), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
            XxlJobLogger.log("拉取前海支付信息数据_任务异常...");
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("拉取前海支付信息数据_任务结束...");
        return ReturnT.SUCCESS;
    }
}
