package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.ThreadUtil;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.api.ResponseCode;
import com.insta360.compass.libs.aliyun.sms.SMSService;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.email.BaseCloudEmail;
import com.insta360.store.business.cloud.email.CloudEmailFactory;
import com.insta360.store.business.cloud.email.CloudSubscribeRenewSuccessEmail;
import com.insta360.store.business.cloud.email.CloudSubscribeSuccessEmail;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.configuration.utils.ProfileUtil;
import com.insta360.store.business.discount.dto.bo.PaymentChannelDiscountCalculateBO;
import com.insta360.store.business.discount.dto.bo.PaymentChannelDiscountResultBO;
import com.insta360.store.business.discount.service.impl.helper.PaymentChannelDiscountHelper;
import com.insta360.store.business.integration.avalara.bo.AvalaraPaymentDiscountResultBO;
import com.insta360.store.business.integration.avalara.bo.ItemTaxInfoBO;
import com.insta360.store.business.integration.avalara.bo.TaxRecalculationBO;
import com.insta360.store.business.integration.avalara.constant.TaxConstant;
import com.insta360.store.business.integration.avalara.enums.AvalaraTransactionType;
import com.insta360.store.business.integration.avalara.enums.StoreTaxType;
import com.insta360.store.business.integration.avalara.enums.StoreTransactionType;
import com.insta360.store.business.integration.avalara.service.factory.TaxSimpleFactory;
import com.insta360.store.business.integration.avalara.service.hadler.TaxService;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.PriceService;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.email.*;
import com.insta360.store.business.order.model.*;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderItemAverageService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.handler.OrderCoreCalculateHandler;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.outgoing.mq.cloud.helper.StoreCloudProductMessageSendHelper;
import com.insta360.store.business.outgoing.mq.cloud.helper.StorePspMessageSendHelper;
import com.insta360.store.business.outgoing.mq.discount.helper.DiscountMessageSendHelper;
import com.insta360.store.business.outgoing.mq.insurance.helper.InsuranceMessageSendHelper;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import com.insta360.store.business.outgoing.mq.reseller.helper.ResellerSendMessageHelper;
import com.insta360.store.business.outgoing.mq.weapp.helper.WeappMessageSendHelper;
import com.insta360.store.business.outgoing.rpc.point.dto.PointAccount;
import com.insta360.store.business.outgoing.rpc.point.service.PointService;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.trade.bo.CalculateTaxBO;
import com.insta360.store.business.trade.model.CreditCardPaymentInfo;
import com.insta360.store.business.trade.service.CreditCardPaymentInfoService;
import com.insta360.store.business.tradeup.email.BaseTradeupEmail;
import com.insta360.store.business.tradeup.email.TradeupEmailFactory;
import com.insta360.store.business.tradeup.email.TradeupPayedSuccessEmail;
import com.insta360.store.business.tradeup.model.TradeupOrder;
import com.insta360.store.business.tradeup.service.TradeupOrderService;
import com.insta360.store.business.user.service.StoreAccountService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/02/15
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderPayedMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderPayedMqConsumer.class);

    /**
     * insta 课程短信模版
     */
    private static final String INSTA_COURSE_SHORT_MESSAGE = "SMS_160275239";

    /**
     * trade up 订单支付成功短信模版
     */
    private static final String TRADEUP_ORDER_PAYED_SHORT_MESSAGE = "SMS_205409090";

    @Autowired
    SMSService smsService;

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    PriceService priceService;

    @Autowired
    OrderService orderService;

    @Autowired
    PointService pointService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    TaxSimpleFactory taxSimpleFactory;

    @Autowired
    OrderEmailFactory orderEmailFactory;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    TradeupOrderService tradeupOrderService;

    @Autowired
    TradeupEmailFactory tradeupEmailFactory;

    @Autowired
    StoreAccountService storeAccountService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    WeappMessageSendHelper weappMessageSendHelper;

    @Autowired
    OrderItemAverageService orderItemAverageService;

    @Autowired
    DiscountMessageSendHelper discountMessageSendHelper;

    @Autowired
    ResellerSendMessageHelper resellerSendMessageHelper;

    @Autowired
    OrderCoreCalculateHandler orderCoreCalculateHandler;

    @Autowired
    InsuranceMessageSendHelper insuranceMessageSendHelper;

    @Autowired
    CreditCardPaymentInfoService creditCardPaymentInfoService;

    @Autowired
    PaymentChannelDiscountHelper paymentChannelDiscountHelper;

    @Autowired
    StoreCloudProductMessageSendHelper storeCloudProductMessageSendHelper;

    @Autowired
    CloudEmailFactory cloudEmailFactory;

    @Autowired
    StorePspMessageSendHelper storePspMessageSendHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_payed)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("接收到订单支付成功事件推送。消息体:{}", messageBody);

        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.error("订单支付事件消费失败。订单不存在。订单id:{}", messageBody);
                return Action.CommitMessage;
            }

            // 订单支付信息
            OrderPayment orderPayment = orderMessage.getOrderPayment();
            LOGGER.info("order payment info:{}", orderPayment);
            if (orderPayment == null) {
                LOGGER.error("payment info not found.order_number:{}", order.getOrderNumber());
                return Action.CommitMessage;
            }

            // 云服务订单订阅记录场景通知
            storeCloudProductMessageSendHelper.sendCloudProductSubscribeCreateMessage(order, orderPayment);

            // psp权益订单下发处理
            storePspMessageSendHelper.sendStorePspCreateMessage(order, orderPayment);

            // klarna 支付订单capture有效期监控
            String delayTime = storeConfigService.getConfigValue(StoreConfigKey.klarna_capture_monitor);
            LOGGER.info("delayTime:{}", delayTime);
            orderMessageSendHelper.sendOrderPayedKlarnaCaptureMonitorMessage(order, Long.valueOf(delayTime));

            // 支付成功保险激活通知
            LocalDateTime orderPayTime = orderPayment.getPayTime();
            insuranceMessageSendHelper.sendOrderPayedInsuranceActivationMessage(order, orderPayTime);

            // 订单支付后同步创建分销订单
            resellerSendMessageHelper.sendCreateResellerOrderMsg(order, orderPayTime);

            // 工单订单需要同步工单系统
            orderMessageSendHelper.sendSyncCsOrderMessage(order, orderPayment);

            // 积分抽奖代金券使用结果同步
            discountMessageSendHelper.sendGiftCardUseInfoMessage(order);

            // 微信小程序订单支付后的数据同步
            weappMessageSendHelper.sendOrderSyncWeappMessage(order);

            // 支付后交易对账
            orderMessageSendHelper.sendAfterPaymentAmountCheckingMessage(orderPayment);

            // 支付后自动推单（自动备货）
            orderMessageSendHelper.sendToGyAutoMessage(order);

            // 库存扣减
            orderMessageSendHelper.sendStockDeductionMessage(order);

            // 增加积分
            this.addOrderPoint(order, orderPayment);

            // 以旧换新订单支付成功（短信/邮件的发送）
            this.tradeUpOrderPayed(order);

            // 支付成功邮件发送
            this.sendEmail(order);

            // 支付相关数据的记录
            this.savePaymentInfo(order);

            // 设置发货时间
            this.updateDeliveryTime(order, orderPayTime);

            // 支付后后置处理
            ThreadUtil.execute(() -> this.afterPaymentExecution(order, orderPayment, orderPayment.paymentChannel(), orderPayTime));

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("订单支付后消费处理异常。消息体:{%s}. 原因:{%s}", messageBody, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单支付后消费处理异常。消息体:{%s}. 原因:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return Action.CommitMessage;
        }
    }

    /**
     * 设置发货时间
     *
     * @param order
     * @param orderPayTime
     */
    private void updateDeliveryTime(Order order, LocalDateTime orderPayTime) {
        // 更新预计发货时间
        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        if (Objects.isNull(orderDelivery)) {
            LOGGER.info("订单支付后,更新预计发货时间失败. order : {}", JSON.toJSONString(order));
//            FeiShuMessageUtil.storeGeneralMessage(String.format("订单支付后,更新预计发货时间失败. 订单号[%s]", order.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW, FeiShuAtUser.WKX);
            return;
        }
        Integer estimateDays = orderDelivery.getEstimateDays();
        if (orderDelivery.getEstimatedDeliveryDate() == null && estimateDays != null) {
            orderDelivery.setEstimatedDeliveryDate(orderPayTime.toLocalDate().plusDays(estimateDays));
            orderDeliveryService.updateById(orderDelivery);
        }
    }

    /**
     * 支付后处理
     *
     * @param order
     * @param orderPayment
     * @param paymentChannel
     * @param payTime
     */
    private void afterPaymentExecution(Order order, OrderPayment orderPayment, PaymentChannel paymentChannel, LocalDateTime payTime) {
        // avalara计税通知
        try {
            this.avalaraTransaction(order, paymentChannel, payTime);
        } catch (Exception e) {
            LOGGER.error(String.format("订单支付后,调用Avalara处理异常... orderNumber:%s,paymentChannel:%s", order.getOrderNumber(), paymentChannel.name()), e);
        }

        // paypal折扣分摊
        try {
            this.savePayPalDiscount(order, orderPayment, paymentChannel, payTime);
        } catch (Exception e) {
            LOGGER.error(String.format("订单支付后,支付渠道优惠折扣分摊异常... orderNumber:%s,paymentChannel:%s", order.getOrderNumber(), paymentChannel.name()), e);
        }

        // 处理对公转账渠道告警
        this.handleTransferPayment(order, paymentChannel);
    }

    /**
     * 处理对公转账渠道
     *
     * @param order
     * @param paymentChannel
     */
    private void handleTransferPayment(Order order, PaymentChannel paymentChannel) {
        if (PaymentChannel.isTransferPayments(paymentChannel)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单号%s，新增对公转账订单，请及时认款和推单！", order.getOrderNumber()),
                    FeiShuGroupRobot.PaymentInfo, FeiShuAtUser.LZJ, FeiShuAtUser.ZM);
        }
    }

    /**
     * Avalara 计税
     *
     * @param order
     * @param payTime
     */
    private void avalaraTransaction(Order order, PaymentChannel paymentChannel, LocalDateTime payTime) {
        if (StoreTaxType.NORMAL.equals(order.storeTaxType()) || InstaCountry.CA.equals(order.country())) {
            LOGGER.info("非计税订单不用进行Avalara同步。order_number:{}, payment_channel:{}", order.getOrderNumber(), paymentChannel.name());
            return;
        }

        if (TaxConstant.NOT_COMMIT_CHANNEL.contains(paymentChannel)) {
            LOGGER.info("领用订单不用进行Avalara同步。order_number:{}, payment_channel:{}", order.getOrderNumber(), paymentChannel.name());
            return;
        }

        String orderNumber = order.getOrderNumber();
        StoreTransactionType storeTransactionType = null;
        switch (order.storeTaxType()) {
            case NORMAL:
                LOGGER.info("未使用计税。不予处理。order_number:{}", orderNumber);
                break;
            // 正常commit
            case AVALAR:
                storeTransactionType = StoreTransactionType.ORDER_PAYED_TRANSACTION;
                break;
            // 离线税率要创建计税记录
            case OFFLINE:
            case FINALLY:
            case FASTEN:
                storeTransactionType = StoreTransactionType.ORDER_PAYED_OFFLINE_TRANSACTION;
                break;
            default:
                break;
        }
        if (storeTransactionType == null) {
            return;
        }

        // 计税处理
        CalculateTaxBO calculateTax = new CalculateTaxBO();
        calculateTax.setOrderPayTime(payTime);
        calculateTax.setOrderNumber(orderNumber);
        calculateTax.setContactEmail(order.getContactEmail());
        calculateTax.setAvalaraTransactionType(AvalaraTransactionType.SALES_INVOICE);
        calculateTax.setPrePaymentChannel(paymentChannel.name());
        calculateTax.setOrderPayTime(payTime);
        LOGGER.info("avalara transaction commit begin. order_number:{}, transaction_type:{}, pay_time:{}", orderNumber, storeTransactionType.getNameEn(), payTime);
        TaxService taxService = taxSimpleFactory.getTaxService(storeTransactionType, order.country());
        taxService.executeTransaction(calculateTax);
        LOGGER.info("avalara transaction commit end. order_number:{}, transaction_type:{}, pay_time:{}", orderNumber, storeTransactionType.getNameEn(), payTime);
    }

    /**
     * paypal支付活动折扣（活动结束后版本下线）
     *
     * @param order
     * @param orderPayment
     * @param paymentChannel
     * @param payTime
     */
    private void savePayPalDiscount(Order order, OrderPayment orderPayment, PaymentChannel paymentChannel, LocalDateTime payTime) {
        LOGGER.info("订单支付后,PayPal支付折扣分摊处理开始... orderNumber:{},paymentChannel:{}", order.getOrderNumber(), paymentChannel.name());

        // 是否非计税订单
        boolean isNotTaxOrder = StoreTaxType.NORMAL.equals(order.storeTaxType());
        // 订单固定税金
        BigDecimal fixedTax = BigDecimal.ZERO;
        if (isNotTaxOrder) {
            fixedTax = new BigDecimal(String.valueOf(orderPayment.getTax()));
        }

        // 获取商品
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());

        // 运费
        BigDecimal shippingCost = new BigDecimal(String.valueOf(orderPayment.getShippingCost()));

        // 订单总税费
        BigDecimal orderTotalTax = new BigDecimal(String.valueOf(orderPayment.getTax()));

        // 商品总消费税(包含运费税费)
        BigDecimal avalaraTax = orderTotalTax.subtract(fixedTax);

        // 支付优惠计算
        PaymentChannelDiscountCalculateBO discountCalculateBo = new PaymentChannelDiscountCalculateBO();
        discountCalculateBo.setOrderItemList(orderItems);
        discountCalculateBo.setPaymentChannel(paymentChannel.name());
        discountCalculateBo.setArea(order.getArea());
        discountCalculateBo.setPayTime(payTime);
        discountCalculateBo.setShippingFee(shippingCost);
        discountCalculateBo.setFixedTax(fixedTax);

        PaymentChannelDiscountResultBO paymentChannelDiscountResult = paymentChannelDiscountHelper.discountCalculate(discountCalculateBo);
        if (Objects.isNull(paymentChannelDiscountResult)) {
            LOGGER.info("订单支付后,订单不符合支付渠道优惠规则. orderNumber:{},paymentChannel:{}", order.getOrderNumber(), paymentChannel.name());
            return;
        }

        // 支付优惠后订单总消费税（包含运费税费）
        BigDecimal avalaraNewTax = BigDecimal.ZERO;
        // 商品消费税明细Map
        ArrayListMultimap<Integer, ItemTaxInfoBO> itemTaxInfoMap = ArrayListMultimap.create();
        if (!isNotTaxOrder) {
            // 计算支付优惠后的子项税费结果
            TaxRecalculationBO taxRecalculation = new TaxRecalculationBO();
            taxRecalculation.setOrder(order);
            taxRecalculation.setOrderPayment(orderPayment);
            taxRecalculation.setPaymentChannel(paymentChannel.name());
            taxRecalculation.setPayTime(payTime);

            TaxService taxService = taxSimpleFactory.getTaxService(StoreTransactionType.ORDER_PAYED_AGAIN_TRANSACTION, order.country());
            AvalaraPaymentDiscountResultBO avalaraPaymentDiscountResult = taxService.paymentDiscountTaxCalculation(taxRecalculation);
            // 支付优惠后的商品总消费税
            avalaraNewTax = Objects.isNull(avalaraPaymentDiscountResult) ? avalaraTax : avalaraPaymentDiscountResult.getTotalTax();
            // 商品税费明细Map
            itemTaxInfoMap = Objects.isNull(avalaraPaymentDiscountResult) ? ArrayListMultimap.create() : avalaraPaymentDiscountResult.getItemTaxInfoMap();
        }

        // 订单支付优惠总额
        BigDecimal paymentTotalDiscountAmount = paymentChannelDiscountResult.getPaymentChannelTotalDiscount();
        // 订单总优惠金额
        BigDecimal orderTotalDiscountAmount = paymentTotalDiscountAmount.add(new BigDecimal(String.valueOf(orderPayment.getTotalDiscountFee())));
        // 重置订单总税费
        orderTotalTax = fixedTax.subtract(paymentChannelDiscountResult.getFixedTaxPayDiscount()).add(avalaraNewTax);

        // 重置订单支付信息
        orderPayment.setTax(orderTotalTax.floatValue());
        orderPayment.setTotalDiscountFee(orderTotalDiscountAmount.floatValue());
        orderPayment.setCouponFee(orderTotalDiscountAmount.floatValue());
        orderPayment.setPaymentChannelDiscountMark(Boolean.TRUE);
        // 支付优惠后的运费税费
        List<ItemTaxInfoBO> shippingCostTaxInfos = itemTaxInfoMap.get(TaxConstant.SHIPPING_FEE_COMMODITY_ID);
        if (CollectionUtils.isNotEmpty(shippingCostTaxInfos)) {
            BigDecimal shippingCostTax = shippingCostTaxInfos.stream().map(ItemTaxInfoBO::getTax).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderPayment.setShippingCostTax(shippingCostTax);
        }

        // 订单商品优惠金额均摊结果（包含交易券优惠、支付优惠）
        List<OrderItemAverage> orderItemDiscountAverageList = Lists.newArrayList();

        // 重置商品优惠金额、税费
        List<OrderItem> orderItemList = paymentChannelDiscountResult.getOrderItemList();
        for (OrderItem orderItem : orderItemList) {
            if (orderItem.getIsGift()) {
                continue;
            }
            // 支付优惠金额均摊结果
            List<OrderItemAverage> orderItemPaymentDiscountAverageList = orderCoreCalculateHandler.averageCalculate(orderItem, orderItem.getTotalPayChannelDiscount());
            // 交易券优惠金额均摊结果
            List<OrderItemAverage> orderItemAverageList = orderItemAverageService.listByOrderItemId(orderItem.getId());
            if (CollectionUtils.isEmpty(orderItemAverageList)) {
                orderItemAverageList = orderItemPaymentDiscountAverageList;
            } else {
                Map<Integer, Double> averageAmountMap = orderItemPaymentDiscountAverageList.stream()
                        .collect(Collectors.toMap(OrderItemAverage::getSortId, OrderItemAverage::getAverageDiscount));
                orderItemAverageList.forEach(orderItemAverage -> {
                    // 支付优惠均摊金额
                    Double paymentAverageDiscount = averageAmountMap.get(orderItemAverage.getSortId());
                    BigDecimal averageDiscount = new BigDecimal(String.valueOf(orderItemAverage.getAverageDiscount())).add(new BigDecimal(String.valueOf(paymentAverageDiscount)));
                    orderItemAverage.setAverageDiscount(averageDiscount.doubleValue());
                });
            }
            orderItemDiscountAverageList.addAll(orderItemAverageList);

            // 重置商品消费税
            BigDecimal itemTotalTax = orderItem.getTotalTax();
            List<ItemTaxInfoBO> itemTaxInfos = itemTaxInfoMap.get(orderItem.getId());
            if (CollectionUtils.isNotEmpty(itemTaxInfos)) {
                itemTotalTax = itemTaxInfos.stream().map(ItemTaxInfoBO::getTax).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 重置后的商品总优惠金额（交易券优惠 + 支付优惠）
            BigDecimal totalDiscount = orderItem.getTotalDiscount().add(orderItem.getTotalPayChannelDiscount());
            // 重置后的商品平均优惠 （不准确）
            BigDecimal itemDiscountFree = totalDiscount.divide(new BigDecimal(String.valueOf(orderItem.getNumber())), 2, BigDecimal.ROUND_HALF_UP);

            orderItem.setTotalDiscount(totalDiscount);
            orderItem.setDiscountFee(itemDiscountFree.floatValue());
            orderItem.setTotalTax(itemTotalTax);
        }

        // 更新订单明细
        orderHelper.batchUpdateOrderInfo(order, orderPayment, orderItemList, orderItemDiscountAverageList);

        LOGGER.info("订单支付后,PayPal支付折扣分摊处理结束... orderNumber:{},paymentChannel:{}", order.getOrderNumber(), paymentChannel.name());
    }

    /**
     * trade up订单支付成功
     *
     * @param order
     */
    private void tradeUpOrderPayed(Order order) {
        LOGGER.info("trade up order payed...");
        // 校验是否是trade up订单
        TradeupOrder tradeupOrder = tradeupOrderService.getByGiftCardCode(order.getGiftCardCode());
        if (tradeupOrder == null) {
            return;
        }

        LOGGER.info("执行trade up订单支付成功发送短信、邮件的处理，开始。订单：" + order.getOrderNumber());
        // 中国地区发短信
        if (InstaCountry.CN.equals(tradeupOrder.country())) {
            String cnReceiverPhone = storeConfigService.getConfigValue(StoreConfigKey.tradeup_cn_receiver_phone);
            JSONObject para = new JSONObject();
            para.put("order_number", order.getOrderNumber());
            para.put("phone", cnReceiverPhone);

            OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
            if (orderDelivery == null) {
                return;
            }

            String phone = orderDelivery.getPhone();
            try {
                smsService.sendMessage("Insta360影石", TRADEUP_ORDER_PAYED_SHORT_MESSAGE, para, phone);
            } catch (Exception e) {
                LOGGER.error(String.format("【以旧换新订单支付成功通知】短信发送失败。模版:{%s}，手机号:{%s}", TRADEUP_ORDER_PAYED_SHORT_MESSAGE, orderDelivery.getPhone()), e);
                String message = "【以旧换新订单支付成功通知】短信发送失败。原因：" + e.getMessage() +
                        "。模版：" + TRADEUP_ORDER_PAYED_SHORT_MESSAGE + "，手机号：" + phone;
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice);
            }
        }

        // TODO: 2020/4/11 trade up 邮件A
        BaseTradeupEmail email = tradeupEmailFactory.getEmail(tradeupOrder, TradeupPayedSuccessEmail.class);
        email.doSend(tradeupOrder.getEmail());
        LOGGER.info("执行trade up订单支付成功后发送短信、邮件的处理，结束。订单：" + order.getOrderNumber());
    }

    /**
     * 支付成功邮件发送
     *
     * @param order
     */
    private void sendEmail(Order order) {
        LOGGER.info("send email...");
        // 工单订单不发邮件
        if (order.isRepairOrder()) {
            return;
        }

        // 续费订单只发送续费成功邮件
        if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            // 发送云订阅成功邮件
            BaseCloudEmail renewSubscribeSuccessEmail = cloudEmailFactory.getSubscribeOrderEmail(order, CloudSubscribeRenewSuccessEmail.class);
            renewSubscribeSuccessEmail.doSend(order.getContactEmail());
            return;
        }

        String toAddress = order.getContactEmail();
        if (orderService.containProduct(order.getId(), Product.PRO2_ID)) {
            // pro2邮件
            BaseOrderEmail email = orderEmailFactory.getEmail(order, OrderPayedEmailPro2.class);
            email.doSend(toAddress);
        } else if (orderService.containsCommodity(order.getId(), Commodity.TITAN_BOOKING_ID)) {
            // titan预订邮件
            BaseOrderEmail email = orderEmailFactory.getEmail(order, OrderPayedEmailTitan.class);
            email.doSend(toAddress);
        } else {
            // 纯订阅商品订单不发送支付成功邮件
            if (!orderHelper.isPureSubscribeOrder(order)) {
                BaseOrderEmail email = orderEmailFactory.getEmail(order, OrderPayedEmail.class);
                email.doSend(toAddress);
            }
        }

        // 购买Insta课程的订单发短信
        if (orderService.containProduct(order.getId(), Product.INSTA_COURSE_ID)) {
            if (InstaCountry.CN.equals(order.country())) {
                OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", ProfileUtil.getFullName(orderDelivery.getFirstName(), orderDelivery.getLastName()));
                jsonObject.put("order_num", order.getOrderNumber());
                try {
                    smsService.sendMessage("Insta360", INSTA_COURSE_SHORT_MESSAGE, jsonObject, orderDelivery.getPhone());
                } catch (Exception e) {
                    LOGGER.error(String.format("【购买Insta课程支付成功短信推送】短信发送失败。模版:{%s}，手机号:{%s}", INSTA_COURSE_SHORT_MESSAGE, orderDelivery.getPhone()), e);
                    String message = "【购买Insta课程支付成功短信推送】短信发送失败。原因：" + e.getMessage() +
                            "。模版：" + INSTA_COURSE_SHORT_MESSAGE + "，手机号：" + orderDelivery.getPhone();
                    FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice);
                }
            }
        }

        if (!orderHelper.isPureSubscribeOrder(order)) {
            // 台湾地区订单支付成功，需要发送'实名认证邮件通知'邮件
            if (InstaCountry.TW.equals(order.country())) {
                BaseOrderEmail email = orderEmailFactory.getEmail(order, OrderAuthenticationNotifyEmail.class);
                email.doSend(order.getContactEmail());
            }
        }

        // 发送订阅邮件
        if (order.getCloudSubscribeMark()) {
            // 发送云订阅成功邮件
            BaseCloudEmail subscribeSuccessEmail = cloudEmailFactory.getSubscribeOrderEmail(order, CloudSubscribeSuccessEmail.class);
            subscribeSuccessEmail.doSend(order.getContactEmail());
        }
    }

    /**
     * 增加积分
     *
     * @param order
     * @param orderPayment
     */
    private void addOrderPoint(Order order, OrderPayment orderPayment) {
        LOGGER.info("add order point...");
        if (order.isGuestOrder() || order.isRepairOrder()) {
            return;
        }

        // 用户信息
        Integer userId = order.getUserId();
        if (Objects.isNull(userId)) {
            return;
        }

        // 换算成美元，1美元1分。金额为0不加积分。
        Price price = priceService.changeCurrency(orderPayment.getTotalPayPrice(), Currency.USD);
        int credit = (int) price.getAmount();
        if (credit == 0) {
            return;
        }

        try {
            // 添加积分
            PointAccount pointAccount = new PointAccount();
            pointAccount.setPoints(credit);
            pointAccount.setAccount(userId);
            pointAccount.setMark(order.getOrderNumber());
            pointAccount.setTime(TimeUtil.toTimestamp(LocalDateTime.now()));
            pointAccount.setAction("store_order");
            Response addPointResponse = pointService.putAndCheck(pointAccount);
            LOGGER.info("add order point...  addPointResponse：{}", addPointResponse.toString());
            if (ResponseCode.FAIL.equals(addPointResponse.getCode())) {
                FeiShuMessageUtil.storeGeneralMessage("积分新增调用失败。", FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("积分新增调用失败。account:{%s},credit:{%s},orderNumber:{%s}", userId, credit, order.getOrderNumber()), e);
            FeiShuMessageUtil.storeGeneralMessage("积分新增调用失败。", FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW);
        }
    }

    /**
     * 记录支付相关的信息
     *
     * @param order
     */
    private void savePaymentInfo(Order order) {
        CreditCardPaymentInfo creditCardPaymentInfo = creditCardPaymentInfoService.getByOrderNumber(order.getOrderNumber());
        if (creditCardPaymentInfo == null) {
            return;
        }

        // 记录过的不在重复记录，本次记录是对运营后台的状态变动做的补偿措施
        if (creditCardPaymentInfo.getPaymentResult() || StorePaymentMethodEnum.parse(creditCardPaymentInfo.getPayMethod()) != null) {
            return;
        }

        // save pay result
        LocalDateTime now = LocalDateTime.now();
        creditCardPaymentInfo.setUpdateTime(now);
        creditCardPaymentInfo.setOrderPayTime(now);
        creditCardPaymentInfo.setPaymentResult(true);
        creditCardPaymentInfo.setPaymentAuthCode("");
        creditCardPaymentInfo.setPaymentAuthText("");
        creditCardPaymentInfo.setPaymentAuthTime(now);
        creditCardPaymentInfo.setPayMethod(StorePaymentMethodEnum.OTHER.getName());
        creditCardPaymentInfoService.updateById(creditCardPaymentInfo);
    }
}
