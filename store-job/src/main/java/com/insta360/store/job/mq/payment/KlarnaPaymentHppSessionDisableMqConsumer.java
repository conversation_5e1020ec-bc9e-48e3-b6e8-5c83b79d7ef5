package com.insta360.store.job.mq.payment;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.payment.dto.PaymentMessageDTO;
import com.insta360.store.business.payment.service.impl.handler.BaseKlarnaPaymentHandler;
import com.insta360.store.business.payment.service.impl.handler.PaymentHandlerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/05/05
 * @Description: Klarna hpp session disable
 * <p>
 * 1、一个订单可以创建多个hpp session
 * 2、每一个hpp session都具备单独的支付条件
 * 3、在新的hpp session创建以后需要对这个订单之前所创建的所有hpp session进行禁用处理
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class KlarnaPaymentHppSessionDisableMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(KlarnaPaymentHppSessionDisableMqConsumer.class);

    @Autowired
    OrderService orderService;

    @Autowired
    PaymentHandlerFactory paymentHandlerFactory;

    /**
     * Klarna hpp session disable
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.klarna_hpp_session_disable)
//    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("订单支付状态同步forter。message:{}", messageBody);

        try {
            PaymentMessageDTO paymentMessage = JSONObject.parseObject(messageBody, PaymentMessageDTO.class);

            // order number
            String orderNumber = paymentMessage.getOrderNumber();
            Order order = orderService.getByOrderNumber(orderNumber);
            if (order == null) {
                LOGGER.error("klarna hpp session 禁用失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // hpp session
            List<String> hppSessions = paymentMessage.getKlarnaHppSessions();
            if (CollectionUtils.isEmpty(hppSessions)) {
                LOGGER.error("klarna hpp session 禁用失败。hpp session 不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 禁用
            BaseKlarnaPaymentHandler klarnaPaymentHandler = (BaseKlarnaPaymentHandler) paymentHandlerFactory.getPaymentHandler(PaymentChannel.klarna);
            klarnaPaymentHandler.klarnaHppSessionDisable(hppSessions, order.getArea());
        } catch (Exception e) {
            LOGGER.error(String.format("klarna hpp session disable erro. error_message:{%s}, messsage_body:{%s}", e.getMessage(), messageBody), e);
        }

        return Action.CommitMessage;
    }
}
