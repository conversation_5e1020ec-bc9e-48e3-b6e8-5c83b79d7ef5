package com.insta360.store.job.task;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.integration.lingxing.service.impl.helper.LxRequestHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 领星订单同步任务
 *
 * <AUTHOR>
 * @date 2023/10/07
 */
@Component
public class LxOrderSyncTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(LxOrderSyncTask.class);

    @Autowired
    LxRequestHelper lxRequestHelper;

    /**
     * 领星订单同步任务
     *
     * @param param 参数
     * @return {@link ReturnT}<{@link String}>
     */
    @XxlJob("lxOrderSyncJobHandler")
    public ReturnT<String> lxOrderSyncJobHandler(String param) {
        XxlJobLogger.log("同步领星订单至管易、商城_任务开始...");

        LOGGER.info("Sync lingxing order to store begin.");

        try {
            lxRequestHelper.pullLxOrderSaveAndSync();
        } catch (InstaException e) {
            LOGGER.error(String.format("领星请求发生业务可接受异常 %s", e.getMessage()), e);
            return ReturnT.FAIL;
        }

        LOGGER.info("Sync lingxing order to store end.");

        XxlJobLogger.log("同步马帮订单至管易、商城_任务结束...");
        return ReturnT.SUCCESS;
    }
}
