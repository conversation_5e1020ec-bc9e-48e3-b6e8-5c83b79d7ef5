package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.bo.CloudSubscribeCreateBO;
import com.insta360.store.business.cloud.dto.StoreCloudProductMqDTO;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.impl.helper.CloudServiceSubscribeEngineHelper;
import com.insta360.store.business.integration.checkout.lib.response.CreateGetCkoOrderDetailResponse;
import com.insta360.store.business.integration.checkout.service.CkoPaymentSyncService;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import com.insta360.store.business.payment.lib.checkout.CheckoutConfig;
import com.insta360.store.business.payment.lib.checkout.CheckoutHkConfiguration;
import com.insta360.store.business.payment.lib.checkout.CheckoutUsConfiguration;
import com.insta360.store.business.payment.lib.checkout.enums.CkoPaymentStatusEnum;
import com.insta360.store.business.payment.service.impl.helper.CkoPaymentHelper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 商城云服务订单订阅记录创建MQ消息消费者
 * @Date 2024/6/19
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class CloudProductSubscribeCreateMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudProductSubscribeCreateMqConsumer.class);

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    CloudServiceSubscribeEngineHelper cloudServiceSubscribeEngineHelper;

    @Autowired
    CkoPaymentSyncService ckoPaymentSyncService;

    @Autowired
    CkoPaymentHelper ckoPaymentHelper;

    @Autowired
    CheckoutHkConfiguration checkoutHkConfiguration;

    @Autowired
    CheckoutUsConfiguration checkoutUsConfiguration;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_create_notify)
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[商城云服务订阅]云服务订单支付后订阅创建及云存储通知MQ消费开始,msgId:{}, msg:{}", message.getMsgID(), messageBody);

        try {
            StoreCloudProductMqDTO storeCloudProductMqDto = JSON.parseObject(messageBody, StoreCloudProductMqDTO.class);
            if (Objects.isNull(storeCloudProductMqDto)) {
                return Action.CommitMessage;
            }

            Order subscribeOrder = storeCloudProductMqDto.getSubscribeOrder();
            if (Objects.isNull(subscribeOrder) || !subscribeOrder.getCloudSubscribeMark()) {
                return Action.CommitMessage;
            }
            OrderPayment orderPayment = storeCloudProductMqDto.getOrderPayment();
            if (Objects.isNull(orderPayment)) {
                return Action.CommitMessage;
            }
            if (!OrderPaymentState.PAYED.equals(orderPayment.paymentState()) || StringUtils.isBlank(orderPayment.getChannelPaymentId())) {
                LOGGER.info("[商城云服务订阅]云服务订单的支付状态异常,无需处理。订单号:{},支付状态:{},渠道交易流水号:{}", subscribeOrder.getOrderNumber(), orderPayment.getState(), orderPayment.getChannelPaymentId());
                return Action.CommitMessage;
            }

            // 幂等处理，避免重复消费（云服务订单与订阅记录1对1关系）
            CloudStorageSubscribe cloudStorageSubscribe = cloudStorageSubscribeService.getByOrderNumber(subscribeOrder.getOrderNumber());
            if (Objects.nonNull(cloudStorageSubscribe)) {
                LOGGER.info("[商城云服务订阅]订单号:{}的订阅记录已存在,无需重复消费", subscribeOrder.getOrderNumber());
                return Action.CommitMessage;
            }

            // cko 订阅支付成功校验capture状态(首订才需要更新cko扣款信息)
            if (PaymentChannel.isCkoPayChannel(orderPayment.paymentChannel())
                    && PaymentSubscribeType.FIRST_SUBSCRIBE.equals(subscribeOrder.paymentSubscribeType())) {
                // 对应配置
                CheckoutConfig checkoutConfig = InstaCountry.US.equals(subscribeOrder.country()) ? checkoutUsConfiguration : checkoutHkConfiguration;
                CreateGetCkoOrderDetailResponse ckoOrderStateResponse = ckoPaymentSyncService.getCkoOrderStateResponse(checkoutConfig, subscribeOrder.getOrderNumber(), orderPayment.getChannelPaymentId());
                if (Objects.isNull(ckoOrderStateResponse)) {
                    return Action.ReconsumeLater;
                }

                // Captured状态校验
                if (!CkoPaymentStatusEnum.isAuthSuccess(ckoOrderStateResponse.getStatus())) {
                    LOGGER.info("[商城云服务订阅]订单号:{} cko交易状态非Captured/Authorized。", subscribeOrder.getOrderNumber());
                    FeiShuMessageUtil.storeGeneralMessage(String.format("cko 支付成功交易状态非Captured/Authorized，订单号:{%s}"
                            , subscribeOrder.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                    return Action.CommitMessage;
                }
                // 保存cko扣款信息
                ckoPaymentHelper.saveUserPayInfo(subscribeOrder.getOrderNumber(), orderPayment.paymentChannel(), ckoOrderStateResponse);
            }

            // 创建云服务订阅
            CloudSubscribeCreateBO cloudSubscribeCreateBo = new CloudSubscribeCreateBO();
            cloudSubscribeCreateBo.setSubscribeOrder(subscribeOrder);
            cloudSubscribeCreateBo.setSubscribePayment(orderPayment);
            cloudSubscribeCreateBo.setScenesType(ServiceScenesType.parse(subscribeOrder.getSubscribeScenesType()));
            cloudServiceSubscribeEngineHelper.createSubscribe(cloudSubscribeCreateBo);
            LOGGER.info("[商城云服务订阅]云服务订单支付后订阅创建及云存储通知MQ消费结束,msg:{}", messageBody);
        } catch (Exception e) {
            LOGGER.error(String.format("[商城云服务订阅]云服务订单支付后订阅创建及云存储通知MQ消费异常。消息体: %s", messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅]订单支付成功后订阅创建及云存储通知异常. 消息体:{%s}  异常信息:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        return Action.CommitMessage;
    }
}
