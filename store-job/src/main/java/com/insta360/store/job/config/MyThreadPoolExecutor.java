package com.insta360.store.job.config;

import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: wbt
 * @Date: 2021/05/22
 * @Description:
 */
public class MyThreadPoolExecutor extends ThreadPoolExecutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(MyThreadPoolExecutor.class);

    /**
     * 统计每一个worker的执行时间
     */
    private ConcurrentHashMap<String, LocalDateTime> startTimes;

    /**
     * 线程池名称
     */
    private String poolName;

    /**
     * 构造线程池
     *
     * @param corePoolSize
     * @param maximumPoolSize
     * @param keepAliveTime
     * @param unit
     * @param workQueue
     * @param poolName
     */
    public MyThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, String poolName) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, new MyThreadFactory(poolName));
        this.startTimes = new ConcurrentHashMap<>();
        this.poolName = poolName;
    }

    /**
     * 缓存型线程池
     *
     * @param poolName
     * @return
     */
    public static ExecutorService newCachedThreadPool(String poolName) {
        return new MyThreadPoolExecutor(0, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS, new SynchronousQueue(), poolName);
    }

    /**
     * 线程池延迟关闭时（等待线程池里的任务都执行完毕），统计线程池情况
     */
    @Override
    public void shutdown() {
        // 统计已执行任务、正在执行任务、未执行任务数量
        LOGGER.info(String.format(this.poolName + " Going to shutdown. Executed tasks: %d, Running tasks: %d, Pending tasks: %d",
                this.getCompletedTaskCount(), this.getActiveCount(), this.getQueue().size()));
        super.shutdown();
    }

    /**
     * 线程池立即关闭时，统计线程池信息
     *
     * @return
     */
    @Override
    public List<Runnable> shutdownNow() {
        // 统计已执行任务、正在执行任务、未执行任务数量
        LOGGER.info(
                String.format(this.poolName + " Going to immediately shutdown. Executed tasks: %d, Running tasks: %d, Pending tasks: %d",
                        this.getCompletedTaskCount(), this.getActiveCount(), this.getQueue().size()));
        return super.shutdownNow();
    }

    /**
     * worker开始执行的时间
     *
     * @param t
     * @param r
     */
    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        startTimes.put(String.valueOf(r.hashCode()), LocalDateTime.now());
    }

    /**
     * worker执行完毕，统计执行线程信息
     *
     * @param r
     * @param t
     */
    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        LocalDateTime startDate = startTimes.remove(String.valueOf(r.hashCode()));
        LocalDateTime finishDate = LocalDateTime.now();

        Duration duration = Duration.between(startDate, finishDate);
        // 统计任务耗时、初始线程数、核心线程数、正在执行的任务数量、已完成任务数量、任务总数、队列里缓存的任务数量、池中存在的最大线程数、最大允许的线程数、线程空闲时间、线程池是否关闭、线程池是否终止
        LOGGER.info(String.format(this.poolName
                        + "-pool-monitor: Duration: %d ms, PoolSize: %d, CorePoolSize: %d, Active: %d, Completed: %d, Task: %d, Queue: %d, LargestPoolSize: %d, MaximumPoolSize: %d,KeepAliveTime: %d, isShutdown: %s, isTerminated: %s",
                duration.toMillis(), this.getPoolSize(), this.getCorePoolSize(), this.getActiveCount(), this.getCompletedTaskCount(), this.getTaskCount(),
                this.getQueue().size(), this.getLargestPoolSize(), this.getMaximumPoolSize(), this.getKeepAliveTime(TimeUnit.MILLISECONDS),
                this.isShutdown(), this.isTerminated()));
    }

    /**
     * 线程工厂
     */
    static class MyThreadFactory implements ThreadFactory {

        // 线程池数量
        private static final AtomicInteger poolNumber = new AtomicInteger(1);

        // 分组
        private final ThreadGroup group;

        // 第几个线程
        private final AtomicInteger theadNumber = new AtomicInteger(1);

        // 线程前缀
        private final String namePrefix;

        public MyThreadFactory(String poolName) {
            SecurityManager securityManager = System.getSecurityManager();
            this.group = (securityManager != null) ? securityManager.getThreadGroup() : Thread.currentThread().getThreadGroup();
            this.namePrefix = poolName + "-pool-" + poolNumber.getAndIncrement() + "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(group, r, namePrefix + theadNumber.getAndIncrement(), 0);

            // 守护线程
            if (thread.isDaemon()) {
                thread.setDaemon(false);
            }

            // 线程优先级（默认优先级）
            if (thread.getPriority() != Thread.NORM_PRIORITY) {
                thread.setPriority(Thread.NORM_PRIORITY);
            }

            return thread;
        }
    }

    /**
     * 拒绝策略
     */
    static class MyRejectedExecution implements RejectedExecutionHandler {

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            String msg = String.format("store线程池已满!"
                            + " Pool Size: %d (active: %d, core: %d, max: %d, largest: %d), Task: %d (completed: %d),"
                            + " Executor status:(isShutdown:%s, isTerminated:%s, isTerminating:%s)!",
                    e.getPoolSize(), e.getActiveCount(), e.getCorePoolSize(), e.getMaximumPoolSize(), e.getLargestPoolSize(),
                    e.getTaskCount(), e.getCompletedTaskCount(), e.isShutdown(), e.isTerminated(), e.isTerminating());
            FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
    }
}
