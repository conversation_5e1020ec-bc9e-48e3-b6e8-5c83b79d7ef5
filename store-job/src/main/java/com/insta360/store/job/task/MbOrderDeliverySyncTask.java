package com.insta360.store.job.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.store.business.integration.mabang.model.MbOrder;
import com.insta360.store.business.integration.mabang.model.MbOrderDelivery;
import com.insta360.store.business.integration.mabang.service.MbOrderDeliveryService;
import com.insta360.store.business.integration.mabang.service.MbOrderService;
import com.insta360.store.business.integration.mabang.service.MbSyncService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/09/12
 * @Description: 定时同步马帮订单的物流信息
 */
@Component
public class MbOrderDeliverySyncTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(MbOrderDeliverySyncTask.class);

    @Autowired
    MbOrderService mbOrderService;

    @Autowired
    MbSyncService mbSyncService;

    @Autowired
    MbOrderDeliveryService mbOrderDeliveryService;

    @XxlJob("mbOrderDeliverySyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("同步马帮订单的物流信息_任务开始...");
        // 所有没有物流信息的非fba的订单
        List<MbOrder> mbOrders = mbOrderService.listByNotExpressCode();
        if (CollectionUtils.isEmpty(mbOrders)) {
            return ReturnT.SUCCESS;
        }
        for (MbOrder mbOrder : mbOrders) {
            try {
                // 同步订单信息
                Boolean result = syncOrderDeliveryInfo(mbOrder);
                if (result && LOGGER.isInfoEnabled()) {
                    LOGGER.info(String.format("Sync mb order %s From Guanyi success: ", mbOrder.getMbOrderNumber()));
                }
            } catch (Exception e) {
                if (LOGGER.isInfoEnabled()) {
                    LOGGER.info(String.format("Sync mb order %s From Guanyi Error: ", mbOrder.getMbOrderNumber()) + "，" + e.getMessage());
                }
            }
        }

        XxlJobLogger.log("同步马帮订单的物流信息_任务结束...");
        return ReturnT.SUCCESS;
    }

    private Boolean syncOrderDeliveryInfo(MbOrder mbOrder) {
        String result = mbSyncService.syncOrderDeliveryFromGy(mbOrder.getMbSalesNumber());
        JSONObject resultJson = JSONObject.parseObject(result);

        JSONArray deliveryJson = resultJson.getJSONArray("deliverys");
        if (deliveryJson != null && !deliveryJson.isEmpty()) {
            // 根据第一条的发货记录进行记录
            JSONObject delivery = deliveryJson.getJSONObject(0);

            if (delivery != null) {
                // 物流信息
                String expressCode = delivery.getString("express_code");
                String expressNo = delivery.getString("express_no");

                // 发货时间
                JSONObject deliveryStatusInfo = delivery.getJSONObject("delivery_statusInfo");
                String expressTime = deliveryStatusInfo.getString("delivery_date");

                MbOrderDelivery orderDelivery = mbOrderDeliveryService.getById(mbOrder.getId());
                if (orderDelivery != null) {
                    // 更新物流信息
                    orderDelivery.setExpressCode(expressNo);
                    orderDelivery.setExpressCompany(expressCode);
                    orderDelivery.setExpressTime(LocalDateTime.parse(expressTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    mbOrderDeliveryService.updateById(orderDelivery);

                    return true;
                }
            }
        }
        return false;
    }
}
