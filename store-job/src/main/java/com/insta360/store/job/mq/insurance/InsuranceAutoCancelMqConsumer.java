package com.insta360.store.job.mq.insurance;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.impl.helper.RmaInsuranceOrderHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: py
 * @Date: 2023/6/7
 * @Description: 售后单自动作废care和延保消费
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class InsuranceAutoCancelMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(InsuranceAutoCancelMqConsumer.class);

    @Autowired
    RmaInsuranceOrderHelper rmaInsuranceOrderHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.rma_order_auto_cancel_insurance)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[售后单自动作废]接收到消息,消息体:[%s]", messageBody));

        try {
            RmaOrder rmaOrder = JSON.parseObject(messageBody, RmaOrder.class);

            // 作废
            rmaInsuranceOrderHelper.cancelInsurance(rmaOrder);
            LOGGER.info(String.format("[售后单自动作废]自动作废成功,消息体:[%s]", messageBody));

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[售后单自动作废]增值服务消费失败,消息体:[%s],原因:[%s]", messageBody, e.getMessage()), e);
            return Action.CommitMessage;
        }
    }
}
