package com.insta360.store.job.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.integration.mabang.enums.MbOrderState;
import com.insta360.store.business.integration.mabang.service.MbSyncService;
import com.insta360.store.business.integration.mabang.service.impl.helper.SyncMbOrderHelper;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * @Author: wbt
 * @Date: 2020/08/19
 * @Description: 定时任务-同步马帮订单至管易、商城
 */
@Component
public class MbOrderSyncTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(MbOrderSyncTask.class);

    @Autowired
    MbSyncService mbSyncService;

    @Autowired
    SyncMbOrderHelper syncMbOrderHelper;

    @XxlJob("mbOrderSyncJobHandler")
    public ReturnT<String> mbOrderSyncJobHandler(String param) {
        XxlJobLogger.log("同步马帮订单至管易、商城_任务开始...");
        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("Sync mabang order to store begin.");
        }

        // 同步马帮配货中的订单
        mbSyncService.syncPreparedOrderToStore();

        // 同步马帮其他状态(已发货，已完成，已作废)的订单
        mbSyncService.syncOtherOrderToStore();

        if (LOGGER.isInfoEnabled()) {
            LOGGER.info("Sync mabang order to store end.");
        }

        XxlJobLogger.log("同步马帮订单至管易、商城_任务结束...");
        return ReturnT.SUCCESS;
    }

    /**
     * 历史订单数据任务
     */
    @GrafanaDataStats(statisticsType = {GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.TIMER, GrafanaStatisticsType.GAUGE},
            keyType = GrafanaKeyType.EXTERNAL_CALL_MB,
            businessType = GrafanaBusinessType.EXTERNAL_CALL)
    @XxlJob("mbHistoryOrderSyncJobHandler")
    public ReturnT<String> mbHistoryOrderSyncJobHandler(String param) {
        XxlJobLogger.log("同步马帮历史订单至管易、商城_任务开始...");
        JSONObject orderDetailJson = mbSyncService.syncGetOrderDetail(MbOrderState.other);
        String code = orderDetailJson.getString("code");
        try {
            // 拿到总页数
            // 自旋一次，失败了就真的失败了，等待第二次执行
            String pageCount = "000".equals(code) ? orderDetailJson.getString("pageCount") : mbSyncService.syncGetOrderDetail(MbOrderState.other).getString("pageCount");
            for (int pageNo = 1; pageNo <= Integer.parseInt(pageCount); pageNo++) {
                orderDetailJson = mbSyncService.syncGetOrderDetail(MbOrderState.other, pageNo);
                code = orderDetailJson.getString("code");
                if ("000".equals(code)) {
                    JSONArray orders = orderDetailJson.getJSONArray("data");
                    if (orders == null || orders.size() == 0) {
                        break;
                    }
                    // 同步订单信息
                    syncMbOrderHelper.doSyncOrderToStore(orders);
                } else {
                    String message = "马帮订单同步同步失败（历史）。北京时间：" + LocalDateTime.now(ZoneId.of("+8"))
                            + "，原因：" + orderDetailJson.getString("message");
                    FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW);
                }
            }
        } catch (Exception e) {
            // 请求失败：read timed out
            LOGGER.error("sync fail for get order detail. error message:{}", e.getMessage(), e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("同步马帮历史订单至管易、商城_任务结束...");
        return ReturnT.SUCCESS;
    }
}
