package com.insta360.store.job.mq.monitor.cache;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.configuration.cache.monitor.redis.put.factory.CachePutHandlerFactory;
import com.insta360.store.business.configuration.cache.monitor.redis.put.handler.CachePutService;
import com.insta360.store.business.outgoing.mq.cache.dto.StoreCacheDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;

/**
 * @Author: wbt
 * @Date: 2023/08/31
 * @Description: 缓存更新监控
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class CachePutMonitorMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(CachePutMonitorMqConsumer.class);

    @Autowired
    CachePutHandlerFactory cachePutHandlerFactory;

    /**
     * 缓存更新监控
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cache_put_event)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("接受到缓存更新消息。消息ID:{}, 消息体:{}", message.getMsgID(), messageBody);

        try {
            StoreCacheDTO storeCacheInfo = JSON.parseObject(messageBody, StoreCacheDTO.class);
            String cacheType = storeCacheInfo.getCacheType();
            if (StringUtil.isBlank(cacheType)) {
                LOGGER.error("缓存更新消息消费失败。缓存类型不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 缓存更新
            CachePutService cachePutService = cachePutHandlerFactory.getCachePutService(storeCacheInfo.getCacheType());
            cachePutService.cachePut(storeCacheInfo.getCachePutKeyParameter());
        } catch (Exception e) {
            // 超时则重投递
            if (e instanceof SocketTimeoutException) {
                LOGGER.error(String.format("缓存更新消息消费失败。原因:{%s},消息体:{%s}", e.getMessage(), messageBody), e);
                return Action.ReconsumeLater;
            }
        }
        LOGGER.info("缓存更新消息消费成功。消息ID:{}, 消息体:{}", message.getMsgID(), messageBody);
        return Action.CommitMessage;
    }
}
