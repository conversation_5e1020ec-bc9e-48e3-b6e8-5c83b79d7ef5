package com.insta360.store.job.mq.insurance;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.insurance.bo.InsuranceBO;
import com.insta360.store.business.insurance.enums.InsuranceOrderOrigin;
import com.insta360.store.business.insurance.service.impl.fatory.InsuranceFactory;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.model.OrderInsurance;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderInsuranceService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.insurance.dto.InsuranceDTO;
import com.insta360.store.business.product.enums.ProductCategoryFinalType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2022/3/4
 * @Description: 单独购买增值服务激活mq消息消费
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class InsuranceSoloActivationMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(InsuranceSoloActivationMqConsumer.class);

    @Autowired
    OrderInsuranceService orderInsuranceService;

    @Autowired
    OrderService orderService;

    @Autowired
    InsuranceFactory insuranceFactory;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductService productService;

    @Autowired
    OrderDeliveryService orderDeliveryService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_payed_insurance_activation)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[单独购买保险激活]接收到消息,消息体:[%s]", messageBody));

        try {
            InsuranceDTO insuranceParam = JSON.parseObject(messageBody, InsuranceDTO.class);
            // 判断是否为单独购买的增值服务
            Integer orderId = insuranceParam.getOrderId();
            boolean commitMessage = judgeSoloInsurance(orderId);
            if (commitMessage) {
                return Action.CommitMessage;
            }

            // 单独购买记录
            OrderInsurance orderInsurance = orderInsuranceService.getByOrder(orderId);
            if (orderInsurance == null) {
                LOGGER.error(String.format("[单独购买保险激活]激活失败,订单没有保险记录信息.订单id:%s,尝试重新投递信息.", orderId));
                return Action.ReconsumeLater;
            }

            LocalDateTime payTime = insuranceParam.getPayTime();
            if (Objects.isNull(payTime)) {
                payTime = LocalDateTime.now();
                LOGGER.info(String.format("[单独购买保险激活]支付时间为null,以当前时间为准.序列号:[%s],订单id:%s", orderInsurance.getSerial(), orderId));
            }

            Order order = orderService.getById(orderId);
            OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(orderId);

            InsuranceBO activationCreation = new InsuranceBO();
            activationCreation.setInsuranceType(orderInsurance.getInsuranceType());
            activationCreation.setDeviceType(orderInsurance.getDeviceType());
            activationCreation.setDeviceSerial(orderInsurance.getSerial());
            activationCreation.setOrder(order);
            activationCreation.setInsuranceNumber(1);
            activationCreation.setActivateTime(payTime);
            activationCreation.setOrderOrigin(InsuranceOrderOrigin.store.name());
            activationCreation.setOrderNumber(order.getOrderNumber());
            activationCreation.setEmail(order.getContactEmail());
            activationCreation.setArea(order.getArea());
            activationCreation.setPhone(orderDelivery.getPhone());

            // 激活
            BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(orderInsurance.getInsuranceType());
            insuranceHandler.autoActivationInsurance(activationCreation);
            LOGGER.info(String.format("[单独购买保险激活]激活成功.订单id:%s,serial:%s", orderInsurance.getOrderId(), orderInsurance.getSerial()));
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[单独购买保险激活]激活失败,消息体:[%s],原因:[%s]", messageBody, e.getMessage()), e);
            return Action.CommitMessage;
        }
    }

    /**
     * 判断订单是否为单独购买的增值服务
     *
     * @param orderId
     * @return
     */
    private boolean judgeSoloInsurance(Integer orderId) {
        List<OrderItem> orderItems = orderItemService.getByOrder(orderId);
        int maxSize = 1;
        if (CollectionUtils.isEmpty(orderItems)) {
            LOGGER.info(String.format("[单独购买保险激活]orderItem数量为0,不符合条件,订单id:%s", orderId));
            return Boolean.TRUE;
        }
        // orderItem数量不为1
        if (maxSize != orderItems.size()) {
            LOGGER.info(String.format("[单独购买保险激活]orderItem数量超过1,并非单独购买的增值服务,订单id:%s", orderId));
            return Boolean.TRUE;
        }

        Integer productId = orderItems.get(0).getProduct();
        Product product = productService.getById(productId);
        if (Objects.isNull(product)) {
            LOGGER.info(String.format("[单独购买保险激活]产品不存在,中止激活.产品id:%s,订单id:%s", productId, orderId));
            return Boolean.TRUE;
        }

        // 不是增值服务
        if (!ProductCategoryFinalType.CF_CLIMB_SERVICE.name().equals(product.getCategoryKey())) {
            LOGGER.info(String.format("[单独购买保险激活]产品不是增值服务,中止激活.产品id:%s,订单id:%s", productId, orderId));
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
