package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.exception.RetryHandlerException;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.payment.bo.OrderPaymentCheckBO;
import com.insta360.store.business.payment.service.impl.handler.BasePaymentHandler;
import com.insta360.store.business.payment.service.impl.handler.PaymentHandlerFactory;
import com.insta360.store.business.trade.enums.CreditCardPaymentActionEnum;
import com.insta360.store.business.trade.model.CreditCardPaymentAuthResult;
import com.insta360.store.business.trade.service.CreditCardPaymentAuthResultService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2022/12/28
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderPaymentCheckMonitorConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderPaymentCheckMonitorConsumer.class);

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    PaymentHandlerFactory paymentHandlerFactory;

    @Autowired
    CreditCardPaymentAuthResultService creditCardPaymentAuthResultService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_payment_check_monitor)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("进入订单交易信息核对流程。消息体:{}", messageBody);

        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            OrderPaymentCheckBO orderPaymentCheck = orderMessage.getOrderPaymentCheck();
            Order order = orderService.getByOrderNumber(orderPaymentCheck.getOrderNumber());
            if (order == null) {
                LOGGER.error("订单交易信息核对失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            PaymentChannel paymentChannel = orderPaymentCheck.getPaymentChannel();
            if (paymentChannel == null) {
                LOGGER.error("订单交易信息核对失败。支付渠道不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 对账前对比一下当前的正在支付的渠道是否是目前对账的支付渠道
            CreditCardPaymentAuthResult creditCardPaymentAuthResult = creditCardPaymentAuthResultService.
                    getLastRecordByActionType(order.getOrderNumber(), CreditCardPaymentActionEnum.ORDER_PAYED.getActionName());
            if (creditCardPaymentAuthResult != null && StringUtil.isNotBlank(creditCardPaymentAuthResult.getPayMethod())) {
                if (!paymentChannel.name().equals(creditCardPaymentAuthResult.getPayMethod())) {
                    LOGGER.info("当前交易对账渠道:{}, 最近一次的支付渠道:{}。两者不一致不予对账。order_number:{}", paymentChannel.name(),
                            creditCardPaymentAuthResult.getPayMethod(), order.getOrderNumber());
                    return Action.CommitMessage;
                }
            }

            // 如果已成功收到交易结果回传，则无需进行交易信息核对环节。
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (!OrderPaymentState.PAYMENT_PENDING.equals(orderPayment.paymentState())) {
                LOGGER.info("订单交易信息核对完结，无需核对。订单号:{}", order.getOrderNumber());
                return Action.CommitMessage;
            }

            // 支付处理器
            BasePaymentHandler paymentHandler = paymentHandlerFactory.getPaymentHandler(paymentChannel);

            // 根据当前时间推算出交易对账发起的时间
            LocalDateTime sendMessageTime = LocalDateTime.now().minusSeconds(paymentHandler.getTradeCheckDelayTime() / 1000);

            // 如果当前时间减去延迟对账的时间 不等于 交易对账发起的时间。则不予处理。
            // 考虑其它流程的时间开销，两者误差限定在10s内（后续视情况酌情动态修改）。
            if (Duration.between(sendMessageTime, creditCardPaymentAuthResult.getCreateTime()).getSeconds() > 10) {
                LOGGER.info("同一支付渠道的对账时间间隔超过10秒，不予对账。订单号:{}", order.getOrderNumber());
                return Action.CommitMessage;
            }

            // 交易对账
            paymentHandler.handlePaymentResultCheck(orderPaymentCheck);
        } catch (Exception e) {
            // 超时则重投递
            if (e instanceof RetryHandlerException) {
                return Action.ReconsumeLater;
            }
            LOGGER.error(String.format("订单交易信息核对失败。消息体:{%s}. 原因:{%s}", messageBody, e.getMessage()), e);
            return Action.CommitMessage;
        }
        return Action.CommitMessage;
    }
}
