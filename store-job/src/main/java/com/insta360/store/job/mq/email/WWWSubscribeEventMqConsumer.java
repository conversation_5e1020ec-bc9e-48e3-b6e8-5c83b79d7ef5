package com.insta360.store.job.mq.email;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpBroadcastProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpBroadcastChannelEnum;
import com.insta360.store.business.email.constants.EmailSubscribeConstant;
import com.insta360.store.business.email.enums.EmailSubscribeStatus;
import com.insta360.store.business.email.service.impl.helper.EmailHelper;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.subscribe.dto.EmailSubscribeDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Author: py
 * @Date: 2024/4/1
 * @Description: 官网退订/订阅通知商城
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class WWWSubscribeEventMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(WWWSubscribeEventMqConsumer.class);

    @Autowired
    EmailHelper emailHelper;

    @MessageTcpBroadcastProcessor(messageChannel = MessageTcpBroadcastChannelEnum.www_email_subscribe_broad_all)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[官网订阅状态通知]收到广播消息,messageBody：{}", messageBody);
        try {
            EmailSubscribeDTO emailSubscribe = JSON.parseObject(messageBody, EmailSubscribeDTO.class);
            if (StringUtil.isBlank(emailSubscribe.getEmail())) {
                LOGGER.error(String.format("[官网订阅状态通知]订阅状态修改失败,邮箱不存在.消息体[%s]", messageBody));
                return Action.CommitMessage;
            }

            String event = emailSubscribe.getEvent();
            EmailSubscribeStatus subscribeStatus = EmailSubscribeStatus.parse(event);
            if (Objects.isNull(subscribeStatus)) {
                LOGGER.error(String.format("[官网订阅状态通知]订阅状态修改失败,订阅状态错误.消息体[%s]", messageBody));
                return Action.CommitMessage;
            }

            // 获取国家
            InstaCountry country = StringUtils.isNotBlank(emailSubscribe.getCountry()) ? InstaCountry.parse(emailSubscribe.getCountry()) : null;

            // 修改订阅状态
            emailHelper.upsertSubscribe(country, emailSubscribe.getEmail(), subscribeStatus, EmailSubscribeConstant.subscribeActivityPage);
            LOGGER.info(String.format("[官网订阅状态通知]订阅状态修改成功,消息体[%s]", messageBody));
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[官网订阅状态通知]订阅状态修改失败,消息体[%s],原因[%s]", messageBody, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[官网订阅状态通知]订阅状态修改失败,消息体[%s],原因[%s]", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return Action.CommitMessage;
        }
    }
}
