package com.insta360.store.job.task.review;

import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.OrderStateRecord;
import com.insta360.store.business.order.service.OrderStateRecordService;
import com.insta360.store.business.review.model.Review;
import com.insta360.store.business.review.model.ReviewEmailSendRecord;
import com.insta360.store.business.review.service.ReviewEmailSendRecordService;
import com.insta360.store.business.review.service.ReviewService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/04/19
 * @Description:
 */
@Component
public class ReviewMonitorTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReviewMonitorTask.class);

    /**
     * 0
     */
    private static final Integer ZERO = 0;

    /**
     * 邮件发送率最低阈值
     */
    private static final Float EMAIL_SEND_RATE = 0.5f;

    /**
     * 时间格式化
     */
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    ReviewService reviewService;

    @Autowired
    OrderStateRecordService orderStateRecordService;

    @Autowired
    ReviewEmailSendRecordService reviewEmailSendRecordService;

    /**
     * 评论数据监控
     *
     * @param param
     * @return
     */
    @XxlJob("reivewMonitorTaskHandler")
    public ReturnT<String> execute(String param) {
        LOGGER.info("触发评论数据监控任务。。。开始");

        LocalDate now = LocalDate.now().minusDays(1);

        // 前一天的 00:00
        String yesterDayMinTime = FORMATTER.format(LocalDateTime.of(now, LocalTime.MIN));

        // 前一天的 23:59
        String yesterDayMaxTime = FORMATTER.format(LocalDateTime.of(now, LocalTime.MAX));

        // 昨天订单变为成功的订单总数
        List<OrderStateRecord> orderStateRecords = orderStateRecordService.listByBetweenCreateTime(yesterDayMinTime, yesterDayMaxTime, OrderState.success.getCode());
        int orderStateRecordSize = orderStateRecords.size();
        if (orderStateRecordSize == ZERO) {
            this.monitorNotify(ZERO, ZERO);
            return ReturnT.SUCCESS;
        }

        // 昨天评论邮件发送的总数
        List<ReviewEmailSendRecord> reviewEmailSendRecords = reviewEmailSendRecordService.listByBetweenCreateTime(yesterDayMinTime, yesterDayMaxTime);
        int reviewEmailSendRecordSize = reviewEmailSendRecords.size();

        // 昨天的评论总数
        List<Review> reviews = reviewService.listByBetweenCreateTime(yesterDayMinTime, yesterDayMaxTime);
        int reivewSize = reviews.size();

        // 如果邮件发送率小于50% 或者 评论数量等于0，则给出异常提示
        float emailSendRate = reviewEmailSendRecordSize / (orderStateRecordSize * 1.0f);
        float maxRate = Math.max(emailSendRate, EMAIL_SEND_RATE);
        if (maxRate <= EMAIL_SEND_RATE || reivewSize == ZERO) {
            this.monitorNotify(Math.round(emailSendRate), reivewSize);
        }

        LOGGER.info("触发评论数据监控任务。。。结束");
        return ReturnT.SUCCESS;
    }

    /**
     * 飞书通知
     *
     * @param emailSendRate
     * @param reviewCount
     */
    private void monitorNotify(Integer emailSendRate, Integer reviewCount) {
        String msg = String.format("评论数据异常提示！昨日评论邮件 邮件发送率 = %s，评论提交数量 = %s。数据异常请跟进排查。", emailSendRate * 100 + "%", reviewCount);
        FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.ReviewChange, FeiShuAtUser.LCY, FeiShuAtUser.CYJ, FeiShuAtUser.GQY);
    }
}
