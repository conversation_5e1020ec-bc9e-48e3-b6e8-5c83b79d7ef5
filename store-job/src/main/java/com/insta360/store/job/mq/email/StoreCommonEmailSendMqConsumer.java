package com.insta360.store.job.mq.email;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.admin.email.service.UserGuideEmailRecordService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.email.BaseOrderEmail;
import com.insta360.store.business.order.email.OrderEmailFactory;
import com.insta360.store.business.outgoing.mq.email.dto.StoreEmailCommonDTO;
import com.insta360.store.job.mq.email.factory.EmailSendFactory;
import com.insta360.store.job.mq.email.handler.BaseEmailSendHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 商城邮件消费类
 * @Date 2024/11/20 上午11:46
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class StoreCommonEmailSendMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCommonEmailSendMqConsumer.class);

    @Autowired
    EmailSendFactory emailSendFactory;

    @Autowired
    OrderEmailFactory orderEmailFactory;

    @Autowired
    UserGuideEmailRecordService userGuideEmailRecordService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_common_email_send_delay)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("商城邮件发送消费开始，messageBody：{}", messageBody);
        try {
            StoreEmailCommonDTO storeEmailMessage = JSON.parseObject(messageBody, StoreEmailCommonDTO.class);
            String emailSendBusinessType = storeEmailMessage.getEmailSendBusinessType();

            // 找到指定的业务类，进行邮件发送
            BaseEmailSendHandler emailSendHandler = emailSendFactory.getEmailSendHandler(emailSendBusinessType);
            if (Objects.isNull(emailSendHandler)) {
                LOGGER.error("商城邮件发送消费失败，找不到业务类型：emailSendBusinessType：{}", emailSendBusinessType);
                FeiShuMessageUtil.storeGeneralMessage(String.format("商城邮件MQ消费失败，找不到业务类型：emailSendBusinessType：%s", emailSendBusinessType), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
                return Action.CommitMessage;
            }

            // 邮件模板
            BaseOrderEmail email = emailSendHandler.getEmail(storeEmailMessage);
            if (Objects.isNull(email)) {
                return Action.CommitMessage;
            }
            // send
            email.doSend(storeEmailMessage.getToEmailAddress());
            LOGGER.info("商城公用邮件mq，消费成功, 消息体:{}", messageBody);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("商城公用邮件mq，消费异常,消息体[%s],原因[%s]", messageBody, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("商城公用邮件mq，消费异常,消息体[%s],原因[%s]", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
            return Action.CommitMessage;
        }
    }
}
