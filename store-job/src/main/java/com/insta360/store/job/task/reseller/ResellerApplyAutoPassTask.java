package com.insta360.store.job.task.reseller;

import com.insta360.store.business.reseller.enums.ResellerApplyState;
import com.insta360.store.business.reseller.model.ResellerApply;
import com.insta360.store.business.reseller.service.ResellerApplyService;
import com.insta360.store.business.reseller.service.ResellerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description: 定时任务-分销商申请自动通过
 */
@Component
public class ResellerApplyAutoPassTask {

    public static final Logger logger = LoggerFactory.getLogger(ResellerApplyAutoPassTask.class);

    @Autowired
    ResellerApplyService applyService;

    @Autowired
    ResellerService resellerService;

    @XxlJob("resellerApplyAutoPassJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("分销商申请自动通过_任务开始...");
        List<ResellerApply> resellerApplies = applyService.getApplies(ResellerApplyState.unhandled);
        if (CollectionUtils.isEmpty(resellerApplies)) {
            return ReturnT.SUCCESS;
        }

        for (ResellerApply resellerApply : resellerApplies) {
            try {
                // 设置为成功
                resellerApply.setState(ResellerApplyState.success.getCode());
                applyService.updateById(resellerApply);
                // 创建分销账户
                resellerService.create(resellerApply);
            } catch (Exception e) {
                logger.error(String.format("账户:%s 所属的分销商申请自动通过失败...", resellerApply.getUserId()), e);
            }
        }
        XxlJobLogger.log("分销商申请自动通过_任务结束...");
        return ReturnT.SUCCESS;
    }
}
