package com.insta360.store.job.mq.email.factory;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.email.enums.EmailSendBusinessType;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.job.mq.email.handler.BaseEmailSendHandler;
import com.insta360.store.job.mq.email.handler.base.OrderOnDeliveryX4Handler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/11/21 上午11:38
 */
@Component
public class EmailSendFactory {

    /**
     * 按业务类型找到指定的handler
     * @param businessType 业务类型
     * @return 指定处理类
     */
    public BaseEmailSendHandler getEmailSendHandler(String businessType) {
        EmailSendBusinessType type = EmailSendBusinessType.parse(businessType);
        if (Objects.isNull(type)) {
            return null;
        }
        switch (type) {
            case ORDER_ON_DELIVERY_X4_EMAIL:
                OrderOnDeliveryX4Handler handler = ApplicationContextHolder.getApplicationContext().getBean(OrderOnDeliveryX4Handler.class);
                handler.setEmailSendBusinessType(type);
                return handler;
            default:
                return null;
        }
    }
}
