package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.ResponseCode;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.constant.StoreBenefitConstant;
import com.insta360.store.business.cloud.dto.CloudSubscribeNotifyDTO;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.model.CloudStorageSubscribeChangeNotify;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeChangeNotifyService;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.StoreSubscribeFinishService;
import com.insta360.store.business.cloud.service.impl.factory.StoreCloudSubscribeFactory;
import com.insta360.store.business.cloud.service.impl.helper.StoreSubscribeHelper;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.impl.handler.RmaOrderSpecialSceneHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/26
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class StoreCloudBenefitCallbackMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudBenefitCallbackMqConsumer.class);

    @Autowired
    CloudStorageSubscribeChangeNotifyService cloudStorageSubscribeChangeNotifyService;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    StoreCloudSubscribeFactory storeCloudSubscribeFactory;

    @Autowired
    RmaOrderSpecialSceneHandler rmaOrderSpecialSceneHandler;

    @Autowired
    StoreSubscribeHelper storeSubscribeHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_callback_notify)
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[商城云服务订阅]云存储权益下发结果回调开始...,msg:{}", messageBody);
        // 订阅记录
        CloudStorageSubscribe cloudStorageSubscribe = null;
        try {
            CloudSubscribeNotifyDTO cloudSubscribeNotifyDto = JSON.parseObject(messageBody, CloudSubscribeNotifyDTO.class);
            // 通知记录
            CloudStorageSubscribeChangeNotify cloudStorageSubscribeChangeNotify = cloudStorageSubscribeChangeNotifyService.getById(Long.valueOf(cloudSubscribeNotifyDto.getNotificationId()));
            if (Objects.isNull(cloudStorageSubscribeChangeNotify)) {
                return Action.CommitMessage;
            }
            // 通知是否已消费
            if (!cloudStorageSubscribeChangeNotify.getAckMark()) {
                cloudStorageSubscribeChangeNotifyService.updateNotifyAckMark(cloudStorageSubscribeChangeNotify);
            }

            // 订阅记录
            cloudStorageSubscribe = cloudStorageSubscribeService.getById(cloudStorageSubscribeChangeNotify.getSubscribeId());
            if (Objects.isNull(cloudStorageSubscribe)) {
                LOGGER.info("[商城云服务订阅]云存储权益下发结果回调,订阅记录不存在。subscribeChangeNotify:{}", JSON.toJSONString(cloudStorageSubscribeChangeNotify));
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅]云存储权益下发结果回调,订阅记录不存在。subscribeChangeNotify:%s", JSON.toJSONString(cloudStorageSubscribeChangeNotify)), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }
            // 权益是否已下发
            if (cloudStorageSubscribe.getExecuteMark()) {
                LOGGER.info("[商城云服务订阅]云存储权益下发结果回调,当前订阅已下发权益,不予处理。cloudStorageSubscribe:{}", JSON.toJSONString(cloudStorageSubscribe));
                return Action.CommitMessage;
            }

            // 解析场景类型
            ServiceScenesType scenesType = ServiceScenesType.parse(cloudSubscribeNotifyDto.getNotificationType());
            if (!ServiceScenesType.isChargeScenes(scenesType)) {
                LOGGER.info("[商城云服务订阅]云存储权益下发结果回调,当前订阅场景类型非扣费场景,不予处理。cloudStorageSubscribe:{}", JSON.toJSONString(cloudStorageSubscribe));
                return Action.CommitMessage;
            }

            // 售后单
            RmaOrder rmaOrder = null;
            // 权益下发结果
            Boolean isSubscribeSuccess = Boolean.FALSE;
            // 云存储回调响应码
            Integer code = cloudSubscribeNotifyDto.getCode();
            if (ResponseCode.SUCCESS.equals(code)) {
                // 获取具体实例
                StoreSubscribeFinishService storeSubscribeFinishService = storeCloudSubscribeFactory.getStoreSubscribeFinishService(scenesType);
                // 下发商城权益
                isSubscribeSuccess = storeSubscribeFinishService.handleSubscribeFinalStep(cloudStorageSubscribe);
                if (!isSubscribeSuccess && ServiceScenesType.UPGRADE.equals(scenesType)) {
                    rmaOrder = rmaOrderSpecialSceneHandler.cloudSubscribeRefundOrderCreate(cloudStorageSubscribe.getOrderNumber(), cloudStorageSubscribe.getCommodityId(), isSubscribeSuccess);
                    // 关闭当前预处理订阅
                    storeSubscribeHelper.closeUpgradeSubscribe(cloudStorageSubscribe);
                }
            } else {
                String errorMsg = String.format("[商城云服务订阅]云存储权益下发结果回调权益下发失败,云存储响应: %s \n 订单号: {%s}", JSON.toJSONString(cloudSubscribeNotifyDto), cloudStorageSubscribe.getOrderNumber());
                if (ServiceScenesType.UPGRADE.equals(scenesType)) {
                    rmaOrder = rmaOrderSpecialSceneHandler.cloudSubscribeRefundOrderCreate(cloudStorageSubscribe.getOrderNumber(), cloudStorageSubscribe.getCommodityId(), isSubscribeSuccess);
                    // 关闭当前预处理订阅
                    storeSubscribeHelper.closeUpgradeSubscribe(cloudStorageSubscribe);
                } else {
                    if (StoreBenefitConstant.CHANNEL_MUTUALLY_EXCLUSIVE_ERROR_CODE.equals(code)) {
                        errorMsg = String.format("【云存渠道冲突通知】权益下发失败,云存储响应: %s  \n   订单号: {%s} \n 用户在商城下单支付云服务，但存在跨渠道冲突。请联系并告知用户该订单的账号已在其他渠道购买了云服务，并为用户办理退款", JSON.toJSONString(cloudSubscribeNotifyDto), cloudStorageSubscribe.getOrderNumber());
                    }
                }
                // 推送飞书告警
                FeiShuMessageUtil.storeGeneralMessage(errorMsg, FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.LQ, FeiShuAtUser.CYJ);
            }

            if (!isSubscribeSuccess && Objects.nonNull(rmaOrder)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("订单号[%s]支付成功但升级失败，已自动生成售后单，售后单号[%s]，请为用户办理退款。", cloudStorageSubscribe.getOrderNumber(), rmaOrder.getRmaNumber()), FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.LQ, FeiShuAtUser.CYJ);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("[商城云服务订阅]云存储权益下发结果回调处理,商城权益下发异常。通知内容:%s 云服务订阅信息:%s 异常信息:{%s}", messageBody, JSON.toJSONString(cloudStorageSubscribe), e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅]云存储权益下发结果回调处理,商城权益下发失败。通知内容:%s 云服务订阅信息:%s 异常信息:{%s}", messageBody, JSON.toJSONString(cloudStorageSubscribe), e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }
        LOGGER.info("[商城云服务订阅]云存储权益下发结果回调处理结束...,msg:{}", messageBody);
        return Action.CommitMessage;
    }
}