package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.integration.avalara.bo.AvalaraResponseResolveBO;
import com.insta360.store.business.integration.avalara.enums.StoreTransactionType;
import com.insta360.store.business.integration.avalara.service.factory.TaxSimpleFactory;
import com.insta360.store.business.integration.avalara.service.hadler.TaxService;
import com.insta360.store.business.order.model.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @description: Avalara计税结果处理
 * @author: py
 * @create: 2024-06-05 16:19
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderCreateAvalaraCalculateTaxResultMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCreateAvalaraCalculateTaxResultMqConsumer.class);

    @Autowired
    TaxSimpleFactory taxSimpleFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_create_avalara_calculate_tax_result)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext context) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[Avalara计税结果处理]接收到消息,消息体:[%s]", messageBody));

        try {
            AvalaraResponseResolveBO avalaraResponseResolve = JSON.parseObject(messageBody, AvalaraResponseResolveBO.class);
            if (Objects.isNull(avalaraResponseResolve)) {
                LOGGER.error(String.format("[Avalara计税结果处理]数据为空,处理失败,消息体:[%s]", messageBody));
                return Action.CommitMessage;
            }

            Order order = avalaraResponseResolve.getOrder();
            TaxService taxService = taxSimpleFactory.getTaxService(StoreTransactionType.ORDER_CREATE_TRANSACTION, order.country());
            taxService.resolveTransaction(avalaraResponseResolve);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error("[Avalara计税结果处理]异常.消息体:{}.原因:{}", messageBody, e.getMessage());
            LOGGER.error("[Avalara计税结果处理]异常堆栈", e);
            return Action.CommitMessage;
        }
    }
}
