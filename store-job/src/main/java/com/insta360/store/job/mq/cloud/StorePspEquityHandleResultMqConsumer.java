package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.dto.StorePspEquityHandleResultDTO;
import com.insta360.store.business.cloud.model.PspEquityRecord;
import com.insta360.store.business.cloud.service.PspEquityRecordService;
import com.insta360.store.business.cloud.service.impl.helper.PspEquityHelper;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.constants.OrderBizMarkConstant;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2025/05/16
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class StorePspEquityHandleResultMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(StorePspEquityHandleResultMqConsumer.class);

    @Autowired
    PspEquityHelper pspEquityHelper;

    @Autowired
    OrderService orderService;

    @Autowired
    PspEquityRecordService pspEquityRecordService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_psp_equity_handle_result_notify)
    @Override
    public Action consume(Message message, ConsumeContext context) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[商城云服务]云服务PSP订单支付后权益下发通知MQ消费开始,msgId:{%s}, msg:{%s}", message.getMsgID(), messageBody));

        try {
            StorePspEquityHandleResultDTO pspEquityHandleResultDto = JSON.parseObject(messageBody, StorePspEquityHandleResultDTO.class);
            LOGGER.info("[商城云服务]云服务PSP订单权益处理结果{}", pspEquityHandleResultDto);
            if (Objects.isNull(pspEquityHandleResultDto)) {
                return Action.CommitMessage;
            }

            Order pspOrder = orderService.getByOrderNumber(pspEquityHandleResultDto.getOrderNumber());
            if (Objects.isNull(pspOrder) || !pspOrder.isMark(OrderBizMarkConstant.psp_order_mark)) {
                LOGGER.info("psp 订单权益处理信息异常{}", pspOrder);
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务]PSP订单信息不完整消费异常. 消息体:%s", pspEquityHandleResultDto), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            PspEquityRecord equityRecord = pspEquityRecordService.getByOrderNumber(pspOrder.getOrderNumber());
            if (Objects.isNull(equityRecord)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务]PSP权益记录不完整消费异常. 消息体:%s", pspEquityHandleResultDto), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            String type = pspEquityHandleResultDto.getType();
            switch (type) {
                case "normal":
                    equityRecord.setAckMark(pspEquityHandleResultDto.getSuccess());
                    break;
                case "refund":
                    equityRecord.setRefundAckMark(pspEquityHandleResultDto.getSuccess());
                default:
                    break;
            }

            // 权益处理失败告警
            if (Boolean.FALSE.equals(pspEquityHandleResultDto.getSuccess())) {
                String text = "refund".equals(type) ? "终止权益" : "权益下发";
                FeiShuMessageUtil.storeGeneralMessage(String.format("订单号[%s]，%s处理失败，请关注", pspOrder.getOrderNumber(), text), FeiShuGroupRobot.PspNotice, FeiShuAtUser.CYJ);
            }

            pspEquityRecordService.updateById(equityRecord);
        } catch (Exception e) {
            LOGGER.error(String.format("[商城云服务]云服务PSP订单支付后权益下发通知MQ消费异常,msgId:{%s}, msg:{%s}", message.getMsgID(), messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务]云服务PSP订单支付后权益下发通知MQ消费异常. 消息体:%s, 异常信息:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        return Action.CommitMessage;
    }
}
