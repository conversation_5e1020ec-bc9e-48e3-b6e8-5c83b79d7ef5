package com.insta360.store.job.task.reseller;

import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 每日检查reseller订单的paytime
 * @author: py
 * @create: 2022-04-24 16:03
 */
@Component
public class ResellerCheckPayTimeTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(ResellerCheckPayTimeTask.class);

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderService orderService;

    @XxlJob("resellerCheckPayTimeJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("每日检查reseller订单的paytime_任务开始...");
        // 批量获取不存在支付时间的分销订单
        List<ResellerOrder> resellerOrders = resellerOrderService.listOrderByPayTimeIsNull();
        if (CollectionUtils.isEmpty(resellerOrders)) {
            XxlJobLogger.log("没有支付时间为空的分销订单。skip。");
            return ReturnT.SUCCESS;
        }

        List<String> orderNumbers = resellerOrders.stream().map(ResellerOrder::getOrderNumber).collect(Collectors.toList());
        List<Order> orders = orderService.listByOrderNumber(orderNumbers);
        // key : orderId
        // value : orderNumber
        Map<Integer, String> orderMaps = orders.stream().collect(Collectors.toMap(Order::getId, Order::getOrderNumber));

        // key: orderNumber
        // value: payTime
        Map<String, LocalDateTime> payTimeMap = new HashMap<>();

        // 数据封装
        List<Integer> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        List<OrderPayment> orderPayments = orderPaymentService.listByOrderIds(orderIds);
        orderPayments.forEach(orderPayment -> payTimeMap.put(orderMaps.get(orderPayment.getOrder()), orderPayment.getPayTime()));
        resellerOrders.forEach(resellerOrder -> resellerOrder.setPayTime(payTimeMap.get(resellerOrder.getOrderNumber())));

        // 批量同步分销订单支付时间
        resellerOrderService.updateBatchById(resellerOrders);

        XxlJobLogger.log("每日检查reseller订单的paytime_任务结束...");
        return ReturnT.SUCCESS;
    }
}
