package com.insta360.store.job.mq.guanyi;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.integration.guanyi.bo.GuanyiOrderInterceptContextBO;
import com.insta360.store.business.integration.guanyi.enums.GyOrderInterceptState;
import com.insta360.store.business.integration.guanyi.enums.GyOrderInterceptTypeState;
import com.insta360.store.business.integration.guanyi.service.GuanyiSyncService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.guanyi.dto.GySyncOrderDTO;
import com.insta360.store.business.rma.enums.RmaState;
import com.insta360.store.business.rma.enums.RmaType;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Description 退款订单拦截通知mq消费
 * @Date 2021/10/14
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class RmaOrderInterceptMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(RmaOrderInterceptMqConsumer.class);

    @Autowired
    OrderService orderService;

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    GuanyiSyncService guanyiSyncService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.rma_order_state_gy_sync)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[退款订单拦截通知管易mq消费]接收到mq message:{}", json);

        GySyncOrderDTO syncOrder;
        try {
            syncOrder = JSON.parseObject(json, GySyncOrderDTO.class);
        } catch (Exception e) {
            LOGGER.error("[退款订单拦截通知管易mq消费]Gson转换失败。", e);
            return Action.CommitMessage;
        }

        // 通知管易拦截拒付订单
        RmaOrder rmaOrder = syncOrder.getRmaOrder();
        LOGGER.info("[退款订单拦截通知管易mq消费]rmaOrder:{}", JSON.toJSONString(rmaOrder));
        if (rmaOrder == null) {
            return Action.CommitMessage;
        }

        Order order = orderService.getById(rmaOrder.getOrderId());
        OrderItem orderItem = orderItemService.getById(rmaOrder.getOrderItemId());
        LOGGER.info("[退款订单拦截通知管易mq消费]order:{}, orderItem:{}", JSON.toJSONString(order), JSON.toJSONString(orderItem));
        if (order == null || orderItem == null) {
            return Action.CommitMessage;
        }

        // 非仅退款订单，或者订单状态不是'已配货'、'部分发货'，则不进行管易拦截
        if(!RmaType.rma_refund.equals(rmaOrder.rmaType()) || !OrderState.afterPayedAndBeforeDelivery(order.orderState())) {
            return Action.CommitMessage;
        }

        // 退款订单拦截上下文
        GuanyiOrderInterceptContextBO interceptContext = new GuanyiOrderInterceptContextBO();
        interceptContext.setOrderNumber(order.getOrderNumber());
        interceptContext.setInterceptType(GyOrderInterceptTypeState.refund.getState());
        interceptContext.setInterceptReason(rmaOrder.getReason());

        if(RmaState.init.equals(rmaOrder.rmaState())) {
            // 通知管易拦截该订单，避免该单发货。
            interceptContext.setInterceptState(GyOrderInterceptState.intercept.getState());
            boolean result = guanyiSyncService.syncInterceptGuanyiOrder(interceptContext);
            if (result) {
                guanyiSyncService.syncRefundUpdateGuanyiOrder(order, orderItem, rmaOrder.getState());
            } else {
                String msg = "订单：" + order.getOrderNumber() + "，拦截失败（管易），请及时处理！";
                FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.MainNotice, FeiShuAtUser.ZM, FeiShuAtUser.YCT, FeiShuAtUser.JHF, FeiShuAtUser.GQY);
            }
        } else {
            // 1、通知管易修改订单售后状态
            guanyiSyncService.syncRefundUpdateGuanyiOrder(order, orderItem, rmaOrder.getState());
            // 2、通知管易取消拦截订单
            interceptContext.setInterceptState(GyOrderInterceptState.cancel_intercept.getState());
            guanyiSyncService.syncInterceptGuanyiOrder(interceptContext);
        }

        return Action.CommitMessage;
    }
}
