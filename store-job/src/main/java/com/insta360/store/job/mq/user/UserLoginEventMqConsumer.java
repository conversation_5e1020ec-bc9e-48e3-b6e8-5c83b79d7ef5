package com.insta360.store.job.mq.user;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.outgoing.mq.user.dto.UserLoginMessageDTO;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.StoreAccountLoginHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @description: 用户登录事件同步
 * @author: py
 * @create: 2024-05-22 16:29
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class UserLoginEventMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(UserLoginEventMqConsumer.class);

    @Autowired
    StoreAccountLoginHelper storeAccountLoginHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_user_login_event)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[用户登录同步]成功接收到消息,msgId:{}, msg:{}", message.getMsgID(), messageBody));

        try {
            UserLoginMessageDTO userLoginParam = JSON.parseObject(messageBody, UserLoginMessageDTO.class);
            if (Objects.isNull(userLoginParam)) {
                return Action.CommitMessage;
            }
            StoreAccount storeAccount = userLoginParam.getStoreAccount();
            if (Objects.isNull(storeAccount)) {
                LOGGER.error("[用户登录同步]storeAccount不存在");
                return Action.CommitMessage;
            }
            storeAccountLoginHelper.bindStoreAccount(storeAccount);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[用户登录同步]发生异常,消息体[%s],原因[%s]", messageBody, e.getMessage()), e);
            return Action.CommitMessage;
        }
    }
}
