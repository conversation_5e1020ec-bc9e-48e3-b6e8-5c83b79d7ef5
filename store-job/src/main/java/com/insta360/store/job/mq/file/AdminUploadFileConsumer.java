package com.insta360.store.job.mq.file;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.admin.upload.dto.UploadBodyMsgDTO;
import com.insta360.store.business.admin.upload.model.UploadTaskRecord;
import com.insta360.store.business.admin.upload.service.StoreAdminUploadService;
import com.insta360.store.business.admin.upload.service.factory.AdminUploadServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/10/11
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class AdminUploadFileConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminUploadFileConsumer.class);
    @Autowired
    AdminUploadServiceFactory adminUploadServiceFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.business_upload_file_data,threadSize = 5)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext context) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[商城业务数据批量导入]接收到数据导入处理消息,消息体:{}", messageBody);

        try {
            UploadBodyMsgDTO uploadBodyMsgDto = JSON.parseObject(messageBody, UploadBodyMsgDTO.class);
            if (Objects.isNull(uploadBodyMsgDto)) {
                return Action.CommitMessage;
            }

            // 上传记录
            UploadTaskRecord uploadTaskRecord = uploadBodyMsgDto.getUploadTaskRecord();
            // 数据导入处理
            StoreAdminUploadService service = adminUploadServiceFactory.getService(uploadTaskRecord.getBusinessType());
            service.doImport(uploadBodyMsgDto);
        } catch (Exception e) {
            LOGGER.error(String.format("[商城业务数据批量导入]mq消费异常... 消息体:%s", messageBody), e);
            if(e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        return Action.CommitMessage;
    }
}
