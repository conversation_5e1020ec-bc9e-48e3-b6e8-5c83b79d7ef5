package com.insta360.store.job.task.reseller;

import com.insta360.store.business.reseller.enums.ResellerOrderState;
import com.insta360.store.business.reseller.model.ResellerOrder;
import com.insta360.store.business.reseller.service.ResellerBonusService;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import com.insta360.store.business.reseller.service.impl.helper.ResellerOrderHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description: 定时任务-分销订单'可提现'状态节点自动流转
 */
@Component
public class ResellerOrderSyncTask {

    public static final Logger logger = LoggerFactory.getLogger(ResellerOrderSyncTask.class);

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    ResellerBonusService resellerBonusService;

    @Autowired
    ResellerOrderHelper resellerOrderHelper;

    @XxlJob("resellerOrderWithdrawStatusSyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("分销订单'可提现'状态节点自动流转_任务开始...");
        List<ResellerOrder> resellerOrders = resellerOrderService.getUnavailableOrders();
        if (CollectionUtils.isEmpty(resellerOrders)) {
            XxlJobLogger.log("本次任务暂未扫描到'已支付'的分销订单...");
            return ReturnT.SUCCESS;
        }
        for (ResellerOrder resellerOrder : resellerOrders) {
            try {
                boolean result = resellerOrderHelper.withdrawalAllowedCheck(resellerOrder);
                if (result) {
                    // 设置为可提现
                    resellerOrder.setState(ResellerOrderState.withdraw.getCode());
                    resellerOrderService.updateById(resellerOrder);
                    // 发放奖励金
                    resellerBonusService.giveBonus(resellerOrder);
                }
            } catch (Exception e) {
                XxlJobLogger.log(e);
                logger.error(String.format("分销订单:%s 可提现状态自动变更场景发生异常...", resellerOrder.getOrderNumber()), e);
            }
        }
        XxlJobLogger.log("分销订单'可提现'状态节点自动流转_任务结束...");
        return ReturnT.SUCCESS;
    }
}
