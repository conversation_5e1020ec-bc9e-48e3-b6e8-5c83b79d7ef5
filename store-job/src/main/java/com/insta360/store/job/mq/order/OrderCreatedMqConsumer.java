package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.enums.QuotaAdjustmentType;
import com.insta360.store.business.common.constants.RedisKeyConstant;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.bo.OrderGuestEmailSendBO;
import com.insta360.store.business.order.bo.OrderPromotionBO;
import com.insta360.store.business.order.email.*;
import com.insta360.store.business.order.email.constant.OrderEmailConstantPool;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.outgoing.mq.check.helper.DoubleCheckSendHelper;
import com.insta360.store.business.outgoing.mq.cloud.helper.StoreCloudProductMessageSendHelper;
import com.insta360.store.business.outgoing.mq.insurance.helper.InsuranceMessageSendHelper;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.model.ResellerUtmSource;
import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.reseller.service.ResellerUtmSourceService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.insta360.store.business.commodity.model.Commodity.TITAN_BOOKING_ID;

/**
 * @Author: wbt
 * @Date: 2022/05/25
 * @Description: 订单创建事件通知聚合
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderCreatedMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCreatedMqConsumer.class);

    @Autowired
    OrderService orderService;

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    ResellerService resellerService;

    @Autowired
    OrderEmailFactory orderEmailFactory;

    @Autowired
    DoubleCheckSendHelper doubleCheckSendHelper;

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    ResellerUtmSourceService resellerUtmSourceService;

    @Autowired
    InsuranceMessageSendHelper insuranceMessageSendHelper;

    @Autowired
    StoreCloudProductMessageSendHelper storeCloudProductMessageSendHelper;

    /**
     * 订单创建事件通知聚合
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_created)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.error("订单支付事件消费失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }
            LOGGER.info("订单[{}]接收到消息事件,消息体为:{}", order.getOrderNumber(), orderMessage);

            // 云服务订单
            if (Objects.nonNull(order.getCloudSubscribeMark()) && order.getCloudSubscribeMark()) {
                // 取消除当前订单外其他未支付的云服务订单
                orderHelper.autoCancelUnpaidSubscribeOrder(order);
                // 云服务续费订单，需发起扣款
                if (PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
                    storeCloudProductMessageSendHelper.sendRenewOrderPaymentMessage(order, 0L);
                }
            }

            // 订单创建Avalara计税结果处理
            orderMessageSendHelper.sendOrderCreateAvalaraCalculateTaxResultMessage(orderMessage.getAvalaraResponseJson(), order);

            // Care/延保 单独购买保存相机序列号
            insuranceMessageSendHelper.sendOrderCreatedInsuranceBindMessage(order.getId(), orderMessage.getDeviceSerial());

            // 订单自动取消队列
            orderMessageSendHelper.sendOrderAutoCancelMessage(order);

            // 订单创建成功优惠折扣拆分处理
            orderMessageSendHelper.sendOrderCreatedDiscountSplitMessage(order);

            // 订单创建成功后进行二次校验消息
            doubleCheckSendHelper.sendDoubleCheckMessage(order.getId(), DoubleCheckEnum.OrderCreateCheck);

            // 定制贴数据处理
            orderMessageSendHelper.sendCustomImageHandleMessage(order.getId(), orderMessage.getCustomItemImageInfos());

            // 云服务配件折扣权益额度扣减
            orderHelper.discountBenefitQuotaAdjustment(order, QuotaAdjustmentType.DEDUCT);

            // 创建订单邮件发送
            this.sendProfessionalEmail(order);

            // 记录分销推广参数
            this.checkOrderPromotion(orderMessage.getOrderPromotion(), order);

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error("订单创建事件聚合异常.消息体:{}.原因:{}", messageBody, e.getMessage());
            LOGGER.error("订单创建事件聚合异常堆栈", e);
            return Action.CommitMessage;
        }
    }

    /**
     * 创建订单邮件发送
     *
     * @param order
     */
    private void sendProfessionalEmail(Order order) {
        // 如果不是游客订单，不用理会
        if (!order.isGuestOrder()) {
            return;
        }
        // 云服务续费订单不发送邮件
        if (order.getCloudSubscribeMark() && PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            return;
        }

        // 用户邮箱
        String customerEmail = order.getContactEmail();
        // 游客下单-邮件风控key
        String emailControlKey = RedisKeyConstant.GUEST_ORDER_EMAIL_CONTROL + customerEmail;
        OrderGuestEmailSendBO orderGuestEmailSend = (OrderGuestEmailSendBO) RedisTemplateUtil.getValue(emailControlKey);

        // 风控中
        if (Objects.nonNull(orderGuestEmailSend) && OrderEmailConstantPool.GUEST_EMAIL_CONTROL_STATUS.equals(orderGuestEmailSend.getState())) {
            LOGGER.info("guest email controlling, no send:{}", order.getOrderNumber());
            return;
        }

        // 第6封时，发送风控提示邮件
        if (guestControllingEmailRule(orderGuestEmailSend)) {
            BaseOrderEmail email = orderEmailFactory.getEmail(order, guestOrderDuplicateNotifyEmail.class);
            email.doSend(customerEmail);
            orderGuestEmailSend.setState(OrderEmailConstantPool.GUEST_EMAIL_CONTROL_STATUS);
            // 风控一小时
            RedisTemplateUtil.setKeyValue(emailControlKey,
                    orderGuestEmailSend,
                    OrderEmailConstantPool.RISK_CONTROL_EMAIL_TIME,
                    TimeUnit.HOURS);
            LOGGER.info("guest email controlling, send controlling email:{}", order.getOrderNumber());
            return;
        }

        this.doSendGuestOrderEmail(order);

        LOGGER.info("guest email send okay:{}", order.getOrderNumber());

        // 更新缓存内容
        this.updateGuestOrderEmailRecord(orderGuestEmailSend, emailControlKey);
    }

    /**
     * 记录分销推广参数
     *
     * @param orderPromotion
     * @param order
     */
    private void checkOrderPromotion(OrderPromotionBO orderPromotion, Order order) {
        if (Objects.isNull(orderPromotion)) {
            LOGGER.info("orderPromotion is null.");
            return;
        }

        LOGGER.info("checkOrderPromotion. begin. orderNumber:{}，promotion prams：{}", order.getOrderNumber(), JSON.toJSONString(orderPromotion));
        Reseller reseller = resellerService.getByPromoCode(orderPromotion.getPromoCode());
        if (Objects.isNull(reseller)) {
            LOGGER.error("分销码：{}，但对应的分销商不存在，订单号为：{}", orderPromotion.getPromoCode(), order.getOrderNumber());
            return;
        }

        // 字段长度校验
        if (this.isLengthBeyond(orderPromotion.getUtmCampaign())
                || this.isLengthBeyond(orderPromotion.getUtmSource())
                || this.isLengthBeyond(orderPromotion.getUtmMedium())) {
            LOGGER.error("分销推广参数长度超出限制。promotion prams：{}", orderPromotion);
            FeiShuMessageUtil.storeGeneralMessage(String.format("分销推广参数长度超出限制。promotion prams：{%s}", orderPromotion),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return;
        }

        // order_number幂等性校验
        if (Objects.nonNull(resellerUtmSourceService.getByOrderNumber(order.getOrderNumber()))) {
            LOGGER.error("分销达人记录orderNumber幂等性校验不通过。orderNumber：{}", order.getOrderNumber());
            FeiShuMessageUtil.storeGeneralMessage(String.format("分销达人记录orderNumber幂等性校验不通过。orderNumber：{%s}", order.getOrderNumber()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return;
        }

        // 数据落库
        ResellerUtmSource resellerUtmSource = new ResellerUtmSource();
        resellerUtmSource.setOrderNumber(order.getOrderNumber());
        resellerUtmSource.setCreateTime(order.getCreateTime());
        resellerUtmSource.setUpdateTime(order.getCreateTime());
        resellerUtmSource.setPromoCode(orderPromotion.getPromoCode());
        resellerUtmSource.setUtmCampaign(orderPromotion.getUtmCampaign());
        resellerUtmSource.setUtmSource(orderPromotion.getUtmSource());
        resellerUtmSource.setUtmMedium(orderPromotion.getUtmMedium());
        resellerUtmSourceService.save(resellerUtmSource);
    }

    /**
     * 字符串长度超出阈值
     *
     * @param str
     * @return
     */
    private Boolean isLengthBeyond(String str) {
        return StringUtils.length(str) > 500;
    }

    /**
     * 执行游客下单邮件发送
     *
     * @param order
     */
    private void doSendGuestOrderEmail(Order order) {
        LOGGER.info("send guest professional item email. begin. orderNumber:{}", order.getOrderNumber());
        // 发送创建订单通知邮件
        BaseOrderEmail email;
        if (orderService.containsCommodity(order.getId(), TITAN_BOOKING_ID)) {
            // titan预订邮件
            email = orderEmailFactory.getEmail(order, GuestOrderCreateEmailTitan.class);
        } else {
            // 普通订单邮件
            email = orderEmailFactory.getEmail(order, GuestOrderCreatedEmail.class);
        }

        if (email != null) {
            email.doSend(order.getContactEmail());
        }

        LOGGER.info("send guest professional item email. end. orderNumber:{}", order.getOrderNumber());
    }

    /**
     * 更新游客订单邮件发送缓存内容
     * - 若缓存不存在，初始化并设置过期时间为5分钟
     * - 若缓存存在，递增发送计数并保持原有过期时间
     *
     * @param orderGuestEmailSend 当前邮件发送记录对象（可为null）
     * @param emailControlKey     Redis缓存key
     */
    private void updateGuestOrderEmailRecord(OrderGuestEmailSendBO orderGuestEmailSend, String emailControlKey) {
        // 初始化或更新发送记录
        if (Objects.isNull(orderGuestEmailSend)) {
            orderGuestEmailSend = new OrderGuestEmailSendBO();
            orderGuestEmailSend.setGuestOrderSize(1);
            RedisTemplateUtil.setKeyValue(emailControlKey,
                    orderGuestEmailSend,
                    OrderEmailConstantPool.GUEST_EMAIL_CONTROL_WINDOW_MINUTES,
                    TimeUnit.MINUTES);
        } else {
            orderGuestEmailSend.setGuestOrderSize(orderGuestEmailSend.getGuestOrderSize() + 1);
            RedisTemplateUtil.setKeyValue(emailControlKey,
                    orderGuestEmailSend,
                    RedisTemplateUtil.getKeyExpire(emailControlKey),
                    TimeUnit.SECONDS);
        }
    }

    /**
     * 游客风控邮件发送时机
     *
     * @param orderGuestEmailSend
     * @return
     */
    private Boolean guestControllingEmailRule(OrderGuestEmailSendBO orderGuestEmailSend) {
        return Objects.nonNull(orderGuestEmailSend) &&
                orderGuestEmailSend.getGuestOrderSize() >= OrderEmailConstantPool.MAX_ORDER_EMAILS_BEFORE_CONTROL;
    }
}
