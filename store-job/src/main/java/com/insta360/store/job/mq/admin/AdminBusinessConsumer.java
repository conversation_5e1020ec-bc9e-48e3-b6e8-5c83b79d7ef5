package com.insta360.store.job.mq.admin;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.util.UUIDUtils;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.admin.business.AdminBusinessFactory;
import com.insta360.store.business.admin.business.BusinessResult;
import com.insta360.store.business.admin.business.handler.BaseAdminBusinessHandler;
import com.insta360.store.business.exception.RetryHandlerException;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.admin.dto.AdminBusinessBO;
import com.insta360.store.business.outgoing.mq.admin.enums.AdminBusinessType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * [后台综合业务] job执行 消费者
 *
 * <AUTHOR>
 * @date 2024/05/16
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class AdminBusinessConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminBusinessConsumer.class);

    @Autowired
    AdminBusinessFactory adminBusinessFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_admin_business_common)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[后台综合业务] 接收到message:{}", messageBody);
        String uuid = UUIDUtils.generateUuid();
        AdminBusinessBO adminBusinessBo = JSON.parseObject(messageBody, AdminBusinessBO.class);

        AdminBusinessType businessType = Optional.ofNullable(adminBusinessBo).map(AdminBusinessBO::getAdminBusinessType).orElse(null);
        if (businessType == null) {
            LOGGER.error("[后台综合业务] 缺少必要参数 抛弃该消息 data:{}", adminBusinessBo);
            return Action.CommitMessage;
        }
        adminBusinessBo.setExecuteTaskId(uuid);
        Integer businessId = adminBusinessBo.getBusinessId();

        // 通过枚举获取工厂
        BaseAdminBusinessHandler adminBusinessHandler = adminBusinessFactory.getAdminBusinessHandler(businessType);
        if (adminBusinessHandler == null) {
            LOGGER.error("[后台综合业务] 未获取到处理器 抛弃该消息 businessId:{} uuid:{} data:{}", businessId, uuid, adminBusinessBo);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[后台综合业务] 未获取到处理器 type:%s businessId:%s data:%s", businessType, businessId, adminBusinessBo), FeiShuGroupRobot.InternalWarning);
            return Action.CommitMessage;
        }
        LOGGER.info("[后台综合业务] 开始执行... businessId:{} uuid:{} data:{}", businessId, uuid, adminBusinessBo);
        try {
            BusinessResult businessResult = adminBusinessHandler.executeBusiness(adminBusinessBo);
            LOGGER.info("[后台综合业务] 执行完成 businessId:{} uuid:{} data:{}", businessId, uuid, businessResult);
        } catch (Exception e) {
            LOGGER.error("[后台综合业务] 发生业务异常此消息将抛弃 businessId:{} uuid:{} message:{}", businessId, uuid, e.getMessage(), e);
            if (e instanceof RetryHandlerException) {
                return Action.ReconsumeLater;
            }
            FeiShuMessageUtil.storeGeneralMessage(String.format("[后台综合业务] 发生异常 businessId:%s uuid:%s type:%s error:%s stack:%s", businessType, uuid, businessId, e.getMessage(), ExceptionUtil.stacktraceToString(e)), FeiShuGroupRobot.InternalWarning);
        }
        return Action.CommitMessage;
    }

}
