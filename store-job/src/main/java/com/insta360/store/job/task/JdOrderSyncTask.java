package com.insta360.store.job.task;

import com.insta360.store.business.integration.jingdong.constant.JdConstantPool;
import com.insta360.store.business.integration.jingdong.service.impl.helper.SyncJdOrderHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2021/1/4
 * @Description: 定时任务-同步京东订单至管易、商城
 */
@Component
public class JdOrderSyncTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(JdOrderSyncTask.class);

    @Autowired
    SyncJdOrderHelper orderHelper;

    @XxlJob("jdOrderSyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("同步京东订单至管易、商城_任务开始...");
        // 同步京东采购订单到商城和管易
        Integer recordCount = null;
        try {
            recordCount = orderHelper.syncOrder(JdConstantPool.PAGE_INDEX);
        } catch (Exception e) {
            LOGGER.error("京东订单同步管易异常...", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        int indexSize = (Objects.isNull(recordCount) ? 0 : recordCount) / Integer.parseInt(JdConstantPool.PAGE_SIZE);
        if (indexSize < 1) {
            return ReturnT.SUCCESS;
        }

        for (int index = 2; index <= indexSize + 1; index++) {
            try {
                orderHelper.syncOrder(String.valueOf(index));
            } catch (Exception e) {
                LOGGER.error("京东订单同步管易异常...", e);
                XxlJobLogger.log(e);
            }
        }
        XxlJobLogger.log("同步京东订单至管易、商城_任务结束...");
        return ReturnT.SUCCESS;
    }
}
