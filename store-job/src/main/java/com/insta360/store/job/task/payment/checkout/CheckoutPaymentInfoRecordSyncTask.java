package com.insta360.store.job.task.payment.checkout;

import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.payment.service.impl.helper.CkoTransactionRecordHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: checkout支付数据拉取
 * @author: py
 * @create: 2024-11-20 14:52
 */
@Component
public class CheckoutPaymentInfoRecordSyncTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(CheckoutPaymentInfoRecordSyncTask.class);

    @Autowired
    CkoTransactionRecordHelper ckoTransactionRecordHelper;

    @XxlJob("checkoutPaymentInfoRecordSyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("同步checkout支付数据_任务开始...");
        LOGGER.info("同步checkout支付数据_任务开始...");

        try {
            // 获取hk、us、eu的报告数据
            ckoTransactionRecordHelper.listReportDetails();
        } catch (Exception e) {
            XxlJobLogger.log("同步checkout支付数据_出现异常...");
            LOGGER.error(String.format("同步checkout支付数据_出现异常.异常信息:%s", e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("同步checkout支付数据_出现异常.异常信息:%s", e.getMessage()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return ReturnT.FAIL;
        }

        XxlJobLogger.log("同步checkout支付数据_任务结束...");
        LOGGER.info("同步checkout支付数据_任务开始...");
        return ReturnT.SUCCESS;
    }
}
