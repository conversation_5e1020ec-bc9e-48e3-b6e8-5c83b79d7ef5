package com.insta360.store.job.mq.cloud;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.bo.StoreCloudBusinessTypeResultBO;
import com.insta360.store.business.cloud.constant.StoreBenefitConstant;
import com.insta360.store.business.cloud.dto.BenefitChangeDTO;
import com.insta360.store.business.cloud.enums.BenefitPlatform;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitService;
import com.insta360.store.business.cloud.service.StoreBenefitSubscribeService;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitContext;
import com.insta360.store.business.cloud.service.impl.factory.StoreBenefitFactory;
import com.insta360.store.business.cloud.service.impl.handler.benefit.StoreCloudBenefitBusinessHandler;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.utils.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 云存储权益变更MQ消费
 * @Date 2024/5/13
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class AppCloudBenefitChangeMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppCloudBenefitChangeMqConsumer.class);

    @Autowired
    StoreCloudBenefitBusinessHandler storeCloudBenefitBusinessHandler;

    @Autowired
    CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    StoreBenefitFactory storeBenefitFactory;

    /**
     * 处理云存储权益变更通知的消息消费方法
     *
     * @param message        消息对象，包含消息体内容。
     * @param consumeContext 消费上下文，提供额外的消费环境信息。
     * @return 消息处理结果，指示消息是否处理成功或者需要重试。
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_change_notify)
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[云存储权益变更通知]消费消息,msg:{}", messageBody);
        try {
            // 解析消息体为业务对象
            BenefitChangeDTO benefitChangeParam = JSON.parseObject(messageBody, BenefitChangeDTO.class);
            Integer userId = benefitChangeParam.getUserId();
            Long expirationTime = benefitChangeParam.getExpirationTime();
            String skuId = benefitChangeParam.getSkuId();
            // 校验消息体参数
            CommonUtil.validationObject(benefitChangeParam);

            // 商城渠道订阅权益不消费
            if(BenefitPlatform.STORE.getName().equals(benefitChangeParam.getPlatform())) {
                return Action.CommitMessage;
            }

            // 根据用户ID查询云存储权益信息
            CloudStorageStoreBenefit storeBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(userId);
            if (Objects.nonNull(storeBenefit)) {
                // 检查是否重复消费相同的权益变更通知
                if (storeBenefit.getSkuId().equals(skuId) && storeBenefit.getExpirationTime().equals(expirationTime)) {
                    LOGGER.info("[云存储权益变更通知]重复消费，不予处理。msg:{}", JSON.toJSONString(benefitChangeParam));
                    return Action.CommitMessage;
                }
            }

            if (!StoreBenefitConstant.LIMIT_SKU_LIST.contains(skuId)) {
                LOGGER.info("[云存储权益变更通知]接收到无效的权益变更通知，skuId: {}, msg: {}", skuId, messageBody);
                FeiShuMessageUtil.storeGeneralMessage(String.format("[云存储权益变更通知]接收到无效的权益变更通知，skuId: {%s}, msg: %s", skuId, messageBody), FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.CYJ);
                return Action.CommitMessage;
            }

            // 处理权益变更业务逻辑
            StoreBenefitContext storeBenefitContext = storeCloudBenefitBusinessHandler.getBenefitChangeContext(benefitChangeParam, storeBenefit);
            StoreCloudBusinessTypeResultBO typeMappingResult = storeBenefitContext.getTypeMappingResult();
            // 根据订阅类型获取对应的权益订阅服务，并处理相关业务
            StoreBenefitSubscribeService storeBenefitSubscribeService = storeBenefitFactory.getStoreBenefitSubscribeService(typeMappingResult.getSkuSubscribeType());
            storeBenefitSubscribeService.handle(storeBenefitContext);
        } catch (Exception e) {
            // 记录消费异常日志并发送飞书通知
            LOGGER.error(String.format("[云存储权益变更通知]消费异常，msg: %s", messageBody), e);
            if (e instanceof InstaException) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[云存储权益变更通知]消费异常，msg: %s 错误信息: {%s}", messageBody, e.getMessage()), FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.CYJ);
                return Action.CommitMessage;
            }
            FeiShuMessageUtil.storeGeneralMessage(String.format("[云存储权益变更通知]消费异常，msg: %s 错误信息: %s", messageBody, ExceptionUtil.stacktraceToString(e)), FeiShuGroupRobot.CloudSubscribe, FeiShuAtUser.TW, FeiShuAtUser.CYJ);
            return Action.ReconsumeLater;
        }

        // 消息处理成功
        LOGGER.info("[云存储权益变更通知]消费成功，msg:{}", messageBody);
        return Action.CommitMessage;
    }

}
