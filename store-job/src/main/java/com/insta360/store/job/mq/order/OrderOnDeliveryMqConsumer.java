package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.email.enums.EmailSendBusinessType;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.outgoing.mq.email.helper.StoreCommonEmailSendHelper;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import com.insta360.store.business.outgoing.mq.weapp.helper.WeappMessageSendHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: wbt
 * @Date: 2022/05/30
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderOnDeliveryMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderOnDeliveryMqConsumer.class);

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    WeappMessageSendHelper weappMessageSendHelper;

    @Autowired
    StoreCommonEmailSendHelper storeCommonEmailSendHelper;

    /**
     * 订单已发货事件通知聚合
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_on_delivery)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.info("订单全部发货事件消费失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 订单发货后 klarna交易捕获
            orderMessageSendHelper.sendOrderOnDeliveryKlarnaCaptureMessage(order);

            // 订单全部发货后同步forter
            orderMessageSendHelper.sendOrderOnDeliverySyncForterMessage(order);

            // 订单全部发货后自动开具发票
            orderMessageSendHelper.sendOrderOnDeliveryAutoInvoiceMessage(order);

            // 全部发货后自动帮助订单扭转收货状态
            orderMessageSendHelper.sendOrderAutoConfirmDeliveryMessage(order);

            // 订单全部发货后同步微信小程序
            weappMessageSendHelper.sendOrderSyncWeappMessage(order);

            // x4发送用户指引邮件
            storeCommonEmailSendHelper.emailSend(order, EmailSendBusinessType.ORDER_ON_DELIVERY_X4_EMAIL);

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error("订单发货事件聚合异常。消息体:{}. 原因:{}", messageBody, e);
            return Action.CommitMessage;
        }
    }
}
