package com.insta360.store.job.task.cloud;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.cloud.bo.CloudSubscribeChangeNotifyBO;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.StoreSubscribeChangeService;
import com.insta360.store.business.cloud.service.impl.factory.StoreCloudSubscribeFactory;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.payment.service.impl.helper.PayPalPaymentHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/7/9
 */
@Component
public class StoreCloudSubscribeExpiredTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudSubscribeExpiredTask.class);

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    StoreCloudSubscribeFactory storeCloudSubscribeFactory;

    @Autowired
    PayPalPaymentHelper payPalPaymentHelper;

    /**
     * 执行商城云服务订阅过期处理的定时任务。
     * 该方法负责检查并处理已过期的商城云服务订阅记录，包括更新订阅状态、通知云存储和回收权益。
     *
     * @param param 任务参数，本例中未使用。
     * @return 返回执行结果，成功时返回SUCCESS。
     */
    @XxlJob("storeCloudSubscribeExpiredJobHandler")
    public ReturnT<String> execute(String param) {
        // 记录任务开始
        XxlJobLogger.log("开始本次定时任务处理。");

        // 查询已过期的商城云服务订阅记录
        List<CloudStorageSubscribe> cloudStorageSubscribeList = cloudStorageSubscribeService.listExpiredSubscribeByTime();
        // 如果没有过期记录，则任务结束
        if (CollectionUtils.isEmpty(cloudStorageSubscribeList)) {
            XxlJobLogger.log("未扫描出已过期的商城云服务订阅记录。");
            return ReturnT.SUCCESS;
        }

        // 遍历过期订阅记录，进行处理
        for (CloudStorageSubscribe cloudStorageSubscribe : cloudStorageSubscribeList) {
            try {
                // 创建订阅变更通知对象
                CloudSubscribeChangeNotifyBO cloudSubscribeChangeNotifyBo = new CloudSubscribeChangeNotifyBO();
                cloudSubscribeChangeNotifyBo.setSubscribeId(cloudStorageSubscribe.getId());
                cloudSubscribeChangeNotifyBo.setScenesType(ServiceScenesType.EXPIRED);
                // 根据场景类型获取订阅变更服务，并调用通知方法
                StoreSubscribeChangeService storeSubscribeChangeService = storeCloudSubscribeFactory.getStoreSubscribeChangeService(ServiceScenesType.EXPIRED);
                storeSubscribeChangeService.handleSubscribeUpdateNotification(cloudSubscribeChangeNotifyBo);

                // paypal pending取消交易处理
                payPalPaymentHelper.refundPendingPayment(cloudStorageSubscribe);
            } catch (Exception e) {
                // 记录处理失败的订阅记录和异常信息
                LOGGER.error(String.format("商城云服务订阅过期处理失败. 订阅记录: %s", JSON.toJSONString(cloudStorageSubscribe)), e);
                // 发送失败通知到飞书机器人
                FeiShuMessageUtil.storeGeneralMessage(String.format("商城云服务订阅过期处理失败. 订阅记录: %s", JSON.toJSONString(cloudStorageSubscribe)), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        }

        return ReturnT.SUCCESS;
    }
}
