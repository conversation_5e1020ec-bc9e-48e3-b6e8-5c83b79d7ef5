package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.forter.support.factory.ForterSyncFactory;
import com.insta360.store.business.forter.support.handler.ForterSyncService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.payment.bo.OrderSyncForterBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: wbt
 * @Date: 2022/11/01
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderPayedSyncForterConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderPayedSyncForterConsumer.class);

    @Autowired
    ForterSyncFactory forterSyncFactory;

    @Autowired
    OrderPaymentService orderPaymentService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_payed_sync_forter)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("订单支付状态同步forter。message:{}", messageBody);
        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            OrderSyncForterBO orderSyncForter = orderMessage.getOrderSyncForter();
            if (orderSyncForter == null) {
                LOGGER.error("订单支付事件同步forter失败。同步参数不存在。message:{}", messageBody);
                return Action.CommitMessage;
            }

            Order order = orderSyncForter.getOrder();
            if (order == null) {
                LOGGER.error("订单支付事件同步forter失败。订单参数不存在。message:{}", messageBody);
                return Action.CommitMessage;
            }

            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (orderPayment == null) {
                LOGGER.error("订单支付事件同步forter失败。订单支付记录不存在。message:{}", messageBody);
                return Action.CommitMessage;
            }

            // sync
            ForterSyncService forterSyncHandle = forterSyncFactory.getForterSyncHandle(orderSyncForter.getPaymentChannel());
            Boolean syncResult = forterSyncHandle.syncForter(orderSyncForter);

            // 同步失败则重试
            return syncResult ? Action.CommitMessage : Action.ReconsumeLater;
        } catch (Exception e) {
            LOGGER.error(String.format("订单支付状态同步forter失败。消息体:{%s}. 原因:{%s}", messageBody, e.getMessage()), e);
            return Action.CommitMessage;
        }
    }
}
