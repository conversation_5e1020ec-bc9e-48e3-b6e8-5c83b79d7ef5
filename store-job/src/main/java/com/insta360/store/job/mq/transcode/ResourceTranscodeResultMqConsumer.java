package com.insta360.store.job.mq.transcode;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.commodity.dto.CommodityDisplayDTO;
import com.insta360.store.business.commodity.enums.DisplayCompressState;
import com.insta360.store.business.commodity.exception.CommodityErrorCode;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.utils.UrlUtils;
import com.insta360.store.business.meta.enums.OSSFileUrlEnum;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.transcode.dto.TranscodeMessageDTO;
import com.insta360.store.business.transcode.enums.BusinessType;
import com.insta360.store.business.transcode.service.impl.BaseTranscodeHandler;
import com.insta360.store.business.transcode.service.impl.factory.TranscodeFactory;
import com.insta360.store.job.controller.cache.CommodityDisplayCachePack;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Objects;

/**
 * 资源转码mq消费者
 *
 * @Author: py
 * @Description:
 * @date 2024/05/24
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class ResourceTranscodeResultMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceTranscodeResultMqConsumer.class);

    /**
     * 重投递最大次数
     */
    private static final Integer MAX_RE_CONSUMPTION_TIMES = 5;

    @Autowired
    TranscodeFactory transcodeFactory;

    @Autowired
    CommodityDisplayCachePack commodityDisplayCachePack;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_transcode_result)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext context) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[资源转码]转码结果开始处理,消息体:{}", messageBody);

        if (message.getReconsumeTimes() >= MAX_RE_CONSUMPTION_TIMES) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("[主图转码]重投递超过最大次数,参数:[%s]", messageBody),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return Action.CommitMessage;
        }

        try {
            if (StringUtil.isBlank(messageBody)) {
                LOGGER.error("[资源转码]转码消息体为空：{}", messageBody);
                return Action.CommitMessage;
            }

            TranscodeMessageDTO transcodeMessageParam = JSON.parseObject(messageBody, TranscodeMessageDTO.class);
            String businessType = transcodeMessageParam.getBusinessType();
            BaseTranscodeHandler transcodeHandler = transcodeFactory.getTranscodeHandler(businessType);
            transcodeHandler.saveTranscodeResult(transcodeMessageParam);

            if (BusinessType.DISPLAY.name().equals(businessType)) {
                // 海外oss校验
                CommodityDisplayDTO commodityDisplayParam = transcodeMessageParam.getCommodityDisplayParam();
                Integer displayId = commodityDisplayParam.getId();
                CommodityDisplay commodityDisplay = commodityDisplayService.getById(displayId);
                if (Objects.isNull(commodityDisplay)) {
                    LOGGER.error(String.format("[主图转码]校验海外oss链接,展示图id不存在:%s", displayId));
                    return Action.CommitMessage;
                }

                if (StringUtil.isBlank(commodityDisplayParam.getUrlM()) || StringUtil.isBlank(commodityDisplayParam.getUrlS())) {
                    LOGGER.error(String.format("[主图转码]中图和小图结果为空,数据:%s", commodityDisplayParam));
                    return Action.CommitMessage;
                }

                LOGGER.info(String.format("[主图转码]消息重投递次数:%s,displayId:%s", message.getReconsumeTimes(), displayId));

                if (checkTranscodeUrl(commodityDisplayParam.getUrlM(), displayId)) {
                    return Action.ReconsumeLater;
                }

                if (checkTranscodeUrl(commodityDisplayParam.getUrlS(), displayId)) {
                    return Action.ReconsumeLater;
                }

                // 修改display的状态
                commodityDisplay.setCompressState(DisplayCompressState.completed.getCode());
                commodityDisplayService.updateByDisplayId(commodityDisplay);

                // 缓存更新
                CachePutKeyParameterBO cachePutKeyParameter = new CachePutKeyParameterBO();
                cachePutKeyParameter.setCommodityIds(Arrays.asList(commodityDisplay.getCommodity()));
                commodityDisplayCachePack.modifyDisplay(cachePutKeyParameter);
                LOGGER.info(String.format("[主图转码]缓存更新结束:%s", commodityDisplayParam));
            }
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[资源转码]转码数据更新出现异常,data:%s,异常信息:%s", messageBody, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[资源转码]转码数据更新出现异常,data:%s", messageBody),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ, FeiShuAtUser.PY);
            return Action.CommitMessage;
        }
    }

    /**
     * 校验海外oss链接是否存在
     *
     * @param transcodeUrl
     * @param displayId
     * @return
     */
    private Boolean checkTranscodeUrl(String transcodeUrl, Integer displayId) {
        // 校验海外的oss
        String ossForeignResourceUrl = UrlUtils.replaceUrlPrefixCdnToOss(transcodeUrl, OSSFileUrlEnum.image, false);
        boolean urlForeignExists = UrlUtils.urlExists(ossForeignResourceUrl);
        if (!urlForeignExists) {
            LOGGER.error(String.format("[主图转码]准备重投递校验海外oss链接,展示图id:%s,转码结果资源没有同步海外:%s", displayId, ossForeignResourceUrl));
            return true;
        }
        return false;
    }
}
