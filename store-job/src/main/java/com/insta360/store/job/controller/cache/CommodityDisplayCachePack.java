package com.insta360.store.job.controller.cache;

import com.insta360.store.business.configuration.cache.http.annotation.HttpRequestCache;
import com.insta360.store.business.configuration.cache.http.enums.StoreBusinessType;
import com.insta360.store.business.configuration.cache.monitor.redis.put.annotation.CachePutMonitor;
import com.insta360.store.business.configuration.cache.monitor.redis.put.bo.CachePutKeyParameterBO;
import com.insta360.store.business.configuration.cache.type.CachePutType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @description: 套餐主图转码缓存更新
 * @author: py
 * @create: 2024-05-28 17:55
 */
@Component
public class CommodityDisplayCachePack {

    public static final Logger LOGGER = LoggerFactory.getLogger(CommodityDisplayCachePack.class);

    /**
     * 更新套餐展示图转码数据
     *
     * @param cachePutKeyParameter
     */
    @CachePutMonitor(cacheableType = CachePutType.COMMODITY_DISPLAY)
    @HttpRequestCache(businessType = StoreBusinessType.NAVIGATION_BAR, isRefreshCache = true)
    public void modifyDisplay(CachePutKeyParameterBO cachePutKeyParameter) {
        LOGGER.info(String.format("[主图转码]缓存更新参数:%s", cachePutKeyParameter));
    }
}
