package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.google.common.collect.Lists;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.TimeUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderPaymentState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderAdminRemark;
import com.insta360.store.business.order.model.OrderOperatorRecord;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderAdminRemarkService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.payment.bo.PaymentReconciliationCheckBO;
import com.insta360.store.business.payment.bo.PaymentReconciliationCheckResultBO;
import com.insta360.store.business.payment.enums.PaymentReconciliationStatus;
import com.insta360.store.business.payment.model.OrderPaymentTransactionReconciliation;
import com.insta360.store.business.payment.service.OrderPaymentTransactionReconciliationService;
import com.insta360.store.business.payment.service.impl.handler.BasePaymentHandler;
import com.insta360.store.business.payment.service.impl.handler.PaymentHandlerFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 订单支付交易对账MQ消费者
 * @Date 2024/4/6
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderPaymentTransactionReconciliationConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderPaymentTransactionReconciliationConsumer.class);

    @Autowired
    OrderService orderService;

    @Autowired
    PaymentHandlerFactory paymentHandlerFactory;

    @Autowired
    OrderAdminRemarkService orderAdminRemarkService;

    @Autowired
    OrderPaymentTransactionReconciliationService orderPaymentTransactionReconciliationService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_payed_transaction_check)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[订单支付后交易对账]接收到交易对账Mq消息. msg:{}", messageBody);
        OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
        // 订单支付信息
        OrderPayment orderPayment = orderMessage.getOrderPayment();

        // 订单信息
        Order order = orderService.getById(orderPayment.getOrder());
        if (Objects.isNull(order)) {
            LOGGER.info("[订单支付后交易对账]订单不存在. orderId:{}", orderPayment.getOrder());
            return Action.CommitMessage;
        }

        if (!OrderPaymentState.PAYED.equals(orderPayment.paymentState())) {
            LOGGER.info("[订单支付后交易对账]订单支付状态异常. orderNumber:{}, paymentState:{}", order.getOrderNumber(), orderPayment.getState());
            return Action.CommitMessage;
        }

        // 订单支付渠道
        PaymentChannel paymentChannel = orderPayment.paymentChannel();
        if (Objects.isNull(paymentChannel) || PaymentChannel.isInternalChannel(paymentChannel)) {
            LOGGER.info("[订单支付后交易对账]订单支付渠道为空或者为内部渠道. orderNumber:{}, paymentChannel:{}", order.getOrderNumber(), Objects.nonNull(paymentChannel) ? paymentChannel.name() : null);
            return Action.CommitMessage;
        }

        if (PaymentChannel.alipay.equals(paymentChannel) || PaymentChannel.repairment_alipay.equals(paymentChannel)) {
            LOGGER.info("[订单支付后交易对账]支付宝支付暂不支持对账. orderNumber:{}, paymentChannel:{}", order.getOrderNumber(), paymentChannel.name());
            return Action.CommitMessage;
        }

        if(StringUtils.isBlank(orderPayment.getChannelPaymentId())) {
            LOGGER.info("[订单支付后交易对账]订单支付流水号为空. orderNumber:{}", order.getOrderNumber());
            return Action.CommitMessage;
        }

        // 查询交易对账表，是否已存在对账记录
        OrderPaymentTransactionReconciliation reconciliationRecord = orderPaymentTransactionReconciliationService.getByOrderNumber(order.getOrderNumber());
        if (Objects.nonNull(reconciliationRecord) && PaymentReconciliationStatus.RECONCILED.equals(reconciliationRecord.parseStatus())) {
            LOGGER.info("[订单支付后交易对账]重复对账,订单已成功对账. orderNumber:{}", order.getOrderNumber());
            return Action.CommitMessage;
        }

        // 初始化对账记录
        if (Objects.isNull(reconciliationRecord)) {
            reconciliationRecord = this.initReconciliationRecord(order, orderPayment);
        }

        try {
            PaymentReconciliationCheckBO paymentReconciliationCheckParam = new PaymentReconciliationCheckBO();
            paymentReconciliationCheckParam.setOrderNumber(order.getOrderNumber());
            paymentReconciliationCheckParam.setOrder(order);
            paymentReconciliationCheckParam.setOrderPayment(orderPayment);
            // 调用交易对账接口

            BasePaymentHandler paymentHandler = paymentHandlerFactory.getPaymentHandler(paymentChannel);
            PaymentReconciliationCheckResultBO reconciliationCheckResult = paymentHandler.handleTransactionReconciliation(paymentReconciliationCheckParam);
            if(Objects.isNull(reconciliationCheckResult)) {
                FeiShuMessageUtil.storeGeneralMessage(String.format("[订单支付后交易对账]支付对账异常,未返回对账结果。 订单号:{%s}, 支付渠道:{%s}, 交易流水号:{%s}", order.getOrderNumber(), orderPayment.getChannel(), orderPayment.getChannelPaymentId()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }
            // 对账后置处理
            this.performPostReconciliationActions(order, reconciliationCheckResult);
            // 更新对账记录
            this.updateReconciliationRecord(reconciliationRecord, reconciliationCheckResult);
        } catch (Exception e) {
            LOGGER.error(String.format("[订单支付后交易对账]支付对账异常. 订单号:{%s}, 支付渠道:{%s}, 交易流水号:{%s}", order.getOrderNumber(), orderPayment.getChannel(), orderPayment.getChannelPaymentId()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[订单支付后交易对账]支付对账异常. 订单号:{%s}, 支付渠道:{%s}, 交易流水号:{%s}, 异常信息:{%s}", order.getOrderNumber(), orderPayment.getChannel(), orderPayment.getChannelPaymentId(), e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        LOGGER.info("[订单支付后交易对账]交易对账Mq消费流程处理结束. msg:{}", messageBody);
        return Action.CommitMessage;
    }

    /**
     * 初始化对账记录
     *
     * @param order
     * @param orderPayment
     */
    private OrderPaymentTransactionReconciliation initReconciliationRecord(Order order, OrderPayment orderPayment) {
        OrderPaymentTransactionReconciliation reconciliationRecord = new OrderPaymentTransactionReconciliation();
        reconciliationRecord.setTransactionId(orderPayment.getChannelPaymentId());
        reconciliationRecord.setOrderNumber(order.getOrderNumber());
        reconciliationRecord.setPaymentChannel(orderPayment.getChannel());
        reconciliationRecord.setReconciliationStatus(PaymentReconciliationStatus.PENDING.getCode());
        orderPaymentTransactionReconciliationService.save(reconciliationRecord);
        return reconciliationRecord;
    }

    /**
     * 更新对账记录
     *
     * @param reconciliationRecord
     * @param reconciliationCheckResult
     */
    private void updateReconciliationRecord(OrderPaymentTransactionReconciliation reconciliationRecord, PaymentReconciliationCheckResultBO reconciliationCheckResult) {
        BeanUtils.copyProperties(reconciliationCheckResult, reconciliationRecord);
        orderPaymentTransactionReconciliationService.updateById(reconciliationRecord);
    }

    /**
     * 对账后置处理
     *
     * @param order
     * @param reconciliationCheckResult
     */
    private void performPostReconciliationActions(Order order, PaymentReconciliationCheckResultBO reconciliationCheckResult) {
        // 非对账异常不处理
        if (!PaymentReconciliationStatus.ERROR.getCode().equals(reconciliationCheckResult.getReconciliationStatus())) {
            return;
        }

        // 订单商家备注更新
        this.updateOrderAdminRemark(order);
    }

    /**
     * 更新订单商家备注
     *
     * @param order
     */
    private void updateOrderAdminRemark(Order order) {
        OrderAdminRemark adminRemark = orderAdminRemarkService.getByOrder(order.getId());
        String content = TimeUtil.getTimePath() + " 订单对账异常，拦截推单";

        OrderOperatorRecord orderOperatorRecord = new OrderOperatorRecord();
        orderOperatorRecord.setOrderId(order.getId());
        orderOperatorRecord.setUsername("系统");
        orderOperatorRecord.setJobNumber("-1");
        orderOperatorRecord.setOperatorType("商家备注");
        orderOperatorRecord.setFromData(Objects.isNull(adminRemark) ? null : adminRemark.getContent());
        orderOperatorRecord.setToData(content);
        orderOperatorRecord.setCreateTime(LocalDateTime.now());
        orderOperatorRecord.setUpdateTime(LocalDateTime.now());

        if (Objects.isNull(adminRemark)) {
            adminRemark = new OrderAdminRemark();
            adminRemark.setOrder(order.getId());
            adminRemark.setCreateTime(LocalDateTime.now());
            adminRemark.setContent(content);
        } else {
            String oldRemark = adminRemark.getContent();
            if (Objects.isNull(oldRemark)) {
                adminRemark.setContent(content);
            } else {
                oldRemark = oldRemark.length() > 950 ? oldRemark.substring(0, 950) : oldRemark;
                adminRemark.setContent(content + "\n" + oldRemark);
            }
        }

        orderAdminRemarkService.saveRemarkAndOperator(Lists.newArrayList(adminRemark), Lists.newArrayList(orderOperatorRecord));
    }
}
