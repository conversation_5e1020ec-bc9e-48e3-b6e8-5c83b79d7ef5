package com.insta360.store.job;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @Author: wbt
 * @Date: 2021/01/12
 * @Description:
 */
@EnableRetry
@EnableAsync
@EnableCaching
@EnableScheduling
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(value = {"com.insta360.*"})
@MapperScan({"com.insta360.store.business.**.dao"})
@EnableFeignClients(basePackages = {"com.insta360.store.business.outgoing.**"})
public class StoreJobApplication {

    public static void main(String[] args) {
        SpringApplication.run(StoreJobApplication.class, args);
    }
}

