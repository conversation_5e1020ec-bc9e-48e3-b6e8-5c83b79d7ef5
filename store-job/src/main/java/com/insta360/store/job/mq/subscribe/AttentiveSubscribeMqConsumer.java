package com.insta360.store.job.mq.subscribe;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.integration.attentive.service.Handler.AttentiveHandlerFactory;
import com.insta360.store.business.integration.attentive.service.Handler.BaseAttentiveHandler;
import com.insta360.store.business.integration.attentive.service.helper.AttentiveHelper;
import com.insta360.store.business.outgoing.mq.subscribe.dto.EmailSubscribeDTO;
import com.insta360.store.business.user.enums.SubscribeActivityPageEnum;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.service.EmailSubscribeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @description: attentive数据上报消费
 * @author: py
 * @create: 2023-09-11 14:42
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class AttentiveSubscribeMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(AttentiveSubscribeMqConsumer.class);

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    AttentiveHelper attentiveHelper;

    @Autowired
    AttentiveHandlerFactory attentiveHandlerFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_attentive_subscribe, threadSize = 5)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        EmailSubscribeDTO emailSubscribeParam = JSON.parseObject(messageBody, EmailSubscribeDTO.class);

        EmailSubscribe emailSubscribe = emailSubscribeService.getById(emailSubscribeParam.getSubscribeId());
        if (emailSubscribe == null) {
            LOGGER.error("[attentive]数据消费失败,subscribeId:" + emailSubscribeParam.getSubscribeId());
            return Action.CommitMessage;
        }

        String activityPage = emailSubscribe.getActivityPage();
        SubscribeActivityPageEnum subscribeActivityPageEnum = SubscribeActivityPageEnum.parse(activityPage);
        if (Objects.isNull(subscribeActivityPageEnum)) {
            return Action.CommitMessage;
        }
        BaseAttentiveHandler attentiveHandler = attentiveHandlerFactory.getAttentiveHandler(subscribeActivityPageEnum);
        attentiveHandler.subscribeUser(emailSubscribe, emailSubscribeParam);
        LOGGER.info("[attentive]数据消费成功，subscribeId:" + emailSubscribeParam.getSubscribeId());
        return Action.CommitMessage;
    }
}
