package com.insta360.store.job.task.commodity;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.commodity.service.impl.helper.CommodityCustomsTaxStockHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/3/27
 */
@Component
public class CommodityOmsStockSyncTask {

    @Autowired
    CommodityCustomsTaxStockHelper commodityCustomsTaxStockHelper;

    @XxlJob("commodityOmsStockSyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("US关税套餐库存初始化调度任务开始。");

        commodityCustomsTaxStockHelper.customsTaxStockHandle(InstaCountry.US);

        XxlJobLogger.log("US关税套餐库存初始化调度任务结束。");
        return ReturnT.SUCCESS;
    }
}
