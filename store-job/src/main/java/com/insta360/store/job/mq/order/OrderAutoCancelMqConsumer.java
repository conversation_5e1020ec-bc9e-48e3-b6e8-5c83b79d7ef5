package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.google.common.collect.Lists;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.order.service.impl.helper.OrderCronHelper;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.payment.service.impl.helper.PayPalPaymentHelper;
import com.insta360.store.business.product.model.Product;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2022/03/31
 * @Description: 订单自动取消消费端
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderAutoCancelMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderAutoCancelMqConsumer.class);

    @Autowired
    OrderService orderService;

    @Autowired
    OrderCronHelper orderCronHelper;

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    PayPalPaymentHelper payPalPaymentHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_auto_cancel_monitor)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.error("订单支付事件消费失败。订单不存在。订单id:{}", messageBody);
                return Action.CommitMessage;
            }

            // 延迟消费再查一次
            order = orderService.getById(order.getId());
            if (this.checkOrder(order)) {
                // 处理paypal订阅升级pending订单
                this.cancelPayPalPendingPay(order);
                // 订单自动取消
                this.autoOrderCancel(order);
            }

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("订单自动取消失败。消息体:{%s}. 原因:{%s}", messageBody, e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("订单自动取消失败。消息体:{%s}. 原因:{%s}", messageBody, e.getMessage()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return Action.CommitMessage;
        }
    }

    /**
     * 处理paypal订阅升级pending订单
     *
     * @param order
     */
    private void cancelPayPalPendingPay(Order order) {
        LOGGER.info("最新取消订单信息。。{}", order);
        if (!order.getCloudSubscribeMark() && ServiceScenesType.UPGRADE.name().equals(order.getSubscribeScenesType())) {
            return;
        }
        if (!OrderState.payment_pending.equals(order.orderState())) {
            return;
        }
        OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
        PaymentChannel paymentChannel = orderPayment.paymentChannel();
        if (Objects.isNull(paymentChannel) || !PaymentChannel.isPayPalSubscribeChannel(paymentChannel)) {
            return;
        }
        if (StringUtil.isBlank(orderPayment.getChannelPaymentId())) {
            LOGGER.error(String.format("paypal pending订单缺失交易号。。订单信息{%s}", order));
            FeiShuMessageUtil.storeGeneralMessage(String.format("paypal pending订单缺失交易号。。订单号{%s}", order.getOrderNumber()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return;
        }
        try {
            payPalPaymentHelper.doRefundPaypalPayment(order);
        } catch (Exception e) {
            LOGGER.error(String.format("paypal pending订单缺失交易号。。订单信息{%s}", order), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("paypal pending订单取消失败。。订单号{%s} 原因{%s}", order.getOrderNumber(), e.getMessage()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
    }

    /**
     * 订单条件校验
     *
     * @param order
     * @return
     */
    private boolean checkOrder(Order order) {
        // 工单维修的订单不自动取消
        if (order.isRepairOrder()) {
            return false;
        }

        // 只考虑未支付和pending的订单
        List<OrderState> orderStates = Lists.newArrayList(OrderState.init);
        if (Objects.nonNull(order.getCloudSubscribeMark()) && order.getCloudSubscribeMark()) {
            orderStates.add(OrderState.payment_pending);
        }

        // 常规订单只考虑‘未支付’状态，云服务订单还需考虑’支付处理中‘状态
        if (!orderStates.contains(order.orderState())) {
            return false;
        }

        // Titan尾款的订单不自动取消
        boolean resultTitanRest = orderService.containsCommodity(order.getId(), Commodity.TITAN_REST_ID);
        if (resultTitanRest) {
            return false;
        }

        // Titan的订单不自动取消
        boolean resultTitan = orderService.containProduct(order.getId(), Product.TITAN_ID);
        if (resultTitan) {
            return false;
        }

        // 包含Pro2的订单不自动取消
        return !orderService.containProduct(order.getId(), Product.PRO2_ID);
    }

    /**
     * 订单自动取消
     *
     * @param order
     */
    private void autoOrderCancel(Order order) {
        // order cancel
        orderCronHelper.cancel(order);
    }
}
