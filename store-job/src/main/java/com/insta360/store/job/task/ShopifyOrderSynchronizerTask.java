package com.insta360.store.job.task;

import com.insta360.compass.core.util.TimeUtil;
import com.insta360.store.business.integration.guanyi.enums.GySyncOrderEnum;
import com.insta360.store.business.integration.guanyi.service.GuanyiSyncService;
import com.insta360.store.business.integration.shopify.module.ShopifyOrder;
import com.insta360.store.business.integration.shopify.service.ShopifyOrderService;
import com.insta360.store.business.outgoing.mq.guanyi.dto.GySendMessageDTO;
import com.insta360.store.business.outgoing.mq.guanyi.helper.GyMessageSendHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/05/09
 * @Description: 同步shopify订单到管易
 */
@Component
public class ShopifyOrderSynchronizerTask {

    public static final Logger logger = LoggerFactory.getLogger(ShopifyOrderSynchronizerTask.class);

    public static final String LOCAL_TIME = "2018-06-05 20:00:00";

    @Autowired
    ShopifyOrderService shopifyOrderService;

    @Autowired
    GuanyiSyncService guanyiSyncService;

    @Autowired
    GyMessageSendHelper gyMessageSendHelper;

    @XxlJob("shopifyOrderSyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("同步shopify订单到管易_任务开始...");
        doSync(getFromTime());
        XxlJobLogger.log("同步shopify订单到管易_任务开始...");
        return ReturnT.SUCCESS;
    }

    private LocalDateTime getFromTime() {
        LocalDateTime fromTime = null;
        ShopifyOrder order = shopifyOrderService.getShopifyOrder();
        if (order != null) {
            fromTime = order.getCreateTime();
        }
        if (fromTime == null) {
            LocalDateTime dateTime = LocalDateTime.parse(LOCAL_TIME);
            fromTime = TimeUtil.parseLocalDateTime(dateTime, ZoneId.systemDefault(), ZoneId.of("GMT+8"));
        }
        return fromTime;
    }

    private void doSync(LocalDateTime fromTime) {
        if (logger.isInfoEnabled()) {
            logger.info(String.format("Sync shopify order [%s]", fromTime.toString()));
        }

        List<ShopifyOrder> orders = shopifyOrderService.syncOrders(fromTime);
        for (ShopifyOrder shopifyOrder : orders) {
            try {
                // sync order
                gyMessageSendHelper.sendOrderSyncMessage(new GySendMessageDTO(shopifyOrder.getId(), GySyncOrderEnum.SHOPIFY_ORDER.getOrderType()));
                logger.info(String.format("Sync shopify order [%s]", shopifyOrder.getOrderNumber()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
