package com.insta360.store.job.mq.guanyi;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.integration.guanyi.enums.GySyncOrderEnum;
import com.insta360.store.business.integration.guanyi.service.GuanyiSyncService;
import com.insta360.store.business.integration.jingdong.lib.model.JdOrder;
import com.insta360.store.business.integration.jingdong.lib.model.JdRmaOrder;
import com.insta360.store.business.meta.bo.ToGuanyiOrder;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.enums.OrderStockSourceType;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderOperatorRecord;
import com.insta360.store.business.order.service.OrderOperatorRecordService;
import com.insta360.store.business.outgoing.mq.guanyi.dto.GySyncOrderDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 多渠道订单同步mq消费
 * @Date 2021/10/20
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderChannelSyncMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(OrderChannelSyncMqConsumer.class);

    @Autowired
    GuanyiSyncService guanyiSyncService;

    @Autowired
    OrderOperatorRecordService orderOperatorRecordService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.all_order_gy_sync, threadSize = 2)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        Boolean result;
        GySyncOrderDTO gySyncOrder;
        LOGGER.info("[多渠道订单同步管易mq消费]接收到消息:{}", json);

        try {
            gySyncOrder = JSON.parseObject(json, GySyncOrderDTO.class);
            GySyncOrderEnum gySyncOrderEnum = GySyncOrderEnum.parse(gySyncOrder.getTag());
            if (gySyncOrderEnum == null) {
                return Action.CommitMessage;
            }

            // 多类型订单
            ToGuanyiOrder order = (ToGuanyiOrder) ApplicationContextHolder
                    .getApplicationContext()
                    .getBean(gySyncOrderEnum.getBaseDao())
                    .selectById(gySyncOrder.getOrderId());

            if (order == null) {
                return Action.CommitMessage;
            }

            // 推单是否成功
            result = guanyiSyncService.syncToGuanyi(order);
        } catch (Exception e) {
            LOGGER.error("orderChannelSyncMqConsumer request:{}, message:{}", json, e.getMessage(), e);
            return Action.ReconsumeLater;
        }

        // 识别消息中，订单备货来源是否为系统
        if (result && OrderStockSourceType.SYSTEM.getCode().equals(gySyncOrder.getSource())) {
            this.updateOrderOperatorRecord(gySyncOrder.getOrderId());
        }

        return Action.CommitMessage;
    }

    /**
     * 系统推单成功后，记录日志
     *
     * @param orderId
     */
    private void updateOrderOperatorRecord(Integer orderId) {
        OrderOperatorRecord orderOperatorRecord = new OrderOperatorRecord();
        orderOperatorRecord.setOrderId(orderId);
        orderOperatorRecord.setJobNumber(OrderStockSourceType.SYSTEM.getCode());
        orderOperatorRecord.setUsername(OrderStockSourceType.SYSTEM.getName());
        orderOperatorRecord.setOperatorType("订单状态");
        orderOperatorRecord.setFromData(OrderState.payed.getNameZh());
        orderOperatorRecord.setToData(OrderState.prepared.getNameZh());
        orderOperatorRecord.setCreateTime(LocalDateTime.now());
        orderOperatorRecord.setUpdateTime(LocalDateTime.now());
        orderOperatorRecordService.save(orderOperatorRecord);
    }
}
