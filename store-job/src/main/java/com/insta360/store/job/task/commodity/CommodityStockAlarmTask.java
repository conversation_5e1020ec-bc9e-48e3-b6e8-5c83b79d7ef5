package com.insta360.store.job.task.commodity;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.commodity.bo.CommodityLowInventoryBO;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 套餐库存预警定时任务
 * @Date 2023/9/27
 */
@Component
public class CommodityStockAlarmTask {

    private static final String msg = "【库存预警】 \n 产品名称：%s \n 套餐名称：%s \n 套餐ID：%s \n 该套餐库存已低于5件,请及时补货或注意跟进套餐销售状态。";

    @Autowired
    CommodityService commodityService;

    @XxlJob("commodityStockAlarmJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("套餐库存不足预警_任务开始...");

        List<CommodityLowInventoryBO> lowInventoryList = commodityService.listByCommodityLowInventory(5, SaleState.normal);
        for (CommodityLowInventoryBO commodityLowInventory : lowInventoryList) {
            String fsMsg = String.format(msg, commodityLowInventory.getProductName(), commodityLowInventory.getCommodityName(), commodityLowInventory.getCommodityId());

            FeiShuMessageUtil.storeGeneralMessage(fsMsg, FeiShuGroupRobot.MainNotice, FeiShuAtUser.ZLY, FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW,
                    FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
        }

        XxlJobLogger.log("套餐库存不足预警_任务结束... 低库存套餐列表:{}", JSON.toJSONString(lowInventoryList));
        return ReturnT.SUCCESS;
    }
}
