package com.insta360.store.job.mq.email.handler;

import com.insta360.store.business.email.enums.EmailSendBusinessType;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/11/21 上午11:28
 */
public abstract class BaseEmailSendHandler implements BaseEmailSendInterface {

    /**
     * 邮件业务类型枚举
     */
    protected EmailSendBusinessType emailSendBusinessType;

    public EmailSendBusinessType getEmailSendBusinessType() {
        return emailSendBusinessType;
    }

    public void setEmailSendBusinessType(EmailSendBusinessType emailSendBusinessType) {
        this.emailSendBusinessType = emailSendBusinessType;
    }
}
