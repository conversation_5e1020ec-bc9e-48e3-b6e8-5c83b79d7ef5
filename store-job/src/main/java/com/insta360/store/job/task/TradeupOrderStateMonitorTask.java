package com.insta360.store.job.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.store.business.tradeup.enums.TradeupOrderState;
import com.insta360.store.business.tradeup.model.TradeupOrder;
import com.insta360.store.business.tradeup.service.TradeupOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description: 定时任务-订单状态流转监控
 */
@Component
public class TradeupOrderStateMonitorTask {

    public static final Logger logger = LoggerFactory.getLogger(TradeupOrderStateMonitorTask.class);

    @Autowired
    TradeupOrderService orderService;

    @XxlJob("tradeupOrderStateMonitorJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("订单状态流转监控_任务开始...");
        // 发短信邮件提醒一直没邮寄的用户
        this.notifyUserToDelivery();
        XxlJobLogger.log("订单状态流转监控_任务结束...");
        return ReturnT.SUCCESS;
    }

    private void notifyUserToDelivery() {
        // 创建订单15天后状态没变化则发短信提醒
        LocalDateTime needNotifyTime = LocalDateTime.now().minusDays(15);
        QueryWrapper<TradeupOrder> qw = new QueryWrapper<>();
        qw.eq("state", TradeupOrderState.evaluated.getCode());
        qw.eq("is_notify_delivery", false);
        qw.isNotNull("order_id");
        qw.lt("create_time", needNotifyTime);
        List<TradeupOrder> orders = orderService.list(qw);

        if (logger.isInfoEnabled()) {
            logger.info(String.format("%d trade up orders found to notify", orders.size()));
        }

        for (TradeupOrder order : orders) {
            orderService.notifyExpressProcess(order);

            if (logger.isDebugEnabled()) {
                logger.debug("Auto notify trade up order user" + order.getOrderNumber());
            }
        }
    }
}

