package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.dto.CallbackNotifyMonitorDTO;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.model.CloudStorageSubscribeChangeNotify;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeChangeNotifyService;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/6/27
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class StoreCloudBenefitCallbackMonitorMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudBenefitCallbackMonitorMqConsumer.class);

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    CloudStorageSubscribeChangeNotifyService cloudStorageSubscribeChangeNotifyService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_callback_monitor)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[云存储权益下发结果回调延迟监控]已接收到消息... msg:{}", messageBody);
        try {
            // 消息体转换
            CallbackNotifyMonitorDTO callbackNotifyMonitorDto = JSON.parseObject(messageBody, CallbackNotifyMonitorDTO.class);
            // 回调结果监控检查
            this.callbackMonitorCheck(callbackNotifyMonitorDto);
        } catch (Exception e) {
            LOGGER.error(String.format("[云存储权益下发结果回调延迟监控]核对异常. 消息体:%s", messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[云存储权益下发结果回调延迟监控]核对异常. 消息体:%s, 异常信息:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        return Action.CommitMessage;
    }

    /**
     * 云存储权益下发结果回调延迟监控检查
     *
     * @param callbackNotifyMonitorDto
     */
    private void callbackMonitorCheck(CallbackNotifyMonitorDTO callbackNotifyMonitorDto) {
        // 消息体
        String json = JSON.toJSONString(callbackNotifyMonitorDto);
        // 查询对应通知记录
        CloudStorageSubscribeChangeNotify subscribeChangeNotify = cloudStorageSubscribeChangeNotifyService.getById(callbackNotifyMonitorDto.getNotifyId());
        if (Objects.isNull(subscribeChangeNotify)) {
            LOGGER.info("[云存储权益下发结果回调延迟监控]商城云服务服务场景变更通知记录不存在. msg:{}", json);
            throw new InstaException(CloudSubscribeErrorCode.NotExistSubscribeChangeNotifyException);
        }
        if (!callbackNotifyMonitorDto.getSubscribeId().equals(subscribeChangeNotify.getSubscribeId())) {
            LOGGER.info("[云存储权益下发结果回调延迟监控]通知记录对应的订阅ID不一致. msg:{}, subscribeChangeNotify:{}", json, JSON.toJSONString(subscribeChangeNotify));
            throw new InstaException(CloudSubscribeErrorCode.SubscribeDataIsInconsistentException);
        }
        if (!subscribeChangeNotify.getAckMark()) {
            LOGGER.info("[云存储权益下发结果回调延迟监控]云存储回调结果消息还未消费. msg:{}, subscribeChangeNotify:{}", json, JSON.toJSONString(subscribeChangeNotify));
            throw new InstaException(CloudSubscribeErrorCode.AckNotReturnedException);
        }

        // 订阅记录
        CloudStorageSubscribe cloudStorageSubscribe = cloudStorageSubscribeService.getById(callbackNotifyMonitorDto.getSubscribeId());
        if (Objects.isNull(cloudStorageSubscribe)) {
            LOGGER.info("[云存储权益下发结果回调延迟监控]当期订阅记录不存在. msg:{}, subscribeChangeNotify:{}", json, JSON.toJSONString(subscribeChangeNotify));
            throw new InstaException(CloudSubscribeErrorCode.NotExistSubscribeRecordException);
        }
        if (!cloudStorageSubscribe.getExecuteMark()) {
            LOGGER.info("[云存储权益下发结果回调延迟监控]当期订阅记录还未下发商城权益. msg:{}, subscribeChangeNotify:{}", json, JSON.toJSONString(subscribeChangeNotify));
            throw new InstaException(CloudSubscribeErrorCode.NotIssuedStoreBenefitException);
        }
    }
}
