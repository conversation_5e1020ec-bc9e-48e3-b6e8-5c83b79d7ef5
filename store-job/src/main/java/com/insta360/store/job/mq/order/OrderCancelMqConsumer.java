package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.enums.QuotaAdjustmentType;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: wbt
 * @Date: 2022/06/02
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderCancelMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCancelMqConsumer.class);

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    OrderHelper orderHelper;

    /**
     * 订单取消事件通知聚合
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_cancel)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();

            // 订单取消后同步forter
            orderMessageSendHelper.sendOrderCancelSyncForterMessage(order, true);

            // 云服务配件折扣权益额度回退
            orderHelper.discountBenefitQuotaAdjustment(order, QuotaAdjustmentType.ADD);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error("订单取消事件聚合异常。消息体:{}. 原因:{}", messageBody, e);
            return Action.CommitMessage;
        }
    }
}
