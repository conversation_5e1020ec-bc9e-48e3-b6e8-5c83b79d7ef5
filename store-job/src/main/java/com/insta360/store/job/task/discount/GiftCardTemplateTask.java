package com.insta360.store.job.task.discount;

import com.insta360.store.business.discount.config.DiscountCommonConfig;
import com.insta360.store.business.discount.model.GiftCardTemplate;
import com.insta360.store.business.discount.service.GiftCardTemplateService;
import com.insta360.store.business.discount.service.impl.helper.monitor.GiftCardTemplateMonitorHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 代金券模版有效期检查-定时任务
 * @Date 2022/9/28
 */
@Component
public class GiftCardTemplateTask {

    private static final int ZERO = 0;

    @Autowired
    private GiftCardTemplateService giftCardTemplateService;

    @Autowired
    private GiftCardTemplateMonitorHelper giftCardTemplateMonitorHelper;

    @Autowired
    private DiscountCommonConfig discountCommonConfig;

    @XxlJob("giftCardTemplateOverdueReminderJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("代金券模版有效期检查_任务开始...");
        List<GiftCardTemplate> giftCardTemplateList = giftCardTemplateService.listByEnable(true);
        if (CollectionUtils.isEmpty(giftCardTemplateList)) {
            XxlJobLogger.log("未扫描出已启用的代金券模块.");
            return ReturnT.SUCCESS;
        }
        LocalDateTime currentTime = LocalDateTime.now();
        giftCardTemplateList.stream()
                .forEach(
                        giftCardTemplate -> {
                            LocalDateTime invalidTime = giftCardTemplate.getInvalidTime();
                            //计算代金券模版预过期天数
                            long intervalDays = intervalDaysCalculate(currentTime, invalidTime);
                            if (intervalDays <= ZERO) {
                                return;
                            }
                            if (intervalDays <= discountCommonConfig.getTemplateExpireDay()) {
                                giftCardTemplateMonitorHelper.doPackTemplateExpireTaskText(giftCardTemplate);
                            }
                        });

        XxlJobLogger.log("代金券模版有效期检查_任务结束...");
        return ReturnT.SUCCESS;
    }

    /**
     * 计算代金券模版预过期天数
     *
     * @param currentTime
     * @param invalidTime
     * @return
     */
    private long intervalDaysCalculate(LocalDateTime currentTime, LocalDateTime invalidTime) {
        return Duration.between(currentTime, invalidTime).toDays();
    }
}
