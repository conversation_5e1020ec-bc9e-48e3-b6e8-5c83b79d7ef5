package com.insta360.store.job.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.store.business.integration.guanyi.constant.GyConstantPool;
import com.insta360.store.business.integration.guanyi.lib.GuanyiConfiguration;
import com.insta360.store.business.integration.guanyi.lib.request.GyTradeGetDeliverysRequest;
import com.insta360.store.business.integration.lingxing.model.LxOrder;
import com.insta360.store.business.integration.lingxing.model.LxOrderDelivery;
import com.insta360.store.business.integration.lingxing.service.LxOrderDeliveryService;
import com.insta360.store.business.integration.lingxing.service.LxOrderService;
import com.insta360.store.business.integration.mabang.service.MbSyncService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 领星订单物流信息同步任务
 *
 * <AUTHOR>
 * @date 2023/10/07
 */
@Component
@RestController
public class LxOrderDeliverySyncTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(LxOrderDeliverySyncTask.class);

    @Autowired
    LxOrderService lxOrderService;

    @Autowired
    LxOrderDeliveryService lxOrderDeliveryService;

    @Autowired
    GuanyiConfiguration guanyiConfiguration;

    @Autowired
    MbSyncService mbSyncService;

    @XxlJob("lxOrderDeliverySyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("同步领星订单的物流信息_任务开始...");
        List<LxOrderDelivery> lxOrderDeliveryList = lxOrderDeliveryService.listByNotExpressCode();
        // 没有物流信息的FBM的订单
        if (CollectionUtils.isEmpty(lxOrderDeliveryList)) {
            return ReturnT.SUCCESS;
        }
        List<String> orderNumberList = lxOrderDeliveryList.stream().map(LxOrderDelivery::getLxOrderNumber).collect(Collectors.toList());
        List<LxOrder> lxOrders = lxOrderService.listByOrderNumbers(orderNumberList);
        for (LxOrder lxOrder : lxOrders) {
            boolean synced = true;
            try {
                synced = syncLxOrderDeliveryInfo(lxOrder);
            } catch (Exception e) {
                synced = false;
                String message = String.format("订单【%s】,亚马逊单号:%s,物流信息同步,发生异常", lxOrder.getLxOrderNumber(), lxOrder.getLxPlatformNumber());
                LOGGER.error("message:{}", message, e);
                XxlJobLogger.log(message);
            } finally {
                if (!synced) {
                    LOGGER.warn("订单【{}】,亚马逊单号:{},物流信息同步失败", lxOrder.getLxOrderNumber(), lxOrder.getLxPlatformNumber());
                }
            }
        }
        XxlJobLogger.log("同步马帮订单的物流信息_任务结束...");
        return ReturnT.SUCCESS;
    }

    /**
     * 同步领星订单物流信息
     *
     * @param lxOrder 领星订单
     * @return {@link Boolean}
     */
    private Boolean syncLxOrderDeliveryInfo(LxOrder lxOrder) {
        String result = syncOrderDeliveryFromGy(lxOrder.getLxPlatformNumber());
        JSONObject resultJson = JSONObject.parseObject(result);
        LOGGER.info("管易获取到到地址：json:{}", resultJson);
        JSONArray deliveryJson = resultJson.getJSONArray("deliverys");
        if (CollectionUtils.isEmpty(deliveryJson)) {
            return false;
        }
        // 根据第一条的发货记录进行记录
        JSONObject delivery = deliveryJson.getJSONObject(0);
        if (delivery == null) {
            return false;
        }
        // 物流信息
        String expressCode = delivery.getString("express_code");
        String expressNo = delivery.getString("express_no");

        // 发货时间
        JSONObject deliveryStatusInfo = delivery.getJSONObject("delivery_statusInfo");
        String expressTime = deliveryStatusInfo.getString("delivery_date");

        LxOrderDelivery orderDelivery = lxOrderDeliveryService.getByLxOrderNumber(lxOrder.getLxOrderNumber());
        if (orderDelivery == null) {
            return false;
        }
        // 更新物流信息
        orderDelivery.setExpressCode(expressNo);
        orderDelivery.setExpressCompany(expressCode);
        orderDelivery.setExpressTime(LocalDateTime.parse(expressTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return lxOrderDeliveryService.updateById(orderDelivery);
    }

    /**
     * 同步管易物流信息
     *
     * @param orderNumber 订单编号
     * @return {@link String}
     */
    public String syncOrderDeliveryFromGy(String orderNumber) {
        GyTradeGetDeliverysRequest request = new GyTradeGetDeliverysRequest();
        request.setOuter_code(orderNumber);
        request.setDelivery(GyConstantPool.ON_DELIVERY);
        return request.executePost(guanyiConfiguration);
    }
}
