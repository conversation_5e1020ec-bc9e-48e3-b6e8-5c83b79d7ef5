package com.insta360.store.job.mq.user;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpBroadcastProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpBroadcastChannelEnum;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.user.constant.UserConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/6/10 上午10:23
 */
@Component
public class UserLogoutBroadCastConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserLogoutBroadCastConsumer.class);

    @MessageTcpBroadcastProcessor(messageChannel = MessageTcpBroadcastChannelEnum.user_logout)
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String userToken = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[用户登出]商城UserLogoutBroadCastConsumer开始消费, messageBody:{%s}", userToken));
        try {
            if (StringUtil.isBlank(userToken)) {
                LOGGER.info(String.format("[用户登出]商城UserLogoutBroadCastConsumer消息为空, message:{%s}", JSON.toJSONString(message)));
                return Action.CommitMessage;
            }
            String key = UserConstant.USER_TOKEN_KEY + userToken;
            RedisTemplateUtil.delKey(key);
        } catch (Exception e) {
            LOGGER.info(String.format("[用户登出]商城UserLogoutBroadCastConsumer消费异常, message:{%s}, 异常原因:{%s}", JSON.toJSONString(message), e.getMessage()));
            FeiShuMessageUtil.storeGeneralMessage(String.format("[用户登出]商城UserLogoutBroadCastConsumer消费异常, message:{%s}, 异常原因:{%s}",
                    JSON.toJSONString(message), e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }
        LOGGER.info(String.format("[用户登出]商城UserLogoutBroadCastConsumer消费成功, message:{%s}", JSON.toJSONString(message)));
        return Action.CommitMessage;
    }
}
