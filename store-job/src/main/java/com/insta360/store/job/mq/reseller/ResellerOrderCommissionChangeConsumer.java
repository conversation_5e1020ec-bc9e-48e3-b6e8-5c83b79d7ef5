package com.insta360.store.job.mq.reseller;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.reseller.service.impl.helper.ResellerOrderHelper;
import com.insta360.store.business.rma.model.RmaOrder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Description 分销订单佣金变更MQ消费者
 * @Date 2022/12/14
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class ResellerOrderCommissionChangeConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResellerOrderCommissionChangeConsumer.class);


    @Autowired
    private ResellerOrderHelper resellerOrderHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.rma_state_change_auto_commission_recalculation)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("分销订单佣金重计算MQ消费者，消息体:{}", messageBody);
        if (StringUtils.isEmpty(messageBody)) {
            return Action.CommitMessage;
        }
        try {
            RmaOrder rmaOrder = JSON.parseObject(messageBody, RmaOrder.class);
            resellerOrderHelper.resellerOrderCommissionRecalculate(rmaOrder);
        } catch (InstaException e) {
            LOGGER.error("分销订单佣金重计算MQ消费者，业务处理异常... 消息体:" + JSON.toJSONString(messageBody), e);
            return Action.CommitMessage;
        }

        return Action.CommitMessage;
    }
}
