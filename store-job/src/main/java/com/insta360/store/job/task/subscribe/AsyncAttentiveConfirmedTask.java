package com.insta360.store.job.task.subscribe;

import com.insta360.store.business.integration.attentive.service.helper.AttentiveHelper;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.service.EmailSubscribeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 拉取attentive用户确认订阅的数据
 * @author: py
 * @create: 2023-11-02 10:37
 */
@Component
public class AsyncAttentiveConfirmedTask {

    private final static Logger LOGGER = LoggerFactory.getLogger(AsyncAttentiveConfirmedTask.class);

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    AttentiveHelper attentiveHelper;

    @XxlJob("asyncAttentiveConfirmedJobHandler")
    public ReturnT<String> execute(String param) {
        List<EmailSubscribe> emailSubscribes = emailSubscribeService.listNotConfirmedUsers();
        Map<String, List<EmailSubscribe>> subscribeMap = emailSubscribes.stream().collect(Collectors.groupingBy(EmailSubscribe::getPhoneNumber));
        LOGGER.info("准备拉取attentive的用户订阅确认情况的数据......");
        // 针对所有手机号进行确认
        subscribeMap.forEach((phoneNumber, subscribeList) -> {
            LOGGER.info(String.format("获取attentive用户订阅确认情况，数据拉取，手机号为:%s；订阅list：%s", phoneNumber, subscribeList.toString()));
            Boolean userConfirmed = attentiveHelper.asyncAttentiveConfirmedUser(phoneNumber);
            if (userConfirmed) {
                // 已经回复了Y，把所有的数据更新
                List<Integer> subscribeIds = subscribeList.stream().map(EmailSubscribe::getId).collect(Collectors.toList());
                emailSubscribeService.updateConfirmed(subscribeIds);
                LOGGER.info(String.format("用户回复了Y，订阅成功，数据库的数据更新成功。手机号为:%s", phoneNumber));
            }
        });
        LOGGER.info("拉取attentive的用户订阅确认情况的数据结束");
        return ReturnT.SUCCESS;
    }
}
