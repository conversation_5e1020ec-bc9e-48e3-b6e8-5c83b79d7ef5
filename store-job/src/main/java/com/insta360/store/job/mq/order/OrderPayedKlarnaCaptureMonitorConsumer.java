package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.outgoing.mq.order.helper.OrderMessageSendHelper;
import com.insta360.store.business.payment.constants.KlarnaConstant;
import com.insta360.store.business.payment.lib.klarna.config.KlarnaPaymentConfiguration;
import com.insta360.store.business.payment.lib.klarna.request.BaseKlarnaPaymentRequest;
import com.insta360.store.business.payment.lib.klarna.response.KlarnaOrderDetailResponse;
import com.insta360.store.business.payment.service.impl.handler.BaseKlarnaPaymentHandler;
import com.insta360.store.business.payment.service.impl.handler.PaymentHandlerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/04/14
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderPayedKlarnaCaptureMonitorConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderPayedKlarnaCaptureMonitorConsumer.class);

    /**
     * 时间格式化
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 第一次监控时间，过期前三天
     */
    private static final Integer FIRST_NOTIFY_DAY = 3;

    /**
     * 第二次监控时间，过期前一天
     */
    private static final Integer SECOND_NOTIFY_DAY = 1;

    /**
     * 需要监控的时间。过期前3天 和 过期前1天
     */
    private static final List<Integer> MONITOR_DAYS = Arrays.asList(FIRST_NOTIFY_DAY, SECOND_NOTIFY_DAY);

    @Autowired
    OrderPaymentService orderPaymentService;

    @Autowired
    OrderMessageSendHelper orderMessageSendHelper;

    @Autowired
    OrderService orderService;

    @Autowired
    PaymentHandlerFactory paymentHandlerFactory;

    /**
     * klaran capture 有效期监控
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_payed_klarna_capture_monitor)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("接收到 klarna capture monitor 消息投递。消息体:{}", messageBody);

        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.error("klarna capture 有效期校验失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 查询最新订单信息
            Order needOrder = orderService.getByOrderNumber(order.getOrderNumber());
            if (needOrder == null) {
                LOGGER.error("klarna capture 有效期校验失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 只关注支付后、发货完成前
            if (!KlarnaConstant.AFTER_PAYED_BEFORE_DELIVERY.contains(needOrder.orderState())) {
                LOGGER.error("订单状态不在规定范围内。不予处理。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 只关注支付渠道为klarna的订单
            OrderPayment orderPayment = orderPaymentService.getByOrder(needOrder.getId());
            PaymentChannel paymentChannel = orderPayment.paymentChannel();
            if (!PaymentChannel.isKlarnaChannel(paymentChannel)) {
                LOGGER.error("支付渠道不是klarna。不予处理。消息体:{}", messageBody);
                return Action.CommitMessage;
            }
            BaseKlarnaPaymentHandler paymentHandler = (BaseKlarnaPaymentHandler) paymentHandlerFactory.getPaymentHandler(paymentChannel);
            // 配置信息
            KlarnaPaymentConfiguration klarnaPaymentConfiguration = paymentHandler.getKlarnaPaymentConfiguration(order.getArea());
            // 获取订单详情
            String channelPaymentId = orderPayment.getChannelPaymentId();
            BaseKlarnaPaymentRequest orderDetailRequest = paymentHandler.getOrderDetailRequest(channelPaymentId, klarnaPaymentConfiguration);
            String orderDetailResult;
            try {
                LOGGER.info("[capture监控]klarna 查询交易订单详情 requestParam:{}", JSON.toJSONString(orderDetailRequest));
                orderDetailResult = orderDetailRequest.executeGet();
                LOGGER.info("[capture监控]klarna 查询交易订单详情 responseBody:{}", orderDetailResult);
            } catch (Exception e) {
                LOGGER.error(String.format("[capture监控]klarna 查询交易订单详情异常. 异常信息:{%s}, 交易流水号:{%s}, 订单号:{%s}", e.getMessage(), channelPaymentId, order.getOrderNumber()), e);
                FeiShuMessageUtil.storeGeneralMessage(String.format("capture监控查询订单详情错误 异常信息{%s}", e.getMessage()),
                        FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.ReconsumeLater;
            }

            int days;
            KlarnaOrderDetailResponse orderDetailResponse = KlarnaOrderDetailResponse.parse(orderDetailResult);
            if (!orderDetailResponse.isErrorResponse()) {
                // 以klarna响应最新capture过期时间为准
                LocalDateTime expiresAt = orderDetailResponse.getExpires_at();
                long expireDay = expiresAt.toLocalDate().toEpochDay();
                // 当前时间
                long now = LocalDate.now().toEpochDay();
                LOGGER.info("klarna_capture_now:{}", now);

                // 时间差
                days = (int) (expireDay - now);
                LOGGER.info("time difference:{}", days);

                // 如果最新capture过期时间大于第一次监控时间，则进入下一次监控且更新过期时间
                if (days > FIRST_NOTIFY_DAY) {
                    int delayTime = (days - FIRST_NOTIFY_DAY) * 3600 * 24;
                    orderMessageSendHelper.sendOrderPayedKlarnaCaptureMonitorMessage(needOrder, (long) delayTime);
                    orderPayment.setKlarnaCaptureTimeout(expiresAt);
                    orderPaymentService.updateById(orderPayment);
                    return Action.CommitMessage;
                }
            }

            // klarna capture到期时间通知
            LocalDate klarnaCaptureTimeout = orderPayment.getKlarnaCaptureTimeout().toLocalDate();
            LOGGER.info("klarna_capture_timeout:{}", klarnaCaptureTimeout);

            // 当前时间
            long now = LocalDate.now().toEpochDay();
            LOGGER.info("klarna_capture_now:{}", now);

            // 时间差
            days = (int) (klarnaCaptureTimeout.toEpochDay() - now);
            LOGGER.info("time difference:{}", days);

            // 给出飞书提醒
            if (MONITOR_DAYS.contains(days)) {
                FeiShuMessageUtil.storeGeneralMessage(this.getMessageContent(days, needOrder, orderPayment.getKlarnaCaptureTimeout()),
                        FeiShuGroupRobot.KlarnaPayment, FeiShuAtUser.ZM, FeiShuAtUser.LZJ);
            }

            // 如果是3天的话，则需要再次推送一次（延迟两天）
            if (FIRST_NOTIFY_DAY == days) {
                orderMessageSendHelper.sendOrderPayedKlarnaCaptureMonitorMessage(needOrder, 172800L);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("klarna capture monitor error. error_message:{%s}, messsage_body:{%s}", e.getMessage(), messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("capture监控错误 异常信息{%s}", e.getMessage()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        return Action.CommitMessage;
    }

    /**
     * 消息推送内容格式
     *
     * @param days
     * @param order
     * @param klarnaCaptureTimeout
     * @return
     */
    private String getMessageContent(Integer days, Order order, LocalDateTime klarnaCaptureTimeout) {
        return String.format(KlarnaConstant.KLARNAM_CPAUTE_MONITOR_MESSAGE_CONTENT, days, order.getOrderNumber(), order.getArea(),
                DATE_TIME_FORMATTER.format(klarnaCaptureTimeout.plusHours(8)));
    }
}
