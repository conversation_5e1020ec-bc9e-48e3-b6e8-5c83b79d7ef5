package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.dto.CloudCardBenefitNotifyMqDTO;
import com.insta360.store.business.cloud.enums.BenefitType;
import com.insta360.store.business.cloud.enums.CardBenefitCapacityType;
import com.insta360.store.business.cloud.enums.SkuSubscribeType;
import com.insta360.store.business.cloud.model.CloudStorageCardBenefit;
import com.insta360.store.business.cloud.model.CloudStorageCardBenefitDetail;
import com.insta360.store.business.cloud.service.CloudStorageCardBenefitDetailService;
import com.insta360.store.business.cloud.service.CloudStorageCardBenefitService;
import com.insta360.store.business.insurance.config.InsuranceCommonConfiguration;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 云存实体卡权益通知
 * @Date 2025/3/3
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class CloudCardBenefitNotifyMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudCardBenefitNotifyMqConsumer.class);

    @Autowired
    CloudStorageCardBenefitService cloudStorageCardBenefitService;

    @Autowired
    InsuranceCommonConfiguration insuranceCommonConfiguration;

    @Autowired
    CloudStorageCardBenefitDetailService cloudStorageCardBenefitDetailService;

    /**
     * 处理云存实体卡权益下发
     *
     * @param message        消息对象，包含消息体内容。
     * @param consumeContext 消费上下文，提供额外的消费环境信息。
     * @return 消息处理结果，指示消息是否处理成功或者需要重试。
     */
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cloud_entitlement_adhoc_notify)
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[云存实体卡]云存实体卡权益下发MQ消费开始,msgId:{}, msg:{}", message.getMsgID(), messageBody);

        try {
            CloudCardBenefitNotifyMqDTO cloudCardBenefitNotifyMqParam = JSON.parseObject(messageBody, CloudCardBenefitNotifyMqDTO.class);
            if (cloudCardBenefitNotifyMqParam == null) {
                LOGGER.error("[云存实体卡]参数解析失败,param为null,msg:{}", messageBody);
                return Action.CommitMessage;
            }

            String bizId = cloudCardBenefitNotifyMqParam.getBizId();
            if (StringUtil.isBlank(bizId)) {
                LOGGER.error("[云存实体卡]bizId为空,msg:{}", messageBody);
                return Action.CommitMessage;
            }

            // 判断bizID是否已经存在
            CloudStorageCardBenefit cloudStorageCardBenefit = cloudStorageCardBenefitService.getByBizId(bizId);
            if (Objects.nonNull(cloudStorageCardBenefit)) {
                LOGGER.error("[云存实体卡]bizId已存在,已经下发过权益，本次不下发,msg:{}", messageBody);
                return Action.CommitMessage;
            }

            Integer enable = cloudCardBenefitNotifyMqParam.getEnabled();
            if (enable == null || enable != 1) {
                LOGGER.error("[云存实体卡]enabled字段不为1,msg:{}", messageBody);
                return Action.CommitMessage;
            }

            Integer duration = cloudCardBenefitNotifyMqParam.getDuration();
            if (!SkuSubscribeType.YEARLY.getDuration().equals(duration)) {
                LOGGER.error("[云存实体卡]非年度订阅,拒绝接收,msg:{}", messageBody);
                return Action.CommitMessage;
            }

            Double total = cloudCardBenefitNotifyMqParam.getTotal();
            CardBenefitCapacityType cardBenefitCapacityType = CardBenefitCapacityType.matchType(total);
            if (cardBenefitCapacityType == null) {
                LOGGER.error("[云存实体卡]容量错误,msg:{}", messageBody);
                return Action.CommitMessage;
            }

            Long expiration = cloudCardBenefitNotifyMqParam.getExpiration();
            if (expiration == null || expiration < System.currentTimeMillis()) {
                LOGGER.error("[云存实体卡]过期时间错误,已过期或不存在,msg:{}", messageBody);
                return Action.CommitMessage;
            }

            // 记录实体卡数据
            CloudStorageCardBenefit storageCardBenefit = cloudCardBenefitNotifyMqParam.getPojoObject();
            cloudStorageCardBenefitService.save(storageCardBenefit);
            LOGGER.info("[云存实体卡]记录实体卡数据,msg:{},实体卡数据:{}", messageBody, storageCardBenefit);

            // 下发实体卡权益
            Map<BenefitType, List<String>> benefitTypeRegionMap = getTypeRegionMap();
            String region = cloudCardBenefitNotifyMqParam.getRegion();

            List<CloudStorageCardBenefitDetail> cloudStorageCardBenefitDetails =
                    cardBenefitCapacityType.getBenefitTypeList()
                            .stream().map(benefitType -> {
                                List<String> regions = benefitTypeRegionMap.get(benefitType);
                                if (regions.contains(region)) {
                                    return this.buildDetailParam(benefitType, storageCardBenefit);
                                }
                                return null;
                            }).filter(Objects::nonNull).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(cloudStorageCardBenefitDetails)) {
                LOGGER.info(String.format("[云存实体卡]权益数据为空,msg:%s,地区:%s,权益类型:%s", messageBody, region, cardBenefitCapacityType));
                return Action.CommitMessage;
            }

            // 记录对应的权益数据
            cloudStorageCardBenefitDetailService.saveBatch(cloudStorageCardBenefitDetails);
            LOGGER.info("[云存实体卡]云存实体卡权益下发MQ消费结束,msg:{},权益数据:{}", messageBody, cloudStorageCardBenefitDetails);
        } catch (Exception e) {
            LOGGER.error(String.format("[云存实体卡]云存实体卡权益下发MQ消费异常,消息体:%s", messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[云存实体卡]云存实体卡权益下发MQ消费异常，消息体:{%s}，异常信息:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            // 记录消费异常日志并发送飞书通知
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        // 消息处理成功
        return Action.CommitMessage;
    }

    /**
     * 获取权益上线地区
     *
     * @return
     */
    private Map<BenefitType, List<String>> getTypeRegionMap() {
        Map<BenefitType, List<String>> benefitTypeRegionMap = new HashMap<>(2);
        benefitTypeRegionMap.put(BenefitType.CARE, insuranceCommonConfiguration.getCareOnlineRegions());
        benefitTypeRegionMap.put(BenefitType.EXTEND, insuranceCommonConfiguration.getExtendOnlineRegions());
        return benefitTypeRegionMap;
    }

    /**
     * 构建参数
     *
     * @param benefitType
     * @param storageCardBenefit
     * @return
     */
    private CloudStorageCardBenefitDetail buildDetailParam(BenefitType benefitType, CloudStorageCardBenefit storageCardBenefit) {
        CloudStorageCardBenefitDetail cloudStorageCardBenefitDetail = new CloudStorageCardBenefitDetail();
        cloudStorageCardBenefitDetail.setBenefitId(storageCardBenefit.getId());
        cloudStorageCardBenefitDetail.setBenefitType(benefitType.getType());
        cloudStorageCardBenefitDetail.setUsed(false);
        return cloudStorageCardBenefitDetail;
    }
}
