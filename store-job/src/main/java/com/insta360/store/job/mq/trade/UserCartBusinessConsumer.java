package com.insta360.store.job.mq.trade;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.outgoing.mq.trade.dto.UserCartBusinessMessageDTO;
import com.insta360.store.business.trade.service.impl.helper.cart.UserCartHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Date 2023/5/15 18:02
 * @Description:
 * @Version 1.0
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class UserCartBusinessConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserCartBusinessConsumer.class);

    @Autowired
    UserCartHelper userCartHelper;

    @Override
    @MessageTcpDudep
    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_user_cart_business)
    public Action consume(Message message, ConsumeContext context) {
        LOGGER.info("购物车更新事件接收参数 message:{} content:{}", message, context);
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtil.isBlank(messageBody)) {
            LOGGER.info("购物车更新事件接收参数 message为空 message:" + message);
            return Action.CommitMessage;
        }
        UserCartBusinessMessageDTO userCartBusinessMessage = JSON.parseObject(messageBody, UserCartBusinessMessageDTO.class);
        userCartHelper.sendNewEmailMessage(userCartBusinessMessage.getUserCartEmailEnum(), userCartBusinessMessage.getStoreAccount(), userCartBusinessMessage.getUuid());
        return Action.CommitMessage;
    }
}
