package com.insta360.store.job.mq.invoice;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.integration.yipiaoyun.service.YiPiaoYunAutoInvoiceService;
import com.insta360.store.business.order.service.OrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;


/**
 * @description: 工单自动开具发票
 * @author: py
 * @create: 2022-05-19 15:05
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class RepairmentAutoInvoiceMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(RepairmentAutoInvoiceMqConsumer.class);

    @Autowired
    private YiPiaoYunAutoInvoiceService yiPiaoYunAutoInvoiceService;

    @Autowired
    OrderService orderService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.work_order_invoice_auto_issue, threadSize = 6)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String orderNumber = new String(message.getBody(), StandardCharsets.UTF_8);
        String storeOrderNumber = JSON.parseObject(orderNumber).getString("storeOrderNumber");
        LOGGER.info("工单自动开具发票 商城消费者收到消息开始消费:{}", storeOrderNumber);
        try {
            LOGGER.info("工单自动开具发票 开始消费 orderNumber:{}", storeOrderNumber);
            yiPiaoYunAutoInvoiceService.autoInvoiceHandle(orderService.getByOrderNumber(storeOrderNumber));
            return Action.CommitMessage;
        } catch (InstaException e) {
            LOGGER.error(String.format("工单自动开具发票 业务异常.... orderNumber: %s,  message: %s", JSON.toJSONString(message), e.getMessage()), e);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("工单自动开具发票 系统错误.... orderNumber: %s,  message: %s", JSON.toJSONString(message), e.getMessage()), e);
            return Action.CommitMessage;
        }
    }
}
