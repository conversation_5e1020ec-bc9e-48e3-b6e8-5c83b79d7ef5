package com.insta360.store.job.mq.insurance;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceServiceActivationHelper;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDeliveryUniqueCode;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * @Author: py
 * @Date: 2024/1/11
 * @Description: 随机购买增值服务激活延迟消息队列
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class InsuranceActivationDelayMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(InsuranceActivationDelayMqConsumer.class);

    @Autowired
    InsuranceServiceActivationHelper insuranceServiceActivationHelper;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_insurance_activation_check)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[随机购买兜底]成功接收到消息,参数:[%s]", messageBody));

        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);

            // 参数校验
            OrderDeliveryUniqueCode orderDeliveryUniqueCode = orderMessage.getOrderDeliveryUniqueCode();
            if (Objects.isNull(orderDeliveryUniqueCode)) {
                LOGGER.error("[随机购买兜底]失败,参数不存在");
                return Action.CommitMessage;
            }

            Integer orderId = orderDeliveryUniqueCode.getOrderId();
            Order order = orderService.getById(orderId);
            List<OrderItem> orderItems = orderItemService.getByOrder(orderId);
            LOGGER.info("[随机购买兜底]orderDeliveryUniqueCode:" + orderDeliveryUniqueCode);

            // 随机购买兜底激活
            insuranceServiceActivationHelper.activation(orderDeliveryUniqueCode, orderItems, order);

            // 随机未购买的发送推广邮件[非激活的相机]
            insuranceServiceActivationHelper.sendPromoteEmail(orderDeliveryUniqueCode, orderItems, order);

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[随机购买兜底]发生异常,消息体[%s],原因[%s]", messageBody, e.getMessage()));
            return Action.CommitMessage;
        }
    }
}
