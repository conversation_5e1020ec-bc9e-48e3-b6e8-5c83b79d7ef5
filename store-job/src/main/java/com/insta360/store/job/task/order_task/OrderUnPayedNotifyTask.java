package com.insta360.store.job.task.order_task;

import com.insta360.store.business.order.email.BaseOrderEmail;
import com.insta360.store.business.order.email.OrderUnPayedEmail;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.enums.PaymentSubscribeType;
import com.insta360.store.business.trade.model.StoreEmailSendRule;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2020/11/12
 * @Description: 定时任务-订单催付
 */
@Component
public class OrderUnPayedNotifyTask extends BaseCreateOrderAfterTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderUnPayedNotifyTask.class);

    private static final String EMAIL_SUFFIX = "insta360.com";

    private static final String TASK_TYPE = "un_payed_task";

    @XxlJob("orderUnPayedNotifyJobHandler")
    public ReturnT<String> execute(String param) {
        try {
            XxlJobLogger.log("订单催付_任务开始...");
            this.run();
            XxlJobLogger.log("订单催付_任务结束...");
        } catch (Exception e) {
            LOGGER.error("订单催付_任务失败", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    @Override
    public void run() {
        // un payed
        orderUnPayedNotify(getOrders(LocalDateTime.now(), OrderState.init));
    }

    /**
     * 1、订单未支付两小时后
     * 2、邮箱后缀不为insta360
     *
     * @param payedOrders
     */
    private void orderUnPayedNotify(List<Order> payedOrders) {
        payedOrders.stream()
                .filter(order -> LocalDateTime.now().isAfter(order.getCreateTime().plusHours(2)))
                .filter(this::checkEmailRule)
                .forEach(this::updateEmailCheckRule);
    }

    /**
     * 剔除后缀为insta360的邮箱
     */
    private Boolean checkEmailRule(Order order) {
        return !EMAIL_SUFFIX.equals(splitEmail(order.getContactEmail()));
    }

    /**
     * 拿到邮箱结尾标识
     */
    private String splitEmail(String email) {
        List<String> splitAfterEmail = Arrays.asList(email.split("@"));
        return splitAfterEmail.get(splitAfterEmail.size() - 1);
    }

    /**
     * 更新订单校验规则
     *
     * @param order
     */
    public void updateEmailCheckRule(Order order) {
        // 云服务续费订单不发送邮件
        if(order.getCloudSubscribeMark() && PaymentSubscribeType.RENEW_SUBSCRIBE.equals(order.paymentSubscribeType())) {
            return;
        }

        BaseOrderEmail orderEmail = emailFactory.getEmail(order, OrderUnPayedEmail.class);
        String templateName = orderEmail.getTemplateName();
        StoreEmailSendRule storeEmailSendRule = storeEmailSendRuleService.getTemplateSendRuleByOrderId(order.getId(), templateName);
        super.saveStoreEmailSendRuleSendEmail(order, storeEmailSendRule, getTaskType(), orderEmail);
    }

    @Override
    public String getTaskType() {
        return TASK_TYPE;
    }
}
