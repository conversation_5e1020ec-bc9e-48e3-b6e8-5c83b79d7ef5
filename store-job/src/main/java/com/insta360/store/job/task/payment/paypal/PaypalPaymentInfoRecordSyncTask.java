package com.insta360.store.job.task.payment.paypal;

import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.payment.service.impl.helper.PaypalTransactionRecordHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @description: paypal支付数据拉取
 * 美国的报表时当地时间的中午12点前生成 （西八区）
 * 香港地区也是当地时间的中午12点前生成 （东八区）
 * 举例 零时区 17号 12点执行
 * 西八区 此时17号04点 已经在16号的12点生成了15号的报告
 * 东八区 此时17号20点 已经在17号的12点生成了16号的报告
 * @author: py
 * @create: 2024-12-06 14:52
 */
@Component
public class PaypalPaymentInfoRecordSyncTask {

    public static final Logger LOGGER = LoggerFactory.getLogger(PaypalPaymentInfoRecordSyncTask.class);

    @Autowired
    PaypalTransactionRecordHelper paypalTransactionRecordHelper;

    @XxlJob("paypalPaymentInfoRecordSyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("同步paypal支付数据_任务开始...");
        LOGGER.info("同步paypal支付数据_任务开始...");

        try {
            // 获取支付报告数据
            paypalTransactionRecordHelper.listReportDetails();
        } catch (Exception e) {
            XxlJobLogger.log("同步paypal支付数据_出现异常...");
            LOGGER.error(String.format("[paypal数据拉取同步]同步paypal支付数据_出现异常.异常信息:%s", e.getMessage()), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("同步paypal支付数据_出现异常.异常信息:%s", e.getMessage()),
                    FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return ReturnT.FAIL;
        }

        XxlJobLogger.log("同步paypal支付数据_任务结束...");
        LOGGER.info("同步paypal支付数据_任务结束...");
        return ReturnT.SUCCESS;
    }
}
