package com.insta360.store.job.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.utils.MathUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2020-01-11
 * @Description: 定时任务-每日商城订单销售预报
 */
@Component
public class OrderProblemMonitorTask {

    /**
     * 需要发送【未支付订单过多提醒】的未支付订单阈值
     */
    private static final Double UNPAYMENT_RATE = 0.3;

    /**
     * 时间格式化
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");

    @Autowired
    OrderService orderService;

    @Autowired
    OrderPaymentService orderPaymentService;

    public static void main(String[] args) {
        LocalDateTime now = LocalDateTime.now();

        // 创建该小时的开始和结束时间，确保保留日期信息
        LocalDateTime startOfHour = now.withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfHour = now.withMinute(59).withSecond(59).withNano(999999999);

        // 输出结果
        System.out.println("当前时间: " + now);
        System.out.println("整点时间范围: " + startOfHour + " ～ " + endOfHour);
    }

    @XxlJob("orderProblemMonitorJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("每日商城订单销售预报_任务开始...");
        LocalDateTime now = LocalDateTime.now();

        // 创建该小时的开始和结束时间，确保保留日期信息
        LocalDateTime startOfHour = now.plusHours(-1).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfHour = now.plusHours(-1).withMinute(59).withSecond(59).withNano(999999999);

        StringBuilder sb = new StringBuilder();

        QueryWrapper<Order> qw = new QueryWrapper<>();
        qw.eq("is_repair", false);
        qw.between("create_time", startOfHour, endOfHour);

        List<Order> orders = orderService.list(qw);
        if (CollectionUtils.isEmpty(orders)) {
            FeiShuMessageUtil.storeGeneralMessage("订单总数量为0", FeiShuGroupRobot.StoreOrderNumber, FeiShuAtUser.TW);
            return ReturnT.SUCCESS;
        }

        sb.append(String.format("整点报时 - 当前是北京时间 %s\n", now.plusHours(8).format(DATE_TIME_FORMATTER)));
        String timeFormat = String.format("[%s] - [%s]", startOfHour.plusHours(8).format(DATE_TIME_FORMATTER), endOfHour.plusHours(8).format(DATE_TIME_FORMATTER));
        sb.append(String.format("为你推送 %s 订单数:[%d]\n", timeFormat, orders.size()));

        List<Order> cnOrders = orders.stream().filter(o -> InstaCountry.CN.equals(o.country())).collect(Collectors.toList());
        List<Order> usOrders = orders.stream().filter(o -> InstaCountry.US.equals(o.country())).collect(Collectors.toList());
        List<Order> unPayOrders = orders.stream().filter(o -> OrderState.init.equals(o.orderState())).collect(Collectors.toList());
        List<Order> payOrders = orders.stream().filter(o -> OrderState.payed.equals(o.orderState())).collect(Collectors.toList());
        List<Order> prepareOrders = orders.stream().filter(o -> OrderState.prepared.equals(o.orderState())).collect(Collectors.toList());
        List<Order> partDeliveryOrders = orders.stream().filter(o -> OrderState.part_delivery.equals(o.orderState())).collect(Collectors.toList());
        List<Order> pendingOrders = orders.stream().filter(o -> OrderState.payment_pending.equals(o.orderState())).collect(Collectors.toList());
        List<Order> canceledOrders = orders.stream().filter(o -> OrderState.canceled.equals(o.orderState())).collect(Collectors.toList());

        removeMarketAndDevReceive(payOrders);
        removeMarketAndDevReceive(prepareOrders);
        sb.append("已配货订单：" + prepareOrders.size() + "(" + MathUtil.roundingOff((double) prepareOrders.size() / orders.size() * 100.0) + "%)" + "\n");
        sb.append("已支付订单：" + payOrders.size() + "(" + MathUtil.roundingOff((double) payOrders.size() / orders.size() * 100.0) + "%)" + "\n");
        sb.append("未支付订单：" + unPayOrders.size() + "(" + MathUtil.roundingOff((double) unPayOrders.size() / orders.size() * 100.0) + "%)" + "\n");
        sb.append("部分发货订单：" + partDeliveryOrders.size() + "(" + MathUtil.roundingOff((double) partDeliveryOrders.size() / orders.size() * 100.0) + "%)" + "\n");
        sb.append("支付处理中订单：" + pendingOrders.size() + "(" + MathUtil.roundingOff((double) pendingOrders.size() / orders.size() * 100.0) + "%)" + "\n");
        sb.append("已取消订单：" + canceledOrders.size() + "(" + MathUtil.roundingOff((double) canceledOrders.size() / orders.size() * 100.0) + "%)" + "\n");

        List<Order> cnPayOrders = payOrders.stream().filter(o -> InstaCountry.CN.equals(o.country())).collect(Collectors.toList());
        List<Order> usPayOrders = payOrders.stream().filter(o -> InstaCountry.US.equals(o.country())).collect(Collectors.toList());

        sb.append("国内订单 x" + cnOrders.size() + "，已支付 x" + cnPayOrders.size() + "\n");
        sb.append("美国订单 x" + usOrders.size() + "，已支付 x" + usPayOrders.size() + "\n");
        sb.append("其他国家/地区订单 x" + (orders.size() - cnOrders.size() - usOrders.size()) + "，已支付 x" + (payOrders.size() - cnPayOrders.size() - usPayOrders.size()) + "\n");

        int limitSize = 10;
        if (orders.size() < limitSize) {
            sb.append("警告⚠️ 订单数量过少，请及时处理。\n");
        }
        FeiShuMessageUtil.storeGeneralMessage(sb.toString(), FeiShuGroupRobot.StoreOrderNumber, FeiShuAtUser.ALL);

        // 未支付订单数阈值超过30%就另外发送一条飞书推送
        if (unPayOrders.size() > orders.size() * UNPAYMENT_RATE) {
            String msg = nonPayExceedLimitMsg(orders, unPayOrders, now);
            FeiShuMessageUtil.storeGeneralMessage(msg, FeiShuGroupRobot.StoreOrderNumber, FeiShuAtUser.LCY, FeiShuAtUser.LCN, FeiShuAtUser.ZXT);
        }

        XxlJobLogger.log("每日商城订单销售预报_任务结束...");
        return ReturnT.SUCCESS;
    }

    /**
     * 【未支付订单过多提醒】飞书消息推送内容
     *
     * @param orders
     * @param unPayOrders
     * @param now
     * @return
     */
    private String nonPayExceedLimitMsg(List<Order> orders, List<Order> unPayOrders, LocalDateTime now) {
        StringBuilder msg = new StringBuilder();
        msg.append("【未支付订单过多提醒】" + "\n");
        msg.append("整点报时 - 当前是北京时间" + (now.getHour() + 8) % 24 + "点" + now.getMinute() + "分\n");
        msg.append("为你报送最近一小时订单数：" + orders.size() + "\n");
        msg.append("未支付订单：" + unPayOrders.size() + "(" + MathUtil.roundingOff((double) unPayOrders.size() / orders.size() * 100.0) + "%)" + "\n");
        return msg.toString();
    }

    /**
     * 剔除市场领用/研发领用的订单
     *
     * @param orders
     */
    private void removeMarketAndDevReceive(List<Order> orders) {
        Iterator<Order> iterator = orders.iterator();
        while (iterator.hasNext()) {
            Order order = iterator.next();
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (PaymentChannel.insta360.equals(orderPayment.paymentChannel())
                    || PaymentChannel.insta360_develop.equals(orderPayment.paymentChannel())) {
                iterator.remove();
            }
        }
    }
}
