package com.insta360.store.job.mq.insurance;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceServiceActivationHelper;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDeliveryUniqueCode;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderDeliveryUniqueCodeService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.insurance.dto.InsuranceDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Author: py
 * @Date: 2024/1/11
 * @Description: app端相机激活 1、通知商城绑定随机购买的增值服务  2、没有购买增值服务的发送推广邮件
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class CameraActivationNotifyMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(CameraActivationNotifyMqConsumer.class);

    @Autowired
    OrderDeliveryUniqueCodeService orderDeliveryUniqueCodeService;

    @Autowired
    InsuranceServiceActivationHelper insuranceServiceActivationHelper;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_insurance_activation_by_serial_activation)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[app相机激活通知]接收到app相机激活的通知.消息体[%s]", messageBody));

        try {
            InsuranceDTO insuranceParam = JSON.parseObject(messageBody, InsuranceDTO.class);

            // 判断是否是商城购买的相机
            String deviceSerial = insuranceParam.getSerial();
            OrderDeliveryUniqueCode orderDeliveryUniqueCode = orderDeliveryUniqueCodeService.getByUniqueCode(deviceSerial);
            if (Objects.isNull(orderDeliveryUniqueCode)) {
                LOGGER.info(String.format("[app相机激活通知]并非商城购买的相机,跳过流程,相机序列号[%s]", deviceSerial));
                return Action.CommitMessage;
            }

            // 随机购买相机激活时间
            LocalDateTime cameraActivationTime = insuranceParam.getCreateTime();
            if (Objects.isNull(cameraActivationTime)) {
                cameraActivationTime = LocalDateTime.now();
            }
            orderDeliveryUniqueCode.setCreateTime(cameraActivationTime);

            Integer orderId = orderDeliveryUniqueCode.getOrderId();
            Order order = orderService.getById(orderId);
            List<OrderItem> orderItems = orderItemService.getByOrder(orderId);
            LOGGER.info("[app相机激活通知]orderDeliveryUniqueCode:" + orderDeliveryUniqueCode);

            // 随机购买的进行激活
            insuranceServiceActivationHelper.activation(orderDeliveryUniqueCode, orderItems, order);

            // 随机未购买的发送推广邮件
            insuranceServiceActivationHelper.sendPromoteEmail(orderDeliveryUniqueCode, orderItems, order);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[app相机激活通知]发生异常,消息体[%s],原因[%s]", messageBody, e.getMessage()));
            return Action.CommitMessage;
        }
    }
}
