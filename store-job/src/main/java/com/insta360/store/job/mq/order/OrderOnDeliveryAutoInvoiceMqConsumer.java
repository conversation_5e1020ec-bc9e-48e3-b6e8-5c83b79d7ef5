package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.integration.yipiaoyun.service.YiPiaoYunAutoInvoiceService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: wbt
 * @Date: 2022/05/30
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderOnDeliveryAutoInvoiceMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderOnDeliveryAutoInvoiceMqConsumer.class);

    @Autowired
    YiPiaoYunAutoInvoiceService yiPiaoYunAutoInvoiceService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_on_delivery_auto_invoice)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();
            if (order == null) {
                LOGGER.info("[商城大陆开票]订单自动开票事件消费失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // auto invoice
            yiPiaoYunAutoInvoiceService.autoInvoiceHandle(order);

            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("[商城大陆开票]订单自动开票消费异常。消息体:{}. 原因:{}", messageBody, e.getMessage()), e);
            return Action.CommitMessage;
        }
    }
}
