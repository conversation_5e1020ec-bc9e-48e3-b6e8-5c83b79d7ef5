package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.forter.enums.ForterSyncOrderStatusType;
import com.insta360.store.business.forter.support.factory.ForterSyncFactory;
import com.insta360.store.business.forter.support.handler.ForterSyncService;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderPayment;
import com.insta360.store.business.order.service.OrderPaymentService;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.payment.bo.OrderSyncForterBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @Author: wbt
 * @Date: 2022/05/05
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderOnDeliverySyncForterConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderOnDeliverySyncForterConsumer.class);

    @Autowired
    ForterSyncFactory forterSyncFactory;

    @Autowired
    OrderPaymentService orderPaymentService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_on_delivery_sync_forter)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        if (StringUtil.isBlank(messageBody)) {
            LOGGER.error("order sync forter error. order info missed.");
        }

        try {
            OrderMessageDTO orderMessage = JSONObject.parseObject(messageBody, OrderMessageDTO.class);
            Order order = orderMessage.getOrder();

            // 订单支付信息
            OrderPayment orderPayment = orderPaymentService.getByOrder(order.getId());
            if (orderPayment == null) {
                LOGGER.error("order payment not found. order:{}", order);
                return Action.CommitMessage;
            }

            // 是否是信用卡支付
            boolean creditCardChannel = PaymentChannel.isCreditCardChannel(orderPayment.paymentChannel());

            // 参数构造
            OrderSyncForterBO orderSyncForter = new OrderSyncForterBO();
            orderSyncForter.setOrder(order);
            orderSyncForter.setOrderPayment(orderPayment);
            orderSyncForter.setForterSyncOrderStatusType(creditCardChannel ? ForterSyncOrderStatusType.ORDER_ON_DELIVERY : ForterSyncOrderStatusType.ORDER_CANCEL);
            if (!creditCardChannel) {
                orderSyncForter.setUserCancel(false);
            }

            // sync forter
            ForterSyncService forterSyncService = forterSyncFactory.getForterSyncHandle(orderPayment.paymentChannel());
            Boolean syncResult = forterSyncService.syncForter(orderSyncForter);

            // 同步失败则重试
            return syncResult ? Action.CommitMessage : Action.ReconsumeLater;
        } catch (Exception e) {
            // 其它未知异常暂时先commit，发现了根据日志定位
            LOGGER.error("touch off other ex...", e);
            return Action.CommitMessage;
        }
    }
}
