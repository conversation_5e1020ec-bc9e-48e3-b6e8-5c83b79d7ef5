package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.dto.CloudSubscribeRenewMqDTO;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.enums.SubscribeSubStatus;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.bo.OrderSheet;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.impl.aop.annotation.LogOrderStateChange;
import com.insta360.store.business.order.service.impl.helper.OrderHelper;
import com.insta360.store.business.trade.service.TradeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 处理商城云服务订阅续费扣款的消息消费者
 * @Date 2024/8/9
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class CloudProductSubscribeRenewConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudProductSubscribeRenewConsumer.class);

    @Autowired
    OrderHelper orderHelper;

    @Autowired
    TradeService tradeService;

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_cloud_product_subscribe_renew_deduction)
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 接收到的消息内容
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[商城云服务订阅续费]已接收到消息... msg:{}", messageBody);
        try {
            // 将消息体转换为CloudSubscribeRenewMqDTO对象
            CloudSubscribeRenewMqDTO cloudSubscribeRenewMqDto = JSON.parseObject(messageBody, CloudSubscribeRenewMqDTO.class);
            // 检查转换结果是否为空或关键参数是否为空
            if (Objects.isNull(cloudSubscribeRenewMqDto) || Objects.isNull(cloudSubscribeRenewMqDto.getCloudStorageSubscribe())) {
                LOGGER.info("[商城云服务订阅续费]消息关键参数为空. msg:{}", messageBody);
                return Action.CommitMessage;
            }

            // 获取订阅记录详情
            CloudStorageSubscribe cloudStorageSubscribe = cloudSubscribeRenewMqDto.getCloudStorageSubscribe();
            // 重新查询一次订阅记录，确保状态未变更
            CloudStorageSubscribe subscribeRecord = cloudStorageSubscribeService.getById(cloudStorageSubscribe.getId());

            // 检查订阅状态和订阅子状态是否符合续费条件
            if (!subscribeRecord.isSubscribing() || !SubscribeSubStatus.RENEW_DEDUCTION_HANDLE_ING.getCode().equals(subscribeRecord.getSubscribeSubState())) {
                LOGGER.info("[商城云服务订阅续费]续费订阅母单状态已变更. subscribeRecord:{}", JSON.toJSONString(subscribeRecord));
                // 发送飞书消息通知异常
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅续费]云服务续费订单创建失败,续费订阅母单状态已变更. 原始订单号: [%s]", subscribeRecord.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            // 查询当前用户最新的那笔生效的订阅记录
            CloudStorageSubscribe lastSubscribeRecord = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(cloudStorageSubscribe.getInstaAccount());
            // 检查续费订阅母单与用户当前最新订阅记录是否一致
            if (!subscribeRecord.getId().equals(lastSubscribeRecord.getId()) && !subscribeRecord.getSubscribeSubState().equals(lastSubscribeRecord.getSubscribeSubState())) {
                LOGGER.info("[商城云服务订阅续费]续费订阅母单与用户当前最新订阅记录不一致. subscribeRecord:{}, lastSubscribeRecord: {}", JSON.toJSONString(subscribeRecord), JSON.toJSONString(lastSubscribeRecord));
                // 发送飞书消息通知异常
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅续费]云服务续费订单创建失败,续费订阅母单与用户当前最新订阅记录不一致. 原始订单号: [%s]", subscribeRecord.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            // 构建续费新单的订单详情
            OrderSheet orderSheet = orderHelper.createCloudSubscribeOrderSheet(lastSubscribeRecord, ServiceScenesType.RENEW);
            // 检查订单详情构建结果
            if (Objects.isNull(orderSheet)) {
                LOGGER.info("[商城云服务订阅续费]续费新单创建所需参数构建失败. lastSubscribeRecord:{}", JSON.toJSONString(lastSubscribeRecord));
                // 发送飞书消息通知异常
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅续费]云服务续费订单创建失败,续费新单创建所需参数构建失败. 原始订单号: [%s]", subscribeRecord.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                return Action.CommitMessage;
            }

            // 创建续费订单
            Order order = ApplicationContextHolder.getApplicationContext().getBean(CloudProductSubscribeRenewConsumer.class).createRenewOrder(orderSheet);
            if (Objects.isNull(order)) {
                LOGGER.info("[商城云服务订阅续费]续费订单创建失败. lastSubscribeRecord:{}, orderSheet:{}", JSON.toJSONString(lastSubscribeRecord), JSON.toJSONString(orderSheet));
                // 发送飞书消息通知异常
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅续费]云服务续费订单创建失败,续费订单创建失败. 原始订单号: [%s]", subscribeRecord.getOrderNumber()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        } catch (Exception e) {
            // 异常处理：记录日志并发送飞书通知
            LOGGER.error(String.format("[商城云服务订阅续费]云服务续费订单创建异常. 消息体:%s", messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务订阅续费]云服务续费订单创建异常. 消息体:%s, 异常信息:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            // 根据异常类型决定是否需要重试
            if (e instanceof InstaException) {
                return Action.CommitMessage;
            }
            return Action.ReconsumeLater;
        }

        // 处理完成，提交消息
        return Action.CommitMessage;
    }

    /**
     * 创建续费订单
     *
     * @param orderSheet
     * @return
     */
    @LogOrderStateChange
    public Order createRenewOrder(OrderSheet orderSheet) {
        // 创建云服务续费订单
        return tradeService.createTrade(orderSheet);
    }
}