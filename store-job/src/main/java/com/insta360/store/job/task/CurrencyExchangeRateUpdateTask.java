package com.insta360.store.job.task;

import com.insta360.store.business.meta.model.MetaCurrency;
import com.insta360.store.business.meta.service.MetaCurrencyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/3/4
 * @Description: 定时任务-国际货币汇率拉取更新
 */
@Component
public class CurrencyExchangeRateUpdateTask {

    public static final Logger logger = LoggerFactory.getLogger(CurrencyExchangeRateUpdateTask.class);

    @Autowired
    MetaCurrencyService metaCurrencyService;

    @XxlJob("currencyExchangeRateUpdateJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("国际货币汇率拉取更新_任务开始...");
        List<MetaCurrency> currencyList = metaCurrencyService.getCurrencies();
        if (CollectionUtils.isEmpty(currencyList)) {
            return ReturnT.SUCCESS;
        }

        for (MetaCurrency metaCurrency : currencyList) {
            try {
                metaCurrencyService.syncCurrencyFromInternet(metaCurrency);
            } catch (Exception e) {
                XxlJobLogger.log("币种:{} 汇率更新失败... error:{}", metaCurrency.getName(), ExceptionUtils.getStackTrace(e));
            }
        }
        XxlJobLogger.log("国际货币汇率拉取更新_任务结束...");
        return ReturnT.SUCCESS;
    }
}
