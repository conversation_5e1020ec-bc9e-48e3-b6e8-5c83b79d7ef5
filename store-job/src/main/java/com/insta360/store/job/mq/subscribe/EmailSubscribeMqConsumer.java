package com.insta360.store.job.mq.subscribe;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.discount.service.impl.helper.other.EmailSubscribeGiftCardHelper;
import com.insta360.store.business.outgoing.mq.subscribe.dto.EmailSubscribeDTO;
import com.insta360.store.business.user.email.ActivitySubscribeEmail;
import com.insta360.store.business.user.email.BaseUserEmail;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.model.EmailSubscribeActivityPage;
import com.insta360.store.business.user.service.EmailSubscribeActivityPageService;
import com.insta360.store.business.user.service.EmailSubscribeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * @Author: wkx
 * @Date: 2022/4/28
 * @Description: 邮箱订阅消费
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class EmailSubscribeMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(EmailSubscribeMqConsumer.class);

    @Autowired
    EmailSubscribeGiftCardHelper subscribeGiftCardHelper;

    @Autowired
    EmailSubscribeService emailSubscribeService;

    @Autowired
    EmailSubscribeActivityPageService emailSubscribeActivityPageService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_email_subscribe_notify)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        EmailSubscribeDTO emailSubscribeDTO = JSON.parseObject(messageBody, EmailSubscribeDTO.class);

        EmailSubscribe emailSubscribe = emailSubscribeService.getById(emailSubscribeDTO.getSubscribeId());
        if (emailSubscribe == null) {
            LOGGER.info("邮箱订阅消费失败:" + emailSubscribeDTO.getSubscribeId());
            return Action.CommitMessage;
        }
        String activityPage = emailSubscribe.getActivityPage();
        LOGGER.info("邮箱订阅消费：" + emailSubscribe.getEmail() + ":" + activityPage);
        EmailSubscribeActivityPage emailSubscribeActivityPage = emailSubscribeActivityPageService.getByActivityName(activityPage);
        if (emailSubscribeActivityPage == null || StringUtil.isBlank(emailSubscribeActivityPage.getEmailKey())) {
            // 暂无默认邮件模板（不排除以后有）
            LOGGER.info("活动订阅模版解析失败/无默认邮件模板:" + emailSubscribeDTO.getSubscribeId() + emailSubscribeActivityPage);
            return Action.CommitMessage;
        }
        Class<? extends BaseUserEmail> emailTemplate = ActivitySubscribeEmail.class;
        subscribeGiftCardHelper.doExecute(emailSubscribe, LocalDateTime.now(), emailTemplate, emailSubscribeActivityPage);
        return Action.CommitMessage;
    }
}
