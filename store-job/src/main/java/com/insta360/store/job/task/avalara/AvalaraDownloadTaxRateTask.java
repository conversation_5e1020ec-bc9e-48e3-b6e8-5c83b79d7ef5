package com.insta360.store.job.task.avalara;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.integration.avalara.constant.TaxConstant;
import com.insta360.store.business.integration.avalara.enums.StoreTransactionType;
import com.insta360.store.business.integration.avalara.service.factory.TaxSimpleFactory;
import com.insta360.store.business.integration.avalara.service.hadler.TaxService;
import com.insta360.store.business.trade.bo.CalculateTaxBO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/06/28
 * @Description:
 */
@Component
public class AvalaraDownloadTaxRateTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(AvalaraDownloadTaxRateTask.class);

    /**
     * 参与获取离线税率的国家地区
     */
    private static final List<InstaCountry> COUNTRIES = Arrays.asList(InstaCountry.US);

    @Autowired
    TaxSimpleFactory taxSimpleFactory;

    /**
     * avalara离线税率下载
     *
     * @return
     */
    @XxlJob("avalaraDownloadTaxRateTask")
    @Transactional(rollbackFor = RuntimeException.class)
    public ReturnT<String> execut(String param) {
        XxlJobLogger.log("Avalara离线税率下载任务开始...");
        for (InstaCountry country : COUNTRIES) {
            LOGGER.info("触发{}国家地区的离线税率获取。开始。", country.name());
            // 下载时间
            String downloadDate = TaxConstant.DATE_TIME_FORMATTER.format(LocalDateTime.now());

            CalculateTaxBO calculateTax = new CalculateTaxBO();
            calculateTax.setOfflineRateCountry(country);
            calculateTax.setDownloadDate(downloadDate);

            TaxService taxService = taxSimpleFactory.getTaxService(StoreTransactionType.DOWNLOAD_TAX_RATE, country);
            taxService.executeTransaction(calculateTax);
            LOGGER.info("触发{}国家地区的离线税率获取。结束。", country.name());
        }
        return ReturnT.SUCCESS;
    }
}
