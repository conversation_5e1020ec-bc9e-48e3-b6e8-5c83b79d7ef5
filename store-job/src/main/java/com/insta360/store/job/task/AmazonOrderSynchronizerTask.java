package com.insta360.store.job.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonservices.mws.orders._2013_09_01.MarketplaceWebServiceOrdersException;
import com.insta360.store.business.integration.amazon.bo.AmazonOrderResult;
import com.insta360.store.business.integration.amazon.helper.AmazonOrderLoader;
import com.insta360.store.business.integration.amazon.lib.MwsClient;
import com.insta360.store.business.integration.amazon.model.AmazonConfig;
import com.insta360.store.business.integration.amazon.model.AmazonOrder;
import com.insta360.store.business.integration.amazon.service.AmazonConfigService;
import com.insta360.store.business.integration.amazon.service.AmazonOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Author: wbt
 * @Date: 2020/05/09
 * @Description: 同步亚马逊订单到管易
 */
@Component
public class AmazonOrderSynchronizerTask {

    public static final Logger logger = LoggerFactory.getLogger(AmazonOrderSynchronizerTask.class);

    public static final Long AMAZON_API_LIMIT_WAIT = 90 * 1000L;

    public static final Long AMAZON_API_LIMIT_ERROR_WAIT = 2 * 60 * 1000L;

    public static final LocalDateTime DEFAULT_FROM_TIME = LocalDateTime.parse("2018-07-16T11:43:00");

    @Autowired
    AmazonOrderLoader orderLoader;

    @Autowired
    AmazonOrderService amazonOrderService;

    @Autowired
    AmazonConfigService amazonConfigService;

    private Set<String> runRecord = new HashSet<>();

    @XxlJob("amazonOrderSyncJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("同步亚马逊订单到管易、商城_任务开始...");
        List<String> zoneKeys = amazonConfigService.getKeys();
        if (CollectionUtils.isEmpty(zoneKeys)) {
            return ReturnT.SUCCESS;
        }
        for (String zoneKey : zoneKeys) {
            LocalDateTime fromDateTime = getFromTime(zoneKey);
            try {
                doSync(zoneKey, fromDateTime);
                TimeUnit.MILLISECONDS.sleep(AMAZON_API_LIMIT_WAIT);
            } catch (Exception e) {
                XxlJobLogger.log("亚马逊订单同步异常... zoneKey:{},error:{}", zoneKey, ExceptionUtils.getStackTrace(e));
            }
            runRecord.add(zoneKey);
        }
        XxlJobLogger.log("同步亚马逊订单到管易、商城_任务结束...");
        return ReturnT.SUCCESS;
    }

    private void doSync(String zoneKey, LocalDateTime fromDateTime) throws InterruptedException {
        if (logger.isInfoEnabled()) {
            logger.info(String.format("Start sync amazon orders... [%s, from %s]", zoneKey, fromDateTime));
        }

        AmazonConfig amazonGySyncConfig = amazonConfigService.getByKey(zoneKey);
        MwsClient mwsClient = amazonGySyncConfig.getMwsClient();

        AmazonOrderResult amazonOrderResult = null;
        String nextToken = null;
        while (amazonOrderResult == null || nextToken != null) {
            if (logger.isInfoEnabled()) {
                logger.info(String.format("Start sync amazon orders page... [%s, from %s, token %s]", zoneKey, fromDateTime, nextToken));
            }

            amazonOrderResult = listOrders(mwsClient, fromDateTime, nextToken);
            if (amazonOrderResult != null) {
                nextToken = amazonOrderResult.getNextToken();
                JSONArray orderArray = amazonOrderResult.getOrders();
                if (orderArray != null) {
                    handleOrders(orderArray, zoneKey);
                }
            }
            TimeUnit.MILLISECONDS.sleep(AMAZON_API_LIMIT_WAIT);
        }
    }

    private AmazonOrderResult listOrders(MwsClient mwsClient, LocalDateTime fromDateTime, String nextToken) throws InterruptedException {
        AmazonOrderResult amazonOrderResult = null;
        try {
            if (nextToken != null) {
                amazonOrderResult = orderLoader.listOrdersByNextToken(mwsClient, nextToken);
            } else {
                amazonOrderResult = orderLoader.listOrders(mwsClient, fromDateTime);
            }
        } catch (MarketplaceWebServiceOrdersException e) {
            // API访问限制报错
            e.printStackTrace();
            TimeUnit.MILLISECONDS.sleep(AMAZON_API_LIMIT_ERROR_WAIT);
        }
        return amazonOrderResult;
    }

    private LocalDateTime getFromTime(String zoneKey) {
        if (!runRecord.contains(zoneKey)) {
            return DEFAULT_FROM_TIME;
        }
        AmazonOrder amazonOrder = amazonOrderService.getByZone(zoneKey);

        if (amazonOrder == null || amazonOrder.getPurchaseTime() == null) {
            return DEFAULT_FROM_TIME;
        } else {
            LocalDateTime purchaseTime = amazonOrder.getPurchaseTime();
            return purchaseTime.plusDays(-30);
        }
    }

    private void handleOrders(JSONArray orderArray, String zoneKey) throws InterruptedException {
        for (int i = 0; i < orderArray.size(); i++) {
            JSONObject orderJson = orderArray.getJSONObject(i);
            logger.info("amazon orderJson result:{}", orderJson.toJSONString());
            boolean needSync = amazonOrderService.checkSyncEnabled(orderJson, zoneKey);
            if (!needSync) {
                continue;
            }
            syncOrder(orderJson, zoneKey);
            TimeUnit.MILLISECONDS.sleep(AMAZON_API_LIMIT_WAIT);
        }
    }

    private void syncOrder(JSONObject orderJson, String zoneKey) throws InterruptedException {
        String orderNumber = orderJson.getString("AmazonOrderId");

        if (logger.isInfoEnabled()) {
            logger.info(String.format("Start sync single amazon order... [%s, %s]", zoneKey, orderNumber));
        }

        try {
            amazonOrderService.syncOrder(orderJson, zoneKey);
        } catch (MarketplaceWebServiceOrdersException e) {
            if (logger.isErrorEnabled()) {
                logger.error(String.format("Error on sync single amazon order... [%s, %s]", zoneKey, orderNumber));
            }
            // API访问限制报错
            System.out.println(e.getMessage());
            TimeUnit.MILLISECONDS.sleep(AMAZON_API_LIMIT_ERROR_WAIT);
        } catch (Exception e) {
            logger.info("Error on sync single am:{} error:{}", orderNumber, e.getMessage());
        }

        if (logger.isDebugEnabled()) {
            logger.debug("Sync Amazon Order: " + orderNumber);
        }
    }
}
