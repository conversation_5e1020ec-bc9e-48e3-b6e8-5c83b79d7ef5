package com.insta360.store.job.mq.review;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderAdminRemark;
import com.insta360.store.business.order.service.OrderAdminRemarkService;
import com.insta360.store.business.outgoing.mq.review.dto.ReviewMessageDTO;
import com.insta360.store.business.review.email.BaseReviewEmail;
import com.insta360.store.business.review.email.ReviewEmail;
import com.insta360.store.business.review.email.ReviewEmailFactory;
import com.insta360.store.business.review.model.ReviewEmailSendRecord;
import com.insta360.store.business.review.service.ReviewEmailSendRecordService;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaOrderService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2022/07/07
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class ReviewEmailSendMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReviewEmailSendMqConsumer.class);

    @Autowired
    RmaOrderService rmaOrderService;

    @Autowired
    ReviewEmailFactory reviewEmailFactory;

    @Autowired
    OrderAdminRemarkService orderAdminRemarkService;

    @Autowired
    ReviewEmailSendRecordService reviewEmailSendRecordService;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_success_send_review_email)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("进入评论邮件发送校验逻辑。消息体：{}", messageBody);

        try {
            ReviewMessageDTO reviewMessage = JSON.parseObject(messageBody, ReviewMessageDTO.class);
            Order order = reviewMessage.getOrder();
            if (order == null) {
                LOGGER.error("订单评论邮件发送事件消费失败。订单不存在。消息体:{}", messageBody);
                return Action.CommitMessage;
            }

            // 发送评论邮件
            this.sendReviewEmail(order);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error(String.format("评论邮件发送事件消费失败。消息体: %s. 原因: %s", messageBody, e.getMessage()), e);
            return Action.CommitMessage;
        }
    }

    /**
     * 评论邮件发送策略：
     * 1、剔除工单订单
     * 2、剔除包含备注的订单
     * 3、剔除存在售后行为的订单（只要存在售后记录就剔除）
     * 4、剔除小程序的订单
     *
     * @param order
     */
    private void sendReviewEmail(Order order) {
        // 工单订单不发 && 小程序订单不发
        if (order.isRepairOrder() || order.isWeappOrder()) {
            return;
        }

        // 包含备注的订单不发
        OrderAdminRemark orderAdminRemark = orderAdminRemarkService.getByOrder(order.getId());
        if (orderAdminRemark != null) {
            return;
        }

        // 存在售后行为的订单不发（只要存在售后记录）
        List<RmaOrder> rmaOrders = rmaOrderService.listByOrderId(order.getId());
        if (CollectionUtils.isNotEmpty(rmaOrders)) {
            return;
        }

        // 一个订单只发送一次
        ReviewEmailSendRecord reviewEmailSendRecord = reviewEmailSendRecordService.getReviewEmailSendRecord(order.getOrderNumber(), order.getContactEmail());
        if (reviewEmailSendRecord != null) {
            return;
        }

        // 邮件发送
        this.doSendEmail(order);
    }

    /**
     * 邮件发送
     *
     * @param order
     */
    private void doSendEmail(Order order) {
        BaseReviewEmail email = reviewEmailFactory.getEmail(order, ReviewEmail.class);
        email.doSend(order.getContactEmail());

        // 保存评论邮箱的发送记录
        this.saveReviewEmailSendRecord(order);
    }

    /**
     * 保存评论邮箱的发送记录
     *
     * @param order
     */
    private void saveReviewEmailSendRecord(Order order) {
        ReviewEmailSendRecord reviewEmailSendRecord = new ReviewEmailSendRecord();
        reviewEmailSendRecord.setSendTime(LocalDateTime.now());
        reviewEmailSendRecord.setEmail(order.getContactEmail());
        reviewEmailSendRecord.setOrderNumber(order.getOrderNumber());
        reviewEmailSendRecord.setEmailKey(ReviewEmail.REVIEW_EMAIL_TEMPLATE_NAME);
        reviewEmailSendRecordService.save(reviewEmailSendRecord);
    }
}
