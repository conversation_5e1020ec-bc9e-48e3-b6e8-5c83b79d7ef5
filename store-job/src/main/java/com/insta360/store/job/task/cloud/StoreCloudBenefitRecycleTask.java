package com.insta360.store.job.task.cloud;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.insta360.store.business.cloud.bo.StoreCloudBusinessTypeResultBO;
import com.insta360.store.business.cloud.enums.BenefitCapacityType;
import com.insta360.store.business.cloud.enums.BenefitStatus;
import com.insta360.store.business.cloud.enums.ServiceScenesType;
import com.insta360.store.business.cloud.enums.SkuSubscribeType;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitService;
import com.insta360.store.business.cloud.service.StoreBenefitSubscribeService;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitContext;
import com.insta360.store.business.cloud.service.impl.factory.StoreBenefitFactory;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 商城云服务特殊权益过期回收定时任务
 * @Date 2024/5/16
 */
@Component
public class StoreCloudBenefitRecycleTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreCloudBenefitRecycleTask.class);

    @Autowired
    private CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    private StoreBenefitFactory storeBenefitFactory;

    @XxlJob("storeCloudBenefitRecycleJobHandler")
    public ReturnT<String> execute(String param) {
        XxlJobLogger.log("开始本次定时任务处理。");
        // 查询当天截止当前时间为止，有哪些权益已过期
        List<CloudStorageStoreBenefit> storeBenefitList = cloudStorageStoreBenefitService.listExpiredStoreBenefit();
        if (CollectionUtils.isEmpty(storeBenefitList)) {
            XxlJobLogger.log("未扫描出即将过期的用户权益记录。");
            return ReturnT.SUCCESS;
        }

        LOGGER.info("[商城云服务权益]已扫描出权益过期且未回收的记录数:{}", storeBenefitList.size());

        for (CloudStorageStoreBenefit storeBenefit : storeBenefitList) {
            try {
                LOGGER.info("[商城云服务权益]权益回收开始,storeBenefit:{}", JSON.toJSONString(storeBenefit));
                this.recycling(storeBenefit);
                LOGGER.info("[商城云服务权益]权益回收结束,storeBenefit:{}", JSON.toJSONString(storeBenefit));
            } catch (Exception e) {
                LOGGER.error(String.format("[商城云服务权益]定时任务扫描权益自然过期业务处理,处理失败。storeBenefit:%s", JSON.toJSONString(storeBenefit)), e);
                FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务权益]定时任务扫描权益自然过期业务处理,处理失败。。storeBenefit:%s 错误信息:[%s]", JSON.toJSONString(storeBenefit), ExceptionUtil.stacktraceToString(e)), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            }
        }

        XxlJobLogger.log("本次权益回收任务已处理完毕。");
        return ReturnT.SUCCESS;
    }

    /**
     * 对云存储商城权益进行回收处理。
     * 该方法首先解析订阅类型和容量类型，然后将权益状态设置为过期，并更新修改时间，标记为已回收。
     * 接着，根据解析得到的订阅类型、容量类型和服务场景类型创建云服务业务类型映射BO。
     * 之后，利用业务上下文和相应的订阅服务处理逻辑对权益进行处理。
     *
     * @param storeBenefit 包含云存储商城权益信息的对象。
     */
    private void recycling(CloudStorageStoreBenefit storeBenefit) {
        // 解析订阅类型
        SkuSubscribeType skuSubscribeType = storeBenefit.parseSkuSubscribeType();
        // 解析容量类型
        BenefitCapacityType capacityType = storeBenefit.parseCapacityType();

        // 更新权益状态为过期并记录回收操作
        storeBenefit.setStatus(BenefitStatus.EXPIRED.getName());
        storeBenefit.setModifyTime(LocalDateTime.now());
        storeBenefit.setRecycled(true);

        // 创建云服务业务类型映射BO，用于后续业务处理
        StoreCloudBusinessTypeResultBO typeResult = new StoreCloudBusinessTypeResultBO(skuSubscribeType, ServiceScenesType.EXPIRED, capacityType);

        // 构建业务上下文，包括业务类型映射和权益信息
        StoreBenefitContext storeBenefitContext = new StoreBenefitContext(typeResult, null, storeBenefit, null, true);

        // 根据订阅类型获取对应的订阅服务处理实例，并执行回收处理逻辑
        StoreBenefitSubscribeService storeBenefitSubscribeService = storeBenefitFactory.getStoreBenefitSubscribeService(skuSubscribeType);
        storeBenefitSubscribeService.handle(storeBenefitContext);
    }
}
