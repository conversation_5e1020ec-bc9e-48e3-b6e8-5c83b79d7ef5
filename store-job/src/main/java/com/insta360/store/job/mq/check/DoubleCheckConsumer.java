package com.insta360.store.job.mq.check;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.configuration.check.DoubleCheckFactory;
import com.insta360.store.business.configuration.check.DoubleCheckInterface;
import com.insta360.store.business.configuration.check.bo.CheckResultBO;
import com.insta360.store.business.exception.RetryHandlerException;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.check.bo.DoubleCheckBO;
import com.insta360.store.business.reseller.service.ResellerOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/11/18
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class DoubleCheckConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(DoubleCheckConsumer.class);

    @Autowired
    OrderService orderService;

    @Autowired
    ResellerOrderService resellerOrderService;

    @Autowired
    DoubleCheckFactory doubleCheckFactory;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_keypoint_business_double_check)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("商城二次参数校验接收到message:{}", messageBody);
        try {
            DoubleCheckBO doubleCheckBo = JSON.parseObject(messageBody, DoubleCheckBO.class);
            if (doubleCheckBo == null || doubleCheckBo.getCheckType() == null) {
                LOGGER.error("参数二次校验缺少必要参数,抛弃该消息,data:{}", doubleCheckBo);
                return Action.CommitMessage;
            }
            // 通过枚举获取工厂
            DoubleCheckInterface doubleCheckHandler = doubleCheckFactory.getDoubleCheckHandler(doubleCheckBo.getCheckType());
            if (doubleCheckHandler == null) {
                LOGGER.error("参数二次校验未获取到处理器,抛弃该消息,data:{}", doubleCheckBo);
                return Action.CommitMessage;
            }
            LOGGER.info("商城二次参数开始执行... data:{}", doubleCheckBo);
            List<CheckResultBO> checkResultBoList = doubleCheckHandler.doubleCheck(doubleCheckBo);
            doubleCheckHandler.sendWarningMessage(doubleCheckBo, checkResultBoList);
        } catch (Exception e) {
            LOGGER.error("参数二次校验发生业务异常,此消息将抛弃", e);
            if (e instanceof RetryHandlerException) {
                return Action.ReconsumeLater;
            }
        }
        return Action.CommitMessage;
    }
}
