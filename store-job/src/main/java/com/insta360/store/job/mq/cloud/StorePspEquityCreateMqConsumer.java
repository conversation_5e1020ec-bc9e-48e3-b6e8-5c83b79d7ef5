package com.insta360.store.job.mq.cloud;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.cloud.dto.StorePspOrderDTO;
import com.insta360.store.business.cloud.service.impl.helper.PspEquityHelper;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.constants.OrderBizMarkConstant;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2025/05/15
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class StorePspEquityCreateMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(StorePspEquityCreateMqConsumer.class);

    @Autowired
    PspEquityHelper pspEquityHelper;

    @Autowired
    UserAccountHelper userAccountHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_psp_equity_handle)
    @Override
    public Action consume(Message message, ConsumeContext context) {
        // 接收到的消息
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info(String.format("[商城云服务]云服务PSP订单支付后权益下发通知MQ消费开始,msgId:{%s}, msg:{%s}", message.getMsgID(), messageBody));

        try {
            StorePspOrderDTO storePspOrderDto = JSON.parseObject(messageBody, StorePspOrderDTO.class);
            if (Objects.isNull(storePspOrderDto)) {
                return Action.CommitMessage;
            }

            Order pspOrder = storePspOrderDto.getPspOrder();
            if (Objects.isNull(pspOrder) || !pspOrder.isMark(OrderBizMarkConstant.psp_order_mark)) {
                LOGGER.error("psp 订单权益处理信息异常{}", pspOrder);
                return Action.CommitMessage;
            }

            // 游客订单处理
            if (StoreAccount.GUEST_USER_ID.equals(pspOrder.getUserId())) {
                LOGGER.info("psp访客订单处理 {}", pspOrder);
                // 判断游客邮箱是否已注册
                StoreAccount storeAccount = userAccountHelper.getStoreAccountByEmail(pspOrder.getContactEmail());
                if (Objects.isNull(storeAccount)) {
                    pspEquityHelper.savePspOrderEquity(pspOrder);
                    return Action.CommitMessage;
                }
                pspOrder.setUserId(storeAccount.getInstaAccount());
            }

            // 下发psp权益
            pspEquityHelper.handlePspEquity(pspOrder, storePspOrderDto.getOrderPayment());

        } catch (Exception e) {
            LOGGER.error(String.format("[商城云服务]云服务PSP订单支付后权益下发通知MQ消费异常,msgId:{%s}, msg:{%s}", message.getMsgID(), messageBody), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("[商城云服务]云服务PSP订单支付后权益下发通知MQ消费异常. 消息体:%s, 异常信息:{%s}", messageBody, e.getMessage()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }
        return Action.CommitMessage;
    }
}
