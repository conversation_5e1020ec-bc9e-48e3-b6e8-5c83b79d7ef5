package com.insta360.store.job.mq.order;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.configuration.check.enums.DoubleCheckEnum;
import com.insta360.store.business.outgoing.mq.check.helper.DoubleCheckSendHelper;
import com.insta360.store.business.outgoing.mq.order.dto.OrderMessageDTO;
import com.insta360.store.business.trade.service.impl.helper.CustomImageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * @description: 定制贴数据处理
 * @author: py
 * @create: 2024-06-05 16:19
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class OrderCustomImageHandleMqConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCustomImageHandleMqConsumer.class);

    @Autowired
    DoubleCheckSendHelper doubleCheckSendHelper;

    @Autowired
    CustomImageHelper customImageHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_order_create_bind_custom)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext context) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("定制贴数据处理.消息体:{}", messageBody);

        try {
            OrderMessageDTO orderMessage = JSON.parseObject(messageBody, OrderMessageDTO.class);
            if (CollectionUtils.isEmpty(orderMessage.getCustomItemImageInfos())) {
                LOGGER.error("定制贴数据处理异常,定制贴数据为空.消息体:{}.", messageBody);
                return Action.CommitMessage;
            }

            Integer orderId = orderMessage.getOrderId();
            // 绑定定制贴
            customImageHelper.bindCustomImage(orderId, orderMessage.getCustomItemImageInfos());

            // 定制贴二次校验
            doubleCheckSendHelper.sendDoubleCheckMessage(orderId, DoubleCheckEnum.CustomImageBindCheck);
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error("定制贴数据处理异常.消息体:{}.原因:{}", messageBody, e.getMessage());
            LOGGER.error("定制贴数据处理异常堆栈", e);
            return Action.CommitMessage;
        }
    }
}
