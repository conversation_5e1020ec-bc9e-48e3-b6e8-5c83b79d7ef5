//package com.insta360.store.job.task;
//
//
//
//import com.insta360.store.business.integration.jingdong.constant.JdConstantPool;
//import com.insta360.store.business.integration.jingdong.service.impl.helper.SyncJdRmaOrderHelper;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//
///**
// * @Author: wkx
// * @Date: 2021/1/15
// * @Description:
// */
////@Component
//public class JdRmaOrderSyncTask {
//
//    public static final Logger LOGGER = LoggerFactory.getLogger(JdOrderSyncTask.class);
//
//    @Autowired
//    SyncJdRmaOrderHelper jdRmaOrderHelper;
//
//
//    @Scheduled(fixedDelay = 15 * 60 * 1000)
//    public void run() {
//        if (LOGGER.isInfoEnabled()) {
//            LOGGER.info("Sync jd rmaOrder to store and gy begin.");
//        }
//        // 同步京东采购订单到商城和管易
//        Integer recordCount = jdRmaOrderHelper.syncReturnOrder(JdConstantPool.PAGE_INDEX);
//        int indexSize = recordCount / Integer.parseInt(JdConstantPool.PAGE_SIZE);
//        if (indexSize < 1) {
//            if (LOGGER.isInfoEnabled()) {
//                LOGGER.info("Sync jd rmaOrder to store and gy end.");
//            }
//            return;
//        }
//
//        for (int index = 2; index <= indexSize + 1; index++) {
//            jdRmaOrderHelper.syncReturnOrder(String.valueOf(index));
//        }
//        if (LOGGER.isInfoEnabled()) {
//            LOGGER.info("Sync jd rmaOrder to store and gy end.");
//        }
//    }
//}
