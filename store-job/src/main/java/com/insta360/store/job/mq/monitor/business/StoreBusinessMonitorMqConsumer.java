package com.insta360.store.job.mq.monitor.business;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpChannelEnum;
import com.insta360.store.business.configuration.monitor.bo.StoreBusinessMonitorBO;
import com.insta360.store.business.configuration.monitor.support.handler.BaseMonitorHandler;
import com.insta360.store.business.configuration.monitor.support.helper.MonitorKeySelectHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2022/8/8
 * @Description:
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class StoreBusinessMonitorMqConsumer implements MessageListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(StoreBusinessMonitorMqConsumer.class);

    @Autowired
    MonitorKeySelectHelper monitorKeySelectHelper;

    @MessageTcpProcessor(messageChannel = MessageTcpChannelEnum.store_business_monitor)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        StoreBusinessMonitorBO monitorBO = JSON.parseObject(messageBody, StoreBusinessMonitorBO.class);
        LOGGER.info("monitor consume start ......" + monitorBO);
        if (Objects.isNull(monitorBO)) {
            LOGGER.error("BO参数有误！！");
            return Action.CommitMessage;
        }

        // 业务key选择器
        try {
            BaseMonitorHandler monitorHandler = monitorKeySelectHelper.keySelect(monitorBO.getKeyType());
            monitorHandler.handler(monitorBO);
        } catch (Exception e) {
            LOGGER.error("监控消费异常：" + monitorBO + "____" + e.getCause().getMessage());
        }
        LOGGER.info("monitor consume end ......" + monitorBO);
        return Action.CommitMessage;
    }
}
