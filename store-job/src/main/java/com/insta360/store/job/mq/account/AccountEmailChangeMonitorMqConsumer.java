package com.insta360.store.job.mq.account;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.dudep.annotations.MessageTcpDudep;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpBroadcastProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpBroadcastChannelEnum;
import com.insta360.store.business.cloud.service.impl.helper.GuestSubscribeManagerHelper;
import com.insta360.store.business.cloud.service.impl.helper.PspEquityHelper;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.outgoing.mq.user.helper.UserMessageSendHelper;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.user.enums.UserStrategyType;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.job.mq.account.dto.AccountMessageDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 用户账户邮箱修改通知 && 新用户注册通知
 * @Date 2024/11/1
 */
@Component
@ConditionalOnProperty(prefix = "timer.mq", name = "enable", havingValue = "true")
public class AccountEmailChangeMonitorMqConsumer implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(AccountEmailChangeMonitorMqConsumer.class);

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    ResellerService resellerService;

    @Autowired
    UserMessageSendHelper userMessageSendHelper;

    @Autowired
    GuestSubscribeManagerHelper storeCloudSubscribeManagerHelper;

    @Autowired
    PspEquityHelper pspEquityHelper;

    @MessageTcpBroadcastProcessor(messageChannel = MessageTcpBroadcastChannelEnum.user_account_sync)
    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("[新广播]接收到用户账户邮箱修改通知 && 新用户注册通知事件推送。消息ID：{}, 消息体:{}", message.getMsgID(), messageBody);
        AccountMessageDTO accountMessage;
        try {
            accountMessage = JSONObject.parseObject(messageBody, AccountMessageDTO.class);
        } catch (Exception e) {
            LOGGER.error(String.format("用户账户邮箱修改通知 && 新用户注册通知事件 参数解析失败。原因:{%s}, 消息体:{%s}", e.getMessage(), messageBody), e);
            return Action.CommitMessage;
        }

        if (Objects.isNull(accountMessage)) {
            LOGGER.error("用户账户邮箱修改通知 && 新用户注册通知事件 缺少必要参数。消息体:{}", messageBody);
            return Action.CommitMessage;
        }

        // 消息策略
        switch (UserStrategyType.parse(accountMessage.getMessageStrategy())) {
            // 用户名称邮箱修改
            case USERNAME_UPDATE:
                return usernameUpdate(accountMessage);
            // 新用户注册
            case SIGN_UP:
                return userSignUp(accountMessage);
            default:
                return Action.CommitMessage;
        }
    }

    /**
     * 修改账户用户名
     *
     * @param accountMessage
     * @return
     */
    private Action usernameUpdate(AccountMessageDTO accountMessage) {
        // 参数校验
        if (Objects.isNull(accountMessage.getUserId()) || StringUtils.isBlank(accountMessage.getOldEmail()) || StringUtils.isBlank(accountMessage.getUsername())) {
            LOGGER.error("执行账户邮箱修改操作异常。参数缺失。accountMessage:{}", accountMessage);
            return Action.CommitMessage;
        }

        StoreAccount storeAccount = new StoreAccount();
        storeAccount.setInstaAccount(accountMessage.getUserId());
        storeAccount.setUsername(accountMessage.getUsername());

        // 注销
        if (Objects.nonNull(accountMessage.getDeregisterMark()) && accountMessage.getDeregisterMark()) {
            // 处理云服务订阅快捷支付信息
            storeCloudSubscribeManagerHelper.handleDeregister(storeAccount);
        } else {
            // 邮箱修改
            // 1、处理游客云服务订阅
            storeCloudSubscribeManagerHelper.handleGuestRegistration(storeAccount);

            // 2、处理代金券绑定邮箱更换
            LOGGER.info("执行账户代金券修改操作");
            updateGiftCard(accountMessage.getOldEmail(), accountMessage.getUsername());
            LOGGER.info("执行账户代金券修改操作完成");

            // 3、处理分销商邮箱修改
            LOGGER.info("执行分销账户邮箱修改操作");
            updateResellerEmail(accountMessage);
            LOGGER.info("执行分销账户邮箱修改操作完成");
        }

        return Action.CommitMessage;
    }

    /**
     * 更新分销商的邮箱地址
     * 当分销商的邮箱地址需要变更时，调用此方法更新
     * 如果找不到对应的分销商记录，则不执行任何操作
     *
     * @param accountMessage 包含用户ID、新邮箱地址和旧邮箱地址的信息
     */
    private void updateResellerEmail(AccountMessageDTO accountMessage) {
        try {
            // 根据用户ID获取分销商记录
            List<Reseller> resellers = resellerService.listByEmail(accountMessage.getOldEmail());
            // 如果分销商不存在，则直接返回，无需更新
            if (CollectionUtils.isEmpty(resellers)) {
                LOGGER.info("执行分销账户邮箱修改操作失败 分销商不存在 accountID:{} oldEmail:{} username:{}", accountMessage.getUserId(), accountMessage.getOldEmail(), accountMessage.getUsername());
                return;
            }
            if (resellers.size() > 1) {
                LOGGER.info("执行分销账户邮箱修改操作失败 分销商记录数量异常 accountID:{} oldEmail:{} username:{} resellers:{}", accountMessage.getUserId(), accountMessage.getOldEmail(), accountMessage.getUsername(), resellers);
                FeiShuMessageUtil.storeGeneralMessage(String.format("执行分销账户邮箱修改操作失败 分销商记录数量异常 accountID:%s oldEmail:%s username:%s resellers:%s", accountMessage.getUserId(), accountMessage.getOldEmail(), accountMessage.getUsername(), resellers), FeiShuGroupRobot.ResellerSpecific, FeiShuAtUser.RESELLER_NOTICE);
                return;
            }
            Reseller reseller = resellers.get(0);
            // 更新分销商的邮箱地址为新的邮箱地址
            reseller.setEmail(accountMessage.getUsername());
            resellerService.updateResellerById(reseller);

            String message = String.format("分销商邮箱修改完成 oldEmail:%s username:%s", accountMessage.getOldEmail(), accountMessage.getUsername());
            LOGGER.info(message);
            FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.ResellerSpecific, FeiShuAtUser.RESELLER_NOTICE);
        } catch (Exception e) {
            // 记录更新操作中的异常信息
            LOGGER.error("执行分销账户邮箱修改操作异常 oldEmail:{} username:{}", accountMessage.getOldEmail(), accountMessage.getUsername(), e);
            FeiShuMessageUtil.storeGeneralMessage(String.format("分销商邮箱修改失败 oldEmail:%s username:%s", accountMessage.getOldEmail(), accountMessage.getUsername()), FeiShuGroupRobot.ResellerSpecific, FeiShuAtUser.RESELLER_NOTICE);
        }
    }

    /**
     * 更新对应邮件所绑定的代金券
     *
     * @param oldEmail
     */
    private void updateGiftCard(String oldEmail, String newEmail) {
        giftCardService.batchUpdateBindEmail(oldEmail, newEmail);
    }

    /**
     * 用户注册
     *
     * @param accountMessage
     */
    private Action userSignUp(AccountMessageDTO accountMessage) {
        LOGGER.info("[用户注册通知] account:{}", accountMessage);
        if (Objects.isNull(accountMessage.getUserId()) || StringUtils.isBlank(accountMessage.getUsername())) {
            LOGGER.error("[用户注册通知] 参数缺失 account:{}", accountMessage);
            return Action.CommitMessage;
        }
        // 注册用户执行云服务订阅与权益下发
        StoreAccount storeAccount = new StoreAccount();
        storeAccount.setInstaAccount(accountMessage.getUserId());
        storeAccount.setUsername(accountMessage.getUsername());
        storeCloudSubscribeManagerHelper.handleGuestRegistration(storeAccount);

        // 注册用户执行PSP权益处理
        pspEquityHelper.handleGuestPspEquity(storeAccount);

        // 将使用该邮箱下单的游客订单进行用户绑定
        userMessageSendHelper.sendUserLoginMessage(storeAccount);

        return Action.CommitMessage;
    }
}
