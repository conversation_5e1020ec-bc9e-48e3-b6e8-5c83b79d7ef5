package com.insta360.store.service.controller.meta.cache.cacheable;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.meta.model.AvalaraTaxProvince;
import com.insta360.store.business.meta.service.AvalaraTaxProvinceService;
import com.insta360.store.service.controller.meta.vo.AvalaraTaxProvinceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 收税州
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/3
 */
@Component
public class AvalaraTaxProvinceCachePack {

    @Autowired
    AvalaraTaxProvinceService avalaraTaxProvinceService;

    /**
     * 封装批量avalara税州
     *
     * @param instaCountry insta国家
     * @return {@link Response}<{@link ?} {@link extends} {@link Map}>
     */
    @CacheTtlMonitor(value = CacheableType.AVALARA_TAX_PROVINCE, cacheKey = "'-AvalaraTaxProvincePack-' + methodName + '-countryCode-' + #instaCountry")
    @Cacheable(value = CacheableType.AVALARA_TAX_PROVINCE, key = "caches[0].name  + '-AvalaraTaxProvincePack-' + methodName + '-countryCode-' + #instaCountry")
    public List<AvalaraTaxProvinceVO> doPackListAvalaraTaxProvince(InstaCountry instaCountry) {
        List<AvalaraTaxProvince> avalaraTaxProvinceList = avalaraTaxProvinceService.listObtainTaxProvinces(instaCountry);
        return avalaraTaxProvinceList.stream().map(AvalaraTaxProvinceVO::new).collect(Collectors.toList());
    }
}
