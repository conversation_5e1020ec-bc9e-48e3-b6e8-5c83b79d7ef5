package com.insta360.store.service.rpc.insurance.response;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025/2/20 下午6:43
 */
public class InsuranceSwitchBindingResponse implements Serializable {

    /**
     * 换绑结果: "success", "failure", "partial_failure"
     */
    private String status;

    /**
     * 增值服务数组对象
     */
    private List<Service> services;

    public InsuranceSwitchBindingResponse(String status, List<Service> services) {
        this.status = status;
        this.services = services;
    }

    public InsuranceSwitchBindingResponse() {
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<Service> getServices() {
        return services;
    }

    public void setServices(List<Service> services) {
        this.services = services;
    }

    /**
     * Service 内部类，表示单个增值服务
     */
    public static class Service {
        /**
         * 增值服务名称: "care", "care_plus", "care_card", "extend"
         */
        private String serviceName;

        /**
         * 换绑结果: "success", "failure"
         */
        private String rebindResult;

        public Service(String serviceName, String rebindResult) {
            this.serviceName = serviceName;
            this.rebindResult = rebindResult;
        }

        public Service() {
        }

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public String getRebindResult() {
            return rebindResult;
        }

        public void setRebindResult(String rebindResult) {
            this.rebindResult = rebindResult;
        }

        @Override
        public String toString() {
            return "Service{" +
                    "serviceName='" + serviceName + '\'' +
                    ", rebindResult='" + rebindResult + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "InsuranceRebindResponse{" +
                "status='" + status + '\'' +
                ", services=" + services +
                '}';
    }
}
