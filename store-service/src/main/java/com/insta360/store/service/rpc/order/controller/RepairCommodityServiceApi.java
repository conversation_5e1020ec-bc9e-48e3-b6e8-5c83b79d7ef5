package com.insta360.store.service.rpc.order.controller;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.service.rpc.order.vo.CommodityPriceVO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: wkx
 * @Date: 2021/10/14
 * @Description:
 */
@RestController
public class RepairCommodityServiceApi {

    @Autowired
    CommodityPriceService commodityPriceService;

    /**
     * 套餐价格
     *
     * @param commodityId
     * @param country
     * @return
     */
    @GetMapping("/rpc/store/service/order/getCommodityPriceById")
    public Response<Object> getCommodityPriceById(@RequestParam(required = false) Integer commodityId,
                                                  @RequestParam(required = false) String country) {
        if (commodityId == null || StringUtils.isBlank(country)) {
            return Response.failed("获取套餐价格失败");
        }

        CommodityPrice commodityPrice = commodityPriceService.getPrice(commodityId, InstaCountry.parse(country));
        if (commodityPrice == null) {
            return Response.failed("获取套餐价格失败");
        }

        CommodityPriceVO commodityPriceVO = new CommodityPriceVO();
        commodityPriceVO.setCommodityId(commodityPrice.getCommodityId());
        commodityPriceVO.setOriginPrice(commodityPrice.getOriginAmount());
        commodityPriceVO.setPrice(commodityPrice.getAmount());
        commodityPriceVO.setCurrency(commodityPrice.getCurrency());
        return Response.ok(commodityPriceVO);
    }
}

