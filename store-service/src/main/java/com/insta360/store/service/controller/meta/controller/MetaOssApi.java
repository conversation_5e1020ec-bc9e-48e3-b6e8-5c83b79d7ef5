package com.insta360.store.service.controller.meta.controller;

import cn.hutool.core.io.FileUtil;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.libs.aliyun.oss.dto.OSSDirectSignInfo;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.meta.service.impl.helper.MetaOssHelper;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.meta.vo.OssUploadInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/24 19:14
 * @Description: OSS获取操作
 * @Version 1.0
 */
@RestController
public class MetaOssApi extends BaseApi {

    /**
     * 最大允许调用次数
     */
    private static final Integer MAX_SIZE = 10;

    @Autowired
    MetaOssHelper metaOssHelper;

    /**
     * OSS 获取签名
     *
     * @param fileSize
     * @param fileName
     * @param signatureNumber
     * @return
     */
//    @AvoidRepeatableCommit(timeOut = 500)
    @GetMapping("/store/meta/oss/listSignatures")
    public Response<? extends Map> listSignatures(@RequestParam Long fileSize,
                                                  @RequestParam String fileName,
                                                  @RequestParam Integer signatureNumber) {
        // 最大一次允许生成10个
        if (signatureNumber > MAX_SIZE || StringUtil.isBlank(fileName)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 文件名
        String fileNamePrefix = FileUtil.mainName(fileName);
        // 文件尾缀
        String fileNameSuffix = FileUtil.extName(fileName);

        if (StringUtil.isAnyBlank(fileNamePrefix, fileNameSuffix)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 获取文件上传相关信息
        List<OssUploadInfoVO> ossUploadInfos = new ArrayList<>(signatureNumber);
        for (int i = 0; i < signatureNumber; i++) {
            String needFileName = fileNamePrefix + i + "." + fileNameSuffix;
            OSSDirectSignInfo ossDirectSignInfo = metaOssHelper.getSignature(InstaCountry.CN.equals(getApiCountry()), needFileName, fileSize);
            ossUploadInfos.add(new OssUploadInfoVO(ossDirectSignInfo));
        }
        return Response.ok("uploadInfos", ossUploadInfos);
    }

}
