package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategorySubsetInside;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: py
 * @Date: 2023/11/16
 * @Description:
 */
public class NavigationBarCategorySubsetVO implements Serializable {

    private Integer id;

    /**
     * 二级名称
     */
    private String categorySubsetName;

    /**
     * 二级内部名称
     */
    private String subsetInsideName;

    /**
     * 跳转链接
     */
    private String urlLink;

    /**
     * 二级导航栏类型（配件）
     */
    private String type;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 配件三级导航栏
     */
    private List<NavigationBarCategoryThirdVO> thirdList;

    public NavigationBarCategorySubsetVO() {
    }

    public NavigationBarCategorySubsetVO(NavigationBarCategorySubsetInside subsetInside) {
        if (subsetInside != null) {
            BeanUtil.copyProperties(subsetInside, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCategorySubsetName() {
        return categorySubsetName;
    }

    public void setCategorySubsetName(String categorySubsetName) {
        this.categorySubsetName = categorySubsetName;
    }

    public String getSubsetInsideName() {
        return subsetInsideName;
    }

    public void setSubsetInsideName(String subsetInsideName) {
        this.subsetInsideName = subsetInsideName;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public void setUrlLink(String urlLink) {
        this.urlLink = urlLink;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<NavigationBarCategoryThirdVO> getThirdList() {
        return thirdList;
    }

    public void setThirdList(List<NavigationBarCategoryThirdVO> thirdList) {
        this.thirdList = thirdList;
    }

    @Override
    public String toString() {
        return "NavigationBarCategorySubsetVO{" +
                "id=" + id +
                ", categorySubsetName='" + categorySubsetName + '\'' +
                ", subsetInsideName='" + subsetInsideName + '\'' +
                ", urlLink='" + urlLink + '\'' +
                ", type='" + type + '\'' +
                ", orderIndex=" + orderIndex +
                ", thirdList=" + thirdList +
                '}';
    }
}
