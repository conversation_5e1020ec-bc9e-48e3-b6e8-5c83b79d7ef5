package com.insta360.store.service.common.interceptor;

import com.insta360.store.business.reseller.service.ResellerService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.common.WebApiContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: hyc
 * @Date: 2019/3/2
 * @Description:
 */
public class ResellerInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    ResellerService resellerService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        WebApiContext apiContext = WebApiContext.get();
        if (apiContext != null) {
            StoreAccount account = apiContext.getAccessUser();
            if (account != null) {
                apiContext.setReseller(resellerService.getByUserId(account.getInstaAccount()));
            }
        }

        return super.preHandle(request, response, handler);
    }
}
