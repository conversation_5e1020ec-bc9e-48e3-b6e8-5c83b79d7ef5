package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.CategoryPageCommodityInfoPack;
import com.insta360.store.service.controller.meta.format.CategoryPagePack;
import com.insta360.store.service.controller.meta.vo.AccessoryCategoryFilterVO;
import com.insta360.store.service.controller.meta.vo.CategoryPageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2023/11/11
 * @Description:
 */
@Component
public class CategoryPageCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(CategoryPageCachePutPack.class);

    @Autowired
    CategoryPageCommodityInfoPack categoryPageCommodityInfoPack;

    @Autowired
    CategoryPagePack categoryPagePack;

    /**
     * 类目页缓存
     *
     * @param country
     * @param language
     * @param categoryKey
     * @return
     */
    @CachePut(value = CacheableType.CATEGORY_PAGE_FILTER, key = "caches[0].name  + '-CategoryPageCachePack-' + methodName +  '-categoryKey-' + #categoryKey + '-country-' + #country+ '-language-' + #language")
    public AccessoryCategoryFilterVO listCategoryFilter(String categoryKey, InstaCountry country, InstaLanguage language) {
        AccessoryCategoryFilterVO accessoryCategoryFilter = categoryPagePack.listCategoryFilter(categoryKey, country, language);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[categoryKey:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.CATEGORY_PAGE_FILTER, "listCategoryFilter", categoryKey, country, language, accessoryCategoryFilter));
        return accessoryCategoryFilter;
    }

    /**
     * 类目页缓存
     *
     * @param categoryKey
     * @param country
     * @param language
     * @return
     */
    @CachePut(value = CacheableType.CATEGORY_PAGE, key = "caches[0].name  + '-CategoryPageCachePack-' + methodName +  '-categoryKey-' + #categoryKey + '-country-' + #country+ '-language-' + #language")
    public CategoryPageVO packCategoryPage(String categoryKey, InstaCountry country, InstaLanguage language) {
        CategoryPageVO categoryPage = categoryPageCommodityInfoPack.packCategoryPage(categoryKey, country, language);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[categoryKey:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.CATEGORY_PAGE, "packCategoryPage", categoryKey, country, language, categoryPage));
        return categoryPage;
    }
}
