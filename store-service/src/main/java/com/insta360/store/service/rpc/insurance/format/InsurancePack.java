package com.insta360.store.service.rpc.insurance.format;

import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.insurance.config.InsuranceCommonConfiguration;
import com.insta360.store.business.insurance.constant.InsuranceCommonConstant;
import com.insta360.store.business.insurance.enums.AppPurchaseInsuranceType;
import com.insta360.store.business.insurance.model.CareInsurance;
import com.insta360.store.business.insurance.model.CareInsuranceActivationCard;
import com.insta360.store.business.insurance.model.CarePlusInsurance;
import com.insta360.store.business.insurance.model.ExtendInsurance;
import com.insta360.store.business.insurance.service.CareInsuranceActivationCardService;
import com.insta360.store.business.insurance.service.CareInsuranceService;
import com.insta360.store.business.insurance.service.CarePlusInsuranceService;
import com.insta360.store.business.insurance.service.ExtendInsuranceService;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceCheckHelper;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceHelper;
import com.insta360.store.business.insurance.service.impl.helper.http.DeviceInfoHelper;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceActivationInfo;
import com.insta360.store.service.rpc.insurance.vo.CareInsuranceVO;
import com.insta360.store.service.rpc.insurance.vo.ExtendInsuranceVO;
import com.insta360.store.service.rpc.insurance.vo.InsuranceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: py
 * @create: 2023-09-13 14:06
 */
@Component
public class InsurancePack {

    private static final Logger LOGGER = LoggerFactory.getLogger(InsurancePack.class);

    @Autowired
    CareInsuranceService careInsuranceService;

    @Autowired
    ExtendInsuranceService extendInsuranceService;

    @Autowired
    CareInsuranceActivationCardService careInsuranceActivationCardService;

    @Autowired
    DeviceInfoHelper deviceInfoHelper;

    @Autowired
    InsuranceHelper insuranceHelper;

    @Autowired
    InsuranceCheckHelper insuranceCheckHelper;

    @Autowired
    InsuranceCommonConfiguration insuranceCommonConfig;

    @Autowired
    CarePlusInsuranceService carePlusInsuranceService;

    /**
     * 通过序列号查询增值服务信息
     *
     * @param deviceSerial
     * @param area
     * @return
     */
    public InsuranceVO packInsurance(String deviceSerial, String area) {
        // 查询数据
        CareInsurance careInsurance = careInsuranceService.getCareInsurance(deviceSerial);
        CareInsuranceActivationCard careCardInsurance = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
        ExtendInsurance extendInsurance = extendInsuranceService.getBySerial(deviceSerial);
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(deviceSerial);

        CareInsuranceVO careInsuranceVO = null;
        if (Objects.nonNull(careInsurance)) {
            careInsuranceVO = new CareInsuranceVO(careInsurance);
            careInsuranceVO.setCareInsuranceType(ServiceType.care.name());
        }
        if (Objects.nonNull(careCardInsurance)) {
            careInsuranceVO = new CareInsuranceVO(careCardInsurance);
            careInsuranceVO.setCareInsuranceType(ServiceType.care_card.name());
        }
        if (Objects.nonNull(carePlusInsurance)) {
            careInsuranceVO = new CareInsuranceVO(carePlusInsurance);
            careInsuranceVO.setCareInsuranceType(ServiceType.care_plus.name());
        }

        ExtendInsuranceVO extendInsuranceVo;
        if (Objects.nonNull(extendInsurance)) {
            extendInsuranceVo = new ExtendInsuranceVO(extendInsurance);
        } else {
            extendInsuranceVo = saveDefaultTime(deviceSerial, area);
        }
        InsuranceVO insuranceVo = new InsuranceVO();
        insuranceVo.setCareInsurance(careInsuranceVO);
        insuranceVo.setExtendInsurance(extendInsuranceVo);
        return insuranceVo;
    }

    /**
     * 封装延保的默认时间
     *
     * @param deviceSerial
     * @param area
     * @return
     */
    private ExtendInsuranceVO saveDefaultTime(String deviceSerial, String area) {
        // 封装默认时间
        DeviceActivationInfo activationInfo = deviceInfoHelper.getDeviceActivationInfo(deviceSerial);
        LOGGER.info("repair rpc activationInfo:" + activationInfo);
        if (Objects.isNull(activationInfo)) {
            return null;
        }
        ExtendInsuranceVO extendInsuranceVO = new ExtendInsuranceVO();
        extendInsuranceVO.setDeviceSerial(deviceSerial);
        // 欧盟地区默认2年、其他1年
        if (insuranceHelper.listEuropeArea().contains(area)) {
            extendInsuranceVO.setBindTime(activationInfo.getCreateTime());
            extendInsuranceVO.setExpireTime(activationInfo.getCreateTime().plusYears(2));
        } else {
            extendInsuranceVO.setBindTime(activationInfo.getCreateTime());
            extendInsuranceVO.setExpireTime(activationInfo.getCreateTime().plusYears(1));
        }
        extendInsuranceVO.setIsBuy(false);
        return extendInsuranceVO;
    }

    /**
     * 校验序列号是否购买过保险服务
     *
     * @param serial
     * @param purchaseInsuranceType
     * @param area
     * @return
     */
    public Map<String, Boolean> getInsurancePurchaseInfo(String serial, AppPurchaseInsuranceType purchaseInsuranceType, String area) {
        Map<String, Boolean> insuranceMap = new HashMap<>(2);
        Object value = RedisTemplateUtil.getValue(InsuranceCommonConstant.INSURANCE_PURCHASE_INFO_PREFIX + serial);
        if (Objects.nonNull(value)) {
            Map<String, Boolean> valueMap = (HashMap<String, Boolean>) value;
            insuranceMap = valueMap;
        }

        switch (purchaseInsuranceType) {
            case careInfo:
                getCarePurchaseInfo(insuranceMap, serial, area);
                break;
            case extendInfo:
                getExtendPurchaseInfo(insuranceMap, serial, area);
                break;
            case insuranceInfo:
                getInsuranceInfo(insuranceMap, serial, area);
                break;
            default:
                break;
        }
        return insuranceMap;
    }

    /**
     * 获取两个保险的购买信息
     *
     * @param insuranceMap
     * @param serial
     * @param area
     */
    private void getInsuranceInfo(Map<String, Boolean> insuranceMap, String serial, String area) {
        String careInfoKey = AppPurchaseInsuranceType.careInfo.getKey();
        String extendInfoKey = AppPurchaseInsuranceType.extendInfo.getKey();

        Boolean care = insuranceMap.get(careInfoKey);
        Boolean extend = insuranceMap.get(extendInfoKey);
        if (Objects.nonNull(care) && Objects.nonNull(extend)) {
            return;
        }

        if (Objects.isNull(care)) {
            // 未上线care
            Boolean careOnline = checkCareOnline(insuranceMap, area);
            if (careOnline) {
                Boolean carePurchaseInfo = insuranceCheckHelper.getCarePurchaseInfo(serial, ServiceType.care.name());
                insuranceMap.put(careInfoKey, carePurchaseInfo);
            }
        }

        if (Objects.isNull(extend)) {
            // 如果未上线延保或者欧盟地区
            Boolean extendOnline = checkExtendOnline(insuranceMap, area);
            if (extendOnline) {
                Boolean extendPurchaseInfo = insuranceCheckHelper.getExtendPurchaseInfo(serial, ServiceType.extend.name());
                insuranceMap.put(extendInfoKey, extendPurchaseInfo);
            }
        }
        RedisTemplateUtil.setKeyValue(InsuranceCommonConstant.INSURANCE_PURCHASE_INFO_PREFIX + serial, insuranceMap, 2, TimeUnit.HOURS);
    }

    /**
     * 获取care的购买信息
     *
     * @param insuranceMap
     * @param serial
     * @param area
     */
    private void getCarePurchaseInfo(Map<String, Boolean> insuranceMap, String serial, String area) {
        String careInfoKey = AppPurchaseInsuranceType.careInfo.getKey();
        Boolean care = insuranceMap.get(careInfoKey);
        if (Objects.nonNull(care)) {
            return;
        }

        // 未上线care
        Boolean checkOnline = checkCareOnline(insuranceMap, area);
        if (checkOnline) {
            Boolean carePurchaseInfo = insuranceCheckHelper.getCarePurchaseInfo(serial, ServiceType.care.name());
            insuranceMap.put(careInfoKey, carePurchaseInfo);
        }

        RedisTemplateUtil.setKeyValue(InsuranceCommonConstant.INSURANCE_PURCHASE_INFO_PREFIX + serial, insuranceMap, 2, TimeUnit.HOURS);
    }

    /**
     * 校验未上线的care
     *
     * @param insuranceMap
     * @param area
     */
    private Boolean checkCareOnline(Map<String, Boolean> insuranceMap, String area) {
        String careInfoKey = AppPurchaseInsuranceType.careInfo.getKey();
        List<String> careOnlineRegions = insuranceCommonConfig.getCareOnlineRegions();
        if (!careOnlineRegions.contains(area)) {
            insuranceMap.put(careInfoKey, true);
            return false;
        }
        return true;
    }

    /**
     * 获取延保的购买信息
     *
     * @param insuranceMap
     * @param serial
     * @param area
     */
    private void getExtendPurchaseInfo(Map<String, Boolean> insuranceMap, String serial, String area) {
        String extendInfoKey = AppPurchaseInsuranceType.extendInfo.getKey();
        Boolean extend = insuranceMap.get(extendInfoKey);
        if (Objects.nonNull(extend)) {
            return;
        }

        // 如果未上线延保或者欧盟地区
        Boolean checkOnline = checkExtendOnline(insuranceMap, area);

        if (checkOnline) {
            Boolean extendPurchaseInfo = insuranceCheckHelper.getExtendPurchaseInfo(serial, ServiceType.extend.name());
            insuranceMap.put(extendInfoKey, extendPurchaseInfo);
        }
        RedisTemplateUtil.setKeyValue(InsuranceCommonConstant.INSURANCE_PURCHASE_INFO_PREFIX + serial, insuranceMap, 2, TimeUnit.HOURS);
    }

    /**
     * 校验延保的上线地区
     *
     * @param insuranceMap
     * @param area
     */
    private Boolean checkExtendOnline(Map<String, Boolean> insuranceMap, String area) {
        String extendInfoKey = AppPurchaseInsuranceType.extendInfo.getKey();
        List<String> extendOnlineRegions = insuranceCommonConfig.getExtendOnlineRegions();
        List<String> europeAreas = insuranceCommonConfig.getEuropeAreas();
        if (!extendOnlineRegions.contains(area) || europeAreas.contains(area)) {
            insuranceMap.put(extendInfoKey, true);
            return false;
        }
        return true;
    }
}
