package com.insta360.store.service.controller.tradeup.controller;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.tradeup.exception.TradeupErrorCode;
import com.insta360.store.business.tradeup.model.TradeupEvaluationRule;
import com.insta360.store.business.tradeup.service.TradeupBrandService;
import com.insta360.store.business.tradeup.service.TradeupDeviceService;
import com.insta360.store.business.tradeup.service.TradeupEvaluationRuleService;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.tradeup.cache.cacheable.TradeupInfoCachePack;
import com.insta360.store.service.controller.tradeup.vo.TradeupEvaluationRuleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2020/04/08
 * @Description:
 */
@RestController
public class TradeupInfoApi extends BaseApi {

    @Autowired
    TradeupBrandService tradeupBrandService;

    @Autowired
    TradeupDeviceService tradeupDeviceService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    TradeupEvaluationRuleService tradeupEvaluationRuleService;

    @Autowired
    TradeupInfoCachePack tradeupInfoCachePack;

    /**
     * 获取换新的目标机型
     */
    @GetMapping("/store/tradeup/fresh/info/getTypes")
    public Response<? extends Map> getTypes() {
        return Response.ok("types", tradeupInfoCachePack.doPackTradeupTypesInfo());
    }

    /**
     * 获取参与换新的品牌
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @GetMapping("/store/tradeup/fresh/info/getBrands")
    public Response<? extends Map> getBrands(@RequestParam(required = false) Integer typeId) {
        return Response.ok("brands", tradeupBrandService.getBrands(typeId));
    }

    /**
     * 获取参与换新的设备
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @GetMapping("/store/tradeup/fresh/info/getDevices")
    public Response<? extends Map> getDevices(@RequestParam(required = false) Integer brandId) {
        return Response.ok("devices", tradeupDeviceService.getByBrand(brandId));
    }

    /**
     * 获取参与换新的设备的评估选项
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @GetMapping("/store/tradeup/fresh/info/getRules")
    public Response<? extends Map> getRules(@RequestParam(required = false, value = "device") Integer device) {
        // 拿到国家配置
        CountryConfig config = countryConfigService.getByCountry(getApiCountry());
        Currency currency = config.currency();

        TradeupEvaluationRule rules = tradeupEvaluationRuleService.getByDevice(device);
        // 校验规则
        if (rules == null) {
            throw new InstaException(TradeupErrorCode.InvalidRuleException);
        }

        TradeupEvaluationRuleVO ruleVO = new TradeupEvaluationRuleVO(rules);
        // 根据不同国家返回各自货币的估价
        JSONObject priceMap = JSONObject.parseObject(rules.getPriceMap());
        Float amount = priceMap.getFloat(currency.name());
        if (amount == null) {
            throw new InstaException(TradeupErrorCode.InvalidRuleException);
        }

        Price price = new Price(currency, amount);
        ruleVO.setPrice(price);
        ruleVO.setPriceMap(null);

        // 活动价
        JSONObject promoPriceMap = JSONObject.parseObject(rules.getPromoPriceMap());
        Price promoPrice = null;
        if (promoPriceMap != null) {
            Float promoAmount = promoPriceMap.getFloat(currency.name());
            promoPrice = new Price(currency, promoAmount);
        }
        ruleVO.setPromoPrice(promoPrice);
        ruleVO.setPromoPriceMap(null);
        ruleVO.setDeviceRef(tradeupDeviceService.getById(device));

        return Response.ok("rules", ruleVO);
    }
}
