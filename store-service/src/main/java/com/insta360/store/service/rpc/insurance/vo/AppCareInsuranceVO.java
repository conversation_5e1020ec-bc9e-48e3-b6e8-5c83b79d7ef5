package com.insta360.store.service.rpc.insurance.vo;

import java.io.Serializable;

/**
 * @description:
 * @author: py
 * @create: 2025-02-12 18:25
 */
public class AppCareInsuranceVO implements Serializable {

    /**
     * care类型
     */
    private String careType;

    /**
     * 基础链接
     */
    private String baseUrl;

    public String getCareType() {
        return careType;
    }

    public void setCareType(String careType) {
        this.careType = careType;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    @Override
    public String toString() {
        return "AppCareInsuranceVO{" +
                "careType='" + careType + '\'' +
                ", baseUrl='" + baseUrl + '\'' +
                '}';
    }
}
