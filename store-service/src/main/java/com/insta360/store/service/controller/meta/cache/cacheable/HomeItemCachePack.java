package com.insta360.store.service.controller.meta.cache.cacheable;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.HomeItemInfoPack;
import com.insta360.store.service.controller.meta.vo.HomeItemMainVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2022/02/23
 * @Description:
 */
@Component
public class HomeItemCachePack {

    @Autowired
    HomeItemInfoPack homeItemInfoPack;

    /**
     * 缓存home item 配置列表数据封装（类目页）
     *
     * @param homeItemId
     * @param language
     * @param country
     * @return
     */
    @CacheTtlMonitor(value = CacheableType.HOME_ITEM_KEY, cacheKey = "'-HomeItemCachePack-' + methodName + '-homeItem-' + #homeItem + '-language-' + #language + '-country-' + #country")
    @Cacheable(value = CacheableType.HOME_ITEM_KEY, key = "caches[0].name + '-HomeItemCachePack-' + methodName + '-homeItemId-' + #homeItemId + '-language-' + #language + '-country-' + #country")
    public HomeItemMainVO doPackCacheHomeItemInfo(Integer homeItemId, InstaLanguage language, InstaCountry country) {
        return homeItemInfoPack.doPackHomeItemInfo(homeItemId, language, country);
    }
}
