package com.insta360.store.service.common.interceptor;

import com.insta360.compass.core.web.api.ApiHeader;
import com.insta360.compass.core.web.api.ApiHeaderParser;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.api.ResponseCode;
import com.insta360.store.business.configuration.utils.RedisTemplateUtil;
import com.insta360.store.business.outgoing.rpc.user.dto.UserAccount;
import com.insta360.store.business.outgoing.rpc.user.service.UserAccountService;
import com.insta360.store.business.user.cache.UserAccountCache;
import com.insta360.store.business.user.constant.UserConstant;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.common.WebApiContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * @Author: mowi
 * @Date: 2018/11/29
 * @Description: 截获用户的token，找出account放进WebApiContext
 */
public class AccessUserInterceptor extends HandlerInterceptorAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccessUserInterceptor.class);

    @Autowired
    ApiHeaderParser apiHeaderParser;

    @Autowired
    UserAccountService userAccountService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // token不存在直接放行
        String userToken = apiHeaderParser.parseHeaderValue(request, ApiHeader.UserToken);
        LOGGER.info("user token: " + userToken);
        if (userToken == null) {
            return super.preHandle(request, response, handler);
        }

        // 如果缓存中存在
        UserAccountCache userAccountCache = (UserAccountCache) RedisTemplateUtil.getValue(UserConstant.USER_TOKEN_KEY + userToken);
        if (userAccountCache != null) {
            LOGGER.info("登录命中缓存。user_info:{}", userAccountCache);
            this.resetStoreAccount(new StoreAccount(userAccountCache));
            return super.preHandle(request, response, handler);
        }

        // 如果缓存中不存在，则需要调用RPC接口进行鉴权
        Response<UserAccount> userAccountResponse = userAccountService.getByUserToken(userToken);
        UserAccount userAccount = userAccountResponse.getData();
        if (!ResponseCode.SUCCESS.equals(userAccountResponse.getCode()) || userAccount == null) {
            LOGGER.error("RPC接口调用失败。token:{}", userToken);
            return super.preHandle(request, response, handler);
        }

        // 放入redis缓存（过期时间15天）
        userAccountCache = new UserAccountCache();
        userAccountCache.setAccountId(userAccount.getId());
        userAccountCache.setUsername(userAccount.getUsername());
        userAccountCache.setLanguage(userAccount.getLanguage());
        userAccountCache.setCountry(userAccount.getCountry());
        userAccountCache.setCreateTime(userAccount.getCreateTime());

        RedisTemplateUtil.setKeyValue(UserConstant.USER_TOKEN_KEY + userToken, userAccountCache, 15, TimeUnit.DAYS);
        this.resetStoreAccount(new StoreAccount(userAccount));
        return super.preHandle(request, response, handler);
    }

    /**
     * 重置storeAccount，将用户信息放入请求上下文之中
     *
     * @param userAccount
     */
    private void resetStoreAccount(StoreAccount userAccount) {
        // 如果是安全的请求，找出account放进WebApiContext
        WebApiContext apiContext = WebApiContext.get();
        if (apiContext != null) {
            apiContext.setAccessUser(userAccount);
        }
    }
}
