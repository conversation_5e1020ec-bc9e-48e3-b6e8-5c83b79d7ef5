package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.GraphicNavigationPack;
import com.insta360.store.service.controller.meta.vo.GraphicNavigationVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 图文导航缓存更新处理类
 * @Date 2023/11/6
 */
@Component
public class GraphicNavigationCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(GraphicNavigationCachePutPack.class);

    @Autowired
    GraphicNavigationPack graphicNavigationPack;

    /**
     * 图文导航缓存更新
     *
     * @param country
     * @param language
     * @param endpoint
     * @return
     */
    @CachePut(value = CacheableType.GRAPHIC_NAVIGATION, key = "caches[0].name + '-GraphicNavigationCachePack-' + methodName + '-country-' + #country + '-language-' + #language + '-endpoint-' + #endpoint")
    public List<GraphicNavigationVO> packGraphicNavigation(InstaCountry country, InstaLanguage language, Integer endpoint) {
        List<GraphicNavigationVO> graphicNavigationList = graphicNavigationPack.packGraphicNavigation(country, language, endpoint);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[endpoint:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.GRAPHIC_NAVIGATION, "packGraphicNavigation", endpoint, country, language, graphicNavigationList));
        return graphicNavigationList;
    }
}
