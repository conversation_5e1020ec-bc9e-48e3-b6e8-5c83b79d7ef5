package com.insta360.store.service.controller.reseller.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 分销订单商品VO
 * @Date 2023/4/26
 */
public class ResellerOrderItemVO implements Serializable {

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 商品购买数量
     */
    private Integer number;

    /**
     * 商品价格
     */
    private PriceVO itemPrice;

    /**
     * 商品预估收益
     */
    private Double estimatedIncome;

    /**
     * 套餐图片
     */
    private String commodityPicture;

    /**
     * 是否退款中
     */
    private Boolean refundProcess;

    /**
     * 是否赠品
     */
    private Boolean isGift;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 产品类目
     */
    private ProductCategoryMainVO productCategory;

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Double getEstimatedIncome() {
        return estimatedIncome;
    }

    public void setEstimatedIncome(Double estimatedIncome) {
        this.estimatedIncome = estimatedIncome;
    }

    public Boolean getRefundProcess() {
        return refundProcess;
    }

    public void setRefundProcess(Boolean refundProcess) {
        this.refundProcess = refundProcess;
    }

    public String getCommodityPicture() {
        return commodityPicture;
    }

    public void setCommodityPicture(String commodityPicture) {
        this.commodityPicture = commodityPicture;
    }

    public PriceVO getItemPrice() {
        return itemPrice;
    }

    public void setItemPrice(PriceVO itemPrice) {
        this.itemPrice = itemPrice;
    }

    public Boolean getIsGift() {
        return isGift;
    }

    public void setIsGift(Boolean isGift) {
        this.isGift = isGift;
    }

    public ProductCategoryMainVO getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(ProductCategoryMainVO productCategory) {
        this.productCategory = productCategory;
    }

    @Override
    public String toString() {
        return "ResellerOrderItemVO{" +
                "productName='" + productName + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", number=" + number +
                ", itemPrice=" + itemPrice +
                ", estimatedIncome=" + estimatedIncome +
                ", commodityPicture='" + commodityPicture + '\'' +
                ", refundProcess=" + refundProcess +
                ", isGift=" + isGift +
                ", productType=" + productType +
                ", productCategory=" + productCategory +
                '}';
    }
}
