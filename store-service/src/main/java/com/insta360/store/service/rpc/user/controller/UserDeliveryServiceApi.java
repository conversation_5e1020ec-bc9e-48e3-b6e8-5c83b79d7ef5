package com.insta360.store.service.rpc.user.controller;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.exception.CommonErrorCode;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.user.dto.UserDeliveryDTO;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserDelivery;
import com.insta360.store.business.user.service.UserDeliveryService;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import com.insta360.store.business.user.service.impl.helper.UserDeliveryHelper;
import com.insta360.store.service.rpc.user.vo.UserDeliveryRpcVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/09/26
 * @Description:
 */
@RestController
public class UserDeliveryServiceApi {

    @Autowired
    UserDeliveryService userDeliveryService;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    UserDeliveryHelper userDeliveryHelper;

    @Autowired
    UserAccountHelper userAccountHelper;

    /**
     * 列出用户的所有收货地址
     *
     * @param instaAccount
     * @return
     */
    @GetMapping("/rpc/store/service/user/delivery/listAll")
    public Response<? extends Map> listAll(@RequestParam Integer instaAccount) {
        // 收货地址信息
        List<UserDelivery> userDeliveries = userDeliveryService.listUserDeliveries(instaAccount);
        List<UserDeliveryRpcVO> userDeliveryVos = userDeliveries.stream().map(UserDeliveryRpcVO::new).collect(Collectors.toList());
        return Response.ok("userDeliveries", userDeliveryVos);
    }

    /**
     * 新增收货地址
     *
     * @param userDeliveryParam
     * @return
     */
    @PostMapping(path = "/rpc/store/service/user/delivery/addDelivery", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> addDelivery(@RequestBody UserDeliveryDTO userDeliveryParam) {
        CountryConfig countryConfig = countryConfigService.getByCountryText(userDeliveryParam.getCountry());
        if (countryConfig == null) {
            return Response.failed(CommonErrorCode.CountryNotSupportException);
        }

        // 防止客户把手机号填进了手机区号
        String phoneCode = userDeliveryParam.getPhoneCode();
        if (StringUtil.isNotBlank(phoneCode) && phoneCode.length() > 10) {
            return Response.failed(CommonErrorCode.InvalidParameterException);
        }

        // 历史原因，如果官网没传provinceCode进行一个兜底
        userDeliveryHelper.checkComplementFieldInformation(userDeliveryParam, InstaCountry.parse(countryConfig.getCountryCode()));

        // reset country code
        userDeliveryParam.setCountryCode(countryConfig.getCountryCode());
        UserDelivery userDelivery = userDeliveryService.addDelivery(userDeliveryParam.getInstaAccount(), userDeliveryParam.getPojoObject());
        UserDeliveryRpcVO userDeliveryRpcVo = new UserDeliveryRpcVO();
        userDeliveryRpcVo.setId(userDelivery.getId());
        return Response.ok(userDeliveryRpcVo);
    }

    /**
     * 更新收货地址
     *
     * @param userDeliveryParam
     * @return
     */
    @PostMapping(path = "/rpc/store/service/user/delivery/updateDelivery", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> updateDelivery(@RequestBody UserDeliveryDTO userDeliveryParam) {
        CountryConfig countryConfig = countryConfigService.getByCountryText(userDeliveryParam.getCountry());
        if (countryConfig == null) {
            return Response.failed(CommonErrorCode.CountryNotSupportException);
        }

        // 防止客户把手机号填进了手机区号
        String phoneCode = userDeliveryParam.getPhoneCode();
        if (StringUtil.isNotBlank(phoneCode) && phoneCode.length() > 10) {
            return Response.failed(CommonErrorCode.InvalidParameterException);
        }

        // 历史原因，如果官网没传provinceCode进行一个兜底
        userDeliveryHelper.checkComplementFieldInformation(userDeliveryParam, InstaCountry.parse(countryConfig.getCountryCode()));

        // reset country code
        userDeliveryParam.setCountryCode(countryConfig.getCountryCode());
        userDeliveryService.updateDelivery(userDeliveryParam.getInstaAccount(), userDeliveryParam.getPojoObject());
        return Response.ok();
    }

    /**
     * 删除收货地址
     *
     * @param userDeliveryParam
     * @return
     */
    @PostMapping(path = "/rpc/store/service/user/delivery/deleteDelivery", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<Object> deleteDelivery(@RequestBody UserDeliveryDTO userDeliveryParam) {
        // 调用删除接口表示一定存在收货地址，也就一定会存在商城账户
        StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(userDeliveryParam.getInstaAccount());
        if (storeAccount == null) {
            return Response.failed(CommonErrorCode.PermissionDeniedException);
        }

        // delete
        userDeliveryService.deleteDelivery(storeAccount.getInstaAccount(), userDeliveryParam.getId());
        return Response.ok();
    }
}
