package com.insta360.store.service.controller.prime.notify;

import com.alibaba.fastjson.JSON;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.prime.bo.PrimeWebhookNotifyBO;
import com.insta360.store.service.common.BaseApi;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * service/prime/notify
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/30
 */

@RestController
public class PrimeNotifyApi extends BaseApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrimeNotifyApi.class);


    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping("/store/bwp/notify/buyabilityChange")
    public void buyabilityChange(@RequestBody String payload) {
        LOGGER.info("接收到 bwp buyabilityChange参数回调 参数为:{}", payload);
        FeiShuMessageUtil.storeGeneralMessage(payload, FeiShuGroupRobot.DevNotice);
        PrimeWebhookNotifyBO primeWebhookNotifyBO = JSON.parseObject(payload, PrimeWebhookNotifyBO.class);
        LOGGER.info("接收到 bwp buyabilityChange参数回调 封装参数为:{}", primeWebhookNotifyBO);
        String idempotencyKey = primeWebhookNotifyBO.getIdempotencyKey();
        if (StringUtils.isBlank(idempotencyKey)) {
            LOGGER.error("接收到 bwp buyabilityChange参数回调 参数idempotencyKey为空");
            FeiShuMessageUtil.storeGeneralMessage("接收到 bwp buyabilityChange参数回调 参数idempotencyKey为空", FeiShuGroupRobot.InternalWarning, FeiShuAtUser.WXQ);
            return;
        }

    }
}
