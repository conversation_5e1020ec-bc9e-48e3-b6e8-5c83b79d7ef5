package com.insta360.store.service.controller.cloud.format;

import com.google.common.collect.Lists;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.cloud.bo.BenefitDiscountItemBO;
import com.insta360.store.business.cloud.bo.CloudSubscribeVerifyUpgradeResultBO;
import com.insta360.store.business.cloud.enums.*;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefit;
import com.insta360.store.business.cloud.model.CloudStorageStoreBenefitDetail;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.model.SubscribeBillingAddress;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitDetailService;
import com.insta360.store.business.cloud.service.CloudStorageStoreBenefitService;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.cloud.service.SubscribeBillingAddressService;
import com.insta360.store.business.cloud.service.impl.helper.CloudStorageOpenPlatformHelper;
import com.insta360.store.business.cloud.service.impl.helper.StoreBenefitCheckHelper;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.order.enums.OrderState;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderBillingAddress;
import com.insta360.store.business.order.model.OrderDelivery;
import com.insta360.store.business.order.service.OrderBillingAddressService;
import com.insta360.store.business.order.service.OrderDeliveryService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.rpc.cloud.dto.CloudUserConfig;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.UserPayInfoService;
import com.insta360.store.service.controller.cloud.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/16
 */
@Component
public class StoreBenefitPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(StoreBenefitPack.class);

    @Autowired
    private CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    private SubscribeBillingAddressService subscribeBillingAddressService;

    @Autowired
    private OrderBillingAddressService orderBillingAddressService;

    @Autowired
    private CloudStorageStoreBenefitDetailService cloudStorageStoreBenefitDetailService;

    @Autowired
    private CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    private CommodityInfoService commodityInfoService;

    @Autowired
    private CommodityPriceService commodityPriceService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderDeliveryService orderDeliveryService;

    @Autowired
    private UserPayInfoService userPayInfoService;

    @Autowired
    private CloudStorageOpenPlatformHelper cloudStorageOpenPlatformHelper;

    @Autowired
    private StoreBenefitCheckHelper storeBenefitCheckHelper;

    /**
     * 封装订阅折扣商品明细vo
     *
     * @param benefitDiscountItemList
     * @return
     */
    public BenefitDiscountInfoVO doPackBenefitDiscountInfo(List<BenefitDiscountItemBO> benefitDiscountItemList) {
        if (CollectionUtils.isEmpty(benefitDiscountItemList)) {
            return new BenefitDiscountInfoVO();
        }

        List<BenefitDiscountItemVO> benefitDiscountItemVoList = benefitDiscountItemList.stream()
                .map(benefitDiscountItem -> new BenefitDiscountItemVO(benefitDiscountItem))
                .collect(Collectors.toList());

        // 总优惠金额
        BigDecimal totalDiscountAmount = benefitDiscountItemList.stream()
                .map(BenefitDiscountItemBO::getBenefitDiscountTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 封装VO
        BenefitDiscountInfoVO benefitDiscountInfo = new BenefitDiscountInfoVO();
        benefitDiscountInfo.setTotalDiscountAmount(totalDiscountAmount);
        benefitDiscountInfo.setBenefitDiscountItems(benefitDiscountItemVoList);
        return benefitDiscountInfo;
    }

    /**
     * 封装用户云服务订阅及权益相关信息
     *
     * @param storeAccount
     * @param country
     * @param language
     * @return
     */
    public StoreCloudStorageInfoVO doPackStoreBenefitInfo(StoreAccount storeAccount, InstaCountry country, InstaLanguage language) {
        // 查询该用户是否存在云存储订阅权益记录
        CloudStorageStoreBenefit storeBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(storeAccount.getInstaAccount());
        if (Objects.isNull(storeBenefit)) {
            // 如果没有云存储订阅权益记录，返回成功响应但不包含权益信息
            return new StoreCloudStorageInfoVO();
        }

        StoreCloudStorageInfoVO storeCloudStorageInfo = new StoreCloudStorageInfoVO();
        // 判断权益是否已过期或者状态为已过期，如果是，则设置权益状态为已过期并返回
        if (storeBenefit.isExpired()) {
            storeCloudStorageInfo.setBenefitStatus(BenefitStatus.EXPIRED.getName());
            return storeCloudStorageInfo;
        }
        // 设置权益状态为激活状态
        storeCloudStorageInfo.setBenefitStatus(BenefitStatus.ACTIVE.getName());
        storeCloudStorageInfo.setPlatformSource(storeBenefit.getPlatformSource());
        storeCloudStorageInfo.setNextOneCameraMark(storeBenefit.getNextOneCameraDiscount());

        // 封装店铺特殊权益明细信息到返回对象中
        this.doPackInfo(storeCloudStorageInfo, storeBenefit);
        // 封装商城云服务相关信息
        storeCloudStorageInfo.setCloudSubscribeInfo(this.doPackCloudSubscribeInfo(storeAccount, country, language));
        // 封装账户所属地，需走云服务rpc
        CloudUserConfig userConfig = cloudStorageOpenPlatformHelper.getUserConfig(storeBenefit.getUserId());
        if (Objects.nonNull(userConfig)) {
            storeCloudStorageInfo.setStorageRegion(userConfig.getStoreRegion());
        }
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(storeAccount.getInstaAccount());
        if (Objects.nonNull(userPayInfo)) {
            storeCloudStorageInfo.setNotPayIdMark(StringUtils.isBlank(userPayInfo.getPayId()));
        }

        return storeCloudStorageInfo;
    }

    /**
     * 封装用户云服务订阅相关信息
     *
     * @param storeAccount
     * @param country
     * @param language
     * @return
     */
    private CloudSubscribeInfoVO doPackCloudSubscribeInfo(StoreAccount storeAccount, InstaCountry country, InstaLanguage language) {
        // 用户云服务订阅信息
        CloudStorageSubscribe cloudStorageSubscribe = cloudStorageSubscribeService.getByInstaAccountAndSubscribe(storeAccount.getInstaAccount());
        if (Objects.isNull(cloudStorageSubscribe)) {
            return new CloudSubscribeInfoVO();
        }

        // 获取用户云服务订阅订单号
        Order order = orderService.getByOrderNumber(cloudStorageSubscribe.getOrderNumber());
        // 获取用户订阅支付相关信息
        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(cloudStorageSubscribe.getInstaAccount());

        // 封装云服务订阅相关信息
        CloudSubscribeInfoVO cloudSubscribeInfo = new CloudSubscribeInfoVO();
        cloudSubscribeInfo.setAutoRenewal(SubscribeStatus.SUBSCRIBE_ING.equals(cloudStorageSubscribe.parseSubscribeStatus()));
        cloudSubscribeInfo.setAutoDeduction(SubscribeSubStatus.RENEW_DEDUCTION_ING.equals(SubscribeSubStatus.parse(cloudStorageSubscribe.getSubscribeSubState())));
        cloudSubscribeInfo.setCloudSubscribeCommodity(this.doPackCloudSubscribeCommodity(language, cloudStorageSubscribe));
        cloudSubscribeInfo.setCloudSubscribePaymentInfo(this.doPackPaymentInfo(userPayInfo, cloudStorageSubscribe));
        cloudSubscribeInfo.setSubscribeBillingAddress(this.doPackBillingAddress(order, userPayInfo));
        cloudSubscribeInfo.setSubscribeOrderAddress(this.doPackOrderAddress(order));
        cloudSubscribeInfo.setVerifyUpgradeQualificationResult(this.doPackVerifyUpgradeQualificationResult(storeAccount, country));
        cloudSubscribeInfo.setExpireTime(cloudStorageSubscribe.getExpireTime());
        cloudSubscribeInfo.setSubscribeDay(cloudStorageSubscribe.getStartTime());
        cloudSubscribeInfo.setNextDeductDay(cloudStorageSubscribe.getNextDeductDay());

        return cloudSubscribeInfo;
    }

    /**
     * 封装云服务订阅升级资格校验结果
     *
     * @param storeAccount
     * @param country
     * @return
     */
    private VerifyUpgradeQualificationResultVO doPackVerifyUpgradeQualificationResult(StoreAccount storeAccount, InstaCountry country) {
        // 获取升级资格校验结果
        CloudSubscribeVerifyUpgradeResultBO cloudSubscribeVerifyUpgradeResultBo = storeBenefitCheckHelper.verifyUpgrade(storeAccount, country);
        if (Objects.isNull(cloudSubscribeVerifyUpgradeResultBo)) {
            return new VerifyUpgradeQualificationResultVO();
        }

        VerifyUpgradeQualificationResultVO verifyUpgradeQualificationResult = new VerifyUpgradeQualificationResultVO();
        verifyUpgradeQualificationResult.setCanUpgrade(cloudSubscribeVerifyUpgradeResultBo.getCanUpgrade());
        verifyUpgradeQualificationResult.setBusinessCode(cloudSubscribeVerifyUpgradeResultBo.getBusinessCode());

        return verifyUpgradeQualificationResult;
    }

    /**
     * 封装订单地址
     *
     * @param order
     * @return
     */
    private CloudSubscribeOrderAddressVO doPackOrderAddress(Order order) {
        if (Objects.isNull(order)) {
            return new CloudSubscribeOrderAddressVO();
        }

        OrderDelivery orderDelivery = orderDeliveryService.getOrderDelivery(order.getId());
        if (Objects.isNull(orderDelivery)) {
            return new CloudSubscribeOrderAddressVO();
        }

        CloudSubscribeOrderAddressVO cloudSubscribeOrderAddress = new CloudSubscribeOrderAddressVO();
        BeanUtils.copyProperties(orderDelivery, cloudSubscribeOrderAddress);

        return cloudSubscribeOrderAddress;
    }

    /**
     * 封装订阅最新账单地址
     *
     * @param order
     * @param userPayInfo
     * @return
     */
    private CloudSubscribeBillingAddressVO doPackBillingAddress(Order order, UserPayInfo userPayInfo) {
        // 获取用户订阅支付相关信息
        CloudSubscribeBillingAddressVO subscribeBillingAddressVo = new CloudSubscribeBillingAddressVO();
        if (Objects.isNull(userPayInfo) || Objects.isNull(order)) {
            return subscribeBillingAddressVo;
        }

        // 优先拿用户更新的账单地址
        SubscribeBillingAddress subscribeBillingAddress = subscribeBillingAddressService.getByInstaAccount(userPayInfo.getInstaAccount());
        if (Objects.nonNull(subscribeBillingAddress)) {
            BeanUtil.copyProperties(subscribeBillingAddress, subscribeBillingAddressVo);
            return subscribeBillingAddressVo;
        }

        OrderBillingAddress orderBillingAddress = orderBillingAddressService.getOrderBillingAddressByOrderId(order.getId());
        BeanUtil.copyProperties(orderBillingAddress, subscribeBillingAddressVo);
        return subscribeBillingAddressVo;
    }

    /**
     * 封装云服务套餐信息
     *
     * @param language
     * @param cloudStorageSubscribe
     * @return
     */
    private CloudSubscribeCommodityVO doPackCloudSubscribeCommodity(InstaLanguage language, CloudStorageSubscribe cloudStorageSubscribe) {
        // 云服务套餐ID
        Integer commodityId = cloudStorageSubscribe.getCommodityId();
        // 关联的云服务订单
        String orderNumber = cloudStorageSubscribe.getOrderNumber();

        Order order = orderService.getByOrderNumber(orderNumber);
        if (Objects.isNull(order)) {
            throw new InstaException(CloudSubscribeErrorCode.NotExistSubscribeOrderException);
        }

        // 云服务套餐相关信息
        CloudSubscribeCommodityVO cloudSubscribeCommodity = new CloudSubscribeCommodityVO();
        cloudSubscribeCommodity.setCommodityId(commodityId);
        // 云服务套餐多语言明细
        CommodityInfo commodityInfo = commodityInfoService.getInfo(commodityId, language);
        if (Objects.nonNull(commodityInfo)) {
            cloudSubscribeCommodity.setCommodityName(commodityInfo.getName());
        }
        // 云服务套餐价格配置
        CommodityPrice commodityPrice = commodityPriceService.getPrice(commodityId, order.country());
        if (Objects.nonNull(commodityPrice)) {
            cloudSubscribeCommodity.setDeductPrice(commodityPrice.price());
            cloudSubscribeCommodity.setOriginPrice(commodityPrice.originPrice());
        }

        return cloudSubscribeCommodity;
    }

    /**
     * 封装云服务订阅支付信息。
     *
     * @param userPayInfo
     * @param cloudStorageSubscribe
     */
    private CloudSubscribePaymentInfoVO doPackPaymentInfo(UserPayInfo userPayInfo, CloudStorageSubscribe cloudStorageSubscribe) {
        if (Objects.isNull(userPayInfo)) {
            return new CloudSubscribePaymentInfoVO();
        }

        // 支付方式
        String paymentMethod = userPayInfo.getPaymentMethod();
        // 扣款类别
        String deductCategory = userPayInfo.getDeductCategory();
        // 支付方式枚举
        StorePaymentMethodEnum paymentMethodEnum = userPayInfo.parsePaymentMethod();
        // Apple pay & Google pay 也属于cko支付方式
        if (StorePaymentMethodEnum.CKO_PAYMENT.equals(paymentMethodEnum) && StringUtils.isNotBlank(userPayInfo.getPaymentSubMethod())) {
            paymentMethod = userPayInfo.getPaymentSubMethod();
        }

        // CKO & OCEAN 特殊处理卡号，只外漏后四位
        if (Lists.newArrayList(StorePaymentMethodEnum.OCEAN_PAYMENT, StorePaymentMethodEnum.CKO_PAYMENT).contains(paymentMethodEnum)) {
            deductCategory = StringUtils.substringAfterLast(userPayInfo.getDeductCategory(), "*");
        }

        CloudSubscribePaymentInfoVO cloudSubscribePaymentInfo = new CloudSubscribePaymentInfoVO();
        cloudSubscribePaymentInfo.setPaymentMethod(paymentMethod);
        cloudSubscribePaymentInfo.setDeductCategory(deductCategory);
        cloudSubscribePaymentInfo.setCardType(userPayInfo.getCardType());
        cloudSubscribePaymentInfo.setFailRenewOrder(packFailRenewOrder(userPayInfo, cloudStorageSubscribe));

        return cloudSubscribePaymentInfo;
    }

    /**
     * 封装续费失败订单
     *
     * @param userPayInfo
     * @param cloudStorageSubscribe
     * @return
     */
    private String packFailRenewOrder(UserPayInfo userPayInfo, CloudStorageSubscribe cloudStorageSubscribe) {
        // 不在24h续费周期内，提前结束
        long expireTime = cloudStorageSubscribe.getExpireTime() - System.currentTimeMillis();
        if (!(expireTime < 24 * 3600 * 1000 && expireTime > 0)) {
            LOGGER.info(String.format("不再续费周期不允许扣款，订阅信息{%s}", cloudStorageSubscribe));
            return null;
        }

        Order renewOrder = orderService.getCloudSubscribeRenewOrder(cloudStorageSubscribe.getInstaAccount(), OrderState.init.getCode());
        if (Objects.isNull(renewOrder)) {
            LOGGER.info(String.format("续费订单为空，userId{%s}", cloudStorageSubscribe.getInstaAccount()));
            return null;
        }
        return renewOrder.getOrderNumber();
    }

    /**
     * 处理商城特殊权益信息。
     *
     * @param storeCloudStorageInfo
     * @param storeBenefit
     */
    private void doPackInfo(StoreCloudStorageInfoVO storeCloudStorageInfo, CloudStorageStoreBenefit storeBenefit) {
        CloudStorageStoreBenefitDetail storeBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.EXTEND.getType());
        if (Objects.nonNull(storeBenefitDetail) && !storeBenefitDetail.getExpired()) {
            StoreBenefitInfoVO benefitInfo = new StoreBenefitInfoVO();
            BeanUtils.copyProperties(storeBenefitDetail, benefitInfo);
            storeCloudStorageInfo.setExtendBenefitInfo(benefitInfo);
        }

        // 订阅类型
        SkuSubscribeType skuSubscribeType = storeBenefit.parseSkuSubscribeType();
        if (SkuSubscribeType.YEARLY.equals(skuSubscribeType)) {
            // care 权益
            CloudStorageStoreBenefitDetail careBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.CARE.getType());
            if (Objects.nonNull(careBenefitDetail) && !careBenefitDetail.getExpired()) {
                StoreBenefitInfoVO benefitInfo = new StoreBenefitInfoVO();
                BeanUtils.copyProperties(careBenefitDetail, benefitInfo);
                storeCloudStorageInfo.setCareBenefitInfo(benefitInfo);
            }
            // 配件折扣 权益
            CloudStorageStoreBenefitDetail discountBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.ACCESSORIES_DISCOUNT.getType());
            if (Objects.nonNull(discountBenefitDetail) && !discountBenefitDetail.getExpired()) {
                StoreBenefitInfoVO benefitInfo = new StoreBenefitInfoVO();
                BeanUtils.copyProperties(discountBenefitDetail, benefitInfo);
                storeCloudStorageInfo.setDiscountBenefitInfo(benefitInfo);
            }
        }

        // 设置容量类型
        Optional.ofNullable(storeBenefit.parseCapacityType())
                .ifPresent(capacityType -> storeCloudStorageInfo.setCapacityType(capacityType.getCode()));
    }
}
