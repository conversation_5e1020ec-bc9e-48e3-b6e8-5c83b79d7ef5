package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.business.meta.model.AvalaraTaxProvince;
import com.insta360.store.business.meta.service.AvalaraTaxProvinceService;
import com.insta360.store.service.controller.meta.vo.AvalaraTaxProvinceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/11/15
 * @Description:
 */
@Component
public class AvalaraTaxCachePutPack {

    @Autowired
    AvalaraTaxProvinceService avalaraTaxProvinceService;

    /**
     * 缓存 avalara tax 省份信息
     *
     * @param instaCountry
     * @return
     */
    @CachePut(value = CacheableType.AVALARA_TAX_PROVINCE, key = "caches[0].name  + '-AvalaraTaxProvincePack-' + methodName + '-countryCode-' + #instaCountry")
    public List<AvalaraTaxProvinceVO> doPackListAvalaraTaxProvince(InstaCountry instaCountry) {
        List<AvalaraTaxProvince> avalaraTaxProvinceList = avalaraTaxProvinceService.listObtainTaxProvinces(instaCountry);
        return avalaraTaxProvinceList.stream().map(AvalaraTaxProvinceVO::new).collect(Collectors.toList());
    }
}
