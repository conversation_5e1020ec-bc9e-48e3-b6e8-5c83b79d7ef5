package com.insta360.store.service.controller.meta.cache.cacheable;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.SeoConfigPack;
import com.insta360.store.service.controller.meta.vo.SeoConfigVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/11/15
 * @Description:
 */
@Component
public class SeoCachePack {

    @Autowired
    SeoConfigPack seoConfigPack;

    /**
     * seo 缓存
     *
     * @param language
     * @return
     */
    @CacheTtlMonitor(value = CacheableType.SEO_INFO, cacheKey = "'-SeoCachePack-' + methodName + '-language-' + #language")
    @Cacheable(value = CacheableType.SEO_INFO, key = "caches[0].name + '-SeoCachePack-' + methodName + '-language-' + #language")
    public List<SeoConfigVO> seoConfigPack(InstaLanguage language) {
        return seoConfigPack.seoConfigPack(language);
    }
}
