package com.insta360.store.service.controller.search.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/4
 */
public class SearchDocumentVO implements Serializable {

    /**
     * 主表ID
     */
    private Long documentId;

    /**
     * 产品
     */
    private ProductResultVO product;

    /**
     * 套餐
     */
    private CommodityResultVO commodity;

    /**
     * 套餐价格
     */
    private CommodityPriceResultVO commodityPrice;

    /**
     * 过滤器信息
     */
    private FilterInfoBaseVO filterInfo;

    /**
     * 新品标签
     */
    private Boolean newTagInfo;

    /**
     * 是否缺货（spu）
     */
    private Boolean outOfStockTag;

    /**
     * 语言
     */
    private String language;

    /**
     * 国家/地区
     */
    private String country;

    public ProductResultVO getProduct() {
        return product;
    }

    public void setProduct(ProductResultVO product) {
        this.product = product;
    }

    public CommodityResultVO getCommodity() {
        return commodity;
    }

    public void setCommodity(CommodityResultVO commodity) {
        this.commodity = commodity;
    }

    public CommodityPriceResultVO getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(CommodityPriceResultVO commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public FilterInfoBaseVO getFilterInfo() {
        return filterInfo;
    }

    public void setFilterInfo(FilterInfoBaseVO filterInfo) {
        this.filterInfo = filterInfo;
    }

    public Boolean getNewTagInfo() {
        return newTagInfo;
    }

    public void setNewTagInfo(Boolean newTagInfo) {
        this.newTagInfo = newTagInfo;
    }

    public Boolean getOutOfStockTag() {
        return outOfStockTag;
    }

    public void setOutOfStockTag(Boolean outOfStockTag) {
        this.outOfStockTag = outOfStockTag;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Override
    public String toString() {
        return "SearchDocumentVO{" +
                "documentId=" + documentId +
                ", product=" + product +
                ", commodity=" + commodity +
                ", commodityPrice=" + commodityPrice +
                ", filterInfo=" + filterInfo +
                ", newTagInfo=" + newTagInfo +
                ", outOfStockTag=" + outOfStockTag +
                ", language='" + language + '\'' +
                ", country='" + country + '\'' +
                '}';
    }
}
