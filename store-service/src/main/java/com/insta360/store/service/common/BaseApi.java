package com.insta360.store.service.common;

import com.insta360.compass.core.common.BaseController;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.prerelease.context.ProductDataPreReleaseContext;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.config.StoreConfiguration;
import com.insta360.store.service.controller.product.format.CommodityPack;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: mowi
 * @Date: 2018/11/26
 * @Description:
 */
public class Base<PERSON><PERSON> extends BaseController {

    @Autowired
    StoreConfiguration storeConfiguration;

    @Autowired
    StoreApiHeaderParser storeApiHeaderParser;

    public StoreAccount getAccessUser() {
        WebApiContext apiContext = WebApiContext.get();
        if (apiContext != null) {
            return apiContext.getAccessUser();
        } else {
            return null;
        }
    }

    @Override
    public InstaLanguage getApiLanguage() {
        InstaLanguage language = super.getApiLanguage();
        return language != null ? language : storeConfiguration.getDefaultLanguage();
    }

    @Override
    public InstaCountry getApiCountry() {
        InstaCountry country = super.getApiCountry();
        return country != null ? country : storeConfiguration.getDefaultCountry();
    }

    /**
     * 默认语言配置
     *
     * @return
     */
    protected InstaLanguage getDefaultLanguage() {
        return storeConfiguration.getDefaultLanguage();
    }

    /**
     * 分销码
     *
     * @return
     */
    public String getResellerCode() {
        return storeApiHeaderParser.parseHeaderValue(request, StoreApiHeader.ResellerCode);
    }

    /**
     * 限时推广分销码
     *
     * @return
     */
    public String getFlashPromoCode() {
        return storeApiHeaderParser.parseHeaderValue(request, StoreApiHeader.ResellerFlashPromoCode);
    }

    /**
     * 获取新品数据是否展示标识位
     *
     * @return
     */
    public Boolean getProductNewDataDisplay() {
        return ProductDataPreReleaseContext.get().getDisplay();
    }

    /**
     * 获取用户应用信息
     *
     * @return
     */
    public String getUserAgent() {
        return storeApiHeaderParser.parseHeaderValue(request, StoreApiHeader.UserAgent);
    }

    protected CommodityPack.PackSetting getCommoditySetting() {
        CommodityPack.PackSetting setting = new CommodityPack.PackSetting(this);
        setting.setWithCommodityInfo(true);
        setting.setWithPrice(true);
        setting.setWithDisplay(true);
        setting.setWithTradeRule(true);
        setting.setWithStock(true);
        setting.setWithClimbService(true);
        setting.setWithServiceType(true);
        setting.setWithSaleState(true);

        setting.setWithProductUrlKey(true);
        setting.setWithProductName(true);
        setting.setWithDeliveryTimeText(true);
        setting.setWithProductDescription(true);
        setting.setWithDeliveryTimeConfig(true);
        return setting;
    }
}
