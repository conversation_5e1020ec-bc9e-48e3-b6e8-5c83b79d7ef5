package com.insta360.store.service.common.interceptor;

import com.insta360.store.business.configuration.trace.TraceLog;
import com.insta360.store.business.configuration.trace.TraceLogContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * TraceLog拦截器
 *
 * <AUTHOR>
 * @date 2025/06/04
 */
public class TraceLogInterceptor extends HandlerInterceptorAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(TraceLogInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return super.preHandle(request, response, handler);
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        TraceLog businessLog = handlerMethod.getMethodAnnotation(TraceLog.class);
        if (businessLog == null) {
            LOGGER.info("[TraceLog]未获取到注解信息. handlerMethod:{}", handlerMethod);
            return super.preHandle(request, response, handler);
        }
        // 获取前端traceID
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);

        if (!(handler instanceof HandlerMethod)) {
            return;
        }
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        TraceLog businessLog = handlerMethod.getMethodAnnotation(TraceLog.class);
        if (businessLog == null) {
            LOGGER.info("[TraceLog]未获取到注解信息. handlerMethod:{}", handlerMethod);
            return;
        }

        TraceLogContext.clear();
    }
}
