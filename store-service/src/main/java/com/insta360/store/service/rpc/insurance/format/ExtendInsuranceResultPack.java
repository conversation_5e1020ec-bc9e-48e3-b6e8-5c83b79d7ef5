package com.insta360.store.service.rpc.insurance.format;

import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.insurance.model.ExtendInsurance;
import com.insta360.store.business.insurance.service.ExtendInsuranceService;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceHelper;
import com.insta360.store.business.insurance.service.impl.helper.http.DeviceInfoHelper;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceActivationInfo;
import com.insta360.store.service.rpc.insurance.vo.ExtendInsuranceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/4/13
 * @Description:
 */
@Component
public class ExtendInsuranceResultPack {

    private static Logger logger = LoggerFactory.getLogger(ExtendInsuranceResultPack.class);

    @Autowired
    ExtendInsuranceService extendInsuranceService;

    @Autowired
    DeviceInfoHelper deviceInfoHelper;

    @Autowired
    InsuranceHelper insuranceHelper;

    /**
     * 封装延保数据返回（app）
     *
     * @param serials
     * @param area
     * @return
     */
    public List<ExtendInsuranceVO> packExtendInsuranceBySerials(List<String> serials, String area) {
        List<ExtendInsurance> extendInsuranceList = extendInsuranceService.getBySerials(serials);
        List<ExtendInsuranceVO> extendInsuranceVOList = extendInsuranceList.stream().map(extendInsurance -> {
            ExtendInsuranceVO extendInsuranceVO = new ExtendInsuranceVO();
            extendInsuranceVO.setDeviceSerial(extendInsurance.getDeviceSerial());
            extendInsuranceVO.setExpireTime(extendInsurance.getExpireTime());
            extendInsuranceVO.setIncludeGifts(extendInsurance.getIncludeGifts());
            extendInsuranceVO.setIsBuy(StringUtil.isNotBlank(extendInsurance.getOrderNumber()));
            return extendInsuranceVO;
        }).collect(Collectors.toList());
        List<String> extendSerials = extendInsuranceList.stream().map(ExtendInsurance::getDeviceSerial).collect(Collectors.toList());
        serials.removeAll(extendSerials);
        // 封装默认时间
        if (!CollectionUtils.isEmpty(serials)) {
            List<ExtendInsuranceVO> defaultExtendInsuranceList = serials.stream().map(serial -> {
                DeviceActivationInfo activationInfo = deviceInfoHelper.getDeviceActivationInfo(serial);
                logger.info("app rpc activationInfo:" + activationInfo);
                if (activationInfo == null) {
                    return null;
                }
                ExtendInsuranceVO extendInsuranceVO = new ExtendInsuranceVO();
                // 欧盟地区默认2年、其他1年
                if (insuranceHelper.listEuropeArea().contains(area)) {
                    extendInsuranceVO.setExpireTime(activationInfo.getCreateTime().plusYears(2));
                } else {
                    extendInsuranceVO.setExpireTime(activationInfo.getCreateTime().plusYears(1));
                }
                extendInsuranceVO.setDeviceSerial(serial);
                extendInsuranceVO.setIncludeGifts(false);
                extendInsuranceVO.setIsBuy(false);
                return extendInsuranceVO;
            }).collect(Collectors.toList());
            extendInsuranceVOList.addAll(defaultExtendInsuranceList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }
        return extendInsuranceVOList;
    }

    /**
     * 封装延保数据返回（工单）
     *
     * @param serial
     * @param area
     * @return
     */
    public ExtendInsuranceVO packExtendInsuranceBySerial(String serial, String area) {
        ExtendInsurance extendInsurance = extendInsuranceService.getBySerial(serial);
        if (extendInsurance != null) {
            ExtendInsuranceVO extendInsuranceVO = new ExtendInsuranceVO();
            extendInsuranceVO.setDeviceSerial(extendInsurance.getDeviceSerial());
            extendInsuranceVO.setExpireTime(extendInsurance.getExpireTime());
            extendInsuranceVO.setIsBuy(StringUtil.isNotBlank(extendInsurance.getOrderNumber()));
            return extendInsuranceVO;
        }

        // 封装默认时间
        DeviceActivationInfo activationInfo = deviceInfoHelper.getDeviceActivationInfo(serial);
        logger.info("repair rpc activationInfo:" + activationInfo);
        if (activationInfo == null) {
            return null;
        }
        ExtendInsuranceVO extendInsuranceVO = new ExtendInsuranceVO();
        extendInsuranceVO.setDeviceSerial(serial);
        // 欧盟地区默认2年、其他1年
        if (insuranceHelper.listEuropeArea().contains(area)) {
            extendInsuranceVO.setExpireTime(activationInfo.getCreateTime().plusYears(2));
        } else {
            extendInsuranceVO.setExpireTime(activationInfo.getCreateTime().plusYears(1));
        }
        extendInsuranceVO.setIsBuy(false);
        return extendInsuranceVO;
    }
}
