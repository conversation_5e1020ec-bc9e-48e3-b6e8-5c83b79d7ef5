package com.insta360.store.service.controller.meta.vo;

import cn.hutool.core.bean.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategoryThird;
import com.insta360.store.service.controller.product.vo.CommodityTagInfoVO;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * @Author: py
 * @Date: 2023-11-14
 * @Description:
 */
public class NavigationBarCategoryThirdVO implements Serializable {

    /**
     * 二级导航id
     */
    private Integer subsetId;

    /**
     * 三级导航多语言名称
     */
    private String thirdName;

    /**
     * 三级导航内部名称
     */
    private String thirdInsideName;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * view more跳转链接
     */
    private String urlLink;

    /**
     * 图片链接
     */
    private String imgUrl;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 新品标签
     */
    private Boolean newTag;

    public NavigationBarCategoryThirdVO() {
    }

    public NavigationBarCategoryThirdVO(NavigationBarCategoryThird navigationBarCategoryThird) {
        if (Objects.nonNull(navigationBarCategoryThird)) {
            BeanUtil.copyProperties(navigationBarCategoryThird, this);
        }
    }

    public Integer getSubsetId() {
        return subsetId;
    }

    public void setSubsetId(Integer subsetId) {
        this.subsetId = subsetId;
    }

    public String getThirdName() {
        return thirdName;
    }

    public void setThirdName(String thirdName) {
        this.thirdName = thirdName;
    }

    public String getThirdInsideName() {
        return thirdInsideName;
    }

    public void setThirdInsideName(String thirdInsideName) {
        this.thirdInsideName = thirdInsideName;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public void setUrlLink(String urlLink) {
        this.urlLink = urlLink;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getNewTag() {
        return newTag;
    }

    public void setNewTag(Boolean newTag) {
        this.newTag = newTag;
    }

    @Override
    public String toString() {
        return "NavigationBarCategoryThirdVO{" +
                "subsetId=" + subsetId +
                ", thirdName='" + thirdName + '\'' +
                ", thirdInsideName='" + thirdInsideName + '\'' +
                ", commodityId=" + commodityId +
                ", urlLink='" + urlLink + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", orderIndex=" + orderIndex +
                ", newTag=" + newTag +
                '}';
    }
}