package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.service.controller.meta.cache.cacheput.CategoryPageCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wkx
 * @Date: 2023/11/23
 * @Description:
 */
@RestController
public class CategoryPageCachePutApi {

    @Autowired
    CategoryPageCachePutPack categoryPageCachePutPack;

    /**
     * 全部类目页筛选器缓存
     *
     * @param country
     * @param language
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/meta/cp/listAllCategoryFilter")
    public void listAllCategoryFilter(@RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        categoryPageCachePutPack.listCategoryFilter(ProductCategoryMainType.CM_ACCESSORY.name(), country, language);
        categoryPageCachePutPack.listCategoryFilter(ProductCategoryMainType.CM_VIRTUAL_SERVICE.name(), country, language);
    }

    /**
     * 类目页筛选器缓存
     *
     * @param categoryKey
     * @param country
     * @param language
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/meta/cp/listCategoryFilter")
    public void listCategoryFilter(@RequestParam String categoryKey, @RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        categoryPageCachePutPack.listCategoryFilter(categoryKey, country, language);
    }

    /**
     * 全部类目页缓存
     *
     * @param language
     * @param country
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/meta/cp/listAllCategory")
    public void listAllCategory(@RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        categoryPageCachePutPack.packCategoryPage(ProductCategoryMainType.CM_ACCESSORY.name(), country, language);
        categoryPageCachePutPack.packCategoryPage(ProductCategoryMainType.CM_VIRTUAL_SERVICE.name(), country, language);
    }

    /**
     * 类目页缓存
     *
     * @param categoryKey
     * @param language
     * @param country
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/meta/cp/listCategory")
    public void listCategory(@RequestParam String categoryKey, @RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        categoryPageCachePutPack.packCategoryPage(categoryKey, country, language);
    }
}
