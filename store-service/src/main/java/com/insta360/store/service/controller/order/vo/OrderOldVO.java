package com.insta360.store.service.controller.order.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.admin.order.print.bo.PrintRuleCheckResultBO;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderInvoice;
import com.insta360.store.business.order.model.OrderStateRecord;
import com.insta360.store.service.controller.tradeup.vo.TradeupOrderVO;

import java.util.List;

/**
 * @Author: hyc
 * @Date: 2019/2/18
 * @Description:
 */
public class OrderOldVO extends Order {

    @JSONField(name = "is_guest_order")
    private Boolean isGuestOrder;

    private List<OrderItemVO> items;

    private OrderDeliveryVO delivery;

    private OrderInvoice invoice;

    private OrderPaymentVO payment;

    @JSONField(name = "allow_refund")
    private Boolean allowRefund;

    @JSONField(name = "trade_up_order")
    private TradeupOrderVO tradeupOrder;

    @JSONField(name = "order_delivery_part")
    private List<OrderDeliveryPartlyVO> orderDeliveryPartlies;

    @JSONField(name = "order_state_records")
    private List<OrderStateRecord> orderStateRecords;

    /**
     * 是否允许售后申请 默认：true
     */
    private boolean refundApply = true;

    /**
     * 海外订单发票按钮
     */
    private PrintRuleCheckResultBO printRuleCheckResult;

    public PrintRuleCheckResultBO getPrintRuleCheckResult() {
        return printRuleCheckResult;
    }

    public void setPrintRuleCheckResult(PrintRuleCheckResultBO printRuleCheckResult) {
        this.printRuleCheckResult = printRuleCheckResult;
    }

    public OrderOldVO() {
    }

    public OrderOldVO(Order order) {
        if (order != null) {
            BeanUtil.copyProperties(order, this);
        }
    }

    public boolean isRefundApply() {
        return refundApply;
    }

    public void setRefundApply(boolean refundApply) {
        this.refundApply = refundApply;
    }

    public Boolean getIsGuestOrder() {
        return isGuestOrder;
    }

    public void setIsGuestOrder(Boolean guestOrder) {
        isGuestOrder = guestOrder;
    }

    public List<OrderItemVO> getItems() {
        return items;
    }

    public void setItems(List<OrderItemVO> items) {
        this.items = items;
    }

    public OrderDeliveryVO getDelivery() {
        return delivery;
    }

    public void setDelivery(OrderDeliveryVO delivery) {
        this.delivery = delivery;
    }

    public OrderInvoice getInvoice() {
        return invoice;
    }

    public void setInvoice(OrderInvoice invoice) {
        this.invoice = invoice;
    }

    public OrderPaymentVO getPayment() {
        return payment;
    }

    public void setPayment(OrderPaymentVO payment) {
        this.payment = payment;
    }

    public Boolean getAllowRefund() {
        return allowRefund;
    }

    public void setAllowRefund(Boolean allowRefund) {
        this.allowRefund = allowRefund;
    }

    public TradeupOrderVO getTradeupOrder() {
        return tradeupOrder;
    }

    public void setTradeupOrder(TradeupOrderVO tradeupOrder) {
        this.tradeupOrder = tradeupOrder;
    }

    public List<OrderDeliveryPartlyVO> getOrderDeliveryPartlies() {
        return orderDeliveryPartlies;
    }

    public void setOrderDeliveryPartlies(List<OrderDeliveryPartlyVO> orderDeliveryPartlies) {
        this.orderDeliveryPartlies = orderDeliveryPartlies;
    }

    public List<OrderStateRecord> getOrderStateRecords() {
        return orderStateRecords;
    }

    public void setOrderStateRecords(List<OrderStateRecord> orderStateRecords) {
        this.orderStateRecords = orderStateRecords;
    }
}
