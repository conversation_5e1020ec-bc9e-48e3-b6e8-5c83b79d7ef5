package com.insta360.store.service.controller.meta.vo;

import com.insta360.store.business.meta.bo.Price;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2021/09/16
 * @Description:
 */
public class HomeItemPriceVO implements Serializable {

    /**
     * 现价
     */
    private Price price;

    /**
     * 原价
     */
    private Price originPrice;

    /**
     * 节省金
     */
    private Price savePrice;

    /**
     * 最大折扣力度
     */
    private Price maxDiscount;

    /**
     * 控制展示sku/spu的价格(默认false-sku，true-spu)
     */
    private Boolean showSpuPrice;

    /**
     * 控制展示缺货标签
     * Out of stock：SPU纬度展示（需所有SKU都为缺货状态）
     */
    private Boolean showOutOfStock;

    public Price getPrice() {
        return price;
    }

    public void setPrice(Price price) {
        this.price = price;
    }

    public Price getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Price originPrice) {
        this.originPrice = originPrice;
    }

    public Price getSavePrice() {
        return savePrice;
    }

    public void setSavePrice(Price savePrice) {
        this.savePrice = savePrice;
    }

    public Price getMaxDiscount() {
        return maxDiscount;
    }

    public void setMaxDiscount(Price maxDiscount) {
        this.maxDiscount = maxDiscount;
    }

    public Boolean getShowSpuPrice() {
        return showSpuPrice;
    }

    public void setShowSpuPrice(Boolean showSpuPrice) {
        this.showSpuPrice = showSpuPrice;
    }

    public Boolean getShowOutOfStock() {
        return showOutOfStock;
    }

    public void setShowOutOfStock(Boolean showOutOfStock) {
        this.showOutOfStock = showOutOfStock;
    }

    @Override
    public String toString() {
        return "HomeItemPriceVO{" +
                "price=" + price +
                ", originPrice=" + originPrice +
                ", savePrice=" + savePrice +
                ", maxDiscount=" + maxDiscount +
                ", showSpuPrice=" + showSpuPrice +
                ", showOutOfStock=" + showOutOfStock +
                '}';
    }
}
