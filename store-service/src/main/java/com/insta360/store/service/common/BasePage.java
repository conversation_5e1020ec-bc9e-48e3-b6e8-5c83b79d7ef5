package com.insta360.store.service.common;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.ServletUtil;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.configuration.gateway.GatewayConfiguration;
import com.insta360.store.business.configuration.utils.EncodingUtil;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.meta.enums.PaymentChannel;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.payment.bo.PaymentChannelBO;
import com.insta360.store.business.payment.enums.StorePaymentMethodEnum;
import com.insta360.store.business.payment.service.impl.channel.PaymentChannelFactory;
import com.insta360.store.business.payment.service.impl.channel.PaymentChannelService;
import com.insta360.store.service.config.StoreConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Author: hyc
 * @Date: 2019-05-17
 * @Description:
 */
public class BasePage extends BaseApi {

    public static final Logger LOGGER = LoggerFactory.getLogger(BasePage.class);

    /**
     * 商城订单支付结果页
     */
    @Value("${payment.pay_result_page_url}")
    private String payResultPageUrl;

    /**
     * 工单订单支付结果页
     */
    @Value("${payment.repair_pay_result_page_url}")
    private String repairPayResultPageUrl;

    /**
     * 商城订单二次支付页
     */
    @Value("${payment.order_pay_page_url}")
    private String orderPayPageUrl;

    /**
     * 工单订单二次支付页
     */
    @Value("${payment.repair_order_pay_page_url}")
    private String repairOrderPayPageUrl;

    @Autowired
    protected OrderService orderService;

    @Autowired
    StoreConfiguration storeConfiguration;

    @Autowired
    PaymentChannelFactory paymentChannelFactory;

    @Autowired
    protected GatewayConfiguration gatewayConfiguration;

    @Override
    public InstaLanguage getApiLanguage() {
        return getApiLanguage(storeConfiguration.getDefaultLanguage());
    }

    public InstaLanguage getApiLanguage(InstaLanguage defaultLanguage) {
        InstaLanguage language = super.getApiLanguage();
        return language != null ? language : defaultLanguage;
    }

    public String getPayResultPageUrl() {
        return payResultPageUrl;
    }

    public String getRepairPayResultPageUrl() {
        return repairPayResultPageUrl;
    }

    public String getOrderPayPageUrl() {
        return orderPayPageUrl;
    }

    public String getRepairOrderPayPageUrl() {
        return repairOrderPayPageUrl;
    }

    /**
     * 解析订单号（TODO: 07/09/2023 需要优化为注入到接口参数中）
     *
     * @return
     */
    private String getOrderNumber() {
        // 拼接在return_url中
        String orderNumber = request.getParameter("order");
        if (StringUtil.isBlank(orderNumber)) {
            LOGGER.error("payment request里面不存在orderId！");
            throw new InstaException(-1, "payment request里面不存在orderId！");
        }

        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            LOGGER.error("订单不存在。order number:{}", orderNumber);
            throw new InstaException(-1, "订单不存在");
        }
        return orderNumber;
    }

    /**
     * 商城订单支付结果页 TODO: 07/09/2023 后面应逐步优化为使用 #orderPayResultPage()
     *
     * @return
     */
    public String storePageUrl() {
        String orderNumber;
        try {
            orderNumber = getOrderNumber();
        } catch (Exception e) {
            return gatewayConfiguration.getStoreUrl() + "/order/list";
        }

        String url = String.format(this.getPayResultPageUrl(), EncodingUtil.encode(RSAUtil.encryptByPub(orderNumber)));

        // weixin_h5支付返回会携带app标识
        String app = request.getParameter("app");
        if (StringUtil.isNotBlank(app)) {
            url = url + "?app=" + app;
        }
        return url;
    }

    /**
     * 获取支付渠道
     *
     * @param paymentChannelParam
     * @param storePaymentMethodEnum
     * @return
     */
    protected PaymentChannel getPaymentChannel(PaymentChannelBO paymentChannelParam, StorePaymentMethodEnum storePaymentMethodEnum) {
        PaymentChannelService paymentChannelService = paymentChannelFactory.getPaymentChannel(storePaymentMethodEnum);
        return paymentChannelService.getPaymentChannel(paymentChannelParam);
    }

    /**
     * 订单支付结果页（success or failure）
     *
     * @param order
     * @return
     */
    public String orderPayResultPage(Order order) {
        String orderNumber = order.getOrderNumber();

        // 根据订单灵活判断是工单订单还是商城订单
        return order.isRepairOrder()
                ? String.format(this.getRepairPayResultPageUrl(), "?order=" + orderNumber)
                : String.format(this.getPayResultPageUrl(), EncodingUtil.encode(RSAUtil.encryptByPub(orderNumber)));
    }

    /**
     * 订单二次支付页
     *
     * @param order
     * @return
     */
    protected String orderPayPage(Order order) {
        String orderNumber = order.getOrderNumber();

        // 根据订单灵活判断是工单订单还是商城订单
        return order.isRepairOrder()
                ? String.format(this.getRepairOrderPayPageUrl(), "?order=" + orderNumber)
                : String.format(this.getOrderPayPageUrl(), EncodingUtil.encode(RSAUtil.encryptByPub(orderNumber)));
    }

    /**
     * 支付结果页面全局异常
     *
     * @param e
     * @param response
     */
    protected void doHandlerPaymentException(Exception e, HttpServletResponse response, String pageType) {
        String reqeustParam = ServletUtil.getParaString(request);
        LOGGER.info("request param:{}, error message:{}", reqeustParam, e.getMessage());

        StringBuilder errorBuilder = new StringBuilder();
        errorBuilder.append("Payment Page Error\n")
                .append("pageType：").append(pageType)
                .append("\n")
                .append(e.getClass().getSimpleName()).append(": ").append(e.getLocalizedMessage());
        if (e instanceof InstaException) {
            FeiShuMessageUtil.storeGeneralMessage(request.getParameter("order") + " 支付返回时报错。\n" + errorBuilder, FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
        }

        try {
            response.sendRedirect(storePageUrl());
        } catch (IOException ex) {
            LOGGER.error(String.format("链接重定向失败。异常原因:{%s}, page_type:{%s}, request:{%s}", ex.getMessage(), pageType, reqeustParam), ex);
        }
    }
}
