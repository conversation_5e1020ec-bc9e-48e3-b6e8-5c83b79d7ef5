package com.insta360.store.service.controller.reseller.vo;

import com.insta360.store.business.meta.bo.Price;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2021/8/4
 * @Description:
 */
public class ResellerCommissionResponseVO implements Serializable {

    private Price unavailable;

    private Price available;

    private Price gained;

    private Price total;

    public Price getUnavailable() {
        return unavailable;
    }

    public void setUnavailable(Price unavailable) {
        this.unavailable = unavailable;
    }

    public Price getAvailable() {
        return available;
    }

    public void setAvailable(Price available) {
        this.available = available;
    }

    public Price getGained() {
        return gained;
    }

    public void setGained(Price gained) {
        this.gained = gained;
    }

    public Price getTotal() {
        return total;
    }

    public void setTotal(Price total) {
        this.total = total;
    }
}
