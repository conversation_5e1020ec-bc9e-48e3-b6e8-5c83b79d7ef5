package com.insta360.store.service.controller.meta.filter;

import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.meta.enums.NavigationBarTypeEnum;
import com.insta360.store.business.meta.enums.NavigationSubsetTypeEnum;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.service.controller.meta.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/05/06
 * @Description:
 */
@Component
public class NavigationBarDataFilter {

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    /**
     * listNavigationBarInfos接口的新品数据过滤
     *
     * @param navigationBarVo
     * @return
     */
    public NavigationBarCategoryInfoVO listNavigationBarCategoryInfosDataFilter(NavigationBarCategoryInfoVO navigationBarVo) {
        // 新品产品列表
        List<Product> newProducts = productService.listNewProducts(true);

        // 新品套餐列表
        List<Commodity> newCommodities = commodityService.listNewCommodities(true);
        if (newProducts.isEmpty() && newCommodities.isEmpty()) {
            return navigationBarVo;
        }

        List<Integer> newProductIds = newProducts.stream().map(Product::getId).collect(Collectors.toList());
        List<Integer> newCommodityIds = newCommodities.stream().map(Commodity::getId).collect(Collectors.toList());

        // 导航栏适配套餐列表过滤
        navigationCommodityFilter(navigationBarVo, newCommodityIds);

        // 套餐信息列表数据过滤
        commodityFilter(navigationBarVo, newProductIds, newCommodityIds);

        return navigationBarVo;
    }

    /**
     * 套餐信息列表数据过滤
     *
     * @param navigationBarVo
     * @param newProductIds
     * @param newCommodityIds
     */
    private void commodityFilter(NavigationBarCategoryInfoVO navigationBarVo, List<Integer> newProductIds, List<Integer> newCommodityIds) {
        List<NavigationBarCommodityInfoVO> commodityInfoVoList = navigationBarVo.getCommodityInfos();
        if (CollectionUtils.isEmpty(commodityInfoVoList)) {
            return;
        }
        List<NavigationBarCommodityInfoVO> commodityInfoVos = commodityInfoVoList
                .stream()
                .filter(navigationBarCommodityInfoVO -> {
                    // new product list check
                    if (newProductIds.contains(navigationBarCommodityInfoVO.getProductId())) {
                        return false;
                    }

                    // new commodity list check
                    return !newCommodityIds.contains(navigationBarCommodityInfoVO.getCommodityId());
                }).collect(Collectors.toList());

        // reset
        navigationBarVo.setCommodityInfos(commodityInfoVos);
    }

    /**
     * 导航栏适配套餐列表过滤
     *
     * @param navigationBarVo
     * @param newCommodityIds
     */
    private void navigationCommodityFilter(NavigationBarCategoryInfoVO navigationBarVo, List<Integer> newCommodityIds) {
        List<NavigationBarCategoryVO> navigationBarInfos = navigationBarVo.getNavigationBarInfos();
        if (CollectionUtils.isEmpty(navigationBarInfos)) {
            return;
        }

        for (NavigationBarCategoryVO navigationBarInfo : navigationBarInfos) {
            // 配件导航栏
            if (NavigationBarTypeEnum.ACCESSORY_NEW.getType().equals(navigationBarInfo.getType())) {
                List<NavigationBarCategorySubsetVO> accessorySubsetList = navigationBarInfo.getSubsetList();
                // 兼容二级类目没有配置
                if (CollectionUtils.isEmpty(accessorySubsetList)) {
                    continue;
                }
                for (NavigationBarCategorySubsetVO accessorySubset : accessorySubsetList) {
                    List<NavigationBarCategoryThirdVO> thirdList = accessorySubset.getThirdList();
                    // 套餐过滤
                    if (NavigationSubsetTypeEnum.COMMODITY.getType().equals(accessorySubset.getType())) {
                        // new commodity list check
                        thirdList = thirdList.stream()
                                .filter(third -> !newCommodityIds.contains(third.getCommodityId()))
                                .collect(Collectors.toList());
                        accessorySubset.setThirdList(thirdList);
                    }
                }
            }

            // 过滤Banner类型结构
            if (!NavigationBarTypeEnum.isBannerType(navigationBarInfo.getType())) {
                List<NavigationBarCategorySubsetInfoVO> categorySubsets = navigationBarInfo.getCategorySubsets();
                // 兼容二级类目没有配置
                if (CollectionUtils.isEmpty(categorySubsets)) {
                    continue;
                }
                for (NavigationBarCategorySubsetInfoVO categorySubset : categorySubsets) {
                    List<NavigationBarCategoryCommodityVO> navigationBarCommodityInfos = categorySubset.getNavigationBarCommodityInfos();
                    navigationBarCommodityInfos = navigationBarCommodityInfos.stream()
                            // new commodity list check
                            .filter(navigationBarCommodityInfoVO -> !newCommodityIds.contains(navigationBarCommodityInfoVO.getCommodityId()))
                            .collect(Collectors.toList());
                    // reset
                    categorySubset.setNavigationBarCommodityInfos(navigationBarCommodityInfos);
                }
            }

            // reset
            navigationBarVo.setNavigationBarInfos(navigationBarInfos);
        }
    }
}
