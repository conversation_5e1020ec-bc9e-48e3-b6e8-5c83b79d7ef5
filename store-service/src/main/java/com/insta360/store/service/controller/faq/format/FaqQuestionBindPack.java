package com.insta360.store.service.controller.faq.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.faq.model.*;
import com.insta360.store.business.faq.service.*;
import com.insta360.store.service.controller.faq.vo.FaqCategoryVO;
import com.insta360.store.service.controller.faq.vo.FaqQuestionBindVO;
import com.insta360.store.service.controller.faq.vo.FaqQuestionQaBindVO;
import com.insta360.store.service.controller.faq.vo.FaqQuestionSubsetBindVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/4/27
 * @Description:
 */
@Component
public class FaqQuestionBindPack {

    @Autowired
    FaqQuestionBindService questionBindService;

    @Autowired
    FaqCategoryService categoryService;

    @Autowired
    FaqQuestionSubsetBindService questionSubsetBindService;

    @Autowired
    FaqCategorySubsetService subsetService;

    @Autowired
    FaqQuestionQaBindService questionQaBindService;

    @Autowired
    FaqCategoryQuestionInsideService questionInsideService;

    @Autowired
    FaqCategoryQuestionInfoService questionInfoService;

    /**
     * 封装一级qa绑定信息
     *
     * @param type
     * @param pageKey
     * @param language
     * @param country
     */
    public FaqCategoryVO doPackQuestionBind(String type, String pageKey, InstaLanguage language, InstaCountry country) {
        FaqCategoryVO categoryVO = new FaqCategoryVO();
        List<FaqQuestionBind> questionBinds = questionBindService.listQuestionBindEnable(type, pageKey);
        if (CollectionUtils.isEmpty(questionBinds)) {
            return categoryVO;
        }
        categoryVO.setQuestionBinds(questionBinds.stream().map(questionBind -> {
            // 一级类目
            FaqCategory category = categoryService.getByCategoryInsideId(questionBind.getCategoryInsideId(), language);
            if (category == null) {
                return null;
            }
            List<FaqQuestionSubsetBind> subsetBinds = questionSubsetBindService.listByQuestionBindsIdEnable(questionBind.getId());
            FaqQuestionBindVO questionBindVO = new FaqQuestionBindVO(category);
            questionBindVO.setOrderIndex(questionBind.getOrderIndex());
            questionBindVO.setQuestionSubsetBinds(packQuestionSubsetBind(subsetBinds, language, country));
            return questionBindVO;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
        return categoryVO;
    }

    /**
     * 封装二级qa绑定信息
     *
     * @param subsetBinds
     * @param language
     * @param country
     * @return
     */
    private List<FaqQuestionSubsetBindVO> packQuestionSubsetBind(List<FaqQuestionSubsetBind> subsetBinds, InstaLanguage language, InstaCountry country) {
        return subsetBinds.stream().map(subsetBind -> {
            // 二级类目
            FaqCategorySubset categorySubset = subsetService.getBySubsetInsideId(subsetBind.getCategorySubsetInsideId(), language);
            // 问题多语言
            List<FaqQuestionQaBind> questionQaBinds = questionQaBindService.listByQuestionQaBindsIdEnable(subsetBind.getId());
            if (CollectionUtils.isEmpty(questionQaBinds)) {
                return null;
            }
            FaqQuestionSubsetBindVO subsetBindVO = new FaqQuestionSubsetBindVO(categorySubset);
            subsetBindVO.setQuestionQaBinds(packQuestionQaBind(questionQaBinds, language, country));
            subsetBindVO.setOrderIndex(subsetBind.getOrderIndex());
            return subsetBindVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 封装qa绑定信息
     *
     * @param questionQaBinds
     * @param language
     * @param country
     * @return
     */
    private List<FaqQuestionQaBindVO> packQuestionQaBind(List<FaqQuestionQaBind> questionQaBinds, InstaLanguage language, InstaCountry country) {
        return questionQaBinds.stream().map(questionQaBind -> {
            FaqCategoryQuestionInside questionInside = questionInsideService.getByIdEnable(questionQaBind.getQuestionId());
            if (questionInside == null) {
                return null;
            }
            FaqCategoryQuestionInfo questionInfo = questionInfoService.getByQuestionInsideId(questionInside.getId(), language, country);
            // 防空字符串校验
            if (questionInfo == null || StringUtil.isBlank(questionInfo.getQuestion()) || StringUtil.isBlank(questionInfo.getAnswer())) {
                return null;
            }
            FaqQuestionQaBindVO questionQaBindVO = new FaqQuestionQaBindVO(questionInfo);
            questionQaBindVO.setOrderIndex(questionQaBind.getOrderIndex());
            return questionQaBindVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
