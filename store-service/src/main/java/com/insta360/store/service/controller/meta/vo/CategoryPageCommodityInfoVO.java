package com.insta360.store.service.controller.meta.vo;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2023/11/8
 * @Description:
 */
public class CategoryPageCommodityInfoVO implements Serializable {

    /**
     * 套餐主图
     */
    private String commodityDisplayImage;

    /**
     * 功能描述
     */
    private String functionDescription;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品key
     */
    private String productKey;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 套餐销售状态
     */
    private Integer commoditySaleState;

    /**
     * 增值服务类型
     */
    private String serviceType;

    public String getCommodityDisplayImage() {
        return commodityDisplayImage;
    }

    public void setCommodityDisplayImage(String commodityDisplayImage) {
        this.commodityDisplayImage = commodityDisplayImage;
    }

    public String getFunctionDescription() {
        return functionDescription;
    }

    public void setFunctionDescription(String functionDescription) {
        this.functionDescription = functionDescription;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductKey() {
        return productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Integer getCommoditySaleState() {
        return commoditySaleState;
    }

    public void setCommoditySaleState(Integer commoditySaleState) {
        this.commoditySaleState = commoditySaleState;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    @Override
    public String toString() {
        return "CategoryPageCommodityInfoVO{" +
                "commodityDisplayImage='" + commodityDisplayImage + '\'' +
                ", functionDescription='" + functionDescription + '\'' +
                ", productName='" + productName + '\'' +
                ", productKey='" + productKey + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", commoditySaleState=" + commoditySaleState +
                ", serviceType='" + serviceType + '\'' +
                '}';
    }
}
