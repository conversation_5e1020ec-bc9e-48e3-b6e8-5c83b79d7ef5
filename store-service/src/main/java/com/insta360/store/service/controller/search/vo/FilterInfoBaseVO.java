package com.insta360.store.service.controller.search.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/9
 */
public class FilterInfoBaseVO implements Serializable {

    /**
     * 适配机型
     */
    private List<String> adapterTypes;

    /**
     * 配件类目图文导航
     */
    private List<String> accessoriesCategory;

    /**
     * 一级导航栏
     */
    private List<String> navigationBar;

    /**
     * 商品促销标记
     */
    private Integer discountMark;

    public List<String> getAdapterTypes() {
        return adapterTypes;
    }

    public void setAdapterTypes(List<String> adapterTypes) {
        this.adapterTypes = adapterTypes;
    }

    public List<String> getAccessoriesCategory() {
        return accessoriesCategory;
    }

    public void setAccessoriesCategory(List<String> accessoriesCategory) {
        this.accessoriesCategory = accessoriesCategory;
    }

    public List<String> getNavigationBar() {
        return navigationBar;
    }

    public void setNavigationBar(List<String> navigationBar) {
        this.navigationBar = navigationBar;
    }

    public Integer getDiscountMark() {
        return discountMark;
    }

    public void setDiscountMark(Integer discountMark) {
        this.discountMark = discountMark;
    }

    @Override
    public String toString() {
        return "FilterInfoBaseVO{" +
                "adapterTypes=" + adapterTypes +
                ", accessoriesCategory=" + accessoriesCategory +
                ", navigationBar=" + navigationBar +
                ", discountMark=" + discountMark +
                '}';
    }
}
