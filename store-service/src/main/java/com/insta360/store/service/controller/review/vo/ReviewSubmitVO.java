package com.insta360.store.service.controller.review.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2022/07/05
 * @Description:
 */
public class ReviewSubmitVO implements Serializable {

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 该订单评论是否在有效期（评论邮件发送30天内）（false：不允许评论；true：允许评论）
     */
    private Boolean reviewRule;

    /**
     * 订单子项基本信息
     */
    private List<ReviewOrderItemVO> orderItems;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Boolean getReviewRule() {
        return reviewRule;
    }

    public void setReviewRule(Boolean reviewRule) {
        this.reviewRule = reviewRule;
    }

    public List<ReviewOrderItemVO> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<ReviewOrderItemVO> orderItems) {
        this.orderItems = orderItems;
    }

    @Override
    public String toString() {
        return "ReviewSubmitVO{" +
                "orderNumber='" + orderNumber + '\'' +
                ", reviewRule=" + reviewRule +
                ", orderItems=" + orderItems +
                '}';
    }
}
