package com.insta360.store.service.controller.cloud.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/5/16
 */
public class BenefitDiscountInfoVO implements Serializable {

    /**
     * 享受折扣的商品
     */
    private List<BenefitDiscountItemVO> benefitDiscountItems;

    /**
     * 折扣总额
     */
    private BigDecimal totalDiscountAmount;

    public List<BenefitDiscountItemVO> getBenefitDiscountItems() {
        return benefitDiscountItems;
    }

    public void setBenefitDiscountItems(List<BenefitDiscountItemVO> benefitDiscountItems) {
        this.benefitDiscountItems = benefitDiscountItems;
    }

    public BigDecimal getTotalDiscountAmount() {
        return totalDiscountAmount;
    }

    public void setTotalDiscountAmount(BigDecimal totalDiscountAmount) {
        this.totalDiscountAmount = totalDiscountAmount;
    }
}
