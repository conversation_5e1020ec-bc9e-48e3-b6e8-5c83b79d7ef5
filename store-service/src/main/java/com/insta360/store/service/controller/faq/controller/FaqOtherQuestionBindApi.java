package com.insta360.store.service.controller.faq.controller;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.faq.cache.cacheable.FaqQuestionCacheablePack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2022/4/29
 * @Description:
 */
@RestController
public class FaqOtherQuestionBindApi extends BaseApi {

    @Autowired
    FaqQuestionCacheablePack questionCachePack;

    /**
     * 获取页面绑定qa(非类目结构)
     *
     * @param type
     * @param pageKey
     * @return
     */
    @GetMapping("/store/faq/other/listQuestionBind")
    public Response<? extends Map> listQuestionBind(@RequestParam(required = false, value = "type") String type,
                                                    @RequestParam(required = false, value = "pageKey") String pageKey) {
        return Response.ok("faqQuestionBinds", questionCachePack.doPackCacheOtherQuestionInfo(type, pageKey, getApiLanguage(), getApiCountry()));
    }
}
