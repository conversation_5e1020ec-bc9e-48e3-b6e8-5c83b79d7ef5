package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.SeoConfig;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/11/8
 * @Description:
 */
public class SeoConfigVO implements Serializable {

    /**
     * 页面类型
     */
    private String type;

    /**
     * 页面key
     */
    private String pageUrlKey;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 多语言文案
     */
    private SeoConfigInfoVO configInfo;

    public SeoConfigVO() {
    }

    public SeoConfigVO(SeoConfig seoConfig) {
        if (seoConfig != null) {
            BeanUtil.copyProperties(seoConfig, this);
        }
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPageUrlKey() {
        return pageUrlKey;
    }

    public void setPageUrlKey(String pageUrlKey) {
        this.pageUrlKey = pageUrlKey;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public SeoConfigInfoVO getConfigInfo() {
        return configInfo;
    }

    public void setConfigInfo(SeoConfigInfoVO configInfo) {
        this.configInfo = configInfo;
    }

    @Override
    public String toString() {
        return "SeoConfigVO{" +
                "type='" + type + '\'' +
                ", pageUrlKey='" + pageUrlKey + '\'' +
                ", productId=" + productId +
                ", configInfo=" + configInfo +
                '}';
    }
}
