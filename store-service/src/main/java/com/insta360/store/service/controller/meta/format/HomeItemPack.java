package com.insta360.store.service.controller.meta.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.model.CommoditySaleState;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommoditySaleStateService;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.model.HomepageItem;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.service.controller.meta.vo.HomepageItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * @Author: hyc
 * @Date: 2019/3/5
 * @Description:
 */
@Component
public class HomeItemPack {

    @Autowired
    ProductService productService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    @Cacheable(value = {"cache"}, key = "caches[0].name + '-HomeItemPack-' + methodName + '-item-' + #item + 'language' + #language + '-country-' + #country")
    public HomepageItemVO doPack(HomepageItem item, InstaLanguage language, InstaCountry country) {
        Integer productId = item.getProduct();
        Product product = productService.getById(productId);
        if (!product.getEnabled() || !item.isEnabled()) {
            return null;
        }

        HomepageItemVO itemVO = new HomepageItemVO(item);

        // 悬浮图片
        if (StringUtil.isBlank(item.getHoverImage())) {
            itemVO.setHoverImage(item.getImage());
        }

        // 封面图片
        if (!InstaLanguage.zh_CN.equals(language)) {
            itemVO.setHeaderImage(item.getHeaderImageEn());
        }

        itemVO.setHeaderImageEn(null);

        // 产品链接
        itemVO.setLink(product.productLink());

        // 产品详情
        ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(productId, language);
        itemVO.setProductInfo(productInfo);

        // 封面套餐价格
        Integer commodityId = item.getPriceReferenceCommodity();
        CommodityPrice commodityPrice = commodityPriceService.getPrice(commodityId, country);

        // 现价
        Price price = commodityPrice != null ? commodityPrice.price() : null;
        itemVO.setPrice(price);

        // 原价
        Price originPrice = commodityPrice == null ? null : commodityPrice.originPrice();
        if (originPrice != null && originPrice.getAmount() != price.getAmount()) {
            itemVO.setOriginPrice(originPrice);
        }

        // 参照套餐的销售状态
        CommoditySaleState saleState = commoditySaleStateService.getSaleState(item.getPriceReferenceCommodity(), country);
        itemVO.setSaleState(saleState);

        return itemVO;
    }
}
