package com.insta360.store.service.controller.reseller.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.product.model.Product;

/**
 * @Author: hyc
 * @Date: 2019/2/27
 * @Description:
 */
public class ResellerProductVO extends Product {

    @JSONField(name = "exp_point")
    private Integer expPoint;

    @JSONField(name = "commission_rate")
    private Double commissionRate;

    @JSONField(name = "sale_count")
    private Integer saleCount;

    public ResellerProductVO() {
    }

    public ResellerProductVO(Product product) {
        if (product != null) {
            BeanUtil.copyProperties(product, this);
        }
    }

    public Integer getExpPoint() {
        return expPoint;
    }

    public void setExpPoint(Integer expPoint) {
        this.expPoint = expPoint;
    }

    public Double getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(Double commissionRate) {
        this.commissionRate = commissionRate;
    }

    public Integer getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(Integer saleCount) {
        this.saleCount = saleCount;
    }
}
