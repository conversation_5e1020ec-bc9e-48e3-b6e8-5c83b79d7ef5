package com.insta360.store.service.controller.trade.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 地址
 * @Date 2023/6/16
 */
public class AddressLocationInfoVO implements Serializable {

    /**
     *
     */
    private String locationCode;

    /**
     * 街道地址第一行
     */
    private String line1;

    /**
     * 街道地址第二行
     */
    private String line2;

    /**
     * 街道地址第三行
     */
    private String line3;

    /**
     * 城市
     */
    private String city;

    /**
     * 省份/州地址（ISO 3166 region codes）
     */
    private String region;

    /**
     * 国家二字码（ISO 3166 code identifying the country.）
     */
    private String country;

    /**
     * 邮编
     */
    private String postalCode;

    public String getLocationCode() {
        return locationCode;
    }

    public void setLocationCode(String locationCode) {
        this.locationCode = locationCode;
    }

    public String getLine1() {
        return line1;
    }

    public void setLine1(String line1) {
        this.line1 = line1;
    }

    public String getLine2() {
        return line2;
    }

    public void setLine2(String line2) {
        this.line2 = line2;
    }

    public String getLine3() {
        return line3;
    }

    public void setLine3(String line3) {
        this.line3 = line3;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    @Override
    public String toString() {
        return "AddressLocationInfoVO{" +
                "locationCode='" + locationCode + '\'' +
                ", line1='" + line1 + '\'' +
                ", line2='" + line2 + '\'' +
                ", line3='" + line3 + '\'' +
                ", city='" + city + '\'' +
                ", region='" + region + '\'' +
                ", country='" + country + '\'' +
                ", postalCode='" + postalCode + '\'' +
                '}';
    }
}
