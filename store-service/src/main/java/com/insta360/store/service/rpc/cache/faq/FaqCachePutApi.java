package com.insta360.store.service.rpc.cache.faq;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.faq.cache.cacheput.FaqQuestionCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wbt
 * @Date: 2023/08/21
 * @Description: FAQ缓存更新
 */
@RestController
public class FaqCachePutApi {

    @Autowired
    FaqQuestionCachePutPack faqQuestionCachePutPack;

    /**
     * 获取页面绑定QA（类目结构）
     *
     * @param type
     * @param pageKey
     * @param language
     * @param country
     */
    @GetMapping("/rpc/store/service/cacheput/faq/listQuestionBind")
    public void listQuestionBind(@RequestParam String type,
                                 @RequestParam String pageKey,
                                 @RequestParam InstaCountry country,
                                 @RequestParam InstaLanguage language) {
        faqQuestionCachePutPack.doPackCacheQuestionInfo(type, pageKey, language, country);
    }

    /**
     * 获取页面绑定QA（非类目结构）
     *
     * @param type
     * @param pageKey
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/faq/other/listQuestionBind")
    public void listOtherQuestionBind(@RequestParam String type,
                                      @RequestParam String pageKey,
                                      @RequestParam InstaCountry country,
                                      @RequestParam InstaLanguage language) {
        faqQuestionCachePutPack.doPackCacheOtherQuestionInfo(type, pageKey, language, country);
    }
}
