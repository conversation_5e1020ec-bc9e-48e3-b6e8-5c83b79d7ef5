package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.service.controller.meta.cache.cacheput.AvalaraTaxCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wkx
 * @Date: 2023/11/15
 * @Description:
 */
@RestController
public class AvalaraTaxCachePutApi {

    @Autowired
    AvalaraTaxCachePutPack avalaraTaxCachePutPack;

    /**
     * 根据地区更新计税州数据
     *
     * @param country
     */
    @GetMapping("/rpc/store/service/cacheput/meta/tax/province/listAvalaraTaxProvince")
    public void listAvalaraTaxProvince(@RequestParam InstaCountry country) {
        avalaraTaxCachePutPack.doPackListAvalaraTaxProvince(country);
    }
}
