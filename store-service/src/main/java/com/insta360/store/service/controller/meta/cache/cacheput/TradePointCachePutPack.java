package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.TradePointPack;
import com.insta360.store.service.controller.meta.vo.TradePointResponseVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2023/10/11
 * @Description:
 */
@Component
public class TradePointCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(TradePointCachePutPack.class);

    @Autowired
    TradePointPack tradePointPack;

    /**
     * 缓存trade point数据
     *
     * @param country
     * @param language
     * @return
     */
    @CachePut(value = CacheableType.TRADE_POINT, key = "caches[0].name + '-TradePointCachePack-' + methodName + '-country-' + #country + '-language-' + #language")
    public List<TradePointResponseVO> doPackTradePointInfos(InstaCountry country, InstaLanguage language) {
        List<TradePointResponseVO> tradePointResponseVos = tradePointPack.doPackTradePointInfos(country, language);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.TRADE_POINT, "doPackTradePointInfos", country, language, tradePointResponseVos));
        return tradePointResponseVos;
    }
}
