package com.insta360.store.service.controller.rma.format;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.rma.model.RmaDelivery;
import com.insta360.store.business.rma.model.RmaOrder;
import com.insta360.store.business.rma.service.RmaDeliveryService;
import com.insta360.store.service.controller.rma.vo.RmaItemVO;
import com.insta360.store.service.controller.rma.vo.RmaOrderInfoVO;
import com.insta360.store.service.controller.rma.vo.RmaOrderLogisticsVO;
import com.insta360.store.service.controller.rma.vo.RmaOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: hyc
 * @Date: 2019-11-13
 * @Description:
 */
@Component
public class RmaOrderPack {

    @Autowired
    RmaDeliveryService rmaDeliveryService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    OrderService orderService;

    public RmaOrderVO doPack(RmaOrder rmaOrder, InstaLanguage language) {

        RmaOrderVO rmaOrderVO = new RmaOrderVO(rmaOrder);
        RmaDelivery delivery = rmaDeliveryService.getByRmaOrder(rmaOrder.getId());
        rmaOrderVO.setDelivery(delivery);

        OrderItem orderItem = orderItemService.getById(rmaOrder.getOrderItemId());
        rmaOrderVO.setOrderItem(orderItem);

        ProductInfo productInfo = productInfoService.getInfo(orderItem.getProduct(), language);
        rmaOrderVO.setProductName(productInfo.getName());

        CommodityInfo commodityInfo = commodityInfoService.getInfo(orderItem.getCommodity(), language);
        rmaOrderVO.setCommodityName(commodityInfo.getName());

        CommodityDisplay firstDisplay = commodityDisplayService.getFirstDisplay(orderItem.getCommodity());
        rmaOrderVO.setCommodityDisplay(firstDisplay.getUrl());

        Order order = orderService.getById(rmaOrder.getOrderId());
        rmaOrderVO.setOrderNumber(order.getOrderNumber());

        return rmaOrderVO;
    }

    /**
     * 封装售后单信息
     *
     * @param rmaOrder
     * @param language
     * @return
     */
    public RmaOrderInfoVO doPackInfo(RmaOrder rmaOrder, InstaLanguage language) {
        if (Objects.isNull(rmaOrder)) {
            return new RmaOrderInfoVO();
        }

        Order order = orderService.getById(rmaOrder.getOrderId());
        if (Objects.isNull(order)) {
            return new RmaOrderInfoVO();
        }

        // 售后单基础信息封装
        RmaOrderInfoVO rmaOrderInfo = new RmaOrderInfoVO();
        rmaOrderInfo.setRmaNumber(rmaOrder.getRmaNumber());
        rmaOrderInfo.setOrderNumber(order.getOrderNumber());
        rmaOrderInfo.setQuantity(rmaOrder.getQuantity());
        rmaOrderInfo.setRmaType(rmaOrder.getRmaType());
        rmaOrderInfo.setRefundReason(rmaOrder.getReason());
        rmaOrderInfo.setExtraReason(rmaOrder.getExtraReason());
        rmaOrderInfo.setRefundApplyTime(rmaOrder.getCreateTime());
        rmaOrderInfo.setRefundCurrency(rmaOrder.getRefundCurrency());
        rmaOrderInfo.setTotalRefundAmount(new BigDecimal(String.valueOf(rmaOrder.getRefundAmount())));
        rmaOrderInfo.setRmaState(rmaOrder.getState());
        rmaOrderInfo.setFormerState(rmaOrder.getCloseState());

        // 售后商品信息封装
        OrderItem orderItem = orderItemService.getById(rmaOrder.getOrderItemId());
        if (Objects.nonNull(orderItem)) {
            RmaItemVO rmaItem = new RmaItemVO();
            Optional.ofNullable(productInfoService.getInfo(orderItem.getProduct(), language))
                    .ifPresent(productInfo -> rmaItem.setProductName(productInfo.getName()));

            Optional.ofNullable(commodityInfoService.getInfo(orderItem.getCommodity(), language))
                    .ifPresent(commodityInfo -> rmaItem.setCommodityName(commodityInfo.getName()));

            Optional.ofNullable(commodityDisplayService.getFirstDisplay(orderItem.getCommodity()))
                    .ifPresent(commodityDisplay -> rmaItem.setCommodityDisplay(commodityDisplay.getUrl()));
            rmaOrderInfo.setOrderItem(rmaItem);
        }

        // 售后物流信息封装
        RmaDelivery rmaDelivery = rmaDeliveryService.getByRmaOrder(rmaOrder.getId());
        if (Objects.nonNull(rmaDelivery)) {
            RmaOrderLogisticsVO rmaOrderLogistics = new RmaOrderLogisticsVO();
            rmaOrderLogistics.setCustomerReturnExpressCompany(rmaDelivery.getExpressFromCompany());
            rmaOrderLogistics.setCustomerReturnExpressNumber(rmaDelivery.getExpressFromNumber());
            rmaOrderLogistics.setCustomerReturnExpressTime(rmaDelivery.getExpressFromTime());
            rmaOrderLogistics.setPlatformReturnExpressCompany(rmaDelivery.getExpressToCompany());
            rmaOrderLogistics.setPlatformReturnExpressNumber(rmaDelivery.getExpressToNumber());
            rmaOrderLogistics.setPlatformReturnExpressTime(rmaDelivery.getExpressToTime());
            rmaOrderInfo.setRmaOrderLogistics(rmaOrderLogistics);
        }

        return rmaOrderInfo;
    }
}
