package com.insta360.store.service.common.mq;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.libs.cacheSync.callback.AllNodeMessageCallback;
import com.insta360.compass.libs.cacheSync.dto.CacheDataChangeEvent;
import com.insta360.compass.libs.cacheSync.enums.CacheOrigin;
import com.insta360.store.business.configuration.verification.enums.ParameterBusinessType;
import com.insta360.store.business.configuration.verification.support.factory.StoreParameterVerificationFactory;
import com.insta360.store.business.configuration.verification.support.handler.BaseStoreParameterVerificationHandler;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 商城参数校验组件缓存同步刷新MQ广播消费
 * @Date 2022/10/17
 */
@Component
public class StoreParameterCacheConsumer implements AllNodeMessageCallback {

    private static final Logger log = LoggerFactory.getLogger(StoreParameterCacheConsumer.class);

    @Autowired
    StoreParameterVerificationFactory storeParameterVerificationFactory;

    @Override
    public void receive(CacheDataChangeEvent event) {
        boolean success = true;
        if (Objects.isNull(event)) {
            log.info("商城参数校验组件缓存同步刷新——MQ广播消费失败! 消息体为空...");
            success = false;
        }
        if (!CacheOrigin.store.equals(event.getCacheOrigin())) {
            log.info("商城参数校验组件缓存同步刷新——MQ广播消费失败! 业务来源匹配失败... cacheOrigin:{}", event.getCacheOrigin().getModuleName());
            success = false;
        }
        ParameterBusinessType businessType = ParameterBusinessType.matchName(event.getCacheKey());
        if (Objects.isNull(businessType)) {
            log.info("商城参数校验组件缓存同步刷新——MQ广播消费失败! 非缓存业务类型... event:{}", JSON.toJSONString(event));
            return;
        }
        if (!success) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("商城参数校验组件缓存同步刷新MQ广播消费异常，消息体:%s", JSON.toJSONString(event)), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return;
        }
        // 刷新缓存
        BaseStoreParameterVerificationHandler storeParameterVerificationHandler = storeParameterVerificationFactory.getStoreParameterVerificationHandler(businessType);
        storeParameterVerificationHandler.putCache(event.getExtraMessage());
    }
}
