package com.insta360.store.service.controller.review.cache.cacheput;

import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.review.format.ReviewPack;
import com.insta360.store.service.controller.review.vo.ReviewRateInfoVO;
import com.insta360.store.service.controller.review.vo.ReviewRateLevelInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2023/11/16
 * @Description:
 */
@Component
public class ReviewInfoCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReviewInfoCachePutPack.class);

    @Autowired
    ReviewPack reviewPack;

    /**
     * 获取产品的总评论数和平均分缓存信息
     *
     * @param productId
     * @return
     */
    @CachePut(value = CacheableType.REVIEW_INFO, key = "caches[0].name  + '-ReviewCachePack-' + methodName + '-productId-' + #productId")
    public ReviewRateInfoVO doPackCacheReviewRateInfo(Integer productId) {
        ReviewRateInfoVO reviewRateInfo = reviewPack.doPackReviewRateInfo(productId);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[productId:%s],缓存更新响应:[%s]",
                CacheableType.REVIEW_INFO, "doPackCacheReviewRateInfo", productId, reviewRateInfo));
        return reviewRateInfo;
    }

    /**
     * 获取产品的所有评论星级的数量缓存信息
     *
     * @param productId
     * @return
     */
    @CachePut(value = CacheableType.REVIEW_INFO, key = "caches[0].name  + '-ReviewCachePack-' + methodName + '-productId-' + #productId")
    public ReviewRateLevelInfoVO doPackCacheReviewRateLevelInfo(Integer productId) {
        ReviewRateLevelInfoVO reviewRateLevelInfo = reviewPack.doPackReviewRateLevelInfo(productId);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[productId:%s],缓存更新响应:[%s]",
                CacheableType.REVIEW_INFO, "doPackCacheReviewRateLevelInfo", productId, reviewRateLevelInfo));
        return reviewRateLevelInfo;
    }
}
