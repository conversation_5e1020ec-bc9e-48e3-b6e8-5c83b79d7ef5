package com.insta360.store.service.common.authorization;

import com.insta360.compass.core.web.security.AuthorizationChecker;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.common.WebApiContext;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: hyc
 * @Date: 2019/2/28
 * @Description:
 */
public class AccessUser<PERSON>hecker implements AuthorizationChecker {

    AccessUserChecker() {
    }

    @Override
    public boolean check(HttpServletRequest request) {
        WebApiContext apiContext = WebApiContext.get();
        if (apiContext != null) {
            StoreAccount accessUser = apiContext.getAccessUser();
            if (accessUser != null) {
                return true;
            }
        }
        return false;
    }
}
