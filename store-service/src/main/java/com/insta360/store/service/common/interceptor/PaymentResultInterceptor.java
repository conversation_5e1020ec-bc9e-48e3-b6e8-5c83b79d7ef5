package com.insta360.store.service.common.interceptor;

import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.store.business.payment.context.PaymentResultDataContext;
import com.insta360.store.business.payment.service.impl.aop.payment_notify.PaymentNotifyResultHandler;
import com.insta360.store.business.payment.service.impl.aop.payment_return.PaymentReturnResultHandler;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: wkx
 * @Date: 2022/11/29
 * @Description:
 */
public class PaymentResultInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        PaymentNotifyResultHandler paymentNotifyAnnotation = handlerMethod.getMethodAnnotation(PaymentNotifyResultHandler.class);
        PaymentReturnResultHandler paymentReturnResultHandler = handlerMethod.getMethodAnnotation(PaymentReturnResultHandler.class);

        if (paymentNotifyAnnotation == null && paymentReturnResultHandler == null) {
            return super.preHandle(request, response, handler);
        }

        PaymentResultDataContext paymentResultDataContext = ApplicationContextHolder.getApplicationContext().getBean(PaymentResultDataContext.class);
        PaymentResultDataContext.set(paymentResultDataContext);
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);
        // 销毁
        PaymentResultDataContext.remove();
    }
}