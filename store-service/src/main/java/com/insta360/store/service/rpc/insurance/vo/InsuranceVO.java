package com.insta360.store.service.rpc.insurance.vo;

import java.io.Serializable;

/**
 * @description:
 * @author: py
 * @create: 2023-09-13 14:09
 */
public class InsuranceVO implements Serializable {

    /**
     * 延保
     */
    private ExtendInsuranceVO extendInsurance;

    /**
     * care
     */
    private CareInsuranceVO careInsurance;

    public ExtendInsuranceVO getExtendInsurance() {
        return extendInsurance;
    }

    public void setExtendInsurance(ExtendInsuranceVO extendInsurance) {
        this.extendInsurance = extendInsurance;
    }

    public CareInsuranceVO getCareInsurance() {
        return careInsurance;
    }

    public void setCareInsurance(CareInsuranceVO careInsurance) {
        this.careInsurance = careInsurance;
    }

    @Override
    public String toString() {
        return "InsuranceVO{" +
                "extendInsurance=" + extendInsurance +
                ", careInsurance=" + careInsurance +
                '}';
    }
}
