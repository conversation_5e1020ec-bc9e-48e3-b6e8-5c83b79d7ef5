package com.insta360.store.service.controller.user.format;

import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.discount.dto.GiftCardTextResult;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.model.GiftCardInfo;
import com.insta360.store.business.discount.model.GiftCardInfoDetail;
import com.insta360.store.business.discount.service.GiftCardInfoDetailService;
import com.insta360.store.business.discount.service.GiftCardInfoService;
import com.insta360.store.business.discount.service.GiftCardTextService;
import com.insta360.store.service.controller.user.vo.UserGiftCardNewVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: hyc
 * @Date: 2019-08-21
 * @Description:
 */
@Component
public class GiftCardPack {

    @Autowired
    GiftCardTextService giftCardTextService;

    @Autowired
    GiftCardInfoService giftCardInfoService;

    @Autowired
    GiftCardInfoDetailService giftCardInfoDetailService;

    public List<UserGiftCardNewVO> doPack(List<GiftCard> giftCards, InstaLanguage language) {
        return giftCards
                .stream()
                .map(gc -> doPack(gc, language))
                .collect(Collectors.toList());
    }

    public UserGiftCardNewVO doPack(GiftCard giftCard, InstaLanguage language) {
        UserGiftCardNewVO giftCardVO = new UserGiftCardNewVO(giftCard);
        giftCardVO.setEffectTime(giftCard.getCreateTime());

        GiftCardTextResult textResult = giftCardTextService.getGiftCardText(giftCard, language);
        giftCardVO.setText(textResult);

        GiftCardInfo info = giftCardInfoService.getById(giftCard.getInfoTag());
        if (info != null) {
            // 标题
            JSONObject titleJson = JSONObject.parseObject(info.getTitle());
            String title = null;
            if(Objects.nonNull(titleJson)) {
                title = titleJson.getString(language.name());
                if(StringUtils.isBlank(title)) {
                    title = titleJson.getString(InstaLanguage.en_US.name());
                }
            }

            // 包含内容
            GiftCardInfoDetail infoDetail = giftCardInfoDetailService.getInfo(info.getTag(), language);
            info.setTitle(title);
            giftCardVO.setInfo(info);
            giftCardVO.setInfoDetail(infoDetail);
        }

        // 备注仅供内部使用
        giftCardVO.setRemark(null);
        return giftCardVO;
    }
}
