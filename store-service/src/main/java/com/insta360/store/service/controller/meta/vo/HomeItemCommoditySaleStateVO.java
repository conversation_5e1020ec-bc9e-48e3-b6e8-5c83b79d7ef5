package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.model.CommoditySaleState;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2021/10/11
 * @Description:
 */
public class HomeItemCommoditySaleStateVO implements Serializable {

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 国家
     */
    private String country;

    /**
     * 销售状态
     */
    private Integer saleState;

    public HomeItemCommoditySaleStateVO() {
    }

    public HomeItemCommoditySaleStateVO(CommoditySaleState commoditySaleState) {
        if (commoditySaleState != null) {
            BeanUtil.copyProperties(commoditySaleState, this);
        }
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Integer getSaleState() {
        return saleState;
    }

    public void setSaleState(Integer saleState) {
        this.saleState = saleState;
    }

    @Override
    public String toString() {
        return "HomeItemCommoditySaleState{" +
                "commodityId=" + commodityId +
                ", country='" + country + '\'' +
                ", saleState=" + saleState +
                '}';
    }
}
