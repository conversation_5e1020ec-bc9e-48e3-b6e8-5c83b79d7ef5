package com.insta360.store.service.controller.meta.cache.cacheable;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.GraphicNavigationPack;
import com.insta360.store.service.controller.meta.vo.GraphicNavigationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2023/3/16
 * @Description:
 */
@Component
public class GraphicNavigationCachePack {


    @Autowired
    GraphicNavigationPack graphicNavigationPack;

    /**
     * 图文导航缓存
     *
     * @param country
     * @param language
     * @param endpoint
     * @return
     */
    @CacheTtlMonitor(value = CacheableType.GRAPHIC_NAVIGATION, cacheKey = "'-GraphicNavigationCachePack-' + methodName + '-country-' + #country + '-language-' + #language + '-endpoint-' + #endpoint")
    @Cacheable(value = CacheableType.GRAPHIC_NAVIGATION, key = "caches[0].name + '-GraphicNavigationCachePack-' + methodName + '-country-' + #country + '-language-' + #language + '-endpoint-' + #endpoint")
    public List<GraphicNavigationVO> packGraphicNavigation(InstaCountry country, InstaLanguage language, Integer endpoint) {
        return graphicNavigationPack.packGraphicNavigation(country, language, endpoint);
    }
}
