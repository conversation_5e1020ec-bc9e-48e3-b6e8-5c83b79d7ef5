package com.insta360.store.service.config.cache;

import com.insta360.compass.core.config.cacheSync.BaseCacheSyncConfigure;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpBroadcastChannelEnum;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wbt
 * @Date: 2022/12/10
 * @Description:
 */
@Configuration
public class StoreCacheSyncConfiguration extends BaseCacheSyncConfigure {

    @Override
    public MessageTcpBroadcastChannelEnum getMessageTcpBroadcastChannelEnum() {
        return MessageTcpBroadcastChannelEnum.store_cache_update_sync;
    }
}
