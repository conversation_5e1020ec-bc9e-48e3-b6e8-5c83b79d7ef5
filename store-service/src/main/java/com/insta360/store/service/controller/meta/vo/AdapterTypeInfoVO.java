package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.AdapterTypeInfo;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/11/8
 * @Description:
 */
public class AdapterTypeInfoVO implements Serializable {

    /**
     * 适配类型id
     */
    private Integer adapterTypeId;

    /**
     * 描述信息
     */
    private String infoName;

    public AdapterTypeInfoVO() {
    }

    public AdapterTypeInfoVO(AdapterTypeInfo adapterTypeInfo) {
        if (Objects.nonNull(adapterTypeInfo)) {
            BeanUtil.copyProperties(adapterTypeInfo, this);
        }
    }

    public Integer getAdapterTypeId() {
        return adapterTypeId;
    }

    public void setAdapterTypeId(Integer adapterTypeId) {
        this.adapterTypeId = adapterTypeId;
    }

    public String getInfoName() {
        return infoName;
    }

    public void setInfoName(String infoName) {
        this.infoName = infoName;
    }

    @Override
    public String toString() {
        return "AdapterTypeInfoVO{" +
                "adapterTypeId=" + adapterTypeId +
                ", infoName='" + infoName + '\'' +
                '}';
    }
}
