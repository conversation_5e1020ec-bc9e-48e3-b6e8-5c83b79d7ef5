package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.HomepageItemCommodityGroup;
import com.insta360.store.service.controller.product.vo.CommodityTagInfoVO;
import com.insta360.store.service.controller.review.vo.ReviewRateInfoVO;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/09/16
 * @Description:
 */
public class HomeItemCommodityGroupVO implements Serializable {

    private Integer id;

    /**
     * 类目id
     */
    private Integer homeItemId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 适配机型名称列表
     */
    private List<String> adapterTypeNames;

    /**
     * 套餐关联的tag信息
     */
    private List<CommodityTagInfoVO> tagInfos;

    /**
     * 对应的套餐详细信息
     */
    private HomeItemCommodityInfoVO itemInfos;

    /**
     * 价格列表
     */
    private HomeItemPriceVO homeItemPrice;

    /**
     * 销售状态
     */
    private HomeItemCommoditySaleStateVO commoditySaleState;

    /**
     * 交易规则
     */
    private HomeItemCommodityTradeRuleVO commodityTradeRule;

    /**
     * 套餐功能描述
     */
    private CommodityFunctionDescriptionVO functionDescription;

    /**
     * 评论星级
     */
    private ReviewRateInfoVO reviewRateInfo;

    /**
     * 产品内部名称
     */
    private String productInternalName;

    public HomeItemCommodityGroupVO() {
    }

    public HomeItemCommodityGroupVO(HomepageItemCommodityGroup homepageItemCommodityGroup) {
        if (homepageItemCommodityGroup != null) {
            BeanUtil.copyProperties(homepageItemCommodityGroup, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getHomeItemId() {
        return homeItemId;
    }

    public void setHomeItemId(Integer homeItemId) {
        this.homeItemId = homeItemId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public List<String> getAdapterTypeNames() {
        return adapterTypeNames;
    }

    public void setAdapterTypeNames(List<String> adapterTypeNames) {
        this.adapterTypeNames = adapterTypeNames;
    }

    public List<CommodityTagInfoVO> getTagInfos() {
        return tagInfos;
    }

    public void setTagInfos(List<CommodityTagInfoVO> tagInfos) {
        this.tagInfos = tagInfos;
    }

    public HomeItemCommodityInfoVO getItemInfos() {
        return itemInfos;
    }

    public void setItemInfos(HomeItemCommodityInfoVO itemInfos) {
        this.itemInfos = itemInfos;
    }

    public HomeItemPriceVO getHomeItemPrice() {
        return homeItemPrice;
    }

    public void setHomeItemPrice(HomeItemPriceVO homeItemPrice) {
        this.homeItemPrice = homeItemPrice;
    }

    public HomeItemCommoditySaleStateVO getCommoditySaleState() {
        return commoditySaleState;
    }

    public void setCommoditySaleState(HomeItemCommoditySaleStateVO commoditySaleState) {
        this.commoditySaleState = commoditySaleState;
    }

    public HomeItemCommodityTradeRuleVO getCommodityTradeRule() {
        return commodityTradeRule;
    }

    public void setCommodityTradeRule(HomeItemCommodityTradeRuleVO commodityTradeRule) {
        this.commodityTradeRule = commodityTradeRule;
    }

    public CommodityFunctionDescriptionVO getFunctionDescription() {
        return functionDescription;
    }

    public void setFunctionDescription(CommodityFunctionDescriptionVO functionDescription) {
        this.functionDescription = functionDescription;
    }

    public ReviewRateInfoVO getReviewRateInfo() {
        return reviewRateInfo;
    }

    public void setReviewRateInfo(ReviewRateInfoVO reviewRateInfo) {
        this.reviewRateInfo = reviewRateInfo;
    }

    public String getProductInternalName() {
        return productInternalName;
    }

    public void setProductInternalName(String productInternalName) {
        this.productInternalName = productInternalName;
    }

    @Override
    public String toString() {
        return "HomeItemCommodityGroupVO{" +
                "id=" + id +
                ", homeItemId=" + homeItemId +
                ", productId=" + productId +
                ", commodityId=" + commodityId +
                ", orderIndex=" + orderIndex +
                ", adapterTypeNames=" + adapterTypeNames +
                ", tagInfos=" + tagInfos +
                ", itemInfos=" + itemInfos +
                ", homeItemPrice=" + homeItemPrice +
                ", commoditySaleState=" + commoditySaleState +
                ", commodityTradeRule=" + commodityTradeRule +
                ", functionDescription=" + functionDescription +
                ", reviewRateInfo=" + reviewRateInfo +
                ", productInternalName='" + productInternalName + '\'' +
                '}';
    }
}
