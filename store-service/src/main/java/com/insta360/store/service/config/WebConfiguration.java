package com.insta360.store.service.config;

import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.insta360.compass.core.bean.ApplicationContextHolder;
import com.insta360.compass.core.web.config.BaseWebMvcConfigure;
import com.insta360.compass.core.web.security.AuthorizationHandler;
import com.insta360.store.service.common.StoreApiHeaderParser;
import com.insta360.store.service.common.authorization.WebAuthorizationHandler;
import com.insta360.store.service.common.interceptor.*;
import org.beetl.core.resource.ClasspathResourceLoader;
import org.beetl.ext.spring.BeetlGroupUtilConfiguration;
import org.beetl.ext.spring.BeetlSpringViewResolver;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: mowi
 * @Date: 2018/11/26
 * @Description:
 */
@Configuration
public class WebConfiguration extends BaseWebMvcConfigure {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        super.addInterceptors(registry);

        // 项目自定义的拦截器必须在super方法之后定义，因为可能会依赖父类中定义的拦截器
        registry.addInterceptor(accessUserInterceptor());
        registry.addInterceptor(resellerInterceptor());
        registry.addInterceptor(productDataPreReleaseInterceptor());
        registry.addInterceptor(paymentResultInterceptor());
        registry.addInterceptor(traceLogInterceptor());

        // 有可能依赖用户的信息
        registry.addInterceptor(orderInterceptor());
        registry.addInterceptor(authorizationHandler());
        registry.addInterceptor(tradeUpOrderInterceptor());
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        super.configureMessageConverters(converters);

        // 把返回值转换成xml
        converters.add(new StringHttpMessageConverter());
    }

    @Bean
    public StoreApiHeaderParser storeApiHeaderParser() {
        return new StoreApiHeaderParser();
    }

    @Bean
    public AuthorizationHandler authorizationHandler() {
        return new WebAuthorizationHandler();
    }

    @Bean
    public AccessUserInterceptor accessUserInterceptor() {
        return new AccessUserInterceptor();
    }

    @Bean
    public ResellerInterceptor resellerInterceptor() {
        return new ResellerInterceptor();
    }

    @Bean
    public TraceLogInterceptor traceLogInterceptor() {
        return new TraceLogInterceptor();
    }

    @Bean
    public OrderInterceptor orderInterceptor() {
        return new OrderInterceptor(ApplicationContextHolder.getApplicationContext());
    }

    @Bean
    public TradeUpOrderInterceptor tradeUpOrderInterceptor() {
        return new TradeUpOrderInterceptor(ApplicationContextHolder.getApplicationContext());
    }

    @Bean
    public ProductDataPreReleaseInterceptor productDataPreReleaseInterceptor() {
        return new ProductDataPreReleaseInterceptor();
    }

    @Bean
    public PaymentResultInterceptor paymentResultInterceptor() {
        return new PaymentResultInterceptor();
    }

    /**
     * beetl 视图模版解析器
     *
     * @return
     */
    @Bean
    public BeetlSpringViewResolver beetlSpringViewResolver() {
        BeetlGroupUtilConfiguration beetlGroupUtilConfiguration = new BeetlGroupUtilConfiguration();
        beetlGroupUtilConfiguration.setResourceLoader(new ClasspathResourceLoader(this.getClass().getClassLoader(), "templates"));
        beetlGroupUtilConfiguration.init();

        BeetlSpringViewResolver beetlSpringViewResolver = new BeetlSpringViewResolver();
        beetlSpringViewResolver.setConfig(beetlGroupUtilConfiguration);
        beetlSpringViewResolver.setViewNames("*.btl", "*.html");
        return beetlSpringViewResolver;
    }

    /**
     * 自定义 FastJson Serializer Converters
     *
     * @return
     */
    @Bean
    public HttpMessageConverters fastJsonConverters() {
        FastJsonHttpMessageConverter fastJsonConverter = new FastJsonHttpMessageConverter();

        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(SerializerFeature.DisableCircularReferenceDetect);
        fastJsonConfig.setFeatures(Feature.OrderedField);
        fastJsonConverter.setFastJsonConfig(fastJsonConfig);
        // 请求支持类型
        fastJsonConverter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, new MediaType("application", "*+json")));

        return new HttpMessageConverters(fastJsonConverter);
    }
}
