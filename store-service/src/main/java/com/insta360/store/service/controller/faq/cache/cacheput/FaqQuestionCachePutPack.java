package com.insta360.store.service.controller.faq.cache.cacheput;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.faq.format.FaqOtherQuestionBindPack;
import com.insta360.store.service.controller.faq.format.FaqQuestionBindPack;
import com.insta360.store.service.controller.faq.vo.FaqCategoryVO;
import com.insta360.store.service.controller.faq.vo.FaqOtherQuestionBindVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2023/08/21
 * @Description:
 */
@Component
public class FaqQuestionCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(FaqQuestionCachePutPack.class);

    @Autowired
    FaqQuestionBindPack questionBindPack;

    @Autowired
    FaqOtherQuestionBindPack otherQuestionBindPack;

    /**
     * faq页面缓存（类目结构）
     *
     * @param type
     * @param pageKey
     * @param language
     * @param country
     */
    @CachePut(value = CacheableType.FAQ_QUESTION, key = "caches[0].name  + '-QuestionCachePack-' + methodName + '-type-' + #type + '-pageKey-' + #pageKey + '-language-' + #language + '-country-' + #country")
    public FaqCategoryVO doPackCacheQuestionInfo(String type, String pageKey, InstaLanguage language, InstaCountry country) {
        FaqCategoryVO faqCategory = questionBindPack.doPackQuestionBind(type, pageKey, language, country);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[type:%s,pageKey:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.FAQ_QUESTION, "doPackCacheQuestionInfo", type, pageKey, language, country, faqCategory));
        return faqCategory;
    }

    /**
     * faq页面缓存（非类目结构）
     *
     * @param type
     * @param pageKey
     * @param language
     * @param country
     * @return
     */
    @CachePut(value = CacheableType.FAQ_OTHER_QUESTION, key = "caches[0].name  + '-OtherQuestionCachePack-' + methodName + '-type-' + #type + '-pageKey-' + #pageKey + '-language-' + #language + '-country-' + #country")
    public FaqOtherQuestionBindVO doPackCacheOtherQuestionInfo(String type, String pageKey, InstaLanguage language, InstaCountry country) {
        FaqOtherQuestionBindVO faqOtherQuestionBind = otherQuestionBindPack.doPackOtherQuestionBind(type, pageKey, language, country);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[type:%s,pageKey:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.FAQ_OTHER_QUESTION, "doPackCacheOtherQuestionInfo", type, pageKey, language, country, faqOtherQuestionBind));
        return faqOtherQuestionBind;
    }
}
