package com.insta360.store.service.controller.product.cache.cacheput;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.product.format.CommodityDifferencePack;
import com.insta360.store.service.controller.product.format.CommodityPack;
import com.insta360.store.service.controller.product.format.CommodityRecommendationPack;
import com.insta360.store.service.controller.product.vo.CommodityDifferenceVO;
import com.insta360.store.service.controller.product.vo.CommodityRecommendationVO;
import com.insta360.store.service.controller.product.vo.CommodityVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2023/11/6
 * @Description: 套餐更新缓存层
 */
@Component
public class CommodityCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommodityCachePutPack.class);

    @Autowired
    CommodityPack commodityPack;

    @Autowired
    CommodityDifferencePack commodityDifferencePack;

    @Autowired
    CommodityRecommendationPack commodityRecommendationPack;

    /**
     * 更新缓存单个套餐详细信息
     *
     * @param commodityId
     * @param country
     * @param language
     * @return
     */
    @CachePut(value = CacheableType.COMMODITY_INFO, key = "caches[0].name + '-CommodityCachePack-' + methodName + '-commodityId-' + #commodityId + '-country-' + #country + '-language-' + #language")
    public CommodityVO doPackCacheCommodityInfo(Integer commodityId, InstaCountry country, InstaLanguage language) {
        CommodityVO commodityVo = commodityPack.doPackCacheCommodityInfo(commodityId, country, language);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[commodityId:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.COMMODITY_INFO, "doPackCacheCommodityInfo", commodityId, country, language, commodityVo));
        return commodityVo;
    }

    /**
     * 更新缓存指定产品所有套餐的包装清单信息
     *
     * @param productId
     * @param language
     * @return
     */
    @CachePut(value = CacheableType.COMMODITY_DIFFERENCE, key = "caches[0].name + '-CommodityCachePack-' + methodName + '-productId-' + #productId + '-language-' + #language+ '-country-' + #country")
    public CommodityDifferenceVO doPackCacheCommodityDifference(Integer productId, InstaLanguage language, InstaCountry country) {
        CommodityDifferenceVO commodityDifferenceVo = commodityDifferencePack.doPackCommodityDifference(productId, language, country);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[productId:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.COMMODITY_DIFFERENCE, "doPackCacheCommodityDifference", productId, country, language, commodityDifferenceVo));
        return commodityDifferenceVo;
    }

    /**
     * 获取指定产品所有套餐的推荐配件信息
     *
     * @param productId
     * @param language
     * @param country
     * @return
     */
    @CachePut(value = CacheableType.COMMODITY_RECOMMENDATION, key = "caches[0].name + '-CommodityCachePack-' + methodName + '-productId-' + #productId + '-country-' + #country + '-language-' + #language")
    public CommodityRecommendationVO doPackCacheCommodityRecommendation(Integer productId, InstaCountry country, InstaLanguage language) {
        CommodityRecommendationVO commodityRecommendationVo = commodityRecommendationPack.doPackCommoditiesRecommendation(productId, country, language);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[productId:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.COMMODITY_RECOMMENDATION, "doPackCacheCommodityRecommendation", productId, country, language, commodityRecommendationVo));
        return commodityRecommendationVo;
    }
}
