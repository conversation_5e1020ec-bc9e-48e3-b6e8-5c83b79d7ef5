package com.insta360.store.service.controller.user.controller;

import com.google.common.collect.Lists;
import com.insta360.compass.core.bean.PageQuery;
import com.insta360.compass.core.bean.PageResult;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.security.annotation.Authorization;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.discount.dto.ao.giftcard.GiftCardQueryPageAO;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.user.format.GiftCardPack;
import com.insta360.store.service.controller.user.vo.UserGiftCardNewVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: mowi
 * @Date: 2019-03-19
 * @Description:
 */
@RestController
public class UserGiftCardApi extends BaseApi {

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    GiftCardPack giftCardPack;

    /**
     * 获取用户的代金券
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization
    @GetMapping("/store/trade/coupon/getUserGiftCards")
    public Response<? extends Map> getUserGiftCards(@RequestParam(required = false, value = "page_size") Integer pageSize,
                                                    @RequestParam(required = false, value = "page_number") Integer pageNumber,
                                                    @RequestParam(required = false, value = "include_used", defaultValue = "true") Boolean includeUsed,
                                                    @RequestParam(required = false, value = "include_expired", defaultValue = "true") Boolean includeExpired) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNumber(pageNumber, 1);
        pageQuery.setPageSize(pageSize, 10);

        GiftCardQueryPageAO giftCardQueryPageAO = new GiftCardQueryPageAO();
        giftCardQueryPageAO.setPageQuery(pageQuery);
        giftCardQueryPageAO.setBindEmail(getAccessUser().getUsername());
        giftCardQueryPageAO.setIncludeExpired(includeExpired);
        giftCardQueryPageAO.setIncludeUsed(includeUsed);

        PageResult<GiftCard> giftCardPageResult = giftCardService.queryUserGiftCardPage(giftCardQueryPageAO);
        List<GiftCard> giftCards = giftCardPageResult.getList();
        giftCards = Optional.ofNullable(giftCards)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(
                        giftCard ->
                                !"tradeup".equals(giftCard.getInfoTag()))
                .collect(Collectors.toList());
        List<UserGiftCardNewVO> giftCardVOS = giftCardPack.doPack(giftCards, getApiLanguage());
        return Response.ok(giftCardPageResult.replaceList(giftCardVOS));
    }
}
