package com.insta360.store.service.rpc.commodity.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2025/05/14
 * @Description:
 */
public class PspCommodityInfoVO implements Serializable {

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 现价
     */
    private Float amount;

    /**
     * 货币符号
     */
    private String signal;

    /**
     * 货币
     */
    private String currency;

    /**
     * 套餐销售国家/地区
     */
    private List<String> saleCountries;

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public String getSignal() {
        return signal;
    }

    public void setSignal(String signal) {
        this.signal = signal;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public List<String> getSaleCountries() {
        return saleCountries;
    }

    public void setSaleCountries(List<String> saleCountries) {
        this.saleCountries = saleCountries;
    }

    @Override
    public String toString() {
        return "PspCommodityInfoVO{" +
                "commodityId=" + commodityId +
                ", commodityName='" + commodityName + '\'' +
                ", amount=" + amount +
                ", signal='" + signal + '\'' +
                ", currency='" + currency + '\'' +
                ", saleCountries=" + saleCountries +
                '}';
    }
}
