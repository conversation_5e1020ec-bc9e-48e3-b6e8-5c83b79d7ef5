package com.insta360.store.service.controller.meta.cache.cacheable;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.NavigationBarCategoryPack;
import com.insta360.store.service.controller.meta.vo.NavigationBarCategoryInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * @Author: wbt
 * @Date: 2022/02/23
 * @Description:
 */
@Component
public class NavigationBarCachePack {

    @Autowired
    NavigationBarCategoryPack navigationBarCategoryPack;

    /**
     * 缓存导航栏数据
     *
     * @param country
     * @param language
     * @return
     */
    @CacheTtlMonitor(value = CacheableType.NAVIGATION_BAR_CATEGORY_KEY, cacheKey = "'-NavigationBarCategoryCachePack-' + methodName + '-country-' + #country + '-language-' + #language")
    @Cacheable(value = CacheableType.NAVIGATION_BAR_CATEGORY_KEY, key = "caches[0].name + '-NavigationBarCategoryCachePack-' + methodName + '-country-' + #country + '-language-' + #language")
    public NavigationBarCategoryInfoVO doPackCacheNavigationBarCategoryInfo(InstaCountry country, InstaLanguage language) {
        return navigationBarCategoryPack.doPackCacheNavigationBarCategoryInfo(country, language);
    }
}
