package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.GraphicNavigationMain;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/3/14
 * @Description:
 */
public class GraphicNavigationMainVO implements Serializable {

    private Integer id;

    /**
     * 图文导航id
     */
    private Integer graphicNavigationId;

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 图片链接
     */
    private String imageLink;

    /**
     * 内部名称
     */
    private String insideName;

    /**
     * 网页链接
     */
    private String webLink;

    /**
     * 产品url key
     */
    private String urlKey;

    /**
     * 新品标签
     */
    private Boolean newTag;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * ng infos
     */
    GraphicNavigationInfoVO graphicNavigationInfo;

    public GraphicNavigationMainVO() {
    }

    public GraphicNavigationMainVO(GraphicNavigationMain graphicNavigationMain) {
        if (Objects.nonNull(graphicNavigationMain)) {
            BeanUtil.copyProperties(graphicNavigationMain, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGraphicNavigationId() {
        return graphicNavigationId;
    }

    public void setGraphicNavigationId(Integer graphicNavigationId) {
        this.graphicNavigationId = graphicNavigationId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getImageLink() {
        return imageLink;
    }

    public void setImageLink(String imageLink) {
        this.imageLink = imageLink;
    }

    public String getInsideName() {
        return insideName;
    }

    public void setInsideName(String insideName) {
        this.insideName = insideName;
    }

    public String getWebLink() {
        return webLink;
    }

    public void setWebLink(String webLink) {
        this.webLink = webLink;
    }

    public Boolean getNewTag() {
        return newTag;
    }

    public void setNewTag(Boolean newTag) {
        this.newTag = newTag;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public GraphicNavigationInfoVO getGraphicNavigationInfo() {
        return graphicNavigationInfo;
    }

    public void setGraphicNavigationInfo(GraphicNavigationInfoVO graphicNavigationInfo) {
        this.graphicNavigationInfo = graphicNavigationInfo;
    }

    public String getUrlKey() {
        return urlKey;
    }

    public void setUrlKey(String urlKey) {
        this.urlKey = urlKey;
    }
}
