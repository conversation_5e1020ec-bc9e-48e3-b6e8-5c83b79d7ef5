package com.insta360.store.service.controller.reseller.controller;

import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.common.WebApiContext;

/**
 * @Author: hyc
 * @Date: 2019/2/27
 * @Description:
 */
public class BaseResellerApi extends BaseApi {

    protected Reseller getReseller() {
        WebApiContext apiContext = WebApiContext.get();
        if (apiContext != null) {
            return apiContext.getReseller();
        } else {
            return null;
        }
    }
}
