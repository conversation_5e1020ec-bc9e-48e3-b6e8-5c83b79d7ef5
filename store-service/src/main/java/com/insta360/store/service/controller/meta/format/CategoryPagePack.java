package com.insta360.store.service.controller.meta.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.meta.enums.CategoryTextFilterType;
import com.insta360.store.business.meta.model.*;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.*;
import com.insta360.store.business.meta.service.impl.helper.MetaBatchHelper;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.service.controller.meta.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2023/10/30
 * @Description:
 */
@Component
public class CategoryPagePack {

    private static final Logger LOGGER = LoggerFactory.getLogger(CategoryPagePack.class);

    @Autowired
    ProductCategoryImageTextSelectorService productCategoryImageTextSelectorService;

    @Autowired
    ProductCategoryImageTextInfoService productCategoryImageTextInfoService;

    @Autowired
    ProductCategoryTextFilterMainService productCategoryTextFilterMainService;

    @Autowired
    ProductCategoryTextFilterInfoService productCategoryTextFilterInfoService;

    @Autowired
    ProductCategoryTextFilterService productCategoryTextFilterService;

    @Autowired
    MetaBatchHelper metaBatchHelper;

    /**
     * 图文筛选器数据封装
     *
     * @param categoryFilterVo
     * @param imageTextSelectorList
     * @param imageTextInfos
     * @param categoryKey
     */
    private void packImageTextInfo(AccessoryCategoryFilterVO categoryFilterVo,
                                   List<ProductCategoryImageTextSelector> imageTextSelectorList,
                                   List<ProductCategoryImageTextInfo> imageTextInfos, String categoryKey) {
        Map<Integer, ProductCategoryImageTextInfo> textInfoMap = imageTextInfos.stream()
                .collect(Collectors.toMap(ProductCategoryImageTextInfo::getSelectorId, i -> i));

        categoryFilterVo.setImageTextSelectorList(imageTextSelectorList.stream().map(imageTextSelector -> {
            ProductCategoryImageTextSelectorVO imageTextSelectorVo = new ProductCategoryImageTextSelectorVO(imageTextSelector);
            ProductCategoryImageTextInfo imageTextInfo = textInfoMap.get(imageTextSelector.getId());
            if (Objects.isNull(imageTextInfo) && ProductCategoryMainType.CM_ACCESSORY.name().equals(categoryKey)) {
                LOGGER.error(String.format("图文筛选器id:[%s]没有配置多语言数据", imageTextSelector.getId()));
                FeiShuMessageUtil.storeGeneralMessage(String.format("图文筛选器id:[%s]没有配置多语言数据，请马上处理！", imageTextSelector.getId()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
                return null;
            }

            if (Objects.isNull(imageTextInfo) && ProductCategoryMainType.CM_VIRTUAL_SERVICE.name().equals(categoryKey)) {
                LOGGER.error(String.format("图文筛选器id:[%s]没有配置多语言数据", imageTextSelector.getId()));
                return null;
            }
            imageTextSelectorVo.setImageTextName(imageTextInfo.getImageTextName());
            return imageTextSelectorVo;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
    }

    /**
     * 封装文字筛选器数据
     *
     * @param categoryFilterVo
     * @param country
     * @param language
     * @return
     */
    private void packTextFilterInfo(AccessoryCategoryFilterVO categoryFilterVo, InstaCountry country, InstaLanguage language, String categoryKey) {
        List<ProductCategoryTextFilterMain> productCategoryTextFilterMains = productCategoryTextFilterMainService.listEnabledTextFilter(categoryKey);
        if (CollectionUtils.isEmpty(productCategoryTextFilterMains)) {
            return;
        }

        List<Integer> textFilterMainIds = productCategoryTextFilterMains.stream().map(ProductCategoryTextFilterMain::getId).collect(Collectors.toList());
        List<ProductCategoryTextFilterInfo> productCategoryTextFilterInfos = productCategoryTextFilterInfoService.listByMainIds(textFilterMainIds, country, language);
        if (CollectionUtils.isEmpty(productCategoryTextFilterInfos)) {
            return;
        }

        // 文字筛选器数据封装
        Map<Integer, ProductCategoryTextFilterInfo> categoryTextFilterInfoMap = productCategoryTextFilterInfos.stream().collect(Collectors.toMap(ProductCategoryTextFilterInfo::getTextFilterMainId, o -> o));
        List<ProductCategoryTextFilterMainVO> categoryTextFilterMainVoList = productCategoryTextFilterMains.stream().map(productCategoryTextFilterMain -> {
            Integer textFilterMainId = productCategoryTextFilterMain.getId();
            List<ProductCategoryTextFilter> productCategoryTextFilters = productCategoryTextFilterService.listTextFilterByMainId(textFilterMainId);
            List<Integer> textFilterIds = productCategoryTextFilters.stream()
                    .map(ProductCategoryTextFilter::getAdapterTypeMainId).collect(Collectors.toList());

            // 筛选器title多语言
            ProductCategoryTextFilterInfo productCategoryTextFilterInfo = categoryTextFilterInfoMap.get(textFilterMainId);
            if (Objects.isNull(productCategoryTextFilterInfo)) {
                return null;
            }

            ProductCategoryTextFilterMainVO textFilterMainVo = new ProductCategoryTextFilterMainVO(productCategoryTextFilterMain);
            textFilterMainVo.setTextFilterName(productCategoryTextFilterInfo.getTextFilterName());

            // 筛选器多语言信息
            selectBuilder(categoryFilterVo, language, productCategoryTextFilterMain.getFilterType(), textFilterIds, textFilterMainVo, productCategoryTextFilters);
            return textFilterMainVo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        categoryFilterVo.setTextLinks(categoryTextFilterMainVoList);
    }

    /**
     * 选择构建方法
     *
     * @param categoryFilterVo
     * @param language
     * @param filterType
     * @param textFilterIds
     * @param textFilterMainVo
     * @param productCategoryTextFilters
     */
    private void selectBuilder(AccessoryCategoryFilterVO categoryFilterVo, InstaLanguage language, String filterType, List<Integer> textFilterIds, ProductCategoryTextFilterMainVO textFilterMainVo, List<ProductCategoryTextFilter> productCategoryTextFilters) {
        CategoryTextFilterType textFilterType = CategoryTextFilterType.parse(filterType);
        switch (textFilterType) {
            case adapter_type:
                buildAdapterType(categoryFilterVo, language, textFilterIds, textFilterMainVo, productCategoryTextFilters);
                break;
            case scenery_tag:
                buildSceneryTag(language, textFilterIds, textFilterMainVo, productCategoryTextFilters);
                break;
            default:
        }
    }

    /**
     * 构建场景标签参数
     *
     * @param language
     * @param textFilterIds
     * @param textFilterMainVo
     * @param productCategoryTextFilters
     */
    private void buildSceneryTag(InstaLanguage language, List<Integer> textFilterIds, ProductCategoryTextFilterMainVO textFilterMainVo, List<ProductCategoryTextFilter> productCategoryTextFilters) {
        Map<Integer, SceneryTagInfo> sceneryTagInfoMap = metaBatchHelper.sceneryTagMapSceneryTagIds(textFilterIds, language);
        textFilterMainVo.setAdapterTypeList(productCategoryTextFilters.stream().map(productCategoryTextFilter -> {
            ProductCategoryTextFilterVO textFilterVo = new ProductCategoryTextFilterVO(productCategoryTextFilter);
            SceneryTagInfo sceneryTagInfo = sceneryTagInfoMap.get(textFilterVo.getAdapterTypeMainId());
            if (Objects.isNull(sceneryTagInfo)) {
                return null;
            }
            textFilterVo.setAdapterInfoName(sceneryTagInfo.getName());
            return textFilterVo;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
    }

    /**
     * 构建适配机型
     *
     * @param categoryFilterVo
     * @param language
     * @param textFilterIds
     * @param textFilterMainVo
     * @param productCategoryTextFilters
     */
    private void buildAdapterType(AccessoryCategoryFilterVO categoryFilterVo, InstaLanguage language, List<Integer> textFilterIds, ProductCategoryTextFilterMainVO textFilterMainVo, List<ProductCategoryTextFilter> productCategoryTextFilters) {
        Map<Integer, AdapterTypeInfo> adapterTypeInfoMap = metaBatchHelper.adapterTypeMapAdapterTypeIds(textFilterIds, language);
        textFilterMainVo.setAdapterTypeList(productCategoryTextFilters.stream().map(productCategoryTextFilter -> {
            ProductCategoryTextFilterVO textFilterVo = new ProductCategoryTextFilterVO(productCategoryTextFilter);
            AdapterTypeInfo adapterTypeInfo = adapterTypeInfoMap.get(textFilterVo.getAdapterTypeMainId());
            if (Objects.isNull(adapterTypeInfo)) {
                return null;
            }
            textFilterVo.setAdapterInfoName(adapterTypeInfo.getInfoName());
            return textFilterVo;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
        // todo:兼容旧字段
        categoryFilterVo.setTextFilterMain(textFilterMainVo);
    }

    /**
     * 封装默认展示数据
     *
     * @param textSelector
     * @param categoryPageVo
     */
    public List<CategoryPageCommodityVO> doPackCategoryPage(ProductCategoryImageTextSelector textSelector, CategoryPageVO categoryPageVo) {
        Map<String, List<Integer>> selectorMap = categoryPageVo.getSelectorMap();
        List<Integer> topSortCommodityIds = categoryPageVo.getTopSortCommodityIds();
        List<Integer> commodityIds = selectorMap.get(String.valueOf(textSelector.getId()));
        // 默认all筛选器
        if (textSelector.getDefaultDisplay()) {
            int[] commodityIdsArr = categoryPageVo.getCommodityInfoMap().keySet().stream().mapToInt(Integer::parseInt).toArray();
            commodityIds = Arrays.stream(commodityIdsArr).boxed().collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(commodityIds)) {
            return new ArrayList<>(0);
        }

        // 初始化最终套餐信息列表
        List<CategoryPageCommodityVO> commodityList = new ArrayList<>(commodityIds.size() + topSortCommodityIds.size());
        Map<String, CategoryPageCommodityVO> commodityInfoMap = categoryPageVo.getCommodityInfoMap();

        // 置顶排序
        for (Integer topSortCommodityId : topSortCommodityIds) {
            CategoryPageCommodityVO pageCommodityVo = commodityInfoMap.get(String.valueOf(topSortCommodityId));
            if (Objects.nonNull(pageCommodityVo) && commodityIds.contains(topSortCommodityId)) {
                commodityList.add(pageCommodityVo);
            }
        }

        // 普通sku处理&去重
        commodityIds.removeAll(topSortCommodityIds);
        List<CategoryPageCommodityVO> commodityVoList = commodityIds.stream().map(commodityId -> {
            CategoryPageCommodityVO pageCommodityVo = commodityInfoMap.get(String.valueOf(commodityId));
            if (Objects.isNull(pageCommodityVo)) {
                return null;
            }
            return pageCommodityVo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(commodityVoList)) {
            // 根据多规则排序
            commodityList.addAll(sortCommodityInfos(commodityVoList));
        }
        return commodityList.stream().limit(18).collect(Collectors.toList());
    }

    /**
     * 根据多规则排序
     * 1. 运营后台置顶产品：根据运营配置的顺序
     * 2. 新品标签：商品被打上新品标签的在前
     * 3. 机型：商品为新机型且适配标签少的排在最前
     * ***1. 新旧，机型最新的在前（根据适配机型id）
     * ***2. 适配机型标签少的靠前（专属），适配机型标签多的靠后（通用）
     * ***3. 适配机型标签为空排在机型排序规则的最后
     * 4. 价格：有活动价且减价多的在前
     * 5. 商品创建时间：时间最新的在前
     * 6. 缺货的产品置底：置底的产品按商品创建时间排序
     *
     * @param commodityVoList
     * @return
     */
    private List<CategoryPageCommodityVO> sortCommodityInfos(List<CategoryPageCommodityVO> commodityVoList) {
        List<CategoryPageCommodityVO> normalSkuList = new ArrayList<>(commodityVoList.size());
        List<CategoryPageCommodityVO> outOfStockList = commodityVoList.stream().filter(commodityVo -> {
            if (SaleState.out_of_stock.getCode() == commodityVo.getItemInfos().getCommoditySaleState()) {
                return true;
            }
            normalSkuList.add(commodityVo);
            return false;
        }).collect(Collectors.toList());

        // 排序
        normalSkuList.sort(Comparator.comparing(CategoryPageCommodityVO::tagInfoSort, Comparator.reverseOrder())
                .thenComparing(CategoryPageCommodityVO::adapterTypeSizeSort, Comparator.reverseOrder())
                .thenComparing(commodityVo -> commodityVo.getAdapterTypeNames().size())
                .thenComparing(CategoryPageCommodityVO::getPriceSort, Comparator.reverseOrder())
                .thenComparing(CategoryPageCommodityVO::getCreateTime, Comparator.reverseOrder())
        );

        outOfStockList.sort(Comparator.comparing(CategoryPageCommodityVO::getCreateTime, Comparator.reverseOrder()));
        normalSkuList.addAll(outOfStockList);
        return normalSkuList;
    }

    /**
     * 根据类目页key查询类目页筛选器
     *
     * @param country
     * @param language
     * @param categoryKey
     * @return
     */
    public AccessoryCategoryFilterVO listCategoryFilter(String categoryKey, InstaCountry country, InstaLanguage language) {
        AccessoryCategoryFilterVO categoryFilterVo = new AccessoryCategoryFilterVO();

        List<ProductCategoryImageTextSelector> imageTextSelectorList = productCategoryImageTextSelectorService.listEnabledByCategoryKey(categoryKey);
        if (CollectionUtils.isEmpty(imageTextSelectorList) && ProductCategoryMainType.CM_ACCESSORY.name().equals(categoryKey)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("国家:[%s]，类目页:[%s]没有配置图文筛选器，请马上处理！", country.name(), categoryKey), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
            return categoryFilterVo;
        }

        if (CollectionUtils.isEmpty(imageTextSelectorList) && ProductCategoryMainType.CM_VIRTUAL_SERVICE.name().equals(categoryKey)) {
            LOGGER.info(String.format("国家:[%s],类目页:[%s]没有配置图文筛选器", country.name(), categoryKey));
            return categoryFilterVo;
        }

        List<Integer> selectorIds = imageTextSelectorList.stream().map(ProductCategoryImageTextSelector::getId).collect(Collectors.toList());
        List<ProductCategoryImageTextInfo> imageTextInfos = productCategoryImageTextInfoService.listBySelectorIdsAndCountry(country, language, selectorIds);
        if (CollectionUtils.isEmpty(imageTextInfos) && ProductCategoryMainType.CM_ACCESSORY.name().equals(categoryKey)) {
            FeiShuMessageUtil.storeGeneralMessage(String.format("国家:[%s]，类目页:[%s]图文筛选器【全部没有】配置多语言数据，请马上处理！", country.name(), categoryKey), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.PY);
            return categoryFilterVo;
        }

        if (CollectionUtils.isEmpty(imageTextInfos) && ProductCategoryMainType.CM_VIRTUAL_SERVICE.name().equals(categoryKey)) {
            LOGGER.info(String.format("国家:[%s],类目页:[%s]图文筛选器[全部没有]配置多语言数据", country.name(), categoryKey));
            return categoryFilterVo;
        }

        // 图文筛选器
        packImageTextInfo(categoryFilterVo, imageTextSelectorList, imageTextInfos, categoryKey);

        // 文字筛选器
        packTextFilterInfo(categoryFilterVo, country, language, categoryKey);
        return categoryFilterVo;
    }
}
