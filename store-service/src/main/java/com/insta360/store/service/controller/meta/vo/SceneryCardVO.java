package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategorySceneryCardMain;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: py
 * @create: 2025-03-17 17:44
 */
public class SceneryCardVO implements Serializable {

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 套餐ID
     */
    private Integer commodityId;

    /**
     * 套餐的多语言名称
     */
    private String commodityName;

    /**
     * 卡片素材
     */
    private List<SceneryCardResourceVO> sceneryCardResourceList;

    public SceneryCardVO() {
    }

    public SceneryCardVO(ProductCategorySceneryCardMain productCategorySceneryCardMain) {
        if (productCategorySceneryCardMain != null) {
            BeanUtil.copyProperties(productCategorySceneryCardMain, this);
        }
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public List<SceneryCardResourceVO> getSceneryCardResourceList() {
        return sceneryCardResourceList;
    }

    public void setSceneryCardResourceList(List<SceneryCardResourceVO> sceneryCardResourceList) {
        this.sceneryCardResourceList = sceneryCardResourceList;
    }

    @Override
    public String toString() {
        return "SceneryCardVO{" +
                "orderIndex=" + orderIndex +
                ", productId=" + productId +
                ", commodityId=" + commodityId +
                ", commodityName='" + commodityName + '\'' +
                ", sceneryCardResourceList=" + sceneryCardResourceList +
                '}';
    }
}
