package com.insta360.store.service.rpc.cloud.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.cloud.bo.CloudBenefitBindResultBO;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 商城权益使用VO
 * @Date 2024/5/13
 */
public class UseBeneFitVO implements Serializable {

    /**
     * 存在care权益
     */
    private Boolean existCare;

    /**
     * 存在延保权益
     */
    private Boolean existExtend;

    /**
     * 保险类型
     */
    private String insuranceType;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 序列号
     */
    private String deviceSerial;

    /**
     * 代金券code
     */
    private String giftCardCode;

    /**
     * 是否为商城支持且生效的地区
     */
    private Boolean storeSale;

    /**
     * care权益状态
     */
    private Integer careBenefit;

    /**
     * 延保权益状态
     */
    private Integer extendBenefit;

    /**
     * 配件权益状态
     */
    private Integer accessoryBenefit;

    /**
     * 下一台相机优惠
     */
    private Integer cameraDiscount;

    public UseBeneFitVO() {
    }

    public UseBeneFitVO(Integer code) {
        this.careBenefit = code;
        this.extendBenefit = code;
        this.accessoryBenefit = code;
        this.cameraDiscount = code;
    }

    public UseBeneFitVO(String giftCardCode) {
        this.giftCardCode = giftCardCode;
    }

    public UseBeneFitVO(CloudBenefitBindResultBO cloudBenefitBindResultParam) {
        if (cloudBenefitBindResultParam != null) {
            BeanUtil.copyProperties(cloudBenefitBindResultParam, this);
        }
    }

    public Boolean getExistCare() {
        return existCare;
    }

    public void setExistCare(Boolean existCare) {
        this.existCare = existCare;
    }

    public Boolean getExistExtend() {
        return existExtend;
    }

    public void setExistExtend(Boolean existExtend) {
        this.existExtend = existExtend;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getGiftCardCode() {
        return giftCardCode;
    }

    public void setGiftCardCode(String giftCardCode) {
        this.giftCardCode = giftCardCode;
    }

    public Boolean getStoreSale() {
        return storeSale;
    }

    public void setStoreSale(Boolean storeSale) {
        this.storeSale = storeSale;
    }

    public Integer getCareBenefit() {
        return careBenefit;
    }

    public void setCareBenefit(Integer careBenefit) {
        this.careBenefit = careBenefit;
    }

    public Integer getExtendBenefit() {
        return extendBenefit;
    }

    public void setExtendBenefit(Integer extendBenefit) {
        this.extendBenefit = extendBenefit;
    }

    public Integer getAccessoryBenefit() {
        return accessoryBenefit;
    }

    public void setAccessoryBenefit(Integer accessoryBenefit) {
        this.accessoryBenefit = accessoryBenefit;
    }

    public Integer getCameraDiscount() {
        return cameraDiscount;
    }

    public void setCameraDiscount(Integer cameraDiscount) {
        this.cameraDiscount = cameraDiscount;
    }

    @Override
    public String toString() {
        return "UseBeneFitVO{" +
                "existCare=" + existCare +
                ", existExtend=" + existExtend +
                ", insuranceType='" + insuranceType + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", giftCardCode='" + giftCardCode + '\'' +
                ", storeSale=" + storeSale +
                ", careBenefit=" + careBenefit +
                ", extendBenefit=" + extendBenefit +
                ", accessoryBenefit=" + accessoryBenefit +
                ", cameraDiscount=" + cameraDiscount +
                '}';
    }
}
