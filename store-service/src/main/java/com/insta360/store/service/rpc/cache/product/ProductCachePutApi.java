package com.insta360.store.service.rpc.cache.product;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.product.cache.cacheput.ProductCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 产品页缓存更新RPC服务
 * @Date 2023/11/1
 */
@RestController
public class ProductCachePutApi {

    @Autowired
    ProductCachePutPack productCachePutPack;

    /**
     * 获取产品页缓存信息
     *
     * @param productId
     * @param country
     * @param language
     */
    @GetMapping("/rpc/store/service/cacheput/product/getInfo")
    public void getProductInfo(@RequestParam Integer productId, @RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        productCachePutPack.doPackCacheProductCacheInfo(productId, country, language);
    }

    /**
     * 获取产品配置适配缓存信息
     *
     * @param productId
     * @param language
     */
    @GetMapping("/rpc/store/service/cacheput/product/getProductAccessoryCompatibility")
    public void getProductAccessoryCompatibility(@RequestParam Integer productId, @RequestParam InstaLanguage language) {
        productCachePutPack.doPackCacheProductAccessoryCompatibility(productId, language);
    }
}
