package com.insta360.store.service.rpc.insurance.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.insurance.model.ExtendInsurance;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/06/04
 * @Description: RPC接口返回值使用驼峰
 */
public class ExtendInsuranceVO implements Serializable {

    private Integer id;

    /**
     * 保险号
     */
    private String insuranceNumber;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 序列号
     */
    private String deviceSerial;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 是否包含赠品延保
     */
    private Boolean includeGifts;

    /**
     * 是否有购买延保
     */
    private Boolean isBuy;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 区域
     */
    private String area;

    /**
     * 绑定时间
     */
    private LocalDateTime bindTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 保险作废
     */
    private Boolean enabled;

    public ExtendInsuranceVO() {
    }

    public ExtendInsuranceVO(ExtendInsurance extendInsurance) {
        if (extendInsurance != null) {
            BeanUtil.copyProperties(extendInsurance, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getIsBuy() {
        return isBuy;
    }

    public void setIsBuy(Boolean buy) {
        isBuy = buy;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getIncludeGifts() {
        return includeGifts;
    }

    public void setIncludeGifts(Boolean includeGifts) {
        this.includeGifts = includeGifts;
    }

    public LocalDateTime getBindTime() {
        return bindTime;
    }

    public void setBindTime(LocalDateTime bindTime) {
        this.bindTime = bindTime;
    }

    @Override
    public String toString() {
        return "ExtendInsuranceVO{" +
                "id=" + id +
                ", insuranceNumber='" + insuranceNumber + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", includeGifts=" + includeGifts +
                ", isBuy=" + isBuy +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", area='" + area + '\'' +
                ", bindTime=" + bindTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", expireTime=" + expireTime +
                ", enabled=" + enabled +
                '}';
    }
}
