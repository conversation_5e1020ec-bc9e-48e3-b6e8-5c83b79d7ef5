package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.meta.cache.cacheput.BannerCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wbt
 * @Date: 2023/12/14
 * @Description:
 */
@RestController
public class BannerCachePutApi {

    @Autowired
    BannerCachePutPack bannerCachePutPack;

    /**
     * banner数据缓存
     *
     * @param country
     * @param language
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/meta/banner/getBanner")
    public void getBanner(@RequestParam InstaCountry country, @RequestParam InstaLanguage language, @RequestParam String app) {
        bannerCachePutPack.getBanner(country, language, app);
    }
}
