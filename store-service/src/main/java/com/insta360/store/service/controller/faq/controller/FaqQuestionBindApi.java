package com.insta360.store.service.controller.faq.controller;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.faq.cache.cacheable.FaqQuestionCacheablePack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2022/4/27
 * @Description:
 */
@RestController
public class FaqQuestionBindApi extends BaseApi {

    @Autowired
    FaqQuestionCacheablePack questionCachePack;

    /**
     * 获取页面绑定qa(类目结构)
     *
     * @param type
     * @param pageKey
     * @return
     */
    @GetMapping("/store/faq/listQuestionBind")
    public Response<? extends Map> listQuestionBind(@RequestParam(required = false, value = "type") String type,
                                                    @RequestParam(required = false, value = "pageKey") String pageKey) {
        return Response.ok("faqQuestionBinds", questionCachePack.doPackCacheQuestionInfo(type, pageKey, getApiLanguage(), getApiCountry()));
    }
}
