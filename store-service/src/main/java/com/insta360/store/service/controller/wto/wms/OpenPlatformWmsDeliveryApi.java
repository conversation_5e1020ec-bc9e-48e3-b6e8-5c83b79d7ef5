package com.insta360.store.service.controller.wto.wms;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.annotations.AvoidRepeatableCommit;
import com.insta360.compass.core.common.BaseController;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.libs.security.annotations.CheckSecurity;
import com.insta360.store.business.integration.wto.wms.bo.WmsExecuteBO;
import com.insta360.store.business.integration.wto.wms.bo.WmsOrderDeliveryCreateShipmentBO;
import com.insta360.store.business.integration.wto.wms.dto.OrderDeliveryCreateShipmentDTO;
import com.insta360.store.business.outgoing.mq.wto.helper.StoreDataSyncOmsMessageSendHelper;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

/**
 * @description:
 * @author: py
 * @create: 2025-01-15 15:21
 */
@RestController
public class OpenPlatformWmsDeliveryApi extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpenPlatformWmsDeliveryApi.class);

    @Autowired
    StoreDataSyncOmsMessageSendHelper storeDataSyncOmsMessageSendHelper;

    /**
     * WMS调用商城接口创建运单
     *
     * @param orderDeliveryCreateShipmentParam
     * @return
     */
    @PostMapping("/store/openapi/order/delivery/createShipment")
    @AvoidRepeatableCommit(timeOut = 500)
    @CheckSecurity
    public Response<? extends Map> createShipment(@RequestBody OrderDeliveryCreateShipmentDTO orderDeliveryCreateShipmentParam) {
        LOGGER.info(String.format("[WMS]收到WMS创建运单信息通知,参数:[%s]", JSON.toJSONString(orderDeliveryCreateShipmentParam)));
        if (Objects.isNull(orderDeliveryCreateShipmentParam) || CollectionUtils.isEmpty(orderDeliveryCreateShipmentParam.getOrderNumberList())) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }
        if (orderDeliveryCreateShipmentParam.getOrderNumberList().size() > 50) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 数据转换
        WmsOrderDeliveryCreateShipmentBO wmsOrderDeliveryCreateShipmentParam = new WmsOrderDeliveryCreateShipmentBO();
        BeanUtils.copyProperties(orderDeliveryCreateShipmentParam, wmsOrderDeliveryCreateShipmentParam);

        WmsExecuteBO wmsExecuteBo = new WmsExecuteBO();
        wmsExecuteBo.setOrderDeliveryCreateShipmentParam(wmsOrderDeliveryCreateShipmentParam);
        storeDataSyncOmsMessageSendHelper.sendStoreDataSyncWmsMessage(wmsExecuteBo);
        return Response.ok("success", Boolean.TRUE);
    }
}
