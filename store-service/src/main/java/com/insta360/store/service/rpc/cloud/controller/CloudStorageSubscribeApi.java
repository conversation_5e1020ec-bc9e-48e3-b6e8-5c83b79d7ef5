package com.insta360.store.service.rpc.cloud.controller;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.cloud.dto.UseBenefitDTO;
import com.insta360.store.business.cloud.exception.CloudSubscribeErrorCode;
import com.insta360.store.service.rpc.cloud.format.CloudStorageBenefitPack;
import com.insta360.store.service.rpc.cloud.format.CloudSubscribeOtherPack;
import com.insta360.store.service.rpc.cloud.vo.CloudSubscribeSkuVO;
import com.insta360.store.service.rpc.cloud.vo.UseBeneFitVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 云存储商城订阅RPC服务
 * @Date 2024/5/13
 */
@RestController
public class CloudStorageSubscribeApi {

    @Autowired
    CloudStorageBenefitPack cloudStorageBenefitPack;

    @Autowired
    CloudSubscribeOtherPack cloudSubscribeOtherPack;

    /**
     * 权益状态查询
     *
     * @param userId
     * @return
     */
    @GetMapping("/rpc/store/service/cloud/getBenefit")
    public Response<UseBeneFitVO> getBenefit(@RequestParam Integer userId) {
        if (Objects.isNull(userId)) {
            return Response.failed(-1, "参数不能为空");
        }

        UseBeneFitVO useBeneFitVo = cloudStorageBenefitPack.getBenefit(userId);
        return Response.ok(useBeneFitVo);
    }

    /**
     * 权益使用
     *
     * @param useBenefitParam
     * @return
     */
    @PostMapping("/rpc/store/service/cloud/useBenefit")
    public Response<UseBeneFitVO> useBenefit(@Validated @RequestBody UseBenefitDTO useBenefitParam) {
        if (Objects.isNull(useBenefitParam)) {
            return Response.failed(-1, "参数不能为空");
        }

        return Response.ok(cloudStorageBenefitPack.useBenefit(useBenefitParam));
    }

    /**
     * 通过RPC服务获取订阅的SKU列表
     * <p>
     * 此方法用于响应对订阅SKU列表的获取请求它使用了@RequestMapping注解来指定处理的HTTP方法为GET，
     * 并且路径为"/rpc/store/service/cloud/getSubscribeSkus"它不接受任何请求参数
     *
     * @return 包含订阅SKU信息的响应对象，如果操作成功，响应状态为OK，并包含一个CloudSubscribeSkuVO列表；
     * 如果操作失败或发生异常，可以通过响应的状态和消息来判断错误原因
     */
    @GetMapping("/rpc/store/service/cloud/getSubscribeSkus")
    public Response<List<CloudSubscribeSkuVO>> getSubscribeSkus() {
        return Response.ok(cloudSubscribeOtherPack.doPackSkus());
    }

    /**
     * 获取care & 延保上线地区
     *
     * @param countryCode
     * @return
     */
    @GetMapping("/rpc/store/service/cloud/getRegion")
    public Response<UseBeneFitVO> getRegion(@RequestParam String countryCode) {
        if (StringUtil.isBlank(countryCode)) {
            return Response.failed(-1, "请求错误");
        }
        return Response.ok(cloudStorageBenefitPack.getRegion(countryCode));
    }

    /**
     * 查询用户权益信息
     */
    @GetMapping("/rpc/store/service/cloud/getCloudStorageSubscribeInfo")
    public Response<UseBeneFitVO> getCloudStorageSubscribeInfo(@RequestParam Integer accountId) {
        if (Objects.isNull(accountId)) {
            throw new InstaException(CloudSubscribeErrorCode.CorrectParamNotFoundException);
        }

        // 如果存在云存储订阅权益记录，返回包含权益信息的成功响应
        return Response.ok(cloudStorageBenefitPack.doPackStoreBenefitInfo(accountId));
    }
}
