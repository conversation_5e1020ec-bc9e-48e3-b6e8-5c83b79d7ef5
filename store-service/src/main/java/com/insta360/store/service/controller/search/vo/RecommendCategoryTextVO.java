package com.insta360.store.service.controller.search.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/27 上午10:43
 */
public class RecommendCategoryTextVO implements Serializable {

    /**
     * 类型
     */
    private String type;

    /**
     * 标题文案
     */
    private String category;

    /**
     * 子项列表
     */
    private List<RecommendItemTextVO> recommendItems;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<RecommendItemTextVO> getRecommendItems() {
        return recommendItems;
    }

    public void setRecommendItems(List<RecommendItemTextVO> recommendItems) {
        this.recommendItems = recommendItems;
    }

    @Override
    public String toString() {
        return "RecommendCategoryTextVO{" +
                "type='" + type + '\'' +
                ", category='" + category + '\'' +
                ", recommendItems=" + recommendItems +
                '}';
    }
}
