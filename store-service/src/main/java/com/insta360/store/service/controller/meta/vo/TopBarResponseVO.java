package com.insta360.store.service.controller.meta.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/08/17
 * @Description:
 */
public class TopBarResponseVO implements Serializable {

    /**
     * top bar id
     */
    private Integer topBarId;

    /**
     * 内部名称
     */
    private String topBarName;

    /**
     * 词条信息
     */
    private String entryInfo;

    /**
     * 上线时间
     */
    private LocalDateTime startTime;

    /**
     * 下线时间
     */
    private LocalDateTime endTime;

    /**
     * 词条对应的变量映射
     */
    private List<TopBarResponseVO.PlaceholderEntry> placeholders;

    public Integer getTopBarId() {
        return topBarId;
    }

    public void setTopBarId(Integer topBarId) {
        this.topBarId = topBarId;
    }

    public String getTopBarName() {
        return topBarName;
    }

    public void setTopBarName(String topBarName) {
        this.topBarName = topBarName;
    }

    public String getEntryInfo() {
        return entryInfo;
    }

    public void setEntryInfo(String entryInfo) {
        this.entryInfo = entryInfo;
    }

    public List<PlaceholderEntry> getPlaceholders() {
        return placeholders;
    }

    public void setPlaceholders(List<PlaceholderEntry> placeholders) {
        this.placeholders = placeholders;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "TopBarResponseVO{" +
                "topBarId=" + topBarId +
                ", topBarName=" + topBarName +
                ", entryInfo='" + entryInfo + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", placeholders=" + placeholders +
                '}';
    }

    public static class PlaceholderEntry implements Serializable {

        private String key;

        private String value;

        private String placeholderType;

        public PlaceholderEntry() {
        }

        public PlaceholderEntry(String key, String value, String placeholderType) {
            this.key = key;
            this.value = value;
            this.placeholderType = placeholderType;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getPlaceholderType() {
            return placeholderType;
        }

        public void setPlaceholderType(String placeholderType) {
            this.placeholderType = placeholderType;
        }

        @Override
        public String toString() {
            return "PlaceholderEntry{" +
                    "key='" + key + '\'' +
                    ", value='" + value + '\'' +
                    ", placeholderType='" + placeholderType + '\'' +
                    '}';
        }
    }
}
