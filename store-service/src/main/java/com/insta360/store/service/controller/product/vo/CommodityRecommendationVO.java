package com.insta360.store.service.controller.product.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2021/8/4
 * @Description:
 */
public class CommodityRecommendationVO implements Serializable {

    /**
     * 推荐配件集合
     */
    private List<ProductCommodityRecommendationVO> productCommodityRecommendation;

    /**
     * 推荐配件套餐信息集合
     */
    private List<CommodityVO> productCommodityInfo;

    public List<ProductCommodityRecommendationVO> getProductCommodityRecommendation() {
        return productCommodityRecommendation;
    }

    public void setProductCommodityRecommendation(List<ProductCommodityRecommendationVO> productCommodityRecommendation) {
        this.productCommodityRecommendation = productCommodityRecommendation;
    }

    public List<CommodityVO> getProductCommodityInfo() {
        return productCommodityInfo;
    }

    public void setProductCommodityInfo(List<CommodityVO> productCommodityInfo) {
        this.productCommodityInfo = productCommodityInfo;
    }

    @Override
    public String toString() {
        return "CommodityRecommendationVO{" +
                "productCommodityRecommendation=" + productCommodityRecommendation +
                ", productCommodityInfo=" + productCommodityInfo +
                '}';
    }
}
