package com.insta360.store.service.rpc.user.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.user.model.UserDelivery;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2022/09/26
 * @Description:
 */
public class UserDeliveryRpcVO implements Serializable {

    private Integer id;

    /**
     * last name
     */
    private String lastName;

    /**
     * first name
     */
    private String firstName;

    /**
     * 国家
     */
    private String country;

    /**
     * 国家二字码
     */
    private String countryCode;

    /**
     * 省份/洲地址二字码
     */
    private String provinceCode;

    /**
     * 省份/洲地址
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区/县（中国大陆才会存在）
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 附加地址
     */
    private String subAddress;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 手机区号
     */
    private String phoneCode;

    /**
     * 国际区号对应的国家或者地区
     */
    private String phoneCodeArea;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 是否设置为默认地址
     */
    private Boolean isDefault;

    public UserDeliveryRpcVO() {
    }

    public UserDeliveryRpcVO(UserDelivery userDelivery) {
        if (userDelivery != null) {
            BeanUtil.copyProperties(userDelivery, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getPhoneCodeArea() {
        return phoneCodeArea;
    }

    public void setPhoneCodeArea(String phoneCodeArea) {
        this.phoneCodeArea = phoneCodeArea;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    @Override
    public String toString() {
        return "UserDeliveryRpcVO{" +
                "id=" + id +
                ", lastName='" + lastName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", country='" + country + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", phoneCodeArea='" + phoneCodeArea + '\'' +
                ", phone='" + phone + '\'' +
                ", isDefault=" + isDefault +
                '}';
    }
}
