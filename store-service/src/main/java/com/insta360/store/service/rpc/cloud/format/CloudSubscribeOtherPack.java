package com.insta360.store.service.rpc.cloud.format;

import com.insta360.store.business.cloud.enums.BenefitPlatform;
import com.insta360.store.business.cloud.model.CloudStorageSku;
import com.insta360.store.business.cloud.service.CloudStorageSkuService;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.product.enums.ProductCategoryFinalType;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.service.rpc.cloud.vo.CloudSubscribeSkuVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/8
 */
@Component
public class CloudSubscribeOtherPack {

    @Autowired
    ProductService productService;

    @Autowired
    CommodityService commodityService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CloudStorageSkuService cloudStorageSkuService;

    /**
     * 封装云订阅服务的SKU信息
     * <p>
     * 此方法通过组合云服务产品、商品、SKU和价格信息，构建并返回一个包含云订阅服务SKU详细信息的列表
     *
     * @return CloudSubscribeSkuVO列表，包含云订阅服务的SKU详细信息，如果任一步骤无数据则返回null
     */
    public List<CloudSubscribeSkuVO> doPackSkus() {
        // 获取云服务产品
        List<Product> productList = productService.listProductsByCategoryKey(ProductCategoryFinalType.CF_CLOUD_SERVICE.name());
        if (CollectionUtils.isEmpty(productList)) {
            return null;
        }

        // 提取云服务产品的ID列表
        List<Integer> productIds = productList.stream().map(Product::getId).collect(Collectors.toList());

        // 根据产品ID列表获取可用的商品列表
        List<Commodity> commodityList = commodityService.listByProductIdsEnable(productIds);
        if (CollectionUtils.isEmpty(commodityList)) {
            return null;
        }

        // 提取商品的ID列表
        List<Integer> commodityIds = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());

        // 根据商品ID列表获取云存储SKU列表
        List<CloudStorageSku> cloudStorageSkuList = cloudStorageSkuService.getSkuByCommodityIds(commodityIds);
        if (CollectionUtils.isEmpty(cloudStorageSkuList)) {
            return null;
        }

        // 根据商品ID列表获取商品价格列表
        List<CommodityPrice> commodityPriceList = commodityPriceService.getPrices(commodityIds);
        if (CollectionUtils.isEmpty(commodityPriceList)) {
            return null;
        }

        // 构建商品ID到SKU ID的映射
        Map<Integer, String> skuIdMap = cloudStorageSkuList.stream().collect(Collectors.toMap(CloudStorageSku::getCommodityId, CloudStorageSku::getSkuId));

        // 根据商品价格和SKU ID映射，构建并返回云订阅服务SKU详细信息列表
        return commodityPriceList.stream().map(commodityPrice -> this.buildCloudSubscribeSkuVO(commodityPrice, skuIdMap.get(commodityPrice.getCommodityId()))).collect(Collectors.toList());
    }


    /**
     * 构建云订阅SKU信息对象
     * 此方法用于将商品价格信息和SKU标识封装到CloudSubscribeSkuVO对象中
     *
     * @param commodityPrice 商品价格对象，包含商品的价格信息
     * @param skuId          SKU标识，用于唯一确定一个商品的具体规格
     * @return 返回封装好的CloudSubscribeSkuVO对象，包含SKU信息和价格信息
     */
    private CloudSubscribeSkuVO buildCloudSubscribeSkuVO(CommodityPrice commodityPrice, String skuId) {
        CloudSubscribeSkuVO cloudSubscribeSkuVo = new CloudSubscribeSkuVO();
        cloudSubscribeSkuVo.setSkuId(skuId);
        cloudSubscribeSkuVo.setPlatform(BenefitPlatform.STORE.getName());
        cloudSubscribeSkuVo.setRegion(commodityPrice.getArea());
        cloudSubscribeSkuVo.setPrice(new BigDecimal(String.valueOf(commodityPrice.getAmount())));
        cloudSubscribeSkuVo.setCurrency(commodityPrice.getCurrency());
        return cloudSubscribeSkuVo;
    }
}
