package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.meta.cache.cacheput.TopBarCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wbt
 * @Date: 2023/08/31
 * @Description: Topbar缓存更新
 */
@RestController
public class TopbarCachePutApi {

    @Autowired
    TopBarCachePutPack topBarCachePutPack;

    /**
     * 按语言和地区获取对应的TopBar配置列表
     *
     * @param country
     * @param language
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/meta/tb/listTopBarInfos")
    public void listTopBarInfos(@RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        topBarCachePutPack.doPackCacheTopBarInfos(country, language);
    }
}
