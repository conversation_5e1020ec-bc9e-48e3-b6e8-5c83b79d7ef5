package com.insta360.store.service.controller.cloud.vo;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2024/08/05
 * @Description:
 */
public class CloudSubscribeBillingAddressVO implements Serializable {

    /**
     * 名
     */
    private String lastName;

    /**
     * 姓
     */
    private String firstName;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 地区
     */
    private String country;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 钱海编码（用于支付时使用）
     */
    private String oceanCode;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 子地址
     */
    private String subAddress;

    /**
     * 电话地区码
     */
    private String phoneCode;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 抬头
     */
    private String taxTitle;

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getTaxTitle() {
        return taxTitle;
    }

    public void setTaxTitle(String taxTitle) {
        this.taxTitle = taxTitle;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getOceanCode() {
        return oceanCode;
    }

    public void setOceanCode(String oceanCode) {
        this.oceanCode = oceanCode;
    }

    @Override
    public String toString() {
        return "CloudSubscribeBillingAddressVO{" +
                "lastName='" + lastName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", country='" + country + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", oceanCode='" + oceanCode + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", phone='" + phone + '\'' +
                ", taxNumber='" + taxNumber + '\'' +
                ", taxTitle='" + taxTitle + '\'' +
                '}';
    }
}
