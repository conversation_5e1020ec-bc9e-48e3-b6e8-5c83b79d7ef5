package com.insta360.store.service.rpc.cache.tradeup;

import com.insta360.store.service.controller.tradeup.cache.cacheput.TradeupInfoCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 以旧换新缓存更新RPC服务
 * @Date 2023/11/6
 */
@RestController
public class TradeupInfoCachePutApi {

    @Autowired
    TradeupInfoCachePutPack tradeupInfoCachePutPack;

    /**
     * 获取以旧换新产品列表
     */
    @GetMapping("/rpc/store/service/cacheput/tradeup/tu/listTradeupTypesInfos")
    public void listTradeupTypesInfos() {
        tradeupInfoCachePutPack.doPackTradeupTypesInfo();
    }
}
