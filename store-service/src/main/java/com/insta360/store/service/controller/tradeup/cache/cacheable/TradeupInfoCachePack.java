package com.insta360.store.service.controller.tradeup.cache.cacheable;

import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.tradeup.format.TradeupInfoPack;
import com.insta360.store.service.controller.tradeup.vo.TradeupTypeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/7/7
 * @Description:
 */
@Component
public class TradeupInfoCachePack {

    @Autowired
    TradeupInfoPack tradeupInfoPack;

    /**
     * typesInfo缓存
     *
     * @return
     */
    @CacheTtlMonitor(value = CacheableType.TRADE_UP_TYPE, cacheKey = "'-TradeupTypesInfoCachePack-' + methodName")
    @Cacheable(value = CacheableType.TRADE_UP_TYPE, key = "caches[0].name + '-TradeupTypesInfoCachePack-' + methodName")
    public List<TradeupTypeVO> doPackTradeupTypesInfo() {
        return tradeupInfoPack.doPackTradeupTypesInfo();
    }
}
