package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.GraphicNavigationInfo;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/3/14
 * @Description:
 */
public class GraphicNavigationInfoVO implements Serializable {

    /**
     * 图文导航主id
     */
    private Integer mainId;

    /**
     * 显示名称
     */
    private String displayName;

    public GraphicNavigationInfoVO() {
    }

    public GraphicNavigationInfoVO(GraphicNavigationInfo graphicNavigationInfo) {
        if (Objects.nonNull(graphicNavigationInfo)) {
            BeanUtil.copyProperties(graphicNavigationInfo, this);
        }
    }

    public Integer getMainId() {
        return mainId;
    }

    public void setMainId(Integer mainId) {
        this.mainId = mainId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
}
