package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.meta.cache.cacheput.TradePointCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wbt
 * @Date: 2023/10/11
 * @Description: TradePoint缓存更新
 */
@RestController
public class TradePointPutApi {

    @Autowired
    TradePointCachePutPack tradePointCachePutPack;

    /**
     * 按语言和地区获取对应的TradePoint配置列表
     *
     * @param country
     * @param language
     * @return
     */
    @GetMapping("/rpc/store/service/cacheput/meta/tp/listTradePointInfos")
    public void listTradePointInfos(@RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        tradePointCachePutPack.doPackTradePointInfos(country, language);
    }
}
