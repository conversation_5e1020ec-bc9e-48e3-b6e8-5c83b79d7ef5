package com.insta360.store.service.controller.meta.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: wkx
 * @Date: 2023/11/8
 * @Description:
 */
public class CategoryPageVO implements Serializable {

    /**
     * 图文筛选器映射
     * key：图文筛选器id
     * value：套餐id列表
     */
    private Map<String, List<Integer>> selectorMap;

    /**
     * 套餐信息映射
     * key：commodityId
     * value：套餐信息
     */
    private Map<String, CategoryPageCommodityVO> commodityInfoMap;

    /**
     * 置顶排序列表
     */
    private List<Integer> topSortCommodityIds;

    /**
     * 场景专区列表
     */
    private List<ScenerySectionVO> scenerySectionList;

    public Map<String, List<Integer>> getSelectorMap() {
        return selectorMap;
    }

    public void setSelectorMap(Map<String, List<Integer>> selectorMap) {
        this.selectorMap = selectorMap;
    }

    public Map<String, CategoryPageCommodityVO> getCommodityInfoMap() {
        return commodityInfoMap;
    }

    public void setCommodityInfoMap(Map<String, CategoryPageCommodityVO> commodityInfoMap) {
        this.commodityInfoMap = commodityInfoMap;
    }

    public List<Integer> getTopSortCommodityIds() {
        return topSortCommodityIds;
    }

    public void setTopSortCommodityIds(List<Integer> topSortCommodityIds) {
        this.topSortCommodityIds = topSortCommodityIds;
    }

    public List<ScenerySectionVO> getScenerySectionList() {
        return scenerySectionList;
    }

    public void setScenerySectionList(List<ScenerySectionVO> scenerySectionList) {
        this.scenerySectionList = scenerySectionList;
    }

    @Override
    public String toString() {
        return "CategoryPageVO{" +
                "selectorMap=" + selectorMap +
                ", commodityInfoMap=" + commodityInfoMap +
                ", topSortCommodityIds=" + topSortCommodityIds +
                ", scenerySectionList=" + scenerySectionList +
                '}';
    }
}
