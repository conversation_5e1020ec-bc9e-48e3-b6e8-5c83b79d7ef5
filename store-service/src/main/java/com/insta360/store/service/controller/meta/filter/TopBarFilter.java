package com.insta360.store.service.controller.meta.filter;

import com.insta360.store.service.controller.meta.vo.TopBarResponseVO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2023/10/18
 * @Description:
 */
@Component
public class TopBarFilter {

    /**
     * topbar 过滤已失效的数据
     *
     * @param topBarResponseVos
     * @return
     * @Description: 避免当前时间还没达到开始时间或者已经超过了结束时间的数据返回
     */
    public List<TopBarResponseVO> listTopBarInfos(List<TopBarResponseVO> topBarResponseVos) {
        LocalDateTime now = LocalDateTime.now();
        return topBarResponseVos
                .stream()
                .filter(topBarResponseVo -> !now.isBefore(topBarResponseVo.getStartTime()))
                .filter(topBarResponseVo -> !now.isAfter(topBarResponseVo.getEndTime()))
                .collect(Collectors.toList());
    }
}
