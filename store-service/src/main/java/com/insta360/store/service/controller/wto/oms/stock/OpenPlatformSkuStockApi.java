package com.insta360.store.service.controller.wto.oms.stock;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.annotations.AvoidRepeatableCommit;
import com.insta360.compass.core.common.BaseController;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.libs.security.annotations.CheckSecurity;
import com.insta360.store.business.integration.wto.enums.OmsIntegrationBusinessType;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsSkuStockSyncNotifyBO;
import com.insta360.store.business.integration.wto.oms.dto.SkuStockSyncNotifyDTO;
import com.insta360.store.business.outgoing.mq.wto.helper.StoreDataSyncOmsMessageSendHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description oms sku库存同步回调服务
 * @Date 2025/4/16
 */
@RestController
public class OpenPlatformSkuStockApi extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpenPlatformSkuStockApi.class);

    @Autowired
    StoreDataSyncOmsMessageSendHelper storeDataSyncOmsMessageSendHelper;

    /**
     * sku库存同步回调
     *
     * @param skuStockSyncNotifyDto
     * @return
     */
    @CheckSecurity
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping("/store/openapi/stock/skuStockSyncCallback")
    public Response<Object> skuStockSyncCallback(@RequestBody SkuStockSyncNotifyDTO skuStockSyncNotifyDto) {
        LOGGER.info("[OMS回调]已接收到oms sku库存同步回调. request:{}", JSON.toJSONString(skuStockSyncNotifyDto));

        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        omsExecuteBo.setOmsSkuStockSyncNotifyBo(new OmsSkuStockSyncNotifyBO(skuStockSyncNotifyDto));
        storeDataSyncOmsMessageSendHelper.sendStoreDataSyncOmsMessage(OmsIntegrationBusinessType.OMS_SKU_STOCK_SYNC_CALLBACK, omsExecuteBo);
        return Response.ok();
    }
}
