package com.insta360.store.service.controller.meta.cache.cacheable;

import com.alibaba.fastjson.JSONArray;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.LocationPack;
import com.insta360.store.service.controller.meta.vo.MetaCountryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Author: wbt
 * @Date: 2022/02/23
 * @Description:
 */
@Component
public class LocationCachePack {

    @Autowired
    LocationPack locationPack;

    /**
     * 缓存所有的国家信息
     *
     * @return
     */
    @CacheTtlMonitor(value = CacheableType.META_COUNTRY, cacheKey = "'-LocationCachePack-' + methodName")
    @Cacheable(value = CacheableType.META_COUNTRY, key = "caches[0].name + '-LocationCachePack-' + methodName")
    public Map<String, List<MetaCountryVO>> doPackCacheCountries() {
        return locationPack.doPackCountries();
    }

    /**
     * 缓存国家的省市信息（会走RPC，不走缓存监控逻辑，避免出现脏数据）
     *
     * @param country
     * @return
     */
    @Cacheable(value = CacheableType.COUNTRY_LOCATION, key = "caches[0].name + '-LocationCachePack-' + methodName + '-country-' + #country")
    public JSONArray doPackCacheLocations(InstaCountry country) {
        return locationPack.doPackLocations(country);
    }
}
