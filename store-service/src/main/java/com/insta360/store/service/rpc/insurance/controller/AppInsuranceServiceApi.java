package com.insta360.store.service.rpc.insurance.controller;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.commodity.service.CommodityService;
import com.insta360.store.business.configuration.gateway.GatewayConfiguration;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.insurance.bo.InsuranceBO;
import com.insta360.store.business.insurance.constant.InsuranceCommonConstant;
import com.insta360.store.business.insurance.dto.AppQueryInsuranceDTO;
import com.insta360.store.business.insurance.dto.ExtendInsuranceDTO;
import com.insta360.store.business.insurance.enums.AppPurchaseInsuranceType;
import com.insta360.store.business.insurance.enums.InsuranceOriginType;
import com.insta360.store.business.insurance.model.InsuranceServiceCommodityBind;
import com.insta360.store.business.insurance.service.CareInsuranceService;
import com.insta360.store.business.insurance.service.ServiceCommodityBindService;
import com.insta360.store.business.insurance.service.impl.fatory.InsuranceFactory;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceCheckHelper;
import com.insta360.store.business.insurance.service.impl.helper.InsuranceServiceActivationHelper;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.service.rpc.insurance.format.ExtendInsuranceResultPack;
import com.insta360.store.service.rpc.insurance.format.InsurancePack;
import com.insta360.store.service.rpc.insurance.vo.AppCareInsuranceVO;
import com.insta360.store.service.rpc.insurance.vo.ExtendInsuranceVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: wbt
 * @Date: 2021/12/03
 * @Description:
 */
@RestController
public class AppInsuranceServiceApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppInsuranceServiceApi.class);

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    CareInsuranceService careInsuranceService;

    @Autowired
    InsuranceServiceActivationHelper activationHelper;

    @Autowired
    ExtendInsuranceResultPack extendInsuranceResultPack;

    @Autowired
    InsurancePack insurancePack;

    @Autowired
    InsuranceFactory insuranceFactory;

    @Autowired
    InsuranceCheckHelper insuranceCheckHelper;

    @Autowired
    ServiceCommodityBindService serviceCommodityBindService;

    @Autowired
    GatewayConfiguration gatewayConfiguration;

    @Autowired
    CommodityService commodityService;

    @Autowired
    ProductService productService;

    /**
     * 校验序列号是否购买过保险服务
     *
     * @param serial
     * @return
     */
    @GetMapping("/rpc/store/service/insurance/app/checkSerialInsurance")
    public Response<? extends Map> checkSerialInsurance(@RequestParam String serial) {
        return careInsuranceService.getCareInsurance(serial) == null
                ? Response.ok("result", false)
                : Response.ok("result", true);
    }

    /**
     * 校验地区是否属于care上线地区
     *
     * @param countryCode
     * @return
     */
    @GetMapping("/rpc/store/service/insurance/app/checkCareIncludeCountry")
    public Response<? extends Map> checkCareIncludeCountry(@RequestParam String countryCode) {
        String careIncludeCountry = storeConfigService.getConfigValue(StoreConfigKey.care_country_config);
        if (careIncludeCountry == null) {
            return Response.ok("careEnable", false);
        }
        return Response.ok("careEnable", Arrays.asList(careIncludeCountry.split(",")).contains(countryCode));
    }

    /**
     * 根据序列号获取延保服务（app）
     *
     * @param extendInsuranceParam
     */
    @PostMapping("/rpc/store/service/insurance/extend/listExtendInsuranceByDeviceSerial")
    public Response<List<ExtendInsuranceVO>> listExtendInsuranceByDeviceSerial(@RequestBody ExtendInsuranceDTO extendInsuranceParam) {
        List<String> serials = extendInsuranceParam.getSerials();
        if (!CollectionUtils.isEmpty(serials)) {
            List<ExtendInsuranceVO> extendInsuranceList = extendInsuranceResultPack.packExtendInsuranceBySerials(serials, extendInsuranceParam.getArea());
            if (!CollectionUtils.isEmpty(extendInsuranceList)) {
                return Response.ok(extendInsuranceList);
            }
        }
        return Response.failed();
    }

    /**
     * 校验序列号是否购买过保险服务
     *
     * @param serial
     * @param condition
     * @param area
     * @return
     */
    @GetMapping("/rpc/store/service/insurance/getInsurancePurchaseInfo")
    public Response<Object> getInsurancePurchaseInfo(@RequestParam String serial,
                                                     @RequestParam Integer condition,
                                                     @RequestParam String area) {
        AppPurchaseInsuranceType purchaseInsuranceType = AppPurchaseInsuranceType.parse(condition);
        if (Objects.isNull(purchaseInsuranceType)) {
            return Response.failed();
        }

        Map<String, Boolean> insuranceMap = insurancePack.getInsurancePurchaseInfo(serial, purchaseInsuranceType, area);
        return Response.ok(insuranceMap);
    }

    /**
     * 赠送延保服务（app）
     *
     * @param extendInsuranceParam
     */
    @PostMapping("/rpc/store/service/insurance/extend/saveExtendInsurance")
    public Response<? extends Map> saveExtendInsurance(@RequestBody ExtendInsuranceDTO extendInsuranceParam) {
        String serial = extendInsuranceParam.getSerial();
        if ((StringUtil.isBlank(serial))) {
            return Response.failed();
        }

        LOGGER.info("[app赠送延保]rpc serial:" + serial);
        Integer extendDays = Objects.isNull(extendInsuranceParam.getExtendDays()) ? InsuranceCommonConstant.EXTEND_NORMAL_TIME : extendInsuranceParam.getExtendDays();
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(ServiceType.extend.name());
        insuranceHandler.presentExtendGift(serial, extendInsuranceParam.getArea(), extendInsuranceParam.getEmail(), extendInsuranceParam.getDeviceType(), extendDays);
        return Response.ok();
    }

    /**
     * 校验是否有购买资格
     *
     * @param appQueryInsuranceParam
     * @return
     */
    @PostMapping("/rpc/store/service/insurance/app/checkQualification")
    public Response<Object> checkQualification(@RequestBody AppQueryInsuranceDTO appQueryInsuranceParam) {
        String deviceSerial = appQueryInsuranceParam.getDeviceSerial();
        String deviceType = appQueryInsuranceParam.getDeviceType();
        String country = appQueryInsuranceParam.getCountry();

        if (StringUtil.isBlank(deviceSerial) || StringUtil.isBlank(deviceType) || StringUtil.isBlank(country)) {
            return Response.failed();
        }

        // 相机对应的增值服务套餐
        InsuranceServiceCommodityBind insuranceServiceCommodityBind = serviceCommodityBindService.getCareByDeviceType(deviceType);
        if (Objects.isNull(insuranceServiceCommodityBind)) {
            return Response.failed();
        }

        Integer serviceId = insuranceServiceCommodityBind.getServiceId();
        ServiceType serviceType = ServiceType.parse(serviceId);
        if (Objects.isNull(serviceType)) {
            return Response.failed();
        }

        // 是否为link系列相机
        Integer activationDay = insuranceServiceCommodityBind.getActivationDay();
        if (InsuranceCommonConstant.CLOUD_ACTIVATION_TIME_LIMIT < activationDay) {
            return Response.failed();
        }

        // 校验
        InstaCountry instaCountry = InstaCountry.parse(country);
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(serviceType.name());
        insuranceHandler.checkOnLine(new InsuranceBO(deviceSerial, instaCountry, InsuranceOriginType.app.name()));
        insuranceHandler.checkSerial(deviceSerial);

        // 是否支持购买care
        insuranceCheckHelper.isCamera(deviceSerial, insuranceServiceCommodityBind);

        // 参数拼接
        Integer commodityId = insuranceServiceCommodityBind.getCommodityId();
        Commodity commodity = commodityService.getById(commodityId);
        Product product = productService.getById(commodity.getProduct());

        String baseUrl = String.format(InsuranceCommonConstant.BASE_URL_LINK,
                gatewayConfiguration.getStoreUrl(), country, product.getUrlKey(),
                insuranceServiceCommodityBind.getCommodityId(), RSAUtil.encryptByPub(deviceSerial));

        AppCareInsuranceVO appCareInsuranceVo = new AppCareInsuranceVO();
        appCareInsuranceVo.setCareType(serviceType.name());
        appCareInsuranceVo.setBaseUrl(baseUrl);
        return Response.ok(appCareInsuranceVo);
    }
}