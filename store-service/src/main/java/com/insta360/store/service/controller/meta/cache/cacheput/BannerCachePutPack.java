package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.BannerPack;
import com.insta360.store.service.controller.meta.vo.BannerMainVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: py
 * @create: 2023-12-08 14:42
 */
@Component
public class BannerCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(BannerCachePutPack.class);

    @Autowired
    BannerPack bannerPack;

    /**
     * 获取banner
     *
     * @param country
     * @param language
     * @param app
     * @return
     */
    @CachePut(value = CacheableType.BANNER_INFO, key = "caches[0].name  + '-BannerCachePack-' + methodName +  '-country-' + #country + '-language-' + #language+ '-app-' + #app")
    public List<BannerMainVO> getBanner(InstaCountry country, InstaLanguage language, String app) {
        List<BannerMainVO> banner = bannerPack.getBanner(country, language, app);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[app:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.BANNER_INFO, "getBanner", app, country, language, banner));
        return banner;
    }
}
