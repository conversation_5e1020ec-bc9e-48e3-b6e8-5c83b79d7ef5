package com.insta360.store.service.rpc.insurance.vo;


import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: wbt
 * @Date: 2021/06/04
 * @Description: RPC接口返回值使用驼峰
 */
public class InsuranceServiceVO implements Serializable {

    private Boolean isCare;

    private String careType;

    private String insuranceNumber;

    private String deviceSerial;

    private LocalDateTime createTime;

    private LocalDateTime bindTime;

    private LocalDateTime useTime;

    private LocalDateTime expireTime;

    private String deviceType;

    private String orderNumber;

    /**
     * 剩余使用次数
     */
    private Integer remainingUsageCount;

    /**
     * 保险类型
     */
    private String insuranceType;

    public Boolean getIsCare() {
        return isCare;
    }

    public void setIsCare(Boolean care) {
        isCare = care;
    }

    public String getCareType() {
        return careType;
    }

    public void setCareType(String careType) {
        this.careType = careType;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getBindTime() {
        return bindTime;
    }

    public void setBindTime(LocalDateTime bindTime) {
        this.bindTime = bindTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }

    public Integer getRemainingUsageCount() {
        return remainingUsageCount;
    }

    public void setRemainingUsageCount(Integer remainingUsageCount) {
        this.remainingUsageCount = remainingUsageCount;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    @Override
    public String toString() {
        return "InsuranceServiceVO{" +
                "isCare=" + isCare +
                ", careType='" + careType + '\'' +
                ", insuranceNumber='" + insuranceNumber + '\'' +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", createTime=" + createTime +
                ", bindTime=" + bindTime +
                ", useTime=" + useTime +
                ", expireTime=" + expireTime +
                ", deviceType='" + deviceType + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", remainingUsageCount=" + remainingUsageCount +
                ", insuranceType='" + insuranceType + '\'' +
                '}';
    }
}
