package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.NavigationBarCategorySubsetInside;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/6/1
 * @Description:
 */
public class NavigationBarCategorySubsetInfoVO implements Serializable {

    private Integer id;

    /**
     * 二级名称
     */
    private String categorySubsetName;

    /**
     * 二级内部名称
     */
    private String subsetInsideName;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 绑定套餐
     */
    private List<NavigationBarCategoryCommodityVO> navigationBarCommodityInfos;

    public NavigationBarCategorySubsetInfoVO() {
    }

    public NavigationBarCategorySubsetInfoVO(NavigationBarCategorySubsetInside subsetInside) {
        if (subsetInside != null) {
            BeanUtil.copyProperties(subsetInside, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCategorySubsetName() {
        return categorySubsetName;
    }

    public void setCategorySubsetName(String categorySubsetName) {
        this.categorySubsetName = categorySubsetName;
    }

    public String getSubsetInsideName() {
        return subsetInsideName;
    }

    public void setSubsetInsideName(String subsetInsideName) {
        this.subsetInsideName = subsetInsideName;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public List<NavigationBarCategoryCommodityVO> getNavigationBarCommodityInfos() {
        return navigationBarCommodityInfos;
    }

    public void setNavigationBarCommodityInfos(List<NavigationBarCategoryCommodityVO> navigationBarCommodityInfos) {
        this.navigationBarCommodityInfos = navigationBarCommodityInfos;
    }

    @Override
    public String toString() {
        return "NavigationBarCategorySubsetInfoVO{" +
                "id=" + id +
                ", categorySubsetName='" + categorySubsetName + '\'' +
                ", subsetInsideName='" + subsetInsideName + '\'' +
                ", orderIndex=" + orderIndex +
                ", navigationBarCommodityInfos=" + navigationBarCommodityInfos +
                '}';
    }
}
