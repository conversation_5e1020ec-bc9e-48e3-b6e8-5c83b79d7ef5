package com.insta360.store.service.controller.rma.vo;

import com.insta360.store.business.rma.model.RmaOrder;

import java.io.Serializable;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 售后申请响应VO
 * @Date 2024/2/27
 */
public class RmaOrderApplyRespVO implements Serializable {

    /**
     * 售后单号
     */
    private String rmaNumber;

    public RmaOrderApplyRespVO() {
    }

    public RmaOrderApplyRespVO(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    /**
     * VO转换
     *
     * @param rmaOrder
     * @return
     */
    public static RmaOrderApplyRespVO convert(RmaOrder rmaOrder) {
        return Optional.ofNullable(rmaOrder).map(r -> new RmaOrderApplyRespVO(r.getRmaNumber())).orElse(new RmaOrderApplyRespVO());
    }

    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    @Override
    public String toString() {
        return "RmaOrderApplyRespVO{" +
                "rmaNumber='" + rmaNumber + '\'' +
                '}';
    }
}
