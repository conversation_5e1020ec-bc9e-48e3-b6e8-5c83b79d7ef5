package com.insta360.store.service.rpc.cache.review;

import com.insta360.store.service.controller.review.cache.cacheput.ReviewInfoCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wbt
 * @Date: 2023/11/16
 * @Description:
 */
@RestController
public class ReviewInfoCachePutApi {

    @Autowired
    ReviewInfoCachePutPack reviewInfoCachePutPack;

    /**
     * 获取某个产品的所有评论星级的评论数量
     *
     * @param productId
     */
    @GetMapping("/rpc/store/service/cacheput/review/getProductReviewRateLevelInfo")
    public void getProductReviewRateLevelInfo(Integer productId) {
        reviewInfoCachePutPack.doPackCacheReviewRateLevelInfo(productId);
    }

    /**
     * 获取产品的总评论数和平均分缓存信息
     *
     * @param productId
     */
    @GetMapping("/rpc/store/service/cacheput/review/getProductReviewRateInfo")
    public void getProductReviewRateInfo(Integer productId) {
        reviewInfoCachePutPack.doPackCacheReviewRateInfo(productId);
    }
}
