package com.insta360.store.service.controller.cloud.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 云服务订阅订单地址VO
 * @Date 2024/11/6
 */
public class CloudSubscribeOrderAddressVO implements Serializable {

    /**
     * last name
     */
    private String lastName;

    /**
     * first name
     */
    private String firstName;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家地区
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份/洲 对应代码
     */
    private String provinceCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 附加地址
     */
    private String subAddress;

    /**
     * 手机区号
     */
    private String phoneCode;

    /**
     * 手机号
     */
    private String phone;

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSubAddress() {
        return subAddress;
    }

    public void setSubAddress(String subAddress) {
        this.subAddress = subAddress;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String toString() {
        return "CloudSubscribeOrderAddressVO{" +
                "lastName='" + lastName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", provinceCode='" + provinceCode + '\'' +
                ", city='" + city + '\'' +
                ", district='" + district + '\'' +
                ", address='" + address + '\'' +
                ", subAddress='" + subAddress + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", phone='" + phone + '\'' +
                '}';
    }
}
