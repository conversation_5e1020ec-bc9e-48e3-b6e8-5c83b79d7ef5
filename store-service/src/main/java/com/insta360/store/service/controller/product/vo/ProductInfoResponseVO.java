package com.insta360.store.service.controller.product.vo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2021/8/4
 * @Description:
 */
public class ProductInfoResponseVO implements Serializable {

    private List<GiftVO> gifts;

    @JSONField(name = "commodity_specific_gifts")
    private JSONArray commoditySpecificGifts;

    public List<GiftVO> getGifts() {
        return gifts;
    }

    public void setGifts(List<GiftVO> gifts) {
        this.gifts = gifts;
    }

    public JSONArray getCommoditySpecificGifts() {
        return commoditySpecificGifts;
    }

    public void setCommoditySpecificGifts(JSONArray commoditySpecificGifts) {
        this.commoditySpecificGifts = commoditySpecificGifts;
    }
}
