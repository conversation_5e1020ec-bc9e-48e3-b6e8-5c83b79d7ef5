package com.insta360.store.service.common.authorization;

import com.insta360.compass.core.web.security.AuthorizationChecker;
import com.insta360.compass.core.web.security.AuthorizationCheckerRegistry;
import com.insta360.compass.core.web.security.AuthorizationHandler;

/**
 * @Author: hyc
 * @Date: 2019/2/28
 * @Description: 每个@Authorization只能check一种Checker，省略Checker时默认用default
 */
public class WebAuthorizationHandler extends AuthorizationHandler {

    @Override
    protected Class<? extends AuthorizationChecker> defineDefaultCheckerClass() {
        return AccessUserChecker.class;
    }

    @Override
    protected void registerChecker(AuthorizationCheckerRegistry registry) {
        registry.addRegistry(new AccessUserChecker());
        registry.addRegistry(new ResellerChecker());
    }
}
