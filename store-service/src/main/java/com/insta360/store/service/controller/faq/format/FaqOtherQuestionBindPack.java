package com.insta360.store.service.controller.faq.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInfo;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInside;
import com.insta360.store.business.faq.model.FaqOtherQuestionBind;
import com.insta360.store.business.faq.model.FaqOtherQuestionQaBind;
import com.insta360.store.business.faq.service.FaqCategoryQuestionInfoService;
import com.insta360.store.business.faq.service.FaqCategoryQuestionInsideService;
import com.insta360.store.business.faq.service.FaqOtherQuestionBindService;
import com.insta360.store.business.faq.service.FaqOtherQuestionQaBindService;
import com.insta360.store.service.controller.faq.vo.FaqOtherQuestionBindVO;
import com.insta360.store.service.controller.faq.vo.FaqQuestionQaBindVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2022/4/29
 * @Description:
 */
@Component
public class FaqOtherQuestionBindPack {

    @Autowired
    FaqOtherQuestionBindService questionBindService;

    @Autowired
    FaqOtherQuestionQaBindService questionQaBindService;

    @Autowired
    FaqCategoryQuestionInsideService questionInsideService;

    @Autowired
    FaqCategoryQuestionInfoService questionInfoService;

    /**
     * 封装页面qa绑定信息
     *
     * @param type
     * @param pageKey
     * @param language
     * @param country
     * @return
     */
    public FaqOtherQuestionBindVO doPackOtherQuestionBind(String type, String pageKey, InstaLanguage language, InstaCountry country) {
        FaqOtherQuestionBindVO otherQuestionBindVO = new FaqOtherQuestionBindVO();
        FaqOtherQuestionBind questionBind = questionBindService.getByQuestionBind(type, pageKey);
        if (questionBind == null) {
            return otherQuestionBindVO;
        }

        List<FaqOtherQuestionQaBind> questionQaBinds = questionQaBindService.listByQuestionBindIdEnable(questionBind.getId());
        if (CollectionUtils.isEmpty(questionQaBinds)) {
            return otherQuestionBindVO;
        }
        otherQuestionBindVO.setQuestionQaBinds(packQuestionQaBind(questionQaBinds, language, country));
        return otherQuestionBindVO;
    }

    /**
     * 封装qa绑定信息
     *
     * @param questionQaBinds
     * @param language
     * @param country
     * @return
     */
    private List<FaqQuestionQaBindVO> packQuestionQaBind(List<FaqOtherQuestionQaBind> questionQaBinds, InstaLanguage language, InstaCountry country) {
        return questionQaBinds.stream().map(questionQaBind -> {
            FaqCategoryQuestionInside questionInside = questionInsideService.getByIdEnable(questionQaBind.getQuestionId());
            if (questionInside == null) {
                return null;
            }
            FaqCategoryQuestionInfo questionInfo = questionInfoService.getByQuestionInsideId(questionInside.getId(), language, country);
            // 防空字符串校验
            if (questionInfo == null || StringUtil.isBlank(questionInfo.getQuestion()) || StringUtil.isBlank(questionInfo.getAnswer())) {
                return null;
            }
            FaqQuestionQaBindVO questionQaBindVO = new FaqQuestionQaBindVO(questionInfo);
            questionQaBindVO.setOrderIndex(questionQaBind.getOrderIndex());
            return questionQaBindVO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
