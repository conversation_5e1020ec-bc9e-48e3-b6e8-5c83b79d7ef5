package com.insta360.store.service.controller.meta.cache.cacheable;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.BannerPack;
import com.insta360.store.service.controller.meta.vo.BannerMainVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: py
 * @create: 2023-12-08 14:42
 */
@Component
public class BannerCachePack {

    @Autowired
    BannerPack bannerPack;

    /**
     * 获取banner
     *
     * @param country
     * @param language
     * @param app
     * @return
     */
    @Cacheable(value = CacheableType.BANNER_INFO, key = "caches[0].name  + '-BannerCachePack-' + methodName +  '-country-' + #country + '-language-' + #language+ '-app-' + #app")
    public List<BannerMainVO> getBanner(InstaCountry country, InstaLanguage language, String app) {
        return bannerPack.getBanner(country, language, app);
    }
}
