package com.insta360.store.service.controller.wto.oms.order;

import com.alibaba.fastjson.JSON;
import com.insta360.compass.core.annotations.AvoidRepeatableCommit;
import com.insta360.compass.core.common.BaseController;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.libs.security.annotations.CheckSecurity;
import com.insta360.store.business.integration.wto.enums.OmsIntegrationBusinessType;
import com.insta360.store.business.integration.wto.oms.bo.OmsExecuteBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsLogisticsTrajectorySyncNotifyBO;
import com.insta360.store.business.integration.wto.oms.bo.OmsOrderDeliveryDetailBO;
import com.insta360.store.business.integration.wto.oms.dto.LogisticsTrajectoryNotifyDTO;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.order.dto.OrderDeliveryDetailDTO;
import com.insta360.store.business.order.dto.OrderItemDeliveryDTO;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderDeliveryPartly;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderDeliveryPartlyService;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.order.service.OrderService;
import com.insta360.store.business.outgoing.mq.wto.helper.StoreDataSyncOmsMessageSendHelper;
import com.insta360.store.business.utils.CommonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wkx
 * @Date: 2024/12/05
 * @Description:
 */
@RestController
public class OpenPlatformOrderDeliveryApi extends BaseController {

    public static final Logger LOGGER = LoggerFactory.getLogger(OpenPlatformOrderDeliveryApi.class);

    @Autowired
    StoreDataSyncOmsMessageSendHelper storeDataSyncOmsMessageSendHelper;

    @Autowired
    OrderService orderService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    OrderDeliveryPartlyService orderDeliveryPartlyService;

    /**
     * oms 发货物流信息同步商城
     *
     * @param orderDeliveryDetailParam
     * @return
     */
    @CheckSecurity
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping("/store/openapi/order/delivery/syncOrderDeliveryInfo")
    public Response<Object> syncOrderDeliveryInfo(@RequestBody OrderDeliveryDetailDTO orderDeliveryDetailParam) {
        LOGGER.info(String.format("oms 发货物流信息同步商城开始 参数 {%s}", JSON.toJSONString(orderDeliveryDetailParam)));
        // 参数必要性校验
        CommonUtil.validationObject(orderDeliveryDetailParam);
        orderDeliveryDetailParam.getDeliveryDetails().forEach(CommonUtil::validationObject);
        // 订单校验
        Order order = orderService.getByOrderNumber(orderDeliveryDetailParam.getOrderNumber());
        if (Objects.isNull(order)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "无效订单");
        }

        // 子项信息校验
        List<OrderItemDeliveryDTO> deliveryDetails = orderDeliveryDetailParam.getDeliveryDetails();
        List<Integer> itemIds = deliveryDetails.stream().map(OrderItemDeliveryDTO::getItemId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemIds)) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "无效订单子项");
        }
        Collection<OrderItem> orderItems = orderItemService.listByIds(itemIds);
        if (itemIds.size() != orderItems.size()) {
            throw new InstaException(CommonErrorCode.InvalidParameter, "无效订单子项");
        }

        Map<Integer, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, i -> i));
        List<OrderDeliveryPartly> orderDeliveryPartlyList = orderDeliveryPartlyService.listByItemIds(itemIds);
        Map<Integer, List<OrderDeliveryPartly>> orderDeliveryPartlyMap = orderDeliveryPartlyList.stream().collect(Collectors.groupingBy(OrderDeliveryPartly::getOrderItem));
        deliveryDetails.forEach(deliveryDetail -> {
            OrderItem orderItem = orderItemMap.get(deliveryDetail.getItemId());
            if (!orderItem.getOrder().equals(order.getId())) {
                throw new InstaException(CommonErrorCode.InvalidParameter, "无效订单子项");
            }
            // 如果发货数量大于可发货数量，则抛异常
            if (deliveryDetail.getItemTotalNumber() < deliveryDetail.getItemNumber()) {
                LOGGER.info(String.format("订单{%s} 子项 {%s} 发货数量大于可发货数量! 发货详情{%s}", order.getOrderNumber(), deliveryDetail.getItemId(), deliveryDetail));
                FeiShuMessageUtil.storeGeneralMessage(String.format("订单{%s} 子项 {%s} 发货数量大于可发货数量!", order.getOrderNumber(),
                        deliveryDetail.getItemId()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                throw new InstaException(CommonErrorCode.InvalidParameter, "发货数量大于可发货数量");
            }

            List<OrderDeliveryPartly> orderDeliveryPartlys = orderDeliveryPartlyMap.get(orderItem.getId());
            if (CollectionUtils.isEmpty(orderDeliveryPartlys)) {
                return;
            }

            Integer deliveryNum = orderDeliveryPartlys.stream().map(OrderDeliveryPartly::getQty).reduce(Integer::sum).get();
            // 如果发货数量大于可发货数量，则抛异常
            if (deliveryDetail.getItemTotalNumber() < deliveryNum + deliveryDetail.getItemNumber()) {
                LOGGER.info(String.format("订单{%s} 子项 {%s} 总发货数量大于可发货数量! 发货详情{%s}", order.getOrderNumber(), deliveryDetail.getItemId(), deliveryDetail));
                FeiShuMessageUtil.storeGeneralMessage(String.format("订单{%s} 子项 {%s} 总发货数量大于可发货数量!", order.getOrderNumber(),
                        deliveryDetail.getItemId()), FeiShuGroupRobot.InternalWarning, FeiShuAtUser.TW);
                throw new InstaException(CommonErrorCode.InvalidParameter, "发货数量大于可发货数量");
            }
        });

        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        OmsOrderDeliveryDetailBO orderDeliveryDetailBo = new OmsOrderDeliveryDetailBO();
        BeanUtil.copyProperties(orderDeliveryDetailParam, orderDeliveryDetailBo);
        omsExecuteBo.setOrderDeliveryDetailBo(orderDeliveryDetailBo);
        omsExecuteBo.setOrderId(order.getId());
        storeDataSyncOmsMessageSendHelper.sendStoreDataSyncOmsMessage(OmsIntegrationBusinessType.STORE_ORDER_DELIVERY, omsExecuteBo);
        LOGGER.info(String.format("oms 发货物流信息同步商城结束 订单号 {%s}", orderDeliveryDetailParam.getOrderNumber()));
        return Response.ok();
    }

    /**
     * 物流轨迹同步回调
     *
     * @param logisticsTrajectoryNotifyDto
     * @return
     */
    @CheckSecurity
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping("/store/openapi/order/delivery/logisticsTrajectorySyncCallback")
    public Response<Object> logisticsTrajectorySyncCallback(@RequestBody LogisticsTrajectoryNotifyDTO logisticsTrajectoryNotifyDto) {
        LOGGER.info("[OMS回调]物流轨迹同步回调开始, request: {}", JSON.toJSONString(logisticsTrajectoryNotifyDto));
        // 构建物流轨迹同步通知bo
        OmsLogisticsTrajectorySyncNotifyBO omsLogisticsTrajectorySyncNotifyBo = JSON.parseObject(JSON.toJSONString(logisticsTrajectoryNotifyDto), OmsLogisticsTrajectorySyncNotifyBO.class);
        OmsExecuteBO omsExecuteBo = new OmsExecuteBO();
        omsExecuteBo.setOmsLogisticsTrajectorySyncNotifyBo(omsLogisticsTrajectorySyncNotifyBo);
        storeDataSyncOmsMessageSendHelper.sendStoreDataSyncOmsMessage(OmsIntegrationBusinessType.STORE_ORDER_DELIVERY_LOGISTICS_CHANGE_CALLBACK, omsExecuteBo);
        LOGGER.info("[OMS回调]物流轨迹同步回调结束, orderNumber: {}", logisticsTrajectoryNotifyDto.getOrderNumber());
        return Response.ok();
    }
}

