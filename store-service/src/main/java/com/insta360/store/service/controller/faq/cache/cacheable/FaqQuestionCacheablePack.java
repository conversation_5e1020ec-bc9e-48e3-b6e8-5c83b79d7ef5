package com.insta360.store.service.controller.faq.cache.cacheable;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.monitor.redis.ttl.annotation.CacheTtlMonitor;;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.faq.format.FaqOtherQuestionBindPack;
import com.insta360.store.service.controller.faq.format.FaqQuestionBindPack;
import com.insta360.store.service.controller.faq.vo.FaqCategoryVO;
import com.insta360.store.service.controller.faq.vo.FaqOtherQuestionBindVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * @Author: wkx
 * @Date: 2022/4/28
 * @Description:
 */
@Component
public class FaqQuestionCacheablePack {

    @Autowired
    FaqQuestionBindPack questionBindPack;

    @Autowired
    FaqOtherQuestionBindPack otherQuestionBindPack;

    /**
     * faq页面缓存（类目结构）
     *
     * @param type
     * @param pageKey
     * @param language
     * @param country
     */
    @CacheTtlMonitor(value = CacheableType.FAQ_QUESTION, cacheKey = "'-QuestionCachePack-' + methodName + '-type-' + #type + '-pageKey-' + #pageKey + '-language-' + #language + '-country-' + #country")
    @Cacheable(value = CacheableType.FAQ_QUESTION, key = "caches[0].name  + '-QuestionCachePack-' + methodName + '-type-' + #type + '-pageKey-' + #pageKey + '-language-' + #language + '-country-' + #country")
    public FaqCategoryVO doPackCacheQuestionInfo(String type, String pageKey, InstaLanguage language, InstaCountry country) {
        return questionBindPack.doPackQuestionBind(type, pageKey, language, country);
    }

    /**
     * faq页面缓存（非类目结构）
     *
     * @param type
     * @param pageKey
     * @param language
     * @param country
     * @return
     */
    @CacheTtlMonitor(value = CacheableType.FAQ_OTHER_QUESTION, cacheKey = "'-OtherQuestionCachePack-' + methodName + '-type-' + #type + '-pageKey-' + #pageKey + '-language-' + #language + '-country-' + #country")
    @Cacheable(value = CacheableType.FAQ_OTHER_QUESTION, key = "caches[0].name  + '-OtherQuestionCachePack-' + methodName + '-type-' + #type + '-pageKey-' + #pageKey + '-language-' + #language + '-country-' + #country")
    public FaqOtherQuestionBindVO doPackCacheOtherQuestionInfo(String type, String pageKey, InstaLanguage language, InstaCountry country) {
        return otherQuestionBindPack.doPackOtherQuestionBind(type, pageKey, language, country);
    }
}
