package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategorySceneryCardResource;

/**
 * @description:
 * @author: py
 * @create: 2025-03-17 17:45
 */
public class SceneryCardResourceVO {

    /**
     * 展示类型 1图/2图
     */
    private String displayType;

    /**
     * 图片类型 左、右、单
     */
    private String imageType;

    /**
     * 图片链接
     */
    private String imageUrl;

    public SceneryCardResourceVO() {
    }

    public SceneryCardResourceVO(ProductCategorySceneryCardResource productCategorySceneryCardResource) {
        if (productCategorySceneryCardResource != null) {
            BeanUtil.copyProperties(productCategorySceneryCardResource, this);
        }
    }

    public String getDisplayType() {
        return displayType;
    }

    public void setDisplayType(String displayType) {
        this.displayType = displayType;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Override
    public String toString() {
        return "SceneryCardResourceVO{" +
                "displayType='" + displayType + '\'' +
                ", imageType='" + imageType + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                '}';
    }
}
