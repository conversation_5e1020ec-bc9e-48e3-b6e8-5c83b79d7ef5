package com.insta360.store.service.rpc.cache.meta;

import com.insta360.store.service.controller.meta.cache.cacheput.LiveBroadcastCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 直播配置页面缓存更新RPC
 * @Date 2023/11/15
 */
@RestController
public class LiveBroadcastCachePutApi {

    @Autowired
    LiveBroadcastCachePutPack liveBroadcastCachePutPack;

    /**
     * 查询直播配置页面列表
     */
    @GetMapping("/rpc/store/service/cacheput/meta/lb/listLiveBroadcastPage")
    public void listLiveBroadcastPage() {
        liveBroadcastCachePutPack.listLiveBroadcastPage();
    }
}
