package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.store.service.controller.meta.cache.cacheput.MetaShippingCostCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 缓存更新 物流更新
 *
 * <AUTHOR>
 * @Description:
 * @date 2024/04/07
 */
@RestController
public class MetaShippingCostCachePutApi {

    @Autowired
    MetaShippingCostCachePutPack metaShippingCostCachePutPack;

    /**
     * 根据地区更新计税州数据
     *
     * @param country
     */
    @GetMapping("/rpc/store/service/cacheput/meta/shipping/cost/getShippingCost")
    public void getShippingCost(@RequestParam InstaCountry country) {
        metaShippingCostCachePutPack.doPackShippingCostVo(country);
    }
}
