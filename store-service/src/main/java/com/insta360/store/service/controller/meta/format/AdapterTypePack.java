package com.insta360.store.service.controller.meta.format;

import com.insta360.store.business.commodity.model.Commodity;
import com.insta360.store.business.meta.service.AdapterTypeInfoService;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductAdapterType;
import com.insta360.store.business.product.service.ProductAdapterTypeService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.service.controller.meta.vo.CategoryPageCommodityVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2024-12-25 19:30
 */
@Component
public class AdapterTypePack {

    @Autowired
    ProductAdapterTypeService productAdapterTypeService;

    @Autowired
    AdapterTypeInfoService adapterTypeInfoService;

    @Autowired
    ProductService productService;

    /**
     * 获取产品的适配机型ID信息
     *
     * @param adapterTypeIds
     * @return
     */
    public Map<Integer, List<Integer>> listAdapterType(List<Integer> adapterTypeIds) {
        // 产品支持的适配类型
        List<ProductAdapterType> productAdapterTypes = productAdapterTypeService.listByAdapterTypeIds(adapterTypeIds);
        if (CollectionUtils.isEmpty(productAdapterTypes)) {
            return new HashMap<>(1);
        }

        // key 适配机型ID  value 产品IDs
        return productAdapterTypes.stream().collect(Collectors.groupingBy(ProductAdapterType::getAdapterTypeId,
                Collectors.mapping(ProductAdapterType::getProductId, Collectors.toList())));
    }

    /**
     * 新品过滤
     *
     * @param adapterTypeMap
     * @return
     */
    public Map<Integer, List<Integer>> listAdapterTypeFilter(Map<Integer, List<Integer>> adapterTypeMap) {
        if (MapUtils.isEmpty(adapterTypeMap)) {
            return adapterTypeMap;
        }

        // 新品产品列表
        List<Product> newProducts = productService.listNewProducts(true);
        if (CollectionUtils.isEmpty(newProducts)) {
            return adapterTypeMap;
        }
        List<Integer> newProductIds = newProducts.stream().map(Product::getId).collect(Collectors.toList());

        // 过滤
        adapterTypeMap.forEach((adapterId, productIds) -> {
            if (CollectionUtils.isNotEmpty(productIds)) {
                // new product list check
                productIds.removeIf(newProductIds::contains);
            }
        });

        return adapterTypeMap;
    }
}
