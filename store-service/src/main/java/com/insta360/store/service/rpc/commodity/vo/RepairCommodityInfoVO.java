package com.insta360.store.service.rpc.commodity.vo;


/**
 * @Author: wbt
 * @Date: 2022/01/04
 * @Description:
 */
public class RepairCommodityInfoVO {

    /**
     * 套餐id
     */
    private Integer commodityId;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 现价
     */
    private Float amount;

    /**
     * 货币符号
     */
    private String signal;

    /**
     * 货币
     */
    private String currency;

    public Integer getCommodityId() {
        return commodityId;
    }

    public void setCommodityId(Integer commodityId) {
        this.commodityId = commodityId;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public String getSignal() {
        return signal;
    }

    public void setSignal(String signal) {
        this.signal = signal;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "RepairCommodityInfoVO{" +
                "commodityId=" + commodityId +
                ", commodityName='" + commodityName + '\'' +
                ", amount=" + amount +
                ", signal='" + signal + '\'' +
                ", currency='" + currency + '\'' +
                '}';
    }
}
