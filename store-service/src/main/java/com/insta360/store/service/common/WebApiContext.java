package com.insta360.store.service.common;

import com.insta360.compass.core.web.api.ApiContext;
import com.insta360.store.business.reseller.model.Reseller;
import com.insta360.store.business.user.model.StoreAccount;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * @Author: mowi
 * @Date: 2018/9/20
 * @Description: 请求上下文，每个请求线程生成一个，记录用户账号和分销账户信息
 */
@Scope("prototype")
@Component
public class WebApiContext extends ApiContext {

    private StoreAccount accessUser;

    private Reseller reseller;

    private String adminJobNumber;

    public StoreAccount getAccessUser() {
        return accessUser;
    }

    public void setAccessUser(StoreAccount accessUser) {
        this.accessUser = accessUser;
    }

    public Reseller getReseller() {
        return reseller;
    }

    public void setReseller(Reseller reseller) {
        this.reseller = reseller;
    }

    public String getAdminJobNumber() {
        return adminJobNumber;
    }

    public void setAdminJobNumber(String adminJobNumber) {
        this.adminJobNumber = adminJobNumber;
    }
}
