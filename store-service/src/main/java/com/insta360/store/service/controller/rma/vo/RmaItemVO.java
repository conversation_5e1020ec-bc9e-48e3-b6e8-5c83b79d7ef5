package com.insta360.store.service.controller.rma.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 售后商品VO
 * @Date 2024/2/27
 */
public class RmaItemVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 套餐主图
     */
    private String commodityDisplay;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 产品名称
     */
    private String productName;

    public String getCommodityDisplay() {
        return commodityDisplay;
    }

    public void setCommodityDisplay(String commodityDisplay) {
        this.commodityDisplay = commodityDisplay;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Override
    public String toString() {
        return "RmaItemVO{" +
                "commodityDisplay='" + commodityDisplay + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", productName='" + productName + '\'' +
                '}';
    }
}
