package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.HomepageItemMain;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/09/15
 * @Description:
 */
public class HomeItemMainVO implements Serializable {

    private Integer id;

    /**
     * 栏目名称
     */
    private String homeItemName;

    /**
     * 栏目类型
     */
    private String homeItemType;

    /**
     * 对应多语言文案系统的key
     */
    private String nameKey;

    /**
     * 首页item的配置信息
     */
    private List<HomeItemCommodityGroupVO> commodityGroups;

    public HomeItemMainVO() {
    }

    public HomeItemMainVO(HomepageItemMain homepageItemMain) {
        if (homepageItemMain != null) {
            BeanUtil.copyProperties(homepageItemMain, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getHomeItemName() {
        return homeItemName;
    }

    public void setHomeItemName(String homeItemName) {
        this.homeItemName = homeItemName;
    }

    public String getHomeItemType() {
        return homeItemType;
    }

    public void setHomeItemType(String homeItemType) {
        this.homeItemType = homeItemType;
    }

    public String getNameKey() {
        return nameKey;
    }

    public void setNameKey(String nameKey) {
        this.nameKey = nameKey;
    }

    public List<HomeItemCommodityGroupVO> getCommodityGroups() {
        return commodityGroups;
    }

    public void setCommodityGroups(List<HomeItemCommodityGroupVO> commodityGroups) {
        this.commodityGroups = commodityGroups;
    }

    @Override
    public String toString() {
        return "HomeItemMainVO{" +
                "id=" + id +
                ", homeItemName='" + homeItemName + '\'' +
                ", homeItemType='" + homeItemType + '\'' +
                ", nameKey='" + nameKey + '\'' +
                ", commodityGroups=" + commodityGroups +
                '}';
    }
}