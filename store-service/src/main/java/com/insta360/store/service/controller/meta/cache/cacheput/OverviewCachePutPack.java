package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.product.format.OverviewPack;
import com.insta360.store.service.controller.product.vo.ProductCommodityTemplateVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: py
 * @create: 2023-11-09 10:40
 */
@Component
public class OverviewCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(OverviewCachePutPack.class);

    @Autowired
    OverviewPack overviewPack;

    /**
     * 缓存overview pc端数据
     *
     * @param productId
     * @param country
     * @param language
     * @return
     */
    @CachePut(value = CacheableType.OVERVIEW_INFO_PC, key = "caches[0].name + '-ProductCachePack-' + methodName + '-productId-' + #productId +'-language-'+#language +'-country-'+ #country")
    public ProductCommodityTemplateVO getStoreOverviewsPc(Integer productId, String language, String country) {
        ProductCommodityTemplateVO storeOverviews = overviewPack.getStoreOverviews(productId, language, country, false);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[productId:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.OVERVIEW_INFO_PC, "getStoreOverviewsPc", productId, country, language, storeOverviews));
        return storeOverviews;
    }

    /**
     * 缓存overview mo端数据
     *
     * @param productId
     * @param country
     * @param language
     * @return
     */
    @CachePut(value = CacheableType.OVERVIEW_INFO_MO, key = "caches[0].name + '-ProductCachePack-' + methodName + '-productId-' + #productId +'-language-'+#language +'-country-'+ #country")
    public ProductCommodityTemplateVO getStoreOverviewsMo(Integer productId, String language, String country) {
        ProductCommodityTemplateVO storeOverviews = overviewPack.getStoreOverviews(productId, language, country, true);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[productId:%s,country:%s,language:%s],缓存更新响应:[%s]",
                CacheableType.OVERVIEW_INFO_MO, "getStoreOverviewsMo", productId, country, language, storeOverviews));
        return storeOverviews;
    }
}
