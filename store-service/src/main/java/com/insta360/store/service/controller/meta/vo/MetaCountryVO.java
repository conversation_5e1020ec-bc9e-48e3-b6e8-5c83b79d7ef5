package com.insta360.store.service.controller.meta.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * @Author: hyc
 * @Date: 2019/2/19
 * @Description:
 */
public class MetaCountryVO {

    private String name;

    @J<PERSON><PERSON>ield(name = "flag_image")
    private String flagImage;

    private String language;

    @J<PERSON>NField(name = "language_text")
    private String languageText;

    private String currency;

    @JSONField(name = "currency_signal")
    private String currencySignal;

    private String code;

    private String phoneCode;

    private String ip;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFlagImage() {
        return flagImage;
    }

    public void setFlagImage(String flagImage) {
        this.flagImage = flagImage;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLanguageText() {
        return languageText;
    }

    public void setLanguageText(String languageText) {
        this.languageText = languageText;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencySignal() {
        return currencySignal;
    }

    public void setCurrencySignal(String currencySignal) {
        this.currencySignal = currencySignal;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}
