package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.HomepageItemCommodityInfo;

import java.io.Serializable;

/**
 * @Author: wbt
 * @Date: 2021/09/13
 * @Description:
 */
public class HomeItemCommodityInfoVO implements Serializable {

    /**
     * 产品名称标注（为空字符串则取产品名）
     */
    private String displayName;

    /**
     * 视频
     */
    private String video;

    /**
     * 视频图片
     */
    private String videoImage;

    /**
     * 套餐主图
     */
    private String commodityDisplayImage;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 小的场景触碰图
     */
    private String hoverImageS;

    /**
     * 大的场景触碰图
     */
    private String hoverImageM;

    /**
     * 小的场景触碰图（移动端）
     */
    private String mobileHoverImageS;

    /**
     * 大的场景触碰图（移动端）
     */
    private String mobileHoverImageM;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品key
     */
    private String productKey;

    /**
     * 套餐名称
     */
    private String commodityName;

    /**
     * 购买限制
     */
    private Integer orderBuyLimit;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 增值服务类型
     */
    private String serviceType;

    public HomeItemCommodityInfoVO() {
    }

    public HomeItemCommodityInfoVO(HomepageItemCommodityInfo homepageItemInfo) {
        if (homepageItemInfo != null) {
            BeanUtil.copyProperties(homepageItemInfo, this);
        }
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getVideo() {
        return video;
    }

    public void setVideo(String video) {
        this.video = video;
    }

    public String getVideoImage() {
        return videoImage;
    }

    public void setVideoImage(String videoImage) {
        this.videoImage = videoImage;
    }

    public String getCommodityDisplayImage() {
        return commodityDisplayImage;
    }

    public void setCommodityDisplayImage(String commodityDisplayImage) {
        this.commodityDisplayImage = commodityDisplayImage;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getHoverImageS() {
        return hoverImageS;
    }

    public void setHoverImageS(String hoverImageS) {
        this.hoverImageS = hoverImageS;
    }

    public String getHoverImageM() {
        return hoverImageM;
    }

    public void setHoverImageM(String hoverImageM) {
        this.hoverImageM = hoverImageM;
    }

    public String getMobileHoverImageS() {
        return mobileHoverImageS;
    }

    public void setMobileHoverImageS(String mobileHoverImageS) {
        this.mobileHoverImageS = mobileHoverImageS;
    }

    public String getMobileHoverImageM() {
        return mobileHoverImageM;
    }

    public void setMobileHoverImageM(String mobileHoverImageM) {
        this.mobileHoverImageM = mobileHoverImageM;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductKey() {
        return productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public Integer getOrderBuyLimit() {
        return orderBuyLimit;
    }

    public void setOrderBuyLimit(Integer orderBuyLimit) {
        this.orderBuyLimit = orderBuyLimit;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    @Override
    public String toString() {
        return "HomeItemCommodityInfoVO{" +
                "displayName='" + displayName + '\'' +
                ", video='" + video + '\'' +
                ", videoImage='" + videoImage + '\'' +
                ", commodityDisplayImage='" + commodityDisplayImage + '\'' +
                ", description='" + description + '\'' +
                ", hoverImageS='" + hoverImageS + '\'' +
                ", hoverImageM='" + hoverImageM + '\'' +
                ", mobileHoverImageS='" + mobileHoverImageS + '\'' +
                ", mobileHoverImageM='" + mobileHoverImageM + '\'' +
                ", productName='" + productName + '\'' +
                ", productKey='" + productKey + '\'' +
                ", commodityName='" + commodityName + '\'' +
                ", orderBuyLimit=" + orderBuyLimit +
                ", stock=" + stock +
                ", serviceType='" + serviceType + '\'' +
                '}';
    }
}
