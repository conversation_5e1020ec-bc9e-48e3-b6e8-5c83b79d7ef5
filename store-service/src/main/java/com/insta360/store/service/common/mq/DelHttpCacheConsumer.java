package com.insta360.store.service.common.mq;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.insta360.compass.libs.rocketmq.tcp.annotations.MessageTcpBroadcastProcessor;
import com.insta360.compass.libs.rocketmq.tcp.enums.MessageTcpBroadcastChannelEnum;
import com.insta360.store.business.configuration.cache.http.aop.HttpRequestCacheAspect;
import com.insta360.store.business.configuration.cache.http.enums.StoreBusinessType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Set;

/**
 * @Author: wbt
 * @Date: 2021/10/15
 * @Description: HTTP缓存删除消费者（全体广播事件）
 */
@Component
public class DelHttpCacheConsumer implements MessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(DelHttpCacheConsumer.class);

    /**
     * HTTP缓存删除消费者
     *
     * @param message
     * @param consumeContext
     * @return
     */
    @MessageTcpBroadcastProcessor(messageChannel = MessageTcpBroadcastChannelEnum.http_etag_cache_broad_all)
//    @MessageTcpDudep
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        // 缓存key
        String cacheKey = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("消费HTTP缓存删除队列。开始。cacheKey:{}", cacheKey);

        // 缓存业务类型
        StoreBusinessType businessType = StoreBusinessType.parseCacheKey(cacheKey);
        if (businessType == null) {
            return Action.CommitMessage;
        }

        // 删除对应的业务缓存
        Set<String> eTags = HttpRequestCacheAspect.ETAG_CACHE_MAP.get(cacheKey);
        HttpRequestCacheAspect.ETAG_CACHE_MAP.remove(cacheKey);
        LOGGER.info("消费HTTP缓存删除队列。结束。cacheKey:{}；eTags:{}", cacheKey, eTags);
        return Action.CommitMessage;
    }
}
