package com.insta360.store.service.controller.meta.vo;

import com.insta360.store.business.meta.bo.ActivityDomainBO;
import com.insta360.store.business.meta.model.ActivityDynamicParam;
import com.insta360.store.business.meta.model.ActivityLocalesConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/6
 */
public class ActivityConfigVO implements Serializable {

    /**
     * 活动 配置key
     */
    private String activityKey;

    /**
     * 埋点名
     */
    private String campaignName;

    /**
     * 全局配置json字符串
     */
    private String globalSetting;

    /**
     * 活动开始时间
     */
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    private LocalDateTime endTime;

    /**
     * 页面组件配置
     */
    private String components;

    /**
     * 动态参数类型
     */
    private List<ActivityDynamicParamVO> dynamicParams;

    public ActivityConfigVO() {
    }

    public ActivityConfigVO(ActivityDomainBO activityDetail) {
        if (activityDetail == null) {
            return;
        }
        BeanUtils.copyProperties(activityDetail, this);

        List<ActivityLocalesConfig> locales = activityDetail.getLocalesConfigs();
        if (CollectionUtils.isNotEmpty(locales)) {
            this.startTime = locales.get(0).getStartTime();
            this.endTime = locales.get(0).getEndTime();
        }
        List<ActivityDynamicParam> dynamicParamList = activityDetail.getDynamicParams();
        if (CollectionUtils.isNotEmpty(dynamicParamList)) {
            dynamicParams = dynamicParamList.stream().filter(Objects::nonNull).map(ActivityDynamicParamVO::new).collect(Collectors.toList());
        }
    }

    public String getActivityKey() {
        return activityKey;
    }

    public void setActivityKey(String activityKey) {
        this.activityKey = activityKey;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getComponents() {
        return components;
    }

    public void setComponents(String components) {
        this.components = components;
    }

    public List<ActivityDynamicParamVO> getDynamicParams() {
        return dynamicParams;
    }

    public void setDynamicParams(List<ActivityDynamicParamVO> dynamicParams) {
        this.dynamicParams = dynamicParams;
    }

    public String getGlobalSetting() {
        return globalSetting;
    }

    public void setGlobalSetting(String globalSetting) {
        this.globalSetting = globalSetting;
    }

    @Override
    public String toString() {
        return "ActivityConfigVO{" +
                "activityKey='" + activityKey + '\'' +
                ", campaignName='" + campaignName + '\'' +
                ", globalSetting='" + globalSetting + '\'' +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", components=" + components +
                ", dynamicParams=" + dynamicParams +
                '}';
    }
}
