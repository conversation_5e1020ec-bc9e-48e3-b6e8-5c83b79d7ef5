package com.insta360.store.service.rpc.discount.controller;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.discount.dto.GiftCardOpenPlatformCreateDTO;
import com.insta360.store.business.discount.dto.RepairOrderDiscountQueryDTO;
import com.insta360.store.business.discount.dto.ao.BatchCreateDiscountCommonAO;
import com.insta360.store.business.discount.dto.ao.CommonPolicyAO;
import com.insta360.store.business.discount.dto.ao.CreateCommonDiscountAO;
import com.insta360.store.business.discount.dto.ro.BatchCreateCommonDiscountRO;
import com.insta360.store.business.discount.dto.ro.CreateCommonDiscountRo;
import com.insta360.store.business.discount.dto.ro.RepairOrderDiscountRO;
import com.insta360.store.business.discount.enums.DiscountModel;
import com.insta360.store.business.discount.enums.PlatformSourceType;
import com.insta360.store.business.discount.enums.PolicyDiscountType;
import com.insta360.store.business.discount.enums.old.DiscountType;
import com.insta360.store.business.discount.exception.StoreDiscountErrorCode;
import com.insta360.store.business.discount.model.GiftCard;
import com.insta360.store.business.discount.model.GiftCardTemplate;
import com.insta360.store.business.discount.provider.DiscountBusinessProvider;
import com.insta360.store.business.discount.service.DiscountWritService;
import com.insta360.store.business.discount.service.GiftCardService;
import com.insta360.store.business.discount.service.GiftCardTemplateService;
import com.insta360.store.business.user.service.impl.helper.StoreTokenHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: wbt
 * @Date: 2021/05/31
 * @Description:
 */
@RestController
public class TradeCodeApi {

    @Autowired
    GiftCardTemplateService giftCardTemplateService;

    @Autowired
    GiftCardService giftCardService;

    @Autowired
    StoreTokenHelper storeTokenHelper;

    @Autowired
    DiscountWritService discountWritService;

    @Autowired
    DiscountBusinessProvider discountBusinessProvider;


    /**
     * 从模板复制代金券
     * 使用jwt token操作，token 解密后包含
     * {template_code} : 模板代金券码
     * {bind_user} : 生成到某用户的邮箱中
     * {remark} : 自定义备注
     */
    @GetMapping("/rpc/store/service/discount/generateByTemplate")
    public Response<String> generateByTemplate(@RequestParam("token") String token) {
        // 校验token，解析出变量
        String templateCode = (String) storeTokenHelper.verify(token, "template_code");
        String bindEmail = (String) storeTokenHelper.verify(token, "bind_user");
        String remark = (String) storeTokenHelper.verify(token, "remark");

        // 校验代金券模板
        GiftCardTemplate giftCardTemplate = giftCardTemplateService.getByCode(templateCode);
        if (Objects.isNull(giftCardTemplate) || !giftCardTemplate.effectTimeCheck()) {
            return Response.failed();
        }
        //构建代金券生成请求参数
        BatchCreateDiscountCommonAO batchCreateGiftCardAo = new BatchCreateDiscountCommonAO();
        batchCreateGiftCardAo.setTemplateCode(giftCardTemplate.getTemplateCode());
        batchCreateGiftCardAo.setBindEmail(bindEmail);
        batchCreateGiftCardAo.setRemark(remark);
        batchCreateGiftCardAo.setDiscountModel(DiscountModel.GIFT_CODE.code);
        batchCreateGiftCardAo.setPlatformSource(PlatformSourceType.TEMPLATE.code);

        String giftCardCode = null;
        BatchCreateCommonDiscountRO resp = discountWritService.batchCreate(batchCreateGiftCardAo);
        if (Objects.nonNull(resp)) {
            giftCardCode = resp.getCodeList().stream().findFirst().get();
        }
        return Response.ok(giftCardCode);
    }

    /**
     * 根据模版生成代金券
     *
     * @param giftCardOpenPlatformCreateDto
     * @return
     */
    @PostMapping(path = "/rpc/store/service/discount/generateGiftCard")
    public Response<String> generateGiftCard(@Validated @RequestBody GiftCardOpenPlatformCreateDTO giftCardOpenPlatformCreateDto) {
        if (Objects.isNull(giftCardOpenPlatformCreateDto)) {
            throw new InstaException(StoreDiscountErrorCode.ILLEGAL_PARAMETER_EXCEPTION);
        }
        String templateCode = giftCardOpenPlatformCreateDto.getTemplateCode();
        // 校验代金券模板
        GiftCardTemplate giftCardTemplate = giftCardTemplateService.getByCode(templateCode);
        if (Objects.isNull(giftCardTemplate)) {
            throw new InstaException(StoreDiscountErrorCode.NOT_EXIST_GIFT_CARD_TEMPLATE);
        }
        if (!giftCardTemplate.effectTimeCheck()) {
            throw new InstaException(StoreDiscountErrorCode.INVALID_GIFT_CARD_TEMPLATE_EXCEPTION);
        }
        //构建代金券生成请求参数
        BatchCreateDiscountCommonAO batchCreateGiftCardAo = new BatchCreateDiscountCommonAO();
        batchCreateGiftCardAo.setTemplateCode(giftCardTemplate.getTemplateCode());
        batchCreateGiftCardAo.setBindEmail(giftCardOpenPlatformCreateDto.getBindEmail());
        batchCreateGiftCardAo.setRemark(giftCardOpenPlatformCreateDto.getRemark());
        batchCreateGiftCardAo.setDiscountModel(DiscountModel.GIFT_CODE.code);
        batchCreateGiftCardAo.setPlatformSource(PlatformSourceType.TEMPLATE.code);

        String giftCardCode = null;
        BatchCreateCommonDiscountRO resp = discountWritService.batchCreate(batchCreateGiftCardAo);
        if (Objects.nonNull(resp)) {
            giftCardCode = resp.getCodeList().stream().findFirst().get();
        }
        return Response.ok(giftCardCode);
    }

    /**
     * 给代金券绑定邮箱
     *
     * @param code
     * @param bindEmail
     * @return
     */
    @GetMapping("/rpc/store/service/discount/bindGiftEmail")
    public Response<Object> bindGiftEmail(@RequestParam String code, @RequestParam String bindEmail) {
        GiftCard giftCard = giftCardService.getByCode(code);
        if (giftCard == null || StringUtil.isNotBlank(giftCard.getBindEmail())) {
            return Response.failed("无效的代金券");
        }

        // 邮箱绑定
        giftCard.setBindEmail(bindEmail);
        giftCardService.updateById(giftCard);

        return Response.ok();
    }


    /**
     * 获取维修工单折扣结果
     *
     * @param repairOrderDiscountQuery
     * @return
     */
    @PostMapping("/rpc/store/service/discount/getRepairOrderDiscountResult")
    public Response<RepairOrderDiscountRO> getRepairOrderDiscountResult(@Validated @RequestBody RepairOrderDiscountQueryDTO repairOrderDiscountQuery) {
        RepairOrderDiscountRO repairOrderDiscountRo = discountBusinessProvider.getRepairOrderDiscount(repairOrderDiscountQuery);
        return Response.ok(repairOrderDiscountRo);
    }

    /**
     * 折扣券创建
     *
     * @param createCommonDiscountAo
     * @return
     */
    @PostMapping("/rpc/store/service/discount/tradeCodeCreate")
    public Response<CreateCommonDiscountRo> tradeCodeCreate(@Validated @RequestBody CreateCommonDiscountAO createCommonDiscountAo) {
        try {
            // 参数校验
            this.tradeCodeCreateParamCheck(createCommonDiscountAo);

            // 基础参数设置
            createCommonDiscountAo.setPlatformSource(PlatformSourceType.REPAIRMENT.code);
            createCommonDiscountAo.setEnabled(Boolean.TRUE);

            // 设置优惠券政策序号
            AtomicInteger i = new AtomicInteger(1);
            Optional.ofNullable(createCommonDiscountAo.getCommonPolicyAoList())
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(list -> list.forEach(policy -> policy.setSortId(i.getAndIncrement())));

            // 创建折扣券
            CreateCommonDiscountRo createCommonDiscountRo = discountWritService.create(createCommonDiscountAo);
            return Response.ok(createCommonDiscountRo);
        } catch (Exception e) {
            if (e instanceof InstaException) {
                return Response.failed(((InstaException) e).getErrorCode(), e.getMessage());
            }
            return Response.failed(-1, e.getMessage());
        }
    }

    /**
     * 折扣券创建参数校验
     *
     * @param createCommonDiscountAo
     * @return
     */
    private void tradeCodeCreateParamCheck(CreateCommonDiscountAO createCommonDiscountAo) {
        if (DiscountModel.COUPON.code != createCommonDiscountAo.getDiscountModel()) {
            throw new InstaException(StoreDiscountErrorCode.ILLEGAL_DISCOUNT_MODEL_EXCEPTION);
        }

        if (!DiscountType.superimposed_discount.value.equals(createCommonDiscountAo.getDiscountType())) {
            throw new InstaException(StoreDiscountErrorCode.ILLEGAL_DISCOUNT_TYPE_EXCEPTION);
        }

        List<CommonPolicyAO> commonPolicyAoList = createCommonDiscountAo.getCommonPolicyAoList();
        if (CollectionUtils.isEmpty(commonPolicyAoList)) {
            throw new InstaException(StoreDiscountErrorCode.DISCOUNT_POLICY_NOT_NULL_EXCEPTION);
        }

        for (CommonPolicyAO commonPolicyAO : commonPolicyAoList) {
            if (Objects.isNull(commonPolicyAO.getPolicyDiscountType()) || PolicyDiscountType.AMOUNT_RATIO.code != commonPolicyAO.getPolicyDiscountType().intValue()) {
                throw new InstaException(StoreDiscountErrorCode.ILLEGAL_POLICY_DISCOUNT_TYPE_EXCEPTION);
            }
            if (Objects.isNull(commonPolicyAO.getCommonRuleBindItemAo()) || CollectionUtils.isEmpty(commonPolicyAO.getCommonRuleBindItemAo().getBindEffectCommodityIdList())) {
                throw new InstaException(StoreDiscountErrorCode.ILLEGAL_POLICY_BIND_ITEM_NOT_NULL_EXCEPTION);
            }
        }
    }
}
