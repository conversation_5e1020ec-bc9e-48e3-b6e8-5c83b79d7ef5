package com.insta360.store.service.controller.order.vo.info;

import com.insta360.store.business.admin.order.print.bo.PrintRuleCheckResultBO;
import com.insta360.store.service.controller.tradeup.vo.info.TradeupOrderInfoVO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2022/10/18
 * @Description:
 */
public class OrderInfoVO implements Serializable {

    /**
     * 订单id（用来兼容发版期间的延迟）
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 订单号（用来兼容发版期间的延迟）
     */
    private String order_number;

    /**
     * 下单邮箱
     */
    private String contactEmail;

    /**
     * 姓
     */
    private String lastName;

    /**
     * first name
     */
    private String firstName;

    /**
     * 订单状态
     */
    private Integer orderState;

    /**
     * 国家地区
     */
    private String area;

    /**
     * 城市
     */
    private String city;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 省份/洲地址
     */
    private String province;

    /**
     * 区号
     */
    private String phoneCode;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 分销码
     */
    private String resellerCode;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;

    /**
     * 订阅场景类型
     */
    private String subscribeScenesType;

    /**
     * 订单子项信息
     */
    private List<OrderItemInfoVO> orderItemInfos;

    /**
     * 订单支付信息
     */
    private OrderPaymentInfoVO orderPaymentInfo;

    /**
     * 以旧换新订单信息
     */
    private TradeupOrderInfoVO tradeupOrderInfo;

    /**
     * 订单发票打印信息
     */
    private PrintRuleCheckResultBO printRuleCheckResult;

    /**
     * 是否展示失败提示信息
     */
    private String payFailedInfo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderState() {
        return orderState;
    }

    public void setOrderState(Integer orderState) {
        this.orderState = orderState;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(String resellerCode) {
        this.resellerCode = resellerCode;
    }

    public String getPhoneCode() {
        return phoneCode;
    }

    public void setPhoneCode(String phoneCode) {
        this.phoneCode = phoneCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public LocalDateTime getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(LocalDateTime orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getOrder_number() {
        return order_number;
    }

    public void setOrder_number(String order_number) {
        this.order_number = order_number;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public OrderPaymentInfoVO getOrderPaymentInfo() {
        return orderPaymentInfo;
    }

    public void setOrderPaymentInfo(OrderPaymentInfoVO orderPaymentInfo) {
        this.orderPaymentInfo = orderPaymentInfo;
    }

    public List<OrderItemInfoVO> getOrderItemInfos() {
        return orderItemInfos;
    }

    public void setOrderItemInfos(List<OrderItemInfoVO> orderItemInfos) {
        this.orderItemInfos = orderItemInfos;
    }

    public TradeupOrderInfoVO getTradeupOrderInfo() {
        return tradeupOrderInfo;
    }

    public void setTradeupOrderInfo(TradeupOrderInfoVO tradeupOrderInfo) {
        this.tradeupOrderInfo = tradeupOrderInfo;
    }

    public PrintRuleCheckResultBO getPrintRuleCheckResult() {
        return printRuleCheckResult;
    }

    public void setPrintRuleCheckResult(PrintRuleCheckResultBO printRuleCheckResult) {
        this.printRuleCheckResult = printRuleCheckResult;
    }

    public String getSubscribeScenesType() {
        return subscribeScenesType;
    }

    public void setSubscribeScenesType(String subscribeScenesType) {
        this.subscribeScenesType = subscribeScenesType;
    }

    public String getPayFailedInfo() {
        return payFailedInfo;
    }

    public void setPayFailedInfo(String payFailedInfo) {
        this.payFailedInfo = payFailedInfo;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    @Override
    public String toString() {
        return "OrderInfoVO{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", orderNumber='" + orderNumber + '\'' +
                ", order_number='" + order_number + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", lastName='" + lastName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", orderState=" + orderState +
                ", area='" + area + '\'' +
                ", city='" + city + '\'' +
                ", zipCode='" + zipCode + '\'' +
                ", province='" + province + '\'' +
                ", phoneCode='" + phoneCode + '\'' +
                ", phone='" + phone + '\'' +
                ", resellerCode='" + resellerCode + '\'' +
                ", orderCreateTime=" + orderCreateTime +
                ", subscribeScenesType='" + subscribeScenesType + '\'' +
                ", orderItemInfos=" + orderItemInfos +
                ", orderPaymentInfo=" + orderPaymentInfo +
                ", tradeupOrderInfo=" + tradeupOrderInfo +
                ", printRuleCheckResult=" + printRuleCheckResult +
                ", payFailedInfo='" + payFailedInfo + '\'' +
                '}';
    }
}
