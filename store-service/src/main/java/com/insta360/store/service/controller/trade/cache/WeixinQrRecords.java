package com.insta360.store.service.controller.trade.cache;

import cn.hutool.core.collection.ConcurrentHashSet;

/**
 * @Author: hyc
 * @Date: 2019-05-20
 * @Description: 缓存触发了微信扫码支付notify的订单号
 */
public class WeixinQrRecords {

    private static WeixinQrRecords instance = new WeixinQrRecords();

    private ConcurrentHashSet<String> set = new ConcurrentHashSet<>();

    public static WeixinQrRecords getInstance() {
        return instance;
    }

    public void add(String orderNumber) {
        set.add(orderNumber);
    }

    public boolean find(String orderNumber) {
        return set.contains(orderNumber);
    }

    public void remove(String orderNumber) {
        set.remove(orderNumber);
    }
}
