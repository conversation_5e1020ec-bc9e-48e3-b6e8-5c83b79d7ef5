package com.insta360.store.service.controller.meta.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.store.business.meta.model.HomepageItemGroup;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2021/8/4
 * @Description:
 */
public class HomeInfoResponseVO implements Serializable {

    private List<HomepageItemVO> items;

    @JSONField(name = "camera_groups")
    private List<HomepageItemGroup> cameraGroups;

    @JSONField(name = "accessory_groups")
    private List<HomepageItemGroup> accessoryGroups;

    @JSONField(name = "service_groups")
    private List<HomepageItemGroup> serviceGroups;

    @JSONField(name = "group_sets")
    private List<List<HomepageItemGroup>> groupSets;

    public List<HomepageItemVO> getItems() {
        return items;
    }

    public void setItems(List<HomepageItemVO> items) {
        this.items = items;
    }

    public List<HomepageItemGroup> getCameraGroups() {
        return cameraGroups;
    }

    public void setCameraGroups(List<HomepageItemGroup> cameraGroups) {
        this.cameraGroups = cameraGroups;
    }

    public List<HomepageItemGroup> getAccessoryGroups() {
        return accessoryGroups;
    }

    public void setAccessoryGroups(List<HomepageItemGroup> accessoryGroups) {
        this.accessoryGroups = accessoryGroups;
    }

    public List<HomepageItemGroup> getServiceGroups() {
        return serviceGroups;
    }

    public void setServiceGroups(List<HomepageItemGroup> serviceGroups) {
        this.serviceGroups = serviceGroups;
    }

    public List<List<HomepageItemGroup>> getGroupSets() {
        return groupSets;
    }

    public void setGroupSets(List<List<HomepageItemGroup>> groupSets) {
        this.groupSets = groupSets;
    }
}
