package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.meta.cache.cacheput.NavigationBarCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: wbt
 * @Date: 2023/11/03
 * @Description: 导航栏缓存更新
 */
@RestController
public class NavigationBarCachePutApi {

    @Autowired
    NavigationBarCachePutPack navigationBarCachePutPack;

    /**
     * 按语言和地区获取对应的导航栏配置列表
     *
     * @param country
     * @param language
     */
    @GetMapping("/rpc/store/service/cacheput/meta/nbc/listNavigationBarCategoryInfos")
    public void listNavigationBarCategoryInfos(@RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        navigationBarCachePutPack.doPackCacheNavigationBarCategoryInfo(country, language);
    }
}
