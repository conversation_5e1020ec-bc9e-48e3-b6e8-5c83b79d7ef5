package com.insta360.store.service.common.interceptor;

import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.order.service.impl.aop.annotation.LogOrderStateChange;
import com.insta360.store.business.order.service.impl.aop.context.OrderStateContext;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.common.WebApiContext;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: hyc
 * @Date: 2019-12-05
 * @Description: 对所有操作订单的接口进行拦截
 */
public class OrderInterceptor extends HandlerInterceptorAdapter {

    private ApplicationContext applicationContext;

    public OrderInterceptor(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        LogOrderStateChange annotation = handlerMethod.getMethodAnnotation(LogOrderStateChange.class);

        if (annotation != null) {

            // 创建
            OrderStateContext orderStateContext = applicationContext.getBean(OrderStateContext.class);

            if (orderStateContext != null) {

                WebApiContext apiContext = WebApiContext.get();
                if(apiContext != null){
                    StoreAccount accessUser = apiContext.getAccessUser();
                    if(accessUser != null){
                        orderStateContext.setOperator(accessUser.getUsername());
                    }

                    String adminJobNumber = apiContext.getAdminJobNumber();
                    if(StringUtil.isNotBlank(adminJobNumber)){
                        orderStateContext.setOperator(adminJobNumber);
                    }
                }

                OrderStateContext.set(orderStateContext);
            }
        }

        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);

        // 销毁
        OrderStateContext.remove();
    }
}
