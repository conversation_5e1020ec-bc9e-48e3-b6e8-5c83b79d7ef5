package com.insta360.store.service.common.interceptor;

import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.tradeup.service.impl.aop.LogTUOChange;
import com.insta360.store.business.tradeup.service.impl.aop.TradeupOrderContext;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.common.WebApiContext;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: wbt
 * @Date: 2020/07/01
 * @Description: 对所有操作Trade Up Order的接口进行拦截（下一步：考虑把订单状态变化记录相整合）
 */
public class TradeUpOrderInterceptor extends HandlerInterceptorAdapter {

    private ApplicationContext applicationContext;

    public TradeUpOrderInterceptor(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        LogTUOChange annotation = handlerMethod.getMethodAnnotation(LogTUOChange.class);

        if (annotation != null) {

            // 请求上下文，记录操作人员的信息
            TradeupOrderContext context = applicationContext.getBean(TradeupOrderContext.class);

            if (context != null) {
                WebApiContext webApiContext = WebApiContext.get();

                if (webApiContext != null) {
                    // 获取用户信息
                    StoreAccount accessUser = webApiContext.getAccessUser();

                    if (accessUser != null) {
                        context.setOperator(accessUser.getUsername());
                    }

                    // 后台管理系统操作人员信息
                    String adminJobNumber = webApiContext.getAdminJobNumber();
                    if (StringUtil.isNotBlank(adminJobNumber)) {
                        context.setOperator(adminJobNumber);
                    }
                }

                // 创建
                TradeupOrderContext.set(context);
            }
        }
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);

        // 销毁
        TradeupOrderContext.remove();
    }
}
