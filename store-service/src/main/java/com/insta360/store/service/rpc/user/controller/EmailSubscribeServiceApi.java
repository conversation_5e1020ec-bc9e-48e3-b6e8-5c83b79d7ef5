package com.insta360.store.service.rpc.user.controller;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.configuration.grafana.annotation.GrafanaDataStats;
import com.insta360.store.business.configuration.grafana.enums.GrafanaBusinessType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaKeyType;
import com.insta360.store.business.configuration.grafana.enums.GrafanaStatisticsType;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.integration.attentive.service.helper.AttentiveHelper;
import com.insta360.store.business.user.constant.UserConstant;
import com.insta360.store.business.user.dto.UserSubscribeDTO;
import com.insta360.store.business.user.enums.EmailSubscribeOriginEnum;
import com.insta360.store.business.user.enums.EmailSubscribeResultEnum;
import com.insta360.store.business.user.model.EmailSubscribe;
import com.insta360.store.business.user.service.impl.helper.EmailSubscribeActivityPageHelper;
import com.insta360.store.service.rpc.user.vo.EmailSubscribeRpcVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @description: 用户订阅rpc
 * @author: py
 * @create: 2022-04-08 10:57
 */
@RestController
public class EmailSubscribeServiceApi {

    @Autowired
    EmailSubscribeActivityPageHelper emailSubscribeActivityPageHelper;

    @Autowired
    AttentiveHelper attentiveHelper;

    /**
     * 提供远程调用的邮箱订阅的服务
     *
     * @param userSubscribeParam
     * @return
     * @deprecated 官网前端已于两年前停止调用，暂留
     */
    @GrafanaDataStats(keyType = GrafanaKeyType.SUBSCRIBE, businessType = GrafanaBusinessType.SUBSCRIBE, statisticsType = {GrafanaStatisticsType.COUNTER, GrafanaStatisticsType.GAUGE, GrafanaStatisticsType.TIMER})
    @AvoidRepeatableCommit(timeOut = 100)
    @PostMapping(path = "/rpc/store/service/user/subscribe/emailSubscribe", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response<? extends Map> emailSubscribe(@RequestBody UserSubscribeDTO userSubscribeParam) {
        if (StringUtil.isBlank(userSubscribeParam.getEmail())) {
            return Response.ok("emailSubscribe", new EmailSubscribeRpcVO(EmailSubscribeResultEnum.SubscribeFailed.name()));
        }

        if (!UserConstant.RPC_SUBSCRIBE_NAME.equals(userSubscribeParam.getActivityPage())) {
            return Response.ok("emailSubscribe", new EmailSubscribeRpcVO(EmailSubscribeResultEnum.SubscribeFailed.name()));
        }
        //设置好来源于官网还是商城
        userSubscribeParam.setSubscribeOrigin(EmailSubscribeOriginEnum.OFFICIAL_WEBSITE.name());
        EmailSubscribeRpcVO emailSubscribeRpcVO = null;
        try {
            EmailSubscribe subscribe = emailSubscribeActivityPageHelper.subscribe(userSubscribeParam);
            emailSubscribeRpcVO = new EmailSubscribeRpcVO(subscribe);

            //给官网返回订阅状态
            emailSubscribeRpcVO.setEmailSubscribeResult(EmailSubscribeResultEnum.SubscribeSuccess.name());

            // 检查活动配置的代金券模版是否失效
            Boolean templateInvalidFlag = emailSubscribeActivityPageHelper.giftCardTemplateInvalidFlagCheck(userSubscribeParam.getActivityPage());
            emailSubscribeRpcVO.setTemplateInvalidFlag(templateInvalidFlag);
        } catch (InstaException ie) {
            emailSubscribeRpcVO = new EmailSubscribeRpcVO(EmailSubscribeResultEnum.AlreadySubscribe.name());
        } catch (Exception e) {
            emailSubscribeRpcVO = new EmailSubscribeRpcVO(EmailSubscribeResultEnum.SubscribeFailed.name());
        }
        return Response.ok("emailSubscribe", emailSubscribeRpcVO);
    }

    /**
     * 通知service拿到上报成功的值
     */
    @GetMapping(path = "/rpc/service/user/subscribe/putResult")
    public Response<Object> getResult(@RequestParam Integer subscribeId) {
        attentiveHelper.putSubscribeId(subscribeId);
        return Response.ok();
    }
}
