package com.insta360.store.service.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqCategory;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/4/27
 * @Description:
 */
public class FaqQuestionBindVO implements Serializable {

    /**
     * 内部一级目录id
     */
    private Integer categoryInsideId;

    /**
     * 多语言类目名称
     */
    private String categoryName;

    /**
     * 语言
     */
    private String language;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 页面二级绑定信息
     */
    List<FaqQuestionSubsetBindVO> questionSubsetBinds;

    public FaqQuestionBindVO() {
    }

    public FaqQuestionBindVO(FaqCategory faqCategory) {
        if (faqCategory != null) {
            BeanUtil.copyProperties(faqCategory, this);
        }
    }

    public Integer getCategoryInsideId() {
        return categoryInsideId;
    }

    public void setCategoryInsideId(Integer categoryInsideId) {
        this.categoryInsideId = categoryInsideId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public List<FaqQuestionSubsetBindVO> getQuestionSubsetBinds() {
        return questionSubsetBinds;
    }

    public void setQuestionSubsetBinds(List<FaqQuestionSubsetBindVO> questionSubsetBinds) {
        this.questionSubsetBinds = questionSubsetBinds;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }
}
