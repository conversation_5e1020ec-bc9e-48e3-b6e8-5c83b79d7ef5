package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.meta.cache.cacheput.HomepageCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 首页缓存更新
 * @author: py
 * @create: 2023-10-25 10:39
 */
@RestController
public class HomepageCachePutApi {

    @Autowired
    HomepageCachePutPack homepageCachePutPack;

    /**
     * 按语言和地区获取对应的首页配置列表
     *
     * @param country
     * @param language
     */
    @GetMapping("/rpc/store/service/cacheput/meta/hp/listHomePageInfos")
    public void listHomepage(@RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        homepageCachePutPack.doPackCacheHomePageInfos(language, country);
    }
}
