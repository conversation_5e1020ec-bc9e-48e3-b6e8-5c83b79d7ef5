package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.GraphicNavigation;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/3/14
 * @Description:
 */
public class GraphicNavigationVO implements Serializable {

    private Integer id;

    /**
     * 导航类型
     */
    private String navigationType;

    /**
     * 图文导航配置
     */
    GraphicNavigationMainVO graphicNavigationMain;

    public GraphicNavigationVO() {
    }

    public GraphicNavigationVO(GraphicNavigation graphicNavigation) {
        if (Objects.nonNull(graphicNavigation)) {
            BeanUtil.copyProperties(graphicNavigation,this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNavigationType() {
        return navigationType;
    }

    public void setNavigationType(String navigationType) {
        this.navigationType = navigationType;
    }

    public GraphicNavigationMainVO getGraphicNavigationMain() {
        return graphicNavigationMain;
    }

    public void setGraphicNavigationMain(GraphicNavigationMainVO graphicNavigationMain) {
        this.graphicNavigationMain = graphicNavigationMain;
    }
}
