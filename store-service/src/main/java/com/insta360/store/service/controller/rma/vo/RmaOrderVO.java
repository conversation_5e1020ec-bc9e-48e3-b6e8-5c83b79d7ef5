package com.insta360.store.service.controller.rma.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.rma.model.RmaDelivery;
import com.insta360.store.business.rma.model.RmaOrder;

/**
 * @Author: hyc
 * @Date: 2019-10-16
 * @Description:
 */
public class RmaOrderVO extends RmaOrder {

    private RmaDelivery delivery;

    @JSONField(name = "order_item")
    private OrderItem orderItem;

    @JSONField(name = "product_name")
    private String productName;

    @JSONField(name = "commodity_name")
    private String commodityName;

    @JSONField(name = "commodity_display")
    private String commodityDisplay;

    @JSONField(name = "order_number")
    private String orderNumber;

    public RmaOrderVO() {
    }

    public RmaOrderVO(RmaOrder rmaOrder) {
        if (rmaOrder != null) {
            BeanUtil.copyProperties(rmaOrder, this);
        }
    }

    public RmaDelivery getDelivery() {
        return delivery;
    }

    public void setDelivery(RmaDelivery delivery) {
        this.delivery = delivery;
    }

    public OrderItem getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(OrderItem orderItem) {
        this.orderItem = orderItem;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCommodityDisplay() {
        return commodityDisplay;
    }

    public void setCommodityDisplay(String commodityDisplay) {
        this.commodityDisplay = commodityDisplay;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }
}
