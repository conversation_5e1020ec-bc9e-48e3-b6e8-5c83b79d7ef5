package com.insta360.store.service.controller.meta.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.commodity.model.CommoditySaleState;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.model.HomepageItem;
import com.insta360.store.business.product.model.ProductInfo;

/**
 * @Author: hyc
 * @Date: 2019/2/20
 * @Description:
 */
public class HomepageItemVO extends HomepageItem {

    private String link;

    @JSONField(name = "product_info")
    private ProductInfo productInfo;

    private Price price;

    @JSONField(name = "origin_price")
    private Price originPrice;

    @JSONField(name = "sale_state")
    private CommoditySaleState saleState;

    public HomepageItemVO() {
    }

    public HomepageItemVO(HomepageItem homeItem) {
        if (homeItem != null) {
            BeanUtil.copyProperties(homeItem, this);
        }
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public ProductInfo getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(ProductInfo productInfo) {
        this.productInfo = productInfo;
    }

    public Price getPrice() {
        return price;
    }

    public void setPrice(Price price) {
        this.price = price;
    }

    public Price getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Price originPrice) {
        this.originPrice = originPrice;
    }

    public CommoditySaleState getSaleState() {
        return saleState;
    }

    public void setSaleState(CommoditySaleState saleState) {
        this.saleState = saleState;
    }
}
