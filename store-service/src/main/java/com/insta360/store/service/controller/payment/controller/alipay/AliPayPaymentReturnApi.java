package com.insta360.store.service.controller.payment.controller.alipay;

import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.payment.constants.PaymentConstant;
import com.insta360.store.service.controller.payment.controller.BasePayment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Author: wkx
 * @Date: 2/26/24
 * @Description:
 */
@Controller
public class AliPayPaymentReturnApi extends BasePayment {

    private static final Logger LOGGER = LoggerFactory.getLogger(AliPayPaymentReturnApi.class);

    /**
     * 支付宝支付
     *
     * @param orderNumber
     * @param response
     * @throws IOException
     */
    @GetMapping("/store/payment/alipay/return/alipay")
    public void alipay(@RequestParam(value = "order") String orderNumber,
                       HttpServletResponse response) throws IOException {
        Order order = orderService.getByOrderNumber(orderNumber);
        if (order == null) {
            throw new InstaException(OrderErrorCode.OrderNotFoundException);
        }

        LOGGER.info("touch off a return url success by alipay");
        response.sendRedirect(orderPayResultPage(order));
    }

    /**
     * 全局异常跳转
     *
     * @param e
     * @param response
     */
    @ExceptionHandler(Exception.class)
    public void handlerPaymentResultException(Exception e, HttpServletResponse response) {
        this.doHandlerPaymentResultException(e, response, PaymentConstant.RETURN_TYPE);
    }
}
