package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.SeoConfigPack;
import com.insta360.store.service.controller.meta.vo.SeoConfigVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description SEO缓存更新处理类
 * @Date 2023/11/15
 */
@Component
public class SeoCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(SeoCachePutPack.class);

    @Autowired
    SeoConfigPack seoConfigPack;

    /**
     * 更新seo缓存
     *
     * @param language
     * @return
     */
    @CachePut(value = CacheableType.SEO_INFO, key = "caches[0].name + '-SeoCachePack-' + methodName + '-language-' + #language")
    public List<SeoConfigVO> seoConfigPack(InstaLanguage language) {
        List<SeoConfigVO> seoConfigVos = seoConfigPack.seoConfigPack(language);
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[language:%s],缓存更新响应:[%s]",
                CacheableType.SEO_INFO, "seoConfigPack", language, seoConfigVos));
        return seoConfigVos;
    }
}
