package com.insta360.store.service.controller.reseller.controller;

import com.insta360.compass.core.web.api.Response;
import com.insta360.compass.core.web.security.annotation.Authorization;
import com.insta360.store.business.configuration.resubmit.annotation.AvoidRepeatableCommit;
import com.insta360.store.business.reseller.dto.ResellerApplyDTO;
import com.insta360.store.business.reseller.dto.condition.ApplyInfo;
import com.insta360.store.business.reseller.enums.ResellerType;
import com.insta360.store.business.reseller.model.ResellerApply;
import com.insta360.store.business.reseller.service.ResellerApplyService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.service.controller.reseller.vo.ResellerApplyResponseVO;
import com.insta360.store.service.controller.reseller.vo.ResellerApplyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: hyc
 * @Date: 2019/2/27
 * @Description:
 */
@RestController
public class ResellerApplyApi extends BaseResellerApi {

    @Autowired
    ResellerApplyService applyService;

    /**
     * 个人分销商申请
     *
     * @param resellerApplyParam
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @Authorization
    @PostMapping(path = "/store/reseller/apply/submitApplyInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public <T> Response<T> submitIndividualApplyInfo(@RequestBody ResellerApplyDTO resellerApplyParam) {

        ApplyInfo applyInfo = new ApplyInfo();
        applyInfo.setAccount(getAccessUser());
        applyInfo.setLanguage(getApiLanguage());
        applyInfo.setType(ResellerType.parse(resellerApplyParam.getType()));
        applyInfo.setInfo(resellerApplyParam.getApplyInfo());
        applyInfo.setProducts(resellerApplyParam.getApplyProducts());
        applyInfo.setInviterCode(resellerApplyParam.getInviterCode());
        applyInfo.setResellerExtension(resellerApplyParam.getResellerExtension());
        applyInfo.setCheck(resellerApplyParam.getCheck());

        applyService.apply(applyInfo);
        return Response.ok();
    }

    /**
     * 企业分销商申请（无需登录）
     *
     * @param resellerApplyParam
     */
    @AvoidRepeatableCommit(timeOut = 500)
    @PostMapping(path = "/store/reseller/agent/apply/submitApplyInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
    public <T> Response<T> submitAgentApplyInfo(@RequestBody ResellerApplyDTO resellerApplyParam) {

        ApplyInfo applyInfo = new ApplyInfo();
        applyInfo.setLanguage(getApiLanguage());
        applyInfo.setType(ResellerType.parse(resellerApplyParam.getType()));
        applyInfo.setInfo(resellerApplyParam.getApplyInfo());
        applyInfo.setProducts(resellerApplyParam.getApplyProducts());
        applyInfo.setInviterCode(resellerApplyParam.getInviterCode());

        applyService.apply(applyInfo);
        return Response.ok();
    }

    /**
     * 获取分销申请信息
     *
     * @return
     */
    @AvoidRepeatableCommit(timeOut = 100)
    @Authorization
    @GetMapping("/store/reseller/apply/getApplyInfo")
    public Response<ResellerApplyResponseVO> getApplyInfo() {
        StoreAccount storeAccount = getAccessUser();
        ResellerApply individualApply = applyService.getApplyInfo(storeAccount.getInstaAccount(), ResellerType.individual);

        ResellerApplyResponseVO resellerApplyResponseVO = new ResellerApplyResponseVO();
        resellerApplyResponseVO.setIndividualApply(new ResellerApplyVO(individualApply));

        return Response.ok(resellerApplyResponseVO);
    }
}
