package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.enums.NavigationBarTypeEnum;
import com.insta360.store.business.meta.model.NavigationBarCategoryInside;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/6/1
 * @Description:
 */
public class NavigationBarCategoryVO implements Serializable {

    private Integer id;

    /**
     * 一级内部名称
     */
    private String categoryInsideName;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 跳转类型
     */
    private String urlType;

    /**
     * 跳转链接
     */
    private String urlLink;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 类型
     */
    private String type;

    /**
     * 是否移动端
     */
    private Boolean isMobile;

    /**
     * 是否pc端
     */
    private Boolean isPc;

    /**
     * 二级类目信息
     */
    private List<NavigationBarCategorySubsetInfoVO> categorySubsets;

    /**
     * 配件二级导航栏信息
     */
    private List<NavigationBarCategorySubsetVO> subsetList;

    /**
     * banner信息
     */
    List<NavigationBarBannerMainVO> bannerMains;

    public NavigationBarCategoryVO() {
    }

    public NavigationBarCategoryVO(NavigationBarCategoryInside categoryInside) {
        initNavigationBarCategoryInside(categoryInside);
    }

    /**
     * 初始化数据
     *
     * @param categoryInside
     */
    private void initNavigationBarCategoryInside(NavigationBarCategoryInside categoryInside) {
        if (categoryInside == null) {
            return;
        }
        BeanUtil.copyProperties(categoryInside, this);

        // 过滤非文字链类型分端数据
        if (!NavigationBarTypeEnum.TEXT_LINK.equals(NavigationBarTypeEnum.parse(categoryInside.getType()))) {
            this.setIsMobile(null);
            this.setIsPc(null);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCategoryInsideName() {
        return categoryInsideName;
    }

    public void setCategoryInsideName(String categoryInsideName) {
        this.categoryInsideName = categoryInsideName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getUrlType() {
        return urlType;
    }

    public void setUrlType(String urlType) {
        this.urlType = urlType;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public void setUrlLink(String urlLink) {
        this.urlLink = urlLink;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getIsMobile() {
        return isMobile;
    }

    public void setIsMobile(Boolean mobile) {
        isMobile = mobile;
    }

    public Boolean getIsPc() {
        return isPc;
    }

    public void setIsPc(Boolean pc) {
        isPc = pc;
    }

    public List<NavigationBarCategorySubsetInfoVO> getCategorySubsets() {
        return categorySubsets;
    }

    public void setCategorySubsets(List<NavigationBarCategorySubsetInfoVO> categorySubsets) {
        this.categorySubsets = categorySubsets;
    }

    public List<NavigationBarBannerMainVO> getBannerMains() {
        return bannerMains;
    }

    public void setBannerMains(List<NavigationBarBannerMainVO> bannerMains) {
        this.bannerMains = bannerMains;
    }

    public List<NavigationBarCategorySubsetVO> getSubsetList() {
        return subsetList;
    }

    public void setSubsetList(List<NavigationBarCategorySubsetVO> subsetList) {
        this.subsetList = subsetList;
    }

    @Override
    public String toString() {
        return "NavigationBarCategoryVO{" +
                "id=" + id +
                ", categoryInsideName='" + categoryInsideName + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", urlType='" + urlType + '\'' +
                ", urlLink='" + urlLink + '\'' +
                ", orderIndex=" + orderIndex +
                ", type='" + type + '\'' +
                ", isMobile=" + isMobile +
                ", isPc=" + isPc +
                ", categorySubsets=" + categorySubsets +
                ", subsetList=" + subsetList +
                ", bannerMains=" + bannerMains +
                '}';
    }
}
