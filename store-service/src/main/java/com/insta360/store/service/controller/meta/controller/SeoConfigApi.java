package com.insta360.store.service.controller.meta.controller;

import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.configuration.prerelease.annotation.ProductDataPreRelease;
import com.insta360.store.service.common.BaseApi;
import com.insta360.store.service.controller.meta.cache.cacheable.SeoCachePack;
import com.insta360.store.service.controller.meta.filter.SeoConfigDataFilter;
import com.insta360.store.service.controller.meta.vo.SeoConfigVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/11/8
 * @Description:
 */
@RestController
public class SeoConfigApi extends BaseApi {

    @Autowired
    SeoCachePack seoCachePack;

    @Autowired
    SeoConfigDataFilter seoConfigDataFilter;

    /**
     * 获取seo配置
     *
     * @return
     */
    @ProductDataPreRelease
    @GetMapping("/store/meta/listSeoConfig")
    public Response<Object> listSeoConfig() {
        List<SeoConfigVO> seoConfigs = seoCachePack.seoConfigPack(getApiLanguage());
        return Response.ok(getProductNewDataDisplay() ? seoConfigs : seoConfigDataFilter.listSeoConfigDataFilter(seoConfigs));
    }
}
