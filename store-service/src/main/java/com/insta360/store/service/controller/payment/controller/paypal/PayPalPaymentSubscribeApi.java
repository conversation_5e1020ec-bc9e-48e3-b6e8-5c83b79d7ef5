package com.insta360.store.service.controller.payment.controller.paypal;

import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.cloud.enums.SubscribeActionType;
import com.insta360.store.business.cloud.enums.SubscribeStatus;
import com.insta360.store.business.cloud.model.CloudStorageSubscribe;
import com.insta360.store.business.cloud.service.CloudStorageSubscribeService;
import com.insta360.store.business.configuration.utils.RSAUtil;
import com.insta360.store.business.payment.service.impl.aop.payment_return.PaymentReturnResultHandler;
import com.insta360.store.business.payment.service.impl.helper.PayPalPaymentHelper;
import com.insta360.store.business.payment.service.impl.helper.PaymentHelper;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.model.UserPayInfo;
import com.insta360.store.business.user.service.StoreAccountService;
import com.insta360.store.business.user.service.UserPayInfoService;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import com.insta360.store.service.controller.payment.controller.BasePayment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2024/06/12
 * @Description:
 */
@RestController
public class PayPalPaymentSubscribeApi extends BasePayment {

    private static final Logger LOGGER = LoggerFactory.getLogger(PayPalPaymentSubscribeApi.class);

    @Autowired
    CloudStorageSubscribeService cloudStorageSubscribeService;

    @Autowired
    UserPayInfoService userPayInfoService;

    @Autowired
    PayPalPaymentHelper payPalPaymentHelper;

    @Autowired
    PaymentHelper paymentHelper;

    @Autowired
    StoreAccountService storeAccountService;

    @Autowired
    UserAccountHelper userAccountHelper;

    /**
     * 创建vault id
     *
     * @param token
     * @param decryptOrderNumber
     * @param response
     * @return
     */
    @PaymentReturnResultHandler
    @GetMapping(path = "/store/payment/paypal/subscribe/createVault")
    public void createVault(@RequestParam(value = "approval_token_id") String token,
                            @RequestParam(value = "insta_callback") String decryptOrderNumber,
                            HttpServletResponse response) throws IOException {
        String orderNumber = RSAUtil.decryptByMyPri(decryptOrderNumber);
        CloudStorageSubscribe storageSubscribe = cloudStorageSubscribeService.getByOrderNumber(orderNumber);
        // 没有生效订阅记录，不允许更新
        if (Objects.isNull(storageSubscribe)) {
            LOGGER.info("没有生效订阅记录，不允许更新");
            return;
        }

        UserPayInfo userPayInfo = userPayInfoService.getByInstaAccount(storageSubscribe.getInstaAccount());
        // 没有支付信息记录，不允许更新
        if (Objects.isNull(userPayInfo)) {
            LOGGER.info(String.format("没有支付信息记录，不允许更新 订阅信息{%s}", storageSubscribe));
            return;
        }

        StoreAccount storeAccount = userAccountHelper.getStoreAccountByUserId(storageSubscribe.getInstaAccount());
        // 没有账户信息，不允许更新
        if (Objects.isNull(storeAccount)) {
            LOGGER.info(String.format("没有账户信息，不允许更新 订阅信息{%s}", storageSubscribe));
            return;
        }

        SubscribeActionType subscribeActionType = SubscribeActionType.UPDATE_CARD;
        // 重新订阅需更新订阅状态
        if (SubscribeStatus.CLOSE_RENEW.equals(SubscribeStatus.parse(storageSubscribe.getSubscribeState()))) {
            storageSubscribe.setSubscribeState(SubscribeStatus.SUBSCRIBE_ING.getCode());
            storageSubscribe.setUpdateTime(LocalDateTime.now());
            subscribeActionType = SubscribeActionType.RENEW_SUBSCRIBE;
        }

        // 创建paypal 保险库信息
        String createVaultResult = payPalPaymentHelper.createVault(storeAccount, storageSubscribe, userPayInfo, token, subscribeActionType);
        if (StringUtil.isBlank(createVaultResult)) {
            createVaultResult = paymentHelper.getSubscribeCancelUrl(userPayInfo.parsePaymentMethod(), subscribeActionType);
        }
        response.sendRedirect(createVaultResult);
    }
}
