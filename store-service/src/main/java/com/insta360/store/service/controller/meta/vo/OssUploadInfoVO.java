package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.compass.libs.aliyun.oss.dto.OSSDirectSignInfo;

/**
 * <AUTHOR>
 * @Date 2023/5/25 10:17
 * @Description:
 * @Version 1.0
 */
public class OssUploadInfoVO {

    /**
     * accessId
     */
    private String accessId;

    /**
     * 指定的权限
     */
    private String policy;

    /**
     * 签名
     */
    private String signature;

    /**
     * host
     */
    private String host;

    /**
     * 过期时间
     * 例子: 1635148393
     */
    private Long expire;

    /**
     * 文件的路径,包含了路径+时间戳MDs目录
     *
     * @return
     */
    private String filePath;

    /**
     * 文件名字
     *
     * @return
     */
    private String fileName;

    public OssUploadInfoVO() {
    }

    public OssUploadInfoVO(OSSDirectSignInfo ossDirectSignInfo) {
        this.init(ossDirectSignInfo);
    }

    /**
     * 值copy
     *
     * @param ossDirectSignInfo
     */
    private void init(OSSDirectSignInfo ossDirectSignInfo) {
        if (ossDirectSignInfo != null) {
            BeanUtil.copyProperties(ossDirectSignInfo, this);
        }
    }

    public void getPojoObject(OSSDirectSignInfo ossDirectSignInfo) {
        if (ossDirectSignInfo != null) {
            BeanUtil.copyProperties(ossDirectSignInfo, this);
            this.setFilePath(ossDirectSignInfo.getFilePath() + "/" + ossDirectSignInfo.getFileName());
        }
    }

    public String getAccessId() {
        return accessId;
    }

    public void setAccessId(String accessId) {
        this.accessId = accessId;
    }

    public String getPolicy() {
        return policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Long getExpire() {
        return expire;
    }

    public void setExpire(Long expire) {
        this.expire = expire;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public String toString() {
        return "OssUploadInfoVO{" +
                "accessId='" + accessId + '\'' +
                ", policy='" + policy + '\'' +
                ", signature='" + signature + '\'' +
                ", host='" + host + '\'' +
                ", expire=" + expire +
                ", filePath='" + filePath + '\'' +
                ", fileName='" + fileName + '\'' +
                '}';
    }
}
