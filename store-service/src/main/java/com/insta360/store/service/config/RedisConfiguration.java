package com.insta360.store.service.config;

import com.insta360.compass.core.cache.redis.BaseRedisConfiguration;
import com.insta360.compass.core.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;

/**
 * @Author: wbt
 * @Date: 2022/02/16
 * @Description:
 */
@Configuration
@EnableConfigurationProperties({CacheProperties.class})
public class RedisConfiguration extends BaseRedisConfiguration {

    @Autowired
    CacheProperties cacheProperties;

    @Override
    protected RedisCacheConfiguration setRedisCacheConfiguration(RedisCacheConfiguration config) {
        CacheProperties.Redis redisProperties = cacheProperties.getRedis();
        if (redisProperties != null) {
            config = config.entryTtl(redisProperties.getTimeToLive());
            return StringUtil.isNotBlank(redisProperties.getKeyPrefix()) ? config.prefixCacheNameWith(redisProperties.getKeyPrefix()) : config;
        }
        return config;
    }
}
