package com.insta360.store.service.rpc.cloud.format;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.store.business.cloud.bo.CloudBenefitBindResultBO;
import com.insta360.store.business.cloud.bo.CloudStorageBenefitBO;
import com.insta360.store.business.cloud.config.CloudStorageConfig;
import com.insta360.store.business.cloud.dto.UseBenefitDTO;
import com.insta360.store.business.cloud.enums.*;
import com.insta360.store.business.cloud.exception.CloudStorageBenefitErrorCode;
import com.insta360.store.business.cloud.model.*;
import com.insta360.store.business.cloud.service.*;
import com.insta360.store.business.cloud.service.impl.context.StoreBenefitBindContext;
import com.insta360.store.business.cloud.service.impl.factory.StoreBenefitBindFactory;
import com.insta360.store.business.cloud.service.impl.helper.StoreBenefitDiscountHelper;
import com.insta360.store.business.discount.dto.ao.BatchCreateDiscountCommonAO;
import com.insta360.store.business.discount.dto.ro.BatchCreateCommonDiscountRO;
import com.insta360.store.business.discount.enums.DiscountModel;
import com.insta360.store.business.discount.enums.PlatformSourceType;
import com.insta360.store.business.discount.service.DiscountWritService;
import com.insta360.store.business.insurance.config.InsuranceCommonConfiguration;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.user.model.StoreAccount;
import com.insta360.store.business.user.service.impl.helper.UserAccountHelper;
import com.insta360.store.service.rpc.cloud.vo.UseBeneFitVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: py
 * @create: 2024-05-15 18:56
 */
@Component
public class CloudStorageBenefitPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudStorageBenefitPack.class);

    @Autowired
    CloudStorageStoreBenefitService cloudStorageStoreBenefitService;

    @Autowired
    CloudStorageStoreBenefitDetailService cloudStorageStoreBenefitDetailService;

    @Autowired
    StoreBenefitBindFactory storeBenefitBindFactory;

    @Autowired
    DiscountWritService discountWritService;

    @Autowired
    CloudStorageCompensateDetailService cloudStorageCompensateDetailService;

    @Autowired
    CloudStorageConfig cloudStorageConfig;

    @Autowired
    StoreBenefitDiscountHelper storeBenefitDiscountHelper;

    @Autowired
    InsuranceCommonConfiguration insuranceCommonConfiguration;

    @Autowired
    CountryConfigService countryConfigService;

    @Autowired
    CloudStorageCardBenefitService cloudStorageCardBenefitService;

    @Autowired
    CloudStorageCardBenefitDetailService cloudStorageCardBenefitDetailService;

    @Autowired
    UserAccountHelper userAccountHelper;

    /**
     * 权益状态查询
     *
     * @param userId
     * @return
     */
    public UseBeneFitVO getBenefit(Integer userId) {
        // 查询线上订阅权益
        Map<Integer, Boolean> subscribeBenefitMap = getSubscribeBenefit(userId);

        // 查询实体卡权益
        Map<Integer, Boolean> cardBenefitMap = getCardBenefit(userId);

        UseBeneFitVO useBeneFitVo = new UseBeneFitVO();
        useBeneFitVo.setExistCare(subscribeBenefitMap.get(BenefitType.CARE.getType()) || cardBenefitMap.get(BenefitType.CARE.getType()));
        useBeneFitVo.setExistExtend(subscribeBenefitMap.get(BenefitType.EXTEND.getType()) || cardBenefitMap.get(BenefitType.EXTEND.getType()));
        return useBeneFitVo;
    }

    /**
     * 查询实体卡权益
     *
     * @param userId
     * @return
     */
    private Map<Integer, Boolean> getCardBenefit(Integer userId) {
        HashMap<Integer, Boolean> resultMap = new HashMap<>(2);
        List<CloudStorageCardBenefit> cloudStorageCardBenefits = cloudStorageCardBenefitService.listBenefitByUserId(userId);
        if (CollectionUtils.isEmpty(cloudStorageCardBenefits)) {
            resultMap.put(BenefitType.CARE.getType(), false);
            resultMap.put(BenefitType.EXTEND.getType(), false);
            return resultMap;
        }
        LOGGER.info(String.format("[云服务权益查询]云存实体卡服务,用户id:%s,benefit:%s", userId, cloudStorageCardBenefits));

        List<Long> benefitIds = cloudStorageCardBenefits.stream().map(CloudStorageCardBenefit::getId).collect(Collectors.toList());
        List<CloudStorageCardBenefitDetail> cloudStorageCardBenefitDetails = cloudStorageCardBenefitDetailService.listByBenefitIds(benefitIds);
        if (CollectionUtils.isEmpty(cloudStorageCardBenefitDetails)) {
            resultMap.put(BenefitType.CARE.getType(), false);
            resultMap.put(BenefitType.EXTEND.getType(), false);
            return resultMap;
        }

        Map<Integer, List<CloudStorageCardBenefitDetail>> cardDetailMap = cloudStorageCardBenefitDetails.stream().collect(Collectors.groupingBy(CloudStorageCardBenefitDetail::getBenefitType));
        List<CloudStorageCardBenefitDetail> cardDetails = cardDetailMap.get(BenefitType.CARE.getType());
        List<CloudStorageCardBenefitDetail> extendDetails = cardDetailMap.get(BenefitType.EXTEND.getType());

        resultMap.put(BenefitType.CARE.getType(), CollectionUtils.isNotEmpty(cardDetails));
        resultMap.put(BenefitType.EXTEND.getType(), CollectionUtils.isNotEmpty(extendDetails));
        return resultMap;
    }

    /**
     * 查询线上订阅的权益
     *
     * @param userId
     * @return
     */
    public Map<Integer, Boolean> getSubscribeBenefit(Integer userId) {
        HashMap<Integer, Boolean> resultMap = new HashMap<>(2);
        CloudStorageStoreBenefit cloudStorageStoreBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(userId);
        if (Objects.isNull(cloudStorageStoreBenefit) || cloudStorageStoreBenefit.isExpired()) {
            resultMap.put(BenefitType.CARE.getType(), false);
            resultMap.put(BenefitType.EXTEND.getType(), false);
            return resultMap;
        }
        LOGGER.info(String.format("[云服务权益查询]线上订阅服务,用户id:%s,benefit:%s", userId, cloudStorageStoreBenefit));

        Long benefitId = cloudStorageStoreBenefit.getId();
        CloudStorageStoreBenefitDetail careDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetailUsable(benefitId, BenefitType.CARE.getType());
        CloudStorageStoreBenefitDetail extendDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetailUsable(benefitId, BenefitType.EXTEND.getType());

        resultMap.put(BenefitType.CARE.getType(), Objects.nonNull(careDetail));
        resultMap.put(BenefitType.EXTEND.getType(), Objects.nonNull(extendDetail));
        return resultMap;
    }

    /**
     * 使用云服务的权益
     *
     * @param useBenefitParam
     */
    public UseBeneFitVO useBenefit(UseBenefitDTO useBenefitParam) {
        Integer userId = useBenefitParam.getUserId();
        CloudStorageStoreBenefit cloudStorageStoreBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(userId);

        // 线上订阅：代金券权益判断和发放
        Boolean needCoupon = useBenefitParam.getNeedCoupon();
        if (Objects.nonNull(needCoupon) && needCoupon) {
            if (Objects.isNull(cloudStorageStoreBenefit) || cloudStorageStoreBenefit.isExpired()) {
                throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotExistException);
            }
            String giftCardCode = this.compensateGiftCard(cloudStorageStoreBenefit, useBenefitParam.getDeviceSerial());
            return new UseBeneFitVO(giftCardCode);
        }

        String type = useBenefitParam.getBenefitType();
        BenefitType benefitType = BenefitType.parse(type);
        if (Objects.isNull(benefitType)) {
            throw new InstaException(CommonErrorCode.InvalidParameter);
        }

        // 获取线上订阅权益
        StoreBenefitBindContext subscribeBenefit = getSubscribeBenefit(useBenefitParam, cloudStorageStoreBenefit, benefitType);

        // 获取实体卡权益
        StoreBenefitBindContext cardBenefit = getCardBenefit(useBenefitParam, benefitType);

        if (Objects.isNull(cardBenefit) && Objects.isNull(subscribeBenefit)) {
            LOGGER.error(String.format("[云存权益兑换]权益兑换失败,未找到对应权益,用户id:%s,序列号:%s,请求权益类型:%s", userId, useBenefitParam.getDeviceSerial(), benefitType.getValue()));
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotExistException);
        }

        StoreBenefitBindContext storeBenefitBindContext;
        if (Objects.isNull(subscribeBenefit)) {
            storeBenefitBindContext = cardBenefit;
        } else if (Objects.isNull(cardBenefit)) {
            storeBenefitBindContext = subscribeBenefit;
        } else {
            // 比较过期时间
            storeBenefitBindContext = subscribeBenefit.getExpirationTime() < cardBenefit.getExpirationTime() ? subscribeBenefit : cardBenefit;
        }

        LOGGER.info(String.format("[云存权益兑换]准备兑换云服务权益,用户id:%s,序列号:%s,请求权益类型:%s", userId, useBenefitParam.getDeviceSerial(), benefitType.getValue()));
        StoreBenefitBindService storeBenefitBindService = storeBenefitBindFactory.getStoreBenefitBindService(benefitType);
        CloudBenefitBindResultBO cloudBenefitBindResultParam = storeBenefitBindService.handle(storeBenefitBindContext);
        return new UseBeneFitVO(cloudBenefitBindResultParam);
    }

    /**
     * 获取线上订阅的权益
     *
     * @param cloudStorageStoreBenefit
     */
    public StoreBenefitBindContext getSubscribeBenefit(UseBenefitDTO useBenefitParam, CloudStorageStoreBenefit cloudStorageStoreBenefit, BenefitType benefitType) {
        if (Objects.isNull(cloudStorageStoreBenefit) || cloudStorageStoreBenefit.isExpired()) {
            LOGGER.error(String.format("[云存权益兑换]线上订阅权益不存在或已过期,用户id:%s,序列号:%s,请求权益类型:%s",
                    useBenefitParam.getUserId(), useBenefitParam.getDeviceSerial(), useBenefitParam.getBenefitType()));
            return null;
        }
        Long benefitId = cloudStorageStoreBenefit.getId();
        CloudStorageStoreBenefitDetail cloudStorageStoreBenefitDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetailUsable(benefitId, benefitType.getType());
        if (Objects.isNull(cloudStorageStoreBenefitDetail) || cloudStorageStoreBenefitDetail.getUsed()) {
            LOGGER.error(String.format("[云存权益兑换]没有查到对应的增值服务权益或已被使用,用户id:%s,序列号:%s,请求权益类型:%s,权益数据:%s",
                    useBenefitParam.getUserId(), useBenefitParam.getDeviceSerial(), useBenefitParam.getBenefitType(), cloudStorageStoreBenefitDetail));
            return null;
        }

        CloudStorageBenefitBO cloudStorageBenefit = new CloudStorageBenefitBO(cloudStorageStoreBenefitDetail);
        StoreBenefitBindContext storeBenefitBindContext = initBindContext(useBenefitParam, cloudStorageBenefit);
        storeBenefitBindContext.setExpirationTime(cloudStorageStoreBenefit.getExpirationTime());
        storeBenefitBindContext.setBusinessType(BenefitBusinessTypeType.cloud_subscribe);
        return storeBenefitBindContext;
    }

    /**
     * 获取实体卡的权益
     *
     * @param useBenefitParam
     * @param benefitType
     */
    public StoreBenefitBindContext getCardBenefit(UseBenefitDTO useBenefitParam, BenefitType benefitType) {
        Integer userId = useBenefitParam.getUserId();
        List<CloudStorageCardBenefit> cloudStorageCardBenefits = cloudStorageCardBenefitService.listBenefitByUserId(userId);
        if (CollectionUtils.isEmpty(cloudStorageCardBenefits)) {
            LOGGER.error(String.format("[云存权益兑换]云存实体卡不存在,用户id:%s,序列号:%s,请求权益类型:%s", useBenefitParam.getUserId(), useBenefitParam.getDeviceSerial(), useBenefitParam.getBenefitType()));
            return null;
        }
        List<Long> benefitIds = cloudStorageCardBenefits.stream().map(CloudStorageCardBenefit::getId).collect(Collectors.toList());
        List<CloudStorageCardBenefitDetail> cloudStorageCardBenefitDetails = cloudStorageCardBenefitDetailService.listByBenefitIdType(benefitIds, benefitType.getType());
        if (CollectionUtils.isEmpty(cloudStorageCardBenefitDetails)) {
            LOGGER.error(String.format("[云存权益兑换]云存实体卡不存在请求权益,用户id:%s,序列号:%s,请求权益类型:%s", useBenefitParam.getUserId(), useBenefitParam.getDeviceSerial(), useBenefitParam.getBenefitType()));
            return null;
        }

        List<Long> detailBenefitIds = cloudStorageCardBenefitDetails.stream().map(CloudStorageCardBenefitDetail::getBenefitId).collect(Collectors.toList());
        List<CloudStorageCardBenefit> suitableBenefits = cloudStorageCardBenefits.stream().filter(cloudStorageCardBenefit -> detailBenefitIds.contains(cloudStorageCardBenefit.getId())).collect(Collectors.toList());
        // 筛选出过期时间最早的
        Optional<CloudStorageCardBenefit> minCardBenefit = suitableBenefits.stream()
                .min(Comparator.comparingLong(CloudStorageCardBenefit::getExpirationTime));
        if (!minCardBenefit.isPresent()) {
            LOGGER.error(String.format("[云存权益兑换]云存实体卡不存在请求权益,未找到对应过期时间的权益,用户id:%s,序列号:%s,请求权益类型:%s", useBenefitParam.getUserId(), useBenefitParam.getDeviceSerial(), useBenefitParam.getBenefitType()));
            return null;
        }

        CloudStorageCardBenefit cloudStorageCardBenefit = minCardBenefit.get();
        Long benefitId = cloudStorageCardBenefit.getId();

        // 再查detail表
        CloudStorageCardBenefitDetail cardBenefitDetail = cloudStorageCardBenefitDetailService.getStoreBenefitDetail(benefitId, benefitType.getType());
        if (Objects.isNull(cardBenefitDetail) || cardBenefitDetail.getUsed()) {
            LOGGER.error(String.format("[云存权益兑换]权益不存在,或已被使用,用户id:%s,序列号:%s,请求权益类型:%s,benefitId:%s",
                    useBenefitParam.getUserId(), useBenefitParam.getDeviceSerial(), useBenefitParam.getBenefitType(), benefitId));
            return null;
        }

        CloudStorageBenefitBO cloudStorageBenefit = new CloudStorageBenefitBO(cardBenefitDetail);
        StoreBenefitBindContext storeBenefitBindContext = initBindContext(useBenefitParam, cloudStorageBenefit);
        storeBenefitBindContext.setExpirationTime(cloudStorageCardBenefit.getExpirationTime());
        storeBenefitBindContext.setBusinessType(BenefitBusinessTypeType.cloud_card);
        return storeBenefitBindContext;
    }

    /**
     * 补偿代金券
     *
     * @param cloudStorageStoreBenefit
     * @return
     */
    private String compensateGiftCard(CloudStorageStoreBenefit cloudStorageStoreBenefit, String deviceSerial) {
        CloudStorageStoreBenefitDetail careDetail = cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(cloudStorageStoreBenefit.getId(), BenefitType.CARE.getType());
        if (Objects.isNull(careDetail) || careDetail.getExpired() || careDetail.getUsed()) {
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotExistException);
        }

        // 获取用户信息
        StoreAccount storeAccount = null;
        try {
            storeAccount = userAccountHelper.getStoreAccountByUserId(cloudStorageStoreBenefit.getUserId());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (Objects.isNull(storeAccount)) {
            throw new InstaException(CloudStorageBenefitErrorCode.InsuranceNotExistException);
        }

        CloudStorageCompensateDetail storageCompensateDetail = cloudStorageCompensateDetailService.getDetailBySerialNumber(cloudStorageStoreBenefit.getUserId(), deviceSerial, BenefitType.CARE);
        if (Objects.nonNull(storageCompensateDetail)) {
            throw new InstaException(CloudStorageBenefitErrorCode.CompensateRepeatException);
        }

        // 封装请求参数
        BatchCreateDiscountCommonAO batchCreateGiftCardAo = new BatchCreateDiscountCommonAO();
        batchCreateGiftCardAo.setTemplateCode(cloudStorageConfig.getTemplateCode());
        batchCreateGiftCardAo.setBindEmail(storeAccount.getUsername());
        batchCreateGiftCardAo.setRemark("cloud_gift");
        batchCreateGiftCardAo.setDiscountModel(DiscountModel.GIFT_CODE.code);
        batchCreateGiftCardAo.setPlatformSource(PlatformSourceType.TEMPLATE.code);
        batchCreateGiftCardAo.setEffectDelayDays(cloudStorageConfig.getEffectDelayDays());

        BatchCreateCommonDiscountRO createCommonDiscountRo;
        try {
            createCommonDiscountRo = discountWritService.batchCreate(batchCreateGiftCardAo);
        } catch (Exception e) {
            LOGGER.error(String.format("云服务care权益补偿代金券失败. userId:{%s}", cloudStorageStoreBenefit.getUserId()), e);
            throw new InstaException(CloudStorageBenefitErrorCode.CompensateGiftCardFailedException);
        }

        String giftCardCode = null;
        if (Objects.nonNull(createCommonDiscountRo) && CollectionUtils.isNotEmpty(createCommonDiscountRo.getCodeList())) {
            giftCardCode = createCommonDiscountRo.getCodeList().stream().findFirst().get();
        }

        if (StringUtils.isNotBlank(giftCardCode)) {
            storeBenefitDiscountHelper.bindDeviceSerialGiftCard(cloudStorageStoreBenefit.getUserId(), deviceSerial, giftCardCode);
        }

        return giftCardCode;
    }

    /**
     * 初始化权益绑定上下文
     *
     * @param useBenefitParam
     * @param cloudStorageBenefit
     * @return
     */
    private StoreBenefitBindContext initBindContext(UseBenefitDTO useBenefitParam, CloudStorageBenefitBO cloudStorageBenefit) {
        StoreBenefitBindContext storeBenefitBindContext = new StoreBenefitBindContext();
        storeBenefitBindContext.setDeviceSerial(useBenefitParam.getDeviceSerial());
        storeBenefitBindContext.setUserId(useBenefitParam.getUserId());
        storeBenefitBindContext.setCloudStorageBenefit(cloudStorageBenefit);
        return storeBenefitBindContext;
    }

    /**
     * 获取care & 延保上线地区
     *
     * @param countryCode
     * @return
     */
    public UseBeneFitVO getRegion(String countryCode) {
        AppChannelCountry appChannelCountry = AppChannelCountry.matchCode(countryCode);
        if (Objects.isNull(appChannelCountry)) {
            throw new InstaException(CloudStorageBenefitErrorCode.NotSupportRegionException);
        }

        // care & 延保上线地区
        String country = appChannelCountry.name();
        List<String> careOnlineRegions = insuranceCommonConfiguration.getCareOnlineRegions();
        List<String> extendOnlineRegions = insuranceCommonConfiguration.getExtendOnlineRegions();

        // 商城支持国家
        InstaCountry instaCountry = InstaCountry.parse(country);
        CountryConfig countryConfig = countryConfigService.getByCountry(instaCountry);

        UseBeneFitVO regionParam = new UseBeneFitVO();
        regionParam.setExistCare(careOnlineRegions.contains(country));
        regionParam.setExistExtend(extendOnlineRegions.contains(country));
        regionParam.setStoreSale(Objects.nonNull(countryConfig));
        return regionParam;
    }

    /**
     * 查询权益状态
     * <p>
     * ○ 配件折扣
     * ■ 1：账户有该权益且没过期
     * ■ 3：账户没有该权益或已过期
     * ○ care
     * ■ 1：账户有该权益且没过期且还没有使用
     * ■ 2：账户有该权益且没过期但已经使用
     * ■ 3：账户没有该权益或已过期
     * ○ 延保
     * ■ 1：账户有该权益且没过期且还没有使用
     * ■ 2：账户有该权益且没过期但已经使用
     * ■ 3：账户没有该权益或已过期
     * ○ 错误码
     * ■ 请求参数不存在对应数据（包括帐号不存在和没有订阅数据）
     *
     * @param userId
     * @return
     */
    public UseBeneFitVO doPackStoreBenefitInfo(Integer userId) {
        // 线上订阅
        UseBeneFitVO subscribeResult = getSubscribeResult(userId);
        // 实体卡
        UseBeneFitVO cardResult = getCardResult(userId);

        // 优先级pk 1>2>3
        UseBeneFitVO finalResult = new UseBeneFitVO();
        finalResult.setAccessoryBenefit(subscribeResult.getAccessoryBenefit() < cardResult.getAccessoryBenefit() ? subscribeResult.getAccessoryBenefit() : cardResult.getAccessoryBenefit());
        finalResult.setCareBenefit(subscribeResult.getCareBenefit() < cardResult.getCareBenefit() ? subscribeResult.getCareBenefit() : cardResult.getCareBenefit());
        finalResult.setExtendBenefit(subscribeResult.getExtendBenefit() < cardResult.getExtendBenefit() ? subscribeResult.getExtendBenefit() : cardResult.getExtendBenefit());
        finalResult.setCameraDiscount(subscribeResult.getCameraDiscount());
        return finalResult;
    }

    /**
     * 实体卡的权益结果
     *
     * @param userId
     * @return
     */
    private UseBeneFitVO getCardResult(Integer userId) {
        List<CloudStorageCardBenefit> cloudStorageCardBenefits = cloudStorageCardBenefitService.listBenefitByUserId(userId);
        if (CollectionUtils.isEmpty(cloudStorageCardBenefits)) {
            return new UseBeneFitVO(BenefitAppType.expired.getCode());
        }
        List<Long> benefitIds = cloudStorageCardBenefits.stream().map(CloudStorageCardBenefit::getId).collect(Collectors.toList());
        List<CloudStorageCardBenefitDetail> cloudStorageCardBenefitDetails = cloudStorageCardBenefitDetailService.listByBenefitIdTypeAll(benefitIds);
        if (CollectionUtils.isEmpty(cloudStorageCardBenefitDetails)) {
            return new UseBeneFitVO(BenefitAppType.expired.getCode());
        }

        Map<Integer, List<CloudStorageCardBenefitDetail>> benefitDetailMap =
                cloudStorageCardBenefitDetails.stream().collect(Collectors.groupingBy(CloudStorageCardBenefitDetail::getBenefitType));

        UseBeneFitVO useBeneFitVo = new UseBeneFitVO();
        // care权益
        Integer careType = Optional.ofNullable(benefitDetailMap.get(BenefitType.CARE.getType()))
                .map(this::getCardBenefitAppType).orElse(BenefitAppType.expired.getCode());
        useBeneFitVo.setCareBenefit(careType);

        // 延保权益
        Integer extendType = Optional.ofNullable(benefitDetailMap.get(BenefitType.EXTEND.getType()))
                .map(this::getCardBenefitAppType).orElse(BenefitAppType.expired.getCode());
        useBeneFitVo.setExtendBenefit(extendType);

        // 配件八折权益 无
        useBeneFitVo.setAccessoryBenefit(BenefitAppType.expired.getCode());
        return useBeneFitVo;
    }

    /**
     * 比较权益的状态
     *
     * @param benefitDetails
     * @return
     */
    private Integer getCardBenefitAppType(List<CloudStorageCardBenefitDetail> benefitDetails) {
        if (CollectionUtils.isEmpty(benefitDetails)) {
            return BenefitAppType.expired.getCode();
        }

        boolean hasUnusedDetails = benefitDetails.stream().anyMatch(detail -> !detail.getUsed());
        if (!hasUnusedDetails) {
            return BenefitAppType.used.getCode();
        }

        return BenefitAppType.normal.getCode();
    }

    /**
     * 线上订阅权益结果
     *
     * @param userId
     * @return
     */
    private UseBeneFitVO getSubscribeResult(Integer userId) {
        // 查询该用户是否存在云存储订阅权益记录
        CloudStorageStoreBenefit storeBenefit = cloudStorageStoreBenefitService.getBenefitByUserId(userId);
        if (Objects.isNull(storeBenefit)) {
            return new UseBeneFitVO(BenefitAppType.expired.getCode());
        }

        // 权益整体过期
        UseBeneFitVO useBeneFitVo = new UseBeneFitVO();
        if (storeBenefit.isExpired()) {
            // 没有云存储订阅权益记录，判断权益是否已过期或者状态为已过期
            return new UseBeneFitVO(BenefitAppType.expired.getCode());
        }

        // care权益
        Integer careType = Optional.ofNullable(cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.CARE.getType()))
                .map(benefitDetail -> getBenefitAppType(benefitDetail.getExpired(), benefitDetail.getUsed())).orElse(BenefitAppType.expired.getCode());
        useBeneFitVo.setCareBenefit(careType);

        // 延保权益
        Integer extendType = Optional.ofNullable(cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.EXTEND.getType()))
                .map(benefitDetail -> getBenefitAppType(benefitDetail.getExpired(), benefitDetail.getUsed())).orElse(BenefitAppType.expired.getCode());
        useBeneFitVo.setExtendBenefit(extendType);

        // 配件折扣权益
        Integer accessoryType = Optional.ofNullable(cloudStorageStoreBenefitDetailService.getStoreBenefitDetail(storeBenefit.getId(), BenefitType.ACCESSORIES_DISCOUNT.getType()))
                .filter(benefitDetail -> !benefitDetail.getExpired()).map(benefitDetail -> BenefitAppType.normal.getCode()).orElse(BenefitAppType.expired.getCode());
        useBeneFitVo.setAccessoryBenefit(accessoryType);

        // 下一台相机优惠
        Integer cameraDiscount = storeBenefit.getNextOneCameraDiscount() && BenefitStatus.ACTIVE.getName().equals(storeBenefit.getStatus()) ?
                BenefitAppType.normal.getCode() : BenefitAppType.expired.getCode();
        useBeneFitVo.setCameraDiscount(cameraDiscount);

        return useBeneFitVo;
    }

    /**
     * 根据过期和使用情况返回权益类型
     *
     * @param expired
     * @param used
     * @return
     */
    public Integer getBenefitAppType(Boolean expired, Boolean used) {
        return expired ? BenefitAppType.expired.getCode() :
                used ? BenefitAppType.used.getCode() : BenefitAppType.normal.getCode();
    }
}
