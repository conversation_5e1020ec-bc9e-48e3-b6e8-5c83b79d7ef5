package com.insta360.store.service.controller.trade.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.TradeCodeGroup;

import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/08/04
 * @Description:
 */
public class DiscountResponseVO {

    private String code;

    @JSONField(name = "code_group")
    private TradeCodeGroup codeGroup;

    @JSONField(name = "code_type")
    private String codeType;

    @JSONField(name = "info_tag")
    private String infoTag;

    /**
     * 优惠码优惠
     */
    private Price fee;

    /**
     * 活动优惠
     */
    private Price activityFee;

    @JSONField(name = "is_free")
    private Boolean isFree;

    private String discountType;

    /**
     * 参与coupon优惠的商品明细
     */
    private List<ItemDiscountInfoVO> itemDiscountInfos;

    /**
     * 绑定优惠券
     */
    private Boolean bindCoupon;

    /**
     * 分销代码
     */
    private String resellerCode;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public TradeCodeGroup getCodeGroup() {
        return codeGroup;
    }

    public void setCodeGroup(TradeCodeGroup codeGroup) {
        this.codeGroup = codeGroup;
    }

    public String getCodeType() {
        return codeType;
    }

    public void setCodeType(String codeType) {
        this.codeType = codeType;
    }

    public String getInfoTag() {
        return infoTag;
    }

    public void setInfoTag(String infoTag) {
        this.infoTag = infoTag;
    }

    public Price getFee() {
        return fee;
    }

    public void setFee(Price fee) {
        this.fee = fee;
    }

    public Boolean getIsFree() {
        return isFree;
    }

    public void setIsFree(Boolean free) {
        isFree = free;
    }

    public String getDiscountType() {
        return discountType;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public Price getActivityFee() {
        return activityFee;
    }

    public void setActivityFee(Price activityFee) {
        this.activityFee = activityFee;
    }

    public List<ItemDiscountInfoVO> getItemDiscountInfos() {
        return itemDiscountInfos;
    }

    public void setItemDiscountInfos(List<ItemDiscountInfoVO> itemDiscountInfos) {
        this.itemDiscountInfos = itemDiscountInfos;
    }

    public Boolean getBindCoupon() {
        return bindCoupon;
    }

    public void setBindCoupon(Boolean bindCoupon) {
        this.bindCoupon = bindCoupon;
    }

    public String getResellerCode() {
        return resellerCode;
    }

    public void setResellerCode(String resellerCode) {
        this.resellerCode = resellerCode;
    }

    @Override
    public String toString() {
        return "DiscountResponseVO{" +
                "code='" + code + '\'' +
                ", codeGroup=" + codeGroup +
                ", codeType='" + codeType + '\'' +
                ", infoTag='" + infoTag + '\'' +
                ", fee=" + fee +
                ", activityFee=" + activityFee +
                ", isFree=" + isFree +
                ", discountType='" + discountType + '\'' +
                ", itemDiscountInfos=" + itemDiscountInfos +
                ", bindCoupon=" + bindCoupon +
                ", resellerCode='" + resellerCode + '\'' +
                '}';
    }
}
