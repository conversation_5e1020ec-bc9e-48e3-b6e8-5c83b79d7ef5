package com.insta360.store.service.rpc.user.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.user.model.EmailSubscribe;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description: 邮件订阅rpc VO
 * @author: py
 * @create: 2022-04-12 15:52
 */
public class EmailSubscribeRpcVO implements Serializable {
    private Integer id;

    private String activityPage;

    private String email;

    private String name;

    private String country;

    private String giftCard;

    private String googleField;

    private Boolean sendEmailRule;

    /**
     * 订阅来源
     */
    private String subscribeOrigin;

    private LocalDateTime createTime;

    private String platform;    // 平台

    private String accountName; // 账户名称

    private String accountLink; // 账户链接

    private String contentDescription;  // 内容描述

    private LocalDateTime updateTime;

    /**
     * 邮件订阅状态的字段
     */
    private String emailSubscribeResult;

    /**
     * 代金券模版失效标记
     */
    private Boolean templateInvalidFlag;

    public EmailSubscribeRpcVO() {
    }

    /**
     * 拷贝到Vo
     *
     * @param emailSubscribe
     */
    public EmailSubscribeRpcVO(EmailSubscribe emailSubscribe) {
        if (emailSubscribe != null) {
            BeanUtil.copyProperties(emailSubscribe, this);
        }
    }

    public EmailSubscribeRpcVO(String emailSubscribeResult) {
        this.emailSubscribeResult = emailSubscribeResult;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityPage() {
        return activityPage;
    }

    public void setActivityPage(String activityPage) {
        this.activityPage = activityPage;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getGiftCard() {
        return giftCard;
    }

    public void setGiftCard(String giftCard) {
        this.giftCard = giftCard;
    }

    public String getGoogleField() {
        return googleField;
    }

    public void setGoogleField(String googleField) {
        this.googleField = googleField;
    }

    public Boolean getSendEmailRule() {
        return sendEmailRule;
    }

    public void setSendEmailRule(Boolean sendEmailRule) {
        this.sendEmailRule = sendEmailRule;
    }

    public String getSubscribeOrigin() {
        return subscribeOrigin;
    }

    public void setSubscribeOrigin(String subscribeOrigin) {
        this.subscribeOrigin = subscribeOrigin;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountLink() {
        return accountLink;
    }

    public void setAccountLink(String accountLink) {
        this.accountLink = accountLink;
    }

    public String getContentDescription() {
        return contentDescription;
    }

    public void setContentDescription(String contentDescription) {
        this.contentDescription = contentDescription;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getEmailSubscribeResult() {
        return emailSubscribeResult;
    }

    public void setEmailSubscribeResult(String emailSubscribeResult) {
        this.emailSubscribeResult = emailSubscribeResult;
    }

    public Boolean getTemplateInvalidFlag() {
        return templateInvalidFlag;
    }

    public void setTemplateInvalidFlag(Boolean templateInvalidFlag) {
        this.templateInvalidFlag = templateInvalidFlag;
    }

    @Override
    public String toString() {
        return "EmailSubscribeRpcVO{" +
                "id=" + id +
                ", activityPage='" + activityPage + '\'' +
                ", email='" + email + '\'' +
                ", name='" + name + '\'' +
                ", country='" + country + '\'' +
                ", giftCard='" + giftCard + '\'' +
                ", googleField='" + googleField + '\'' +
                ", sendEmailRule=" + sendEmailRule +
                ", subscribeOrigin='" + subscribeOrigin + '\'' +
                ", createTime=" + createTime +
                ", platform='" + platform + '\'' +
                ", accountName='" + accountName + '\'' +
                ", accountLink='" + accountLink + '\'' +
                ", contentDescription='" + contentDescription + '\'' +
                ", updateTime=" + updateTime +
                ", emailSubscribeResult='" + emailSubscribeResult + '\'' +
                ", templateInvalidFlag=" + templateInvalidFlag +
                '}';
    }
}
