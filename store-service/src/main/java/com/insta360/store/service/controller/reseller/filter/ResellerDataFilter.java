package com.insta360.store.service.controller.reseller.filter;

import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.reseller.model.ResellerPromoLink;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/05/06
 * @Description:
 */
@Component
public class ResellerDataFilter {

    @Autowired
    ProductService productService;

    /**
     * getLinks接口的新品数据过滤
     *
     * @param promoLinks
     * @return
     */
    public List<ResellerPromoLink> getLinksDataFilter(List<ResellerPromoLink> promoLinks) {
        List<Product> newProducts = productService.listNewProducts(true);
        if (newProducts.isEmpty()) {
            return promoLinks;
        }

        // 数据过滤
        List<Integer> newProductIds = newProducts.stream().map(Product::getId).collect(Collectors.toList());
        return promoLinks.stream().filter(link -> !newProductIds.contains(link.getRelatedProduct())).collect(Collectors.toList());
    }
}
