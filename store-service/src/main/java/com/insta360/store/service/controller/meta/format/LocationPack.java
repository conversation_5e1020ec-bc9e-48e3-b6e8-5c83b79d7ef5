package com.insta360.store.service.controller.meta.format;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.business.meta.model.CountryConfig;
import com.insta360.store.business.meta.model.CountryGroup;
import com.insta360.store.business.meta.service.CountryConfigService;
import com.insta360.store.business.meta.service.CountryGroupService;
import com.insta360.store.business.outgoing.rpc.user.dto.MetaLocation;
import com.insta360.store.business.outgoing.rpc.user.service.MetaLocationService;
import com.insta360.store.service.controller.meta.vo.LocationVO;
import com.insta360.store.service.controller.meta.vo.MetaCountryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2021/05/28
 * @Description:
 */
@Component
public class LocationPack {

    @Autowired
    MetaLocationService metaLocationService;

    @Autowired
    CountryGroupService countryGroupService;

    @Autowired
    CountryConfigService countryConfigService;

    /**
     * 封装所有的国家信息
     *
     * @return
     */
    public Map<String, List<MetaCountryVO>> doPackCountries() {
        Map<String, List<MetaCountryVO>> resultMap = new LinkedHashMap<>();
        List<CountryGroup> countryGroups = countryGroupService.listCountryGroups();
        countryGroups.forEach(group -> {
            List<CountryConfig> countryConfigs = countryConfigService.listByGroup(group.getId());
            List<MetaCountryVO> items = countryConfigs.stream().map(country -> {
                Currency currency = country.currency();
                MetaCountryVO countryVO = new MetaCountryVO();
                countryVO.setName(country.getCountryText());
                countryVO.setLanguage(country.getLanguage());
                countryVO.setLanguageText(country.getLanguageText());
                countryVO.setCurrency(currency.name());
                countryVO.setCurrencySignal(currency.getSignal());
                countryVO.setCode(country.getCountryCode());
                countryVO.setPhoneCode(country.getPhoneCode());
                return countryVO;
            }).collect(Collectors.toList());

            if (!items.isEmpty()) {
                resultMap.put(group.getId(), items);
            }
        });

        return resultMap;
    }

    /**
     * 缓存国家的省市信息
     *
     * @param country
     * @return
     */
    public JSONArray doPackLocations(InstaCountry country) {
        Response<List<MetaLocation>> locationMap = metaLocationService.getCountryLocationMap(country.name());
        List<MetaLocation> locations = locationMap.getData();
        if (locations == null || locations.isEmpty()) {
            return null;
        }

        LocationVO rootNode = new LocationVO();
        this.packSubLocations(rootNode, buildLocationMap(locations));
        LocationVO location = rootNode.getLocationNodes().get(0);
        return this.packLocationNodes(location.getLocationNodes());
    }

    /**
     * 构造数据接口
     *
     * @param locations
     * @return
     */
    private Map<Integer, List<MetaLocation>> buildLocationMap(List<MetaLocation> locations) {
        Map<Integer, List<MetaLocation>> locationMap = new HashMap<>();
        locations.forEach(location -> {
            int parentId = location.getParentId();
            List<MetaLocation> list = locationMap.computeIfAbsent(parentId, k -> new ArrayList<>());
            list.add(location);
        });

        return locationMap;
    }

    /**
     * 返回数据封装
     *
     * @param nodes
     * @return
     */
    public JSONArray packLocationNodes(List<LocationVO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return new JSONArray();
        }

        JSONArray array = new JSONArray();
        for (LocationVO node : nodes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", node.getMetaLocationNode().getName());
            jsonObject.put("name_zh", node.getMetaLocationNode().getNameZh());
            jsonObject.put("sub", packLocationNodes(node.getLocationNodes()));

            array.add(jsonObject);
        }

        return array;
    }

    /**
     * 封装省份/城市信息
     *
     * @param location
     * @param locationMap
     */
    private void packSubLocations(LocationVO location, Map<Integer, List<MetaLocation>> locationMap) {
        MetaLocation parentLocation = location.getMetaLocationNode();
        Integer parentId = parentLocation == null ? 0 : parentLocation.getId();

        List<MetaLocation> list = locationMap.get(parentId);
        if (list == null) {
            return;
        }

        List<LocationVO> nodeList = new ArrayList<>();
        for (MetaLocation metaLocation : list) {
            LocationVO locationVO = new LocationVO();
            locationVO.setMetaLocationNode(metaLocation);
            this.packSubLocations(locationVO, locationMap);

            nodeList.add(locationVO);
        }
        location.setLocationNodes(nodeList);
    }
}
