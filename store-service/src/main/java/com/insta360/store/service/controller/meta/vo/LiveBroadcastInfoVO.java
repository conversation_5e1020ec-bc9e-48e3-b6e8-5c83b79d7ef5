package com.insta360.store.service.controller.meta.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/10/25
 * @Description:
 */
public class LiveBroadcastInfoVO implements Serializable {

    /**
     * 地区
     */
    private List<String> countries;

    /**
     * 语言
     */
    private String language;

    /**
     * 标题
     */
    private String title;

    public LiveBroadcastInfoVO() {
    }

    public List<String> getCountries() {
        return countries;
    }

    public void setCountries(List<String> countries) {
        this.countries = countries;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return "LiveBroadcastInfoVO{" +
                "countries=" + countries +
                ", language='" + language + '\'' +
                ", title='" + title + '\'' +
                '}';
    }
}
