package com.insta360.store.service.rpc.order.vo;

import java.util.List;

/**
 * @Author: wkx
 * @Date: 2021/11/23
 * @Description:
 */
public class OrderVO {

    /**
     * 订单支付数据
     */
    private OrderPaymentVO orderPayment;

    /**
     * 订单数据
     */
    private List<OrderInfoVO> order;

    /**
     * 优惠码
     */
    private String couponCode;

    /**
     * 商城单号
     */
    private String storeOrderNumber;

    public OrderPaymentVO getOrderPayment() {
        return orderPayment;
    }

    public void setOrderPayment(OrderPaymentVO orderPayment) {
        this.orderPayment = orderPayment;
    }

    public List<OrderInfoVO> getOrder() {
        return order;
    }

    public void setOrder(List<OrderInfoVO> order) {
        this.order = order;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getStoreOrderNumber() {
        return storeOrderNumber;
    }

    public void setStoreOrderNumber(String storeOrderNumber) {
        this.storeOrderNumber = storeOrderNumber;
    }

    @Override
    public String toString() {
        return "OrderVO{" +
                "orderPayment=" + orderPayment +
                ", order=" + order +
                ", couponCode='" + couponCode + '\'' +
                ", storeOrderNumber='" + storeOrderNumber + '\'' +
                '}';
    }
}
