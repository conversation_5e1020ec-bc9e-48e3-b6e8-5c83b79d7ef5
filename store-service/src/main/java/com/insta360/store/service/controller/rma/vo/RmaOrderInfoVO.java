package com.insta360.store.service.controller.rma.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 售后单VO
 * @Date 2024/2/27
 */
public class RmaOrderInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 售后单号
     */
    private String rmaNumber;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 售后类型
     * @see com.insta360.store.business.rma.enums.RmaType
     */
    private String rmaType;

    /**
     * 售后数量
     */
    private Integer quantity;

    /**
     * 用户退款原因
     */
    private String refundReason;

    /**
     * 用户退款说明
     */
    private String extraReason;

    /**
     * 售后申请时间
     */
    private LocalDateTime refundApplyTime;

    /**
     * 退款币种
     */
    private String refundCurrency;

    /**
     * 总退款金额
     */
    private BigDecimal totalRefundAmount;

    /**
     * 售后状态
     * @see com.insta360.store.business.rma.enums.RmaState
     */
    private Integer rmaState;

    /**
     * 原状态（当rmaState=（-1，-2）时才会记录上一个状态）
     * @see  com.insta360.store.business.rma.enums.RmaState
     */
    private Integer formerState;

    /**
     * 售后商品信息
     */
    private RmaItemVO orderItem;

    /**
     * 售后单物流信息
     */
    private RmaOrderLogisticsVO rmaOrderLogistics;


    public String getRmaNumber() {
        return rmaNumber;
    }

    public void setRmaNumber(String rmaNumber) {
        this.rmaNumber = rmaNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    public String getExtraReason() {
        return extraReason;
    }

    public void setExtraReason(String extraReason) {
        this.extraReason = extraReason;
    }

    public LocalDateTime getRefundApplyTime() {
        return refundApplyTime;
    }

    public void setRefundApplyTime(LocalDateTime refundApplyTime) {
        this.refundApplyTime = refundApplyTime;
    }

    public String getRefundCurrency() {
        return refundCurrency;
    }

    public void setRefundCurrency(String refundCurrency) {
        this.refundCurrency = refundCurrency;
    }

    public BigDecimal getTotalRefundAmount() {
        return totalRefundAmount;
    }

    public void setTotalRefundAmount(BigDecimal totalRefundAmount) {
        this.totalRefundAmount = totalRefundAmount;
    }

    public RmaItemVO getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(RmaItemVO orderItem) {
        this.orderItem = orderItem;
    }

    public RmaOrderLogisticsVO getRmaOrderLogistics() {
        return rmaOrderLogistics;
    }

    public void setRmaOrderLogistics(RmaOrderLogisticsVO rmaOrderLogistics) {
        this.rmaOrderLogistics = rmaOrderLogistics;
    }

    public String getRmaType() {
        return rmaType;
    }

    public void setRmaType(String rmaType) {
        this.rmaType = rmaType;
    }

    public Integer getRmaState() {
        return rmaState;
    }

    public void setRmaState(Integer rmaState) {
        this.rmaState = rmaState;
    }

    public Integer getFormerState() {
        return formerState;
    }

    public void setFormerState(Integer formerState) {
        this.formerState = formerState;
    }

    @Override
    public String toString() {
        return "RmaOrderInfoVO{" +
                "rmaNumber='" + rmaNumber + '\'' +
                ", orderNumber='" + orderNumber + '\'' +
                ", rmaType='" + rmaType + '\'' +
                ", quantity=" + quantity +
                ", refundReason='" + refundReason + '\'' +
                ", extraReason='" + extraReason + '\'' +
                ", refundApplyTime=" + refundApplyTime +
                ", refundCurrency='" + refundCurrency + '\'' +
                ", totalRefundAmount=" + totalRefundAmount +
                ", rmaState=" + rmaState +
                ", formerState=" + formerState +
                ", orderItem=" + orderItem +
                ", rmaOrderLogistics=" + rmaOrderLogistics +
                '}';
    }
}
