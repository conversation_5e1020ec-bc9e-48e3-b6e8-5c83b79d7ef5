package com.insta360.store.service.controller.faq.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.faq.model.FaqCategoryQuestionInfo;

import java.io.Serializable;

/**
 * @Author: wkx
 * @Date: 2022/4/27
 * @Description:
 */
public class FaqQuestionQaBindVO implements Serializable {

    /**
     * 内部分类问题id
     */
    private Integer categoryQuestionInsideId;

    /**
     * 语言
     */
    private String language;

    /**
     * 地区
     */
    private String area;

    /**
     * 问题
     */
    private String question;

    /**
     * 回答
     */
    private String answer;

    /**
     * 排序
     */
    private Integer orderIndex;

    public FaqQuestionQaBindVO() {
    }

    public FaqQuestionQaBindVO(FaqCategoryQuestionInfo questionInfo) {
        if (questionInfo != null) {
            BeanUtil.copyProperties(questionInfo, this);
        }
    }

    public Integer getCategoryQuestionInsideId() {
        return categoryQuestionInsideId;
    }

    public void setCategoryQuestionInsideId(Integer categoryQuestionInsideId) {
        this.categoryQuestionInsideId = categoryQuestionInsideId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }
}
