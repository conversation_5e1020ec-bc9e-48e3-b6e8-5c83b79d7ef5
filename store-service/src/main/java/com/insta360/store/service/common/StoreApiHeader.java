package com.insta360.store.service.common;

/**
 * @Author: hyc
 * @Date: 2019-08-15
 * @Description:
 */
public enum StoreApiHeader {

    // 商城用户token
    StoreToken("X-Store-Token"),

    // 商城后台管理员工工号
    JobNumber("X-Job-Number"),

    // 分销码
    ResellerCode("X-insrc"),

    // 限时推广分销码
    ResellerFlashPromoCode("X-insfpc"),

    // 用户应用信息
    UserAgent("user-agent");

    public static StoreApiHeader parse(String s) {
        for (StoreApiHeader apiHeader : values()) {
            if (apiHeader.key.equalsIgnoreCase(s)) {
                return apiHeader;
            }
        }
        return null;
    }

    private String key;

    StoreApiHeader(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}
