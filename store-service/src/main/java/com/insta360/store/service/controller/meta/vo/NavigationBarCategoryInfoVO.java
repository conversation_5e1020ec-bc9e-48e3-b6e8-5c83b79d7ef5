package com.insta360.store.service.controller.meta.vo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wkx
 * @Date: 2022/6/1
 * @Description:
 */
public class NavigationBarCategoryInfoVO implements Serializable {

    /**
     * 产品套餐信息
     */
    private List<NavigationBarCommodityInfoVO> commodityInfos;

    /**
     * 导航栏信息
     */
    private List<NavigationBarCategoryVO> navigationBarInfos;

    public List<NavigationBarCommodityInfoVO> getCommodityInfos() {
        return commodityInfos;
    }

    public void setCommodityInfos(List<NavigationBarCommodityInfoVO> commodityInfos) {
        this.commodityInfos = commodityInfos;
    }

    public List<NavigationBarCategoryVO> getNavigationBarInfos() {
        return navigationBarInfos;
    }

    public void setNavigationBarInfos(List<NavigationBarCategoryVO> navigationBarInfos) {
        this.navigationBarInfos = navigationBarInfos;
    }

    @Override
    public String toString() {
        return "NavigationBarCategoryInfoVO{" +
                "commodityInfos=" + commodityInfos +
                ", navigationBarInfos=" + navigationBarInfos +
                '}';
    }
}
