package com.insta360.store.service.rpc.insurance.controller;

import com.insta360.compass.core.exception.CommonErrorCode;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.commodity.enums.ServiceType;
import com.insta360.store.business.insurance.bo.InsuranceSwitchBindingResultBO;
import com.insta360.store.business.insurance.dto.CareActivationCardDTO;
import com.insta360.store.business.insurance.dto.InsuranceSwitchBindingDTO;
import com.insta360.store.business.insurance.enums.InsuranceRebindResponseStatus;
import com.insta360.store.business.insurance.exception.InsuranceErrorCode;
import com.insta360.store.business.insurance.model.*;
import com.insta360.store.business.insurance.service.*;
import com.insta360.store.business.insurance.service.impl.fatory.InsuranceFactory;
import com.insta360.store.business.insurance.service.impl.handler.BaseInsuranceHandler;
import com.insta360.store.business.insurance.service.impl.helper.http.DeviceInfoHelper;
import com.insta360.store.business.outgoing.rpc.app.dto.DeviceInfo;
import com.insta360.store.service.rpc.insurance.format.ExtendInsuranceResultPack;
import com.insta360.store.service.rpc.insurance.format.InsurancePack;
import com.insta360.store.service.rpc.insurance.response.InsuranceSwitchBindingResponse;
import com.insta360.store.service.rpc.insurance.vo.ExtendInsuranceVO;
import com.insta360.store.service.rpc.insurance.vo.InsuranceServiceVO;
import com.insta360.store.service.rpc.insurance.vo.InsuranceVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wbt
 * @Date: 2021/05/31
 * @Description:
 */
@RestController
public class RepairInsuranceServiceApi {

    @Autowired
    CareInsuranceService careInsuranceService;

    @Autowired
    ExtendInsuranceService extendInsuranceService;

    @Autowired
    CareInsuranceActivationCardService careInsuranceActivationCardService;

    @Autowired
    DeviceInfoHelper deviceInfoHelper;

    @Autowired
    ExtendInsuranceResultPack extendInsuranceResultPack;

    @Autowired
    InsurancePack insurancePack;

    @Autowired
    CarePlusInsuranceService carePlusInsuranceService;

    @Autowired
    InsuranceFactory insuranceFactory;

    @Autowired
    InsuranceOperationRecordService insuranceOperationRecordService;

    /**
     * 根据序列号查询care信息（工单）
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/rpc/store/service/insurance/care/getRepairInsuranceBySerial")
    public Response<InsuranceServiceVO> getRepairInsuranceBySerial(@RequestParam(value = "deviceSerial") String deviceSerial) {
        InsuranceServiceVO repairInsuranceVO;

        CareInsurance careInsurance = careInsuranceService.getRepairBySerial(deviceSerial);
        if (careInsurance != null) {
            DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(careInsurance.getDeviceSerial());
            if (deviceInfo == null || !deviceInfo.getDeviceType().equals(careInsurance.getDeviceType())) {
                return Response.failed(-1, "增值服务绑定机型和序列号记性不匹配");
            }
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setCareType("care");
            repairInsuranceVO.setInsuranceType(careInsurance.getInsuranceType());
            repairInsuranceVO.setInsuranceNumber(careInsurance.getInsuranceNumber());
            return Response.ok(repairInsuranceVO);
        }

        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getRepairBySerial(deviceSerial);
        if (activationCard != null) {
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setCareType("care_card");
            repairInsuranceVO.setInsuranceType(activationCard.getInsuranceType());
            repairInsuranceVO.setInsuranceNumber(activationCard.getInsuranceNumber());
            return Response.ok(repairInsuranceVO);
        }

        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getRepairBySerial(deviceSerial);
        if (carePlusInsurance != null) {
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setCareType("care_plus");
            repairInsuranceVO.setInsuranceType(carePlusInsurance.getInsuranceType());
            repairInsuranceVO.setInsuranceNumber(carePlusInsurance.getInsuranceNumber());
            return Response.ok(repairInsuranceVO);
        }
        return Response.failed(-1, "未找到该序列号对应的保险服务");
    }

    /**
     * 根据序列号查询保险信息（工单）
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/rpc/store/service/insurance/care/getInsuranceBySerial")
    public Response getInsuranceBySerial(@RequestParam(value = "deviceSerial") String deviceSerial) {
        InsuranceServiceVO repairInsuranceVO;

        CareInsurance careInsurance = careInsuranceService.getCareInsurance(deviceSerial);
        if (careInsurance != null) {
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setCareType("care");
            repairInsuranceVO.setInsuranceType(careInsurance.getInsuranceType());
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setDeviceSerial(careInsurance.getDeviceSerial());
            repairInsuranceVO.setInsuranceNumber(careInsurance.getInsuranceNumber());
            repairInsuranceVO.setOrderNumber(careInsurance.getOrderNumber());
            repairInsuranceVO.setDeviceType(careInsurance.getDeviceType());
            repairInsuranceVO.setBindTime(careInsurance.getBindTime());
            repairInsuranceVO.setExpireTime(careInsurance.getExpireTime());
            repairInsuranceVO.setCreateTime(careInsurance.getCreateTime());
            repairInsuranceVO.setUseTime(careInsurance.getUseTime());
            return Response.ok(repairInsuranceVO);
        }

        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
        if (activationCard != null) {
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setCareType("care_card");
            repairInsuranceVO.setInsuranceType(activationCard.getInsuranceType());
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setDeviceSerial(activationCard.getDeviceSerial());
            repairInsuranceVO.setInsuranceNumber(activationCard.getInsuranceNumber());
            repairInsuranceVO.setDeviceType(activationCard.getDeviceType());
            repairInsuranceVO.setBindTime(activationCard.getBindTime());
            repairInsuranceVO.setExpireTime(activationCard.getExpireTime());
            repairInsuranceVO.setCreateTime(activationCard.getCreateTime());
            repairInsuranceVO.setUseTime(activationCard.getUseTime());
            repairInsuranceVO.setRemainingUsageCount(activationCard.getRemainingUsageCount());
            return Response.ok(repairInsuranceVO);
        }

        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(deviceSerial);
        if (carePlusInsurance != null) {
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setCareType("care_plus");
            repairInsuranceVO.setInsuranceType(carePlusInsurance.getInsuranceType());
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setDeviceSerial(carePlusInsurance.getDeviceSerial());
            repairInsuranceVO.setInsuranceNumber(carePlusInsurance.getInsuranceNumber());
            repairInsuranceVO.setDeviceType(carePlusInsurance.getDeviceType());
            repairInsuranceVO.setBindTime(carePlusInsurance.getBindTime());
            repairInsuranceVO.setExpireTime(carePlusInsurance.getExpireTime());
            repairInsuranceVO.setCreateTime(carePlusInsurance.getCreateTime());
            repairInsuranceVO.setRemainingUsageCount(carePlusInsurance.getRemainingUsageCount());
            return Response.ok(repairInsuranceVO);
        }
        return Response.failed(-1, "未找到该序列号对应的保险服务");
    }

    /**
     * 使用Care保险服务（工单）
     *
     * @param careActivationCardParam
     */
    @PostMapping(path = "/rpc/store/service/insurance/care/useInsuranceService")
    public Response<Object> useCareInsuranceService(@RequestBody CareActivationCardDTO careActivationCardParam) {
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(ServiceType.care.name());
        insuranceHandler.useVirtualService(careActivationCardParam.getDeviceSerial(), careActivationCardParam.getUseTime());
        return Response.ok();
    }

    /**
     * 使用Care+保险服务（工单）
     *
     * @param careActivationCardParam
     */
    @PostMapping(path = "/rpc/store/service/insurance/carePlus/useInsuranceService")
    public Response<Object> useCarePlusInsurance(@RequestBody CareActivationCardDTO careActivationCardParam) {
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(ServiceType.care_plus.name());
        insuranceHandler.useVirtualService(careActivationCardParam.getDeviceSerial(), careActivationCardParam.getUseTime());
        return Response.ok();
    }

    /**
     * 使用care实体卡保险服务（工单）
     *
     * @param careActivationCardParam
     */
    @PostMapping(path = "/rpc/store/service/insurance/card/useInsuranceService")
    public Response<Object> useCardInsuranceService(@RequestBody CareActivationCardDTO careActivationCardParam) {
        CareInsuranceActivationCard careInsuranceActivationCard = careInsuranceActivationCardService.getByDeviceSerial(careActivationCardParam.getDeviceSerial());
        if (careInsuranceActivationCard == null) {
            throw new InstaException(CommonErrorCode.InsuranceNotFound);
        }
        String insuranceType = careInsuranceActivationCard.getInsuranceType();
        BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(insuranceType);
        insuranceHandler.useCard(careInsuranceActivationCard, careActivationCardParam.getUseTime());
        return Response.ok();
    }

    /**
     * 根据序列号获取延保服务（工单）
     *
     * @param deviceSerial
     */
    @GetMapping("/rpc/store/service/insurance/extend/getRepairBySerial")
    public Response<ExtendInsuranceVO> getRepairBySerial(@RequestParam(value = "deviceSerial") String deviceSerial) {
        ExtendInsurance extendInsurance = extendInsuranceService.getRepairBySerial(deviceSerial);

        if (extendInsurance != null) {
            DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(extendInsurance.getDeviceSerial());
            if (deviceInfo == null || !deviceInfo.getDeviceType().equals(extendInsurance.getDeviceType())) {
                return Response.failed();
            }
        }
        return Response.ok(new ExtendInsuranceVO(extendInsurance));
    }

    /**
     * 根据序列号和地区获取延保服务（工单）
     *
     * @param deviceSerial
     * @param area
     */
    @GetMapping("/rpc/store/service/insurance/extend/getRepairBySerialArea")
    public Response<ExtendInsuranceVO> getRepairBySerial(@RequestParam(value = "deviceSerial") String deviceSerial, @RequestParam(value = "area") String area) {
        if (StringUtil.isBlank(deviceSerial) || StringUtil.isBlank(area)) {
            Response.failed();
        }
        ExtendInsuranceVO extendInsuranceVO = extendInsuranceResultPack.packExtendInsuranceBySerial(deviceSerial, area);
        if (extendInsuranceVO == null) {
            Response.failed("rpc查询序列号失败");
        }
        return Response.ok(extendInsuranceVO);
    }

    /**
     * 根据保险号判断保险类型（工单）
     *
     * @param insuranceNumber
     */
    @GetMapping("/rpc/store/service/insurance/care/getByRepairInsuranceNumber")
    public Response<InsuranceServiceVO> getRepairInsuranceByNumber(@RequestParam(value = "insuranceNumber") String insuranceNumber) {
        InsuranceServiceVO repairInsuranceVO;

        CareInsurance careInsurance = careInsuranceService.getRepairByInsuranceNumber(insuranceNumber);
        if (careInsurance != null) {
            DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(careInsurance.getDeviceSerial());
            if (deviceInfo == null || !deviceInfo.getDeviceType().equals(careInsurance.getDeviceType())) {
                return Response.failed();
            }
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setCareType("care");
            repairInsuranceVO.setInsuranceType(careInsurance.getInsuranceType());
            repairInsuranceVO.setDeviceSerial(careInsurance.getDeviceSerial());
            repairInsuranceVO.setInsuranceNumber(careInsurance.getInsuranceNumber());
            return Response.ok(repairInsuranceVO);
        }

        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getRepairByInsuranceNumber(insuranceNumber);
        if (activationCard != null) {
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setCareType("care_card");
            repairInsuranceVO.setInsuranceType(activationCard.getInsuranceType());
            repairInsuranceVO.setDeviceSerial(activationCard.getDeviceSerial());
            repairInsuranceVO.setInsuranceNumber(activationCard.getInsuranceNumber());
            return Response.ok(repairInsuranceVO);
        }

        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getRepairByInsuranceNumber(insuranceNumber);
        if (carePlusInsurance != null) {
            repairInsuranceVO = new InsuranceServiceVO();
            repairInsuranceVO.setIsCare(true);
            repairInsuranceVO.setCareType("care_plus");
            repairInsuranceVO.setInsuranceType(carePlusInsurance.getInsuranceType());
            repairInsuranceVO.setDeviceSerial(carePlusInsurance.getDeviceSerial());
            repairInsuranceVO.setInsuranceNumber(carePlusInsurance.getInsuranceNumber());
            return Response.ok(repairInsuranceVO);
        }
        return Response.failed(-1, "无效的保险号");
    }

    /**
     * 根据保险号获取延保服务（工单）
     *
     * @param insuranceNumber
     */
    @GetMapping("/rpc/store/service/insurance/extend/getByRepairInsuranceNumber")
    public Response<ExtendInsuranceVO> getRepairByInsuranceNumber(@RequestParam(value = "insuranceNumber") String insuranceNumber) {
        ExtendInsurance extendInsurance = extendInsuranceService.getByInsuranceNumber(insuranceNumber);

        if (extendInsurance != null) {
            DeviceInfo deviceInfo = deviceInfoHelper.getDeviceInfo(extendInsurance.getDeviceSerial());
            if (deviceInfo == null || !deviceInfo.getDeviceType().equals(extendInsurance.getDeviceType())) {
                return Response.failed();
            }
        }
        return Response.ok(new ExtendInsuranceVO(extendInsurance));
    }

    /**
     * 根据序列号查询增值服务（工单）
     *
     * @param deviceSerial
     */
    @GetMapping("/rpc/store/service/insurance/getByDeviceSerial")
    public Response<InsuranceVO> getByDeviceSerial(@RequestParam String deviceSerial,
                                                   @RequestParam String area) {
        InsuranceVO insurance = insurancePack.packInsurance(deviceSerial, area);
        return Response.ok(insurance);
    }

    /**
     * 增值服务自动换绑
     *
     * @param insuranceRebindParams
     * @return
     */
    @PostMapping("/rpc/store/service/insurance/switchBinding")
    public Response<Object> switchBinding(@RequestBody @Validated InsuranceSwitchBindingDTO insuranceRebindParams) {
        String oldDeviceSerial = insuranceRebindParams.getOldDeviceSerial();
        String newDeviceSerial = insuranceRebindParams.getNewDeviceSerial();
        // 新旧设备序列号⼀致，无需换绑
        if (newDeviceSerial.equals(oldDeviceSerial)) {
            throw new InstaException(InsuranceErrorCode.SameDeviceNoNeedToRebindException);
        }

        // 校验设备序列号
        checkDeviceSerial(oldDeviceSerial);
        checkDeviceSerial(newDeviceSerial);

        // 旧机的有效服务
        List<ServiceType> oldDeviceBoundServiceTypes = getOldDeviceAvailableServiceTypes(oldDeviceSerial);
        // 校验旧机已绑定的增值服务
        if (CollectionUtils.isEmpty(oldDeviceBoundServiceTypes)) {
            throw new InstaException(InsuranceErrorCode.NoValidInsuranceToRebindException);
        }
        // 新机已启用的服务
        List<ServiceType> newDeviceBoundServiceTypes = getNewDeviceEnabledServiceTypes(newDeviceSerial);

        // 换绑结果
        List<InsuranceSwitchBindingResponse.Service> switchBindingResponses = new ArrayList<>();
        List<InsuranceSwitchBindingResultBO> rebindResults = new ArrayList<>();

        // 需要换绑的增值服务类型
        List<ServiceType> needSwitchBindingServiceTypes = getNeedSwitchBindingServiceTypes(oldDeviceBoundServiceTypes, newDeviceBoundServiceTypes, switchBindingResponses, rebindResults);

        // 将已有的服务换绑到新的序列号上
        for (ServiceType serviceType : needSwitchBindingServiceTypes) {
            BaseInsuranceHandler insuranceHandler = insuranceFactory.getInsuranceHandler(serviceType.name());
            // 每项已有的增值服务进行换绑
            InsuranceSwitchBindingResultBO rebindResult = insuranceHandler.switchBinding(oldDeviceSerial, newDeviceSerial);
            rebindResults.add(rebindResult);
            switchBindingResponses.add(new InsuranceSwitchBindingResponse.Service(serviceType.name(), rebindResult.getResult()));
        }

        // 解析最终响应结果
        InsuranceRebindResponseStatus responseStatus = InsuranceSwitchBindingResultBO.parse(rebindResults);
        InsuranceSwitchBindingResponse response = new InsuranceSwitchBindingResponse(responseStatus.getStatus(), switchBindingResponses);
        // 成功换绑记录日志
        if (InsuranceRebindResponseStatus.SUCCESS.getStatus().equals(response.getStatus())) {
            InsuranceOperationRecord insuranceOperationRecord = new InsuranceOperationRecord();
            insuranceOperationRecord.setOperationType(insuranceRebindParams.getOperationType());
            insuranceOperationRecord.setSerialFrom(oldDeviceSerial);
            insuranceOperationRecord.setSerialTo(newDeviceSerial);
            insuranceOperationRecord.setOperationReason(insuranceRebindParams.getOperationReason());
            insuranceOperationRecordService.save(insuranceOperationRecord);
        }
        return Response.ok(response);
    }

    /**
     * 检查设备序列号
     *
     * @param deviceSerial
     */
    private void checkDeviceSerial(String deviceSerial) {
        DeviceInfo oldDeviceInfo = deviceInfoHelper.getDeviceInfo(deviceSerial);
        if (oldDeviceInfo == null) {
            String msgError = String.format(InsuranceErrorCode.InvalidDeviceSerial + "：%s", deviceSerial);
            throw new InstaException(InsuranceErrorCode.InvalidDeviceSerial.getCode(), msgError);
        }
    }

    /**
     * 获取旧设备可用的增值服务类型
     *
     * @param deviceSerial 设备序列号
     * @return 已绑定的可用的增值服务类型列表
     */
    private List<ServiceType> getOldDeviceAvailableServiceTypes(String deviceSerial) {
        List<ServiceType> serviceTypes = new ArrayList<>();
        // 设备已绑定的当前可用的增值服务
        CareInsurance careInsurance = careInsuranceService.getCareInsurance(deviceSerial);
        if (careInsurance != null && careInsurance.serviceStillAvailable()) {
            serviceTypes.add(ServiceType.care);
        }
        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
        if (activationCard != null && activationCard.serviceStillAvailable()) {
            serviceTypes.add(ServiceType.care_card);
        }
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(deviceSerial);
        if (carePlusInsurance != null && carePlusInsurance.serviceStillAvailable()) {
            serviceTypes.add(ServiceType.care_plus);
        }
        ExtendInsurance extendInsurance = extendInsuranceService.getBySerial(deviceSerial);
        if (extendInsurance != null && extendInsurance.serviceStillAvailable()) {
            serviceTypes.add(ServiceType.extend);
        }
        return serviceTypes;
    }

    /**
     * 获取新设备启用的增值服务类型
     * 新设备只需要考虑启用状态即可
     * 存在enabled的服务类型，即认为旧机此类服务不必换绑
     * 与后台原有的更改序列号功能保持一致
     *
     * @param deviceSerial 设备序列号
     * @return 已绑定的enabled增值服务类型列表
     */
    private List<ServiceType> getNewDeviceEnabledServiceTypes(String deviceSerial) {
        List<ServiceType> serviceTypes = new ArrayList<>();
        CareInsurance careInsurance = careInsuranceService.getCareInsurance(deviceSerial);
        if (careInsurance != null) {
            serviceTypes.add(ServiceType.care);
        }
        CareInsuranceActivationCard activationCard = careInsuranceActivationCardService.getByDeviceSerial(deviceSerial);
        if (activationCard != null) {
            serviceTypes.add(ServiceType.care_card);
        }
        CarePlusInsurance carePlusInsurance = carePlusInsuranceService.getByDeviceSerial(deviceSerial);
        if (carePlusInsurance != null) {
            serviceTypes.add(ServiceType.care_plus);
        }
        ExtendInsurance extendInsurance = extendInsuranceService.getBySerial(deviceSerial);
        if (extendInsurance != null) {
            serviceTypes.add(ServiceType.extend);
        }
        return serviceTypes;
    }

    /**
     * 获取需要换绑的服务类型
     * 新旧服务无冲突，才可换绑
     * 有冲突需记录该项为失败
     *
     * @param oldDeviceBoundServiceTypes
     * @param newDeviceBoundServiceTypes
     * @param switchBindingResponses
     * @param rebindResults
     * @return
     */
    private List<ServiceType> getNeedSwitchBindingServiceTypes(List<ServiceType> oldDeviceBoundServiceTypes, List<ServiceType> newDeviceBoundServiceTypes,
                                                               List<InsuranceSwitchBindingResponse.Service> switchBindingResponses, List<InsuranceSwitchBindingResultBO> rebindResults) {
        // 新机无任何增值服务，旧机的服务全部换绑
        if (CollectionUtils.isEmpty(newDeviceBoundServiceTypes)) {
            return oldDeviceBoundServiceTypes;
        }
        List<ServiceType> needSwitchBindingServiceTypes = new ArrayList<>();
        // 遍历旧设备已绑定的增值服务类型
        for (ServiceType oldServiceType : oldDeviceBoundServiceTypes) {
            // 判断旧服务类型是否与新服务类型冲突
            if (isConflictingServiceType(oldServiceType, newDeviceBoundServiceTypes)) {
                // 冲突则记录冲突项，并设置结果为 false
                InsuranceSwitchBindingResultBO rebindResult = new InsuranceSwitchBindingResultBO(Boolean.FALSE, InsuranceRebindResponseStatus.FAILURE.getStatus());
                rebindResults.add(rebindResult);
                switchBindingResponses.add(new InsuranceSwitchBindingResponse.Service(oldServiceType.name(), rebindResult.getResult()));
            } else {
                // 不冲突添加，后面进行换绑操作
                needSwitchBindingServiceTypes.add(oldServiceType);
            }
        }
        return needSwitchBindingServiceTypes;
    }

    /**
     * 判断新旧主机所含增值服务是否冲突
     *
     * @param oldServiceType             旧主机的服务类型
     * @param newDeviceBoundServiceTypes 新主机的服务类型列表
     * @return 如果存在冲突返回 true，否则返回 false
     */
    private boolean isConflictingServiceType(ServiceType oldServiceType, List<ServiceType> newDeviceBoundServiceTypes) {
        // 互相冲突的保险类型集合
        List<ServiceType> conflictingServiceTypes = ServiceType.getConflictingServiceTypes();
        // 判断旧机服务类型是否在冲突列表中，并且新机服务类型列表中存在服务类型在冲突列表中
        boolean isOldServiceCare = conflictingServiceTypes.contains(oldServiceType);
        boolean isNewServiceCareConflicting = newDeviceBoundServiceTypes.stream().anyMatch(conflictingServiceTypes::contains);

        // 旧机服务类型是否为 extend，并且新机服务类型列表中也绑有 extend
        boolean isOldServiceExtend = ServiceType.extend.equals(oldServiceType);
        boolean isNewServiceExtendConflicting = newDeviceBoundServiceTypes.contains(ServiceType.extend);

        // 返回最终判断结果
        return (isOldServiceCare && isNewServiceCareConflicting) || (isOldServiceExtend && isNewServiceExtendConflicting);
    }
}
