package com.insta360.store.service.config;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: hyc
 * @Date: 2019/1/23
 * @Description:
 */
@Configuration
public class StoreConfiguration {

    @Value("${app.default-language}")
    private String defaultLanguageKey;

    @Value("${app.default-country}")
    private String defaultCountryKey;

    public InstaLanguage getDefaultLanguage() {
        return InstaLanguage.parse(defaultLanguageKey);
    }

    public InstaCountry getDefaultCountry() {
        return InstaCountry.parse(defaultCountryKey);
    }
}
