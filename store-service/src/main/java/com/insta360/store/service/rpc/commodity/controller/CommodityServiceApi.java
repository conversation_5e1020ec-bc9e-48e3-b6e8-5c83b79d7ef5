package com.insta360.store.service.rpc.commodity.controller;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.compass.core.web.api.Response;
import com.insta360.store.business.commodity.dto.RepairCommodityDTO;
import com.insta360.store.business.commodity.enums.SaleState;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.model.CommodityPrice;
import com.insta360.store.business.commodity.model.CommoditySaleState;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.commodity.service.CommodityPriceService;
import com.insta360.store.business.commodity.service.CommoditySaleStateService;
import com.insta360.store.business.meta.bo.Price;
import com.insta360.store.business.meta.enums.Currency;
import com.insta360.store.service.rpc.commodity.vo.PspCommodityInfoVO;
import com.insta360.store.service.rpc.commodity.vo.RepairCommodityInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/01/04
 * @Description:
 */
@RestController
public class CommodityServiceApi {

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityPriceService commodityPriceService;

    @Autowired
    CommoditySaleStateService commoditySaleStateService;

    /**
     * 获取套餐信息（工单页面调用）
     *
     * @param repairCommodityParam
     * @return
     */
    @PostMapping("/rpc/store/service/commodity/getCommodityInfos")
    public Response<? extends Map> getCommodityInfos(@RequestBody RepairCommodityDTO repairCommodityParam) {
        List<Integer> commodityIds = repairCommodityParam.getCommodityIds();
        if (CollectionUtils.isEmpty(commodityIds) || StringUtil.isBlank(repairCommodityParam.getCountry()) || StringUtil.isBlank(repairCommodityParam.getLanguage())) {
            return Response.failed(-1, "必要参数为空！");
        }

        List<RepairCommodityInfoVO> repairCommodityInfoVos = commodityIds.stream().map(commodityId -> {
            RepairCommodityInfoVO repairCommodityInfo = new RepairCommodityInfoVO();
            repairCommodityInfo.setCommodityId(commodityId);

            // 套餐价格信息
            CommodityPrice commodityPrice = commodityPriceService.getPrice(commodityId, InstaCountry.parse(repairCommodityParam.getCountry()));
            if (commodityPrice != null) {
                repairCommodityInfo.setAmount(commodityPrice.getAmount());
                repairCommodityInfo.setCurrency(commodityPrice.getCurrency());
                Currency currency = Currency.parse(commodityPrice.getCurrency());
                if (currency != null) {
                    repairCommodityInfo.setSignal(currency.getSignal());
                }
            }

            // 套餐多语言信息
            CommodityInfo commodityInfo = commodityInfoService.getInfo(commodityId, InstaLanguage.parse(repairCommodityParam.getLanguage()));
            if (commodityInfo != null) {
                repairCommodityInfo.setCommodityName(commodityInfo.getName());
            }

            return repairCommodityInfo;
        }).collect(Collectors.toList());

        return Response.ok("repairCommodityInfos", repairCommodityInfoVos);
    }


    /**
     * 获取套餐信息（psp调用）
     *
     * @param commodityId
     * @param country
     * @param language
     * @return
     */
    @GetMapping("/rpc/store/service/commodity/getPspCommodityInfo")
    public Response<Object> getPspCommodityInfo(@RequestParam Integer commodityId, @RequestParam String country, @RequestParam String language) {
        // 查询套餐可销售地区
        List<CommoditySaleState> commoditySaleStates = commoditySaleStateService.getSaleStates(commodityId);
        if (CollectionUtils.isEmpty(commoditySaleStates)) {
            return Response.ok();
        }

        List<String> counties = commoditySaleStates.stream()
                .filter(saleState -> !SaleState.outOfStockStates().contains(SaleState.parse(saleState.getSaleState())))
                .map(CommoditySaleState::getCountry).collect(Collectors.toList());

        PspCommodityInfoVO pspCommodityInfoVo = new PspCommodityInfoVO();
        pspCommodityInfoVo.setSaleCountries(counties);

        // 套餐多语言
        CommodityInfo commodityInfo = commodityInfoService.getInfoDefaultEnglish(commodityId, InstaLanguage.parse(language));
        if (commodityInfo != null) {
            pspCommodityInfoVo.setCommodityId(commodityInfo.getCommodity());
            pspCommodityInfoVo.setCommodityName(commodityInfo.getName());
        }

        // 套餐价格
        CommodityPrice commodityPrice = commodityPriceService.getAreaPrice(commodityId, country);
        if (commodityPrice != null) {
            Price price = commodityPrice.price();
            pspCommodityInfoVo.setAmount(price.getAmount());
            pspCommodityInfoVo.setCurrency(price.getCurrency().name());
            pspCommodityInfoVo.setSignal(price.getSignal());
        }

        return Response.ok(pspCommodityInfoVo);
    }

    @GetMapping("/rpc/store/service/commodity/testRPC")
    public Response<Object> testRPC() {

        return Response.ok("goodRpc");
    }

}
