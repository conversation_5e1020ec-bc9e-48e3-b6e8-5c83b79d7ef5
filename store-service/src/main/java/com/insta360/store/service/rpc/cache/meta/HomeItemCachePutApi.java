package com.insta360.store.service.rpc.cache.meta;

import com.insta360.compass.core.enums.InstaCountry;
import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.store.service.controller.meta.cache.cacheput.HomeItemCachePutPack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 类目页缓存更新
 * @author: py
 * @create: 2023-10-30 10:39
 */
@RestController
public class HomeItemCachePutApi {

    @Autowired
    HomeItemCachePutPack homeItemCachePutPack;

    /**
     * 按类目商品ID、语言、地区获取类目商品页数据
     *
     * @param homeItemId
     * @param country
     * @param language
     */
    @GetMapping("/rpc/store/service/cacheput/meta/hi/listHomeItemInfoByHomeItemType")
    public void listHomeItemInfoByHomeItemType(@RequestParam Integer homeItemId, @RequestParam InstaCountry country, @RequestParam InstaLanguage language) {
        homeItemCachePutPack.doPackCacheHomeItemInfo(homeItemId, language, country);
    }
}
