package com.insta360.store.service.controller.meta.cache.cacheput;

import com.insta360.store.business.configuration.cache.type.CacheableType;
import com.insta360.store.service.controller.meta.format.LiveBroadcastPack;
import com.insta360.store.service.controller.meta.vo.LiveBroadcastPageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 直播配置页面缓存更新
 * @Date 2023/11/15
 */
@Component
public class LiveBroadcastCachePutPack {

    private static final Logger LOGGER = LoggerFactory.getLogger(LiveBroadcastCachePutPack.class);

    @Autowired
    LiveBroadcastPack liveBroadcastPack;

    /**
     * 更新直播配置页面缓存
     *
     * @return
     */
    @CachePut(value = CacheableType.LIVE_BROADCAST_PAGE, key = "caches[0].name + '-LiveBroadcastCachePack-' + methodName")
    public List<LiveBroadcastPageVO> listLiveBroadcastPage() {
        List<LiveBroadcastPageVO> liveBroadcastPages = liveBroadcastPack.listLiveBroadcastPage();
        LOGGER.info(String.format("[缓存更新结果]缓存key:%s,方法名:%s,缓存更新参数:[],缓存更新响应:[%s]",
                CacheableType.LIVE_BROADCAST_PAGE, "listLiveBroadcastPage", liveBroadcastPages));
        return liveBroadcastPages;
    }
}
