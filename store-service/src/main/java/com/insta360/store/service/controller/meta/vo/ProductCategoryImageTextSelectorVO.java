package com.insta360.store.service.controller.meta.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.meta.model.ProductCategoryImageTextSelector;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Author: wkx
 * @Date: 2023/10/30
 * @Description:
 */
public class ProductCategoryImageTextSelectorVO implements Serializable {


    private Integer id;

    /**
     * 内部名称
     */
    private String insideName;

    /**
     * 图文链接
     */
    private String imageLink;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 默认展示
     */
    private Boolean defaultDisplay;

    /**
     * 筛选器名称
     */
    private String imageTextName;

    public ProductCategoryImageTextSelectorVO() {
    }

    public ProductCategoryImageTextSelectorVO(ProductCategoryImageTextSelector imageTextSelector) {
        if (Objects.nonNull(imageTextSelector)) {
            BeanUtil.copyProperties(imageTextSelector, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInsideName() {
        return insideName;
    }

    public void setInsideName(String insideName) {
        this.insideName = insideName;
    }

    public String getImageLink() {
        return imageLink;
    }

    public void setImageLink(String imageLink) {
        this.imageLink = imageLink;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    public Boolean getDefaultDisplay() {
        return defaultDisplay;
    }

    public void setDefaultDisplay(Boolean defaultDisplay) {
        this.defaultDisplay = defaultDisplay;
    }

    public String getImageTextName() {
        return imageTextName;
    }

    public void setImageTextName(String imageTextName) {
        this.imageTextName = imageTextName;
    }

    @Override
    public String toString() {
        return "AccessoryCategoryFilterVO{" +
                "id=" + id +
                ", insideName='" + insideName + '\'' +
                ", imageLink='" + imageLink + '\'' +
                ", orderIndex=" + orderIndex +
                ", defaultDisplay=" + defaultDisplay +
                ", imageTextName='" + imageTextName + '\'' +
                '}';
    }
}
