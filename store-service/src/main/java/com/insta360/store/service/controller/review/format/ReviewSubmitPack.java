package com.insta360.store.service.controller.review.format;

import com.insta360.compass.core.enums.InstaLanguage;
import com.insta360.compass.core.exception.InstaException;
import com.insta360.compass.core.util.StringUtil;
import com.insta360.store.business.commodity.model.CommodityDisplay;
import com.insta360.store.business.commodity.model.CommodityInfo;
import com.insta360.store.business.commodity.service.CommodityDisplayService;
import com.insta360.store.business.commodity.service.CommodityInfoService;
import com.insta360.store.business.meta.enums.StoreConfigKey;
import com.insta360.store.business.meta.robot.FeiShuAtUser;
import com.insta360.store.business.meta.robot.FeiShuGroupRobot;
import com.insta360.store.business.meta.robot.FeiShuMessageUtil;
import com.insta360.store.business.meta.service.StoreConfigService;
import com.insta360.store.business.order.exception.OrderErrorCode;
import com.insta360.store.business.order.model.Order;
import com.insta360.store.business.order.model.OrderItem;
import com.insta360.store.business.order.service.OrderItemService;
import com.insta360.store.business.product.enums.ProductCategoryMainType;
import com.insta360.store.business.product.exception.ProductErrorCode;
import com.insta360.store.business.product.model.Product;
import com.insta360.store.business.product.model.ProductInfo;
import com.insta360.store.business.product.service.ProductInfoService;
import com.insta360.store.business.product.service.ProductService;
import com.insta360.store.business.product.service.impl.helper.ProductCategoryHelper;
import com.insta360.store.business.review.exception.ReviewErrorCode;
import com.insta360.store.business.review.model.ReviewEmailSendRecord;
import com.insta360.store.business.review.service.ReviewEmailSendRecordService;
import com.insta360.store.service.controller.review.vo.ReviewOrderItemVO;
import com.insta360.store.service.controller.review.vo.ReviewSubmitVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: wbt
 * @Date: 2022/07/05
 * @Description:
 */
@Component
public class ReviewSubmitPack {

    @Autowired
    ProductService productService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    StoreConfigService storeConfigService;

    @Autowired
    CommodityInfoService commodityInfoService;

    @Autowired
    CommodityDisplayService commodityDisplayService;

    @Autowired
    ReviewEmailSendRecordService reviewEmailSendRecordService;

    @Autowired
    ProductCategoryHelper productCategoryHelper;

    /**
     * 评论订单信息封装
     * <p>
     * items 展示排序：主机 > 配件 > 增值服务 > 其他类型商品
     *
     * @param order
     * @return
     */
    public ReviewSubmitVO doPackReviewSubmitInfo(Order order, InstaLanguage language) {
        List<OrderItem> orderItems = orderItemService.getByOrder(order.getId());
        if (CollectionUtils.isEmpty(orderItems)) {
            throw new InstaException(OrderErrorCode.OrderItemNotFoundException);
        }

        // 评论邮件发送记录
        ReviewEmailSendRecord reviewEmailSendRecord = reviewEmailSendRecordService.getReviewEmailSendRecord(order.getOrderNumber(), order.getContactEmail());
        if (reviewEmailSendRecord == null) {
            throw new InstaException(ReviewErrorCode.ReviewEmailNotFoundException);
        }

        ReviewSubmitVO reviewSubmit = new ReviewSubmitVO();
        reviewSubmit.setOrderNumber(order.getOrderNumber());

        // 如果超过了评论有效期，则不进行产品数据的封装
        String configValue = storeConfigService.getConfigValue(StoreConfigKey.review_delay_time_config);
        if (LocalDateTime.now().isAfter(reviewEmailSendRecord.getSendTime().plusDays(StringUtil.isNotBlank(configValue) ? Integer.parseInt(configValue) : 30))) {
            reviewSubmit.setReviewRule(false);
            return reviewSubmit;
        }

        List<ReviewOrderItemVO> reviewOrderItems = orderItems.stream().map(orderItem -> {
            // 校验产品信息
            Product product = productService.getById(orderItem.getProduct());
            if (product == null) {
                throw new InstaException(ProductErrorCode.ProductNotFoundException);
            }

            // 产品多语言文案
            ProductInfo productInfo = productInfoService.getInfoDefaultEnglish(orderItem.getProduct(), language);
            if (productInfo == null) {
                String message = "产品多语言配置信息缺失。产品Id:" + orderItem.getProduct() + ", 语言:" + language;
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY, FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW,
                        FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            }

            // 套餐多语言文案
            CommodityInfo commodityInfo = commodityInfoService.getInfoDefaultEnglish(orderItem.getCommodity(), language);
            if (commodityInfo == null) {
                String message = "套餐多语言配置信息缺失。产品Id:" + orderItem.getProduct() + ", 套餐Id: " + orderItem.getCommodity() + ", 语言:" + language;
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY, FeiShuAtUser.LCN, FeiShuAtUser.ZXY,
                        FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            }

            // 套餐主图
            CommodityDisplay commodityDisplay = commodityDisplayService.getFirstDisplay(orderItem.getCommodity());
            if (commodityDisplay == null) {
                String message = "套餐主图配置信息缺失。产品Id:" + orderItem.getProduct() + ", 套餐Id: " + orderItem.getCommodity();
                FeiShuMessageUtil.storeGeneralMessage(message, FeiShuGroupRobot.MainNotice, FeiShuAtUser.TW, FeiShuAtUser.ZLY,
                        FeiShuAtUser.LCN, FeiShuAtUser.ZXY, FeiShuAtUser.CWW, FeiShuAtUser.LCY, FeiShuAtUser.LR, FeiShuAtUser.CZY);
            }
            ProductCategoryMainType categoryMainType = productCategoryHelper.getCategoryMainByKey(product.getCategoryKey());

            // 数据封装
            ReviewOrderItemVO reviewOrderItem = new ReviewOrderItemVO();
            reviewOrderItem.setOrderItemId(orderItem.getId());
            reviewOrderItem.setNumber(orderItem.getNumber());
            reviewOrderItem.setIsGift(orderItem.getIsGift());
            reviewOrderItem.setReviewState(orderItem.getReviewState());
            reviewOrderItem.setImageUrl(commodityDisplay == null ? "" : commodityDisplay.getUrl());
            reviewOrderItem.setProductName(productInfo == null ? "" : productInfo.getName());
            reviewOrderItem.setProductUrlKey(product.getUrlKey());
            reviewOrderItem.setProductType(product.getType());
            reviewOrderItem.setCommodityName(commodityInfo == null ? "" : commodityInfo.getName());
            reviewOrderItem.setOrderIndex(categoryMainType.getOrderIndex());
            return reviewOrderItem;
        }).sorted(Comparator.comparing(ReviewOrderItemVO::getOrderIndex)).collect(Collectors.toList());

        reviewSubmit.setReviewRule(true);
        reviewSubmit.setOrderItems(reviewOrderItems);
        return reviewSubmit;
    }
}
