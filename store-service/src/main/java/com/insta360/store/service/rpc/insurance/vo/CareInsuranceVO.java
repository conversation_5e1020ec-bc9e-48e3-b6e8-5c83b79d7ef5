package com.insta360.store.service.rpc.insurance.vo;

import com.insta360.compass.core.util.BeanUtil;
import com.insta360.store.business.insurance.model.CareInsurance;
import com.insta360.store.business.insurance.model.CareInsuranceActivationCard;
import com.insta360.store.business.insurance.model.CarePlusInsurance;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @description:
 * @author: py
 * @create: 2023-09-13 14:09
 */
public class CareInsuranceVO implements Serializable {

    private Integer id;

    /**
     * 保险号
     */
    private String insuranceNumber;

    /**
     * 保险类型
     */
    private String insuranceType;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 序列号
     */
    private String deviceSerial;

    /**
     * 绑定时间
     */
    private LocalDateTime bindTime;

    /**
     * 使用时间
     */
    private LocalDateTime useTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * care的保险类型 虚拟、实体卡
     */
    private String careInsuranceType;

    /**
     * 剩余使用次数
     */
    private Integer remainingUsageCount;

    public CareInsuranceVO() {
    }

    public CareInsuranceVO(CareInsurance careInsurance) {
        if (careInsurance != null) {
            BeanUtil.copyProperties(careInsurance, this);
        }
    }

    public CareInsuranceVO(CareInsuranceActivationCard careInsuranceActivationCard) {
        if (careInsuranceActivationCard != null) {
            BeanUtil.copyProperties(careInsuranceActivationCard, this);
        }
    }

    public CareInsuranceVO(CarePlusInsurance carePlusInsurance) {
        if (carePlusInsurance != null) {
            BeanUtil.copyProperties(carePlusInsurance, this);
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInsuranceNumber() {
        return insuranceNumber;
    }

    public void setInsuranceNumber(String insuranceNumber) {
        this.insuranceNumber = insuranceNumber;
    }

    public String getInsuranceType() {
        return insuranceType;
    }

    public void setInsuranceType(String insuranceType) {
        this.insuranceType = insuranceType;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceSerial() {
        return deviceSerial;
    }

    public void setDeviceSerial(String deviceSerial) {
        this.deviceSerial = deviceSerial;
    }

    public LocalDateTime getBindTime() {
        return bindTime;
    }

    public void setBindTime(LocalDateTime bindTime) {
        this.bindTime = bindTime;
    }

    public LocalDateTime getUseTime() {
        return useTime;
    }

    public void setUseTime(LocalDateTime useTime) {
        this.useTime = useTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public String getCareInsuranceType() {
        return careInsuranceType;
    }

    public void setCareInsuranceType(String careInsuranceType) {
        this.careInsuranceType = careInsuranceType;
    }

    public Integer getRemainingUsageCount() {
        return remainingUsageCount;
    }

    public void setRemainingUsageCount(Integer remainingUsageCount) {
        this.remainingUsageCount = remainingUsageCount;
    }

    @Override
    public String toString() {
        return "CareInsuranceVO{" +
                "id=" + id +
                ", insuranceNumber='" + insuranceNumber + '\'' +
                ", insuranceType='" + insuranceType + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", deviceSerial='" + deviceSerial + '\'' +
                ", bindTime=" + bindTime +
                ", useTime=" + useTime +
                ", expireTime=" + expireTime +
                ", careInsuranceType='" + careInsuranceType + '\'' +
                ", remainingUsageCount=" + remainingUsageCount +
                '}';
    }
}
