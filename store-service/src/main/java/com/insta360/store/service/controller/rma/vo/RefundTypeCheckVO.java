package com.insta360.store.service.controller.rma.vo;

import com.insta360.store.business.rma.bo.RefundButtonBO;
import com.insta360.store.business.rma.model.RmaReasonOption;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/3/11
 */
public class RefundTypeCheckVO implements Serializable {

    /**
     * 售后类型的key值
     */
    private String key;

    /**
     * 售后类型的文案值
     */
    private String value;

    /**
     * 售后类型具体描述
     */
    private String desc;

    /**
     * 售后客户端按钮列表
     */
    private List<RefundButtonBO> buttonList;

    /**
     * 售后原因
     */
    private List<RmaReasonOption> refundReasonList;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<RmaReasonOption> getRefundReasonList() {
        return refundReasonList;
    }

    public void setRefundReasonList(List<RmaReasonOption> refundReasonList) {
        this.refundReasonList = refundReasonList;
    }

    public List<RefundButtonBO> getButtonList() {
        return buttonList;
    }

    public void setButtonList(List<RefundButtonBO> buttonList) {
        this.buttonList = buttonList;
    }
}
