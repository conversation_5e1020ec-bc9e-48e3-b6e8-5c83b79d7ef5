package com.insta360.store.service.controller.rma.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 售后单物流信息VO
 * @Date 2024/2/27
 */
public class RmaOrderLogisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户寄回快递物流公司
     */
    private String customerReturnExpressCompany;

    /**
     * 客户寄回快递物流单号
     */
    private String customerReturnExpressNumber;

    /**
     * 客户快递寄出时间
     */
    private LocalDateTime customerReturnExpressTime;

    /**
     * 平台寄出快递物流公司
     */
    private String platformReturnExpressCompany;

    /**
     * 平台寄出快递物流单号
     */
    private String platformReturnExpressNumber;

    /**
     * 平台快递寄出时间
     */
    private LocalDateTime platformReturnExpressTime;

    public String getCustomerReturnExpressCompany() {
        return customerReturnExpressCompany;
    }

    public void setCustomerReturnExpressCompany(String customerReturnExpressCompany) {
        this.customerReturnExpressCompany = customerReturnExpressCompany;
    }

    public String getCustomerReturnExpressNumber() {
        return customerReturnExpressNumber;
    }

    public void setCustomerReturnExpressNumber(String customerReturnExpressNumber) {
        this.customerReturnExpressNumber = customerReturnExpressNumber;
    }

    public LocalDateTime getCustomerReturnExpressTime() {
        return customerReturnExpressTime;
    }

    public void setCustomerReturnExpressTime(LocalDateTime customerReturnExpressTime) {
        this.customerReturnExpressTime = customerReturnExpressTime;
    }

    public String getPlatformReturnExpressCompany() {
        return platformReturnExpressCompany;
    }

    public void setPlatformReturnExpressCompany(String platformReturnExpressCompany) {
        this.platformReturnExpressCompany = platformReturnExpressCompany;
    }

    public String getPlatformReturnExpressNumber() {
        return platformReturnExpressNumber;
    }

    public void setPlatformReturnExpressNumber(String platformReturnExpressNumber) {
        this.platformReturnExpressNumber = platformReturnExpressNumber;
    }

    public LocalDateTime getPlatformReturnExpressTime() {
        return platformReturnExpressTime;
    }

    public void setPlatformReturnExpressTime(LocalDateTime platformReturnExpressTime) {
        this.platformReturnExpressTime = platformReturnExpressTime;
    }

    @Override
    public String toString() {
        return "RmaOrderLogisticsVO{" +
                "customerReturnExpressCompany='" + customerReturnExpressCompany + '\'' +
                ", customerReturnExpressNumber='" + customerReturnExpressNumber + '\'' +
                ", customerReturnExpressTime=" + customerReturnExpressTime +
                ", platformReturnExpressCompany='" + platformReturnExpressCompany + '\'' +
                ", platformReturnExpressNumber='" + platformReturnExpressNumber + '\'' +
                ", platformReturnExpressTime=" + platformReturnExpressTime +
                '}';
    }
}
