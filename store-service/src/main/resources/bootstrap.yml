NACOS_NAMESPACE: dev

GLO<PERSON>L_ENV_VAR_NACOS_AK:
GLOBAL_ENV_VAR_NACOS_SK:

GLOBAL_ENV_VAR_NACOS_USERNAME: nacos_admin
GLOBAL_ENV_VAR_NACOS_PASSWORD: xR4WZjRCZvHd9F3ATE3XVSABT4frKUhBvHpSY7BJ1
GLOBAL_ENV_NACOS_URL: 172.19.47.198:8848

spring:
  application:
    name: store-service

  cloud:
    nacos:
      discovery:
        server-addr: ${GLOBAL_ENV_NACOS_URL}
        namespace: ${NACOS_NAMESPACE}
        accessKey: ${GLOBAL_ENV_VAR_NACOS_AK}
        secretKey: ${GLOBAL_ENV_VAR_NACOS_SK}
        username: ${GLOBAL_ENV_VAR_NACOS_USERNAME}
        password: ${GLO<PERSON>L_ENV_VAR_NACOS_PASSWORD}
      config:
        server-addr: ${GLOBAL_ENV_NACOS_URL}
        namespace: ${NACOS_NAMESPACE}
        accessKey: ${GLOBAL_ENV_VAR_NACOS_AK}
        secretKey: ${GLOBAL_ENV_VAR_NACOS_SK}
        username: ${GLOBAL_ENV_VAR_NACOS_USERNAME}
        password: ${GLOBAL_ENV_VAR_NACOS_PASSWORD}
        group: store
        file-extension: yml
        extension-configs:
          - data-id: common.yml
            group: store
            refresh: true
          - data-id: data.source.yml
            group: store
            refresh: true